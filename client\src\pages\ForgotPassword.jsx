import axios from 'axios'
import logo from '../assets/logo.svg'
import toast, { Toaster } from 'react-hot-toast';
import { useState } from "react";
import { Tooltip } from "@material-tailwind/react";
import { useNavigate } from 'react-router-dom';
import * as yup from 'yup';

export default function ForgotPassword() {
    const [email, setEmail] = useState('');
    const navigate = useNavigate()

    //validate login form
    const validationSchema = yup.object({
        email: yup.string()
            .required('Email is required')
            .max(100, 'Email must be less than 100 characters long') // Maximum length for email
            .matches(
                /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
                'Please enter a valid email address'
            ),
    });

    //validate login form
    async function validateForm() {
        try {
            await validationSchema.validate({ email }, { abortEarly: false });
            handleSubmitEmail();
        } catch (error) {

            if (error.inner) {
                error.inner.forEach(err => toast.error(err.message)); // Display errors
            } else {
                toast.error(error.message);
            }
        }
    }

    const handleSubmitEmail = async () => {
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/forget-password`,
                {
                    email
                }
            )
            if (response.status === 200 && response.data !== null) {
                toast.success(response.data.message)
                if (localStorage.getItem('token') !== null) {
                    localStorage.removeItem('token')
                }
                setTimeout(() => {
                    navigate("/", { state: true })
                }, 2000)
            }
            else {
                toast.error("Something went wrong try again later"); // Fallback message
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("Something went wrong try again later"); // Fallback message
            }
            console.error("Failed To Send Email:", error);
        }
    };

    return (
        <div className="min-h-screen flex flex-col lgo:flex-row bg-[#ffff]">
            <Toaster position="top-center" />
            <div className="w-full lgo:w-1/2 flex flex-col bg-[#ffff] py-12 px-6 smo:px-12 lgo:px-20 xlo:px-24 justify-center">
                <div className="max-w-md mx-auto my-[20vh] xlo:mx-[9vw]">
                    <div className="flex flex-col mb-6">
                        <div className="mb-6">
                            <img src={logo} alt="AccuVelocity" className="h-12 w-auto mx-auto" />
                        </div>
                        {/* Responsive font sizes for different screen sizes */}
                        <h2 className="text-center text-lg smo:text-xl lgo:text-2xl font-semibold text-gray-900 lgo:text-left">Forgot Password</h2>
                        <p className="mt-2 text-center text-xs smo:text-sm lgo:text-base text-gray-600 lgo:text-left mb-6">
                            Enter the email associated with your account and we will send you a link to reset your password. Please note, this link will only be valid for 10 minutes.
                        </p>
                    </div>
                    <form className="space-y-6" onSubmit={(e) => {
                        e.preventDefault();
                        validateForm();
                        }}>
                        <div>
                            <label htmlFor="email-login" className="block font-medium text-gray-800 text-xs smo:text-sm lgo:text-base">Work Email<span className="text-red-500">*</span></label>
                            <input
                                id="email-login"
                                type="email"
                                required
                                className="mt-1 block w-full p-3 border border-gray-300 rounded-md text-xs smo:text-sm lgo:text-base shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                placeholder="Work Email"
                                value={email}
                                onChange={(e) => setEmail(e.target.value)}
                            />
                        </div>
                        <div>
                            <button
                                type="submit"
                                className="w-full flex justify-center py-2 px-4 border text-xs smo:text-sm lgo:text-base border-transparent rounded-md shadow-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                            >
                                Send Mail
                            </button>
                        </div>
                    </form>
                </div>
            </div>
            <div className="w-full lgo:w-1/2 relative">
                <div className="absolute inset-0 bg-[#ffff] pt-14 pr-14 pb-14">
                    <div className="flex h-full items-center justify-center">
                        {/* Adjust max-width and max-height as needed */}
                        <Tooltip content="Image to be changed later">
                            <img src="/Login/image.png" alt="Decorative" className="max-w-full max-h-full" />
                        </Tooltip>
                    </div>
                </div>
            </div>
        </div>

    );
}