// import{ useState, useEffect, useRef } from 'react';
import axios from 'axios';
import toast, { Toaster } from 'react-hot-toast';
import logo from '../../assets/logo.svg'; // Adjust the path as necessary
import { useEffect, useRef, useState } from 'react';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import countries from '../../assets/phoneCountries.json'
import { RxCrossCircled } from "react-icons/rx";
import { IoCheckmarkCircleOutline } from "react-icons/io5";
import { Tooltip } from "@material-tailwind/react";

export default function OtpModal({ isOpen, onClose, email, name, password, phoneNo }) {
    const [inputOtp, setInputOtp] = useState('');
    const [otp, setOtp] = useState('');
    const otpSent = useRef(false);
    const [isResendDisabled, setIsResendDisabled] = useState(false);
    const [timeLeft, setTimeLeft] = useState(30); // 30 seconds cooldown for OTP resend
    const navigate = useNavigate();
    // State variable for referral code
    const [referralCode, setReferralCode] = useState('');
    const [isValidReferralCode, setValidReferralCode] = useState(false);
    const [isValidOTPIcon, setValidOTPIcon] = useState(false);
    const [showInvalidReferralCodeIcon, setShowInvalidReferralCodeIcon] = useState(false);
    const [showInvalidOTPIcon, setShowInvalidOTPIcon] = useState(false);
    const [isOTPValidateAllow, setOTPValidateAllow] = useState(true);
    useEffect(() => {
        if (!isOpen) {
            // Reset all state variables to their default values
            setInputOtp('');
            setOtp('');
            otpSent.current = false;
            setIsResendDisabled(false);
            setTimeLeft(30);
            setReferralCode('');
            setValidReferralCode(false);
            setValidOTPIcon(false);
            setShowInvalidReferralCodeIcon(false);
            setShowInvalidOTPIcon(false);
            setOTPValidateAllow(true)
        }
    }, [isOpen]);

    useEffect(() => {
        console.log("isOTPValidateAllow -> ", isOTPValidateAllow)
    }, [isOTPValidateAllow])

    useEffect(() => {
        // This function will be called when the user tries to leave the page
        const handleBeforeUnload = (e) => {
            if (otpSent.current) {
                const message = "Are you sure you want to terminate the registration process?";
                e.preventDefault(); // If you prevent default behavior in older browsers
                e.returnValue = message; // Chrome requires returnValue to be set
                return message; // Some browsers may display this message
            }
        };

        // Add the event listener for beforeunload
        window.addEventListener('beforeunload', handleBeforeUnload);

        // Remove the event listener when component unmounts or when the OTP process is no longer active
        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, []); // Empty dependency array ensures this effect is only applied once when the component mounts


    useEffect(() => {
        // Trigger sendOtp only if otpSent is false and isOpen is true
        if (isOpen && !otpSent.current) {
            sendOtp();
            otpSent.current = true; // Mark OTP as sent
            setIsResendDisabled(true); // Start countdown immediately
        }
    }, [isOpen, email]); // add isOpen dependency

    useEffect(() => {
        let interval = null;
        if (isResendDisabled) {
            interval = setInterval(() => {
                setTimeLeft((prevTimeLeft) => prevTimeLeft - 1);
            }, 1000);
        }
        return () => clearInterval(interval);
    }, [isResendDisabled]);

    useEffect(() => {
        if (timeLeft === 0 && isResendDisabled) {
            setIsResendDisabled(false);
            setTimeLeft(30); // Reset timer
        }
    }, [timeLeft]);

    function getCountryNameFromPhone(phoneNo, countries) {
        if (!phoneNo) return null; // If phoneNo is empty or undefined

        // Extract the potential dial code (assuming the dial code is at the start of the string)
        let potentialDialCode = phoneNo.match(/^\+\d+/);

        // If there was no match, return null
        if (!potentialDialCode) return null;

        // Since match returns an array, get the first element which is the matched string
        potentialDialCode = potentialDialCode[0];

        // Find the country with the matching dial code
        const country = countries.find(country => phoneNo.startsWith(country.dial_code));

        return country ? country.name : null;
    }

    const sendOtp = async () => {
        if (!email) {
            toast.error("Email is required for OTP.");
            return;
        }
        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/send-otp`, { email });
            if (response.status === 200) {
                toast.success("OTP has been sent to your email.");
                setOtp(response.data.otp);
                setIsResendDisabled(true); // Disable resend button
            } else {
                toast.error("Failed to send OTP.");
            }
        } catch (error) {
            toast.error("An error occurred while sending the OTP.");
        }
    };

    const verifyOtpAndRegister = async () => {

        // Check if referral code is not empty
        if (referralCode && referralCode.trim().length > 0 && referralCode !== undefined && referralCode !== null) {
            try {
                const promo_response = await axios.get(`${import.meta.env.VITE_SERVER}/PromoCode/valid/${referralCode}`);
                // If promo code is valid, proceed
                if (promo_response.status === 200) {
                    setValidReferralCode(true);
                    setShowInvalidReferralCodeIcon(false);
                    toast.success(promo_response.data.message, { duration: 2000 });
                    // Proceed with OTP verification and registration
                    if (inputOtp === otp) {
                        setValidOTPIcon(true);
                        setShowInvalidOTPIcon(false);
                        toast.success("OTP Verified");
                        setOTPValidateAllow(false)
                        await finalizeRegistration();

                    } else {
                        setValidOTPIcon(false);
                        setShowInvalidOTPIcon(true);
                        setOTPValidateAllow(true)
                        toast.error("OTP Verification Failed");
                    }
                }
            } catch (error) {
                if (error.response && error.response.data && error.response.data.detail) {
                    setValidOTPIcon(false);
                    setValidReferralCode(false);
                    setShowInvalidOTPIcon(true);
                    setShowInvalidReferralCodeIcon(true);
                    setOTPValidateAllow(true)
                    // Handle specific error message received from API
                    toast.error(error.response.data.detail);
                } else {
                    setShowInvalidOTPIcon(true);
                    setValidOTPIcon(false);
                    setValidReferralCode(false);
                    setShowInvalidReferralCodeIcon(true);
                    setOTPValidateAllow(true)
                    // Handle generic error
                    toast.error("An error occurred while verifying promo code.");
                }
            }
        } else {
            // If referral code is empty, proceed with OTP verification and registration
            if (inputOtp === otp) {
                setValidOTPIcon(true);
                setShowInvalidOTPIcon(false);
                toast.success("OTP Verified");
                setOTPValidateAllow(false)
                await finalizeRegistration();
            } else {
                setValidOTPIcon(false);
                setShowInvalidOTPIcon(true);
                setOTPValidateAllow(true)
                toast.error("OTP Verification Failed");
            }
        }
    };

    const finalizeRegistration = async () => {
        try {

            // If password is valid, continue with registration
            const formData = new FormData();
            formData.append("email", email);
            formData.append("name", name);
            formData.append("password", password);
            if (phoneNo !== '') {
                formData.append("phoneNumber", phoneNo);
            }
            if (referralCode && referralCode.trim().length > 0 && referralCode !== undefined && referralCode !== null) {
                formData.append("promoCodeStr", referralCode)
            }
            formData.append("Country", getCountryNameFromPhone(phoneNo, countries));

            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/Registration`,
                formData
            );

            if (response.status === 200) {
                const { jwt_token, uid, role } = response.data;
                localStorage.setItem("NotOrgDetailsToken", jwt_token);
                localStorage.setItem("Role", role);
                localStorage.setItem("userId", uid);
                localStorage.setItem("created", response.data.created_at)
                setOTPValidateAllow(true)
                navigate('/OrganizationDetails'); // Use navigate instead of history.push
            }
        } catch (error) {
            setOTPValidateAllow(true)
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error(error.message || "Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help."); // Fallback message
            }
        }
    };

    if (!isOpen) return null;
    return (
        <div className="fixed inset-0 bg-gray-500 bg-opacity-75 flex items-center justify-center z-50">
            <Toaster position="top-center" />
            <div className="min-w-[300px] w-full max-w-sm rounded-lg bg-[#ffff] p-6 shadow-lg z-50">
                <button onClick={onClose} className="float-right font-bold">X</button>
                <img src={logo} alt="Company Logo" className="h-12 mx-auto mb-8" />
                <h1 className="mb-4 text-center text-lg font-semibold text-gray-900">Verify your email to continue</h1>
                <div style={{ position: 'relative' }}>
                    <input
                        type="text"
                        placeholder="Enter Referral Code (optional)"
                        value={referralCode}
                        onChange={(e) => {
                            // Convert input to uppercase and limit to 20 characters
                            const newValue = e.target.value.toUpperCase().slice(0, 20);
                            // Replace non-alphanumeric characters with empty string
                            const sanitizedValue = newValue.replace(/[^a-zA-Z0-9]/g, '');
                            // Set the sanitized value to state
                            setReferralCode(sanitizedValue);
                            // reset invaild referral Code icon & validation icon too
                            setValidReferralCode(false);
                            setShowInvalidReferralCodeIcon(false);
                        }}
                        className="block w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:ring-blue-500 mb-4"
                    />
                    {showInvalidReferralCodeIcon ? (
                        <div className="absolute top-1/2 right-3 transform -translate-y-1/2"><RxCrossCircled className="text-red-500" style={{ fontSize: '130%' }} /></div>
                    ) : isValidReferralCode ? (
                        <div className="absolute top-1/2 right-3 transform -translate-y-1/2"><IoCheckmarkCircleOutline className="text-green-500" style={{ fontSize: '130%' }} /></div>
                    ) : null}
                </div>
                <div style={{ position: 'relative' }}>
                    <input
                        type="number"
                        placeholder="Enter OTP"
                        value={inputOtp}
                        onChange={(e) => {
                            const newOtp = e.target.value.replace(/\D/g, ''); // Remove non-digit characters
                            if (newOtp.length <= 6) { // Check if the new OTP has at most 6 digits
                                setInputOtp(newOtp);
                            }
                        }}
                        onKeyDown={(event) => {
                            // Allow navigation keys: backspace, delete, arrows
                            if (['Backspace', 'Delete', 'ArrowLeft', 'ArrowRight'].includes(event.code)) {
                                return true;
                            }
                            // Prevent 'e' and other non-numeric characters
                            if (event.key === 'e' || event.key === '+' || event.key === '-' || event.key === '.' || event.code === 'Space') {
                                event.preventDefault();
                            }
                            // Trigger OTP verification and registration on Enter
                            if (event.key === 'Enter') {
                                verifyOtpAndRegister();
                            }
                        }}
                        className="block w-full rounded-md border border-gray-300 p-3 shadow-sm focus:border-blue-500 focus:ring-blue-500"
                    />
                    {showInvalidOTPIcon ? (
                        <div className="absolute top-1/2 right-3 transform -translate-y-1/2"><RxCrossCircled className="text-red-500" style={{ fontSize: '130%' }} /></div>
                    ) : isValidOTPIcon ? (
                        <div className="absolute top-1/2 right-3 transform -translate-y-1/2"><IoCheckmarkCircleOutline className="text-green-500" style={{ fontSize: '130%' }} /></div>
                    ) : null}
                </div>
                <div className="mt-6">
                    <Tooltip content={!isOTPValidateAllow ? "Please wait, your request is being processed." : "Click the button to verify your OTP"} placement="bottom">
                        <button
                            onClick={verifyOtpAndRegister}
                            className={`w-full flex justify-center rounded-md border border-transparent bg-[#003654] py-2 px-4 text-sm font-medium text-white shadow-sm hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654] ${isOTPValidateAllow ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
                            disabled={!isOTPValidateAllow}
                        >
                            Verify OTP and Register
                        </button>
                    </Tooltip>
                    <button
                        onClick={() => {
                            sendOtp();
                        }}
                        disabled={isResendDisabled}
                        className={`mt-2 w-full flex justify-center rounded-md border border-transparent bg-gray-200 py-2 px-4 text-sm font-medium text-gray-700 shadow-sm ${isResendDisabled ? "cursor-not-allowed hover:bg-gray-200" : "hover:bg-gray-300"
                            } focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-gray-400`}
                    >
                        {isResendDisabled ? `Resend OTP (${timeLeft}s)` : "Resend OTP"}
                    </button>
                </div>
            </div>
        </div >
    );
}

OtpModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    email: PropTypes.string.isRequired,
    name: PropTypes.string.isRequired,
    password: PropTypes.string.isRequired,
    phoneNo: PropTypes.string,
};