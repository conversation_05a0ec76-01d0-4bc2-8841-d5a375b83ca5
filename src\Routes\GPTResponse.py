from src.Controllers.GPTResponse_controller import CGPTResponseData
from fastapi import APIRouter, Depends, UploadFile, status, Query
#Importing libraries
from typing import List
from src.middleware.checkAuth import user_required
from src.middleware.checkDocument import <PERSON><PERSON>dRequired
from src.Schemas.GPT_Schema import GPT<PERSON><PERSON>NUpdate
# Declaring router
GPTResponseRouter = APIRouter(tags=['GPTResponseData'],  prefix="/api")


@GPTResponseRouter.get("/getALLDocGPTJson/", status_code=status.HTTP_200_OK)
async def GetAllDocs(iUserID = Depends(user_required),iLimit=Query(1, description="Page number starting from 1"), iOffset=Query(10, description="Number of documents per page")):
    #iUserID: int, iLimit: int = Query(10, gt=0), iOffset: int = Query(0, ge=0)
    return await CGPTResponseData.MSGetAllDocExtractJsonDataBaseOnUserID(iUserID=iUserID, iLimit=iLimit, iOffset=iOffset)


@GPTResponseRouter.get("/download/", status_code=status.HTTP_200_OK)
async def DownloadDoc(lsDocIds: List[int] = Query(..., description="List of document IDs to download the extracted data"), 
                        strFileType:str = Query(..., description="Type of file to download,  excel/csv"), 
                        iUserID = Depends(user_required)):
    return await CGPTResponseData.MSDownloadExtractedData(iUserID=iUserID, lsDocIds=lsDocIds, downloadType=strFileType)

@GPTResponseRouter.post("/upload-retrieve-gpt-response")
async def UploadAndRetrieveGptResponse(pdf_file: UploadFile , iUserID = Depends(user_required), strModelName=  Query("Invoice", description="ModelName default is Invoice")):
    return await CGPTResponseData.performDocumentExtractionOnBinaryData(userid=iUserID, file=pdf_file, strModelName=strModelName)


@GPTResponseRouter.get("/documents/")
async def GetDocumentById(DocId: int = Query(..., description="The ID of the document to retrieve"),dictUserData : dict= Depends(DocIdRequired)):
    # iUserID:int, iDocId: int
    return await CGPTResponseData.MSGetDocumentById(iUserID=dictUserData.get("UserId"),iDocId= dictUserData.get("DocId"))

@GPTResponseRouter.post("/upload-updated-gpt-json/")
async def UpdateJson(data:GPTJSONUpdate ,DocId:int =  Query(..., description="The ID of the document to retrieve"),  dictRequestData:int = Depends(DocIdRequired),bInitialCall=False):
    return await CGPTResponseData.MSSetExtractValidatedData(iUserID=dictRequestData.get("UserId"), doc_id=dictRequestData.get("DocId"), data=data,bInitialCall=bInitialCall)