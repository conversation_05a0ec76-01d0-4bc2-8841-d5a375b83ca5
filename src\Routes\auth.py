# Importing libraries
from fastapi import APIRouter, Request, Security, Depends, UploadFile, File, Form, HTTPException, Query
from src.Controllers.auth_controller import <PERSON><PERSON><PERSON>ontroller
from typing import Optional
from src.Schemas.auth_models import LoginRequest, UserRegistrationInput, ForgotPasswordModel, ResetPassword, UpdateUserDetails, UpdatePassword
from starlette.background import BackgroundTasks
from fastapi.security import OAuth2PasswordBearer
from src.middleware.checkAuth import admin_required, user_required
from src.middleware.checkRateLimit import rate_limiter
from datetime import datetime
from pydantic import BaseModel, EmailStr, ValidationError
from config.db_config import AsyncSessionLocal
from ensure import ensure_annotations
from src.Models.models import UpCommingUsers
from sqlalchemy.future import select
from datetime import timedelta
from sqlalchemy.exc import IntegrityError
# Define the token URL if you have one; it's not necessary for just token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl='token')
import traceback

# Declaring router
auth = APIRouter(tags=['Authentication'],  prefix="/api")


async def add_email(email):
    """
    Purpose : Adds an email to the up_comming_users table if it doesn't already exist.

    Inputs  :   (1) request   :   AddEmailRequest object containing the email to add.

    Output  : It returns a message indicating success or raises an HTTPException.

    Example : await CAuthController.add_email(request=AddEmailRequest(email="<EMAIL>"))
    """
    try:
        async with AsyncSessionLocal() as session:
            # Check if the email already exists
            result = await session.execute(select(UpCommingUsers).filter(UpCommingUsers.email == email.lower()))
            existing_email = result.scalars().first()

            if existing_email:
                raise HTTPException(
                    status_code=404, detail="Email already exists")

            # If not, add the new email
            new_email = UpCommingUsers(email=email.lower())
            session.add(new_email)
            await session.commit()

            return {"message": "Email added successfully"}

    except HTTPException as http_exc:
        # Handle the HTTPException first
        await session.rollback()
        raise http_exc
    except IntegrityError:
        await session.rollback()
        raise HTTPException(status_code=400, detail="Email already exists")
    except Exception as e:
        await session.rollback()
        raise HTTPException(
            status_code=500, detail="An internal server error occurred")
    finally:
        await session.close()  # Ensure the session is closed properly

@auth.post("/upcoming-user-add")
async def addEmail(email: str = Form(...)):
    return await add_email(email)


# Login API
@auth.post('/login')
async def login(user: LoginRequest, request: Request):
    return await CAuthController.MSLogin(request=user)
# Registration API


@auth.post('/Registration')
async def userRegistration(email: str = Form(...), password: str = Form(...), name: str = Form(...), roleName: str = Form(None), phoneNumber: Optional[str] = Form(None),    profilePicture: UploadFile = File(None), Country: Optional[str] = Form(None), bUsePaidOCR:Optional[bool] = Form(None), bUsePaidDocExtractor:Optional[bool] = Form(None), promoCodeStr :Optional[str] = Form(None)):
    try:
        return await CAuthController.MSUserRegistration(strEmail=email, strPassword=password, strName=name, roleName=roleName, strPhoneNumber=phoneNumber, profilePicture=profilePicture, Country=Country, bUsePaidOCR= bUsePaidOCR if bUsePaidOCR is not None else False, bUsePaidDocExtractor = bUsePaidDocExtractor if bUsePaidDocExtractor is not None else False, promoCodeStr = promoCodeStr)
    except ValidationError as e:
        # If the data is not valid, raise an HTTPException with a 422 Unprocessable Entity status code
        raise HTTPException(status_code=422, detail=str(e.errors()))
    except Exception as e:
        raise HTTPException(status_code=e.status_code,detail=e.detail)


@auth.post('/initialize')
async def initializeUser(iUserID: int = Form(...)):
    try:
        return await CAuthController.MSInitializeUserData(iUserID=iUserID)
    except ValidationError as e:
        # If the data is not valid, raise an HTTPException with a 422 Unprocessable Entity status code
        raise HTTPException(status_code=422, detail=str(e.errors()))
    except Exception as e:
        raise HTTPException(status_code=e.status_code,detail=e.detail)

# Update User Details


@auth.put("/update/user/{user_id}")
async def update_user(  user_id: int,                # id of the user to update the details for
                        current_user_id:int = Depends(user_required),         # id of the current logged in user
                        name: Optional[str] = Form(None),		                        
                        email: Optional[str] = Form(None),		                        
                        phoneNumber: Optional[str] = Form(None),		                        
                        profilePicture: Optional[UploadFile] = File(None),		                        
                        Country: Optional[str] = Form(None),		                        
                        rolename: Optional[str] = Form(None),	                        
                        usePaidOCR: Optional[bool] = Form(None), 
                        usePaidDocExtractor: Optional[bool] = Form(None), 
                        used_tokens: Optional[int] = Form(None), 
                        api_requested: Optional[int] = Form(None), 
                        page_limit_left: Optional[int] = Form(None),
                        pro_page_limit: Optional[int] = Form(None),
                        standard_page_limit_usage: Optional[int] = Form(None),
                        standard_page_limit: Optional[int] = Form(None),
                        promoCodeStr:Optional[str] = Form(None) 
                    ):
    """
    Update user details, including an optional profile picture.
    - name: Optional[str]
    - email: Optional[str]
    - phoneNumber: Optional[str]
    - profilePicture: Optional[UploadFile]
    
    MSUpdateUserDetails(user_id: int, name: Optional[str] = None, email: Optional[str] = None, phoneNumber: Optional[str] = None, profilePicture: Optional[UploadFile] = FastAPIFile(None), rolename: Optional[str] = None, Country: Optional[str] = None, usePaidOCR: Optional[bool] = None, usePaidDocExtractor: Optional[bool] = None, used_tokens: Optional[int] = None, api_requested: Optional[int] = None, page_limit_left: Optional[int] = None, pro_page_limit: Optional[int] = None, standard_page_limit_usage: Optional[int] = None, standard_page_limit: Optional[int] = None, promoCodeStr: Optional[str] = None, is_subscription_active: bool = None, active_plan_name: str = None, bOverwrite: bool = True)
    """
    try:
        updated_user_details = await CAuthController.MSUpdateUserDetails(
            user_id=user_id,
            current_user_id=current_user_id,
            name=name,
            email=email,
            phoneNumber=phoneNumber,
            profilePicture=profilePicture,
            rolename=rolename,
            Country=Country,
            usePaidOCR=usePaidOCR,
            usePaidDocExtractor=usePaidDocExtractor,
            used_tokens=used_tokens,
            api_requested=api_requested,
            page_limit_left=page_limit_left,
            pro_page_limit=pro_page_limit,
            standard_page_limit_usage=standard_page_limit_usage,
            standard_page_limit=standard_page_limit,
            promoCodeStr=promoCodeStr 
            )
        return updated_user_details
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Get Single User API
@auth.get('/users/{user_id}')
async def get_single_user(user_id: int = Depends(user_required)):
    return await CAuthController.MSGetSingleUser(user_id=user_id)

@auth.get('/user-data/{user_id}')
async def get_single_user_meta_data(user_id: int = Depends(user_required)):
    """
    Get details, metadata, and list of models for a single user.
    """
    return await CAuthController.MSGetUserMetaData(user_id=user_id)

# delete user
@auth.delete('/users/{user_id}')
async def delete_user(user_id: int, admin: bool = Depends(admin_required)):
    return await CAuthController.MSDeleteUser(user_id=user_id)

# Get All Users API


@auth.get('/users')
async def get_all_users(page: Optional[int] = Query(1, alias="page"), per_page: Optional[int] = Query(10, alias="per_page"), admin: bool = Depends(admin_required), user_id: int = Depends(user_required)):
    """
    Endpoint to retrieve all users with pagination. Requires admin privileges.
    - Query Parameter `page`: Specifies the page number for pagination (default is 1).
    - Query Parameter `per_page`: Specifies the number of users per page (default is 10).
    """
    return await CAuthController.MSGetAllUsers(page=page, per_page=per_page, iUserID=user_id)

@auth.get('/u_metadata/')
async def get_all_users(userFilter = Query(..., description="User Name Filter"), page: Optional[int] = Query(1, alias="page"), per_page: Optional[int] = Query(50, alias="per_page"), admin: bool = Depends(admin_required), user_id: int = Depends(user_required)):
    """
    Endpoint to retrieve all users with pagination. Requires admin privileges.
    - Query Parameter `page`: Specifies the page number for pagination (default is 1).
    - Query Parameter `per_page`: Specifies the number of users per page (default is 10).
    """
    return await CAuthController.MSGetAllUsersMetaData(iUserID=user_id, page=page, per_page=per_page, userFilter = userFilter)

@auth.post('/forget-password')
async def forgot_password(background_tasks: BackgroundTasks, fpr: ForgotPasswordModel):
    # iUserID, myBackgroundTasks: BackgroundTasks, objFpr: ForgotPasswordModel
    return await CAuthController.MSForgotPassword(myBackgroundTasks=background_tasks, objFpr=fpr)


@auth.post('/reset-password')
async def reset_password(request: ResetPassword, token: str = Security(oauth2_scheme)):
    # iUserID, objRequest: ResetPassword, strToken: str
    return await CAuthController.MSResetPassword(objRequest=request, strToken=token)


@auth.post('/update-password')
async def update_password(request: UpdatePassword, iUserID: int = Depends(user_required)):
    # iUserID, objRequest: ResetPassword, strToken: str
    return await CAuthController.MSUpdateUserPassword(user_id=iUserID, objRequest=request)


@auth.post('/send-otp')
async def send_otp(request_body: ForgotPasswordModel, myBackgroundTasks: BackgroundTasks):
    return await CAuthController.sendOTPToEmail(myBackgroundTasks=myBackgroundTasks, request_body=request_body)


@auth.get('/email-exists')
async def email_exists(email):
    return await CAuthController.CheckEmailExists(email=email)

@auth.get('/email-validation')
async def email_exists(email):
    return await CAuthController.MSEmailValidation(email=email)

@auth.post("/register_organization")
async def register_organization_route(
    user_id: int = Form(...),
    company_name: str = Form(...),
    company_url: str = Form(default=""),
    strDesignation:str = Form(...),
    team_strength: str = Form(...),
    invoices_processed_per_day: str = Form(...)
):
    try:
        # Assuming the register_organization function is part of a class named OrganizationController
        # and is already defined as a static method
        result = await CAuthController.register_organization(
            user_id=user_id,
            company_name=company_name,
            company_url=company_url,
            strDesignation=strDesignation,
            team_strength=team_strength,
            invoices_processed_per_day=invoices_processed_per_day
        )
        return result
    except Exception as e:
        
        raise HTTPException(status_code=500, detail="An error occurred during the organization registration process")
