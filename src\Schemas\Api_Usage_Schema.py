
from pydantic import BaseModel
from typing import Optional
from config.constants import Constants

class UserAPIUsageSchema(BaseModel):
    user_id: int
    used_tokens: Optional[int] = 0
    api_requested: Optional[int] = 0
    page_limit_left: Optional[int] = int(Constants.MaxTrialPaidDocExtractionPerUser)
    class Config:
        from_attributes = True

class UserAPITokenUsage(BaseModel):
    user_id: int
    used_tokens: int
    class Config:
        from_attributes = True
        
class UserAPIRequestUsage(BaseModel):
    user_id: int
    api_requested: Optional[int] = 1
    class Config:
        from_attributes = True

