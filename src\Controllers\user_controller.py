# Importing libraries
from ensure import ensure_annotations
from fastapi import HTTPException
import traceback

from src.utilities.helperFunc import APIHelper
from config.db_config import engine as db
from src.Models.models import user_table
from src.Schemas.auth_models import check_user


class CUserController:
    def MCheckUsername(request: check_user):
        """
        Purpose : This method is used to check the availability of a username.

        Inputs  :   (1)     request   :   The request object containing the username (check_user)

                    (2)    m_objLogger   :   Logger object for logging information (CLogger)

        Output  : It returns a success response if the username is available, or an error response if the username is already taken.

        Example : MCheckUsername(request=check_user_instance)
        """
        try:
            # Fetching data from the database
            data = db.execute(user_table.select(
                whereclause=user_table.c.username == request.username)).fetchone()

            # If no data is found, username is available
            if not data:
                return APIHelper.send_success_response(data="translations.AVAILABLE_USERNAME")

            # If data is found, username is already taken
            return APIHelper.send_error_response(errorMessageKey="translations.ALREDY_USERNAME")

        except Exception as e:
            raise HTTPException(
                status_code=500, detail="Error occured while checking for username Please try agian")
