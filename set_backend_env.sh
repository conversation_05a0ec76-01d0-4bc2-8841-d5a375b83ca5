#!/bin/bash

# Manually set the AccuvelocityVersion
# you need to manually change env 1. file nano ~/.bashrc 2.  
chmod +x AccuVelocity/set_backend_env.sh
AccuvelocityVersion="V2"  # Change this to "V2" as needed
export ACCUVELOCITY_APP_VERSION="V2"

if [ "$AccuvelocityVersion" = "V1" ]; then
    echo "set: path /home/<USER>/AccuVelocity/"
    APP_DIR="/home/<USER>/AccuVelocity/"
    APP_CMD="/home/<USER>/AccuVelocity/5_Env/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 10"
elif [ "$AccuvelocityVersion" = "V2" ]; then
    echo "set: path /home/<USER>/development_server/AccuVelocity/"
    APP_DIR="/home/<USER>/development_server/AccuVelocity/"
    APP_CMD="/home/<USER>/development_server/AccuVelocity/5_Env/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 10"
else
    echo "Error: Unknown AccuvelocityVersion value"
    echo "Please provide ACCUVELOCITY_APP_VERSION as V1 or V2 in uppercase letters. No other options are allowed."
    exit 1
fi

# Print the selected variables
echo "ACCUVELOCITY_APP_VERSION is set to: ${ACCUVELOCITY_APP_VERSION}"
echo "APP_DIR is set to: ${APP_DIR}"
echo "APP_CMD is set to: ${APP_CMD}"

# User instruction message
echo "Please ensure ACCUVELOCITY_APP_VERSION is correctly chosen from the options V1 or V2 in uppercase letters."
echo "Run this batch script in a normal command terminal, not in any active environment terminal."

# Change to the application directory
cd ${APP_DIR}

# Execute the application command
exec ${APP_CMD}
