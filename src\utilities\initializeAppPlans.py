
import asyncio
import sys
sys.path.append(".")
from src.utilities.DBHelper import CPaymentHelper
import os
from os.path import join, dirname
from dotenv import load_dotenv
# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")

load_dotenv(dotenv_path)

async def initialize_app_plans():
    try:
        livemode =bool(os.getenv('LIVEMODE') == "true")
        await CPaymentHelper.MSInitializeAppPlans(payment_type="recurring",liveMode=livemode)
        print("MSInitializeAppPlans executed successfully.")
    except Exception as e:
        print("An error occurred while executing MSInitializeAppPlans: %s", str(e))
        raise

if __name__ == "__main__":
    try:
        asyncio.run(initialize_app_plans())
    except Exception as e:
        sys.exit(1)
