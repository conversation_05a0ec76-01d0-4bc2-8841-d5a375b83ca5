import sys
sys.path.append(".")

import imaplib
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from config.db_config import AsyncSessionLocal
from fastapi import HTTPException
from src.Models.models import Logs_EmailProcessing
import inspect

class BaseEmailProcessor(ABC):
    def __init__(self, email_account, provider, password, imap_server, imap_port):
        self.email_account = email_account
        self.provider = provider
        self.password = password
        self.imap_server = imap_server
        self.imap_port = imap_port
        self.mail = None

    @staticmethod
    async def log_to_db(iUserID, strLogType, strLogMessage, bDebug = True):
        """
        Asynchronous logging method to log messages to the database.
        """
        try:
            # Automatically detect the calling class name
            stack = inspect.stack()
            # stack[1] refers to the caller's frame, stack[0] is the current frame
            calling_class = stack[1][0].f_locals.get("self", None)
            class_name = calling_class.__class__.__name__ if calling_class else "UnknownClass"
            
            # Format the log message to include the class name
            formatted_log_message = f"[{class_name}] {strLogMessage}"
            if bDebug:
                print(formatted_log_message)
            async with AsyncSessionLocal() as db:
                objLog = Logs_EmailProcessing(
                    UserId=iUserID,
                    LogType=strLogType,
                    LogMessage=strLogMessage
                )
                db.add(objLog)
                await db.commit()
                await db.refresh(objLog)
                return objLog
        except Exception as e:
            raise HTTPException(
                status_code=500, detail="There was an error adding the logs for email polling."
            )

    async def connect_to_mailbox(self):
        """
        Connect to the mailbox.
        """
        try:
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.mail.login(self.email_account, self.password)
            self.mail.select('inbox')
            # await self.log_to_db(None, 'INFO', f"Connected to mailbox for {self.email_account}.")

            return True
        except Exception as e:
            await self.log_to_db(None, 'ERROR', f"Failed to connect to mailbox for {self.email_account}, Error: {e}")
            return False

    async def ensure_connection(self):
        """
        Ensure that the IMAP connection is active and the mailbox is selected.
        If the connection has dropped, reconnect and re-select the inbox.
        """
        try:
            if not self.mail or self.mail.state != 'SELECTED':
                await self.connect_to_mailbox()  # Reconnect to the mailbox if not connected or not in SELECTED state.
        except Exception as e:
            self.logger.error(f"Error ensuring connection: {e}")
            await self.connect_to_mailbox()

    async def disconnect(self):
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to disconnect from mailbox for {self.email_account}, Error: {e}")

    @abstractmethod
    async def process_email(self, email_id, email_content):
        """
        Abstract method for processing emails. Must be overridden by specific processors.
        """
        pass

    async def fetch_emails(self, days_limit):
        """
        Fetch recent emails within the `days_limit` from today.
        """
        if not await self.connect_to_mailbox():
            await self.log_to_db(None, 'ERROR', "Not connected to mailbox.")
            return []

        try:
            date_limit = (datetime.now() - timedelta(days=days_limit)).strftime('%d-%b-%Y')
            status, messages = self.mail.search(None, f'(SINCE {date_limit})')
            if status != 'OK':
                await self.log_to_db(None, 'ERROR', "Failed to fetch emails.")
                return []

            return messages[0].split()
        except Exception as e:
            await self.log_to_db(None, 'ERROR', f"Error fetching emails: {e}")
            return []
        finally:
            await self.disconnect()

