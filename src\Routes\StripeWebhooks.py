import os
import stripe
import traceback
from fastapi import Request, HTTPException
from fastapi.responses import JSONResponse
from pydantic import BaseModel
from fastapi import APIRouter
from src.Controllers.StripePaymentController import CPaymentApp
from os.path import join, dirname
from dotenv import load_dotenv

# Declaring router
PaymentStripeWebhooks = APIRouter(tags=['StripePaymentAppWebhook'], prefix="/api")

# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

# determine production or development stripe server secret key
livemode = bool(os.getenv('LIVEMODE') == "true")
if livemode:
    stripe.api_key = os.getenv('PAYMENT_INTEGRATION_PROD_SECRET_KEY')
    endpoint_secret = os.getenv('PAYMENT_INTEGRATION_WEBHOOK_PROD_SECRET_KEY') 
else:
    stripe.api_key = os.getenv('PAYMENT_INTEGRATION_DEV_SECRET_KEY')
    endpoint_secret = os.getenv('PAYMENT_INTEGRATION_WEBHOOK_DEV_SECRET_KEY') 

@PaymentStripeWebhooks.post("/stripe_webhook")
async def webhook(request: Request):
    """
    Purpose: Handle incoming Stripe webhook events
    Input: Request object containing the Stripe event payload and headers
    Output: JSONResponse indicating success or failure
    Example: POST /api/stripe_webhook
    """
    from src.Controllers.Logs_Controller import CLogController
    try:
        payload = await request.body()
        sig_header = request.headers.get('stripe-signature')

        try:
            event = stripe.Webhook.construct_event(
                payload, sig_header, endpoint_secret
            )
        except ValueError as e:
            # Invalid payload
            raise HTTPException(status_code=400, detail="Invalid payload")
        except stripe.error.SignatureVerificationError as e:
            # Invalid signature
            raise HTTPException(status_code=400, detail="Invalid signature")

        # Handle the event
        event_type = event['type']
        StripeResponse = event['data']['object']
        # Logging the received event
        await CLogController.MSWriteLog(None, "INFO", f"Received event: {event_type}, StripeResponse : {StripeResponse}")
        
        if event_type == "customer.created":
            await CPaymentApp.MSSetStripeUserOnEvent(event['type'],StripeResponse)
        elif event_type in ['invoice.payment_succeeded'] :
            await CPaymentApp.MSCreateUserSubscription(event['type'],StripeResponse)
        elif event_type in ["subscription_schedule.canceled","customer.subscription.deleted","customer.subscription.paused","subscription_schedule.aborted"]:
            # "invoice.payment_failed" not required 
            await CPaymentApp.MSCancelSubscription(event_type, StripeResponse)

        return JSONResponse(content={"success": True})
    except Exception as e:
        await CLogController.MSWriteLog(None, "Error", f"Error processing webhook: {str(e)}, Received event: {event_type}, StripeResponse: {StripeResponse}")
        await CLogController.MSWriteLog(None, "Debug", f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="An error occurred while processing the webhook")

# stripe listen --forward-to localhost:8000/webhook
# stripe listen --events=invoice.payment_succeeded --forward-to localhost:8000/api/stripe_webhook --live --skip-verify 
# stripe listen --forward-to localhost:8000/api/stripe_webhook --events customer.created,subscription_schedule.completed,invoice.payment_succeeded,subscription_schedule.canceled,customer.subscription.deleted,customer.subscription.paused,subscription_schedule.aborted,charge.failed,charge.succeeded
# whsec_XWyxJ1mQH2VesTDyiFSU3NbUmfD0Z7iM