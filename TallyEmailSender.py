import pandas as pd
import smtplib
from email.mime.multipart import MIME<PERSON><PERSON><PERSON>art
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import traceback
import os
from dotenv import load_dotenv
from pathlib import Path
load_dotenv()


class EmailSender:
    @staticmethod
    def send_email(from_address, password, to_addresses, cc_addresses, subject, html_content, smtp_server, smtp_port, lsAttachmentPath=[]):
        """
        Sends an email with the given parameters.

        :param from_address: Sender's email address
        :param password: Sender's email password
        :param to_addresses: List of recipient email addresses
        :param cc_addresses: List of CC email addresses
        :param subject: Subject of the email
        :param html_content: HTML content of the email
        :param smtp_server: SMTP server address
        :param smtp_port: SMTP server port
        """
        # Create the email message
        message = MIMEMultipart("alternative")
        message["From"] = from_address
        message["To"] = ", ".join(to_addresses)
        message["Cc"] = ", ".join(cc_addresses)
        message["Subject"] = subject

        # Attach the HTML content
        message.attach(MIMEText(html_content, "html"))

        if lsAttachmentPath:
            try:
                for attachment_path in lsAttachmentPath:
                    if os.path.isfile(attachment_path):
                        with open(attachment_path, "rb") as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())
                            encoders.encode_base64(part)
                            part.add_header('Content-Disposition', f'attachment; filename={os.path.basename(attachment_path)}')
                            message.attach(part)
                    else:
                        print(f"Attachment file does not exist: {attachment_path}")
            except Exception as e:
                print(f"Failed to add attachments in the email: {e}")
            
        # Combine all recipients for sending
        all_recipients = to_addresses + cc_addresses

        try:
            # Setup the SMTP server
            with smtplib.SMTP(smtp_server, smtp_port) as server:
                server.starttls()
                server.login(from_address, password)
                server.sendmail(from_address, all_recipients, message.as_string())
            print("Email sent successfully!")
        except Exception as e:
            print(f"Error: {e}")

def SendTallyNotificationEmail(csvReportPath, strReceiverName, strSubject, strMailFrom, lsMailTo, strServer, intPort, strPassword, strTotalPagesProcessedToday, strTotalTimeSavedToday, strTotalPagesProcessedTillNow, strTotalTimeSavedTillNow, htmlTemplatePath=Path(r"resource/TallyEmailTemplate.html"), lsCC=[], lsAttachmentPath=[],strSystemName="-",strExecutiontime="-"):
    try:
        # Read the CSV file
        try:
            data = pd.read_csv(csvReportPath)
        except FileNotFoundError:
            print(f"Error: The file {csvReportPath} was not found.")
            return
        except pd.errors.EmptyDataError:
            print("Error: The CSV file is empty.")
            return
        except Exception as e:
            print(f"Error reading CSV file: {e}")
            return

        # Check if DataFrame has no rows (empty data)
        if data.empty:
            html_table = '<p style="text-align: center; font-weight: bold;">No invoices were found.</p>'
            strTotalPagesProcessedToday = "-"
            strTotalTimeSavedToday = "-"
        else:
            data.loc[data['AVTally Status'] == 'Success', 'AVTally Status'] = '<span class="success">Success</span>'
            data.loc[data['AVTally Status'] == 'Duplicate', 'AVTally Status'] = '<span class="duplicate">Duplicate</span>'
            data.loc[data['AVTally Status'] == 'ValidationError', 'AVTally Status'] = '<span class="validation-error">ValidationError</span>'
            data.loc[data['AVTally Status'] == 'PartialSuccess', 'AVTally Status'] = '<span class="partial-success">PartialSuccess</span>'
            data.loc[data['AVTally Status'] == 'Failure', 'AVTally Status'] = '<span class="failure">Failure</span>'
            data.loc[data['AVTally Status'] == 'Skipped', 'AVTally Status'] = '<span class="skipped">Skipped</span>'

            # Create an HTML table using Pandas
            html_table = data.to_html(index=False, classes='styled-table', border=0, escape=False)

        # Read HTML Template from the File
        try:
            with open(htmlTemplatePath, 'r') as html_file:
                html_template = html_file.read()
        except FileNotFoundError:
            print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
            return
        except Exception as e:
            print(f"Error reading HTML template file: {e}")
            return
        
        system_info=None
        if strSystemName != "-":
            system_info=f" | <b>System User: {strSystemName}</b>"
        else:
            system_info =f" | <b>System User: -</b>"
            
        time_info=None
        if strExecutiontime != "-":
            time_info = f" | <b>Execution Time: {strExecutiontime}</b>"
        else:
            time_info = f" | <b>Execution Time: -</b>"
        
        server_name = os.getenv("SYSTEM_NAME", "-")
        
        # Populate the HTML Template with Dynamic Content
        html_content = html_template.format(
            date=pd.Timestamp.today().strftime('%d/%m/%Y'),
            receiverName = strReceiverName,
            total_pages_processed_today=strTotalPagesProcessedToday,
            total_time_saved_today=strTotalTimeSavedToday,
            total_pages_processed_till_now=strTotalPagesProcessedTillNow,
            total_time_saved_till_now=strTotalTimeSavedTillNow,
            table=html_table,
            system_info=system_info,
            time_info=time_info,
            server_name=server_name
        
        )

        # Send the email
        EmailSender.send_email(
            from_address=strMailFrom,
            password=strPassword,
            to_addresses=lsMailTo,
            cc_addresses=lsCC,
            subject=strSubject,
            html_content=html_content,
            smtp_server=strServer,
            smtp_port=intPort, 
            lsAttachmentPath=lsAttachmentPath
        )
    except Exception as e:
        print(f"Error occur in Send Email Notification", traceback.format_exc())




def SendTallyNotificationEmailBankStatement(
    csvReportPath,
    strReceiverName,
    strSubject,
    strMailFrom,
    lsMailTo,
    strServer,
    intPort,
    strPassword,
    TotalPredictedTransactions,
    strTotalTimeSavedToday,
    strTotalPredictedTransactionsTillNow,
    strTotalTimeSavedTillNow,
    htmlTemplatePath=Path(r"resource/TallyEmailTemplateBankStatement.html"),
    lsCC=[],
    lsAttachmentPath=[],
    strSystemName="-",
    dictSummaryData=None,  # ✅ New parameter
    strExecutiontime = '-'
):
    import pandas as pd

    # Read the CSV file
    try:
        data = pd.read_csv(csvReportPath)
    except FileNotFoundError:
        print(f"Error: The file {csvReportPath} was not found.")
        return
    except pd.errors.EmptyDataError:
        print("Error: The CSV file is empty.")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return

    # Generate main transaction table
    if data.empty:
        html_table = '<p style="text-align: center; font-weight: bold;">No Bank Statement were found.</p>'
        TotalPredictedTransactions = "-"
        strTotalTimeSavedToday = "-"
    else:
        data.loc[data['AV_XML_Status'] == 'Success', 'AV_XML_Status'] = '<span class="success">Success</span>'
        data.loc[data['AV_XML_Status'] == 'Duplicate', 'AV_XML_Status'] = '<span class="duplicate">Duplicate</span>'
        data.loc[data['AV_XML_Status'] == 'ValidationError', 'AV_XML_Status'] = '<span class="validation-error">ValidationError</span>'
        data.loc[data['AV_XML_Status'] == 'PartialSuccess', 'AV_XML_Status'] = '<span class="partial-success">PartialSuccess</span>'
        data.loc[data['AV_XML_Status'] == 'Suspense', 'AV_XML_Status'] = '<span class="failure">Suspense</span>'
        data.loc[data['AV_XML_Status'] == 'Skipped', 'AV_XML_Status'] = '<span class="skipped">Skipped</span>'
        html_table = data.to_html(index=False, classes='styled-table', border=0, escape=False)

    # Prepare summary table from dictSummaryData
    if dictSummaryData:
        # Apply styling to XML Status
        xml_status = dictSummaryData.get('XML Status', '')
        if xml_status == 'Success':
            styled_status = '<span class="success">Success</span>'
        elif xml_status == 'Duplicate':
            styled_status = '<span class="duplicate">Duplicate</span>'
        elif xml_status == 'ValidationError':
            styled_status = '<span class="validation-error">ValidationError</span>'
        elif xml_status == 'PartialSuccess':
            styled_status = '<span class="partial-success">PartialSuccess</span>'
        elif xml_status == 'Suspense':
            styled_status = '<span class="failure">Suspense</span>'
        elif xml_status == 'Skipped':
            styled_status = '<span class="skipped">Skipped</span>'
        else:
            styled_status = xml_status
        
        summary_row = f"""
        <div class="highlight-section">
            <h3>Detailed File Summary:</h3>
            <table class="styled-table">
                <thead>
                    <tr>
                        <th>Customer Name</th>
                        <th>Customer Bank Account Name</th>
                        <th>Current Processing Stats</th>
                        <th>Bank Statement File Name</th>
                        <th>Start Execution Time</th>
                        <th>End Execution Time</th>
                        <th>Total No. of Transactions Found</th>
                        <th>Total No. of Predicted Transactions</th>
                        <th>Total No. of Suspense Transactions</th>
                        <th>Total No. of Duplicated Transactions</th>
                        <th>XML Status</th>
                        <th>AccuVelocity Comments</th>
                    </tr>
                </thead>
                <tbody>
                    <tr>
                        <td>{dictSummaryData.get('CustomerName', '')}</td>
                        <td>{dictSummaryData.get('Customer Bank Account Name', '')}</td>
                        <td>{dictSummaryData.get('Current Processing Stats', '')}</td>
                        <td>{dictSummaryData.get('Bank Statement File Name', '')}</td>
                        <td>{dictSummaryData.get('Start Execution Time', '')}</td>
                        <td>{dictSummaryData.get('End Execution Time', '')}</td>
                        <td>{dictSummaryData.get('Total No. of Transactions Found', '')}</td>
                        <td>{dictSummaryData.get('Total No. of Predicted Transactions', '')}</td>
                        <td>{dictSummaryData.get('Total No. of Suspense Transactions', '')}</td>
                        <td>{dictSummaryData.get('Total No. of Duplicated Transactions', '')}</td>
                        <td>{styled_status}</td>
                        <td>{dictSummaryData.get('AccuVelocity Comments', '')}</td>
                    </tr>
                </tbody>
            </table>
        </div>
        """
    else:
        summary_row = ""

    # Read the HTML template
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return

    system_info = f" | <b>System User: {strSystemName}</b>" if strSystemName != "-" else " | <b>System User: -</b>"
    execution_time = f" | <b>Execution Time: {strExecutiontime}</b>" if strExecutiontime != "-" else " | <b>Execution Time: - </b>"
    
    server_name = os.getenv("SYSTEM_NAME", "-")

    # Populate the HTML Template
    html_content = html_template.format(
        date=pd.Timestamp.today().strftime('%d/%m/%Y'),
        receiverName=strReceiverName,
        Total_Predicted_Transactions=TotalPredictedTransactions,
        total_time_saved_today=strTotalTimeSavedToday,
        total_Predicted_Transactions_till_now=strTotalPredictedTransactionsTillNow,
        total_time_saved_till_now=strTotalTimeSavedTillNow,
        table=summary_row + html_table,  # ✅ Insert new summary table above main table
        system_info=system_info,
        execution_time = execution_time,
        server_name=server_name
    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort,
        lsAttachmentPath=lsAttachmentPath
    )

def SendTallyAckNotificationEmail(strReceiverName, lsMailTo, lsCC=[], strSubject="Mail Received and Invoice Processing in Progress", strMailFrom=os.getenv('MAIL_FROM'), strServer=os.getenv('MAIL_SERVER'), intPort=int(os.getenv('SMTP_PORT')), strPassword=os.getenv('MAIL_PASSWORD'), htmlTemplatePath=Path(r"resource/TallyAckTemplate.html")):
    
    # Read HTML Template from the File
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return
    
    server_name = os.getenv("SYSTEM_NAME", "-")

    # Populate the HTML Template with Dynamic Content
    html_content = html_template.format(
        currentDate=pd.Timestamp.today().strftime('%d/%m/%Y'),
        senderName = strReceiverName,
        server_name=server_name
    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort
    )
    
    
def SendVirusDetectedNotification(strReceiverName, lsMailTo, strInfectedFilePath, lsCC=[], strSubject="Accuvelocity Security Alert: Virus Detected in Your File Submission", strMailFrom=os.getenv('MAIL_FROM'), strServer=os.getenv('MAIL_SERVER'), intPort=int(os.getenv('SMTP_PORT')), strPassword=os.getenv('MAIL_PASSWORD'), htmlTemplatePath=Path(r"resource/TemplateVirusDetected.html")):
    
    # Read HTML Template from the File
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return
    
    server_name = os.getenv("SYSTEM_NAME", "-")

    # Populate the HTML Template with Dynamic Content
    html_content = html_template.format(
        currentDate=pd.Timestamp.today().strftime('%d/%m/%Y'),
        senderName = strReceiverName,
        infectedFilePath=strInfectedFilePath,  
        server_name=server_name      
    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort
    )

    
def SendTallyNotificationEmailPurchaseOrder(csvReportPath, strReceiverName, strSubject, strMailFrom, lsMailTo, strServer, intPort, strPassword, strVoucherType, strTotalTimeSavedToday, strTodayTotalPostedVouchers, htmlTemplatePath=Path(r"resource/TallyEmailTemplatePO.html"), lsCC=[], lsAttachmentPath=[],strSystemName="-",strExecutiontime="-", total_time_saved_till_now="", total_posted_vouchers_till_now =""):
    
    # Read the CSV file
    try:
        data = pd.read_csv(csvReportPath)
    except FileNotFoundError:
        print(f"Error: The file {csvReportPath} was not found.")
        return
    except pd.errors.EmptyDataError:
        print("Error: The CSV file is empty.")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return

    # Check if DataFrame has no rows (empty data)
    if data.empty:
        html_table = '<p style="text-align: center; font-weight: bold;">No Bank Statement were found.</p>'
        strTotalTimeSavedToday = "-"
    else:
        data.loc[data['AV_Status'] == 'Success', 'AV_Status'] = '<span class="success">Success</span>'
        data.loc[data['AV_Status'] == 'Failure', 'AV_Status'] = '<span class="failure">Failure</span>'
        data.loc[data['AV_Status'] == 'Skipped', 'AV_Status'] = '<span class="skipped">Skipped</span>'
        data.loc[data['AV_Status'] == 'Duplicate', 'AV_Status'] = '<span class="duplicate">Duplicate</span>'
        
        # Create an HTML table using Pandas
        html_table = data.to_html(index=False, classes='styled-table', border=0, escape=False)

    # Read HTML Template from the File
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return
    
    
    system_info=None
    if strSystemName != "-":
        system_info=f" | <b>System User: {strSystemName}</b>"
    else:
        system_info =f" | <b>System User: -</b>"
        
    time_info=None
    if strExecutiontime != "-":
        time_info = f" | <b>Execution Time: {strExecutiontime}</b>"
    else:
        time_info = f" | <b>Execution Time: -</b>"
    
    
    
    
    server_name = os.getenv("SYSTEM_NAME", "-")
    
    # Populate the HTML Template with Dynamic Content
    html_content = html_template.format(
        date=pd.Timestamp.today().strftime('%d/%m/%Y'),
        receiverName = strReceiverName,
        voucher_type=strVoucherType,
        total_posted_vouchers=strTodayTotalPostedVouchers, 
        total_time_saved_today=strTotalTimeSavedToday,
        table=html_table,
        system_info=system_info,
        time_info=time_info,
        total_posted_vouchers_till_now=total_posted_vouchers_till_now,
        total_time_saved_till_now=total_time_saved_till_now,
        server_name=server_name


    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort, 
        lsAttachmentPath=lsAttachmentPath
    )

def SendTallyNotificationEmailImprestJournal(csvReportPath, strReceiverName, strSubject, strMailFrom, lsMailTo, strServer, intPort, strPassword, strVoucherType, strTotalTimeSavedToday, strTodayTotalPostedVouchers, htmlTemplatePath=Path(r"resource/TallyEmailTemplateImprest.html"), lsCC=[], lsAttachmentPath=[],strSystemName="-",strExecutiontime="-", total_time_saved_till_now="", total_posted_vouchers_till_now =""):
    
    # Read the CSV file
    try:
        data = pd.read_csv(csvReportPath)
    except FileNotFoundError:
        print(f"Error: The file {csvReportPath} was not found.")
        return
    except pd.errors.EmptyDataError:
        print("Error: The CSV file is empty.")
        return
    except Exception as e:
        print(f"Error reading CSV file: {e}")
        return

    # Check if DataFrame has no rows (empty data)
    if data.empty:
        html_table = '<p style="text-align: center; font-weight: bold;">No Bank Statement were found.</p>'
        strTotalTimeSavedToday = "-"
    else:
        data.loc[data['AV XML Generated Status'] == 'Success', 'AV XML Generated Status'] = '<span class="success">Success</span>'
        data.loc[data['AV XML Generated Status'] == 'Duplicate', 'AV XML Generated Status'] = '<span class="duplicate">Duplicate</span>'
        data.loc[data['AV XML Generated Status'] == 'Failure', 'AV XML Generated Status'] = '<span class="failure">Failure</span>'
        data.loc[data['AV XML Generated Status'] == 'Skipped', 'AV XML Generated Status'] = '<span class="skipped">Skipped</span>'
        data.loc[data['AV XML Generated Status'] == 'ValidationError', 'AV XML Generated Status'] = '<span class="validation-error">ValidationError</span>'
        # Create an HTML table using Pandas
        html_table = data.to_html(index=False, classes='styled-table', border=0, escape=False)

    # Read HTML Template from the File
    try:
        with open(htmlTemplatePath, 'r') as html_file:
            html_template = html_file.read()
    except FileNotFoundError:
        print(f"Error: The HTML template file {htmlTemplatePath} was not found.")
        return
    except Exception as e:
        print(f"Error reading HTML template file: {e}")
        return
    
    
    system_info=None
    if strSystemName != "-":
        system_info=f" | <b>System User: {strSystemName}</b>"
    else:
        system_info =f" | <b>System User: -</b>"
        
    time_info=None
    if strExecutiontime != "-":
        time_info = f" | <b>Execution Time: {strExecutiontime}</b>"
    else:
        time_info = f" | <b>Execution Time: -</b>"
    
    
    
    
    server_name = os.getenv("SYSTEM_NAME", "-")
    
    # Populate the HTML Template with Dynamic Content
    html_content = html_template.format(
        date=pd.Timestamp.today().strftime('%d/%m/%Y'),
        receiverName = strReceiverName,
        voucher_type=strVoucherType,
        total_posted_vouchers=strTodayTotalPostedVouchers, 
        total_time_saved_today=strTotalTimeSavedToday,
        table=html_table,
        system_info=system_info,
        time_info=time_info,
        total_posted_vouchers_till_now=total_posted_vouchers_till_now,
        total_time_saved_till_now=total_time_saved_till_now,
        server_name=server_name

    )

    # Send the email
    EmailSender.send_email(
        from_address=strMailFrom,
        password=strPassword,
        to_addresses=lsMailTo,
        cc_addresses=lsCC,
        subject=strSubject,
        html_content=html_content,
        smtp_server=strServer,
        smtp_port=intPort, 
        lsAttachmentPath=lsAttachmentPath
    )

       
if __name__ == "__main__":
    MAIL_USERNAME=os.getenv('MAIL_USERNAME'),

    #************** For Development Use Only ***************

    # SendTallyNotificationEmail(csvReportPath=r"H:\AI Data\17_ParagTraders\1_simpolo\DailyData\2024_12_23\Daily_Automation_Reports\Report_2024_12_23_Riveredge.csv", 
    #                             strReceiverName="Riveredge",
    #                             strSubject="Tally Invoice Posting Report", 
    #                             strMailFrom=os.getenv('MAIL_FROM'), 
    #                             lsMailTo=["<EMAIL>","<EMAIL>","<EMAIL>"], 
    #                             strServer=os.getenv('MAIL_SERVER'), 
    #                             intPort=int(os.getenv('SMTP_PORT')), 
    #                             strPassword=os.getenv('MAIL_PASSWORD'), 
    #                             htmlTemplatePath=r"resource\TallyEmailTemplate.html", 
    #                             lsCC=[],
    #                             strTotalPagesProcessedToday="1",
    #                             strTotalTimeSavedToday="2.5 Minutes",
    #                             strTotalPagesProcessedTillNow="55",
    #                             strTotalTimeSavedTillNow="20 Hours",
    #                             lsAttachmentPath=[]
    #                             )

    #************** For Email Received Acknowledgement Email Development Use Only ***************

    # SendTallyAckNotificationEmail(      strReceiverName="Riveredge", 
    #                                     lsMailTo=["<EMAIL>"],
    #                                     lsCC=[]
    #                             )

    # *************** For PPRODUCTION USE ONLY *************** 
    #   
    # SendTallyNotificationEmail(csvReportPath=r"H:\AI Data\17_ParagTraders\1_simpolo\DailyData\2024_11_20\Daily_Automation_Reports\Report_2024_11_20_email_id_b'66'.csv", 
    #                             strReceiverName="Parag Traders",
    #                             strSubject="Tally Invoice Posting Report", 
    #                             strMailFrom=os.getenv('MAIL_FROM'), 
    #                             lsMailTo=[
    #                                         "<EMAIL>",
    #                                         "<EMAIL>"
    #                                     ], 
    #                             strServer=os.getenv('MAIL_SERVER'), 
    #                             intPort=int(os.getenv('SMTP_PORT')), 
    #                             strPassword=os.getenv('MAIL_PASSWORD'), 
    #                             htmlTemplatePath=r"resource\TallyEmailTemplate.html", 
    #                             lsCC=[
#                                       "<EMAIL>",
#                                       "<EMAIL>"
    #                               ])

    EmailSender.send_email(
                            from_address=os.getenv('MAIL_FROM'),
                            password=os.getenv('MAIL_PASSWORD'),
                            to_addresses=['<EMAIL>'],
                            cc_addresses=[],
                            subject="Accuvelocty Document Processing For Airen Group",
                            html_content="This is a simple message containing infrmation",
                            smtp_server=os.getenv('MAIL_SERVER'),
                            smtp_port=int(os.getenv('SMTP_PORT'))
                        )