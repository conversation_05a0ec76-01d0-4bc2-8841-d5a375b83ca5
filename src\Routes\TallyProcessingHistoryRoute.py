from config.db_config import AsyncSessionLocal
from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy import and_
from pydantic import BaseModel
from typing import List, Optional
from datetime import date, datetime
import zipfile
import io
import json
import os
import enum
import logging
from src.Models.models import AVRequestDetail

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

app = FastAPI(
    title="Document History API",
    description="API for retrieving and downloading document history, with filtering and pagination. Restricted for user_id=11.",
    version="1.0.0"
)

# Dependency for async database session
async def get_db():
    async with AsyncSessionLocal() as db:
        try:
            yield db
        finally:
            await db.close()

# Enums
class VoucherType(str, enum.Enum):
    """Valid voucher types for document filtering."""
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"

class AVXMLGeneratedStatus(str, enum.Enum):
    """Valid statuses for XML generation process."""
    Success = "Success"
    Skipped = "Skipped"
    PartialSuccess = "PartialSuccess"
    Duplicate = "Duplicate"
    NOT_APPLICABLE = "NOT_APPLICABLE"
    ValidationError = "ValidationError"

class TallyXMLImportStatus(str, enum.Enum):
    """Valid statuses for Tally XML import (SUCCESS for status code 200, FAILED for 999 or other)."""
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

# Pydantic models for request/response
class DocumentFilter(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    voucher_type: Optional[VoucherType] = None
    status: Optional[AVXMLGeneratedStatus] = None
    document_name: Optional[str] = None
    tally_status: Optional[TallyXMLImportStatus] = None

    class Config:
        use_enum_values = True

class DocumentResponse(BaseModel):
    ID: int
    strVoucherType: VoucherType
    AVDocumentPtocessedStatus: AVXMLGeneratedStatus
    TallyXMLImportStatus: TallyXMLImportStatus
    ReqDocName: Optional[str]
    ReqDocType: Optional[str]
    CReqGeneratedTimeAt: datetime
    strCustomerName: str
    UserName: str
    has_xml: bool

class PaginatedDocumentResponse(BaseModel):
    documents: List[DocumentResponse]
    total_records: int
    total_pages: int
    current_page: int

# Helper function to extract file name from NetworkLocation
def get_file_name(network_location: Optional[any], fallback: str) -> str:
    """Extract file name from NetworkLocation (JSON string or dict) or return fallback."""
    if not network_location:
        logger.debug(f"No network_location provided, using fallback: {fallback}")
        return fallback
    try:
        if isinstance(network_location, dict):
            data = network_location
        else:
            data = json.loads(network_location)
        if isinstance(data, dict) and "XMLFilePath" in data:
            if isinstance(data["XMLFilePath"], list) and len(data["XMLFilePath"]) > 0:
                # Extract file name from the path
                logger.debug(f"Extracting file name from XMLFilePath: {data['XMLFilePath'][0]}")
                file_name = os.path.basename(data["XMLFilePath"][0])
                return file_name
        logger.debug(f"No 'XMLFilePath' in network_location, using fallback: {fallback}")
        return fallback
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"Error parsing network_location: {str(e)}, using fallback: {fallback}")
        return fallback

# API to fetch document history with pagination
@app.post(
    "/api/documents/history",
    response_model=PaginatedDocumentResponse,
    summary="Fetch paginated document history",
    description="Retrieve a paginated list of documents for a user, filtered by date range, voucher type, XML generation status, document name, and Tally import status. Results are sorted by creation time (descending). Restricted for user_id=11.",
    response_description="A paginated response containing document details, total records, total pages, and current page."
)
async def get_document_history(
    user_id: int,
    filters: DocumentFilter,
    page: int = 1,
    page_size: int = 10,
    db: AsyncSession = Depends(get_db)
):
    if user_id == 11:
        raise HTTPException(status_code=403, detail="This feature is not available for your account.")

    if page < 1:
        raise HTTPException(status_code=400, detail="Page must be greater than 0")
    if page_size < 1 or page_size > 100:
        raise HTTPException(status_code=400, detail="Page size must be between 1 and 100")

    # Build count query
    count_query = select(AVRequestDetail).where(AVRequestDetail.User_UID == user_id)
    if filters.start_date:
        count_query = count_query.where(AVRequestDetail.RecievedDate >= filters.start_date)
    if filters.end_date:
        count_query = count_query.where(AVRequestDetail.RecievedDate <= filters.end_date)
    if filters.voucher_type:
        count_query = count_query.where(AVRequestDetail.strVoucherType == filters.voucher_type)
    if filters.status:
        count_query = count_query.where(AVRequestDetail.AVXMLGeneratedStatus == filters.status)
    if filters.document_name:
        count_query = count_query.where(AVRequestDetail.ReqDocName.ilike(f"%{filters.document_name}%"))
    if filters.tally_status:
        status_code = "200" if filters.tally_status == TallyXMLImportStatus.SUCCESS else "999"
        count_query = count_query.where(AVRequestDetail.ClientImportedXMLStatusCode == status_code)

    count_result = await db.execute(count_query)
    total_records = len(count_result.scalars().all())
    total_pages = (total_records + page_size - 1) // page_size

    # Build data query
    data_query = select(AVRequestDetail).where(AVRequestDetail.User_UID == user_id)
    if filters.start_date:
        data_query = data_query.where(AVRequestDetail.RecievedDate >= filters.start_date)
    if filters.end_date:
        data_query = data_query.where(AVRequestDetail.RecievedDate <= filters.end_date)
    if filters.voucher_type:
        data_query = data_query.where(AVRequestDetail.strVoucherType == filters.voucher_type)
    if filters.status:
        data_query = data_query.where(AVRequestDetail.AVXMLGeneratedStatus == filters.status)
    if filters.document_name:
        data_query = data_query.where(AVRequestDetail.ReqDocName.ilike(f"%{filters.document_name}%"))
    if filters.tally_status:
        status_code = "200" if filters.tally_status == TallyXMLImportStatus.SUCCESS else "999"
        data_query = data_query.where(AVRequestDetail.ClientImportedXMLStatusCode == status_code)

    data_query = data_query.order_by(AVRequestDetail.CReqGeneratedTimeAt.desc())
    data_query = data_query.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(data_query)
    records = result.scalars().all()

    response = []
    for record in records:
        response.append(DocumentResponse(
            ID=record.ID,
            strVoucherType=record.strVoucherType,
            AVDocumentPtocessedStatus=record.AVXMLGeneratedStatus or AVXMLGeneratedStatus.NOT_APPLICABLE,
            TallyXMLImportStatus=TallyXMLImportStatus.SUCCESS if str(record.ClientImportedXMLStatusCode) == "200" else TallyXMLImportStatus.FAILED,
            ReqDocName=record.ReqDocName or f"Request_{record.ID}",
            ReqDocType=record.ReqDocType or "Request",
            CReqGeneratedTimeAt=record.CReqGeneratedTimeAt,
            strCustomerName=record.strCustomerName or f"Customer_{record.ID}",
            UserName=record.strSystemUserName or "Unknown",
            has_xml=bool(record.strXMLResponse)
        ))

    logger.info(f"Fetched {len(response)} documents for user_id={user_id}, page={page}")
    return PaginatedDocumentResponse(
        documents=response,
        total_records=total_records,
        total_pages=total_pages,
        current_page=page
    )


# API to download XML files as ZIP
@app.post(
    "/api/documents/download",
    response_model=None,
    summary="Download XML files as ZIP",
    description="Download XML files for the specified request IDs as a ZIP archive. Only includes AVRequestDetail XMLs. Restricted for user_id=11.",
    response_description="A ZIP file containing XML documents, named using NetworkLocation or fallback."
)
async def download_xml_files(
    user_id: int,
    request_ids: List[int],
    db: AsyncSession = Depends(get_db)
):
    if user_id == 11:
        raise HTTPException(status_code=403, detail="This feature is not available for your account.")

    if not request_ids:
        raise HTTPException(status_code=400, detail="At least one request_id must be provided")

    try:
        buffer = io.BytesIO()
        with zipfile.ZipFile(buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
            query = select(AVRequestDetail).where(
                and_(
                    AVRequestDetail.User_UID == user_id,
                    AVRequestDetail.ID.in_(request_ids)
                )
            )
            result = await db.execute(query)
            records = result.scalars().all()

            if not records:
                logger.warning(f"No documents found for user_id={user_id}, request_ids={request_ids}")
                raise HTTPException(status_code=404, detail="No documents found")

            for record in records:
                if record.strXMLResponse:
                    file_name = get_file_name(record.NetworkLocation, f"AVRequestDetail_{record.ID}.xml")

                    zip_file.writestr(file_name, record.strXMLResponse)
                    logger.debug(f"Added XML for ID={record.ID} as {file_name}")

        if not zip_file.namelist():
            logger.warning(f"No valid XMLs found for user_id={user_id}, request_ids={request_ids}")
            raise HTTPException(status_code=404, detail="No valid XMLs found")

        buffer.seek(0)
        logger.info(f"Generated ZIP with {len(zip_file.namelist())} files for user_id={user_id}")
        return StreamingResponse(
            buffer,
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=documents_{user_id}.zip"}
        )
    except Exception as e:
        logger.error(f"Error generating ZIP for user_id={user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")
