# To run this file as main file (directly run this file)
import json
import math
import sys
sys.path.append(".")
from datetime import datetime
import os
from fastapi import HTTPException
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.PODetailsController import CAVPOProcessingDetailsService
from src.Controllers.GRNDetailsController import CAVGRNProcessingDetailsService
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.Controllers.GoogleDriveController import GoogleDriveService

import requests
import pandas as pd
import numpy as np
import traceback
from src.Controllers.Tally_Controller import CTallyController
from src.utilities.helperFunc import <PERSON>xcelHelper,DateHelper
from src.Schemas.Tally_XML_Schema import BillAllocationSchema, CompanyInfoSchema, ConsigneeDetailsSchema, PartyDetailsSchema, TallyPOVoucherSchema,TallyGRNVoucherSchema, InventoryEntrySchema, LedgerEntrySchema, BatchAllocationSchema, AccountingAllocationSchema, RateDetailSchema, CategoryAllocationSchema, CostCenterAllocationSchema, TallyPurchaseInventoryVoucherSchema
from src.utilities.helperFunc import CFileHandler, CJSONFileReader
from datetime import timedelta
import asyncio
from src.Controllers.Logs_Controller import CLogController
from src.Models.models import VoucherType
# from PWIMatchingOpenAI.matchRunner import CLedgerMatcher
# from PWIMatchingOpenAI import helperFunctionsInAsync
import re
from enum import Enum
from src.Controllers.GeneralizePurchaseWithInventory import CGeneralizePurchaseWithInventory
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode
from src.utilities.PurchaseOrderUtilities import CPOUtilities
from src.utilities.GRNUtilities import CGRNUtilities
from src.utilities.PathHandler import dictProjectPaths

class VersionType(str, Enum):
    V1 = "V1"
    V2 = "V2"
 
class CAbhinavInfrabuild:
    _miTimeSavedPerPOItem = 30  # In seconds
    _mstrCompanyName =  "Abhinav Infrabuild Pvt.Ltd.(24-26)"
    _mStrStoragePathProduction = dictProjectPaths.get("STORAGE_PATH_PRODUCTION", r"H:/AI Data/DailyData/AbhinavInfrabuild")
    _mStrStoragePathDevelopement = dictProjectPaths.get("STORAGE_PATH_DEVELOPMENT", r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild")
    _mStrVendorDetailsPath = dictProjectPaths.get("Abhinav_Vendor_Path", r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Resources/PartyDetails.json")
    
    # NOTE: This is not being Used
    _mstrMappingExcelPath = r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Mappings/Tally_ERP_MappingsV9.xlsx"

    _mStrGSTIN = "23**********1ZY"

    @staticmethod
    def MSCalculateTimeSavedForPO(po_list: list, per_entry_seconds: int) -> dict:
        """
        Calculate the total time saved based on the number of items in each PO.
        
        Args:
            po_list (list): List of dictionaries, each containing an 'Items' key with a list of entries.
            per_entry_seconds (int): Time taken to process a single item in seconds.
        
        Returns:
            dict: Time saved in hours, minutes, and seconds.
        """
        total_seconds_saved = sum(len(po.get("Items", [])) * per_entry_seconds for po in po_list)

        # Convert to hours, minutes, and seconds
        time_saved = timedelta(seconds=total_seconds_saved)
        hours, remainder = divmod(time_saved.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)

        return {
            "hours": hours,
            "minutes": minutes,
            "seconds": seconds
        }


    @staticmethod 
    async def MSGetAllPurchaseOrderXML(iUserID, iRequestID, strExcelFilePath, dictLsClientDocMetaData, bDeveloperMode=False, bDownloadERP = False):
        try:
            # TO GET THE LIST OF Purchase Orders --------------------------------------
            dfPurchaseOrders = CExcelHelper.read_file(strExcelFilePath)

            try:
                await CAVRequestDetail.MSUpdateRecordByClientREQID(RequestID=iRequestID, FileContent=dfPurchaseOrders.to_json(orient="records"))
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                print(traceback.print_exc())
                
            # Group by 'PO No.'
            po_groups = dfPurchaseOrders.groupby('PO NO.')

            # Additional UDF Related Information Fetching
            lsUDFData = None
            if dictLsClientDocMetaData is None or not dictLsClientDocMetaData:
                dictLsClientDocMetaData = {}
            try:
                for key, value in dictLsClientDocMetaData.items():
                    # Remove file extension from the key and the attachment file name
                    key_without_ext = os.path.splitext(key.lower())[0]
                    attachment_without_ext = os.path.splitext(value[0]['stFullpath'].lower())[0]
                    
                    if key_without_ext in attachment_without_ext:
                        lsUDFData = value
            except Exception as e:
                print("Error occur - ", e)
                pass  # Silently execute remaining code
            
            if lsUDFData is None:
                lsUDFData = [{}]

            # Create po_list
            po_list = []
            for po_no, group in po_groups:
                po_data = {
                    'PO No.': po_no,
                    'PO Date': group['PO DATE'].iloc[0],
                    'Vendor': group['VENDOR'].iloc[0],
                    'Items': group[['ITEM', 'QTY', 'UNIT', 'WEIGHT', 'UNIT RATE', 'ITEM AMOUNT', 'DESC', 'NET AMT', 'GST CAL', 'TECH DESC', 'CGST', 'SGST', 'IGST']].to_dict('records')
                }
                po_list.append(po_data)

            # Load ledger and mapping dictionaries from Excel
            # Mapping_Excel_path = CAbhinavInfrabuild._mstrMappingExcelPath
            try:
                drive_service = GoogleDriveService()
                df_mapping = drive_service.handle_file(bisMultipleSheet=True, download = bDownloadERP)
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"There was a problem in getting Item ERP Matching file")
                raise Exception("There was a problem in getting Item ERP Matching file")
            sheets = ["ITEM MAPPING", "ledger sheet"]
            mapping_df, ledger_df = df_mapping[sheets[0]],df_mapping[sheets[1]]
            
            # mapping_df, ledger_df = CExcelHelper.read_excel_sheets(Mapping_Excel_path, sheets)

            # Normalize ledger_df for case-insensitive and space-ignoring matching
            ledger_df['ERP'] = ledger_df['ERP'].astype(str).str.strip()
            ledger_df['Tally'] = ledger_df['Tally'].astype(str).str.strip()
            ledger_df['ERP_normalized'] = ledger_df['ERP'].str.lower().str.strip()
            ledger_dict = ledger_df.set_index('ERP_normalized')['Tally'].to_dict()

            # Normalize mapping_df for case-insensitive and space-ignoring matching
            mapping_df['ERP Item Name '] = mapping_df['ERP Item Name '].astype(str).str.strip()
            mapping_df['Tally Items'] = mapping_df['Tally Items'].astype(str).str.strip()
            
            mapping_df['Tally Unit'] = mapping_df['Tally Unit'].astype(str).str.strip()
            mapping_df['ERP Unit'] = mapping_df['ERP Unit'].astype(str).str.strip()

            mapping_df['ERP Item Name_normalized'] = mapping_df['ERP Item Name '].str.lower().str.strip()
            
            # Check for duplicates
            duplicate_rows = mapping_df[mapping_df['ERP Item Name_normalized'].duplicated(keep=False)]
            if not duplicate_rows.empty:
                duplicate_items = duplicate_rows['ERP Item Name '].unique()
                duplicate_list_str = "\n".join(f"- {item}" for item in duplicate_items)
                raise HTTPException(
                    status_code=500,
                    detail=f"Duplicate ERP Item Names found in mapping Excel. Please update the file to remove duplicates:\n{duplicate_list_str}"
                )
                
            mapping_dict = mapping_df.set_index('ERP Item Name_normalized')[['Tally Unit', "ERP Unit", 'Tally Items']].to_dict(orient='index')

            lsPOProcessingDetails = []
            for i, dictPODetails in enumerate(po_list):
                strXmlData = None
                dictPOProcessingDetail = {
                    "XMLFilePath": "-",
                    "SR_No.":i+1,
                    "PO_No.": dictPODetails.get("PO No.", "-"),
                    "PO_Date": dictPODetails.get("PO Date", "-"),
                    "PO_Vendor_Name": dictPODetails.get("Vendor", "-"),
                    "Items": dictPODetails.get("Items", "-"),
                    "PO_Total_Items": len(dictPODetails.get("Items", "-")),
                    "PO_Items_Added_In_Tally": "-",
                    "PO_Skipped_Items": "-",
                    "Time_Saved": "0 Seconds",
                    "Time_Statistics": {"hours": 0, "minutes": 0, "seconds": 0},
                    "AV_Status": "Skipped",
                    "AV_Comments": "-"
                }

                try:
                    # Normalize and update vendor name using ledger_dict
                    vendor = dictPODetails.get("Vendor")
                    if vendor is None or (isinstance(vendor, float) and math.isnan(vendor)) or (isinstance(vendor, str) and not vendor.strip()) or (isinstance(vendor, str) and vendor == 'nan'):
                        vendor = "AIPL Head Office (Godown Main)"  # Fallback if vendor is missing
                    else:
                        vendor = str(vendor).strip()

                    vendor_normalized = vendor.lower()
                    if vendor_normalized in ledger_dict:
                        vendor = ledger_dict.get(vendor_normalized)
                        if not pd.isna(vendor) and str(vendor).strip().lower() != 'nan':
                                vendor = vendor

                    dictPODetails["Vendor"] = vendor
                    dictPOProcessingDetail["PO_Vendor_Name"] = vendor

                    # Normalize and update item names and units using mapping_dict
                    for item in dictPODetails["Items"]:
                        item["ERP_ITEM"] = item["ITEM"]
                        item_name = item["ITEM"]
                        item_name_normalized = str(item_name).lower().strip()
                        value = mapping_dict.get(item_name_normalized, {}).get('Tally Items')
                        isValidTallyItem = (
                                    value is not None and
                                    not pd.isna(value) and
                                    str(value).strip().lower() != 'nan'
                                    )
                        if isValidTallyItem:
                            item["ITEM"] = mapping_dict.get(item_name_normalized, {}).get('Tally Items')
                            strTallyUnit = mapping_dict.get(item_name_normalized, {}).get('Tally Unit')
                            strERPUnit = mapping_dict.get(item_name_normalized, {}).get('ERP Unit')
                            
                            if not pd.isna(strTallyUnit) and strTallyUnit != 'nan' and strTallyUnit != "":
                                item["UNIT"] = strTallyUnit
                            else:
                                item["UNIT"] = strERPUnit

                    try:
                        if not bDeveloperMode:
                            # Check for duplicates
                            bIsDuplicate = await CAVPOProcessingDetailsService.MSIsDuplicatePO(UserID=iUserID, PONumber=dictPODetails.get("PO No."))
                            if bIsDuplicate:
                                dictPOProcessingDetail["AV_Status"] = "Duplicate"
                                dictPOProcessingDetail["AV_Comments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                                lsPOProcessingDetails.append(dictPOProcessingDetail)
                                await CAVPOProcessingDetailsService.MSUpdatePOProcessingDetails(UserID=iUserID, RequestID=iRequestID, PONumber=dictPOProcessingDetail.get("PO_No."), PODetails=dictPOProcessingDetail, AVStatus=dictPOProcessingDetail.get("AV_Status"), ErrorMessage=dictPOProcessingDetail.get("AV_Comments"))

                                continue

                        strXmlData = CAbhinavInfrabuild.MSGetTallyXML(dictPODetails, lsUdfData=lsUDFData)

                        # Save the XML to file
                        if bDeveloperMode:
                            strDownloadDirPath = CAbhinavInfrabuild._mStrStoragePathDevelopement
                        else:
                            strDownloadDirPath = CAbhinavInfrabuild._mStrStoragePathProduction
                            
                        today_date = datetime.today().strftime('%Y_%m_%d')
                        strDownloadDirPath = os.path.join(strDownloadDirPath, today_date, "PO")
                        os.makedirs(strDownloadDirPath, exist_ok=True)

                        strUploadedDocumentName = os.path.splitext(dictPODetails.get('PO No.', ''))[0]
                        strXMLFileName = f"REQID_{iRequestID}_UID_{iUserID}_PONUM_{dictPOProcessingDetail.get('PO_No.')}.xml"
                        strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                        bIsFileWritten = CFileHandler.MSWriteFile(
                            strFilePath=strTodaysXmlFilePath,
                            fileContent=strXmlData,
                            strWriteMode="w",
                            strEncoding="utf-8"
                        )
                        if bIsFileWritten:
                            # !!!!!!!!!!!!!!!!!!   UNCOMMENT BELOW CODE TO DIRECTLY EPORT XML TO TALLY   
                            # CAbhinavInfrabuild.MSExportTallyData(strTodaysXmlFilePath, url="http://192.168.68.54:10101/")
                            # !!!! !!! !!!!! !!! !!!! !!!!!  !!!! !!! !!!! !!!! !!! !!! !!!! !!!!
                            
                            # Total Amount Details -------------------------------------
                            fTotalAmount = sum(float(item.get("ITEM AMOUNT", 0)) for item in dictPODetails.get("Items", []))
                            fTotalAmount += float(dictPODetails.get("Items", [])[0].get("GST CAL", 0))    # Add GST amount to the total amount
                            
                            if fTotalAmount <= 0:
                                dictPOProcessingDetail["AV_Comments"] = "The total amount is zero. Please review the purchase order."
            
                            dictPOProcessingDetail["XMLFilePath"] = strTodaysXmlFilePath
                            dictPOProcessingDetail["PO_Items_Added_In_Tally"] = len(dictPODetails.get("Items", []))
                            dictPOProcessingDetail["AV_Status"] = "Success"

                            # Calculate Time Saved
                            dictTimeSavedInfo = CAbhinavInfrabuild.MSCalculateTimeSavedForPO(
                                po_list=[dictPODetails],
                                per_entry_seconds=CAbhinavInfrabuild._miTimeSavedPerPOItem
                            )
                            dictPOProcessingDetail["Time_Statistics"] = dictTimeSavedInfo
                            dictPOProcessingDetail["Time_Saved"] = (
                                f"{dictTimeSavedInfo['hours']} hours, "
                                f"{dictTimeSavedInfo['minutes']} minutes, "
                                f"{dictTimeSavedInfo['seconds']} seconds."
                            )


                    except HTTPException as he:
                        dictPOProcessingDetail["PO_Skipped_Items"] = len(dictPODetails.get("Items", []))
                        dictPOProcessingDetail["AV_Comments"] = str(he.detail)
                        dictPOProcessingDetail["AV_Status"] = "Skipped"

                        await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                    except Exception as e:
                        dictPOProcessingDetail["PO_Skipped_Items"] = len(dictPODetails.get("Items", []))
                        dictPOProcessingDetail["AV_Comments"] = "Could not enter the PO in Tally, please enter manually."
                        dictPOProcessingDetail["AV_Status"] = "Skipped"

                        
                        await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")


                        # import traceback
                        # print(traceback.print_exc())
                        # print(f"Failed to create xml for PO: {dictPODetails}")
                    
                except Exception as e:
                    dictPOProcessingDetail["PO_Skipped_Items"] = len(dictPODetails.get("Items", []))
                    dictPOProcessingDetail["AV_Comments"] = "Could not enter the PO in Tally, please enter manually."
                    dictPOProcessingDetail["AV_Status"] = "Skipped"

                    
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                
                lsPOProcessingDetails.append(dictPOProcessingDetail)
                await CAVPOProcessingDetailsService.MSUpdatePOProcessingDetails(UserID=iUserID, RequestID=iRequestID, PONumber=dictPOProcessingDetail.get("PO_No."), PODetails=dictPOProcessingDetail, AVStatus=dictPOProcessingDetail.get("AV_Status"), ErrorMessage=dictPOProcessingDetail.get("AV_Comments"), GeneratedXML=strXmlData)
                    
            return lsPOProcessingDetails
    
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print(traceback.print_exc())


    @staticmethod
    def MSExportTallyData(strRequestXMLPath, strResponseXMLPath="response.xml", url="http://0.0.0.0:9000/"):
        """
        Reads an XML file and sends it to the specified Tally server URL.
        Saves the server response to an XML file.

        Args:
        strRequestXMLPath (str): The path to the XML file to send.
        url (str): The Tally server URL.
        strResponseXMLPath (str): The path where the response XML will be saved.

        Returns:
        str: Confirmation message or error details.
        """
        try:
            # Read XML content from file
            with open(strRequestXMLPath, "r", encoding="utf-8") as file:
                xml_data = file.read()

            # Define headers
            headers = {
                "Content-Type": "text/xml",
                "Cache-Control": "no-cache"
            }

            # Send POST request
            response = requests.post(url, headers=headers, data=xml_data)

            # Save the response to an XML file
            with open(strResponseXMLPath, "w", encoding="utf-8") as file:
                file.write(response.text)

            return f"Response saved to {strResponseXMLPath}"

        except FileNotFoundError:
            return "Error: The specified file was not found."
        except requests.exceptions.RequestException as e:
            return f"Error: Unable to send data to the server. {str(e)}"
        except IOError as e:
            return f"Error: Unable to save the response file. {str(e)}"
        except Exception as GenError:
            return f"Failed to Export the data,  error: {str(GenError)}"
    
    @staticmethod
    def MSFetchVendorInfo(lsVendorInfo, strVendorName):
        """
        Fetch vendor details by intelligently matching the given vendor name.

        Args:
        lsVendorInfo (list): List of vendor dictionaries.
        strVendorName (str): The vendor name to search for.

        Returns:
        dict: Matching vendor details or an empty dictionary if not found.
        """
        try:
            # Normalize the vendor name (remove leading/trailing spaces, convert to lowercase)
            vendor_name = strVendorName.strip().lower()
            
            for vendor in lsVendorInfo:
                # Normalize the vendor's "Name" field
                if "Name" in vendor and vendor["Name"]:
                    normalized_name = vendor["Name"].strip().lower()
                    if normalized_name == vendor_name:
                        return vendor
                    
                # Check in "MailingNames" and "LanguageNames"
                if "MailingNames" in vendor and vendor["MailingNames"]:
                    if any(name.strip().lower() == vendor_name for name in vendor["MailingNames"]):
                        return vendor
                    
                if "LanguageNames" in vendor and vendor["LanguageNames"]:
                    if any(name.strip().lower() == vendor_name for name in vendor["LanguageNames"]):
                        return vendor
            return {}
        except Exception as e:
            raise ValueError(f"Unable to fetch the vendor details for vendor name '{strVendorName}'")
        

    @staticmethod
    def MSGetTallyXML(dictPODetails, lsUdfData=[{}]):
        objSGSTLedger = None
        objCGSTLedger = None
        objIGSTLedger = None
        
        strVendorName = dictPODetails.get("Vendor")

        if "cash" in strVendorName.lower():
            strVendorName = "CASH"
        
        # Fetch all the Party Details from the resorce file
        lsPartyDetails = CJSONFileReader.read_json_file(CAbhinavInfrabuild._mStrVendorDetailsPath)

        dictPartyDetails = CAbhinavInfrabuild.MSFetchVendorInfo(lsVendorInfo=lsPartyDetails, strVendorName=strVendorName)
        company = CompanyInfoSchema(company_name=CAbhinavInfrabuild._mstrCompanyName,
                                    gst_in=CAbhinavInfrabuild._mStrGSTIN,
                                    country_name="India"
                                    )

        seller = PartyDetailsSchema(
            party_name=strVendorName,
            address_list=dictPartyDetails.get("Addresses", []),
            gst_in=dictPartyDetails.get("GSTIN", ""),
            state_name=dictPartyDetails.get("PriorStateName", ""),
            country_name=dictPartyDetails.get("CountryOfResidence", "")
        )

        buyer = PartyDetailsSchema(
            party_name="Abhinav Infrabuild Pvt.Ltd.(24-26)",
            address_list=[
                "207-208 Industry House,A.B.Road Indore(M.P.)",
                "Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)",
                "E-Mail : <EMAIL>"
            ],
            gst_in=CAbhinavInfrabuild._mStrGSTIN,
            state_name="Madhya Pradesh",
            country_name="India",
            pin_code="452001"
        )
        
        lsInventoryEntries = CAbhinavInfrabuild.MSGetInventoryEntries(dictPODetails)
        strCostCenter = CAbhinavInfrabuild.MSGetCostCenter(dictPODetails)
        # PartyLedger Details -------------------------------------
        fTotalAmount = sum(float(item.get("ITEM AMOUNT", 0)) for item in dictPODetails.get("Items", []))
        fTotalAmount += float(dictPODetails.get("Items", [])[0].get("GST CAL", 0))    # Add GST amount to the total amount
        
        objCGSTLedger = None
        objCGSTLedger= None
        objIGSTLedger = None
        # Code to check if value of IGST is not zero
        fIGSTAmount = dictPODetails.get('Items',[])[0].get('IGST',0)
        
        objPartyLedger = LedgerEntrySchema(
                                    ledger_name=strVendorName,
                                    gst_class="Not Applicable",
                                    is_deemed_positive=False,
                                    amount=fTotalAmount,
                                    category_allocations=[
                                                            CategoryAllocationSchema(
                                                                    category="Primary Cost Category",
                                                                    is_deemed_positive=False,
                                                                    cost_center_allocations=[
                                                                                                CostCenterAllocationSchema(
                                                                                                        name= strCostCenter,
                                                                                                        amount=fTotalAmount
                                                                                                    )
                                                                                            ], 
                                                                )
                                                        ]
                                        )
        
        # GST Ledger Details ------------------------------------------------------
        if fIGSTAmount:
            objIGSTLedger = LedgerEntrySchema(
                                        ledger_name="IGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-fIGSTAmount,
                                        category_allocations=[
                                                                CategoryAllocationSchema(
                                                                        category="Primary Cost Category",
                                                                        is_deemed_positive=True,
                                                                        cost_center_allocations=[
                                                                                                    CostCenterAllocationSchema(
                                                                                                            name= strCostCenter ,
                                                                                                            amount=-fIGSTAmount
                                                                                                        )
                                                                                                ], 
                                                            )
                                                            ]
                                            )

        else:
            fCGSTAmount = float(dictPODetails.get("Items")[0].get("CGST")) 
            fSGSTAmount = float(dictPODetails.get("Items")[0].get("SGST"))   
            objCGSTLedger = LedgerEntrySchema(
                                        ledger_name="CGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-fCGSTAmount,
                                        category_allocations=[
                                                                CategoryAllocationSchema(
                                                                        category="Primary Cost Category",
                                                                        is_deemed_positive=True,
                                                                        cost_center_allocations=[
                                                                                                    CostCenterAllocationSchema(
                                                                                                            name= strCostCenter ,
                                                                                                            amount=-fCGSTAmount
                                                                                                        )
                                                                                                ], 
                                                            )
                                                            ]
                                            )
            
            objSGSTLedger = LedgerEntrySchema(
                                        ledger_name="SGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-fSGSTAmount,
                                        category_allocations=[
                                                                CategoryAllocationSchema(
                                                                    category="Primary Cost Category",
                                                                    is_deemed_positive=True,
                                                                    cost_center_allocations=[
                                                                                                CostCenterAllocationSchema(
                                                                                                        name= strCostCenter ,
                                                                                                        amount=-fSGSTAmount
                                                                                                    )
                                                                                            ], 
                                                            )
                                                            ]
                                            )
        
        # --------------------------------------------------------------------------

        # Roundoof Ledger Details
        roundedTotal = round(fTotalAmount)

        # Round-off amount (can be positive or negative)
        roundOffAmount = roundedTotal - fTotalAmount

        objRoundoffLedger = LedgerEntrySchema(
                                    ledger_name="Round off",
                                    is_deemed_positive=True,
                                    amount= abs(roundOffAmount) if roundOffAmount < 0 else -roundOffAmount,
                                )
        
        strPONumber = str(dictPODetails.get("PO No.", ""))
        strNarration = CAbhinavInfrabuild.MSGetNarration(dictPODetails)
        lsLedgerEntries = [
                            objPartyLedger,
                            objSGSTLedger,
                            objCGSTLedger,
                            objIGSTLedger,
                            objRoundoffLedger
                        ]
        lsLedgerEntries = [ledger for ledger in lsLedgerEntries if ledger is not None]
        # For demonstration, we use empty lists.
        po_voucher = TallyPOVoucherSchema(
            company_info=company,
            seller_details=seller,
            buyer_details=buyer,
            narration=strNarration,
            voucher_number=strPONumber, #datetime.now().strftime("AV/PO/%d%m%Y-%H%M%S"),
            po_date=datetime.strptime(dictPODetails.get("PO Date"), "%d/%b/%Y").strftime("%Y%m%d"),
            voucher_type="AV-Purchase Order",
            cost_center=strCostCenter,
            # consigneecstnumber="23211204978",
            # basicduedateofpymt="45 Days",
            # buyerpinnumber="**********",
            # consigneepinnumber="**********",
            inventory_entries=lsInventoryEntries,  
            ledger_entries=lsLedgerEntries,
            udf_data=lsUdfData   
        )
        
        xml_output = po_voucher.to_string(pretty=True)
        return xml_output


    @staticmethod
    def MSGetNarration(dictPoDetails):
        strNarration = ""
        try:
            lsPOItems = dictPoDetails.get("Items") 
            for i, dictItemDetails in enumerate(lsPOItems):
                strItemName = dictItemDetails.get("ERP_ITEM")
                fItemAmount = float(dictItemDetails.get("ITEM AMOUNT"))
                strUnitType = str(dictItemDetails.get("UNIT", ""))
                iQuantity = f"{str(dictItemDetails.get('QTY', ''))} {strUnitType}" 
                strCurrentItemDetails = f"  ({i+1}) Item: {strItemName}  |  Amount: {fItemAmount}  |  Quantity: {iQuantity} "
                strNarration += strCurrentItemDetails
        except Exception as e:
            print(traceback.print_exc())
            print(f"Failed to generate narration:{e}")
        return strNarration
    
    @staticmethod
    def MSGetCostCenter(dictPODetails):
        strPONo = str(dictPODetails.get("PO No.", "")).strip()
        
        if strPONo.lower().replace(" ", "").startswith("sf-"):
            return "CFPI"
        
        lsPONo = strPONo.split("-")
        for part in lsPONo:
            if len(part) >= 4:
                return part

        raise HTTPException(status_code=404, detail=f"Unable to find a valid cost center in PO Number {strPONo}")


    @staticmethod
    def MSGetInventoryEntries(dictPODetails):

        lsStockItemDetails = []
        
        lsPOItems = dictPODetails.get("Items") 
        strCostCenterName = CAbhinavInfrabuild.MSGetCostCenter(dictPODetails)
        for dictItemDetails in lsPOItems:
            
            strItemName = dictItemDetails.get("ITEM")
            strERPItemName = dictItemDetails.get("ERP_ITEM")
 
            fItemAmount = float(dictItemDetails.get("ITEM AMOUNT"))
            strUnitType = str(dictItemDetails.get("UNIT", ""))
            iQuantity = f"{str(dictItemDetails.get('QTY', ''))} {strUnitType}" 

            objBatchAllocation = BatchAllocationSchema(
                godownname="Main Location",
                destinationgodownname="Main Location",
                batchname="Primary Batch",
                amount=-fItemAmount,
                actual_qty=iQuantity,
                billed_qty=iQuantity,
                
                # TODO 1: Need to pass order Numnber
                # TODO 2: Need to pass order due date
            )
            
            objCategoryAllocation = CategoryAllocationSchema( 
                                                                category= "Primary Cost Category",
                                                                is_deemed_positive=True,
                                                                cost_center_allocations=[
                                                                    CostCenterAllocationSchema(name=strCostCenterName, amount=-fItemAmount)
                                                                    ]
                                                                )
            objAccountinAllocation = AccountingAllocationSchema(
                ledgername="Purchase - GST - Contract",  # TODO: Need to verify if the same ledger is used for all 
                is_deemed_positive=True,
                amount=-fItemAmount,
                category_allocations=[objCategoryAllocation]
            )

            objCGSTRateDetails = RateDetailSchema(
                gstrate_duty_head="CGST",
                gstrate_valuation_type="Based on Value",
                gstrate=9
            )

            objSGSTRateDetails = RateDetailSchema(
                gstrate_duty_head="SGST/UTGST",
                gstrate_valuation_type="Based on Value",
                gstrate=9
            )

            objIGSTRateDetails = RateDetailSchema(
                gstrate_duty_head="IGST",
                gstrate_valuation_type="Based on Value",
                gstrate=18
            )

            strItemRate = str(dictItemDetails.get("UNIT RATE"))
            strItemRateWithUnit = f"{strItemRate}/{strUnitType}"

            
            strDescription = ""
            strTechnicalDesc = dictItemDetails.get("TECH DESC")
            if strTechnicalDesc != "" and strTechnicalDesc != None and not pd.isna(strTechnicalDesc):
                strDescription += f"{strTechnicalDesc}"

            objStockItemDetails = InventoryEntrySchema(
                stockitemname=strItemName,
                rate=strItemRateWithUnit,
                amount=-fItemAmount,
                discount=0,
                actual_qty=iQuantity,
                billed_qty=iQuantity,
                gstsourcetype="Stock Item",
                gstledgersource="",
                hsnitemsource=strItemName,
                gst_hsnname="",
                description=strDescription,
                batch_allocations=[objBatchAllocation],
                accounting_allocations=[objAccountinAllocation],
                rate_details=[objCGSTRateDetails, objSGSTRateDetails, objIGSTRateDetails]
            )
            
            lsStockItemDetails.append(objStockItemDetails)

        return lsStockItemDetails
    
    async def MGenerateTallyXML(iUserId, iDocId, dictExtractedData, strVendorName, bRaiseError=False,lsUdfData=[{}],strClientREQID=None, bDownloadERP = False):
        CAbhinavInfrabuild._mIUserId = None
        dictResponse = {
            "AVComments":"",
            "TallyStatus":"",
            "XMLFilePath":"",
            "iSupplierInvoiceNumber":None,
            "TallyAPIResp": None,
            "DocErrorMsg": None
        }
        iSupplierInvoiceNumber = ""
        CAbhinavInfrabuild._mIUserId = iUserId
        XMLData = None
        try:
            await CLogController.MSWriteLog(iUserId, "Info", "Starting XML Generation for ICD.")
            
            iSupplierInvoiceNumber = dictExtractedData.get("InvoiceNo")
            dictResponse["iSupplierInvoiceNumber"] = iSupplierInvoiceNumber
            # iSupplierInvoiceNumber = CICDController.MSExtractInvoiceNumber(iSupplierInvoiceNumber)
            # TODO: iuserid
            if iSupplierInvoiceNumber is None or not iSupplierInvoiceNumber:
                # We Go Base on Doc ID if Invoice Number is Empty or None
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocID=iDocId)
            else:
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocumentNumber=iSupplierInvoiceNumber)
            
            if bIsDuplicate:
                dictResponse["TallyStatus"] = "Duplicate"
                dictResponse["AVComments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                dictResponse["TallyAPIResp"] = {"status": f"Failed to Create the Tally xml : Tally XML: Duplicate Entry Found in AccuVelocity."},
                dictResponse["DocErrorMsg"] = "AccuVelocity Duplicate Validation Entry Found."
                await CLogController.MSWriteLog(iUserId, "Info", f"Duplicate entry found for invoice numbered '{iSupplierInvoiceNumber}'.")
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=iDocId,
                    iUserID=iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"] 
                )
                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")

            # Get Upload Document Attributes
            objUploadedDocs = await CDocumentData.MSGetDocById(
                                                                user_id=iUserId, 
                                                                docId=iDocId,
                                                                isBinaryDataRequired=False
                                                            )
            await CLogController.MSWriteLog(iUserId, "Info", f"Fetched document details for DocId: {iDocId}.")

            try:
                #TODO: please add new vendor
                objGeneralizePWIV3 = CGeneralizePurchaseWithInventory(iUserID  = iUserId, dictExtractedData = dictExtractedData, iDocId = iDocId,strClientREQID = strClientREQID)
                dictTallyResponse = await objGeneralizePWIV3.MGetTallyXML(lsUDFData=lsUdfData, iDocId = iDocId,bDownloadERP = bDownloadERP)
                XMLData = dictTallyResponse.get("xmlContent")
                dictResponse["TallyStatus"] = dictTallyResponse.get("TallyStatus")
                dictResponse["AVComments"] = dictTallyResponse.get("AVComments")
                dictResponse["TallyAPIResp"] = dictTallyResponse.get("TallyAPIResp")
                dictResponse["DocErrorMsg"] = dictTallyResponse.get("DocErrorMsg")
            except ValueError as ve:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = str(ve)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                dictResponse["DocErrorMsg"] = str(ve)
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=iDocId,
                    iUserID=iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"] 
                )
                raise ve
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(e)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=iDocId,
                    iUserID=iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"] 
                )
                raise e
            
            try:
                if XMLData is not None:
                    # Base directory path
                    strDownloadDirPath =  CAbhinavInfrabuild._mStrStoragePathProduction
                    today_date = datetime.today().strftime('%Y_%m_%d')
                    
                    # Create the full directory path with the date-wise folder
                    strDownloadDirPath = os.path.join(strDownloadDirPath, today_date)

                    # Ensure the directory exists
                    os.makedirs(strDownloadDirPath, exist_ok=True)

                    strUploadedDocumentName = os.path.splitext(objUploadedDocs.get('DocName', ''))[0]
                    strXMLFileName = f"{strClientREQID}_DID{iDocId}_DName{strUploadedDocumentName}.xml"
                    strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                    bIsFileWritten = CFileHandler.MSWriteFile(  strFilePath=strTodaysXmlFilePath, 
                                                                fileContent= XMLData, 
                                                                strWriteMode="w", 
                                                                strEncoding=None) 
                    if bIsFileWritten: 
                        dictResponse["XMLFilePath"] = strTodaysXmlFilePath
                        dictResponse["TallyStatus"] = "Success"
                        dictResponse["AVComments"] = "-"
                        dictResponse["TallyAPIResp"] = {"status": f"Successfully created the tally xml at location: {strTodaysXmlFilePath}."}
                        
                        await CLogController.MSWriteLog(iUserId, "Info", f"Successfully stored xml file at location '{strTodaysXmlFilePath}'.")
                        # AVRecordDetail Update  
                        await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                            iUserId=iUserId,
                            dictNewData = {"XMLFilePath": [strTodaysXmlFilePath]},
                            eMode = ENetworkLocationUpdateMode.APPEND,
                            strClientREQID=strClientREQID,
                            docId=iDocId)
                else:
                    await CLogController.MSWriteLog(iUserId, "Warning", f"No XML Content Received FROM Vendor Method") 
                # Update the status of document processing
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                # Update the status of document processing
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=iDocId,
                    iUserID=iUserId,
                    strREQID = strClientREQID,
                    invoice_no=iSupplierInvoiceNumber,
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    DocErrorMsg=dictResponse["DocErrorMsg"] ,
                    resp_date_time=datetime.now(),
                    strAVComments=dictResponse["AVComments"] 
                )
                
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

        except HTTPException as he:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{he}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        
        except ValueError as ve:
            raise ve
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            print(f"An unexpected error occurred: {e}")

            if bRaiseError:
                raise e
        
        await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
            iDocID=iDocId,
            iUserID=iUserId,
            strREQID = strClientREQID,
            invoice_no=dictResponse["iSupplierInvoiceNumber"],
            av_tally_xml_status=dictResponse["TallyStatus"],
            tally_api_resp=dictResponse["TallyAPIResp"],
            resp_date_time=datetime.now(),
            DocErrorMsg=dictResponse["DocErrorMsg"],
            strAVComments=dictResponse["AVComments"]
        )
        return dictResponse


class CAbhinavInfrabuildGRN:
    _miTimeSavedPerGRNItem = 30  # Time (in seconds) saved per GRN item
    _mstrCompanyName = "Abhinav Infrabuild Pvt.Ltd.(24-26)"             
    _mStrStoragePathDevelopement = dictProjectPaths.get("STORAGE_PATH_DEVELOPMENT", r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild")
    _mStrStoragePathProduction = dictProjectPaths.get("STORAGE_PATH_PRODUCTION", r"H:/AI Data/DailyData/AbhinavInfrabuild")
    _mLsItemForGSTSplit = ["M SAND/Crush Sand", "Stone Dust ( Pure )", "Metal 12 mm", "Airen Stone", "Misc. Material", "Kopra", "M SAND/Crush Sand", "Boulder", "M SAND/Crush Sand", "Sand (CUM)", "GSB", "DUST", "MURRUM (CUM)", "Metal 40 MM", "Metal 6mm (Cum)", "Metal 20 MM", "Metal 10 MM"]
    _GRNFileVersion = VersionType.V1
    
    
    @staticmethod
    def MSCalculateTimeSavedForGRN(grn_list: list, per_entry_seconds: int) -> dict:
        """
        Calculate total time saved based on the number of GRN items.
        """
        total_seconds_saved = sum(len(grn.get("Items", [])) * per_entry_seconds for grn in grn_list)
        time_saved = timedelta(seconds=total_seconds_saved)
        hours, remainder = divmod(time_saved.seconds, 3600)
        minutes, seconds = divmod(remainder, 60)
        return {"hours": hours, "minutes": minutes, "seconds": seconds}
        
    @staticmethod
    async def MSGetAllGRNXML(iUserID, iRequestID, strExcelFilePath: str, bDeveloperMode: bool, bDownloadERP = False) -> list:
        """
        Read GRN details from an Excel file, generate GRN XML for each GRN,
        and return a list of processing details. Use PO item rates if available.
        """
        try:
            # Read the Excel file using CExcelHelper
            
            # 
            if CAbhinavInfrabuildGRN._GRNFileVersion == VersionType.V2:  
                dfGRNs = CExcelHelper.read_GRN_Updated_Format(strExcelFilePath)
            else:
                dfGRNs = CExcelHelper.read_file_temp(strExcelFilePath)
                new_columns = {
                    0: "GRN No",
                    1: "GRN Date",
                    2: "Party Bill No",
                    3: "Party Bill Date",
                    4: "Veh No",
                    5: "Ch No",
                    6: "PO No",
                    7: "Indent No",
                    8: "Party",
                    9: "Remarks",
                    10: "Item",
                    11: "Stock Qty",
                    12: "Unit",
                    13: "Weight",
                    14: "Rate",
                    15: "Amount",
                    16: "RST NO"
                }
                dfGRNs = dfGRNs.rename(columns=new_columns)
            
            try:
                await CAVRequestDetail.MSUpdateRecordByClientREQID(RequestID=iRequestID, FileContent=dfGRNs.to_json(orient="records"))
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                print(traceback.print_exc())
                
            # Clean relevant columns by stripping whitespace
            for col in ["Item", "Unit", "Party"]:
                if col in dfGRNs.columns:
                    dfGRNs[col] = dfGRNs[col].astype(str).str.strip()
            
            # Read PO File
            po_info_dict = {}
            strPOFilePath = CPOUtilities.MSGetLatestPOFilePath()
            
            if not os.path.exists(strPOFilePath):
                raise HTTPException(status_code=404, detail="PO file not found, please make sure the latest PO file is downloaded.")
            
            print(f"Found PO File at location '{strPOFilePath}'.")
            dfPurchaseOrders = CExcelHelper.read_file(strPOFilePath)            
            dfPurchaseOrders["PO NO."] = dfPurchaseOrders["PO NO."].astype(str).str.strip()
            
            # Load mapping Excel for item name normalization
            # Mapping_Excel_path = CAbhinavInfrabuild._mstrMappingExcelPath   # Update path as needed/
            # mapping_df, ledger_df = CExcelHelper.read_excel_sheets(Mapping_Excel_path, ["ITEM MAPPING", "ledger sheet"])
            
            # Updated Code to fetch Item Mapping from Google drive
            try:
                drive_service = GoogleDriveService()
                df_mapping = drive_service.handle_file(bisMultipleSheet=True, download = bDownloadERP)
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"There was a problem in getting Item ERP Matching file")
                raise Exception("There was a problem in getting Item ERP Matching file")
            sheets = ["ITEM MAPPING", "ledger sheet"]
            mapping_df, ledger_df = df_mapping[sheets[0]],df_mapping[sheets[1]]
            
            mapping_df['ERP Item Name '] = mapping_df['ERP Item Name '].astype(str).str.strip()
            mapping_df['Tally Items'] = mapping_df['Tally Items'].astype(str).str.strip()
            mapping_df['Tally Unit'] = mapping_df['Tally Unit'].astype(str).str.strip()
            mapping_df['ERP Unit'] = mapping_df['ERP Unit'].astype(str).str.strip()
            mapping_df['ERP Item Name_normalized'] = mapping_df['ERP Item Name '].str.lower().str.strip()
            
            # Check for duplicates
            duplicate_rows = mapping_df[mapping_df['ERP Item Name_normalized'].duplicated(keep=False)]
            if not duplicate_rows.empty:
                duplicate_items = duplicate_rows['ERP Item Name '].unique()
                duplicate_list_str = "\n".join(f"- {item}" for item in duplicate_items)
                raise HTTPException(
                    status_code=500,
                    detail=f"Duplicate ERP Item Names found in mapping Excel. Please update the file to remove duplicates:\n{duplicate_list_str}"
                )
                
            mapping_dict = mapping_df.set_index('ERP Item Name_normalized')[['Tally Unit', "ERP Unit", 'Tally Items']].to_dict(orient='index')
            
            # Normalize PO item names and map to Tally names
            dfPurchaseOrders['ITEM_normalized'] = dfPurchaseOrders['ITEM'].str.lower().str.strip()
            dfPurchaseOrders['Tally Item'] = dfPurchaseOrders['ITEM_normalized'].map(
                lambda x: mapping_dict.get(x, {}).get('Tally Items', dfPurchaseOrders['ITEM'].loc[dfPurchaseOrders['ITEM_normalized'] == x].iloc[0])
            )
            dfPurchaseOrders['Tally Unit'] = dfPurchaseOrders['ITEM_normalized'].map(
                lambda x: mapping_dict.get(x, {}).get('Tally Unit', dfPurchaseOrders['UNIT'].loc[dfPurchaseOrders['ITEM_normalized'] == x].iloc[0])
            )
            
            # Create po_info_dict with aggregated info and all items
            po_info_dict = {}
            for po_no, group in dfPurchaseOrders.groupby("PO NO."):
                po_info_dict[po_no] = {
                    "PO Date": group["PO DATE"].iloc[0],
                    "PO NO.": group["PO NO."].iloc[0],
                    "Items": group.to_dict(orient="records")  # List of dictionaries, each with all item columns
                }
            print("po_info_dict created successfully with all items included.")
            
            # Remove any rows with "TOTAL" (case-insensitive)
            dfGRNs = dfGRNs[~dfGRNs.apply(lambda row: row.astype(str).str.contains("TOTAL", case=False, na=False)).any(axis=1)]
            
            # Group by "GRN No" and create grn_list
            grn_groups = dfGRNs.groupby("GRN No")
            grn_list = []
            for grn_no, group in grn_groups:
                po_no = str(group["PO No"].iloc[0]).strip()
                po_info = po_info_dict.get(po_no, {"PO Date": "", "CST": '0',"Round Off":'0'})
                po_date_str = po_info["PO Date"]
                
                
                # Fetch PO item details from the PO information (already present in po_info_dict)
                po_items = po_info_dict.get(po_no, {}).get("Items", [])
    
                grn_data = {
                    "GRNNO": grn_no,
                    "GRN No": grn_no,
                    "GRN Date": group["GRN Date"].iloc[0],
                    "Supplier": group["Party"].iloc[0],
                    "PO No": group["PO No"].iloc[0] if not pd.isna(group["PO No"].iloc[0]) else "",
                    "Remarks": group["Remarks"].iloc[0],
                    "PO Date": po_date_str,
                    "Items": group[["Item", "Stock Qty", "Unit", "Rate", "Amount"]].to_dict("records"),
                    "Party Bill No": group["Party Bill No"].iloc[0],
                    "Challan No":group["Ch No"].iloc[0],
                    "PO_Items": po_items
                }
                grn_list.append(grn_data)
            
            # Clean and normalize ledger_df for case-insensitive and space-ignoring matching
            ledger_df['ERP'] = ledger_df['ERP'].astype(str).str.strip()
            ledger_df['Tally'] = ledger_df['Tally'].astype(str).str.strip()
            ledger_df['ERP_normalized'] = ledger_df['ERP'].str.lower().str.strip()
            ledger_dict = ledger_df.set_index('ERP_normalized')['Tally'].to_dict()
            
            lsGRNProcessingDetails = []
            for i, dictGRNDetails in enumerate(grn_list):
                dictGRNProcessingDetail = {
                    "XMLFilePath": "-",
                    "SR_No.": i+1,
                    "GRN_No": dictGRNDetails.get("GRN No", "-"),
                    "GRN_Date": dictGRNDetails.get("GRN Date", "-"),
                    "Supplier": dictGRNDetails.get("Supplier", "-"),
                    "Items": dictGRNDetails.get("Items", []),
                    "Total_Items": len(dictGRNDetails.get("Items", [])),
                    "Time_Saved": "0 Seconds",
                    "Time_Statistics": {"hours": 0, "minutes": 0, "seconds": 0},
                    "AV_Status": "Skipped",
                    "AV_Comments": "-"
                }
                strXmlData = None
                
                # Update supplier name using ledger_dict with normalization
                supplier = dictGRNDetails.get("Supplier")
                if supplier is None or (isinstance(supplier, float) and math.isnan(supplier)) or (isinstance(supplier, str) and not supplier.strip()) or (isinstance(supplier, str) and supplier == 'nan'):
                    supplier = "AIPL Head Office (Godown Main)"
                else:
                    supplier = str(supplier).strip()
                
                supplier_normalized = supplier.lower()
                if supplier_normalized in ledger_dict:
                    supplier = ledger_dict[supplier_normalized]
                    if not pd.isna(supplier) and str(supplier).strip().lower() != 'nan':
                        supplier = supplier

                
                dictGRNDetails["Supplier"] = supplier
                dictGRNProcessingDetail["Supplier"] = supplier
                
                # Update item names and units using mapping_dict
                for item in dictGRNDetails["Items"]:
                    item_name = item["Item"]
                    item_name_normalized = str(item_name).lower().strip()
                    value = mapping_dict.get(item_name_normalized, {}).get('Tally Items')
                    isValidTallyItem = (
                        value is not None and
                        not pd.isna(value) and
                        str(value).strip().lower() != 'nan'
                    )
                    if isValidTallyItem:
                        item["ERP_Item"] = item["Item"]
                        item["ERP_Unit"] = item["Unit"]
                        item["ERP_Rate"] = item["Rate"]
                        item["ERP_Amount"] = item["Amount"]
                        
                        item["Item"] = mapping_dict[item_name_normalized]['Tally Items']
                        strTallyUnit = mapping_dict[item_name_normalized]['Tally Unit']
                        strERPUnit = mapping_dict[item_name_normalized]['ERP Unit']
                        
                        if strTallyUnit is not None and not pd.isna(strTallyUnit) and str(strTallyUnit).strip().lower() != 'nan' and strTallyUnit != "":
                            item["Unit"] = strTallyUnit
                        else:
                            item["Unit"] = strERPUnit
                        
                    else:
                        item["ERP_Item"] = item["Item"]
                        item["ERP_Unit"] = item["Unit"]
                        item["ERP_Rate"] = item["Rate"]
                        item["ERP_Amount"] = item["Amount"]

                
                # Match GRN items with PO items and update rates and amounts if matching
                po_no = dictGRNDetails.get("PO No")
                if po_no and po_no in po_info_dict:
                    try:
                        po_items = {
                            item["Tally Item"]: item
                            for item in po_info_dict[po_no].get("Items", [])
                            if "Tally Item" in item
                        }

                        for grn_item in dictGRNDetails.get("Items", []):
                            try:
                                tally_item_name = grn_item.get("Item")
                                if not tally_item_name:
                                    dictGRNProcessingDetail["AV_Comments"] += "Missing 'Item' field in GRN item."
                                    continue

                                po_item = po_items.get(tally_item_name)
                                if not po_item:
                                    dictGRNProcessingDetail["AV_Comments"] += f"Item {tally_item_name} not found in PO {po_no}."
                                    continue

                                grn_unit = grn_item.get("Unit")
                                tally_unit = po_item.get("Tally Unit")
                                isValidTallyItemUnit = (
                                                        tally_unit is not None and
                                                        not pd.isna(tally_unit) and
                                                        str(tally_unit).strip().lower() != 'nan'
                                                    )
                                if isValidTallyItemUnit:
                                    grn_item["Unit"] = tally_unit
                                    
                                try:
                                    print(f"GRN -> {dictGRNDetails.get('GRN No', '-')}  PO NO  {po_no}  -->   BEFORE UPDATES  GRN Item Rate-> {grn_item['Rate']}  GRN Item Quantity-> {float(grn_item.get('Stock Qty', 0))}     GRN ITEM AMOUNT  ->  {grn_item['Amount']} ")

                                    unit_rate = float(po_item.get("UNIT RATE", 0))
                                    stock_qty = float(grn_item.get("Stock Qty", 0))
                                    grn_item["Rate"] = unit_rate
                                    grn_item["Amount"] = unit_rate * stock_qty
                                    
                                    # Calculating Item Wise Applicable
                                    try:
                                        iTotalItemGST = (po_item["VAT"] / po_item["QTY"]) * stock_qty
                                        if po_item["IGST"]:
                                            grn_item["IGST"] = iTotalItemGST
                                        else:
                                            iApplicableGST = iTotalItemGST / 2                                  
                                            grn_item["CGST"] = iApplicableGST
                                            grn_item["SGST"] = iApplicableGST
                                    except Exception as e:
                                        print(F"!! !! Failed to add GST Rates for GRN No {dictGRNDetails.get('GRN No', '-')}  PO NO  {po_no} !! !!, Error:{e}")
                                        await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                                    print(F"GRN -> {dictGRNDetails.get('GRN No', '-')}  PO NO  {po_no}  -->  AFTER UPDATES GRN Item Rate-> {grn_item['Rate']}    GRN Item Quantity-> {stock_qty}     GRN ITEM AMOUNT  ->  {grn_item['Amount']} ")
                                except (ValueError, TypeError) as calc_err:
                                    dictGRNProcessingDetail["AV_Comments"] += (
                                        f"Error calculating amount for item {tally_item_name}: {calc_err}"
                                    )
                                    print(f"Error calculating amount for item {tally_item_name}: {calc_err}")

                            except Exception as item_err:
                                dictGRNProcessingDetail["AV_Comments"] += (
                                    f"Unexpected error processing GRN item '{tally_item_name}': {item_err}"
                                )
                                print(f"Unexpected error processing GRN item '{tally_item_name}': {item_err}")
                    except Exception as outer_err:
                        dictGRNProcessingDetail["AV_Comments"] += f"Error processing PO match for PO {po_no}: {outer_err}"
                        print(f"Error processing PO match for PO {po_no}: {outer_err}")
                else:
                    dictGRNProcessingDetail["AV_Comments"] += f"PO No '{po_no}' not found in PO info."
                    print(f"PO No '{po_no}' not found in PO info.")
    
                try:
                    if not bDeveloperMode:
                        bIsDuplicate = await CAVGRNProcessingDetailsService.MSIsDuplicateGRN(UserID=iUserID, GRNNumber=dictGRNDetails.get("GRN No"))

                        if bIsDuplicate:
                            dictGRNProcessingDetail["AV_Status"] = "Duplicate"
                            dictGRNProcessingDetail["AV_Comments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                            lsGRNProcessingDetails.append(dictGRNProcessingDetail)
                            await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=iUserID, RequestID=iRequestID, GRNNumber=dictGRNProcessingDetail.get("GRN_No"), GRNDetails=dictGRNProcessingDetail, AVStatus=dictGRNProcessingDetail.get("AV_Status"), ErrorMessage=dictGRNProcessingDetail.get("AV_Comments"))

                            continue
                    
                    # Generate XML with updated dictGRNDetails
                    strXmlData = CAbhinavInfrabuildGRN.MSGetTallyGRNXML(dictGRNDetails)
                    
                    # Save XML in date-wise folder
                    if bDeveloperMode:
                        strDownloadDirPath = CAbhinavInfrabuildGRN._mStrStoragePathDevelopement
                    else:
                        strDownloadDirPath = CAbhinavInfrabuildGRN._mStrStoragePathProduction
                    
                    strDownloadDirPath = os.path.join(strDownloadDirPath, datetime.today().strftime("%Y_%m_%d"), "GRN")
                    os.makedirs(strDownloadDirPath, exist_ok=True)
                    strXMLFileName = f"REQID_{iRequestID}_UID_{iUserID}_GRNNUM_{dictGRNDetails.get('GRN No', '').replace('/', '_')}.xml"
                    strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                    
                    bIsFileWritten = CFileHandler.MSWriteFile(
                        strFilePath=strTodaysXmlFilePath,
                        fileContent=strXmlData,
                        strWriteMode="w",
                        strEncoding="utf-8"
                    )
                    if bIsFileWritten:
                        # !!!!!!!!!!!!!!!!!!   UNCOMMENT BELOW CODE TO DIRECTLY EPORT XML TO TALLY   
                        # CAbhinavInfrabuild.MSExportTallyData(strTodaysXmlFilePath, url="http://192.168.68.54:10101/")
                        # !!!! !!! !!!!! !!! !!!! !!!!!  !!!! !!! !!!! !!!! !!! !!! !!!! !!!!
                            
                        
                        fTotalAmount = sum(float(item["Amount"]) for item in dictGRNDetails.get("Items", []))
                        if fTotalAmount <= 0:
                            dictGRNProcessingDetail["AV_Comments"] = "The total amount is zero. Please review the GRN."
            
                        
                        dictGRNProcessingDetail["XMLFilePath"] = strTodaysXmlFilePath
                        dictGRNProcessingDetail["AV_Status"] = "Success"
                        
                        # Calculate Time Saved
                        dictTimeSavedInfo = CAbhinavInfrabuildGRN.MSCalculateTimeSavedForGRN(
                            grn_list=[dictGRNDetails],
                            per_entry_seconds=CAbhinavInfrabuildGRN._miTimeSavedPerGRNItem
                        )
                        dictGRNProcessingDetail["Time_Statistics"] = dictTimeSavedInfo
                        dictGRNProcessingDetail["Time_Saved"] = (
                            f"{dictTimeSavedInfo['hours']} hours, "
                            f"{dictTimeSavedInfo['minutes']} minutes, "
                            f"{dictTimeSavedInfo['seconds']} seconds"
                        )
                        
                        if not dictGRNDetails.get("PO Date", ""):
                            if pd.isna(dictGRNDetails.get('PO No', '')):
                                dictGRNProcessingDetail["AV_Comments"] = f"Not Associated with Any Purchase order."
                            else:
                                dictGRNProcessingDetail["AV_Comments"] = f"PO No: '{dictGRNDetails.get('PO No', '')}' not found in ERP. Please update PO details manually."
                        
                
                except HTTPException as he:
                    dictGRNProcessingDetail["AV_Comments"] = str(he.detail)
                    dictGRNProcessingDetail["AV_Status"] = "Skipped"
                    
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                    
                except Exception as e:
                    dictGRNProcessingDetail["AV_Comments"] = "Could not enter the GRN in Tally, please enter manually."
                    dictGRNProcessingDetail["AV_Status"] = "Skipped"
                    
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                
                lsGRNProcessingDetails.append(dictGRNProcessingDetail)
                await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=iUserID, RequestID=iRequestID, GRNNumber=dictGRNProcessingDetail.get("GRN_No"), GRNDetails=dictGRNProcessingDetail, AVStatus=dictGRNProcessingDetail.get("AV_Status"), ErrorMessage=dictGRNProcessingDetail.get("AV_Comments"), GeneratedXML=strXmlData)
            return lsGRNProcessingDetails
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print(traceback.print_exc())
        return []
    
    @staticmethod
    def MSGetGRNNarration(dictGRNDetails):
        strNarration = ""
        try:
            strRemarks = dictGRNDetails.get("Remarks", "")
            strPartyBillNo = dictGRNDetails.get("Party Bill No", "")

            narration_parts = []

            if pd.notna(strRemarks):
                narration_parts.append(f"Remarks: {strRemarks}")

            if pd.notna(strPartyBillNo):
                narration_parts.append(f"Party Bill No: ({strPartyBillNo})")

            strNarration = " ".join(narration_parts)
        except Exception as e:
            print(f"Failed to create GRN Narration, error: {e}")

        return strNarration

    @staticmethod
    def MSGetChallanNo(strDescription):
        strChallanNo = ""
        try:
            remark = str(strDescription)
            ref_no_match = re.search(r'\b\d{5}\b', remark)
            strChallanNo = ref_no_match.group() if ref_no_match else ""
        except Exception as e:
            print(f"Failed to fetch challan number from '{strDescription}'.")
            
        return strChallanNo
    
    @staticmethod
    def MSGetTallyGRNXML(dictGRNDetails: dict) -> str:
        """
        Generates GRN XML from a dictionary of GRN details using TallyGRNVoucherSchema.
        Uses per-item VAT from PO_Items if available. If any GRN item is in GST_SPLIT_LIST,
        it splits GST per unit for those items and prorates for others; otherwise it applies
        a simple overall GST split.
        """
        # ─── 0) Setup company, party, supplier & buyer ──────────────────────────────
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuildGRN._mstrCompanyName,
            gst_in="23**********1ZY",
            gst_registration_type="Regular"
        )

        # party and challan
        party_name = dictGRNDetails.get("Supplier")
        challanNo = CAbhinavInfrabuildGRN.MSGetChallanNo(str(dictGRNDetails.get("Challan No")))
        if not challanNo or pd.isna(challanNo) or str(challanNo).strip().lower() in ('', 'nan'):
            challanNo = dictGRNDetails.get("GRN No")

        if not party_name or (isinstance(party_name, float) and math.isnan(party_name)) \
        or (isinstance(party_name, str) and not party_name.strip()) \
        or str(party_name).strip().lower() == 'nan':
            party_name = "AIPL Head Office (Godown Main)"
        elif "cash" in party_name.lower():
            party_name = "CASH"

        supplier_info = CJSONFileReader.get_supplier_details_from_ledger(
            party_name,
            CAbhinavInfrabuild._mStrVendorDetailsPath
        )
        supplier = PartyDetailsSchema(
            party_name=party_name,
            address_list=supplier_info.get("address_list", []),
            gst_in=supplier_info.get("gst_in", ""),
            state_name=supplier_info.get("state_name", ""),
            country_name=supplier_info.get("country_name", ""),
            gst_registration_type="Regular"
        )

        buyer = PartyDetailsSchema(
            party_name="Abhinav Infrabuild Pvt.Ltd.",
            address_list=[
                "207-208 Industry House,A.B.Road Indore(M.P.)",
                "Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)",
                "E-Mail : <EMAIL>"
            ],
            gst_in="23**********1ZY",
            state_name="Madhya Pradesh",
            country_name="India",
            pin_code="452001",
            gst_registration_type="Regular"
        )

        # ─── 1) Dates, narration, cost center ───────────────────────────────────────
        grn_date = DateHelper.convert_date(str(dictGRNDetails.get("GRN Date")))
        po_date_str = dictGRNDetails.get("PO Date", "")
        po_date = DateHelper.convert_date(str(po_date_str)) if po_date_str else grn_date

        strCostCenter = CGRNUtilities.MSGetCostCenter(dictGRNDetails)
        strNarration = CAbhinavInfrabuildGRN.MSGetGRNNarration(dictGRNDetails=dictGRNDetails)

        po_items = dictGRNDetails.get("PO_Items", [])
        
        grn_items = dictGRNDetails.get("Items", [])
        
        bIGSTApplicable = float(po_items[0].get("IGST", 0)) > 0 if po_items else False  # By Default, We will pass CGST/SGST
        
        inv_entries = []
        iTotalGSTAmount = 0
        
        for item in grn_items:
            name = item["Item"]
            qty = float(item["Stock Qty"])
            amount = float(item["Amount"])
            iTotalGSTAmount += float(item.get("CGST", 0))
            iTotalGSTAmount += float(item.get("SGST", 0))
            iTotalGSTAmount += float(item.get("IGST", 0))

            inv_entries.append(
                InventoryEntrySchema(
                    stockitemname=name,
                    is_deemed_positive=True,
                    rate=f"{float(item['Rate']):.2f}/{item['Unit']}",
                    amount=-amount,
                    actual_qty=f"{qty} {item['Unit']}",
                    billed_qty=f"{qty} {item['Unit']}",
                    description=(
                        f"Item: {item['ERP_Item']} | Quantity: {qty} {item['ERP_Unit']} "
                        f"| Rate: {float(item['ERP_Rate']):.2f}/{item['ERP_Unit']} "
                        f"| Amount: {item['ERP_Amount']}"
                    ),
                    batch_allocations=[
                        BatchAllocationSchema(
                            tracking_no=challanNo,
                            godownname="Main Location",
                            batchname="Primary Batch",
                            amount=-amount,
                            actual_qty=f"{qty} {item['Unit']}",
                            billed_qty=f"{qty} {item['Unit']}",
                            orderduedate=po_date
                        )
                    ],
                    accounting_allocations=[
                        AccountingAllocationSchema(
                            ledgername="Purchase - GST - Contract",
                            gstclass="Not Applicable",
                            is_deemed_positive=True,
                            amount=-amount,
                            category_allocations=[
                                CategoryAllocationSchema(
                                    category="Primary Cost Category",
                                    is_deemed_positive=True,
                                    cost_center_allocations=[
                                        CostCenterAllocationSchema(name=strCostCenter, amount=-amount)
                                    ]
                                )
                            ]
                        )
                    ],
                    rate_details=[
                        RateDetailSchema(gstrate_duty_head="CGST", gstrate_valuation_type="Based on Value", gstrate=9),
                        RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate_valuation_type="Based on Value", gstrate=9),
                        RateDetailSchema(gstrate_duty_head="IGST", gstrate_valuation_type="Based on Value", gstrate=18)
                    ],
                    gst_hsnname=""
                )
            )

        # ─── 4) Round-off & ledger entries ─────────────────────────────────────────
        fRoundoffAmount = float(dictGRNDetails.get("Round Off", 0))
        ledger_entries = [
            LedgerEntrySchema(
                ledger_name=party_name,
                gst_class="Not Applicable",
                is_deemed_positive=False,
                amount=sum(abs(e.amount) for e in inv_entries),
                category_allocations=[
                    CategoryAllocationSchema(
                        category="Primary Cost Category",
                        is_deemed_positive=False,
                        cost_center_allocations=[
                            CostCenterAllocationSchema(name=strCostCenter, amount=sum(abs(e.amount) for e in inv_entries))
                        ]
                    )
                ]
            ),
            LedgerEntrySchema(
                ledger_name="Round off",
                is_deemed_positive=True,
                amount=abs(fRoundoffAmount) if fRoundoffAmount < 0 else -fRoundoffAmount
            )
        ]

        if bIGSTApplicable:
            ledger_entries.append(
                                    LedgerEntrySchema(
                                        ledger_name="IGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-iTotalGSTAmount,
                                        category_allocations=[
                                            CategoryAllocationSchema(
                                                category="Primary Cost Category",
                                                is_deemed_positive=True,
                                                cost_center_allocations=[
                                                    CostCenterAllocationSchema(name=strCostCenter, amount=-iTotalGSTAmount)
                                                ]
                                            )
                                        ]
                                    )
                                )
        else:
            iApplicableGST = iTotalGSTAmount / 2
            ledger_entries.extend(
                [
                    LedgerEntrySchema(
                                        ledger_name="CGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-iApplicableGST,
                                        category_allocations=[
                                            CategoryAllocationSchema(
                                                category="Primary Cost Category",
                                                is_deemed_positive=True,
                                                cost_center_allocations=[
                                                    CostCenterAllocationSchema(name=strCostCenter, amount=-iApplicableGST)
                                                ]
                                            )
                                        ]
                                    ),
                    LedgerEntrySchema(
                                        ledger_name="SGST",
                                        gst_class="Not Applicable",
                                        is_deemed_positive=True,
                                        amount=-iApplicableGST,
                                        category_allocations=[
                                            CategoryAllocationSchema(
                                                category="Primary Cost Category",
                                                is_deemed_positive=True,
                                                cost_center_allocations=[
                                                    CostCenterAllocationSchema(name=strCostCenter, amount=-iApplicableGST)
                                                ]
                                            )
                                        ]
                    )
                ]
            )
        # ─── 5) Build and return voucher ───────────────────────────────────────────
        strPONumber = dictGRNDetails.get("PO No")
        if pd.isna(strPONumber):
            strPONumber = ""
        grn_voucher = TallyGRNVoucherSchema(
            company_info=company,
            supplier_details=supplier,
            buyer_details=buyer,
            voucher_number=dictGRNDetails.get("GRN No"),
            reference_number=challanNo,
            cost_center=strCostCenter,
            grn_date=grn_date,
            voucher_type="AV-GRN",
            narration=strNarration,
            po_number=strPONumber,
            po_date=po_date,
            inventory_entries=inv_entries,
            ledger_entries=ledger_entries,
            basic_duedate_of_pymt="45 Days"
        )

        return grn_voucher.to_string(pretty=True)

class CBhagwati_XML:
    _mstrPartyName = "Bhagwati Trading Co."
    _mstrPurchaseLedgerName = "Av-Purchase - GST - Contract"
    _mstrPartyState = "Madhya Pradesh"
    _mstrPartyGSTIN = "23ADEPA7548N1ZF"
    _mlsTotalStockItems = []       # To keep track of all the stock items from the invoice
    _mlsNonExistingStockItems = [] # Used to identify non-existing stock items from the invoice

    @staticmethod
    async def MSGetTallyXML(iUserId, dictExtractedData, lsUDFData=[{}]):
        # Initialize class variables with empty lists
        CBhagwati_XML._mlsTotalStockItems = []
        CBhagwati_XML._mlsNonExistingStockItems = []

        # Company Information (Buyer)
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuild._mstrCompanyName,
            gst_registration_type="Regular",
            gst_in="23**********1ZY"
        )

        # Party Details (Seller)
        party = PartyDetailsSchema(
            party_name=CBhagwati_XML._mstrPartyName,
            gst_in=CBhagwati_XML._mstrPartyGSTIN,
            address_list=[
                "Block No.-B1, Shop No.B-4, New Siyaganj, Poddar Plaza",
                "HTL Unit No.-2, VIP Route No.-1, Mal Godam Road",
                "Indore (M.P.)-452007"
            ],
            state_name=CBhagwati_XML._mstrPartyState,
            country_name="India",
            mailing_name=CBhagwati_XML._mstrPartyName
        )

        # Consignee Details (Same as Company)
        consignee = ConsigneeDetailsSchema(
            gst_in="23**********1ZY",
            address_list=["207-208", "Industry House", "Indore"],
            state_name="Madhya Pradesh",
            country_name="India",
            mailing_name="Abhinav Infrabuild Private Limited"
        )

        # Get Inventory Items
        lsStockItemInfo = await CBhagwati_XML.MSGetAllStockItemInfo(dictExtractedData)

        # Party Ledger Info (Credited)
        fTotalAmount = dictExtractedData.get("TotalAmount")  # Negative for credit
        partyBillAllocation = BillAllocationSchema(
            name=dictExtractedData.get("InvoiceNo"),
            billtype="New Ref",
            amount=fTotalAmount
        )
        party_ledger_entry = LedgerEntrySchema(
            ledger_name=CBhagwati_XML._mstrPartyName,
            amount=fTotalAmount,
            is_deemed_positive=False,
            is_party_ledger=True,
            bill_allocation=partyBillAllocation
        )

        # CGST Ledger Info (Debited)
        cgst_entry = LedgerEntrySchema(
            ledger_name="CGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][0]["TaxAmount"],
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # SGST Ledger Info (Debited)
        sgst_entry = LedgerEntrySchema(
            ledger_name="SGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][1]["TaxAmount"],
            is_deemed_positive=True,
            is_party_ledger=False
        )
        
        # fRoundingoffAmount = dictExtractedData.get("RoundingOff")
        # roundoff_entry = LedgerEntrySchema(
        #     ledger_name="Rounding Off (PURCHASE)",
        #     amount= abs(fRoundingoffAmount) if fRoundingoffAmount < 0 else -fRoundingoffAmount ,        # TODO:  Verify for both positive and negative roundoff 
        #     is_deemed_positive=True,
        #     is_party_ledger=False,
        #     round_type="Normal Rounding",
        #     round_limit=1,
        #     method_type="As Total Amount Rounding",
        #     gst_overridden=False,
        #     remove_zero_entries=True
        # )

        # Create Voucher
        voucher = TallyPurchaseInventoryVoucherSchema(
            company_info=company,
            voucher_class="CSLV",
            party_details=party,
            consignee_details=consignee,
            voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
            invoice_date=dictExtractedData.get("InvoiceDate"),
            invoice_no=dictExtractedData.get("InvoiceNo"),
            voucher_type="Av-Purchase - GST - Contract",
            ledger_entries=[party_ledger_entry, cgst_entry, sgst_entry],
            inventory_entries=lsStockItemInfo,
            udf_data=lsUDFData,
            bISParag=False
        )

        strXMLContent = voucher.to_string(pretty=True)
        return {
            "xmlContent": strXMLContent,
            "allStockItems": CBhagwati_XML._mlsTotalStockItems,
            "nonExistingStockItems": CBhagwati_XML._mlsNonExistingStockItems,
            "TallyStatus": "-",
            "AVComments": "-"
        }

    @staticmethod
    async def MSGetStockItemName(dictStockItemDetails):
        # Simply return the item name from the API response
        # Can be enhanced to map or validate against Tally stock items if needed
        # CBhagwati_XML._mlsTotalStockItems.append(dictStockItemDetails)
        # strStockItemName = dictStockItemDetails.get("ItemName")
        # if not strStockItemName:
        #     CBhagwati_XML._mlsNonExistingStockItems.append(dictStockItemDetails)
        # return strStockItemName
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictStockItemDetails,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
            return lsResult[0].get("bestMatch","")
        except Exception as e:
            print(e)

    @staticmethod
    async def MSGetAllStockItemInfo(dictExtractedData):
        lsInventoryItemDetails = []
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictExtractedData,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
        except Exception as e:
            print(e)

        for index, (dictStockItemDetail, result) in enumerate(zip(dictExtractedData.get("ItemDetails", []), lsResult)):
            strStockItemName = result.get("bestMatch","")
            if not strStockItemName:
                continue

            iItemAmount = -dictStockItemDetail.get("AmountBeforeTaxesAndDiscounts")
            strQuantity = f"{dictStockItemDetail.get('Quantity')}"
            strRate = str(dictStockItemDetail.get("Rate"))
            
            strBatchName = dictStockItemDetail.get("DesignName", "Primary Batch")
            strGodownName = "Main Location"
            
            
            batch_alloc = BatchAllocationSchema(
                godownname=strGodownName,
                batchname=strBatchName if strBatchName else "Primary Batch",
                destinationgodownname=strGodownName,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            # Accounting Allocation to Purchase Ledger
            accounting_alloc = AccountingAllocationSchema(
                ledgername=CBhagwati_XML._mstrPurchaseLedgerName,
                amount=iItemAmount  # Positive amount, debited via inventory allocation
            )

            # Rate Details for GST
            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="IGST", gstrate=18)
            ]

            # Inventory Entry
            inv_entry = InventoryEntrySchema(
                # stockitemname=strStockItemName.replace("Batch Primary Batch","").replace('\\" Batch : Primary Batch',"").replace('"',"").strip(),
                stockitemname=strStockItemName,
                gst_hsnname=dictStockItemDetail.get("HSNCode"),
                rate=strRate,
                discount=0.0,  # Assuming no discount unless specified
                amount=iItemAmount,  # Positive amount as per Tally convention
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsInventoryItemDetails.append(inv_entry)

        return lsInventoryItemDetails
    

class CNarayanMarketing_XML:
    _mlsTotalStockItems = []       # Tracks all stock items from the invoice
    _mlsNonExistingStockItems = [] # Tracks non-existing stock items

    @staticmethod
    async def MSGetTallyXML(iUserId, dictExtractedData, lsUDFData=[{}]):
        # Reset class variables
        CNarayanMarketing_XML._mlsTotalStockItems = []
        CNarayanMarketing_XML._mlsNonExistingStockItems = []

        # Company Information (Buyer)
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuild._mstrCompanyName,
            gst_registration_type="Regular",
            gst_in="23**********1ZY"
        )

        # Party Details (Seller - Narayan Marketing)
        party = PartyDetailsSchema(
            party_name=dictExtractedData["SellerDetails"]["SellerName"],
            gst_in=dictExtractedData["SellerDetails"]["SellerGST"],
            address_list=[dictExtractedData["SellerDetails"]["SellerAddress"]],
            state_name=dictExtractedData["SellerDetails"]["SellerState"],
            country_name="India",
            mailing_name=dictExtractedData["SellerDetails"]["SellerName"]
        )

        # Consignee Details (Same as Buyer)
        consignee = ConsigneeDetailsSchema(
            gst_in="23**********1ZY",
            address_list=["207-208", "Industry House", "Indore"],
            state_name="Madhya Pradesh",
            country_name="India",
            mailing_name="Abhinav Infrabuild Private Limited"
        )

        # Get Inventory Items (with discount handling)
        lsStockItemInfo = await CNarayanMarketing_XML.MSGetAllStockItemInfo(dictExtractedData)

        # Party Ledger Entry (Credited with total invoice amount)
        fTotalAmount = dictExtractedData.get("TotalAmount")  # Negative for credit (-3850.0)
        partyBillAllocation = BillAllocationSchema(
            name=dictExtractedData.get("InvoiceNo"),
            billtype="New Ref",
            amount=fTotalAmount
        )
        party_ledger_entry = LedgerEntrySchema(
            ledger_name=party.party_name,
            amount=fTotalAmount,
            is_deemed_positive=False,
            is_party_ledger=True,
            bill_allocation=partyBillAllocation
        )

        # CGST Ledger Entry (Debited)
        cgst_entry = LedgerEntrySchema(
            ledger_name="CGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][0]["TaxAmount"],  # 293.66
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # SGST Ledger Entry (Debited)
        sgst_entry = LedgerEntrySchema(
            ledger_name="SGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][1]["TaxAmount"],  # 293.66
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # Rounding Off Ledger Entry (Debited)
        fRoundingoffAmount = dictExtractedData.get("RoundingOff")  # -0.2
        roundoff_entry = LedgerEntrySchema(
            ledger_name="Round Off",
            amount= abs(fRoundingoffAmount) if fRoundingoffAmount < 0 else -fRoundingoffAmount ,        # TODO:  Verify for both positive and negative roundoff 
            is_deemed_positive=True,
            is_party_ledger=False,
            round_type="Normal Rounding",
            round_limit=1,
            method_type="As Total Amount Rounding",
            gst_overridden=False,
            remove_zero_entries=True
        )

        # Create Voucher
        voucher = TallyPurchaseInventoryVoucherSchema(
            company_info=company,
            voucher_class="CSLV",
            party_details=party,
            consignee_details=consignee,
            voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
            invoice_date=dictExtractedData.get("InvoiceDate"),
            invoice_no=dictExtractedData.get("InvoiceNo"),
            voucher_type="Av-Purchase - GST - Contract",
            ledger_entries=[party_ledger_entry, cgst_entry, sgst_entry, roundoff_entry],
            inventory_entries=lsStockItemInfo,
            udf_data=lsUDFData,
            
        )

        strXMLContent = voucher.to_string(pretty=True)
        return {
            "xmlContent": strXMLContent,
            "allStockItems": CNarayanMarketing_XML._mlsTotalStockItems,
            "nonExistingStockItems": CNarayanMarketing_XML._mlsNonExistingStockItems,
            "TallyStatus": "-",
            "AVComments": "-"
        }

    @staticmethod
    def MSGetStockItemName(dictStockItemDetails):
        # Extract item name and track stock items
        CNarayanMarketing_XML._mlsTotalStockItems.append(dictStockItemDetails)
        strStockItemName = dictStockItemDetails.get("ItemName")
        if not strStockItemName:
            CNarayanMarketing_XML._mlsNonExistingStockItems.append(dictStockItemDetails)
        return strStockItemName

    @staticmethod
    async def MSGetAllStockItemInfo(dictExtractedData):
        lsInventoryItemDetails = []
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictExtractedData,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
        except Exception as e:
            print(e)

        for index, (dictStockItemDetail, result) in enumerate(zip(dictExtractedData.get("ItemDetails", []), lsResult)):
            strStockItemName = result.get("bestMatch","")
            if not strStockItemName:
                continue

            # Extract discount rate and amount (default to 0 if not present)
            fDiscountRate = dictStockItemDetail.get("DiscountRate", 0)
            fDiscountAmount = dictStockItemDetail.get("DiscountAmount", 0)
            
            strBatchName = dictStockItemDetail.get("DesignName", "Primary Batch")
            strGodownName = "Main Location"
            
            

            # Amount after discount (taxable amount)
            iItemAmount = -dictStockItemDetail.get("Taxes")["MainTaxes"][0]["TaxableAmount"]  # 3262.88

            # Quantity and unit
            strQuantity = f"{dictStockItemDetail.get('Quantity')}"  # "1.0 BUC"

            # Rate (before discount)
            strRate = str(dictStockItemDetail.get("Rate"))  # "3850.0"
            
            batch_alloc = BatchAllocationSchema(
                godownname=strGodownName,
                batchname=strBatchName if strBatchName else "Primary Batch",
                destinationgodownname=strGodownName,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            # Accounting Allocation to Purchase Ledger (amount after discount)
            accounting_alloc = AccountingAllocationSchema(
                ledgername="Av-Purchase - GST - Contract",
                amount=iItemAmount  # 3262.88
            )

            # Rate Details for GST
            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=9)
            ]

            # Inventory Entry (with discount rate)
            inv_entry = InventoryEntrySchema(
                stockitemname=strStockItemName,
                gst_hsnname=dictStockItemDetail.get("HSNCode"),
                rate=strRate,
                discount=fDiscountRate,  # 15.25 or 0 if no discount
                amount=iItemAmount,      # 3262.88
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsInventoryItemDetails.append(inv_entry)

        return lsInventoryItemDetails
 
 
class CVSAgencies_XML:
    _mlsTotalStockItems = []       # Tracks all stock items from the invoice
    _mlsNonExistingStockItems = [] # Tracks non-existing stock items

    @staticmethod
    async def MSGetTallyXML(iUserId, dictExtractedData, lsUDFData=[{}]):
        
        # Reset class variables
        CVSAgencies_XML._mlsTotalStockItems = []
        CVSAgencies_XML._mlsNonExistingStockItems = []

        # Assuming these schema classes are defined elsewhere
        # Company Information (Buyer)
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuild._mstrCompanyName,
            gst_registration_type="Regular",
            gst_in="23**********1ZY"
        )

        # Party Details (Seller - V.S. Agencies)
        party = PartyDetailsSchema(
            party_name=dictExtractedData["SellerDetails"]["SellerName"],
            gst_in=dictExtractedData["SellerDetails"]["SellerGST"],
            address_list=[dictExtractedData["SellerDetails"]["SellerAddress"]],
            state_name=dictExtractedData["SellerDetails"]["SellerState"],
            country_name="India",
            mailing_name=dictExtractedData["SellerDetails"]["SellerName"]
        )

        # Consignee Details (Same as Buyer)
        consignee = ConsigneeDetailsSchema(
            gst_in="23**********1ZY",
            address_list=["207-208", "Industry House", "Indore"],
            state_name="Madhya Pradesh",
            country_name="India",
            mailing_name="Abhinav Infrabuild Pvt Ltd"
        )

        # Get Inventory Items
        lsStockItemInfo =await CVSAgencies_XML.MSGetAllStockItemInfo(dictExtractedData)

        # Party Ledger Entry (Credited with total invoice amount)
        fTotalAmount = dictExtractedData.get("TotalAmount")  # 10001.0
        partyBillAllocation = BillAllocationSchema(
            name=dictExtractedData.get("InvoiceNo"),
            billtype="New Ref",
            amount=fTotalAmount
        )
        party_ledger_entry = LedgerEntrySchema(
            ledger_name=party.party_name,
            amount=fTotalAmount,
            is_deemed_positive=False,
            is_party_ledger=True,
            bill_allocation=partyBillAllocation
        )

        # CGST Ledger Entry (Debited)
        cgst_entry = LedgerEntrySchema(
            ledger_name="CGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][0]["TaxAmount"],  # 762.75
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # SGST Ledger Entry (Debited)
        sgst_entry = LedgerEntrySchema(
            ledger_name="SGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][1]["TaxAmount"],  # 762.75
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # Rounding Off Ledger Entry (Debited)
        fRoundingoffAmount = dictExtractedData.get("RoundingOff")  # 0.5
        roundoff_entry = LedgerEntrySchema(
            ledger_name="Round Off",
            amount=abs(fRoundingoffAmount) if fRoundingoffAmount < 0 else -fRoundingoffAmount,
            is_deemed_positive=True,
            is_party_ledger=False,
            round_type="Normal Rounding",
            round_limit=1,
            method_type="As Total Amount Rounding",
            gst_overridden=False,
            remove_zero_entries=True
        )

        # Create Voucher
        voucher = TallyPurchaseInventoryVoucherSchema(
            company_info=company,
            voucher_class="CSLV",
            party_details=party,
            consignee_details=consignee,
            voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
            invoice_date=dictExtractedData.get("InvoiceDate"),
            invoice_no=dictExtractedData.get("InvoiceNo"),
            voucher_type="Av-Purchase - GST - Contract",
            ledger_entries=[party_ledger_entry, cgst_entry, sgst_entry, roundoff_entry],
            inventory_entries=lsStockItemInfo,
            udf_data=lsUDFData,
            
        )

        strXMLContent = voucher.to_string(pretty=True)
        return {
            "xmlContent": strXMLContent,
            "allStockItems": CVSAgencies_XML._mlsTotalStockItems,
            "nonExistingStockItems": CVSAgencies_XML._mlsNonExistingStockItems,
            "TallyStatus": "-",
            "AVComments": "-"
        }

    @staticmethod
    def MSGetStockItemName(dictStockItemDetails):
        # Extract item name and track stock items
        CVSAgencies_XML._mlsTotalStockItems.append(dictStockItemDetails)
        strStockItemName = dictStockItemDetails.get("ItemName")
        if not strStockItemName:
            CVSAgencies_XML._mlsNonExistingStockItems.append(dictStockItemDetails)
        return strStockItemName

    @staticmethod
    async def MSGetAllStockItemInfo(dictExtractedData):
        lsInventoryItemDetails = []
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictExtractedData,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
        except Exception as e:
            print(e)

        for index, (dictStockItemDetail, result) in enumerate(zip(dictExtractedData.get("ItemDetails", []), lsResult)):
            strStockItemName = result.get("bestMatch","")
            if not strStockItemName:
                continue

            # Extract discount rate and amount (default to 0 if not present)
            fDiscountRate = dictStockItemDetail.get("DiscountRate", 0)
            fDiscountAmount = dictStockItemDetail.get("DiscountAmount", 0)
            
            strBatchName = dictStockItemDetail.get("DesignName", "Primary Batch")
            strGodownName = "Main Location"
            
            # Amount after discount (taxable amount)
            iItemAmount = -dictStockItemDetail.get("Taxes")["MainTaxes"][0]["TaxableAmount"]  # 8475.0

            # Quantity and unit
            strQuantity = f"{dictStockItemDetail.get('Quantity')}"  # "250 MTR"

            # Rate (before discount)
            strRate = str(dictStockItemDetail.get("Rate"))  # "33.9"
            
            batch_alloc = BatchAllocationSchema(
                godownname=strGodownName,
                batchname=strBatchName if strBatchName else "Primary Batch",
                destinationgodownname=strGodownName,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            # Accounting Allocation to Purchase Ledger (amount after discount)
            accounting_alloc = AccountingAllocationSchema(
                ledgername="Av-Purchase - GST - Contract",
                amount=iItemAmount  # 8475.0
            )

            # Rate Details for GST
            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=9)
            ]

            # Inventory Entry (with discount rate)
            inv_entry = InventoryEntrySchema(
                stockitemname=strStockItemName,
                gst_hsnname=dictStockItemDetail.get("HSNCode"),
                rate=strRate,
                discount=fDiscountRate,  # 0
                amount=iItemAmount,      # 8475.0
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsInventoryItemDetails.append(inv_entry)

        return lsInventoryItemDetails


class CASHKELON_ENTERPRISES_XML:
    _mlsTotalStockItems = []       # Tracks all stock items from the invoice
    _mlsNonExistingStockItems = [] # Tracks non-existing stock items

    @staticmethod
    async def MSGetTallyXML(iUserId, dictExtractedData, lsUDFData=[{}]):
        # Reset class variables
        CASHKELON_ENTERPRISES_XML._mlsTotalStockItems = []
        CASHKELON_ENTERPRISES_XML._mlsNonExistingStockItems = []

        # Company Information (Buyer)
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuild._mstrCompanyName,
            gst_registration_type="Regular",
            gst_in="23**********1ZY"
        )

        # Party Details (Seller - ASHKELON ENTERPRISES)
        party = PartyDetailsSchema(
            party_name="Ashkelon Enterprises (New)", # "ASHKELON ENTERPRISES"
            gst_in=dictExtractedData["SellerDetails"]["SellerGST"],      # "23AHAPB4403H1Z7"
            address_list=[dictExtractedData["SellerDetails"]["SellerAddress"]],  # "41 SIYAGANJ INDORE"
            state_name=dictExtractedData["SellerDetails"]["SellerState"],  # "Madhya Pradesh"
            country_name="India",
            mailing_name="Ashkelon Enterprises (New)"
        )

        # Consignee Details (Same as Buyer)
        consignee = ConsigneeDetailsSchema(
            gst_in="23**********1ZY",
            address_list=["207-208", "Industry House", "Indore"],
            state_name="Madhya Pradesh",
            country_name="India",
            mailing_name="Abhinav Infrabuild Private Limited"
        )

        # Get Inventory Items
        lsStockItemInfo =await CASHKELON_ENTERPRISES_XML.MSGetAllStockItemInfo(dictExtractedData)

        # Party Ledger Entry (Credited with total invoice amount)
        fTotalAmount = dictExtractedData.get("TotalAmount")  # -24190.0
        partyBillAllocation = BillAllocationSchema(
            name=dictExtractedData.get("InvoiceNo"),  # "AE/CR/24-25/1524"
            billtype="New Ref",
            amount=fTotalAmount
        )
        party_ledger_entry = LedgerEntrySchema(
            ledger_name=party.party_name,
            amount=fTotalAmount,
            is_deemed_positive=False,
            is_party_ledger=True,
            bill_allocation=partyBillAllocation
        )

        # CGST Ledger Entry (Debited)
        cgst_entry = LedgerEntrySchema(
            ledger_name="CGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][0]["TaxAmount"],  # -1845
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # SGST Ledger Entry (Debited)
        sgst_entry = LedgerEntrySchema(
            ledger_name="SGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][1]["TaxAmount"],  # -1845
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # Rounding Off Ledger Entry (Debited)
        fRoundingoffAmount = dictExtractedData.get("RoundingOff")  # 0
        roundoff_entry = LedgerEntrySchema(
            ledger_name="Round Off",
            amount=abs(fRoundingoffAmount) if fRoundingoffAmount < 0 else -fRoundingoffAmount,  # 0
            is_deemed_positive=True,
            is_party_ledger=False,
            round_type="Normal Rounding",
            round_limit=1,
            method_type="As Total Amount Rounding",
            gst_overridden=False,
            remove_zero_entries=True
        )

        # Create Voucher
        voucher = TallyPurchaseInventoryVoucherSchema(
            company_info=company,
            voucher_class="CSLV",
            party_details=party,
            consignee_details=consignee,
            voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
            invoice_date=dictExtractedData.get("InvoiceDate"),  # "20241114"
            invoice_no=dictExtractedData.get("InvoiceNo"),
            voucher_type="Av-Purchase - GST - Contract",
            ledger_entries=[party_ledger_entry, cgst_entry, sgst_entry, roundoff_entry],
            inventory_entries=lsStockItemInfo,
            udf_data=lsUDFData,
            
        )

        strXMLContent = voucher.to_string(pretty=True)
        return {
            "xmlContent": strXMLContent,
            "allStockItems": CASHKELON_ENTERPRISES_XML._mlsTotalStockItems,
            "nonExistingStockItems": CASHKELON_ENTERPRISES_XML._mlsNonExistingStockItems,
            "TallyStatus": "-",
            "AVComments": "-"
        }

    @staticmethod
    def MSGetStockItemName(dictStockItemDetails):
        # Extract item name and track stock items
        CASHKELON_ENTERPRISES_XML._mlsTotalStockItems.append(dictStockItemDetails)
        strStockItemName = dictStockItemDetails.get("ItemName")
        if not strStockItemName:
            CASHKELON_ENTERPRISES_XML._mlsNonExistingStockItems.append(dictStockItemDetails)
        return strStockItemName

    @staticmethod
    async def MSGetAllStockItemInfo(dictExtractedData):
        lsInventoryItemDetails = []
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictExtractedData,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
        except Exception as e:
            print(e)

        for index, (dictStockItemDetail, result) in enumerate(zip(dictExtractedData.get("ItemDetails", []), lsResult)):
            strStockItemName = result.get("bestMatch","")
            if not strStockItemName:
                continue


            # Extract discount rate and amount
            fDiscountRate = dictStockItemDetail.get("DiscountRate", 0)  # 0
            strBatchName = dictStockItemDetail.get("DesignName", "Primary Batch")  # "Primary Batch"
            strGodownName = "Main Location"

            # Amount after discount (taxable amount)
            iItemAmount = -dictStockItemDetail.get("Taxes")["MainTaxes"][0]["TaxableAmount"]  # e.g., -16500

            # Quantity and unit
            strQuantity = f"{dictStockItemDetail.get('Quantity')}"  # e.g., "1 PCS"

            # Rate (before discount)
            strRate = str(dictStockItemDetail.get("Rate"))  # e.g., "16500"

            batch_alloc = BatchAllocationSchema(
                godownname=strGodownName,
                batchname=strBatchName,
                destinationgodownname=strGodownName,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            # Accounting Allocation to Purchase Ledger
            accounting_alloc = AccountingAllocationSchema(
                ledgername="Av-Purchase - GST - Contract",
                amount=iItemAmount
            )

            # Rate Details for GST
            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=9)
            ]

            # Inventory Entry
            inv_entry = InventoryEntrySchema(
                stockitemname=strStockItemName,  # e.g., "GSH-500 PET-VIBRETOR Batch Primary Batch"
                gst_hsnname=dictStockItemDetail.get("HSNCode"),  # e.g., "********"
                rate=strRate,
                discount=fDiscountRate,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsInventoryItemDetails.append(inv_entry)

        return lsInventoryItemDetails
    
    
class CAAKhambatiAndSons_XML:
    _mlsTotalStockItems = []       # Tracks all stock items from the invoice
    _mlsNonExistingStockItems = [] # Tracks non-existing stock items

    @staticmethod
    async def MSGetTallyXML(iUserId, dictExtractedData, lsUDFData=[{}]):
        # Reset class variables
        CAAKhambatiAndSons_XML._mlsTotalStockItems = []
        CAAKhambatiAndSons_XML._mlsNonExistingStockItems = []

        # Company Information (Buyer)
        company = CompanyInfoSchema(
            company_name=CAbhinavInfrabuild._mstrCompanyName,
            gst_registration_type="Regular",
            gst_in="23**********1ZY"
        )

        # Party Details (Seller - A.A.KHAMBA'I & SONS)
        party = PartyDetailsSchema(
            party_name="A.A.Khambati & Sons",  # "A.A.KHAMBA'I & SONS"
            gst_in="23AACFA2860Q1ZD",      # "23AACFA2860Q1ZD"
            # address_list=[dictExtractedData["SellerDetails"]["SellerAddress"]],  # "84, SIYAGUNJ, INDORE"
            address_list=["84","SIYAGUNJ","INDORE"],
            state_name=dictExtractedData["SellerDetails"]["SellerState"],  # "Madhya Pradesh"
            country_name="India",
            mailing_name="A.A.Khambati & Sons"
        )

        # Consignee Details (Same as Buyer)
        consignee = ConsigneeDetailsSchema(
            gst_in="23**********1ZY",
            address_list=["207-208", "Industry House", "Indore"],
            state_name="Madhya Pradesh",
            country_name="India",
            mailing_name="Abhinav Infrabuild Private Limited"
        )

        # Get Inventory Items
        lsStockItemInfo =await CAAKhambatiAndSons_XML.MSGetAllStockItemInfo(dictExtractedData)

        # Party Ledger Entry (Credited with total invoice amount)
        fTotalAmount = dictExtractedData.get("TotalAmount")  # -6915.0
        partyBillAllocation = BillAllocationSchema(
            name=dictExtractedData.get("InvoiceNo"),  # "2608/AK/24-25 Delivery Note"
            billtype="New Ref",
            amount=fTotalAmount
        )
        party_ledger_entry = LedgerEntrySchema(
            ledger_name=party.party_name,
            amount=fTotalAmount,
            is_deemed_positive=False,
            is_party_ledger=True,
            bill_allocation=partyBillAllocation
        )

        # CGST Ledger Entry (Debited)
        cgst_entry = LedgerEntrySchema(
            ledger_name="CGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][0]["TaxAmount"],  # -527.4
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # SGST Ledger Entry (Debited)
        sgst_entry = LedgerEntrySchema(
            ledger_name="SGST",
            amount=-dictExtractedData["Taxes"]["MainTaxes"][1]["TaxAmount"],  # -527.4
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # Freight Ledger Entry (Debited)
        freight_charge = next((charge["ChargeAmount"] for charge in dictExtractedData.get("Charges", []) if charge["ChargeName"] == "Freight/Transportation Charge"), 0)
        freight_entry = LedgerEntrySchema(
            ledger_name="Freight Charges",
            amount=-freight_charge,  # -60.0
            is_deemed_positive=True,
            is_party_ledger=False
        )

        # Rounding Off Ledger Entry (Debited)
        fRoundingoffAmount = dictExtractedData.get("RoundingOff")  # 0.2
        roundoff_entry = LedgerEntrySchema(
            ledger_name="Round Off",
            amount=abs(fRoundingoffAmount) if fRoundingoffAmount < 0 else -fRoundingoffAmount,  # -0.2
            is_deemed_positive=True,
            is_party_ledger=False,
            round_type="Normal Rounding",
            round_limit=1,
            method_type="As Total Amount Rounding",
            gst_overridden=False,
            remove_zero_entries=True
        )

        # Create Voucher
        voucher = TallyPurchaseInventoryVoucherSchema(
            company_info=company,
            voucher_class="CSLV",
            party_details=party,
            consignee_details=consignee,
            voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
            # voucher_number=dictExtractedData.get("InvoiceNo"),
            invoice_date=dictExtractedData.get("InvoiceDate"),  # "20250210"
            invoice_no=dictExtractedData.get("InvoiceNo"),
            voucher_type="Av-Purchase - GST - Contract",
            ledger_entries=[party_ledger_entry, cgst_entry, sgst_entry, freight_entry, roundoff_entry],
            inventory_entries=lsStockItemInfo,
            udf_data=lsUDFData,
        )

        strXMLContent = voucher.to_string(pretty=True)
        return {
            "xmlContent": strXMLContent,
            "allStockItems": CAAKhambatiAndSons_XML._mlsTotalStockItems,
            "nonExistingStockItems": CAAKhambatiAndSons_XML._mlsNonExistingStockItems,
            "TallyStatus": "-",
            "AVComments": "-"
        }

    @staticmethod
    def MSGetStockItemName(dictStockItemDetails):
        # Extract item name and track stock items
        CAAKhambatiAndSons_XML._mlsTotalStockItems.append(dictStockItemDetails)
        strStockItemName = dictStockItemDetails.get("ItemName")
        if not strStockItemName:
            CAAKhambatiAndSons_XML._mlsNonExistingStockItems.append(dictStockItemDetails)
        return strStockItemName

    @staticmethod
    async def MSGetAllStockItemInfo(dictExtractedData):
        lsInventoryItemDetails = []
        try:
            strCompanyName = "abhinav"            # ex: parag | abhinav | gwalia
            strOpenAIModel = "gpt-4.1"

            matcher = CLedgerMatcher(strOpenAIModel)
            
            lsResult = await matcher.MSProcessJson(
                dictExtractedData,
                helperFunctionsInAsync.getEmbeddingsForSeries,
                strCompanyName
            )
        except Exception as e:
            print(e)

        for index, (dictStockItemDetail, result) in enumerate(zip(dictExtractedData.get("ItemDetails", []), lsResult)):
            strStockItemName = result.get("bestMatch","")
            if not strStockItemName:
                continue


            # Extract discount rate (default to 0)
            fDiscountRate = dictStockItemDetail.get("DiscountRate", 0)
            strBatchName = dictStockItemDetail.get("DesignName", "Primary Batch")
            strGodownName = "Main Location"

            # Amount after discount (taxable amount)
            iItemAmount = -dictStockItemDetail.get("AmountBeforeTaxesAndDiscounts")  # e.g., -2950.0

            # Quantity and unit
            strQuantity = f"{dictStockItemDetail.get('Quantity')}"  # e.g., "50 KGS"

            # Rate (before discount)
            strRate = str(dictStockItemDetail.get("Rate"))  # e.g., "59.0"

            batch_alloc = BatchAllocationSchema(
                godownname=strGodownName,
                batchname=strBatchName,
                destinationgodownname=strGodownName,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            # Accounting Allocation to Purchase Ledger
            accounting_alloc = AccountingAllocationSchema(
                ledgername="Av-Purchase - GST - Contract",
                amount=iItemAmount
            )

            # Rate Details for GST
            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=9),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=9)
            ]

            # Inventory Entry
            inv_entry = InventoryEntrySchema(
                stockitemname=strStockItemName,  # e.g., "NAIL 11/2X12"
                gst_hsnname=dictStockItemDetail.get("HSNCode"),  # "********"
                rate=strRate,
                discount=fDiscountRate,
                amount=iItemAmount,
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsInventoryItemDetails.append(inv_entry)

        return lsInventoryItemDetails

class CAbhinavInfrabuildMachineryGRN:
    _miTimeSavedPerGRNItem = 30  # Time (in seconds) saved per GRN item
    _mstrCompanyName = "Abhinav Infrabuild Pvt.Ltd.(24-26)"
    _mStrStoragePathDevelopement = dictProjectPaths.get("STORAGE_PATH_DEVELOPMENT", r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild")
    _mStrStoragePathProduction = dictProjectPaths.get("STORAGE_PATH_PRODUCTION", r"H:/AI Data/DailyData/AbhinavInfrabuild")
    
    @staticmethod
    def MSGetVendorDetails(machine_number: str) -> dict:
        vendor_mapping_file = dictProjectPaths.get("Abhinav_Vendor_Path",r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Resources/PartyDetails.json")
        vendor_info = CJSONFileReader.read_json_file(vendor_mapping_file)
        
        # Check if the machine number exists in the vendor details
        return vendor_info.get(machine_number, {})
    
    @staticmethod
    def MSGetMachineryMappingDetails(bDownloadERP = False) -> dict:
        """
        Fetch the mapping details from the MACHINERY sheet.
        """
        # Mapping Excel path
        # mapping_excel_file = CAbhinavInfrabuild._mstrMappingExcelPath
        # machinery_mapping_df,  = CExcelHelper.read_excel_sheets(mapping_excel_file, ["MACHINERY"])
        
         # Updated Code to fetch Item Mapping from Google drive
        try:
            drive_service = GoogleDriveService()
            df_mapping = drive_service.handle_file(bisMultipleSheet=True, download = bDownloadERP)
        except Exception as e:
                raise Exception("There was a problem in getting Item ERP Matching file")
        machinery_mapping_df = df_mapping["MACHINERY"]
        
        # Normalize the columns (if necessary)
        machinery_mapping_df['MACHINE NO'] = machinery_mapping_df['MACHINE NO'].astype(str).str.strip().str.lower()
        machinery_mapping_df['SITE NAME'] = machinery_mapping_df['SITE NAME'].astype(str).str.strip()
        machinery_mapping_df['ITEM'] = machinery_mapping_df['ITEM'].astype(str).str.strip()
        machinery_mapping_df['UNIT'] = machinery_mapping_df['UNIT'].astype(str).str.strip()
        machinery_mapping_df['PARTY NAME'] = machinery_mapping_df['PARTY NAME'].astype(str).str.strip()

        
        return machinery_mapping_df.set_index('MACHINE NO').to_dict(orient='index')
    
    @staticmethod
    def MSGetChallanNo(strDescription):
        strChallanNo = ""
        try:
            remark = str(strDescription)
            ref_no_match = re.search(r'\b\d{5}\b', remark)
            strChallanNo = ref_no_match.group() if ref_no_match else ""
        except Exception as e:
            print(f"Failed to fetch challan number from '{strDescription}'.")
            
        return strChallanNo
    
    # Update the method to adjust for actual columns in your GRN machinery Excel
    @staticmethod
    async def MSGetGRNXMLForMachinery(iUserID, iRequestID, grn_file_path: str, bDeveloperMode: bool, bDownloadERP = False):
        """
        Read the Machinery GRN Excel file, map the details, and generate GRN XML for machinery.
        """
        try:
            grn_processing_details = []
            strUniqueGRNNo = None
            
            # Read the GRN machinery Excel file with updated columns based on the image
            df_grn = CExcelHelper.read_file_temp(grn_file_path)
            
            # Adjusting column names according to the provided image and expected data
            df_grn.columns = [
                'Sno.', 'Machine Number', 'Date', 'Techometer Reading Opening', 'Techometer Reading Closing', 
                'Techometer Reading Run', 'Fuel Opening', 'Fuel Issue', 'Fuel Closing', 'Fuel Consump.', 
                'Time Opening', 'Time Closing', 'Time Total(HH:MM)', 'Average Lt/Hr', 'Average Km/Lt', 
                'Driver Name', 'Remark || Manual Remark'
            ]
            
            # Ensure the relevant columns are used
            df_grn['Machine Number'] = df_grn['Machine Number'].astype(str).str.strip()
            df_grn = df_grn[~df_grn['Machine Number'].str.contains('Grand Total :', case=False, na=False)]

            df_grn['Time Opening'] = pd.to_numeric(df_grn['Time Opening'], errors='coerce').fillna(0)
            df_grn['Time Closing'] = pd.to_numeric(df_grn['Time Closing'], errors='coerce').fillna(0)
            
            try:
                await CAVRequestDetail.MSUpdateRecordByClientREQID(RequestID=iRequestID, FileContent=df_grn.to_json(orient="records"))
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                print(traceback.print_exc())
                
                
            # Get machinery mapping details (Machine Number to Item Name, Site Name, etc.)
            machinery_mapping_dict = CAbhinavInfrabuildMachineryGRN.MSGetMachineryMappingDetails(bDownloadERP)
            
            for i, row in df_grn.iterrows():
                grn_data = {
                        "SR_NO.": i+1,
                        "XMLFilePath":"-",
                        "Machine No": "-",
                        "Date": "-",
                        "Item Name": "-",
                        "Cost Center": "-",
                        "Vendor Name": "-",
                        "Vendor Details": {},
                        "Quantity": "-",
                        "Unit": "-",
                        "Opening Time": "-", 
                        "Closing Time": "-",
                        "Machine Description": "-",
                        "Reference No": "-",
                        "Time Saved":"0 Seconds",
                        "Time Statistics": {"hours": 0, "minutes": 0, "seconds": 0},
                        "GRN Status": "Skipped",
                        "AV Comments": "-"
                    }
                strXmlData = None
                
                try:
                    machine_number = str(row['Machine Number']).strip()
                    grn_data["Machine No"] = machine_number
                    
                    machine_number_lower = str(row['Machine Number']).strip().lower()
                    
                    # Extract Time info and calculate the quantity
                    grn_date = row["Date"]
                    grn_data["Date"] = grn_date
                    
                    time_opening = row['Time Opening']
                    grn_data["Opening Time"] = time_opening
                    
                    time_closing = row['Time Closing']
                    grn_data["Closing Time"] = time_closing

                    quantity = time_closing - time_opening 
                    grn_data["Quantity"] = quantity

                    if machine_number_lower not in machinery_mapping_dict:
                        grn_data["GRN Status"] = "Skipped"
                        grn_data["AV Comments"] = f"Machine number '{machine_number}' could not be found in the reference Excel file."
                        
                        grn_processing_details.append({
                                                        "XMLFilePath": grn_data["XMLFilePath"],
                                                        "SR_NO.": grn_data["SR_NO."],
                                                        "Machine No": grn_data["Machine No"],
                                                        "Date": grn_data["Date"],
                                                        "Time Saved": grn_data["Time Saved"],
                                                        "Time_Statistics": grn_data["Time Statistics"],
                                                        "AV_Status": grn_data["GRN Status"],
                                                        "AV Comments": grn_data["AV Comments"]
                                                    })
                        continue
                    
                    else: 
                        
                        dictMachineryMappingFromExcel = machinery_mapping_dict[machine_number_lower]
                        
                        ledger_json_path = CAbhinavInfrabuild._mStrVendorDetailsPath
                        vendor_details = CJSONFileReader.get_supplier_details_from_ledger(dictMachineryMappingFromExcel['PARTY NAME'], ledger_json_path)
                        
                        # Get vendor name from the mapped data
                        vendor_name = vendor_details.get('party_name', '')
                        grn_data["Vendor Name"] = vendor_name
                        grn_data["Vendor Details"] = vendor_details
                        
                        # Get unit from the mapped data
                        unit = dictMachineryMappingFromExcel.get('UNIT', '')
                        grn_data["Unit"] = unit

                        if unit.lower() == "day":
                            quantity = 1          # If unit is Day, set quantity to 1
                            grn_data["Quantity"] = quantity
                            
                        if quantity == 0:
                            grn_data["GRN Status"] = "Skipped"
                            grn_data["AV Comments"] = f"Skipped due to zero quantity."
                            
                            grn_processing_details.append({
                                                        "XMLFilePath": grn_data["XMLFilePath"],
                                                        "SR_NO.": grn_data["SR_NO."],
                                                        "Machine No": grn_data["Machine No"],
                                                        "Date": grn_data["Date"],
                                                        "Time Saved": grn_data["Time Saved"],
                                                        "Time_Statistics": grn_data["Time Statistics"],
                                                        "AV_Status": grn_data["GRN Status"],
                                                        "AV Comments": grn_data["AV Comments"]
                                                    })
                            continue
                                       
                        # Check for 5-digit reference number in the Remark column using regex
                        reference_no = CAbhinavInfrabuildMachineryGRN.MSGetChallanNo(str(row['Remark || Manual Remark']))
                        if reference_no:
                            grn_data["Reference No"] = reference_no
                        else:
                            grn_data["Reference No"] = grn_data["Machine No"] 
                            
                        # Fetch item details from machinery mapping
                        item_name = dictMachineryMappingFromExcel.get('ITEM', '')
                        grn_data["Item Name"] = item_name

                        cost_center = dictMachineryMappingFromExcel.get('SITE NAME', '')
                        grn_data["Cost Center"] = cost_center

                        strUniqueGRNNo = f"{cost_center}_{machine_number}_{DateHelper.convert_date(grn_data['Date'].split(' ')[0])}"
                        if not bDeveloperMode:
                            bIsDuplicate = await CAVGRNProcessingDetailsService.MSIsDuplicateGRN(UserID=iUserID, GRNNumber=strUniqueGRNNo)
                            if bIsDuplicate:
                                grn_data["GRN Status"] = "Duplicate"
                                grn_data["AV Comments"] =  "Tally XML: Duplicate Entry Found in AccuVelocity."
                                grn_processing_details.append({
                                                                "XMLFilePath": grn_data["XMLFilePath"],
                                                                "SR_NO.": grn_data["SR_NO."],
                                                                "Machine No": grn_data["Machine No"],
                                                                "Date": grn_data["Date"],
                                                                "Time Saved": grn_data["Time Saved"],
                                                                "Time_Statistics": grn_data["Time Statistics"],
                                                                "AV_Status": grn_data["GRN Status"],
                                                                "AV Comments": grn_data["AV Comments"]
                                                            })
                                
                                await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=iUserID, RequestID=iRequestID, GRNNumber=strUniqueGRNNo, GRNDetails=grn_data, AVStatus=grn_data["GRN Status"], ErrorMessage=grn_data["AV Comments"])

                                continue
                            
                        # Generate XML for the machinery GRN
                        strXmlData = CAbhinavInfrabuildMachineryGRN.MSGenerateMachineryGRNXML(grn_data)
                        
                        # Save the GRN XML in the appropriate directory
                        strDownloadDirPath = CAbhinavInfrabuildMachineryGRN._mStrStoragePathDevelopement if bDeveloperMode else CAbhinavInfrabuildMachineryGRN._mStrStoragePathProduction
                        strDownloadDirPath = os.path.join(strDownloadDirPath, datetime.today().strftime("%Y_%m_%d"), "MachineryGRN")
                        os.makedirs(strDownloadDirPath, exist_ok=True)
                        
                        strXMLFileName = f"REQID_{iRequestID}_UID_{iUserID}_COSTCENTER_{cost_center}_VENDOR_{vendor_name}_{machine_number}_DATE_{DateHelper.convert_date(grn_date.split(' ')[0])}.xml"
                        strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                        bIsFileWritten = CFileHandler.MSWriteFile(
                                                                    strFilePath=strTodaysXmlFilePath,
                                                                    fileContent=strXmlData,
                                                                    strWriteMode="w",
                                                                    strEncoding="utf-8"
                                                                    )
                        if bIsFileWritten:
                            grn_data["XMLFilePath"] = strTodaysXmlFilePath
                            grn_data["GRN Status"] = "Success"
                            grn_data["AV Comments"] = "-"
                            grn_data["Time Saved"] = f"{CAbhinavInfrabuildMachineryGRN._miTimeSavedPerGRNItem} Seconds"
                            grn_data["Time Statistics"]["seconds"] = CAbhinavInfrabuildMachineryGRN._miTimeSavedPerGRNItem
            
                                        
                        # !!!!!!!!!!!!!!!!!!   UNCOMMENT BELOW CODE TO DIRECTLY EPORT XML TO TALLY   
                        # CAbhinavInfrabuild.MSExportTallyData(strTodaysXmlFilePath, url="http://192.168.68.54:10101/")
                        # !!!! !!! !!!!! !!! !!!! !!!!!  !!!! !!! !!!! !!!! !!! !!! !!!! !!!!
                        
                        
                except Exception as e:
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    print(traceback.print_exc())
                    
                grn_processing_details.append({
                        "XMLFilePath": grn_data["XMLFilePath"],
                        "SR_NO.": grn_data["SR_NO."],
                        "Machine No": grn_data["Machine No"],
                        "Date": grn_data["Date"],
                        "Time Saved": grn_data["Time Saved"],
                        "Time_Statistics": grn_data["Time Statistics"],
                        "AV_Status": grn_data["GRN Status"],
                        "AV Comments": grn_data["AV Comments"]
                    })
                
                await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=iUserID, RequestID=iRequestID, GRNNumber=strUniqueGRNNo, GRNDetails=grn_data, AVStatus=grn_data["GRN Status"], ErrorMessage=grn_data["AV Comments"], GeneratedXML=strXmlData)

                
            return grn_processing_details
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print(traceback.print_exc())
        return []
    
    @staticmethod
    def MSGenerateMachineryGRNXML(grn_data: dict) -> str:
        """
        Generate GRN XML for machinery from the provided data dictionary.
        """
        company_info = CompanyInfoSchema(
            company_name=CAbhinavInfrabuildMachineryGRN._mstrCompanyName,
            gst_in="23**********1ZY",
            gst_registration_type="Regular"
        )
        
        vendor_info = PartyDetailsSchema(
            party_name=grn_data['Vendor Name'],
            address_list=grn_data["Vendor Details"].get("address_list", []),
            gst_in=grn_data["Vendor Details"].get("gst_in", ""),
            state_name=grn_data["Vendor Details"].get("state_name", ""),
            gst_registration_type="Regular",
            country_name=grn_data["Vendor Details"].get("country_name", "")
        )
        
        buyer_info = PartyDetailsSchema(
            party_name="Abhinav Infrabuild Pvt.Ltd.",
            gst_in="23**********1ZY",
            gst_registration_type="Regular"
        )
        
        grn_narration = f"Machinery GRN for Machine No: {grn_data['Machine No']} | {grn_data['AV Comments']}"
        
        dAmount = 1.0
        # Build inventory entries for machinery
        inventory_entries = [
            InventoryEntrySchema(
                stockitemname=grn_data["Item Name"],
                is_deemed_positive=True,
                rate="0.00",  # Add appropriate rate here
                amount=-dAmount,  # Amount can be calculated based on rate and quantity
                actual_qty=f"{grn_data['Quantity']} {grn_data['Unit']}",
                billed_qty=f"{grn_data['Quantity']} {grn_data['Unit']}",
                description=f"Machine: {grn_data['Machine No']}  |  Opening Time: {grn_data['Opening Time']}  |  Closing Time: {grn_data['Closing Time']}",
                batch_allocations=[
                    BatchAllocationSchema(
                        tracking_no=grn_data["Reference No"],
                        godownname="Main Location",
                        batchname="Primary Batch",
                        amount=-dAmount,  # Calculate the amount here
                        actual_qty=f"{grn_data['Quantity']} {grn_data['Unit']}",
                        billed_qty=f"{grn_data['Quantity']} {grn_data['Unit']}",
                        orderduedate=DateHelper.convert_date(grn_data["Date"].split(" ")[0])
                    )
                ],
                accounting_allocations=[
                                            AccountingAllocationSchema(
                                                ledgername="Purchase Gst Services Received",
                                                gstclass="Not Applicable",
                                                is_deemed_positive=True,
                                                amount=-dAmount,
                                                category_allocations=[
                                                    CategoryAllocationSchema(
                                                        category="Primary Cost Category",
                                                        is_deemed_positive=True,
                                                        cost_center_allocations=[
                                                            CostCenterAllocationSchema(
                                                                name=grn_data["Cost Center"],  # Site name from machinery_dict added here
                                                                amount=-dAmount
                                                            )
                                                        ]
                                                    )
                                                ]
                                            )
                                        ]
            )
        ]
        
        # Build ledger entries for machinery
        ledger_entries = [
            LedgerEntrySchema(
                ledger_name="Purchase Gst Services Received",
                gst_class="Not Applicable",
                is_deemed_positive=True,
                amount=dAmount,  # Set appropriate amount here
                category_allocations=[
                    CategoryAllocationSchema(
                        category="Primary Cost Category",
                        is_deemed_positive=True,
                        cost_center_allocations=[
                            CostCenterAllocationSchema(name=grn_data['Cost Center'], amount=dAmount)
                        ]
                    )
                ]
            )
        ]
        
        # Create and return the machinery GRN XML
        grn_voucher = TallyGRNVoucherSchema(
            company_info=company_info,
            supplier_details=vendor_info,
            buyer_details=buyer_info,
            voucher_number="", #"grn_data["Machine No"]",
            reference_number=grn_data["Reference No"],
            cost_center=grn_data["Cost Center"],
            grn_date=DateHelper.convert_date(grn_data["Date"].split(" ")[0]),
            voucher_type="AV-MachineryGRN",
            narration=grn_narration,
            inventory_entries=inventory_entries,
            ledger_entries=ledger_entries,
            po_number="",
            po_date="",
            
        )
    
        
        return grn_voucher.to_string(pretty=True)



if __name__ == "__main__":
    iUserID = 4
    # strPOExcelPath = r"h:\Customers\Show Demo - AKS\Purchase Order\list_Excel5452.xls"
    # strOutputXMLData = asyncio.run(CAbhinavInfrabuild.MSGetAllPurchaseOrderXML(iUserID, strPOExcelPath, {}, bDeveloperMode=True))
    
    strGRNExcelFilePath = r"H:\AI Data\Abhinav Infrabuild PVT. LTD\Downloads\grn_register\2025-06-23\grn_register_08-01_list_Excel6230.xls" # Tested
    strOutputXMLData = asyncio.run(CAbhinavInfrabuildGRN.MSGetAllGRNXML(iUserID=iUserID, strExcelFilePath=strGRNExcelFilePath, bDeveloperMode=True,iRequestID="ABC"))
    
    # strGRNHTMLFilePath = r"GitIgnore\list_Excel8319_dup1.xls" #Tested
    # strOutputXMLData = asyncio.run(CAbhinavInfrabuildMachineryGRN.MSGetGRNXMLForMachinery(iUserID=iUserID, grn_file_path=strGRNHTMLFilePath, bDeveloperMode=True, iRequestID="ABC"))

    strFileName = r"GitIgnore\GRNXML\GRN.xml"
    os.makedirs(os.path.dirname(strFileName), exist_ok=True)
    with open(strFileName, "w") as file:
        # Handle based on type
        if isinstance(strOutputXMLData, list):
            strOutputXMLData = "".join(str(x) for x in strOutputXMLData)  # Convert each element to string and join
        elif not isinstance(strOutputXMLData, str):
            strOutputXMLData = str(strOutputXMLData)  # Convert to string if it’s another type
        file.write(strOutputXMLData)

    print(f"File Written Successfully {strFileName}")
