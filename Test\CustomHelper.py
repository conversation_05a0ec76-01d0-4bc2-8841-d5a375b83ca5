import logging
import os
import sys
import zipfile
import xml.etree.ElementTree as ET
import hashlib
import base64
import binascii
import csv
import traceback
from cryptography.fernet import Fernet
import jwt

# Global configuration variables
LOG_DIR = os.path.join("Test", "Logs")
SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')

# Create log directory before configuring logging
os.makedirs(LOG_DIR, exist_ok=True)

# Setup console logger
logging.basicConfig(level=logging.DEBUG, handlers=[
    logging.StreamHandler(sys.stdout),
    logging.FileHandler(os.path.join(LOG_DIR, "test_debug.log"))
])
console_logger = logging.getLogger(__name__)
console_logger.debug(f"Log directory created or exists: {LOG_DIR}")

class TestLogger:
    """Custom logger for test-related logging."""
    def __init__(self, log_dir, log_file):
        os.makedirs(log_dir, exist_ok=True)  # Ensure log_dir exists
        self.logger = logging.getLogger("TestLogger")
        self.logger.setLevel(logging.DEBUG)
        file_handler = logging.FileHandler(os.path.join(log_dir, log_file))
        file_handler.setFormatter(logging.Formatter('%(asctime)s - %(levelname)s - %(message)s'))
        self.logger.addHandler(file_handler)
        self.logger.addHandler(logging.StreamHandler(sys.stdout))

    def MSWriteLog(self, level, message):
        """Log a message at the specified level."""
        if level.lower() == "info":
            self.logger.info(message)
        elif level.lower() == "error":
            self.logger.error(message)
        elif level.lower() == "debug":
            self.logger.debug(message)
        else:
            self.logger.warning(f"Unknown log level {level}: {message}")

class CLicenseHelper:
    """Helper class for license verification."""
    @staticmethod
    def MSVerifyLicense(license_file):
        """
        Verify the license file and extract the JWT token.
        Args:
            license_file (str): Path to the license file.
        Returns:
            dict: Dictionary containing license data with 'Token' field.
        Raises:
            FileNotFoundError: If license file is not found.
            Exception: If decryption or JWT decoding fails.
        """
        try:
            console_logger.debug(f"Verifying license file: {license_file}")
            if not os.path.exists(license_file):
                raise FileNotFoundError(f"License file {license_file} not found")
            with open(license_file, "rb") as lic_file:
                encrypted_license = lic_file.read()
            cipher_suite = Fernet(SECRET_KEY)
            token = cipher_suite.decrypt(encrypted_license).decode("utf-8")
            dictLicenseData = jwt.decode(token, SECRET_KEY, algorithms=["HS256"])
            dictLicenseData["Token"] = token
            console_logger.debug(f"License verification succeeded for {license_file}")
            return dictLicenseData
        except Exception as e:
            console_logger.error(f"License verification failed: {str(e)}")
            console_logger.error(f"Traceback: {traceback.format_exc()}")
            raise

def calculate_checksum(file_path, algorithm="md5"):
    """
    Calculate checksum of a file using the specified algorithm.
    Args:
        file_path (str): Path to the file.
        algorithm (str): Hash algorithm ('md5', 'sha256', 'sha1').
    Returns:
        str: Hexadecimal checksum.
    """
    try:
        console_logger.debug(f"Calculating {algorithm.upper()} checksum for {file_path}")
        hash_obj = hashlib.md5() if algorithm == "md5" else hashlib.sha256() if algorithm == "sha256" else hashlib.sha1()
        with open(file_path, "rb") as f:
            for chunk in iter(lambda: f.read(4096), b""):
                hash_obj.update(chunk)
        checksum = hash_obj.hexdigest()
        console_logger.debug(f"Calculated {algorithm.upper()} checksum: {checksum}")
        return checksum
    except Exception as e:
        console_logger.error(f"Checksum calculation failed for {file_path}: {str(e)}")
        raise

def compare_xml(logger, file1, file2, ignore_tags=None):
    """
    Compare two XML files, ignoring specified tags.
    Args:
        logger: Logger instance for logging.
        file1 (str): Path to the first XML file.
        file2 (str): Path to the second XML file.
        ignore_tags (list): List of tags to ignore during comparison.
    Returns:
        tuple: (bAreSame, strLogMessage) indicating if XMLs match and a descriptive message.
    """
    if ignore_tags is None:
        ignore_tags = []
    try:
        tree1 = ET.parse(file1)
        tree2 = ET.parse(file2)
        root1 = tree1.getroot()
        root2 = tree2.getroot()
        for tag in ignore_tags:
            for elem in root1.findall(f".//{tag}"):
                elem.clear()
            for elem in root2.findall(f".//{tag}"):
                elem.clear()
        xml1_str = ET.tostring(root1, encoding="unicode")
        xml2_str = ET.tostring(root2, encoding="unicode")
        bAreSame = xml1_str == xml2_str
        strLogMessage = "XML files match" if bAreSame else "XML files differ after ignoring specified tags"
        logger.MSWriteLog("info", strLogMessage)
        return bAreSame, strLogMessage
    except Exception as e:
        strLogMessage = f"Error comparing XML files: {str(e)}"
        logger.MSWriteLog("error", strLogMessage)
        logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
        return False, strLogMessage

def compare_csv(logger, file1, file2, ignore_columns=None):
    """
    Compare two CSV files, ignoring specified columns.
    Args:
        logger: Logger instance for logging.
        file1 (str): Path to the first CSV file.
        file2 (str): Path to the second CSV file.
        ignore_columns (list): List of column names to ignore.
    Returns:
        tuple: (bAreSame, strLogMessage) indicating if CSVs match and a descriptive message.
    """
    if ignore_columns is None:
        ignore_columns = []
    try:
        with open(file1, 'r', newline='') as f1, open(file2, 'r', newline='') as f2:
            reader1 = csv.reader(f1)
            reader2 = csv.reader(f2)
            headers1 = next(reader1, None)
            headers2 = next(reader2, None)
            if headers1 is None or headers2 is None:
                strLogMessage = "One or both CSV files are empty or missing headers"
                logger.MSWriteLog("error", strLogMessage)
                return False, strLogMessage
            ignore_indices = []
            for col in ignore_columns:
                if col in headers1:
                    ignore_indices.append(headers1.index(col))
                    logger.MSWriteLog("info", f"Ignoring column '{col}' in file1")
                if col in headers2:
                    ignore_indices.append(headers2.index(col))
                    logger.MSWriteLog("info", f"Ignoring column '{col}' in file2")
            csv1 = [[val for i, val in enumerate(row) if i not in ignore_indices] for row in [headers1] + list(reader1)]
            csv2 = [[val for i, val in enumerate(row) if i not in ignore_indices] for row in [headers2] + list(reader2)]
            bAreSame = csv1 == csv2
            strLogMessage = "CSV files match after ignoring specified columns" if bAreSame else "CSV files differ after ignoring specified columns"
            logger.MSWriteLog("info", strLogMessage)
            return bAreSame, strLogMessage
    except Exception as e:
        strLogMessage = f"Error comparing CSV files: {str(e)}"
        logger.MSWriteLog("error", strLogMessage)
        logger.MSWriteLog("debug", f"Traceback: {traceback.format_exc()}")
        return False, strLogMessage

def extract_zip(zip_path, output_dir, expected_xml_pattern, expected_csv_pattern):
    """
    Extract all files from a ZIP file and return paths to XML and CSV files based on extensions.
    Args:
        zip_path (str): Path to the ZIP file.
        output_dir (str): Directory to extract files to.
        expected_xml_pattern (str): Pattern to match XML filename (ignored for extension-based selection).
        expected_csv_pattern (str): Pattern to match CSV filename (ignored for extension-based selection).
    Returns:
        tuple: (xml_path, csv_path) paths to extracted XML and CSV files (may be None if not found).
    """
    os.makedirs(output_dir, exist_ok=True)  # Ensure output_dir exists
    try:
        with zipfile.ZipFile(zip_path, "r") as zip_file:
            zip_contents = zip_file.namelist()
            console_logger.debug(f"ZIP contents: {zip_contents}")
            zip_file.extractall(output_dir)
            xml_path = None
            csv_path = None
            for filename in zip_contents:
                if filename.lower().endswith(".xml"):
                    xml_path = os.path.join(output_dir, filename)
                elif filename.lower().endswith(".csv"):
                    csv_path = os.path.join(output_dir, filename)
            if not xml_path:
                raise ValueError(f"No XML file found in ZIP")
            if not csv_path:
                raise ValueError(f"No CSV file found in ZIP")
            return xml_path, csv_path
    except Exception as e:
        console_logger.error(f"Error extracting ZIP: {str(e)}")
        raise