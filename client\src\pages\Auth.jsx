import logo from "../assets/logo.svg";
import { Link, useLocation, useNavigate } from "react-router-dom";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { useState } from "react";
import { IoMdEyeOff, IoMdEye } from "react-icons/io";
import * as yup from "yup";
import "react-phone-number-input/style.css";
import OtpModal from "../components/Auth/otpVerification";
import PhoneInput from "react-phone-number-input";
import { parsePhoneNumberFromString } from "libphonenumber-js";
import "../assets/PhoneInputStyle.css";
import ReCAPTCHA from "react-google-recaptcha";
import {
    usePageLimitLeft,
    useTotalPageLimitLeft,
} from "../context/TrailUsageUserData";
import Slider from "react-slick";
import {
    Box,
    Image,
    Input,
    SimpleGrid,
} from "@chakra-ui/react";
import Login_Image_1 from "../assets/Login_Image_1.svg";
import Login_Image_2 from "../assets/Login_Image_2.svg";
import Login_Image_3 from "../assets/Login_Image_3.svg";
import Login_Image_4 from "../assets/Login_Image_4.svg";
import Login_Image_5 from "../assets/Login_Image_5.svg";
import Login_Image_6 from "../assets/Login_Image_6.svg";
// import BlueYoutubeIcon from "../assets/BlueYoutubeIcon.svg";
// import BlueTwitterXIcon from "../assets/BlueTwitterXIcon.svg";
// import BlueLinkedInIcon from "../assets/BlueLinkedInIcon.svg";
// import BlueFacebookIcon from "../assets/BlueFacebookIcon.svg";
import { useUserNameSetter } from "../context/userData";

// const SOCIAL_MEDIA_LINKS = [
//     {
//         title: "Facebook",
//         link: "https://www.linkedin.com/company/AccuVelocity",
//         icon: <Image src={BlueLinkedInIcon} />,
//     },
//     {
//         title: "Twitter / X",
//         link: "https://twitter.com/AccuVelocity",
//         icon: <Image src={BlueTwitterXIcon} />,
//     },
//     {
//         title: "Instagram",
//         link: "https://www.facebook.com/AccuVelocity",
//         icon: <Image src={BlueFacebookIcon} />,
//     },
//     {
//         title: "LinkedIn",
//         link: "https://www.instagram.com/AccuVelocity",
//         icon: <Image src={BlueTwitterXIcon} />,
//     },
//     {
//         title: "Youtube",
//         link: "https://www.youtube.com/AccuVelocity",
//         icon: <Image src={BlueYoutubeIcon} />,
//     },
// ];
export default function Auth() {
    // const navigate = useNavigate();
    const location = useLocation();
    const active = location.state ? "login" : "signup";
    const [name, setName] = useState("");
    const [email, setEmail] = useState("");
    const [emailLogin, setEmailLogin] = useState("");
    const [passwordLogin, setPasswordLogin] = useState("");
    const [password, setPassword] = useState("");
    const [phoneNo, setPhoneNo] = useState("");
    const [showPassword, setShowPassword] = useState(false);
    const [showLoginPassword, setShowLoginPassword] = useState(false);
    const [agreedToPolicy, setAgreedToPolicy] = useState(false);
    // State to manage active tab
    const [activeTab, setActiveTab] = useState(active);
    // const [isModalOpen, setModalOpen] = useState(false);
    // const [isModalPOpen, setModalPOpen] = useState(false);
    const [isOtpModalOpen, setOtpModalOpen] = useState(false);
    const navigate = useNavigate();

    const [passwordCriteria, setPasswordCriteria] = useState({
        minLength: false,
        oneUppercase: false,
        oneNumber: false,
        oneSymbol: false,
    });
    const setPageLimitLeft = usePageLimitLeft();
    const setTotalPageLimitLeft = useTotalPageLimitLeft();
    const [captchaSolved, setCaptchaSolved] = useState(false); // State for CAPTCHA
    const [loginCaptchaSolved, setLoginCaptchaSolved] = useState(false);
    const handleCaptchaChange = (value) => {
        setCaptchaSolved(!!value); // Set state to true if CAPTCHA is solved
    };
    const handleLoginCaptchaChange = (value) => {
        setLoginCaptchaSolved(!!value); // Set state to true if CAPTCHA is solved
    };
    const setUserName = useUserNameSetter()
    const siteKey = import.meta.env.VITE_SITE_KEY;
    // Ensure you've imported the environment variable from the Vite environment.
    const serverUrl = import.meta.env.VITE_SERVER;

    yup.addMethod(yup.string, "phone", function (errorMessage) {
        return this.test("test-phone", errorMessage, function (value) {
            const { path, createError } = this;

            if (!value) return true; // If the field is optional

            const phoneNumber = parsePhoneNumberFromString(value);
            if (!phoneNumber || !phoneNumber.isValid()) {
                return createError({ path, message: errorMessage });
            }

            return true;
        });
    });

    const firstNameValidationSchema = yup.string()
        .trim()
        .test(
            'custom-validation',
            function (value) {
                const { path, createError } = this;

                if (!value) {
                    return createError({ path, message: 'Full Name is required' });
                }

                if (!/^[a-zA-Z\s]+$/.test(value)) {
                    return createError({ path, message: 'Full Name should contain only letters and spaces' });
                }

                if (value.length > 100) {
                    return createError({ path, message: 'Full Name must be less than 100 characters' });
                }

                const spaceCount = (value.match(/\s/g) || []).length;
                if (spaceCount > 3) {
                    return createError({ path, message: 'Full Name can contain at most three spaces' });
                }
                return true;
            }
        );

    // Define the password validation schema
    const passwordValidationSchema = yup.object({
        password: yup
            .string()
            .required("Password is required")
            .min(8, "Password must be at least 8 characters long")
            .max(25, "Password must be less than 25 characters long")
            .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
            .matches(/\d/, "Password must contain at least one number")
            .matches(
                /[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/,
                "Password must contain at least one symbol"
            ),
    });

    // Custom validation schema for email
    const emailValidationSchema = yup
        .string()
        .required("Email is required")
        .max(100, "Email must be less than 100 characters long") // Maximum length for email
        .matches(
            /^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$/,
            "Please enter a valid email address"
        );

    const loginPasswordValidationSchema = yup
        .string()
        .required("Password is required")
        .min(8, "Password must be at least 8 characters long")
        .max(25, "Password must be less than 25 characters long")
        .matches(/[A-Z]/, "Password must contain at least one uppercase letter")
        .matches(/\d/, "Password must contain at least one number")
        .matches(
            /[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/,
            "Password must contain at least one symbol"
        );

    const updatePasswordCriteria = (value) => {
        setPasswordCriteria({
            minLength: value.length >= 8,
            oneUppercase: /[A-Z]/.test(value),
            oneNumber: /\d/.test(value),
            oneSymbol: /[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(value),
        });
    };

    const validatePassword = async (value) => {
        updatePasswordCriteria(value); // Update the criteria every time the password changes
        try {
            await passwordValidationSchema.validate({ password: value });
            // You can set a state for the valid password if you want to display some message or indication
        } catch (error) {
            if (error.path === "password") {
                // Handle the specific error where password does not meet validation criteria
                throw new Error(
                    "Password must be 8+ characters, with uppercase, number, symbol, and no spaces"
                );
            } else {
                // Handle other validation errors
                throw error;
            }
        }
    };

    //validate login form
    const loginSchema = yup.object({
        emailLogin: emailValidationSchema,
        passwordLogin: loginPasswordValidationSchema,
    });

    //validate signup form
    const validationSchema = yup.object({
        name: firstNameValidationSchema,
        password: passwordValidationSchema.password,
        email: emailValidationSchema,
        phoneNo: yup.string().phone("Phone number is not valid"), // Use the custom 'phone' method
    });

    //validate signup form
    const validateForm = async () => {
        try {
            if (
                !captchaSolved &&
                serverUrl === "https://dev.accuvelocity.com/api"
            ) {
                toast.error("Please complete the CAPTCHA."); // Error message if CAPTCHA isn't solved
                return;
            }
            await validationSchema.validate(
                {
                    name,
                    email,
                    password,
                    phoneNo,
                },
                { abortEarly: false }
            ); // To check all fields and not stop at the first error
            handleRegister();
        } catch (error) {
            if (error.inner) {
                const passwordError = error.inner.find(
                    (err) => err.path === "password"
                );
                if (passwordError) {
                    toast.error(
                        "Password must be 8+ characters, with uppercase, number, symbol, and no spaces"
                    );
                    return;
                } else {
                    // Handle other errors
                    error.inner.forEach((err) => {
                        toast.error(err.message);
                    });
                }
            } else {
                toast.error(error.message);
            }
        }
    };

    //validate login form
    async function validateLoginForm() {
        try {
            await loginSchema.validate(
                { emailLogin, passwordLogin },
                { abortEarly: false }
            );
            handleLogin();
        } catch (error) {
            if (error.inner) {
                error.inner.forEach((err) => toast.error(err.message)); // Display errors
            } else {
                toast.error(error.message);
            }
        }
    }


    const toggleOtpModal = () => setOtpModalOpen((prev) => !prev);

    const handleTogglePasswordVisibility = () => setShowPassword(!showPassword);

    const handleToggleLoginPasswordVisibility = () =>
        setShowLoginPassword(!showLoginPassword);

    const handleRegister = async () => {
        // Check password validity and agreement to terms as before
        try {
            await passwordValidationSchema.validate({ password });
            if (!agreedToPolicy) {
                toast.error("You must agree to the terms and conditions to register");
                return;
            }
            try {
                const emailExists = await checkEmailExists(email);
                // console.log("emailExists", emailExists);
                if (emailExists.isEmailExist && emailExists.isEmailExist != null) {
                    toast.error(
                        "Email already exists. Please log in or reset your password if you've forgotten it."
                    );
                    return;
                } else if (
                    !emailExists.isValidEmail &&
                    emailExists.isValidEmail != null
                ) {
                    toast.error(
                        "Invalid email detected. Please use a valid, non-disposable email address to register. Contact <EMAIL> for assistance."
                    );
                    return;
                }
            } catch (error) {
                toast.error("There was an error checking the email. Please try again.");
                return;
            }

            // Instead of creating account, just show OTP verification window
            toggleOtpModal();
        } catch (error) {
            // Handle validation error as before
            toast.error("Password does not meet requirements.");
            return;
        }
    };
    // New function to handle account creation after OTP is verified

    const checkEmailExists = async (email) => {
        try {
            // Construct the URL with the email as a query parameter
            const response = await axios.get(
                `${import.meta.env.VITE_SERVER}/email-validation?email=${email}`
            );
            if (response.status === 200) {
                // Assuming the response format is { exists: true/false }
                return response.data;
            } else {
                // Handle non-200 responses
                console.error("Response was not OK:", response);
                throw new Error("Failed to check if email exists.");
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error("There was a problem with the Axios operation:", error);
            throw error; // Rethrow to be handled by the caller
        }
    };

    const handleLogin = async () => {
        // Check if CAPTCHA is solved, except for the specific test user
        if (
            !loginCaptchaSolved &&
            serverUrl === "https://dev.accuvelocity.com/api" &&
            (emailLogin !== "<EMAIL>" ||
                passwordLogin !== "Ridham@123")
        ) {
            toast.error("Please complete the CAPTCHA."); // Error message if CAPTCHA isn't solved
            return;
        }
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/login`,
                {
                    email: emailLogin,
                    password: passwordLogin,
                }
            );
            if (response.status === 200 && response.data !== null) {
                toast.success("Login successful");
                // Store token in local storage
                localStorage.setItem("token", response.data.jwt_token);
                localStorage.setItem("Role", response.data.role);
                localStorage.setItem("created", response.data.created_at);
                const userResponse = await axios.get(
                    `${import.meta.env.VITE_SERVER}/users/${response.data.uid}`,
                    {
                        headers: {
                            Authorization: `Bearer ${response.data.jwt_token}`,
                        },
                    }
                );

                const pageLimitLeft = userResponse.data.page_limit_left;
                const totalPageLimitLeft = userResponse.data.total_allowed_page_limit;
                const userName = userResponse.data.name;
                setUserName(userName);
                // Store user's page limit in local storage
                setPageLimitLeft(pageLimitLeft);
                setTotalPageLimitLeft(totalPageLimitLeft);
                setTimeout(() => {
                    window.location.href = "/";
                }, 500);
            } else {
                toast.error(response.message); // Fallback message
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error("Login failed:", error);
        }
    };
    var settings = {
        infinite: true,
        arrows: false,
        fade: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 10000,
    };
    return (
        <>
            <OtpModal
                isOpen={isOtpModalOpen}
                onClose={toggleOtpModal}
                email={email}
                name={name}
                password={password}
                phoneNo={phoneNo}
            />
            <Toaster position="top-center" />
            <SimpleGrid height={"100vh"} columns={2} >
                <Box height={"100vh"} className="overflow-auto">
                    <Box
                        mx={"auto"}
                        // className="max-w-md xlo:max-w-xl 2xlo:max-w-2xl lgo:mx-[14vw] smo:mx-[10%]"
                        className="max-w-lg"
                        // width={"fit-content"}
                        width={{ xl: "20vw", lg: "fit-content" }}
                        height={"fit-content"}
                        paddingY={"50px"}
                        paddingTop={activeTab === 'signup' && window.innerHeight < 900 ? '0px' : '50px'}
                    // overflow={"scroll"}
                    >
                        {" "}
                        <div className={`flex flex-col mb-6 smo:mt-[1vh] mdo:mt-[2vh] lgo:mt-[3vh] `}>
                            <div className="mb-6 flex items-center justify-center">
                                <img
                                    src={logo}
                                    alt="AccuVelocity"
                                    className="h-12 w-auto self-center"
                                />
                            </div>
                            <div className="flex bg-[#ECF0FF] p-1 rounded-lg">
                                <div
                                    className={`cursor-pointer text-sm px-4 py-2 ${activeTab === "signup"
                                        ? "bg-[#003654] text-white rounded-lg"
                                        : "bg-[#ECF0FF] text-[#9C9AA5] rounded-lg"
                                        } flex-1 text-center`}
                                    onClick={() => {
                                        setActiveTab("signup");
                                        navigate("/", { replace: true, state: null });
                                    }}
                                >
                                    Sign Up
                                </div>
                                <div
                                    className={`cursor-pointer text-sm px-4 py-2 ${activeTab === "login"
                                        ? "bg-[#003654] text-white rounded-lg"
                                        : "bg-[#ECF0FF] text-[#9C9AA5] rounded-lg"
                                        } flex-1 text-center`}
                                    onClick={() => setActiveTab("login")}
                                >
                                    Log in
                                </div>
                            </div>
                        </div>
                        <div className="flex-grow justify-center">
                            {activeTab === "signup" ? (
                                <>
                                    <form
                                        className="space-y-6"
                                        onSubmit={(e) => {
                                            e.preventDefault();
                                            validateForm();
                                        }}
                                    >
                                        <div>
                                            <label
                                                htmlFor="full-name"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Full Name<span className="text-red-500">*</span>
                                            </label>
                                            <Input
                                                id="full-name"
                                                type="text"
                                                required
                                                placeholder="Your full name"
                                                value={name}
                                                onChange={(e) => setName(e.target.value)}
                                                border={"1px solid #e2e8f0"}
                                                _focusVisible={{ border: "1px solid #e2e8f0" }}
                                                borderRadius={"8px"}
                                                height={"44px"}
                                            ></Input>
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="phone-number"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Phone number
                                            </label>
                                            <PhoneInput
                                                international
                                                defaultCountry="US"
                                                placeholder="Phone number"
                                                value={phoneNo}
                                                onChange={setPhoneNo}
                                            />
                                        </div>
                                        <div>
                                            <label
                                                htmlFor="email"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Work Email<span className="text-red-500">*</span>
                                            </label>

                                            <Input
                                                id="email"
                                                type="email"
                                                required
                                                placeholder="Work Email"
                                                value={email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                border={"1px solid #e2e8f0"}
                                                _focusVisible={{ border: "1px solid #e2e8f0" }}
                                                borderRadius={"8px"}
                                                height={"44px"}
                                            ></Input>
                                        </div>
                                        <div className="relative">
                                            <label
                                                htmlFor="password-login"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Password<span className="text-red-500">*</span>
                                            </label>
                                            <div className="mt-1 flex rounded-md shadow-sm">
                                                <Input
                                                    id="password-login"
                                                    type={showPassword ? "text" : "password"}
                                                    required
                                                    placeholder="Password"
                                                    value={password}
                                                    onChange={(e) => {
                                                        const trimmedValue = e.target.value.replace(
                                                            /\s/g,
                                                            ""
                                                        ); // Remove spaces from the input
                                                        setPassword(trimmedValue);
                                                        validatePassword(trimmedValue);
                                                    }}
                                                    pattern="^\S+$" // Pattern disallowing spaces
                                                    title="Password cannot contain spaces" // Error message for invalid pattern
                                                    border={"1px solid #e2e8f0"}
                                                    _focusVisible={{ border: "1px solid #e2e8f0" }}
                                                    borderRadius={"8px"}
                                                    height={"44px"}
                                                ></Input>
                                                <button
                                                    type="button"
                                                    onClick={handleTogglePasswordVisibility}
                                                    className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3"
                                                >
                                                    {showPassword ? (
                                                        <IoMdEyeOff
                                                            className="h-5 w-5"
                                                            aria-hidden="true"
                                                        />
                                                    ) : (
                                                        <IoMdEye className="h-5 w-5" aria-hidden="true" />
                                                    )}
                                                </button>
                                            </div>
                                            {/* Password validation feedback */}
                                            <ul className="list-none mt-2 mb-6">
                                                <li
                                                    className={
                                                        passwordCriteria.minLength
                                                            ? "text-green-500"
                                                            : "text-gray-500"
                                                    }
                                                >
                                                    {passwordCriteria.minLength ? (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M5 13l4 4L19 7"
                                                            />
                                                        </svg>
                                                    ) : (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M6 18L18 6M6 6l12 12"
                                                            />
                                                        </svg>
                                                    )}
                                                    At least 8 characters
                                                </li>
                                                <li
                                                    className={
                                                        passwordCriteria.oneUppercase
                                                            ? "text-green-500"
                                                            : "text-gray-500"
                                                    }
                                                >
                                                    {passwordCriteria.oneUppercase ? (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M5 13l4 4L19 7"
                                                            />
                                                        </svg>
                                                    ) : (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M6 18L18 6M6 6l12 12"
                                                            />
                                                        </svg>
                                                    )}
                                                    At least one uppercase letter
                                                </li>
                                                <li
                                                    className={
                                                        passwordCriteria.oneNumber
                                                            ? "text-green-500"
                                                            : "text-gray-500"
                                                    }
                                                >
                                                    {passwordCriteria.oneNumber ? (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M5 13l4 4L19 7"
                                                            />
                                                        </svg>
                                                    ) : (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M6 18L18 6M6 6l12 12"
                                                            />
                                                        </svg>
                                                    )}
                                                    At least one number
                                                </li>
                                                <li
                                                    className={
                                                        passwordCriteria.oneSymbol
                                                            ? "text-green-500"
                                                            : "text-gray-500"
                                                    }
                                                >
                                                    {passwordCriteria.oneSymbol ? (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M5 13l4 4L19 7"
                                                            />
                                                        </svg>
                                                    ) : (
                                                        <svg
                                                            className="h-4 w-4 inline mr-1"
                                                            fill="none"
                                                            viewBox="0 0 24 24"
                                                            stroke="currentColor"
                                                        >
                                                            <path
                                                                strokeLinecap="round"
                                                                strokeLinejoin="round"
                                                                strokeWidth="2"
                                                                d="M6 18L18 6M6 6l12 12"
                                                            />
                                                        </svg>
                                                    )}
                                                    At least one symbol
                                                </li>
                                            </ul>
                                        </div>

                                        <ReCAPTCHA
                                            sitekey={siteKey}
                                            onChange={handleCaptchaChange}
                                        />

                                        <div className="flex items-center">
                                            <input
                                                id="policy-agreement"
                                                type="checkbox"
                                                className="h-4 w-4 text-blue-600 focus:ring-blue-500 border-gray-300 rounded"
                                                checked={agreedToPolicy}
                                                onChange={(e) => setAgreedToPolicy(e.target.checked)}
                                            />
                                            <label className="ml-2 block text-sm text-gray-900">
                                                I agree to the{" "}
                                                <span
                                                    onClick={() =>
                                                        window.open("/assets/TermsofService.html", "_blank")
                                                    }
                                                    className="text-[#003654] cursor-pointer underline"
                                                >
                                                    Terms of Service
                                                </span>{" "}
                                                and{" "}
                                                <span
                                                    onClick={() =>
                                                        window.open("/assets/PrivacyPolicy.html", "_blank")
                                                    }
                                                    className="text-[#003654] underline cursor-pointer"
                                                >
                                                    Privacy Policy
                                                </span>
                                                .
                                            </label>
                                        </div>
                                        <div>
                                            <button
                                                type="submit"
                                                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                                            >
                                                Create Account
                                            </button>
                                        </div>
                                    </form>
                                </>
                            ) : (
                                <>
                                    <form
                                        className="space-y-6"
                                        onSubmit={(e) => {
                                            e.preventDefault();
                                            validateLoginForm();
                                        }}
                                    >
                                        <div>
                                            <label
                                                htmlFor="email-login"
                                                className="block text-sm font-medium text-gray-700"
                                            >
                                                Work Email<span className="text-red-500">*</span>
                                            </label>
                                            <Input
                                                id="email-login"
                                                type="email"
                                                required
                                                placeholder="Work Email"
                                                value={emailLogin}
                                                onChange={(e) => setEmailLogin(e.target.value)}
                                                border={"1px solid #e2e8f0"}
                                                _focusVisible={{ border: "1px solid #e2e8f0" }}
                                                borderRadius={"8px"}
                                                height={"44px"}
                                            ></Input>
                                        </div>
                                        <div className="relative">
                                            <div className="relative">
                                                <label
                                                    htmlFor="password-login"
                                                    className="block text-sm font-medium text-gray-700"
                                                >
                                                    Password<span className="text-red-500">*</span>
                                                </label>
                                                <div className="mt-1 flex rounded-md shadow-sm">
                                                    <Input
                                                        id="password-login"
                                                        type={showLoginPassword ? "text" : "password"}
                                                        required
                                                        placeholder="Password"
                                                        value={passwordLogin}
                                                        onChange={(e) => {
                                                            const trimmedValue = e.target.value.replace(
                                                                /\s/g,
                                                                ""
                                                            ); // Remove spaces from the input
                                                            setPasswordLogin(trimmedValue);
                                                        }}
                                                        pattern="^\S+$" // Pattern disallowing spaces
                                                        title="Password cannot contain spaces" // Error message for invalid pattern
                                                        border={"1px solid #e2e8f0"}
                                                        _focusVisible={{ border: "1px solid #e2e8f0" }}
                                                        borderRadius={"8px"}
                                                        height={"44px"}
                                                    ></Input>

                                                    <button
                                                        type="button"
                                                        onClick={handleToggleLoginPasswordVisibility}
                                                        className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3"
                                                    >
                                                        {showLoginPassword ? (
                                                            <IoMdEyeOff
                                                                className="h-5 w-5"
                                                                aria-hidden="true"
                                                            />
                                                        ) : (
                                                            <IoMdEye className="h-5 w-5" aria-hidden="true" />
                                                        )}
                                                    </button>
                                                </div>
                                            </div>
                                        </div>

                                        <ReCAPTCHA
                                            sitekey={siteKey}
                                            onChange={handleLoginCaptchaChange}
                                        />

                                        <div className="mt-6 text-start">
                                            <Link
                                                to="/recover-password"
                                                className="text-sm text-[#003654] hover:underline"
                                            >
                                                Forgot Password?
                                            </Link>
                                        </div>
                                        <div>
                                            <button
                                                type="submit"
                                                className="w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                                            >
                                                Login
                                            </button>
                                        </div>
                                    </form>
                                </>
                            )}
                        </div>
                    </Box>
                </Box>
                <Box height={"100vh"} overflow={"hidden"}>
                    <Box position={"relative"}>
                        {" "}
                        <Slider {...settings}>
                            {[
                                { id: 1, image: Login_Image_1 },
                                { id: 2, image: Login_Image_2 },
                                { id: 3, image: Login_Image_3 },
                                { id: 4, image: Login_Image_4 },
                                { id: 5, image: Login_Image_5 },
                                { id: 6, image: Login_Image_6 },
                            ].map((item) => {
                                return (
                                    <Box key={item.id} position={"relative"}>


                                        <Image
                                            src={item.image}
                                            alt="Decorative"
                                            objectFit={"cover"}
                                            minH={"100vh"}
                                            height={"100vh"}
                                            className="max-w-full max-h-full"
                                        />
                                    </Box>
                                );
                            })}
                        </Slider>
                    </Box>
                </Box>
            </SimpleGrid>
        </>
    );
}
