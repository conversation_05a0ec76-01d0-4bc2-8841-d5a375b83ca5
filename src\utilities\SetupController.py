import traceback
from typing import Optional, List
import json
import sys
sys.path.append("")
import pytz
from fastapi import FastAPI, HTTPException
from pydantic import BaseModel, EmailStr
from sqlalchemy import select
from sqlalchemy.exc import IntegrityError

# Import your asynchronous session and models (adjust import paths as needed)
from config.db_config import AsyncSessionLocal
from src.Models.models import (
    User,
    Role,
    UserAPIUsage,
    TallyUserConfig,
    ModelTable,
    Prompt
)
from src.utilities.helperFunc import Hashing
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.auth_controller import CAuthController
from src.Controllers.Tally_Controller import CTallyController
from fastapi import APIRouter, HTTPException

# Declaring router
setupapp = APIRouter(tags=['TallyUserSetup'],  prefix="/api")
# ---------------------------
# Pydantic models for request
# ---------------------------
class ModelData(BaseModel):
    model_name: str
    family_name: str
    prompt: str
    description: Optional[str] = None

class UserData(BaseModel):
    email: EmailStr
    password: str
    name: str
    phoneNumber: Optional[str] = None
    Country: Optional[str] = None
    profilePicture: Optional[str] = None  # e.g. Base64 encoded string
    usePaidOCR: Optional[bool] = False
    usePaidDocExtractor: Optional[bool] = False

class SetupUserRequest(BaseModel):
    user_id:Optional[int] = None
    user_data: Optional[UserData] = None
    models_data: List[ModelData] = []


# -------------------------------------------------
# Controller Class with your static method included
# -------------------------------------------------
class UserSetupController:
    @staticmethod
    async def MSSetupUser(
        user_id: Optional[int] = None,
        user_data: Optional[dict] = None,
        models_data: List[dict] = []
    ):
        """
        Purpose:
        Sets up a user and associated application data.
        - If a user_id is provided, then it configures additional tables for that user.
        - Otherwise, it creates a new user using the provided user_data (after checking for duplicate email)
        and then configures the associated tables.
        - For each dict in models_data (each must include "model_name", "family_name", and "prompt"),
        it creates (if not already present) a ModelTable row and a related Prompt row.

        Parameters:
        - user_id (Optional[int]): Existing user id to use. If not provided, a new user is created.
        - user_data (Optional[dict]): Dictionary of user attributes.
            Expected keys include: "email", "password", "name", and optionally "phoneNumber", "Country",
            "profilePicture", "usePaidOCR", "usePaidDocExtractor", etc.
        - models_data (List[dict]): List of dictionaries each having:
            - "model_name": (str) Name of the model.
            - "family_name": (str) Family name.
            - "prompt": (str) Prompt text.
            Optionally, a "description" key may be provided.
        
        Returns:
        - The created or updated User object.
        
        Raises:
        - HTTPException: If required data is missing or a duplicate is detected.
        """
        async with AsyncSessionLocal() as session:
            new_user_id = None
            try:
                # If a user_id is provided, fetch the existing user.
                if user_id is not None:
                    result = await session.execute(select(User).filter_by(uid=user_id))
                    user = result.scalars().first()
                    if not user:
                        raise HTTPException(status_code=404, detail="User not found.")
                else:
                    # For a new user, user_data must be provided.
                    if not user_data:
                        raise HTTPException(
                            status_code=400,
                            detail="User data is required for new user registration."
                        )
                    
                    # Check for duplicate email.
                    email = user_data.get("email", "").lower()
                    result = await session.execute(select(User).filter(User.email == email))
                    existing_user = result.scalars().first()
                    if existing_user:
                        raise HTTPException(status_code=400, detail=f"User already exists with provided email, user id: {existing_user}.")
                    
                    # Hash the password using the helper function.
                    hashed_password = Hashing.get_hash(user_data.get("password"))
                    
                    # Determine roleID. If not provided in user_data, default to Role 'General'.
                    if "roleID" in user_data:
                        role_id = user_data["roleID"]
                    else:
                        role_result = await session.execute(select(Role).filter(Role.RoleName == "General"))
                        role_obj = role_result.scalars().first()
                        if not role_obj:
                            raise HTTPException(status_code=400, detail="Default role 'General' not found.")
                        role_id = role_obj.Id
                    
                    # Create the new user.
                    new_user = User(
                        email=email,
                        password=hashed_password,
                        name=user_data.get("name"),
                        roleID=role_id,
                        phoneNumber=user_data.get("phoneNumber"),
                        Country=user_data.get("Country"),
                        profilePicture=user_data.get("profilePicture"),
                        usePaidOCR=user_data.get("usePaidOCR", False),
                        usePaidDocExtractor=user_data.get("usePaidDocExtractor", False)
                    )
                    session.add(new_user)
                    await session.commit()
                    await session.refresh(new_user)
                    user = new_user
                    new_user_id = user.uid

                # --- Set up UserAPIUsage if not already present ---
                result = await session.execute(select(UserAPIUsage).filter_by(user_id=user.uid))
                usage = result.scalars().first()
                if not usage:
                    usage = UserAPIUsage(user_id=user.uid)
                    session.add(usage)
                
                # --- Set up TallyUserConfig if not already present ---
                result = await session.execute(select(TallyUserConfig).filter_by(UserID=user.uid))
                tally_config = result.scalars().first()
                if not tally_config:
                    tally_config = TallyUserConfig(UserID=user.uid, AuthKey="", TallyHeaderObj={})
                    session.add(tally_config)
                
                # --- Process each model entry in models_data ---
                for entry in models_data:
                    model_name = entry.get("model_name")
                    family_name = entry.get("family_name")
                    prompt_text = entry.get("prompt")
                    
                    if not model_name or not family_name or not prompt_text:
                        raise HTTPException(
                            status_code=400,
                            detail="Each model entry must include 'model_name', 'family_name', and 'prompt'."
                        )
                    
                    # Check for duplicate ModelTable entry for this user.
                    result = await session.execute(
                        select(ModelTable).filter_by(UserID=user.uid, Name=model_name, FamilyName=family_name)
                    )
                    model_obj = result.scalars().first()
                    if not model_obj:
                        model_obj = ModelTable(
                            UserID=user.uid,
                            preDefinedModelDict={},  # Set an appropriate default if needed.
                            Name=model_name,
                            FamilyName=family_name,
                            Description=entry.get("description")
                        )
                        session.add(model_obj)
                        await session.commit()
                        await session.refresh(model_obj)
                    
                    # Check for duplicate Prompt entry.
                    result = await session.execute(
                        select(Prompt).filter_by(UserID=user.uid, ModelId=model_obj.Id, prompt=prompt_text)
                    )
                    prompt_obj = result.scalars().first()
                    if not prompt_obj:
                        prompt_obj = Prompt(
                            UserID=user.uid,
                            ModelId=model_obj.Id,
                            ModelSeries=f"{family_name}_{model_name}_GPT_General",
                            prompt=prompt_text
                        )
                        session.add(prompt_obj)
                
                await session.commit()
                await CTallyController.MSManageIntegration(iUserID=user.uid, authKey="", action="subscribe")
                await CLogController.MSWriteLog(user.uid, "Info", f"User setup completed successfully for user id {user.uid}.")
                return user
            
            except HTTPException as http_exc:
                if new_user_id:
                    await CAuthController.MSDeleteUser(user_id=new_user_id)
                await session.rollback()
                await CLogController.MSWriteLog(None, "Debug", f"User setup failed. Traceback: {traceback.format_exc()}")
                raise http_exc
            
            except IntegrityError as ie:
                await session.rollback()
                if new_user_id:
                    await CAuthController.MSDeleteUser(user_id=new_user_id)
                await CLogController.MSWriteLog(None, "Debug", f"User setup integrity error. Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="Registration failed due to integrity error.")
            
            except Exception as e:
                await session.rollback()
                if new_user_id:
                    await CAuthController.MSDeleteUser(user_id=new_user_id)
                await CLogController.MSWriteLog(None, "Debug", f"User setup exception. Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="An error occurred during user setup.")

    def MSPrintPrompt(strPromptFilePath):
        try:
            with open(strPromptFilePath, "r") as file:
                strFileContent = file.read()
                strFileContent = json.dumps(strFileContent)
                print(str(strFileContent))
        except Exception as e:
            print(f"Failed to print prompt, Error:{e}")
            
            
# ------------------------------------
# FastAPI Endpoint using the controller
# ------------------------------------
@setupapp.post("/setup_user")
async def setup_user_endpoint(payload: SetupUserRequest):
    try:
        # Call the static method with the provided user_data and models_data
        user = await UserSetupController.MSSetupUser(
            user_id = payload.user_id if payload.user_id else None,
            user_data=payload.user_data.dict() if payload.user_data else None,
            models_data=[model.dict() for model in payload.models_data]
        )
        return {
            "uid": user.uid,
            "email": user.email,
            "name": user.name
        }
    except HTTPException as http_exc:
        raise http_exc
    except Exception as exc:
        raise HTTPException(status_code=500, detail=str(exc))


# -----------------------
# Run the FastAPI server
# -----------------------
if __name__ == "__main__":
    pass
    # import uvicorn
    # uvicorn.run(app, host="0.0.0.0", port=8123)
