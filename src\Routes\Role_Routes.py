# Importing libraries
from fastapi import APIRouter, Query
from src.Controllers.Role_Controller import CRoleController
from src.Schemas.Role_Schema import CreateRole, UpdateRole
from src.middleware.checkAuth import user_required
from fastapi import APIRouter, Depends

RoleRouter = APIRouter(tags=['Role'],  prefix="/api/role")

@RoleRouter.get('/All')
async def GetAllRole(iLimit: int = Query(10, gt=0), iOffset: int = Query(0, ge=0), iUserID:int=Depends(user_required)):
    return await CRoleController.MSGetAllRoles(iUserID, iLimit, iOffset)

@RoleRouter.post('/Add')
async def CreateRole(objCreateRole: CreateRole, iUserID:int=Depends(user_required)):
    return await CRoleController.MSCreateRole(iUserID, objCreateRole=objCreateRole)

@RoleRouter.put('/Update')
async def UpdateRoleDetails(iRoleId , objRole: UpdateRole, iUserID:int=Depends(user_required)):
    return await CRoleController.MUpdateRoleDetails(iUserID, iRoleId, objRole)

@RoleRouter.delete('/Delete/{iRoleId}')
async def DeleteRole(iRoleId: int,  iUserID:int=Depends(user_required)):
    """
    Delete a role by its ID.
    
    - **iRoleId**: Integer ID of the role to be deleted.
    """
    return await CRoleController.MSDeleteRole(iUserID, iRoleId)

@RoleRouter.get('/{iRoleId}')
async def GetRoleById(iRoleId: int,  iUserID:int=Depends(user_required)):
    """
    Get details of a specific role by its ID.
    
    - **iRoleId**: Integer ID of the role.
    """
    return await CRoleController.MSGetRoleById(iUserID, iRoleId)

