{"type": "json_schema", "json_schema": {"name": "GEBERIT", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "TaxInvoiceNo": {"type": "string", "description": "Invoice number generaaly start with 'TX'"}, "DocRefNo": {"type": "integer", "description": "Document reference number of the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyyyy format) on which the invoice was issued."}, "ConfirmDated": {"type": "integer", "description": "Date when the confirmation was issued (in ddmmyy format)"}, "DeliveryNo": {"type": "integer", "description": "Delivery number associated with the invoice"}, "SellerGSTNo": {"type": "string", "description": "GST Identification Number of the seller"}, "CustomerGSTNo": {"type": "string", "description": "GSTIN of the customer"}, "ContactPerson": {"type": "string", "description": "Name of the person to be contacted regarding the order or transaction."}, "SubTotal": {"type": "number", "description": "The total amount for the invoice before taxes and additional charges."}, "FreightCharges": {"type": "number", "description": "The cost associated with the transportation or shipping of goods, excluding taxes and additional charges."}, "IGSTRate": {"type": "integer", "description": "The rate of Integrated Goods and Services Tax (IGST) applicable to the invoice, expressed as a percentage."}, "IGSTAmount": {"type": "number", "description": "The total amount of Integrated Goods and Services Tax (IGST) calculated for the invoice."}, "TotalAmountINR": {"type": "number", "description": "The total invoice amount in Indian Rupees (INR), including all applicable taxes and charges."}, "Table": {"type": "array", "description": "List of items in the invoice", "items": {"type": "object", "properties": {"Position": {"type": "string", "description": "Position or serial number of the item"}, "ArticleNo": {"type": "string", "description": "Article number or product code of the item"}, "Description": {"type": "string", "description": "Description of the item including specifications ignore in (Barcket text)"}, "HSNNo": {"type": "integer", "description": "HSN code of the item"}, "NetPrice": {"type": "number", "description": "Net price of the item per unit in INR"}, "Quantity": {"type": "number", "description": "Quantity of the item"}, "Unit": {"type": "string", "description": "Unit of measurement for the item (e.g., PC)"}, "TotalAmount": {"type": "number", "description": "Total amount for the item in INR"}}, "required": ["Position", "ArticleNo", "Description", "HSNNo", "NetPrice", "Quantity", "Unit", "TotalAmount"], "additionalProperties": false}}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "TaxInvoiceNo", "DocRefNo", "InvoiceDate", "ConfirmDated", "DeliveryNo", "SellerGSTNo", "CustomerGSTNo", "<PERSON><PERSON><PERSON>", "SubTotal", "FreightCharges", "IGSTRate", "IGSTAmount", "TotalAmountINR", "Table"], "additionalProperties": false}}}