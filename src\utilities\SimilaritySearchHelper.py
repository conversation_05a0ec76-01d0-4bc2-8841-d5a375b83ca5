import pandas as pd
import re
import asyncio
import numpy as np
from difflib import SequenceMatcher  # Import for string similarity scoring

class CSimilaritySearchHelper:
    """
    A helper class for searching and matching similar stock items from a master ledger.

    Purpose:
    - This class is designed to search and map input text to the most similar stock item in a master ledger.
    - It uses text normalization and filtering based on numeric tokens to improve matching accuracy.

    Input:
    - strMasterLedgerFile (str): The file path of the master ledger Excel file.
    - strCollectionKey (str): The column name to search in the master ledger (default is "STOCKITEM").
    - floatSimilarityThreshold (float): The threshold for string similarity score to consider a match (default is 0.5).

    Output:
    - strMappedLedgerEntry (str): The best matching ledger entry for the input text.
    - floatConfidenceScore (float): The similarity score of the best match.

    Example:
    ```
    async def main():
        strMasterLedgerFile = r"FilteredStockItemExport_30_01_2025_20_48.xlsx"
        helper = CSimilaritySearchHelper(strMasterLedgerFile)

        strInputText = "A-1950-WASH BASIN WHITE"
        strMappedLedger, floatConfidence = await helper.get_mapped_ledger(strInputText)

        print("Original in Tax Invoice:", strInputText)
        print("Mapped Ledger:", strMappedLedger)
        print("Confidence Score:", floatConfidence)

    asyncio.run(main())
    ```
    """

    def __init__(
        self, 
        strMasterLedgerFile: str, 
        strCollectionKey: str = "STOCKITEM", 
        floatSimilarityThreshold: float = 0.5
    ):
        """
        Initializes the CSimilaritySearchHelper class.

        Purpose:
        - To set up the initial parameters for the similarity search helper.

        Input:
        - strMasterLedgerFile (str): The file path of the master ledger Excel file.
        - strCollectionKey (str): The column name to search in the master ledger (default is "STOCKITEM").
        - floatSimilarityThreshold (float): The threshold for string similarity score to consider a match (default is 0.5).

        Output:
        - None
        """
        self.strMasterLedgerFile = strMasterLedgerFile
        self.floatSimilarityThreshold = floatSimilarityThreshold  # Set threshold to 0.5
        self.strCollectionKey = strCollectionKey
        self.lstMasterLedger = self.load_master_ledger()

    def load_master_ledger(self):
        """
        Loads master ledger from an Excel file and returns the specified column as a list.

        Purpose:
        - To read the master ledger Excel file and extract the specified column as a list of stock items.

        Input:
        - None

        Output:
        - list: A list of stock items from the specified column in the master ledger.
        """
        dfMasterLedger = pd.read_excel(self.strMasterLedgerFile)
        return dfMasterLedger[self.strCollectionKey].tolist()

    def normalize_text(
        self, 
        strText: str
    ):
        """
        Removes spaces and special characters to normalize the text.

        Purpose:
        - To clean and normalize the input text by removing spaces and special characters.

        Input:
        - strText (str): The input text to be normalized.

        Output:
        - str: The normalized text.
        """
        return re.sub(r'[^a-zA-Z0-9]', '', strText.lower())

    def get_first_token(
        self, 
        strText: str
    ):
        """
        Extracts the first numeric token (e.g., '2567BG' → '2567').

        Purpose:
        - To extract the first numeric token from the input text.

        Input:
        - strText (str): The input text from which to extract the numeric token.

        Output:
        - str or None: The extracted numeric token or None if no numeric token is found.
        """
        match = re.search(r'\b(\d+)', strText)  # Extracts only the numeric part
        return match.group(1) if match else None

    def filter_by_first_token(
        self, 
        strFirstToken: str
    ):
        """
        Filters master ledger based on the first numeric token extracted from the input text.

        Purpose:
        - To filter the master ledger entries that contain the first numeric token.

        Input:
        - strFirstToken (str): The first numeric token extracted from the input text.

        Output:
        - list: A list of filtered ledger entries containing the numeric token.
        """
        if not strFirstToken:
            return []

        lstFilteredLedger = [strEntry for strEntry in self.lstMasterLedger if strFirstToken in strEntry]
        return lstFilteredLedger

    def get_best_match(
        self, 
        strInputText: str, 
        lstFilteredLedger: list
    ):
        """
        Finds the most similar entry in the filtered ledger list based on text similarity.

        Purpose:
        - To find the most similar ledger entry to the input text using string similarity scoring.

        Input:
        - strInputText (str): The input text to be matched.
        - lstFilteredLedger (list): The filtered list of ledger entries to search for the best match.

        Output:
        - tuple: A tuple containing the best matching ledger entry (str) and the similarity score (float).
        """
        strInputNorm = self.normalize_text(strInputText)
        strBestMatch = None
        floatBestScore = 0.0

        for strItem in lstFilteredLedger:
            strItemNorm = self.normalize_text(strItem)
            floatSimilarity = SequenceMatcher(None, strInputNorm, strItemNorm).ratio()

            if floatSimilarity > floatBestScore:
                strBestMatch = strItem
                floatBestScore = floatSimilarity

        # Apply threshold condition
        if floatBestScore < self.floatSimilarityThreshold:
            return None, 0.0  # Return None if confidence is below 0.5

        return strBestMatch, floatBestScore

    async def get_mapped_ledger(
        self, 
        strInputText: str
    ):
        """
        Maps the input text to the best-matching ledger entry after filtering by first token.

        Purpose:
        - To find and return the best matching ledger entry for the given input text.

        Input:
        - strInputText (str): The input text to be mapped.

        Output:
        - tuple: A tuple containing the mapped ledger entry (str) and the confidence score (float).
        """
        strFirstToken = self.get_first_token(strInputText)
        lstFilteredLedger = self.filter_by_first_token(strFirstToken)

        if not lstFilteredLedger:
            return None, 0.0  # No match found

        strMappedLedger, floatConfidence = self.get_best_match(strInputText, lstFilteredLedger)
        return strMappedLedger, floatConfidence



class CSimilaritySearchHelperAquent:
    """
    A helper class for searching and matching similar stock items from a master ledger.

    Purpose:
    - This class is designed to search and map input text to a matching stock item in a master ledger.
    - It first attempts an exact match. If none is found, it will extract a product code (e.g., "A-7068 RGW")
      and then use fuzzy matching to select the best available candidate.
      
    Input:
    - strMasterLedgerFile (str): The file path of the master ledger Excel file.
    - strCollectionKey (str): The column name to search in the master ledger (default is "STOCKITEM").
    - floatSimilarityThreshold (float): The threshold for string similarity score (default is 0.5).

    Output:
    - strMappedLedgerEntry (str): The matching ledger entry or "-" if no match.
    - floatConfidenceScore (float): The confidence score (1.0 for exact match, or the similarity score otherwise).
    """

    def __init__(
        self, 
        strMasterLedgerFile: str, 
        strCollectionKey: str = "StockItemName", 
        floatSimilarityThreshold: float = 0.5
    ):
        self.strMasterLedgerFile = strMasterLedgerFile
        self.floatSimilarityThreshold = floatSimilarityThreshold
        self.strCollectionKey = strCollectionKey
        self.lstMasterLedger = self.load_master_ledger()

    def load_master_ledger(self):
        """
        Loads the master ledger from an Excel file and returns the specified column as a list.
        """
        dfMasterLedger = pd.read_excel(self.strMasterLedgerFile)
        return dfMasterLedger[self.strCollectionKey].tolist()

    def normalize_text(self, strText: str):
        """
        Removes spaces and special characters to normalize the text.
        """
        return re.sub(r'[^a-zA-Z0-9]', '', strText.lower())

    def get_first_token(self, strText: str):
        """
        Extracts a product code by using the first two tokens. If the second token contains a hyphen,
        only the part before the hyphen is used. For example:
            "A-7068 RGW-ROSE GOLD WHITE COLOUR WASH BASIN" returns "A-7068 RGW"
        """
        tokens = strText.split()
        if len(tokens) >= 2:
            # From the second token, take only the part before a hyphen (if present)
            second_token = tokens[1].split('-')[0]
            return f"{tokens[0]} {second_token}"
        return tokens[0]

    def filter_by_first_token(self, strFirstToken: str):
        """
        Filters the master ledger based on:
        1. Normalized token match in normalized entry
        2. If no match and token starts with 'a', retry match after removing 'a'
        """
        if not strFirstToken:
            return []
        
        strTokenNorm = self.normalize_text(strFirstToken)
        bStartsWithA = strTokenNorm.startswith('a')
        strTokenTrimmed = strTokenNorm[1:] if bStartsWithA else strTokenNorm

        matchedEntries = []
        for strEntry in self.lstMasterLedger:
            strEntryNorm = self.normalize_text(strEntry)

            # Step 1: Try full token match
            if strTokenNorm in strEntryNorm:
                matchedEntries.append(strEntry)
            # Step 2: If token starts with 'a', try trimmed version
            elif bStartsWithA and strTokenTrimmed in strEntryNorm:
                matchedEntries.append(strEntry)
        
        return matchedEntries


    def get_best_match(self, strInputText: str, lstFilteredLedger: list):
        """
        Finds the most similar entry in the provided ledger list using fuzzy matching.
        """
        strInputNorm = self.normalize_text(strInputText)
        strBestMatch = None
        floatBestScore = 0.0

        for strItem in lstFilteredLedger:
            strItemNorm = self.normalize_text(strItem)
            floatSimilarity = SequenceMatcher(None, strInputNorm, strItemNorm).ratio()
            if floatSimilarity > floatBestScore:
                strBestMatch = strItem
                floatBestScore = floatSimilarity

        if floatBestScore < self.floatSimilarityThreshold:
            return None, 0.0
        return strBestMatch, floatBestScore

    async def get_mapped_ledger(self, strInputText: str):
        """
        Attempts to map the input text to a ledger entry.
        
        1. Checks for an exact match.
        2. If not found, extracts the product code from the text,
           filters the ledger entries, and applies fuzzy matching.
        3. Returns the best match and the similarity confidence score.
        """
        # Check for an exact match first.
        if strInputText in self.lstMasterLedger:
            return strInputText, 1.0

        # Extract product code from the input text (e.g., "A-7068 RGW")
        product_code = self.get_first_token(strInputText)
        # Filter ledger entries that contain the product code.
        filtered_ledgers = self.filter_by_first_token(product_code)
        
        # If any candidates are found using the product code filter, use fuzzy matching on those.
        best_match=None
        best_score=None
        
        if filtered_ledgers:
            best_match, best_score = self.get_best_match(strInputText, filtered_ledgers)
            if best_match is not None:
                return best_match, best_score
        
        # Otherwise, try fuzzy matching against the entire ledger.
        # best_match, best_score = self.get_best_match(strInputText, self.lstMasterLedger)
        if best_match is not None:
            return best_match, best_score
        
        return "-", 0.0

    async def get_latest_mapped_ledgers(self, lstInputTexts: list):
        """
        Processes a list of input texts and returns their mapped ledger entries.
        """
        mapped_ledgers = []
        for text in lstInputTexts:
            mapped_ledger, _ = await self.get_mapped_ledger(text)
            mapped_ledgers.append(mapped_ledger)
        return mapped_ledgers




if __name__ == "__main__":
    # pass
    async def main():
        strMasterLedgerFile = r"Data\Customer\17_ParagTraders\15_Aquant\AQUANT Stock Item.xlsx"
        helper = CSimilaritySearchHelper(strMasterLedgerFile)

        strInputText = "A-7068 RGW-ROSE GOLD WHITE COLOUR WASH BASIN"
        strMappedLedger, floatConfidence = await helper.get_mapped_ledger(strInputText)

        print("Original in Tax Invoice:", strInputText)
        print("Mapped Ledger:", strMappedLedger)
        print("Confidence Score:", floatConfidence)

    asyncio.run(main())
