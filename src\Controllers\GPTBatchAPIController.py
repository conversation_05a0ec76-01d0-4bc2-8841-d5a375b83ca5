import os
import sys
sys.path.append(".")
from fastapi import  <PERSON><PERSON><PERSON><PERSON>x<PERSON>
from sqlalchemy.exc import SQLAlchemyError
import json
import traceback
from typing import Any
from sqlalchemy import select, desc, func

# Importing local modules and schemas
from src.Models.models import GPTBatchAPIRecords
from src.Schemas.schemas import GPTBatchAPIStatusEnum
from config.db_config import AsyncSessionLocal

class CGPTBatchAPIDB:
    
    @staticmethod
    async def MSInsertBatchRecord(userid:int, batch_object: dict, batch_polling_object: dict, task_details: dict, status: GPTBatchAPIStatusEnum) -> dict:
        """
        Insert a new row into the GPTBatchAPIRecords table.

        Args:
            batch_object (dict): Batch Object in JSON.
            batch_polling_object (dict): Batch Polling Object in JSON.
            task_details (dict): Task Details in JSON.
            status (GPTBatchAPIStatusEnum): Status of the task (IN PROGRESS, COMPLETE).

        Returns:
            dict: Dictionary with the inserted record's details.
        """
        try:
            async with Async<PERSON>essionLocal() as session:
                new_record = GPTBatchAPIRecords(
                    userid = userid,
                    Batch_Object=batch_object,
                    Batch_Polling_Object=batch_polling_object,
                    TaskDetails=task_details,
                    Status=status
                )
                session.add(new_record)
                await session.commit()
                await session.refresh(new_record)
                return {"id": new_record.id, "status": "Record inserted successfully"}
        except Exception as e:
            raise HTTPException(status_code=500, detail="Failed to insert record.")

    @staticmethod
    async def MSUpdateBatchRecord(record_id: int, update_fields: dict) -> dict:
        """
        Update an existing row in GPTBatchAPIRecords.

        Args:
            record_id (int): ID of the record to update.
            update_fields (dict): Dictionary of fields to update.

        Returns:
            dict: Dictionary indicating update status.
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(GPTBatchAPIRecords).where(GPTBatchAPIRecords.id == record_id)
                )
                record = result.scalar()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found.")

                # Update fields dynamically
                for key, value in update_fields.items():
                    setattr(record, key, value)

                await session.commit()
                return {"status": "Record updated successfully"}
        except Exception as e:
            raise HTTPException(status_code=500, detail="Failed to update record.")

    @staticmethod
    async def MSGetBatchRecord(record_id: int) -> dict:
        """
        Fetch a single row from GPTBatchAPIRecords based on the provided record ID 
        and return it as a dictionary.

        Args:
            record_id (int): ID of the specific record to fetch.

        Returns:
            dict: The batch record as a dictionary.
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(GPTBatchAPIRecords).where(GPTBatchAPIRecords.id == record_id)
                )
                record = result.scalar()
                
                if not record:
                    raise HTTPException(status_code=404, detail="Record not found.")
                
                # Convert the record to a dictionary
                return record.to_dict() if hasattr(record, 'to_dict') else {
                    "userid": record.userid,
                    "id": record.id,
                    "Batch_Object": record.Batch_Object,
                    "Batch_Polling_Object": record.Batch_Polling_Object,
                    "TaskDetails": record.TaskDetails,
                    "status": record.Status,
                    "CreatedDateTime": record.CreatedDateTime,
                    "ModifiedDateTime": record.ModifiedDateTime
                }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve record: {str(e)}")


    @staticmethod
    async def getBatchRecordsFromBatchId(batch_id: str) -> dict:
        """
        Fetch batch records by matching Batch_Object.id with the provided batch_id.

        Args:
            batch_id (str): The ID of the batch to fetch.

        Returns:
            dict: The batch record if found, as a dictionary.
        """
        try:
            async with AsyncSessionLocal() as session:
                # Query to match the batch ID in the JSON field
                result = await session.execute(
                    select(GPTBatchAPIRecords)
                    .where(GPTBatchAPIRecords.Batch_Object["id"].as_string() == batch_id)
                )
                record = result.scalar()
                
                if not record:
                    raise HTTPException(status_code=404, detail="Batch record not found.")
                
                # Manually convert the SQLAlchemy object to a dictionary
                return {
                    "userid":record.userid,
                    "id": record.id,
                    "Batch_Object": record.Batch_Object,
                    "Batch_Polling_Object": record.Batch_Polling_Object,
                    "TaskDetails": record.TaskDetails,
                    "status": record.Status,
                    "CreatedDateTime": record.CreatedDateTime,
                    "ModifiedDateTime": record.ModifiedDateTime
                }
                
        except SQLAlchemyError as e:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve batch record: {str(e)}")
    
    @staticmethod
    async def getBatchRecordsWithStatusFilter(status_list: list = []) -> list:
        """
        Fetch batch records and return a list of dictionaries with all fields from each found row.
        Filter the results based on the provided status list.

        Args:
            status_list (list): A list of statuses to filter the records. If empty, no filtering will be applied.

        Returns:
            list: List of dictionaries containing all columns of each record.
        """
        try:
            async with AsyncSessionLocal() as session:
                query = select(GPTBatchAPIRecords)
                
                # Apply status filter only if status_list is not empty
                if status_list:
                    query = query.where(GPTBatchAPIRecords.Status.in_(status_list))
                
                result = await session.execute(query)
                records = result.scalars().all()

                # Convert each record to a full dictionary of its attributes
                dictBatchRecords = [
                    {
                        "userid": record.userid,
                        "id": record.id,
                        "Batch_Object": record.Batch_Object,
                        "Batch_Polling_Object": record.Batch_Polling_Object,
                        "TaskDetails": record.TaskDetails,
                        "status": record.Status,
                        "CreatedDateTime": record.CreatedDateTime,
                        "ModifiedDateTime": record.ModifiedDateTime
                    } for record in records
                ]

                return dictBatchRecords

        except SQLAlchemyError as e:
            raise HTTPException(status_code=500, detail=f"Failed to retrieve batch records: {str(e)}")

import asyncio

async def main():
    # Call your async method and capture the result
    result = await CGPTBatchAPIDB.getBatchRecordsWithStatusFilter(status_list=["in_progress"])
    print(result)
    # result = await CGPTBatchAPIDB.getBatchRecordsFromBatchId(batch_id="batch_67164e841778819084f285ecfbf06c52")
    # print(result)
    # result = await CGPTBatchAPIDB.MSGetBatchRecords(record_id=7)
    # print(result)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())