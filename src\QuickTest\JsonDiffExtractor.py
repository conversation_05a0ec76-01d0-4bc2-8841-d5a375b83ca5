import json
import pandas as pd
from deepdiff import DeepDiff
import sys
import os
import re
import glob

def load_json(file_path):
    """Load a JSON file and return its content."""
    try:
        with open(file_path, 'r') as file:
            return json.load(file)
    except Exception as e:
        print(f"Error loading {file_path}: {e}")
        return None

def normalize_string(value):
    """Normalize a string by removing extra spaces and standardizing commas."""
    if isinstance(value, str):
        # Remove extra spaces (multiple spaces to single space, strip leading/trailing)
        value = re.sub(r'\s+', ' ', value.strip())
        # Standardize commas (e.g., remove spaces before/after commas)
        value = re.sub(r'\s*,\s*', ',', value)
    return value

def normalize_json(data):
    """Recursively normalize strings in a JSON object."""
    if isinstance(data, dict):
        return {k: normalize_json(v) for k, v in data.items()}
    elif isinstance(data, list):
        return [normalize_json(item) for item in data]
    elif isinstance(data, str):
        return normalize_string(data)
    return data

def extract_docid(file_path):
    """Extract numeric DocID from the file path."""
    match = re.search(r'(\d+)', os.path.basename(file_path))
    return match.group(0) if match else None

def compare_json_files(json1, json2, docid):
    """Compare two JSON objects and return their differences, ignoring space and comma differences."""
    if json1 is None or json2 is None:
        print(f"Skipping comparison for DocID {docid} due to invalid JSON.")
        return {}
    # Normalize JSON data to ignore spaces and commas
    normalized_json1 = normalize_json(json1)
    normalized_json2 = normalize_json(json2)
    return DeepDiff(normalized_json1, normalized_json2, ignore_order=True)

def extract_field_name(path):
    """Extract the field name from a DeepDiff path."""
    match = re.search(r"\['([^']+)'\]$", path)
    return match.group(1) if match else path

def find_differing_columns(row1, row2):
    """Identify differing columns between two row dictionaries."""
    if not isinstance(row1, dict) or not isinstance(row2, dict):
        return []
    differing_columns = []
    keys = set(row1.keys()).union(row2.keys())
    for key in keys:
        if row1.get(key) != row2.get(key):
            differing_columns.append((key, row1.get(key, ''), row2.get(key, '')))
    return differing_columns

def diff_to_dataframe(diff, json1, json2, docid):
    """Convert DeepDiff output to a DataFrame, combining Table differences into one row per row index."""
    diff_data = []
    
    for change_type, changes in diff.items():
        print(f"Processing change type: {change_type} for DocID: {docid}")  # Debug: Print change type
        if change_type == 'values_changed':
            # Group Table differences by row index
            itemtable_diffs = {}
            for path, change in changes.items():
                print(f"Path: {path}, Change: {change}")  # Debug: Print path and change
                # Handle Table differences
                if 'Table' in path:
                    match = re.match(r"root\['Table'\]\[(\d+)\]$", path)
                    if match:
                        row_index = int(match.group(1))
                        new_row = change.get('new_value', {})
                        old_row = change.get('old_value', {})
                        differing_columns = find_differing_columns(old_row, new_row)
                        if row_index not in itemtable_diffs:
                            itemtable_diffs[row_index] = []
                        itemtable_diffs[row_index].extend(differing_columns)
                    else:
                        # Handle granular Table differences
                        match = re.match(r"root\['Table'\]\[(\d+)\]\['([^']+)'\]$", path)
                        if match:
                            row_index = int(match.group(1))
                            column_name = match.group(2)
                            if row_index not in itemtable_diffs:
                                itemtable_diffs[row_index] = []
                            itemtable_diffs[row_index].append((column_name, change.get('old_value', ''), change.get('new_value', '')))
                else:
                    # Handle non-Table fields
                    field_name = extract_field_name(path)
                    diff_data.append({
                        'DocID': docid,
                        'Field': field_name,
                        'Grok': change.get('old_value', ''),
                        'GPT': change.get('new_value', '')
                    })
            
            # Process Table differences, combining into one row per row index
            for row_index, diffs in itemtable_diffs.items():
                field_str = f"Table[{row_index}]"
                grok_values = {}
                gpt_values = {}
                for col_name, old_val, new_val in diffs:
                    grok_values[col_name] = old_val
                    gpt_values[col_name] = new_val
                diff_data.append({
                    'DocID': docid,
                    'Field': field_str,
                    'Grok': str(grok_values),
                    'GPT': str(gpt_values)
                })
        
        elif change_type == 'dictionary_item_added':
            for path in changes:
                print(f"Added path: {path} for DocID: {docid}")  # Debug: Print added path
                if 'Table' in path:
                    match = re.match(r"root\['Table'\]\[(\d+)\]\['([^']+)'\]$", path)
                    if match:
                        row_index = int(match.group(1))
                        column_name = match.group(2)
                        diff_data.append({
                            'DocID': docid,
                            'Field': f"Table[{row_index}][{column_name}]",
                            'Grok': '',
                            'GPT': json2['Table'][row_index][column_name] if row_index < len(json2.get('Table', [])) else ''
                        })
                    else:
                        field_name = extract_field_name(path)
                        diff_data.append({
                            'DocID': docid,
                            'Field': field_name,
                            'Grok': '',
                            'GPT': json2.get(field_name, '')
                        })
                else:
                    field_name = extract_field_name(path)
                    diff_data.append({
                        'DocID': docid,
                        'Field': field_name,
                        'Grok': '',
                        'GPT': json2.get(field_name, '')
                    })
        elif change_type == 'dictionary_item_removed':
            for path in changes:
                print(f"Removed path: {path} for DocID: {docid}")  # Debug: Print removed path
                if 'Table' in path:
                    match = re.match(r"root\['Table'\]\[(\d+)\]\['([^']+)'\]$", path)
                    if match:
                        row_index = int(match.group(1))
                        column_name = match.group(2)
                        diff_data.append({
                            'DocID': docid,
                            'Field': f"Table[{row_index}][{column_name}]",
                            'Grok': json1['Table'][row_index][column_name] if row_index < len(json1.get('Table', [])) else '',
                            'GPT': ''
                        })
                    else:
                        field_name = extract_field_name(path)
                        diff_data.append({
                            'DocID': docid,
                            'Field': field_name,
                            'Grok': json1.get(field_name, ''),
                            'GPT': ''
                        })
                else:
                    field_name = extract_field_name(path)
                    diff_data.append({
                        'DocID': docid,
                        'Field': field_name,
                        'Grok': json1.get(field_name, ''),
                        'GPT': ''
                    })
    
    df = pd.DataFrame(diff_data)
    print(f"DataFrame created with {len(diff_data)} rows for DocID: {docid}")  # Debug: Confirm DataFrame size
    return df

def write_diff_to_excel(diff_df, output_path):
    """Write the differences DataFrame to an Excel file."""
    try:
        os.makedirs(os.path.dirname(output_path), exist_ok=True)
        diff_df.to_excel(output_path, index=False, sheet_name='JSON_Diff')
        print(f"Differences written to {output_path}")
    except Exception as e:
        print(f"Error writing to Excel: {e}")
        sys.exit(1)

def main():
    # Define directories for Grok and GPT files
    grok_dir = r"GitIgnore\QuickTestData\Responses\Kohler"
    gpt_dir = r"H:\DEVELOPER_PUBLIC\interns\sneha\QuickTestData\ParagTraders\Kohler"
    output_path = r"H:\DEVELOPER_PUBLIC\interns\sneha\GROKvsGPT DIFF\Kohler_diff_output.xlsx"
    
    # Check if directories exist
    if not os.path.exists(grok_dir):
        print(f"Grok directory does not exist: {grok_dir}")
        sys.exit(1)
    if not os.path.exists(gpt_dir):
        print(f"GPT directory does not exist: {gpt_dir}")
        sys.exit(1)
    
    # Get list of files
    grok_files = glob.glob(os.path.join(grok_dir, "*_response.json"))
    gpt_files = glob.glob(os.path.join(gpt_dir, "strGPTResponse_*.json"))
    
    # # Debug: List files found
    # print(f"Grok files found: {grok_files}")
    # print(f"GPT files found: {gpt_files}")
    
    if not grok_files or not gpt_files:
        print("No matching files found in one or both directories.")
        sys.exit(1)
    
    # Create dictionary of DocIDs to file paths
    grok_docids = {extract_docid(f): f for f in grok_files if extract_docid(f)}
    gpt_docids = {extract_docid(f): f for f in gpt_files if extract_docid(f)}
    
    print(f"Grok DocIDs: {list(grok_docids.keys())}")
    print(f"GPT DocIDs: {list(gpt_docids.keys())}")
    
    all_diffs = []
    
    
    # Compare files with matching DocIDs
    for docid in set(grok_docids.keys()).intersection(gpt_docids.keys()):
        print(f"Processing DocID: {docid}")  # Debug: Print DocID
        
        grok_file = grok_docids[docid]
        gpt_file = gpt_docids[docid]
        
        # Load JSON files
        json1 = load_json(grok_file)
        json2 = load_json(gpt_file)
        
        # Compare files
        diff = compare_json_files(json1, json2, docid)
        
        if not diff:
            print(f"No differences found for DocID {docid}.")
            continue
        
        print(f"Differences found for DocID {docid}:", diff)  # Debug: Print raw diff
        diff_df = diff_to_dataframe(diff, json1, json2, docid)
        all_diffs.append(diff_df)
    
    if not all_diffs:
        print("No differences found across any files or no matching DocIDs.")
        sys.exit(1)
    
    # Combine all differences into a single DataFrame
    final_df = pd.concat(all_diffs, ignore_index=True)
    print(f"Final DataFrame created with {len(final_df)} rows")  # Debug: Confirm final DataFrame size
    write_diff_to_excel(final_df, output_path)

if __name__ == "__main__":
    main()