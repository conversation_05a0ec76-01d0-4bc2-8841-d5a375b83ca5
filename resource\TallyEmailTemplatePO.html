<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Tally Posting Report</title>
    <style>
        body {{
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333333;
        }}

        .container {{
            max-width: full;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }}

        .header {{
            text-align: center;
            border-bottom: 2px solid #003654;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }}

        .header h2 {{
            color: #003654;
            margin: 10px 0;
            font-size: 26px;
        }}

        .header p {{
            font-size: 16px;
            color: #555555;
        }}

        .intro {{
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }}

        .highlight-section {{
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.8;
        }}

        .highlight-section ul {{
            list-style: none;
            padding: 0;
            margin: 0;
        }}

        .highlight-section ul li {{
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }}

        .highlight-section .label {{
            font-weight: bold;
            color: #003654;
        }}

        .metrics-table {{
            width: 100%;
            margin: 0 auto 20px auto;
            font-size: 14px;
            border-collapse: collapse;
        }}

        .metrics-table th, .metrics-table td {{
            text-align: left;
            padding: 10px 15px;
            border: 1px solid #dddddd;
        }}

        .metrics-table th {{
            background-color: #003654;
            color: #ffffff;
        }}

        .metrics-table td {{
            background-color: #f9f9f9;
        }}

        .styled-table {{
            border-collapse: collapse;
            margin: 20px auto;
            font-size: 14px;
            table-layout: fixed;
            width: 100%;
            overflow-x: auto;
        }}

        .styled-table th, .styled-table td {{
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #dddddd;
            word-wrap: break-word;
        }}

        .styled-table th {{
            background-color: #003654;
            color: #ffffff;
            text-transform: uppercase;
        }}

        .styled-table tbody tr:nth-of-type(even) {{
            background-color: #f3f3f3;
        }}

        .styled-table tbody tr:hover {{
            background-color: #f1f1f1;
        }}

        .description {{
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 30px;
        }}

        .description ul {{
            list-style-type: disc;
            padding-left: 20px;
        }}

        .footer {{
            text-align: center;
            font-size: 14px;
            color: #777777;
            border-top: 1px solid #dddddd;
            padding-top: 20px;
        }}

        @media only screen and (max-width: 600px) {{
            .container {{
                padding: 20px;
            }}

            .header h2 {{
                font-size: 22px;
            }}

            .intro, .description, .footer {{
                font-size: 14px;
            }}

            .styled-table th, .styled-table td {{
                padding: 10px 12px;
            }}
        }}

        /* General styles for success and failure */
        .success {{
            color: green;
            font-weight: bold;
        }}

        .failure {{
            color: red;
            font-weight: bold;
        }}

        /* Tally status: PartialSuccess - blue */
        .partial-success {{
            color: #1e88e5; /* medium blue */
            font-weight: bold;
        }}


        /* Tally status: Duplicate - amber */
        .duplicate {{
            color: #ffb300; /* amber/goldenrod */
            font-weight: bold;
        }}

        /* Tally status: ValidationError - alert Yellow */
        .validation-error {{
            color: #e0e034; /* alert yellow */
            font-weight: bold;
        }}
        /* Tally status: Skipped - grey */
        .skipped {{
            color: #808080; /* grey */
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>{voucher_type} - Tally Posting Report for {receiverName}</h2>
            <p><b>Date:</b> <b>{date}</b>{system_info}{time_info} | <b>Server Name:</b> <b>{server_name}</b></p>
        </div>

        <div class="intro">
            <p>Dear <b>{receiverName}</b>,</p>
            <p>Please find attached the Purchase Order Processing Report for your review. Kindly take a moment to verify the details at your earliest convenience.</p>
        </div>

        <div class="highlight-section">
            <h3>Performance Summary:</h3>
            <table class="metrics-table">
                <tr>
                    <th>Metrics</th>
                    <th>Current Processing Stats</th>
                    <th>Till Now Processing Stats</th>

                </tr>
                <tr>
                    <td><b>Total Transactions Processed</b></td>
                    <td>{total_posted_vouchers}</td>
                    <td>{total_posted_vouchers_till_now}</td>
                </tr>
                <tr>
                    <td><b>Total Time Saved</b></td>
                    <td>{total_time_saved_today}</td>
                    <td>{total_time_saved_till_now}</td>
                </tr>
            </table>
        </div>

        {table}

        <div class="description">
            <h3>Purchase Order Table Details:</h3>
            <ul>
                <li><strong>SR No.:</strong> The unique serial number of the record.</li>
                <li><strong>PO No.:</strong> The unique identifier of the Purchase Order.</li>
                <li><strong>PO Date:</strong> The date when the Purchase Order was created.</li>
                <li><strong>PO Vendor Name:</strong> The name of the vendor associated with the Purchase Order.</li>
                <li><strong>PO Total Items:</strong> The total number of items listed in the Purchase Order.</li>
                <li><strong>PO Items Added in Tally:</strong> The number of items from the Purchase Order that have been successfully added to Tally.</li>
                <li><strong>PO Skipped Items:</strong> The number of items from the Purchase Order that were skipped during processing (default is 0).</li>
                <li><strong>Time Saved:</strong> The total time saved, in seconds, through automation or manual intervention.</li>
                <li><strong>AV Status:</strong> The status of the AccuVelocity process (e.g., Success, Skipped, Duplicate).</li>
                <li><strong>AV Comments:</strong> Additional comments related to the AccuVelocity process, providing clarification about skipped items or other discrepancies.</li>
            </ul>

            <h3>GRN Table Details:</h3>
            <ul>
                <li><strong>SR No.:</strong> The unique serial number of the record.</li>
                <li><strong>GRN No.:</strong> The unique identifier of the Goods Received Note.</li>
                <li><strong>GRN Date:</strong> The date when the Goods Received Note was created.</li>
                <li><strong>GRN Vendor Name:</strong> The name of the vendor associated with the Goods Received Note.</li>
                <li><strong>GRN Total Items:</strong> The total number of items listed in the Goods Received Note.</li>
                <li><strong>GRN Items Added in Tally:</strong> The number of items from the Goods Received Note that have been successfully added to Tally.</li>
                <li><strong>GRN Skipped Items:</strong> The number of items from the Goods Received Note that were skipped during processing (default is 0).</li>
                <li><strong>Time Saved:</strong> The total time saved, in seconds, through automation or manual intervention.</li>
                <li><strong>AV Status:</strong> The status of the AccuVelocity process (e.g., Success, Skipped, Duplicate).</li>
                <li><strong>AV Comments:</strong> Additional comments related to the AccuVelocity process, providing clarification about skipped items or other discrepancies.</li>
            </ul>

            <h3>Machinery GRN Table Details:</h3>
            <ul>
                <li><strong>SR No.:</strong> The unique serial number of the record.</li>
                <li><strong>Machine No.:</strong> The unique identifier of the Machine.</li>
                <li><strong>Date:</strong> The date when the Goods Received Note was created.</li>
                <li><strong>Time Saved:</strong> The total time saved, in seconds, through automation or manual intervention.</li>
                <li><strong>AV Status:</strong> The status of the AccuVelocity process (e.g., Success, Skipped, Duplicate).</li>
                <li><strong>AV Comments:</strong> Additional comments related to the AccuVelocity process, providing clarification about skipped items or other discrepancies.</li>
            </ul>
        </div>

        <div class="footer">
            <p>Regards,<br><b>Accuvelocity Team</b></p>
        </div>
    </div>
</body>
</html>
