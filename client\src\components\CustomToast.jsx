/* eslint-disable react/prop-types */
import { CheckCircleIcon, InfoOutlineIcon } from "@chakra-ui/icons";
import { Box, Text } from "@chakra-ui/react";

const CustomToast = ({ title, message }) => {
  return (
    <Box
      color="white"
      p={3}
      background={title === "Error" ? "red" : "#39c449"}
      borderRadius={"12px"}
      display={"flex"}
    >
      {title === "Error" ? (
        <InfoOutlineIcon w={6} h={6} mt={"2px"} />
      ) : (
        <CheckCircleIcon w={6} h={6} mt={"2px"} />
      )}
      <Box ml={"16px"}>
        <Text color={"white"} fontWeight={600} fontSize={"18px"} mb={"5px"}>
          {title}
        </Text>
        <Text color={"white"}>{message}</Text>
      </Box>
    </Box>
  );
};

export default CustomToast;
