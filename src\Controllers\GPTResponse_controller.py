from sqlalchemy.exc import SQLAlchemyError, NoResultFound
from typing import Any
from sqlalchemy import select, desc, func
from enum import Enum
from pathlib import Path
import json
import asyncio
import binascii
import re
import io
import traceback
import aiofiles
from fastapi.responses import JSONResponse
from fastapi import UploadFile, HTTPException, status, Query, WebSocket, WebSocketDisconnect
from src.Models.models import UploadedDoc, DocExtractedData, ModelTable
from config.constants import Constants
from src.utilities.helperFunc import GetFiletypeFromFileName, IsAllFieldApprovedCheck, getAPIJsonStructureResponse, GPTHelper, CExtractedDataHelper, CDictHelper, CExtractCoordinatesHelper, CDocxExcelCsv2PDF, ReadExcelToDict, CJSONFileReader
from src.utilities.ExtractTextHelper import ExtractTextFromDoc
from src.Controllers.DocumentData_controller import CDocumentData
from src.Schemas.GPT_Schema import <PERSON><PERSON><PERSON><PERSON>NUp<PERSON>, DocExtractionAPIModel
from src.Models.models import  StatusEnum

from src.GPTAPI import CGPTAPIResponse
from src.GPTAPIV2 import CGPTAPIResponseV2
from sqlalchemy.future import select
from config.db_config import AsyncSessionLocal
# from src.Controllers.auth_controller import get_single_user_api_usage
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.GeminiAPI import CallGeminiAPI
from src.Controllers.Vendor_Controller import CVendorController
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.user_Logs_Controller import CUserLogController
from sqlalchemy import func
from src.Controllers.auth_controller import CAuthController
from src.utilities.ExtractTextHelper import ExtractTextFromDoc
from src.Controllers.prompt_controller import CPromptController
from src.Controllers.ApplicationLayerProcess import ApplicationLayerProcessing
from src.utilities.DBHelper import CPromptTable,CUserTable, CExtractedValidationTable, CDocumentTable, CExtractionTable
from src.utilities.PromptBuilder import MakeModelGPTPrompt
from src.utilities.S3BucketHelper import CAWSS3Storage
from src.utilities.KeyMapper import CKeyValMapper
from httpx import AsyncClient, RequestError, HTTPStatusError
from dotenv import load_dotenv
import os
from sqlalchemy.orm.attributes import flag_modified
from typing import List, Dict
from io import BytesIO
from starlette.datastructures import UploadFile as StarletteUploadFile
from src.Schemas.Doc_Schema import  AdditionalDocDetails
from src.utilities.helperFunc import  Hashing,CommonHelper
from datetime import datetime
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence

# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)

# Read the value of EXTERNAL_API_SERVER
EXTERNAL_API_SERVER = os.getenv('EXTERNAL_API_SERVER')
from src.utilities.DBHelper import CPromptTable,CUserTable

STATUS_MAPPING = {
    "approved": "Approved",
    "error": "Error",
    "notprocess": "Not Processed",
    "onhold": "On Hold",
    "tobeapproved": "To Be Approved",
    "processing": "Processing"
}


class CGPTResponseData:

    @staticmethod
    async def MSGetAllDocExtractJsonDataBaseOnUserID(iUserID: int, iLimit: int = Query(10, gt=0), iOffset: int = Query(0, ge=0)):
        """
        Purpose : Retrieve a paginated list of GPT-processed data from the database, and deserialize
                JSON data from the 'DocVerifiedData' column.

        Inputs  :   (1) iLimit   : The maximum number of GPT data entries to return.
                    (2) iOffset  : The offset from where to start the query.

        Output  : A list of dictionaries with document IDs as keys and the deserialized JSON data as values.

        Example : await CGPTResponseData.MSGetAllDocExtractJsonDataBaseOnUserID(iLimit=10, iOffset=0)
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", "Process of Fetching All GPTJson Documents Started.")

            async with AsyncSessionLocal() as db:
                # Execute the query with limit and offset
                result = await db.execute(
                    select(DocExtractedData)
                    .order_by(desc(DocExtractedData.ModifiedDateTime))
                    .offset(iOffset)
                    .limit(iLimit)
                )
                doc_data_list = result.scalars().all()

                await CLogController.MSWriteLog(iUserID, "Info", "Successfully Fetched and Deserialized All GPTJson Documents.")
                # Deserialize JSON and construct output list
                output_list = [
                        {
                            'Id': doc_data.Id,
                            'DocId': doc_data.DocId,
                            'DocExtractionPromptID': doc_data.DocExtractionPromptID,
                            'VerifiedData': json.loads(doc_data.DocVerifiedData) if isinstance(doc_data.DocVerifiedData, str) else doc_data.DocVerifiedData,
                            'ApprovedData': json.loads(doc_data.DocApprovedData) if isinstance(doc_data.DocApprovedData, str) else doc_data.DocApprovedData,
                            'CreatedTime': doc_data.CreatedTime,
                            'ModifiedDateTime': doc_data.ModifiedDateTime,
                        } for doc_data in doc_data_list
                    ]

                return output_list

        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(iUserID, "Error", "Oops! We hit a snag while trying to retrieve the Extracted Json document data. Please try again later.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Oops! We hit a snag while trying to retrieve the extracted document data. Please try again later.")
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", "Oops! We hit a snag while trying to retrieve the Extracted Json document data. Please try again later.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code= 500, detail="Oops! There was a problem processing your request.")

    @staticmethod
    async def MSDownloadExtractedData(iUserID: int, lsDocIds: list, downloadType:str):
        """
        Purpose :   To Download document's Extracted data as CSV OR Excel Format

        Inputs  :   (1) iUserID   : Id of use who have called this api
                    (2) lsDocIds  : List of Document ids for which the data will be downloaded.
                    (3) downloadType : Type of the file to be downloaded, Options are (excel, csv)

        Output  :   Binary data
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", "Process of Downloading Extracted Documents Data Started.")
            if downloadType.lower() not in ["excel", "csv"]:
                await CLogController.MSWriteLog(iUserID, "Error", f"Invalid Download Type '{downloadType}', Available Types: ['excel', 'csv].")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                                        status_code=400,
                                        detail="Invalid file type requested for Download. Please provide a valid file format to download the file.")

            docList = []
            async with AsyncSessionLocal() as db:

                for iDocId in lsDocIds:

                    result = await db.execute(
                        select(DocExtractedData).filter(DocExtractedData.DocId == iDocId)
                    )
                    docData = result.scalars().first()

                    if docData:
                        fileResponse = CExtractedDataHelper.MSConvertToFile(CExtractCoordinatesHelper.transformForExtractedFormat(json.loads(docData.DocVerifiedData)), fileType=downloadType)

                        dictCurDocData = {
                                            'DocId': docData.DocId,
                                            'ExtractedData': fileResponse.get("FileData", ""),
                                            'ContentType': fileResponse.get("MediaType", "")
                                        }

                        docList.append(dictCurDocData)

            if docList:
                await CLogController.MSWriteLog(iUserID, "Info", "Successfully downloaded extracted documents data.")
            else:
                await CLogController.MSWriteLog(iUserID, "Warning", "No documents found for the provided IDs.")
            return docList

        except HTTPException as e:
            raise e
        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(iUserID, "Error", "Oops! We hit a snag while trying to retrieve the Extracted Json document data. Please try again later.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Oops! We hit a snag while trying to retrieve the Extracted Json document data. Please try again later.")
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", "Oops! We hit a snag while trying to retrieve the Extracted Json document data. Please try again later.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Oops! There was a problem processing your request.")

    @staticmethod
    async def MSLogExtractionStatus(strLogType, iUserID, iDocId, isTrialPaidDocExtraction, isEndOfExtraction=False,isUserMetaData=False):
        try:
            userData = await CAuthController.MSGetSingleUser(user_id= iUserID)
            dictDocumentData = await CDocumentData.MSGetDocById(user_id=iUserID, docId= iDocId,isBinaryDataRequired=True,isUserMetaData=isUserMetaData)
            if isTrialPaidDocExtraction:
                total_available_pages = userData['total_allowed_page_limit']
                pages_left = userData['page_limit_left']

            else:
                total_available_pages = userData['total_allowed_free_page_limit']
                pages_left = total_available_pages - userData['free_page_limit_usage']

            if isEndOfExtraction:
                iUsedPages = dictDocumentData.get("PageCount", '')
                await CUserLogController.MSWriteLog(iUserID, strLogType, f"Document Extraction - Document Name: {dictDocumentData.get('DocName', '')} | Extraction Status:{STATUS_MAPPING.get((dictDocumentData.get('Status', '').lower()))} | Extraction Mode : {'PRO' if isTrialPaidDocExtraction else 'Lite'} | Pages Used : {iUsedPages} | Pages Left: {pages_left}", "MyDocuments")
            else:
                await CUserLogController.MSWriteLog(iUserID, strLogType, f"Document Extraction - Document Name: {dictDocumentData.get('DocName', '')} | Extraction Status:{STATUS_MAPPING.get((dictDocumentData.get('Status', '').lower()))} | Extraction Mode : {'PRO' if isTrialPaidDocExtraction else 'Lite'}.", "MyDocuments")

        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Error while logging user logs. {str(e)}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

    @staticmethod
    async def performDocumentExtractionFromDocumentID(userid: int, document_id: int, isTrialPaidDocExtraction:bool, bReUseExistGPTResponse:bool = True,strClientREQID=None, strVoucherType=None, bIsDeveloperMode = False):
        async with AsyncSessionLocal() as db:
            PromptId = None  # Default value to use if keys are missing or if there is no "Id"
            bPerformAWSExtraction = False
            objDocMetaDataResponse = None
            dictPromptData = {}
            strModelName = "Unknown"
            # NOTE: GENERALIZE_PWI_V3
            bReasoningEnabled = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = userid, strVoucherType = strVoucherType)
            bGeneralizePWIV3 = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = userid, strVoucherType = strVoucherType)
            try:
                dictAPIEndpointResponse=await ApplicationLayerProcessing.CheckExistDocumentExtractionResponse(userid,document_id,strVoucherType = strVoucherType)
                if dictAPIEndpointResponse:
                    await CDocumentData.MSUpdateDocFieldsFromGPTResponse(dictGPTAPIResopnse= dictAPIEndpointResponse["Document"], iUserId= userid, iDocId = document_id)
                    await CLogController.MSWriteLog(userid, "Info", f"REUSE GPT API Response: - DocID: {document_id}, dictAPIEndpointResponse : {dictAPIEndpointResponse}")
                    return dictAPIEndpointResponse
                else:
                    # if bReUseExistGPTResponse:
                    #     dictCheckExistance = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= userid,document_id=document_id, dictAPIRespnse = dictDocExtractionResponse)
                    #     if dictCheckExistance["bExisingGPTResponseAvailable"]:
                    #         return dictCheckExistance
                    # Get User Configuration Data
                    userData = await CAuthController.MSGetSingleUser(user_id= userid)
                    isGPTEnabled = isTrialPaidDocExtraction and userData.get("isTrialPaidDocExtraction")

                    # Call aws extraction if tally is configured
                    if userData["integration_config"] and userData["integration_config"]["isTallyConfigured"] :
                        bPerformAWSExtraction = True
                        isGPTEnabled = True


                    # await CGPTResponseData.MSLogExtractionStatus(strLogType="Info", iUserID=userid, iDocId=document_id, isTrialPaidDocExtraction=isTrialPaidDocExtraction, isEndOfExtraction=False,isUserMetaData=bPerformAWSExtraction)

                    dictDocumentData = await CDocumentData.MSGetDocById(user_id=userid, docId= document_id,isBinaryDataRequired=True, isUserMetaData=bPerformAWSExtraction, strVoucherType = strVoucherType)
                    # isPaidDocExtraction = bool(userData["usePaidDocExtractor"] == "Yes")



                    # Get Prompt and Model Table Data base on UserID, DocId, is Trial Doc Extraction
                    # NOTE: GENERALIZE_PWI_V3
                    if not bGeneralizePWIV3:
                        # To update weather paid model is used or not for current extraction
                        await CDocumentData.MSUpdateDoc(iUserID=userid, iDocId = document_id, bUsedPaidModel=isGPTEnabled)
                        # TODO: Reasoning Model please add vendorName
                        dictPromptData = await CPromptTable.MSGetPromptDataBaseOnDocID(iUserID=userid, docID=document_id, isPaidDocExtraction=isGPTEnabled)

                        # get prompt id which used for that document
                        PromptId = dictPromptData.get("PromptTable").get("Id")
                        strModelName = dictPromptData.get("ModelTable").get("ModelName")
                        bReasoningEnabled = dictPromptData.get("isReasoningEnabled", False)

                    start_extract_time = datetime.now()
                    # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
                    print(f"Start AWS API (DocID: {document_id}) - {start_extract_time}")
                    objDocMetaData = ExtractTextFromDoc(dictUserData=userData, dictDocumentData=dictDocumentData)

                    # Extract text from Document
                    if bPerformAWSExtraction:
                        strRawTextInput = None
                        if isinstance(dictDocumentData.get("DocumentRawTxt", None), dict):
                            strRawTextInput = dictDocumentData.get("DocumentRawTxt").get("RawTextInput")
                            objDocMetaDataResponse = {
                                "page_height":"",
                                "page_width":"",
                                "extracted_text":strRawTextInput # Customize Raw Text to Serve as Input for the OpenAI Model Response
                            }
                        else:
                            objDocMetaDataResponse = await objDocMetaData.MExtractTxtFromDoc(isGPTEnabled = isGPTEnabled, bPerformAWSExtraction=bPerformAWSExtraction)
                            # Insert AWS Object & Upload Raw Text In Document Table For Resuability
                            if objDocMetaDataResponse:
                                obj_response = {
                                        "AWSExtractedText": objDocMetaDataResponse.get("extracted_text")
                                        }
                                
                                await CGPTResponseData.insert_gpt_result(iUserId=userid, doc_id=document_id, strModelName= strModelName, obj_response=obj_response, dict_response= None, PromptId=PromptId,iModelId=dictDocumentData.get("ModelId", None),isGPTEnabled=isGPTEnabled, bAwsExtraction=bPerformAWSExtraction)
                                # Update the AdditionalDocDetails column in the UploadedDoc table
                                
                                await CDocumentData.MSUpdateUploadedDocRecord(
                                    iUserId=userid,
                                    iDocId=document_id,
                                    DocumentRawTxt = {'RawTextInput': objDocMetaDataResponse.get("extracted_text")},
                                    DocumentRawTxtObject= objDocMetaDataResponse.get("AWSResponse")
                                )
                    else:
                        objDocMetaDataResponse = await objDocMetaData.MExtractTxtFromDoc(isGPTEnabled = isGPTEnabled, bPerformAWSExtraction=bPerformAWSExtraction)

                    end_extract_time =  datetime.now()
                    extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
                    print(f"End AWS API (DocID: {document_id}) -  Time taken: {extract_time_taken:.2f} seconds.")
                    # Rest Document Data
                    if not bGeneralizePWIV3:
                        await CDocumentData.MSResetDocumentData(iUserID=userid,iDocId=document_id)
                        await CExtractedValidationTable.MSDeleteExtractValidatedData(user_id=userid, doc_id=document_id, prompt_id=PromptId)
                    start_extract_time = datetime.now()
                    # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
                    print(f"Start GROK/GPT API  (DocID: {document_id}) - {start_extract_time}")

                    if bPerformAWSExtraction:
                        try:
                            # NOTE: GENERALIZE_PWI_V3
                            if bGeneralizePWIV3:
                                if userid == 11:
                                    dictResponseFormat = CJSONFileReader.read_json_file(Path(r"Data\Customer\Abhinav InfraBuild\APIResponseFormat.json"))
                                elif userid == 10:
                                    dictResponseFormat = CJSONFileReader.read_json_file(Path(r"Data\Customer\VedanshSchool\GPTAPI_GeneralPWOI_ResponseSchema_V3.1.json"))
                                elif userid == 2:
                                     dictResponseFormat = CJSONFileReader.read_json_file(Path(r"Data\Customer\17_ParagTraders\APIResponseFormat.json"))
                                else:
                                    #  ASSUME PWI V3 General Config
                                    dictResponseFormat = CJSONFileReader.read_json_file(Path(r"Data\General_Config\PurchaseWithInventory\APIResponseFormat.json"))
                            else:
                                #TODO: please add new  vendor gpt schema format
                                # WARN: Please remove this static code and make this dynamic
                                    dictResponseFormat = CommonHelper.MSGetResponseFormat(strModelName)
                        except FileNotFoundError as e:
                            raise HTTPException(status_code=500, detail=f"Failed to load response format file: {str(e)}")
                        except json.JSONDecodeError as e:
                            raise HTTPException(status_code=500, detail=f"Invalid JSON format in response file: {str(e)}")

                    # call GPT for paid users
                    if isGPTEnabled:
                        if bPerformAWSExtraction:
                            objAPIResponse = await CGPTResponseData.performGPTExtractionProcess(document_id=document_id,
                                                                                                userData=userData,
                                                                                                objDocMetaDataResponse=objDocMetaDataResponse,
                                                                                                promptData=dictPromptData.get("PromptTable" ,None),
                                                                                                bIsAWSExtration=bPerformAWSExtraction,
                                                                                                dictResponseFormat=dictResponseFormat,
                                                                                                bReasoningEnabled=bReasoningEnabled, strVoucherType=strVoucherType, bIsDeveloperMode = bIsDeveloperMode, strModelName = strModelName)
                        else:
                            objAPIResponse = await CGPTResponseData.performGPTExtractionProcess(document_id=document_id,
                                                                                                userData=userData,
                                                                                                objDocMetaDataResponse=objDocMetaDataResponse,
                                                                                                promptData=dictPromptData.get("PromptTable", None),
                                                                                                bIsAWSExtration=bPerformAWSExtraction,
                                                                                                bReasoningEnabled=bReasoningEnabled)
                    else:
                        # check user if free user use gemini
                        # use gemini when ocr scanned pdf even when user is paid
                        objAPIResponse = await CGPTResponseData.performGeminiExtractionProcess(document_id=document_id,userData=userData, objDocMetaDataResponse=objDocMetaDataResponse, promptData=dictPromptData.get("PromptTable", None))
                    
                    
                    try:
                        obj_response, dictDocExtractionResponse = objAPIResponse.get("obj_response"), objAPIResponse.get("dict_response")
                    except Exception as e:
                        # raise HTTPException(status_code=500,detail="Server is under heavy load. Unable to process this Document, Please try again later.")
                        await CLogController.MSWriteLog(userid, "WARNING", f"Lite Mode :- Incorporate pre-defined sample model data into the document extraction process.")
                        obj_response,dictDocExtractionResponse = dictPromptData.get("ModelTable").get("preDefinedModelDict"), dictPromptData.get("ModelTable").get("preDefinedModelDict")
                    dictDocExtractionResponse["meta_data"] = {}
                    dictDocExtractionResponse["meta_data"]["page_height"] = objDocMetaDataResponse.get("page_height")
                    dictDocExtractionResponse["meta_data"]["page_width"] = objDocMetaDataResponse.get("page_width")

                    if bPerformAWSExtraction:
                        obj_response = {
                            "ExtractionObject": obj_response,
                            "AWSExtractedText": objDocMetaDataResponse.get("extracted_text")
                            }

                    await CGPTResponseData.insert_gpt_result(iUserId=userid, doc_id=document_id, strModelName= strModelName, obj_response=obj_response, dict_response= dictDocExtractionResponse, PromptId=PromptId,iModelId=dictDocumentData.get("ModelId", None),isGPTEnabled=isGPTEnabled, bAwsExtraction=bPerformAWSExtraction)

                    # IGNORE: Set Document Extraction Status to Success
                    await CDocumentData.MSUpdateDocumentStatus(iUserID=userid, iDocId=document_id, eNewStatus="ToBeApproved",DocExtractionAPIStatusCode=200)
                    # await CGPTResponseData.MSLogExtractionStatus(strLogType="Info", iUserID=userid, iDocId=document_id, isTrialPaidDocExtraction=isTrialPaidDocExtraction, isEndOfExtraction=True,isUserMetaData=bPerformAWSExtraction)
                    await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=userid, strPlanType="pro" if isGPTEnabled else "standard" , iPageLimit=dictDocumentData.get("PageCount"), strOperation="subtract" if isGPTEnabled else "add")
                    
                    
                    # UniqueDocumentDate, TotalAmount, UniqueDocumentNumber
                    await CDocumentData.MSUpdateDocFieldsFromGPTResponse(dictGPTAPIResopnse= dictDocExtractionResponse, iUserId= userid, iDocId = document_id, ModelNameAlias= objAPIResponse.get("extraction_parameters", {}).get("ModelNameAlias", "-"))
                    
                    response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= userid,document_id=document_id, dictAPIRespnse = dictDocExtractionResponse, strVoucherType = strVoucherType)
                    end_extract_time =  datetime.now()
                    extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
                    print(f"End GROK/GPT API (DocID: {document_id}) -  Time taken: {extract_time_taken:.2f} seconds.")
                    return response

            except HTTPException as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CDocumentData.MSUpdateDocumentStatus(iUserID=userid, iDocId=document_id, eNewStatus="Error", strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}",strDocErrorMsg=e.detail)
                strClientREQID
                await CAVRequestDetail.MSUpdateRecord(iUserId=userid, strClientREQID = strClientREQID,DocID = document_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE",strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")
                # await CGPTResponseData.MSLogExtractionStatus(strLogType="Error", iUserID=userid, iDocId=document_id, isTrialPaidDocExtraction=isTrialPaidDocExtraction, isEndOfExtraction=False)
                response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= userid,document_id=document_id, dictAPIRespnse = {})
                return response
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                # await CGPTResponseData.MSLogExtractionStatus(strLogType="Error", iUserID=userid, iDocId=document_id, isTrialPaidDocExtraction=isTrialPaidDocExtraction, isEndOfExtraction=False)
                await CAVRequestDetail.MSUpdateRecord(iUserId=userid, strClientREQID = strClientREQID,DocID = document_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE",strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")
                await CDocumentData.MSUpdateDocumentStatus(iUserID=userid, iDocId=document_id, eNewStatus="Error", strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}", strDocErrorMsg=e.detail,DocExtractionAPIStatusCode=e.status_code)

                response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= userid,document_id=document_id, dictAPIRespnse = {})
                return response

    @staticmethod
    async def _callAWSAPI(iUserID, document_id, strVoucherType):
        try:
            objDocMetaDataResponse = None
            start_extract_time = datetime.now()
            # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
            print(f"Start AWS API (DocID: {document_id}) - {start_extract_time}")
            userData = await CAuthController.MSGetSingleUser(user_id= iUserID)

            dictDocumentData = await CDocumentData.MSGetDocById(user_id=iUserID, docId= document_id,isBinaryDataRequired=True, isUserMetaData=True, strVoucherType = strVoucherType)
            objDocMetaData = ExtractTextFromDoc(dictUserData=userData, dictDocumentData=dictDocumentData)

            # Extract text from Document , Use Existed Raw Txt Available
            strRawTextInput = None
            if isinstance(dictDocumentData.get("DocumentRawTxt", None), dict):
                strRawTextInput = dictDocumentData.get("DocumentRawTxt").get("RawTextInput")
                objDocMetaDataResponse = {
                    "page_height":"",
                    "page_width":"",
                    "extracted_text":strRawTextInput # Customize Raw Text to Serve as Input for the OpenAI Model Response
                }
            else:
                objDocMetaDataResponse = await objDocMetaData.MExtractTxtFromDoc(isGPTEnabled = True, bPerformAWSExtraction=True)
                # Insert AWS Object & Upload Raw Text In Document Table For Resuability
                if objDocMetaDataResponse:
                    obj_response = {
                            "AWSExtractedText": objDocMetaDataResponse.get("extracted_text")
                            }
                    
                    await CGPTResponseData.insert_gpt_result(iUserId=iUserID, doc_id=document_id, obj_response=obj_response, dict_response= None,iModelId=dictDocumentData.get("ModelId", None),isGPTEnabled=True, bAwsExtraction=True)
                    # Update the AdditionalDocDetails column in the UploadedDoc table
                    
                    await CDocumentData.MSUpdateUploadedDocRecord(
                        iUserId=iUserID,
                        iDocId=document_id,
                        DocumentRawTxt = {'RawTextInput': objDocMetaDataResponse.get("extracted_text")},
                        DocumentRawTxtObject= objDocMetaDataResponse.get("AWSResponse")
                    )
            end_extract_time =  datetime.now()
            extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
            print(f"End AWS API (DocID: {document_id}) -  Time taken: {extract_time_taken:.2f} seconds.")
            return objDocMetaDataResponse
        except Exception as e:
            print("Error ", traceback.format_exc())
            raise e
    
    @staticmethod
    async def _callExtractionAPI(iUserID, strVoucherType, iDocumentID, strClientREQID, objRawTxtAsUserContent, bIsDeveloperMode=False):
        PromptId = None  # Default value to use if keys are missing or if there is no "Id"
        bPerformAWSExtraction = True
        dictPromptData = {}
        strModelName = "Unknown"
        start_extract_time = datetime.now()
        # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
        print(f"Start GROK/GPT API  (DocID: {iDocumentID}) - {start_extract_time}")
        # NOTE: GENERALIZE_PWI_V3
        bReasoningEnabled = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = iUserID, strVoucherType = strVoucherType)
        bGeneralizePWIV3 = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = iUserID, strVoucherType = strVoucherType)
        try:
            dictAPIEndpointResponse=await ApplicationLayerProcessing.CheckExistDocumentExtractionResponse(iUserID,iDocumentID,strVoucherType = strVoucherType)
            if dictAPIEndpointResponse:
                await CDocumentData.MSUpdateDocFieldsFromGPTResponse(dictGPTAPIResopnse= dictAPIEndpointResponse["Document"], iUserId= iUserID, iDocId = iDocumentID)
                await CLogController.MSWriteLog(iUserID, "Info", f"REUSE GPT API Response: - DocID: {iDocumentID}, dictAPIEndpointResponse : {dictAPIEndpointResponse}")
                return dictAPIEndpointResponse
            else:
                # if bReUseExistGPTResponse:
                #     dictCheckExistance = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= userid,document_id=document_id, dictAPIRespnse = dictDocExtractionResponse)
                #     if dictCheckExistance["bExisingGPTResponseAvailable"]:
                #         return dictCheckExistance
                # Get User Configuration Data
                userData = await CAuthController.MSGetSingleUser(user_id= iUserID)
                isGPTEnabled = True

                dictDocumentData = await CDocumentData.MSGetDocById(user_id=iUserID, docId= iDocumentID,isBinaryDataRequired=True, isUserMetaData=bPerformAWSExtraction, strVoucherType = strVoucherType)

                # Get Prompt and Model Table Data base on UserID, DocId, is Trial Doc Extraction
                # NOTE: GENERALIZE_PWI_V3
                if not bGeneralizePWIV3:
                    # To update weather paid model is used or not for current extraction
                    await CDocumentData.MSUpdateDoc(iUserID=iUserID, iDocId = iDocumentID, bUsedPaidModel=isGPTEnabled)
                    # TODO: Reasoning Model please add vendorName
                    dictPromptData = await CPromptTable.MSGetPromptDataBaseOnDocID(iUserID=iUserID, docID=iDocumentID, isPaidDocExtraction=isGPTEnabled)

                    # get prompt id which used for that document
                    PromptId = dictPromptData.get("PromptTable").get("Id")
                    strModelName = dictPromptData.get("ModelTable").get("ModelName")
                    bReasoningEnabled = dictPromptData.get("isReasoningEnabled", False)

                if bPerformAWSExtraction:
                    try:
                        # NOTE: GENERALIZE_PWI_V3
                        if bGeneralizePWIV3:
                            if iUserID == 11:
                                dictResponseFormat = CJSONFileReader.read_json_file(r"Data\Customer\Abhinav InfraBuild\APIResponseFormat.json")
                            elif iUserID == 10:
                                dictResponseFormat = CJSONFileReader.read_json_file(r"Data\Customer\VedanshSchool\GPTAPI_GeneralPWOI_ResponseSchema_V3.1.json")
                            elif iUserID == 2:
                                    dictResponseFormat = CJSONFileReader.read_json_file(r"Data\Customer\17_ParagTraders\APIResponseFormat.json")
                            else:
                                #  ASSUME PWI V3 General Config
                                dictResponseFormat = CJSONFileReader.read_json_file(r"Data\General_Config\PurchaseWithInventory\APIResponseFormat.json")
                        else:
                            #TODO: please add new  vendor gpt schema format
                            # WARN: Please remove this static code and make this dynamic
                                dictResponseFormat = CommonHelper.MSGetResponseFormat(strModelName)
                    except FileNotFoundError as e:
                        raise HTTPException(status_code=500, detail=f"Failed to load response format file: {str(e)}")
                    except json.JSONDecodeError as e:
                        raise HTTPException(status_code=500, detail=f"Invalid JSON format in response file: {str(e)}")

                # call GPT for paid users
                objAPIResponse = await CGPTResponseData.performGPTExtractionProcess(document_id=iDocumentID,
                                                                                    userData=userData,
                                                                                    objDocMetaDataResponse=objRawTxtAsUserContent,
                                                                                    promptData=dictPromptData.get("PromptTable" ,None),
                                                                                    bIsAWSExtration=bPerformAWSExtraction,
                                                                                    dictResponseFormat=dictResponseFormat,
                                                                                    bReasoningEnabled=bReasoningEnabled, strVoucherType=strVoucherType, bIsDeveloperMode = bIsDeveloperMode, strModelName = strModelName)
        
                
                try:
                    obj_response, dictDocExtractionResponse = objAPIResponse.get("obj_response"), objAPIResponse.get("dict_response")
                except Exception as e:
                    # raise HTTPException(status_code=500,detail="Server is under heavy load. Unable to process this Document, Please try again later.")
                    await CLogController.MSWriteLog(iUserID, "WARNING", f"Lite Mode :- Incorporate pre-defined sample model data into the document extraction process.")
                    obj_response,dictDocExtractionResponse = dictPromptData.get("ModelTable").get("preDefinedModelDict"), dictPromptData.get("ModelTable").get("preDefinedModelDict")
                dictDocExtractionResponse["meta_data"] = {}
                dictDocExtractionResponse["meta_data"]["page_height"] = objRawTxtAsUserContent.get("page_height")
                dictDocExtractionResponse["meta_data"]["page_width"] = objRawTxtAsUserContent.get("page_width")

                if bPerformAWSExtraction:
                    obj_response = {
                        "ExtractionObject": obj_response,
                        "AWSExtractedText": objRawTxtAsUserContent.get("extracted_text")
                        }

                await CGPTResponseData.insert_gpt_result(iUserId=iUserID, doc_id=iDocumentID, strModelName= strModelName, obj_response=obj_response, dict_response= dictDocExtractionResponse, PromptId=PromptId,iModelId=dictDocumentData.get("ModelId", None),isGPTEnabled=isGPTEnabled, bAwsExtraction=bPerformAWSExtraction)

                # IGNORE: Set Document Extraction Status to Success
                await CDocumentData.MSUpdateDocumentStatus(iUserID=iUserID, iDocId=iDocumentID, eNewStatus="ToBeApproved",DocExtractionAPIStatusCode=200)
                await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=iUserID, strPlanType="pro" if isGPTEnabled else "standard" , iPageLimit=dictDocumentData.get("PageCount"), strOperation="subtract" if isGPTEnabled else "add")
                
                
                # UniqueDocumentDate, TotalAmount, UniqueDocumentNumber
                await CDocumentData.MSUpdateDocFieldsFromGPTResponse(dictGPTAPIResopnse= dictDocExtractionResponse, iUserId= iUserID, iDocId = iDocumentID)
                
                response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= iUserID,document_id=iDocumentID, dictAPIRespnse = dictDocExtractionResponse, strVoucherType = strVoucherType)
                end_extract_time =  datetime.now()
                extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
                print(f"End GROK/GPT API (DocID: {iDocumentID}) -  Time taken: {extract_time_taken:.2f} seconds.")
                return response

        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Process the PDF")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=iUserID, iDocId=iDocumentID, eNewStatus="Error", strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}",strDocErrorMsg=e.detail)
            strClientREQID
            await CAVRequestDetail.MSUpdateRecord(iUserId=iUserID, strClientREQID = strClientREQID,DocID = iDocumentID , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE",strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")

            response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= iUserID,document_id=iDocumentID, dictAPIRespnse = {})
            return response
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Process the PDF")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CAVRequestDetail.MSUpdateRecord(iUserId=iUserID, strClientREQID = strClientREQID,DocID = iDocumentID , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE",strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=iUserID, iDocId=iDocumentID, eNewStatus="Error", strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}", strDocErrorMsg=e.detail,DocExtractionAPIStatusCode=e.status_code)

            response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(user_id= iUserID,document_id=iDocumentID, dictAPIRespnse = {})
            return response

    @staticmethod
    async def performGeminiExtractionProcess(document_id,userData, objDocMetaDataResponse, promptData):
        async with AsyncSessionLocal() as db:
            userid = userData.get('uid',None)
            objDocExtraction = None
            try:
                if userid is None:
                    raise HTTPException(status_code=404,detail="User does not exist.")
                if objDocMetaDataResponse.get("extracted_text",None) is None:
                    raise HTTPException(status_code=500,detail="Failed to Retrieve Text from the Document")

                objDocExtraction = CallGeminiAPI(document_id=document_id,UserData=userData, docExtractedTxt=objDocMetaDataResponse.get("extracted_text",None), iDocPageCount = objDocMetaDataResponse.get("page_count",None))

                await objDocExtraction.initialize()
                objResponse = await objDocExtraction.run(objPromptData=promptData)
                return {"obj_response":objResponse.get("response"),
                            "dict_response": objResponse.get("json_objects")}
            except HTTPException as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500,
                    detail="Oops! Something went wrong. Please give it another try in a little while."
                )

    @staticmethod
    async def performGPTExtractionProcess(document_id, userData, objDocMetaDataResponse, promptData, bIsAWSExtration = False, dictResponseFormat = {}, bReasoningEnabled=False, strVoucherType = "Unknown", bIsDeveloperMode = False, strModelName= None):
        async with AsyncSessionLocal() as db:
            userid = userData.get('uid',None)
            objDocExtraction = None
            # NOTE: GENERALIZE_PWI_V3
            bGeneralizePWIV3 = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = userid, strVoucherType = strVoucherType)
            dictResponse = None
            try:
                if userid is None:
                    raise HTTPException(status_code=404,detail="User does not exist.")

                if objDocMetaDataResponse.get("extracted_text",None) is None:
                    raise HTTPException(status_code=500,detail="Failed to Retrieve Text from the Document")
                
                # Initialize Class
                objDocExtraction = CGPTAPIResponseV2(document_id=document_id,user_id=userid, 
                    bDebug=False, 
                    strClientName = userData["name"],
                    strVoucherType = strVoucherType,
                    isDevelopmentMode = bIsDeveloperMode)
                dictExtractAPIParams = {}
                dictMetadata = CGPTAPIResponseV2.MSBuildOpenAIMetadata(
                    strClientName=userData["name"],
                    strvourcherType=strVoucherType,
                    bDevelopmentMode=bIsDeveloperMode
                )
                if bGeneralizePWIV3:
                    strSystemContent = None
                    # Read the system prompt text file
                    # Use GPTo3Reasoning , GPT4o , GrokReasoning, Grok3
                    if userid == 11:
                        strSystemContentFile = r"Data\Customer\Abhinav InfraBuild\GPTAPI_GeneralPWI_SystemPrompt_V3.txt"
                        with open(strSystemContentFile, "r", encoding="utf-8") as file:
                            strSystemContent = file.read()
                        dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName="GrokReasoning", strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = strSystemContent, dictMetadata=dictMetadata, bIsGrokAPI= True, bIsReasoningModel=True, reasoning_effort="high")
                    elif userid == 2:
                        strSystemContentFile = r"Data\Customer\17_ParagTraders\GPTAPI_GeneralPWI_SystemPrompt_V3.txt"
                        with open(strSystemContentFile, "r", encoding="utf-8") as file:
                            strSystemContent = file.read()

                        # NOTE: Grok Reasoning with high Effort
                        dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName="GrokReasoning", strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = strSystemContent, dictMetadata=dictMetadata, bIsGrokAPI= True, bIsReasoningModel=True, reasoning_effort="high")
                    elif userid == 10:
                        strSystemContentFile = Path(r"Data/Customer/VedanshSchool/GPTAPI_GeneralPWOI_SystemPrompt_V3.txt")
                        with open(strSystemContentFile, "r", encoding="utf-8") as file:
                            strSystemContent = file.read()
                        # NOTE: Grok Reasoning with Low Effort
                        dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName="GrokReasoning", strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = strSystemContent, dictMetadata=dictMetadata, bIsGrokAPI= True, bIsReasoningModel=True, reasoning_effort="low") #  
                    else:
                        #  ASSUME PWI V3 General Config, NOTE: Grok API Default Parameter Use             defaultConfig["strModel"] = "grok-3-mini" defaultConfig["max_tokens"] = 27000 defaultConfig["bIsGrokAPI"] = True defaultConfig["bIsReasoningModel"] = True defaultConfig["reasoning_effort"] = "high"
                        strSystemContentFile =  Path(r"Data/General_Config/PurchaseWithInventory/GPTAPI_SystemPrompt_V3.txt")
                        dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName="GrokReasoning", strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = strSystemContent, dictMetadata=dictMetadata, bIsGrokAPI= True, bIsReasoningModel=True, reasoning_effort="low")
                    
                else:
                    # NOTE: Make sure to create ModelID , PromptID - strSystemContent Use
                    # NOTE: Use ModelID for custom Extraction API Configuration
                    # if strModelName is not None and strModelName in ["anl singapore pte. ltd.", "anl singapore pte. ltd","anl singapore pte. ltd. c/o ccai","united liner shipping services llp"]:
                    #     dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName="GrokReasoning", strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = promptData.get("Prompt",None),dictMetadata=dictMetadata)
                    # else:

                    # Using Config File to determine model and its related data

                    dictUserConfig = {}

                    # Reading Config 
                    try:
                        strGrokConfigFilePathb = Path(r"resource/TDLUserConfig.json")
                        with open(strGrokConfigFilePathb, 'r') as file:
                            dictUserConfig = json.load(file)
                    except FileNotFoundError as fe:
                        raise fe
                    
                    # Determining the User
                    result = dictUserConfig[str(userid)]['ExtractionAPIVendorWiseConfig'].get(strModelName, dictUserConfig[str(userid)]['ExtractionAPIVendorWiseConfig']['Unknown'])


                    dictExtractAPIParams = CGPTAPIResponseV2.MSSetExtractAPIConfig(strExtractAPIName=result['strExtractAPIName'], strUserContent = objDocMetaDataResponse.get("extracted_text",None), dictResponseFormat = dictResponseFormat, strSystemContent = promptData.get("Prompt",None),dictMetadata=dictMetadata,
                    bIsGrokAPI = result['bIsGrokAPI'], bIsReasoningModel = result.get('bIsReasoningModel', False),reasoning_effort = result.get('reasoning_effort', 'low'), max_tokens = result['max_tokens'])
                    
                dictResponse = await objDocExtraction.runExtractAPI(**dictExtractAPIParams)

                
                # update user token usage
                # await CUserAPIUsageData.MSUpdateUserApiTokenUsage(iUserId=userid, iAdditionalTokens=dictResponse['response']['usage']['total_tokens'])

                return {"obj_response": dictResponse["response"],
                        "dict_response": dictResponse["json_objects"],
                        "extraction_parameters": dictExtractAPIParams,
                        }
            except HTTPException as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userid, "Error", f"Failed to Process the PDF")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500,
                    detail="Oops! Something went wrong. Please give it another try in a little while."
                )


    @staticmethod
    async def insert_gpt_result(iUserId: int, doc_id: int, isGPTEnabled: bool, obj_response: dict = None, dict_response:dict = None, iModelId:int = None, PromptId: int = None,  bAwsExtraction: bool = False, strModelName: str = None):
        """
        Inserts GPT result into the database.

        Args:
            iUserId (int): The user ID performing the insertion.
            doc_id (int): The ID of the document for which GPT result is being inserted.
            obj_response (dict): The GPT response object.

        Raises:
            HTTPException: If insertion fails.
        """
        # Define db outside of the try block for accessibility in the except block
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(iUserId, "Info", f"Process of inserting GPT result for DocID {doc_id} into the database started.")

                # Check for existing entry
                existing_entry = await db.execute(
                    select(DocExtractedData).where(DocExtractedData.DocId == doc_id, DocExtractedData.DocExtractionPromptID == PromptId)
                )
                existing_entry = existing_entry.scalars().first()

                if bAwsExtraction:
                    JsonSerializeDocVerifiedData = dict_response
                    JsonSerializeDocApprovedData = {}
                else:
                    dictUserProvidedDocFields = await CVendorController.GetAllReqInvoiceFields(iUserID=iUserId , iModelId = iModelId)
                    dictVerifiedData = CKeyValMapper.MSMapUserGivenFields(dictUserProvidedDocFields.get("ModelData"), dict_response)
                    if isGPTEnabled:
                        # GPT Ensure Cordinates Data
                        dictVerifiedData = CExtractCoordinatesHelper.EnsureExtractedDataCordinates(dictVerifiedData)
                    dictInitialApprovedData = await CExtractedDataHelper.MSInitializeVerificationStatus(dictVerifiedData)

                    # Serialize DocVerifiedData and DocApprovedData to JSON if they are dictionaries
                    JsonSerializeDocVerifiedData = json.dumps(dictVerifiedData, ensure_ascii=False) if isinstance(dictVerifiedData, dict) else dictVerifiedData
                    JsonSerializeDocApprovedData = json.dumps(dictInitialApprovedData, ensure_ascii=False) if isinstance(dictInitialApprovedData, dict) else dictInitialApprovedData


                if existing_entry:
                    # Update the existing entry
                    if obj_response is not None:
                        setattr(existing_entry, "Response", obj_response)
                    if JsonSerializeDocVerifiedData is not None and JsonSerializeDocVerifiedData:
                        setattr(existing_entry, "DocVerifiedData", JsonSerializeDocVerifiedData)
                    if JsonSerializeDocApprovedData is not None and JsonSerializeDocApprovedData:
                        setattr(existing_entry, "DocApprovedData", JsonSerializeDocApprovedData)
                    await CLogController.MSWriteLog(iUserId, "Info", f"Updating GPT result for DocID {doc_id} in the database.")
                else:
                    # Create DocExtractedData entry
                    gpt_process_data_entry = DocExtractedData(
                        DocId=doc_id,
                        Response=obj_response,  # Ensure this is correctly serialized if needed
                        DocExtractionPromptID=PromptId,
                        DocVerifiedData=JsonSerializeDocVerifiedData,   # insert json serialization extracted json data into db
                        DocApprovedData=JsonSerializeDocApprovedData
                    )

                    # Add entry to the database
                    db.add(gpt_process_data_entry)
                    await CLogController.MSWriteLog(iUserId, "Info", f"Adding new GPT result for DocID {doc_id} to the database.")

                await db.commit()

                await CLogController.MSWriteLog(iUserId, "Info", f"Successfully committed GPT result for DocID {doc_id} to the database.")
                return

            except SQLAlchemyError as e:
                await db.rollback()

                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to insert GPT result for DocID {doc_id} into the database.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="Error occurred while storing Extracted data result")

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e

            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to insert GPT result for DocID {doc_id} into the database.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="Error Occured while Processing the Document.")


    @staticmethod
    async def MSGetDocumentById(iUserID: int, iDocId: int):
        """
        NOTE :- Mention strOutputType as GPT  if you want to take GPT Json Output
        Purpose: Fetch a document and its associated GPT process data from the database by the document ID.

        Inputs: (1) iDocId: The document ID to fetch the document for.

        Output: A dictionary containing the document data and GPT process data.

        Example: await CDocumentData.get_document_with_docid(iDocId=789)
        """
        if int(iDocId) <= 0:
            await CLogController.MSWriteLog(iUserID, "Error", f"The document ID provided is not valid.")
            raise HTTPException(status_code=400, detail="There was an error processing your request.")

        async with AsyncSessionLocal() as db:
            try:
                docApprovedDict = {}
                dictExtractedDocData = {}
                dictCordinatesData = {}
                docPageHeight = 0
                docPageWidth = 0
                docExtractedData = None

                # Get Document Data
                uploaded_doc_data = await CDocumentData.MSGetDocById(user_id=iUserID, docId= iDocId,isBinaryDataRequired=True)
                # Get Latest Extracted Document -> corresponding Prompt Data if GPT then GPT Prompt Data else Gemini Prompt Data
                if uploaded_doc_data.get("Status").lower() != "error":
                    docExtractedData = await CExtractionTable.MSGetLatestExtractedData(user_id=iUserID,
                                        doc_id= iDocId,bRaiseError=False)


                if docExtractedData is not None:
                    dictExtractedData =  docExtractedData.get("DocVerifiedData")
                    promtId = docExtractedData.get("DocExtractionPromptID")
                    ModelSeries = await CPromptController.MSGetModelNameFromPromptID(iUserID=iUserID, iPromptID=promtId)
                    if Constants.GeminiAPIModelName.lower() in str(ModelSeries).lower() and (dictExtractedData is not None):
                        dictExtractedDocData = CExtractCoordinatesHelper.transformForExtractedFormat(dictExtractedData)
                        dictCordinatesData = CExtractCoordinatesHelper.transformForAppLayerWithCordinates(dictExtractedData)
                    elif Constants.GPTAPIModelName.lower() in str(ModelSeries).lower() and (dictExtractedData is not None):
                        dictCordinatesData = dictExtractedData
                        dictExtractedDocData = CExtractCoordinatesHelper.transformForExtractedFormat(dictCordinatesData)
                        # remove meta data from coordinatesdata
                        if 'meta_data' in dictCordinatesData:
                            dictCordinatesData.pop("meta_data")

                # Get Predefined Extracted Result if Response not found
                dictModelData = await CVendorController.MSGetModelByID(iUserID = iUserID, modelId = uploaded_doc_data.get("ModelId"))
                if docExtractedData is not None:
                    docApprovedDict = docExtractedData.get("DocApprovedData")
                else:
                    docApprovedDict = {}

                if (dictExtractedDocData is None or not dictExtractedDocData or docExtractedData is None) and uploaded_doc_data.get("Status").lower() != "notprocess" and uploaded_doc_data.get("Status").lower() != "processing":
                    dictExtractedDocData = dictModelData.get("preDefinedModelDict")
                    dictCordinatesData = CExtractCoordinatesHelper.transformForAppLayerWithCordinates(dictExtractedDocData)

                # Convert keys to lowercase
                dictExtractedDocData_lower = {key.lower(): value for key, value in dictExtractedDocData.items()}

                # Check if "tables" key is present
                if "tables" not in dictExtractedDocData_lower.keys() and dictExtractedDocData:
                    dictExtractedDocData["Tables"] = []

                if 'meta_data' in dictExtractedDocData:
                    # If 'meta_data' is present, remove it from the dictionary and retrieve 'page_width' and 'page_height'
                    metaData = dictExtractedDocData.pop("meta_data")
                    docPageWidth = metaData.get("page_width") if metaData.get("page_width") is not None else docPageWidth
                    docPageHeight = metaData.get("page_height") if metaData.get("page_height") is not None else docPageHeight

                if 'meta_data' in docApprovedDict:
                    docApprovedDict.pop("meta_data")

                # Get Model Fields Data
                dictUserProvidedDocFields = await CVendorController.GetAllReqInvoiceFields(iUserID=iUserID , iModelId = uploaded_doc_data.get("ModelId"))

                response_data = {
                    "UploadedDoc": uploaded_doc_data,
                    "DocType": uploaded_doc_data.get("file_type"),
                    "DocExtractedData": dictExtractedDocData,
                    "DocCordinateData":dictCordinatesData,
                    "ApprovedFields": docApprovedDict,
                    "ModelFields": dictUserProvidedDocFields.get("ModelData"),
                    "page_height":docPageHeight,
                    "page_width":docPageWidth
                }
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched GPT Processed Data for Document id {iDocId}.")

                return response_data
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except SQLAlchemyError as e:
                # Handle specific database errors
                await CLogController.MSWriteLog(iUserID, "Error", f"Database error while fetching documents for ModelTableTable named': {e}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Unable to fetch the requested document. Please try again later.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch the GPT Processed Data for Document id {iDocId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail=f"There was an error retrieving the document.")

    @staticmethod
    async def MSGetDocumentByIdForAWSExtraction(iUserID: int, iDocId: int):
        """
        NOTE :- Mention strOutputType as GPT  if you want to take GPT Json Output
        Purpose: Fetch a document and its associated GPT process data from the database by the document ID.

        Inputs: (1) iDocId: The document ID to fetch the document for.

        Output: A dictionary containing the document data and GPT process data.

        Example: await CDocumentData.get_document_with_docid(iDocId=789)
        """
        if int(iDocId) <= 0:
            await CLogController.MSWriteLog(iUserID, "Error", f"The document ID provided is not valid.")
            raise HTTPException(status_code=400, detail="There was an error processing your request.")

        async with AsyncSessionLocal() as db:
            try:
                docApprovedDict = {}
                dictExtractedDocData = {}
                dictCordinatesData = {}
                docPageHeight = 0
                docPageWidth = 0
                docExtractedData = None

                # Get Document Data
                uploaded_doc_data = await CDocumentData.MSGetDocById(user_id=iUserID, docId= iDocId,isBinaryDataRequired=True, isUserMetaData=True)
                # Get Latest Extracted Document -> corresponding Prompt Data if GPT then GPT Prompt Data else Gemini Prompt Data
                if uploaded_doc_data.get("Status").lower() != "error":
                    docExtractedData = await CExtractionTable.MSGetLatestExtractedData(user_id=iUserID,
                                        doc_id= iDocId,bRaiseError=False)


                if docExtractedData is not None:
                    dictExtractedData =  docExtractedData.get("DocVerifiedData")

                response_data = {
                    "UploadedDoc": uploaded_doc_data,
                    "DocType": uploaded_doc_data.get("file_type"),
                    "DocExtractedData": dictExtractedData,
                    "DocCordinateData":dictCordinatesData,
                    "ApprovedFields": docApprovedDict,
                    "page_height":docPageHeight,
                    "page_width":docPageWidth
                }
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched GPT Processed Data for Document id {iDocId}.")

                return response_data
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except SQLAlchemyError as e:
                # Handle specific database errors
                await CLogController.MSWriteLog(iUserID, "Error", f"Database error while fetching documents for ModelTableTable named': {e}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Unable to fetch the requested document. Please try again later.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch the GPT Processed Data for Document id {iDocId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail=f"There was an error retrieving the document.")


    @staticmethod
    async def InsertApprovedFields(iUserID, iDocId, dictApprovedField, PromptId):
        '''
        Purpose : To append the approved field in a list in GptProcess table in UserApprovedFields column

        Input : (1) iDocID : Id of document which is processing
                (2) dictApprovedField: Dictionary containing the approved fields

        Output : (1) None

        Example :
        '''
        try:
            async with AsyncSessionLocal() as db:

                # Fetch the latest JSON document based on CreatedTime
                result = await db.execute(
                    select(DocExtractedData)
                    .filter(DocExtractedData.DocId == iDocId, DocExtractedData.DocExtractionPromptID == PromptId)
                    .order_by(desc(DocExtractedData.ModifiedDateTime))
                    .limit(1)
                )
                doc_extracted_data = result.scalars().first()
                if doc_extracted_data is None:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to update approved fields in the document processing system. Please try again. For Document id {iDocId}.")
                    raise HTTPException(status_code=404, detail="There was an error processing your request.")

                # Get the existing approved fields
                strApprovedFields = doc_extracted_data.ApprovedFields if doc_extracted_data.ApprovedFields else []

                if strApprovedFields:
                    lsApprovedFields = list(strApprovedFields.split(","))
                else:
                    lsApprovedFields = []

                # Append unique fields to the approved fields list
                for field in dictApprovedField.keys():
                    if field.strip():
                        if field not in lsApprovedFields:
                            lsApprovedFields.append(field)

                # Creating a single string of all felds
                strApprovedFieldsUpdated = ",".join(lsApprovedFields)

                # Update the record with the modified approved fields list
                doc_extracted_data.ApprovedFields = strApprovedFieldsUpdated
                await db.commit()
                return lsApprovedFields
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e

        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch the All GPT Processed Data Updates for Document id {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Something went wrong during processing.")


    @staticmethod
    async def MSSetExtractValidatedData(iUserID, data: GPTJSONUpdate, doc_id:int, bInitialCall=False):
        """
            Purpose: To insert updates user has made to GPTjson
        """
        async with AsyncSessionLocal() as db:  # Assume AsyncSessionLocal is your async database session
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process to Insert JSON Updates Started.")

                # Fetch the latest JSON document based on CreatedTime
                result = await db.scalar(
                    select(DocExtractedData)
                    .filter(DocExtractedData.DocId == doc_id)
                    .order_by(desc(DocExtractedData.ModifiedDateTime))
                    .limit(1)
                )

                if result is None:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to update approved fields in the document processing system. Please try again. For Document id {doc_id}.")
                    raise HTTPException(status_code=404, detail="There was an error processing your request.")

                dictToBeReturned = json.loads(result.DocVerifiedData)

                CDictHelper.merge_dicts(dictToBeReturned, data.UpdatedVal)

                dictToBeReturned = json.dumps(dictToBeReturned)

                result.DocVerifiedData = dictToBeReturned
                flag_modified(result, "DocVerifiedData")       # Mark the attribute as modified

                # if not bInitialCall:
                dictApprovedStatus = json.loads(result.DocApprovedData)

                await CExtractedDataHelper.MSGetVerifiedData(dictApprovedStatus, data.UpdatedVal, data.bApprove)
                strApprovedstatus = json.dumps(dictApprovedStatus)
                dictApprovedStatus = dictApprovedStatus

                result.DocApprovedData = strApprovedstatus
                flag_modified(result, "DocApprovedData")    # Mark the attribute as modified


                await db.commit()


                # Check if any value in the dictionary is 0
                bisAnyFieldsApprovalPending = any(0 in field.values() for field in dictApprovedStatus["Fields"]) or any(0 in entry.values() for table in dictApprovedStatus["Tables"] for entries in table.values() for entry in entries)

                if bisAnyFieldsApprovalPending and not data.bApprove :
                    documentResponse = await CDocumentData.MSUpdateDocumentStatus(iUserID=iUserID, iDocId=doc_id,eNewStatus="ToBeApproved")
                elif not bisAnyFieldsApprovalPending:
                    documentResponse = await CDocumentData.MSUpdateDocumentStatus(iUserID=iUserID, iDocId=doc_id,eNewStatus="Approved")

                docExtractedData = await CExtractionTable.MSGetLatestExtractedData(user_id=iUserID,
                                      doc_id= doc_id,bRaiseError=False)
                objDocumentData = await CDocumentData.MSGetDocMetaDataByDocId(userid=iUserID,doc_id=doc_id)
                docExtractedData["Status"] = objDocumentData.get("Status")
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Inserted JSON Updates.")
                return docExtractedData
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Insert Json Updates to database")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                status_code=500, detail="Something went wrong during processing.")

    @staticmethod
    async def MSGetAllDocOfVendor(iUserID: int, strModelName: str,  iLimit: int = Query(10, gt=0), iPage: int = Query(1, gt=0),strOutputType="Gemini"):
        async with AsyncSessionLocal() as db:  # Assuming AsyncSessionLocal is correctly set up
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching the All Processed Documents Data for Model Named {strModelName} started.")

                # Fetch Vendor ID by vendorName
                lsObjVendors = await db.execute(select(ModelTable).filter(ModelTable.Name == strModelName, ModelTable.UserID == iUserID))
                objVendor = lsObjVendors.scalars().first()
                if not objVendor:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Model '{strModelName}' not found.")

                    raise HTTPException(status_code=404, detail=f"Model '{strModelName}' not found.")

                # Fetch total count of documents for the Vendor (for pagination)
                total_docs_query = await db.scalar(select(func.count()).where(UploadedDoc.ModelId == objVendor.Id))

                # Calculate offset based on the current page
                iOffset = (iPage - 1) * iLimit

                # Fetch all documents for the Vendor
                docs_result = await db.execute(
                                                select(UploadedDoc)
                                                .filter(UploadedDoc.ModelId == objVendor.Id)
                                                .limit(iLimit)
                                                .offset(iOffset)
                                            )
                lsObjDocs = docs_result.scalars().all()

                lsAllDocsOfVendor = []
                for doc in lsObjDocs:

                    try:
                        doc_gpt_data = await CGPTResponseData.get_document_with_user_updates(iUserId= iUserID, doc_id = doc.DocId)
                    except HTTPException as e:
                        doc_gpt_data = None

                    dictData = {
                                    "DocId": doc.DocId,
                                    "DocumentName": doc.DocName,
                                    "DocumentStatus": doc.Status,
                                    "TallyStatus": doc.TallyStatus,
                                    "Processed_Data": doc_gpt_data["Document"] if doc_gpt_data else {},
                                }

                    lsAllDocsOfVendor.append(dictData)

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched All Processed Documents Data for Model Named {strModelName}.")


                # Calculate pagination details
                total_pages = (total_docs_query + iLimit - 1) // iLimit

                return {
                    "documents": lsAllDocsOfVendor,
                    "pagination": {
                        "total_pages": total_pages,
                        "current_page": iPage,
                        "per_page": iLimit
                    }
                }
                # return lsAllDocsOfVendor

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch Processed Documents Data or Model Named {strModelName}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e

            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch Processed Documents Data or Model Named {strModelName}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(status_code=500, detail=f"Failed to Fetch Processed Documents Data or ModelTableTable Named {strModelName}.")

class ProcessStatus(str, Enum):
    PENDING = "pending"
    IN_PROCESS = "in_process"
    COMPLETED = "completed"
    ABORTED = "aborted"

class DocumentProcessor:
    def __init__(self, max_concurrent_tasks=10):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.process_status = {}
        self.task_references = {}

    async def process_document(self, doc_id, user_id, isTrialPaidDocExtraction):
        try:
            async with self.semaphore:
                if self.process_status.get(doc_id) == ProcessStatus.ABORTED:
                    await CLogController.MSWriteLog(user_id, "Info", f"Processing for document {doc_id} aborted before start.")
                    return
                self.process_status[doc_id] = ProcessStatus.IN_PROCESS
                await CLogController.MSWriteLog(user_id, "Info", f"Processing document {doc_id} started.")
                result = await CGPTResponseData.performDocumentExtractionFromDocumentID(userid=user_id, document_id=doc_id, isTrialPaidDocExtraction=isTrialPaidDocExtraction)
                self.process_status[doc_id] = ProcessStatus.COMPLETED
                await CLogController.MSWriteLog(user_id, "Info", f"Processing document {doc_id} completed.")
                self.task_references.pop(doc_id, None)
                return result

        except asyncio.CancelledError:
            self.process_status[doc_id] = ProcessStatus.ABORTED
            await CLogController.MSWriteLog(user_id, "Error", f"Processing document {doc_id} was cancelled.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=400, detail="Document processing was cancelled. Please try again")

        except HTTPException as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error while Process document with DocID: {doc_id}. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e

        except NoResultFound:
            await CLogController.MSWriteLog(user_id, "Error", f"Document with DocId {doc_id} not found. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=404, detail=f"Requested Document Not Found.")

        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error fetching updates for DocID {doc_id}. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="Something went wrong during processing.")


    async def process_documents(self, doc_ids: list[int], user_id: int,isTrialPaidDocExtraction:bool):
        tasks = []
        for doc_id in doc_ids:
            # Make Document Status Queue Before calling API's
            await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id, eNewStatus="Queue")
            self.process_status[doc_id] = ProcessStatus.PENDING
            task = asyncio.create_task(self.process_document(doc_id, user_id,isTrialPaidDocExtraction))
            self.task_references[doc_id] = task
            tasks.append(task)
        result = await asyncio.gather(*tasks, return_exceptions=True)
        return result

class DocumentProcessorUsingHTTPX:
    def __init__(self, max_concurrent_tasks=50, api_base_url=EXTERNAL_API_SERVER,strClientREQID=None, bDevelopmentMode = False, strVoucherType = "Unknown"):
        self.max_concurrent_tasks = max_concurrent_tasks
        self.semaphore = asyncio.Semaphore(self.max_concurrent_tasks)
        self.process_status = {}
        self.task_references = {}
        self.api_base_url = api_base_url
        self.strClientREQID=strClientREQID
        self.strVoucherType = strVoucherType
        self.bDevelopmentMode = bDevelopmentMode

    async def process_document(self, doc_id, user_id, isTrialPaidDocExtraction, token, strMultiThreadConfigPath=Path(r"resource/multiThradConfig.json")):
        async with self.semaphore:
            if self.process_status.get(doc_id) == ProcessStatus.ABORTED:
                await CLogController.MSWriteLog(user_id, "Info", f"Processing for document {doc_id} aborted before start.")
                return
            result = None
            self.process_status[doc_id] = ProcessStatus.IN_PROCESS
            await CLogController.MSWriteLog(user_id, "Info", f"Processing document {doc_id} started.")
            MultiThreadConfigData = await MakeModelGPTPrompt.ReadConfigFile(UserID=user_id, StrPath=strMultiThreadConfigPath)
            retries = MultiThreadConfigData.get("retries", 1)  # Number of retries
            timeout = MultiThreadConfigData.get("timeout", 400)
            for attempt in range(retries):
                try:
                    async with AsyncClient(timeout=timeout) as client:

                        response = await client.post(
                            f"{self.api_base_url}/extract-document-id?DocId={doc_id}&iUserid={user_id}&isTrialPaidDocExtraction={isTrialPaidDocExtraction}&strClientREQID={self.strClientREQID}&strVoucherType={self.strVoucherType}&bIsDeveloperMode={self.bDevelopmentMode}")
                        print(response.json())
                        response.raise_for_status()
                        
                        # Processing the response
                        result = response.json()

                        if result["APIStatusCode"] == 200:
                            self.process_status[doc_id] = ProcessStatus.COMPLETED
                            await CLogController.MSWriteLog(user_id, "Info", f"Processing document {doc_id} completed.")
                            self.task_references.pop(doc_id, None)
                            return result
                        else:
                            self.process_status[doc_id] = ProcessStatus.ABORTED
                            await CLogController.MSWriteLog(user_id, "Error", f"Document {doc_id} failed to process: {result['DocErrorMsg']}")
                            await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = self.strClientREQID,DocID = doc_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: The extraction service is not running at the URL: {self.api_base_url}.", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE",strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")
                            await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id,
                                                                        eNewStatus="Error",
                                                                        DocExtractionAPIStatusCode=500, strDocErrorMsg=result['DocErrorMsg'])

                            # currently We don't want to retrieve api request
                            return result

                except RequestError as e:
                    if attempt < retries - 1:
                        await CLogController.MSWriteLog(user_id, "Warning", f"Attempt {attempt + 1} failed for DocID {doc_id}. Retrying...")
                    else:
                        await CLogController.MSWriteLog(user_id, "Error", f"Error fetching updates for DocID {doc_id}. {str(e)}")

                        await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id,
                                                                        eNewStatus="Error",
                                                                        DocExtractionAPIStatusCode=500, strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}")
                        # AVRequestDetail strAccuVelocityComments, TracebackLogs,
                        await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = self.strClientREQID,DocID = doc_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: The extraction service is not running at the URL: {self.api_base_url}. RequestError: {str(traceback.format_exc())}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")

                        return DocExtractionAPIModel(document_id=doc_id,Document={}, APIStatusCode=e.response.status_code,detail= str(e),DocExtractionStatus =StatusEnum.Error, IsPaidModel=isTrialPaidDocExtraction)


                except HTTPStatusError as e:
                    if attempt < retries - 1:
                        await CLogController.MSWriteLog(user_id, "Warning", f"Attempt {attempt + 1} failed for DocID {doc_id}. Retrying...")
                    else:
                        try:
                            error_detail = json.loads(e.response.text)["detail"]
                        except KeyError:
                            error_detail = str(e)
                        await CLogController.MSWriteLog(user_id, "Error", f"Error fetching updates for DocID {doc_id}. {error_detail}")

                        await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id,
                                                                            eNewStatus="Error",
                                                                            DocExtractionAPIStatusCode=500, strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}")
                        # AVRequestDetail strAccuVelocityComments, TracebackLogs,
                        await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = self.strClientREQID,DocID = doc_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: The extraction service is not running at the URL: {self.api_base_url}. HTTPStatusError: {str(traceback.format_exc())}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")

                        return DocExtractionAPIModel(document_id=doc_id,Document={}, APIStatusCode=e.response.status_code,detail= error_detail,DocExtractionStatus =StatusEnum.Error, IsPaidModel=isTrialPaidDocExtraction)


                except HTTPException as e:
                    await CLogController.MSWriteLog(user_id, "Error", f"Error fetching updates for DocID {doc_id}. {str(e.detail)}")
                    await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id,
                                                                            eNewStatus="Error",
                                                                            DocExtractionAPIStatusCode=500, strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}")
                    # AVRequestDetail strAccuVelocityComments, TracebackLogs,
                    await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = self.strClientREQID,DocID = doc_id , ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - GPT Extraction: The extraction service is not running at the URL: {self.api_base_url}. HTTPException: {str(traceback.format_exc())}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments="Error 3: Failed to extract or understand document content. Please enter the data manually in Tally.")
                    return DocExtractionAPIModel(document_id=doc_id,Document={}, APIStatusCode=e.status_code,detail= e.detail,DocExtractionStatus =StatusEnum.Error, IsPaidModel=isTrialPaidDocExtraction)


    async def process_documents(self, doc_ids: list[int], user_id: int, isTrialPaidDocExtraction: bool, token: str):

        # start---------Verify if user have enough limit for extracting all the documents------------------

        dictUserData = await CAuthController.MSGetSingleUser(user_id = user_id)

        # Calculate current pages to be processed
        iTotalPageCount = 0
        processDocIds = []

        # Determine total available pages and pages left
        if isTrialPaidDocExtraction:
            total_available_pages = dictUserData['total_allowed_page_limit']
            pages_left = dictUserData['page_limit_left']
        else:
            total_available_pages = dictUserData['total_allowed_free_page_limit']
            pages_left = total_available_pages - dictUserData['free_page_limit_usage']

        for iDocId in doc_ids:
            dictDocData = await CDocumentData.MSGetDocById(user_id=user_id, docId=iDocId,strVoucherType= self.strVoucherType)
            doc_page_count = dictDocData['PageCount']
            processDocIds.append(iDocId)


        tasks = []
        exceedPageLimitDocIds = [docId for docId in doc_ids if docId not in processDocIds]
        for doc_id in processDocIds:
            await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id, eNewStatus="Processing")
            self.process_status[doc_id] = ProcessStatus.PENDING
            task = asyncio.create_task(self.process_document(doc_id, user_id, isTrialPaidDocExtraction, token))
            self.task_references[doc_id] = task
            tasks.append(task)
        results = await asyncio.gather(*tasks, return_exceptions=True)
        for doc_id in exceedPageLimitDocIds:
            await CLogController.MSWriteLog(user_id, "DEBUG", f"UserID - {user_id}, DocID - {doc_id},  Msg - You do not have enough pages for extracting documents, please try upgrading your plan")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=user_id, iDocId=doc_id,
                                                                            eNewStatus="Error",
                                                                            DocExtractionAPIStatusCode=429, strDocDebugMsg=f"You do not have enough pages for extracting documents, please try upgrading your plan")
            results.append(DocExtractionAPIModel(document_id=doc_id,Document={}, APIStatusCode=429,detail= "You do not have enough pages for extracting documents, please try upgrading your plan",DocExtractionStatus =StatusEnum.Error, IsPaidModel=isTrialPaidDocExtraction))
        return results

class DocumentUploader:
    def __init__(self, max_concurrent_uploads=10, strVoucherType = None):
        self.max_concurrent_uploads = max_concurrent_uploads
        self.semaphore = asyncio.Semaphore(self.max_concurrent_uploads)
        self.upload_status = {}
        self.upload_references = {}
        self.strVoucherType = strVoucherType
    
    async def upload_document(self, user_id, document, bUsePaidModel=True, bAutoSelectModel=False, strDocStatus = "NotProcess", DocExtractionAPIStatusCode=404, objAdditionalDocDetails =None, BReUseDocData=False,strFamilyName=None, model_name=None):
        file_name = ""
        DocumentRawTxtExtractBy = None
        DocumentRawTxt = None
        DocumentRawTxtObject = None
        # NOTE: GENERALIZE_PWI_V3 For selected Customers we are running Generalize Approach - which do not need to be had modelID and PromptID
        bIsGeneralizePWIV3 = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = user_id, strVoucherType = self.strVoucherType)
        try:
            async with self.semaphore:
                userData = await CAuthController.MSGetSingleUser(user_id=user_id)
                bIsTallyIntegrated =  userData["integration_config"]["isTallyConfigured"] if userData["integration_config"] else False
                # Read binary data of document
                MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB in bytes
                Comment = "" # Initial No Comment on Document
                file_name = document.filename
                strModelName = "Unknown" if bIsGeneralizePWIV3 else document.DocVendorName
                strFamilyName = "Custom"
                # if document.size > MAX_FILE_SIZE:
                #     raise HTTPException(
                #         status_code=400,
                #         detail=f"File size for '{file_name}' exceeds the maximum limit of 20MB. The uploaded file is {document.size / (1024 * 1024):.2f}MB."
                #     )
                file_data = await document.read()
                file_type = Constants.allowed_content_types.get(document.content_type)
                if not file_type:
                    raise HTTPException(status_code=400, detail="Invalid file type : Please upload a valid file type")
                isOtherFormats = file_type.lower().endswith(('docx', 'xlsx', 'csv','txt'))
                iNumPages = 0
                if file_type == "PDF":
                    dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=file_data)
                    iNumPages, docSize = dictConvertedFileData.get("pageCount"), dictConvertedFileData.get("fileSize")
                elif re.match(r'^jpeg|png|webp|bmp|jpg$', file_type.lower()):
                    iNumPages=1

                strCheckSum = Hashing.calculate_checksum(file_data)
                dictResult = await CDocumentData.MSIsDuplicateFile(strCheckSum,user_id, self.strVoucherType)
                #check for uploaded file is duplicated or not
                if dictResult['bIsDuplicate'] and BReUseDocData == False:
                    dictDuplicateFound = dictResult['MatchedRow']
                    dictDuplicateFound["filename"] = file_name
                    # dictDuplicateFound["APIStatusCode"] = 409 # conflict raise
                    # dictDuplicateFound["DocErrorMsg"] = "A duplicate entry was detected."
                    return dictDuplicateFound
                    # raise HTTPException(status_code=409, detail="Duplicate Record Found")

                if dictResult['bIsDuplicate'] and BReUseDocData == True:
                    # Check additional Mandatory Document given or not
                    dfICDPegasusAdditionalInfo = None
                    if objAdditionalDocDetails is not None: #and dictResult['MatchedRow']['AdditionalDocDetails'] is None:
                        # NOTE: All to store additional information for this list of vendors
                        dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
                        # Flatten the list of vendors from all customers into a single list
                        lsVendorNames = [vendor for vendors in dictAvailableCompany["VendorName"] for vendor in vendors]
                        if dictResult['MatchedRow']["ModelName"].lower() in lsVendorNames:
                            # Process the Excel file and convert it to a dictionary
                            dfICDPegasusAdditionalInfo = ReadExcelToDict(objAdditionalDocDetails)

                            # Update the AdditionalDocDetails column in the UploadedDoc table
                            await CDocumentData.MSUpdateUploadedDocRecord(
                                iUserId=user_id,
                                iDocId=dictResult['MatchedRow']["DocId"],
                                AdditionalDocDetails=dfICDPegasusAdditionalInfo
                            )

                            # Update the local copy of the record to reflect the changes
                            dictResult['MatchedRow']['AdditionalDocDetails'] = dfICDPegasusAdditionalInfo

                            await CLogController.MSWriteLog(user_id, "Info", f"Updated AdditionalDocDetails for document ID: {dictResult['MatchedRow']['DocId']}")

                    dictResult['MatchedRow']["filename"] = file_name
                    if dictResult['MatchedRow']["DocRetryDetails"] is not None:
                        lsPrevious = dictResult['MatchedRow'].get("DocRetryDetails").get("ClientREQID", [])
                        lsPrevious.append(document.strClientREQID)
                        dictDocRetryDetails = {"ClientREQID": lsPrevious}
                    else:
                        dictDocRetryDetails = {"ClientREQID": [document.strClientREQID]}

                    iDocRetryCount = dictResult['MatchedRow'].get("DocRetryCount", 1) + 1

                    await CDocumentData.MSUpdateUploadedDocRecord(
                        iUserId=user_id,
                        iDocId=dictResult['MatchedRow']["DocId"],
                        DocRetryCount=iDocRetryCount,
                        DocRetryDetails=dictDocRetryDetails
                    )
                    # AVRecordDetail Table : DocID, IsRecordUIDPresent Update
                    await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= strCheckSum, DOC_UID=dictResult['MatchedRow']["DocId"], IsRecordUIDPresent=True)
                    return dictResult['MatchedRow']

                S3ObjName = CAWSS3Storage.MSGenerateUniqueObjecID()
                if bIsTallyIntegrated:
                    objCAWSS3Storage = CAWSS3Storage(bucket_name = os.getenv("s3_bucket_name_for_tally_docs"), object_name=S3ObjName, user_id=user_id)
                else:
                    objCAWSS3Storage = CAWSS3Storage(object_name=S3ObjName, user_id=user_id)


                isOCRPDF = await ExtractTextFromDoc.isScannedDoc(file_data=file_data, file_type =file_type) if not isOtherFormats else False

                response =  await objCAWSS3Storage.MSetS3Object(file_data, metadata={"x-amz-meta-filename": str(file_name),"x-amz-meta-user_id": str(user_id), "x-amz-meta-file_type":str(file_type),"x-amz-meta-is_scanned_doc":str(isOCRPDF), "x-amz-meta-iPageCount":str(iNumPages)})

                #! ****** For extracting model name from the document itself ****
                dictResponse = {}

                if not bIsGeneralizePWIV3 and bAutoSelectModel and strModelName.strip() =="":
                    try:
                        dictTempDocData = {
                            "DocBinaryData":file_data,
                            "file_type":file_type,
                            "DocS3ObjectKey":S3ObjName
                        }
                        objDocMetaData = ExtractTextFromDoc(dictUserData=userData, dictDocumentData=dictTempDocData)
                        DocumentRawTxtExtractBy = "AWS"
                        # Extract text from Document
                        objDocMetaDataResponse = await objDocMetaData.MExtractTxtFromDoc(isGPTEnabled = bUsePaidModel, bPerformAWSExtraction=True)
                        DocumentRawTxt = {"RawTextInput": objDocMetaDataResponse.get("extracted_text")}
                        DocumentRawTxtObject = objDocMetaDataResponse.get("AWSResponse")

                        strGPTConfigFilePath = Path(r"resource/GPTConfigForModelExtraction.json")
                        try:
                            with open(strGPTConfigFilePath) as fileGptConfig:
                                dictGPTConfig = json.load(fileGptConfig)
                        except Exception as e:
                            await CLogController.MSWriteLog(user_id, "Error", f"Error while reading gpt configurations from file '{strGPTConfigFilePath}', Error:{e}")

                        objGPTAPIResponse =  CGPTAPIResponse(document_id=0, UserData=userData, docExtractedTxt=objDocMetaDataResponse, iDocPageCount=0)
                        dictResponse = await objGPTAPIResponse.getGPTResponse(strSystemContent=dictGPTConfig["SystemPrompt"], strUserContent=objDocMetaDataResponse["extracted_text"], strModel=dictGPTConfig["GPTModel"],  dictResponseFormat=dictGPTConfig["ResponseFormat"])
                        dictExtractedContent = json.loads(dictResponse["choices"][0]['message']['content'])
                        strModelName = dictExtractedContent["SellerName"]
                        if not strModelName or strModelName is None or strModelName == "null": 
                            raise HTTPException(
                                status_code=404,
                                detail="AccuVelocity AI Software was unable to detect the Vendor in your current integration. "
                                    "Please contact the developer team for assistance at +91 98989 42935 <NAME_EMAIL>."
                            )


                    except Exception as e:
                        raise ValueError("Failed to extract Model from document")

                # Docx, Excel and csv file as input
                if isOtherFormats:
                    objPdfConvertedData = CDocxExcelCsv2PDF.MSConvertFileToPdf(file_data, file_type)
                    if objPdfConvertedData:
                        dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=objPdfConvertedData)
                        iNumPages, ConvertedFileSize = dictConvertedFileData.get("pageCount"), dictConvertedFileData.get("fileSize")

                        file_name = f"{os.path.splitext(file_name)[0]}.pdf"
                        file_type = "PDF"
                        await objCAWSS3Storage.MSetS3Object(
                            objPdfConvertedData,
                            metadata={"x-amz-meta-filename": str(file_name), "x-amz-meta-user_id": str(user_id),
                                    "x-amz-meta-file_type": file_type, "x-amz-meta-is_scanned_doc": str(isOCRPDF),
                                    "x-amz-meta-iPageCount": str(iNumPages)}
                        )

                # Perform upload
                result = await CDocumentData.upload_pdf_to_db(
                    user_id=user_id,
                    strModelName=strModelName.lower(),
                    strFamilyName=strFamilyName,
                    DocS3ObjectKey = S3ObjName,
                    Comment = Comment,
                    file_data=file_data,
                    file_name=file_name,
                    file_type=file_type,
                    is_scanned_doc=int(isOCRPDF),
                    iPageCount=iNumPages,
                    bUsePaidModel=bUsePaidModel,
                    strDocStatus=strDocStatus,
                    DocExtractionAPIStatusCode=DocExtractionAPIStatusCode,
                    dictGPTResponseForModelExtraction=dictResponse, objAdditionalDocDetails=objAdditionalDocDetails,
                    DocumentRawTxt=DocumentRawTxt, # Customize Raw Text to Serve as Input for the OpenAI Model Response
                    DocumentRawTxtObject = DocumentRawTxtObject,
                    DocumentRawTxtExtractBy= DocumentRawTxtExtractBy,
                    BReUseDocData=BReUseDocData,
                    strClientREQID = document.strClientREQID, 
                    strVoucherType = self.strVoucherType
                )

                return result
        except HTTPException as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error uploading document. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= document.HashCode, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - Upload Document:{traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", IsRecordUIDPresent=False,strAccuVelocityComments="Error2: Database upload failed. Please manually enter the data in Tally.")
            return{"filename":file_name, "APIStatusCode":500, "DocErrorMsg":str(e)}

        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error while uploading document. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= document.HashCode, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - Upload Document: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", IsRecordUIDPresent=False,strAccuVelocityComments="Error2: Database upload failed. Please manually enter the data in Tally.")
            return{"filename":file_name, "APIStatusCode":500, "DocErrorMsg":str(e)}
    
    async def upload_documentv2(self, user_id, document, bUsePaidModel=True, bAutoSelectModel=False, strDocStatus = "NotProcess", DocExtractionAPIStatusCode=404, objAdditionalDocDetails =None, BReUseDocData=False,strFamilyName=None, model_name=None):
        file_name = ""
        DocumentRawTxtExtractBy = None
        DocumentRawTxt = None
        DocumentRawTxtObject = None
        # NOTE: GENERALIZE_PWI_V3 For selected Customers we are running Generalize Approach - which do not need to be had modelID and PromptID
        bIsGeneralizePWIV3 = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = user_id, strVoucherType = self.strVoucherType)
        try:
            start_extract_time = datetime.now()
            # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
            print(f"Start upload_documentv2 Document Process (DocID: {document.filename}) - {start_extract_time}")
            userData = await CAuthController.MSGetSingleUser(user_id=user_id)
            bIsTallyIntegrated =  userData["integration_config"]["isTallyConfigured"] if userData["integration_config"] else False
            # Read binary data of document
            MAX_FILE_SIZE = 20 * 1024 * 1024  # 20MB in bytes
            Comment = "" # Initial No Comment on Document
            file_name = document.filename
            strModelName = "Unknown" if bIsGeneralizePWIV3 else document.DocVendorName
            strFamilyName = "Custom"
            # if document.size > MAX_FILE_SIZE:
            #     raise HTTPException(
            #         status_code=400,
            #         detail=f"File size for '{file_name}' exceeds the maximum limit of 20MB. The uploaded file is {document.size / (1024 * 1024):.2f}MB."
            #     )
            file_data = await document.read()
            file_type = Constants.allowed_content_types.get(document.content_type)
            if not file_type:
                raise HTTPException(status_code=400, detail="Invalid file type : Please upload a valid file type")
            isOtherFormats = file_type.lower().endswith(('docx', 'xlsx', 'csv','txt'))
            iNumPages = 0
            if file_type == "PDF":
                dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=file_data)
                iNumPages, docSize = dictConvertedFileData.get("pageCount"), dictConvertedFileData.get("fileSize")
            elif re.match(r'^jpeg|png|webp|bmp|jpg$', file_type.lower()):
                iNumPages=1

            strCheckSum = Hashing.calculate_checksum(file_data)

            dictResult = await CDocumentData.MSIsDuplicateFile(strCheckSum,user_id, self.strVoucherType)
            #check for uploaded file is duplicated or not
            if dictResult['bIsDuplicate'] and BReUseDocData == False:
                dictDuplicateFound = dictResult['MatchedRow']
                dictDuplicateFound["filename"] = file_name
                # dictDuplicateFound["APIStatusCode"] = 409 # conflict raise
                # dictDuplicateFound["DocErrorMsg"] = "A duplicate entry was detected."
                return dictDuplicateFound
                # raise HTTPException(status_code=409, detail="Duplicate Record Found")

            if dictResult['bIsDuplicate'] and BReUseDocData == True:
                # Check additional Mandatory Document given or not
                dfICDPegasusAdditionalInfo = None
                if objAdditionalDocDetails is not None: #and dictResult['MatchedRow']['AdditionalDocDetails'] is None:
                    # NOTE: All to store additional information for this list of vendors
                    dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
                    # Flatten the list of vendors from all customers into a single list
                    lsVendorNames = [vendor for vendors in dictAvailableCompany["VendorName"] for vendor in vendors]
                    if dictResult['MatchedRow']["ModelName"].lower() in lsVendorNames:
                        # Process the Excel file and convert it to a dictionary
                        dfICDPegasusAdditionalInfo = ReadExcelToDict(objAdditionalDocDetails)

                        # Update the AdditionalDocDetails column in the UploadedDoc table
                        await CDocumentData.MSUpdateUploadedDocRecord(
                            iUserId=user_id,
                            iDocId=dictResult['MatchedRow']["DocId"],
                            AdditionalDocDetails=dfICDPegasusAdditionalInfo
                        )

                        # Update the local copy of the record to reflect the changes
                        dictResult['MatchedRow']['AdditionalDocDetails'] = dfICDPegasusAdditionalInfo

                        await CLogController.MSWriteLog(user_id, "Info", f"Updated AdditionalDocDetails for document ID: {dictResult['MatchedRow']['DocId']}")

                dictResult['MatchedRow']["filename"] = file_name
                if dictResult['MatchedRow']["DocRetryDetails"] is not None:
                    lsPrevious = dictResult['MatchedRow'].get("DocRetryDetails").get("ClientREQID", [])
                    lsPrevious.append(document.strClientREQID)
                    dictDocRetryDetails = {"ClientREQID": lsPrevious}
                else:
                    dictDocRetryDetails = {"ClientREQID": [document.strClientREQID]}

                iDocRetryCount = dictResult['MatchedRow'].get("DocRetryCount", 1) + 1

                await CDocumentData.MSUpdateUploadedDocRecord(
                    iUserId=user_id,
                    iDocId=dictResult['MatchedRow']["DocId"],
                    DocRetryCount=iDocRetryCount,
                    DocRetryDetails=dictDocRetryDetails
                )
                # AVRecordDetail Table : DocID, IsRecordUIDPresent Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= strCheckSum, DOC_UID=dictResult['MatchedRow']["DocId"], IsRecordUIDPresent=True)
                return dictResult['MatchedRow']
            
            s3start_extract_time = datetime.now()
            S3ObjName = CAWSS3Storage.MSGenerateUniqueObjecID()
            if bIsTallyIntegrated:
                objCAWSS3Storage = CAWSS3Storage(bucket_name = os.getenv("s3_bucket_name_for_tally_docs"), object_name=S3ObjName, user_id=user_id)
            else:
                objCAWSS3Storage = CAWSS3Storage(object_name=S3ObjName, user_id=user_id)


            isOCRPDF = await ExtractTextFromDoc.isScannedDoc(file_data=file_data, file_type =file_type) if not isOtherFormats else False

            response =  await objCAWSS3Storage.MSetS3Object(file_data, metadata={"x-amz-meta-filename": str(file_name),"x-amz-meta-user_id": str(user_id), "x-amz-meta-file_type":str(file_type),"x-amz-meta-is_scanned_doc":str(isOCRPDF), "x-amz-meta-iPageCount":str(iNumPages)})

            s3end_extract_time =  datetime.now()
            s3extract_time_taken = (s3end_extract_time - s3start_extract_time).total_seconds()
            print(f"End upload_documentv2 Document Process (DocName: {file_name}) -  Time taken: {s3extract_time_taken:.2f} seconds.")

            #! ****** For extracting model name from the document itself ****
            dictResponse = {}

            if not bIsGeneralizePWIV3 and bAutoSelectModel and strModelName.strip() =="":
                try:
                    dictTempDocData = {
                        "DocBinaryData":file_data,
                        "file_type":file_type,
                        "DocS3ObjectKey":S3ObjName
                    }
                    objDocMetaData = ExtractTextFromDoc(dictUserData=userData, dictDocumentData=dictTempDocData)
                    DocumentRawTxtExtractBy = "AWS"
                    # Extract text from Document
                    objDocMetaDataResponse = await objDocMetaData.MExtractTxtFromDoc(isGPTEnabled = bUsePaidModel, bPerformAWSExtraction=True)
                    DocumentRawTxt = {"RawTextInput": objDocMetaDataResponse.get("extracted_text")}
                    DocumentRawTxtObject = objDocMetaDataResponse.get("AWSResponse")

                    strGPTConfigFilePath = r"resource\GPTConfigForModelExtraction.json"
                    try:
                        with open(strGPTConfigFilePath) as fileGptConfig:
                            dictGPTConfig = json.load(fileGptConfig)
                    except Exception as e:
                        await CLogController.MSWriteLog(user_id, "Error", f"Error while reading gpt configurations from file '{strGPTConfigFilePath}', Error:{e}")

                    objGPTAPIResponse =  CGPTAPIResponse(document_id=0, UserData=userData, docExtractedTxt=objDocMetaDataResponse, iDocPageCount=0)
                    dictResponse = await objGPTAPIResponse.getGPTResponse(strSystemContent=dictGPTConfig["SystemPrompt"], strUserContent=objDocMetaDataResponse["extracted_text"], strModel=dictGPTConfig["GPTModel"],  dictResponseFormat=dictGPTConfig["ResponseFormat"])
                    dictExtractedContent = json.loads(dictResponse["choices"][0]['message']['content'])
                    strModelName = dictExtractedContent["SellerName"]
                    if not strModelName or strModelName is None or strModelName == "null": 
                        raise HTTPException(
                            status_code=404,
                            detail="AccuVelocity AI Software was unable to detect the Vendor in your current integration. "
                                "Please contact the developer team for assistance at +91 98989 42935 <NAME_EMAIL>."
                        )


                except Exception as e:
                    raise ValueError("Failed to extract Model from document")

            # Docx, Excel and csv file as input
            if isOtherFormats:
                objPdfConvertedData = CDocxExcelCsv2PDF.MSConvertFileToPdf(file_data, file_type)
                if objPdfConvertedData:
                    dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=objPdfConvertedData)
                    iNumPages, ConvertedFileSize = dictConvertedFileData.get("pageCount"), dictConvertedFileData.get("fileSize")

                    file_name = f"{os.path.splitext(file_name)[0]}.pdf"
                    file_type = "PDF"
                    await objCAWSS3Storage.MSetS3Object(
                        objPdfConvertedData,
                        metadata={"x-amz-meta-filename": str(file_name), "x-amz-meta-user_id": str(user_id),
                                "x-amz-meta-file_type": file_type, "x-amz-meta-is_scanned_doc": str(isOCRPDF),
                                "x-amz-meta-iPageCount": str(iNumPages)}
                    )

            # Perform upload
            result = await CDocumentData.upload_pdf_to_db(
                user_id=user_id,
                strModelName=strModelName.lower(),
                strFamilyName=strFamilyName,
                DocS3ObjectKey = S3ObjName,
                Comment = Comment,
                file_data=file_data,
                file_name=file_name,
                file_type=file_type,
                is_scanned_doc=int(isOCRPDF),
                iPageCount=iNumPages,
                bUsePaidModel=bUsePaidModel,
                strDocStatus=strDocStatus,
                DocExtractionAPIStatusCode=DocExtractionAPIStatusCode,
                dictGPTResponseForModelExtraction=dictResponse, objAdditionalDocDetails=objAdditionalDocDetails,
                DocumentRawTxt=DocumentRawTxt, # Customize Raw Text to Serve as Input for the OpenAI Model Response
                DocumentRawTxtObject = DocumentRawTxtObject,
                DocumentRawTxtExtractBy= DocumentRawTxtExtractBy,
                BReUseDocData=BReUseDocData,
                strClientREQID = document.strClientREQID, 
                strVoucherType = self.strVoucherType
            )
            end_extract_time =  datetime.now()
            extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
            print(f"End upload_documentv2 Document Process (DocName: {document.filename}) -  Time taken: {extract_time_taken:.2f} seconds.")
            return result
        except HTTPException as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error uploading document. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= document.HashCode, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - Upload Document:{traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", IsRecordUIDPresent=False,strAccuVelocityComments="Error2: Database upload failed. Please manually enter the data in Tally.")
            return{"filename":file_name, "APIStatusCode":500, "DocErrorMsg":str(e)}

        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error while uploading document. {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = document.strClientREQID, ReqDocHashCode= document.HashCode, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Skipped",TracebackLogs= f"Error - Upload Document: {traceback.format_exc()}", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", IsRecordUIDPresent=False,strAccuVelocityComments="Error2: Database upload failed. Please manually enter the data in Tally.")
            return{"filename":file_name, "APIStatusCode":500, "DocErrorMsg":str(e)}

    async def upload_documents(self, user_id, documents,strFamilyName, model_name, bUsePaidModel, bAutoSelectModel=False, strDocStatus = "NotProcess", objAdditionalDocDetails:AdditionalDocDetails=None,BReUseDocData=False):
        tasks = []
        for document in documents:
            task = asyncio.create_task(self.upload_document(user_id = user_id,document = document,strFamilyName=strFamilyName, model_name = model_name, bAutoSelectModel=bAutoSelectModel, strDocStatus = strDocStatus, bUsePaidModel =  bUsePaidModel, objAdditionalDocDetails = objAdditionalDocDetails,BReUseDocData = BReUseDocData ))
            tasks.append(task)
        results = await asyncio.gather(*tasks, return_exceptions=True)

        return results  # Returning results if no exceptions

    # async def upload_single_document(self, user_id: int, document: Dict, strModelName: str, websocket: WebSocket):
    #     try:
    #         # Decode the base64 file data
    #         file_data = base64.b64decode(document['data'])
    #         file_name = document['filename']
    #         content_type = document['content_type']

    #         file_content = BytesIO(file_data)
    #         # Create UploadFile object
    #         upload_file = StarletteUploadFile(filename=file_name, file=file_content)

    #         # Upload logic here
    #         response = await self.upload_document(user_id=user_id, document=upload_file, model_name=strModelName, content_type=content_type)
    #         await websocket.send_json({"status": "uploaded", "filename": document['filename'], "DocId": response.get("DocId")})
    #         return response
    #     except Exception as e:
    #         await websocket.send_json({"status": "error", "filename": document['filename'], "error": str(e)})
    #         raise e
    # async def upload_documents(self, user_id: int, documents: List[UploadFile], strModelName: str, websocket: WebSocket):
    #     tasks = []
    #     for document in documents:
    #         task = asyncio.create_task(self.upload_single_document(user_id, document, strModelName, websocket))
    #         tasks.append(task)
    #     results = await asyncio.gather(*tasks)
    #     await websocket.send_json({"status": "All Files Uploaded"})

    #     return results