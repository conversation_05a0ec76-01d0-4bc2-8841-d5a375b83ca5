
# Add repository for Python 3.11
sudo add-apt-repository ppa:deadsnakes/ppa

# Update package list
sudo apt update -y

# Install Python 3.11 and necessary packages
sudo apt install python3.11 software-properties-common -y

# Verify Python version
python3.11 --version

# Set Python 3.11 as the default Python version
sudo update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1

# Verify Python version again
python --version

# Install additional Python 3.11 development packages
sudo apt install python3.11-dev python3.11-venv python3.11-distutils python3.11-gdbm python3.11-tk python3.11-lib2to3 -y

# Install required packages for MySQL client
sudo apt install pkg-config libmysqlclient-dev build-essential python3-dev -y

# Install pip for Python 3.11
sudo apt install python3-pip -y

# Install MySQL client library
pip install mysqlclient

# Install MySQL server
sudo apt install mysql-server -y

# Install Node.js
sudo snap install node --classic

# Configure MySQL
sudo mysql -u root -p
CREATE USER 'riveredge'@'localhost' IDENTIFIED BY 'real123';
GRANT ALL PRIVILEGES ON *.* TO 'riveredge'@'localhost' WITH GRANT OPTION;
CREATE DATABASE DBVelocity;
Exit;

# Add OpenAI API key to environment variables
nano ~/.bashrc
export OPENAI_API_KEY="***************************************************"
source ~/.bashrc

# Install FastAPI, Uvicorn, and Gunicorn
pip install fastapi uvicorn[standard] gunicorn

# Install Supervisor
sudo apt install supervisor -y
# Install Nginx
# Install Certbot for Let's Encrypt SSL certificates
sudo apt install nginx -y
sudo apt install certbot python3-certbot-nginx -y


# Configure Supervisor for FastAPI application
sudo nano /etc/supervisor/conf.d/myfastapiapp.conf
/home/<USER>/Desktop/AccuVelocity/5_Env/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 8
# Add the following configuration to myfastapiapp.conf
[program:myfastapiapp]
directory=/root/Desktop/AccuVelocity
command=/root/Desktop/AccuVelocity/5_Env/bin/gunicorn -k uvicorn.workers.UvicornWorker -w 10 -b 0.0.0.0:8000 src.main:app
autostart=true
autorestart=true
stderr_logfile=/var/log/myfastapiapp/myfastapiapp.err.log
stdout_logfile=/var/log/myfastapiapp/myfastapiapp.out.log

# Create log directory and files
sudo mkdir /var/log/myfastapiapp
sudo touch /var/log/myfastapiapp/myfastapiapp.err.log
sudo touch /var/log/myfastapiapp/myfastapiapp.out.log

sudo nano /var/log/myfastapiapp/myfastapiapp.err.log
sudo nano /var/log/myfastapiapp/myfastapiapp.out.log
# Reload Supervisor to apply changes
sudo supervisorctl reload

# Check status of the Supervisor program
sudo supervisorctl status myfastapiapp

# Stop Supervisor program (if running)
sudo supervisorctl stop myfastapiapp

# Check status again (should be stopped)
sudo supervisorctl status myfastapiapp

# Start Supervisor program
sudo supervisorctl start myfastapiapp


# Configure Nginx server block for FastAPI application
sudo nano /etc/nginx/sites-available/backend.accuvelocity.com

# Add the following Nginx configuration
server {
    listen 80;
    server_name backend.accuvelocity.com;

    location / {
        proxy_pass http://localhost:8000;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }
}

# Enable the Nginx server block
sudo ln -s /etc/nginx/sites-available/backend.accuvelocity.com /etc/nginx/sites-enabled/

# Test Nginx configuration
sudo nginx -t

# Reload Nginx to apply changes
sudo systemctl reload nginx
