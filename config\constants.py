import pytz
from src.Schemas.Vendor_Schema import ModelFieldItem, ModelTable
from src.utilities.PathHandler import dictProjectPaths
from pathlib import Path
# All the app related constants should be mentioned here
class Constants:
    APP_VERSION = "2.0.20240808"
    Advertisement_path= dictProjectPaths.get("strAdvertisementPath", r"H:/AI Data/Advertisements")
    UserRegistrationSuccessMessage = "Welcome to AccuVelocity! Your registration is complete, and you’re all set to streamline your document extraction needs with us. Dive in and explore our powerful features!"  
    PAGER_SIZE=50
    EMAIL_REGEX = r"^(([^<>()[\]\\.,;:\s@\"]+(\.[^<>()[\]\\.,;:\s@\"]+)*)|(\".+\"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$"
    PASSWORD_REGEX = r"^.{8,}$"
    NUMBER_REGEX = r"^\d{10}$"
    # Get the timezone for India/Kolkata
    IndiaTimeZone = pytz.timezone('Asia/Kolkata')
    USEDTTimeZone = pytz.timezone('US/Eastern')
    DateTimeFormat = "%Y-%m-%d %H:%M:%S"
    allowed_content_types = {
                'application/pdf': 'PDF',
                'text/plain': 'TXT',
                'image/jpeg' : 'JPEG',
                'image/jpg' : 'JPG',  
                'image/png' : "PNG",
                'image/bmp':"BMP", 
                'image/webp':"WEBP",
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
                'text/csv': 'CSV'
            }
    GPTConfigPath = Path(r'resource/GPTConfig.json')
    GeminiConfigPath = Path(r"resource/GeminiConfig.json")
    strModelCategoryFormatPath = Path(r"resource/Category_N_Formats.json")
    DevPaymentConfig = Path(r"resource/DevelopmentPaymentConfig.json")
    ProdPaymentConfig = Path(r"resource/ProductionPaymentConfig.json")
    EMAILTEMPLATEPATH = Path(r"resource/EmailTemplateConfig.json")
    ACCULOGOPATH = Path(r"resource/AccuvelocityLogo.png")
    TallyTemplatePath = Path(r"resource/TallyTemplates.json")
    srtTallUserConfig = Path(r'resource/TDLUserConfig.json')
    strVendorWiseCustomerDetails = Path(r"resource/VendorWiseCustomerDetails.xlsx")
    GPTAPISuffix = "General"
    GeminiAPISuffix = "General"
    GPTAPIVersion = "gpt-4o-2024-08-06"
    # Configure gpt batch api completion value
    GPTBatchAPICompletionWindow = "24h"
    # Max Document Allow Size for Trial Period
    MaxTrialPaidDocExtractionPerUser = 12
    # Max Model Field Description Char Length
    MaxModelFieldCharLength = 50
    GPTAPIModelName = "GPT"
    GeminiAPIModelName = "Gemini" 
    lsDefaultModelName = ["Invoice","Resume","Job Description","Receipt", "Account Statement", "Insurance EOR"]
    lsDefaultInvoiceFields = "Remove this variable"
    
    dictInvoiceModelData = {
        "Fields":[
        ModelFieldItem(FieldName="Vendor Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Invoice Number", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Invoice Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Due Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Invoice Amount With Tax", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Tax", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Ship To Or Service To Location", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="LineItemTable",
                Fields=[
                ModelFieldItem(FieldName="Serial Number", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Description", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Quantity", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Rate", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Amount", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }
    dictResumeModelData = {
        "Fields":[
            ModelFieldItem(FieldName="Full Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Gender", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Birth Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Email Address", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Address", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Contact Number", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Linked In Profile URL", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Objective Statement", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Skills", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Certifications", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Achievements", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Languages Known", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Technologies Used", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="Education Details",
                Fields=[
                ModelFieldItem(FieldName="Degree", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Institution Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Graduation Year", FieldCategory="Date", FieldFormat="As per your Document", FieldNotes="",FieldDescription="Ending Month If available And Year"),
                ModelFieldItem(FieldName="GPA/Marks/%", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
                ]
            ),
            ModelTable(
                TableName="Work Experience",
                Fields=[
                ModelFieldItem(FieldName="Company Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Role", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Start Year", FieldCategory="Date", FieldFormat="As per your Document", FieldNotes="",FieldDescription="YYYY"),
                ModelFieldItem(FieldName="End Year", FieldCategory="Date", FieldFormat="As per your Document", FieldNotes="",FieldDescription="YYYY"),
                ModelFieldItem(FieldName="Description / Responsibility", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
                ]
            ),
            ModelTable(
                TableName="Projects",
                Fields=[
                ModelFieldItem(FieldName="Project Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Description", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Technologies Used", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Role", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }

    dictJobDescriptionModelData = {
        "Fields":[
        ModelFieldItem(FieldName="CompanyName", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Location", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="JobTitle", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="JobType", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="RequiredExperience", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="FresherAllowed", FieldCategory="Boolean", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Salary", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Description", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Responsibilities", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Qualifications", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="Benefits", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="requirements", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Language Proficiency", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Industry", FieldCategory="Boolean", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="Skills", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
        # ModelFieldItem(FieldName="HowToApply", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        # ModelFieldItem(FieldName="ApplicationDeadline", FieldCategory="Boolean", FieldFormat="", FieldNotes="",FieldDescription="")
    ],
        "Tables":[ 
            ModelTable(
                TableName="Skills",
                Fields=[
                ModelFieldItem(FieldName="Skill Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="")
                ]
            )
            ]
    }

    dictReceiptModelData = {
        "Fields":[
            ModelFieldItem(FieldName="CompanyName", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="CompanyAddress", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="TypeOfRecipt", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="Use Intelligence To Identify Type Of Recipt Like Petrol Or Restaurent Or SomethingElse"),
            ModelFieldItem(FieldName="TransactionDate", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Time", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="ReceiptNumber", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="PaymentMethod", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Subtotal", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Tax", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="Discounts", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="FinalAmount", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="ItemsPurchased",
                Fields=[
                ModelFieldItem(FieldName="ItemName", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="ItemQuantity", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="ItemPrice", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="TotalPrice", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }

    dictAccountStatementModelData = {
        "Fields":[
            ModelFieldItem(FieldName="BankName", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="BranchName", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="AccountHolder", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="AccountNumber", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="StatementPeriod", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription="It should be in form like from to to period for example MM/DD/YYYY - MM/DD/YYYY."),
            ModelFieldItem(FieldName="Currency unit", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="Define it from data given to you"),
            ModelFieldItem(FieldName="OpeningBalance", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="Always first find opening balance in Transactions if available."),
            ModelFieldItem(FieldName="ClosingBalance", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
            ModelFieldItem(FieldName="TotalCredits", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
            # ModelFieldItem(FieldName="TotalDebits", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="Transactions",
                Fields=[
                ModelFieldItem(FieldName="Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription="If multiple transactions occur on the same day and the date is mentioned only once, use that date for all corresponding transactions."),
                # ModelFieldItem(FieldName="TransactionNumber", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Description", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Amount", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
                # ModelFieldItem(FieldName="Balance", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }

    dictInsuranceEORModelData = {
        "Fields":[
        ModelFieldItem(FieldName="NPI Number", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Provider Tax ID", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Date of Injury", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Allowance", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total BR/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total PPO/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Charge", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Other/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Rendering Provider Name", FieldCategory="Numbers & Text", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Invoice Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="LineItemTable",
                Fields=[
                ModelFieldItem(FieldName="DOS", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Code/Proc", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Units", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Charge", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="BR/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="PPO/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Other/Red", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Allowance", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")  
                ]
            )
        ]  
    }

    dictMedicalInvoiceModelData = {
        "Fields":[
        ModelFieldItem(FieldName="Patient City", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Patient State", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Patient Zip Code", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Charge", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="Fetch from Total Charge Field"),
        ModelFieldItem(FieldName="Amount Paid", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="Fetch from Amount Paid"),
        ModelFieldItem(FieldName="Service Facility Location Information", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription="Fetch from Service Facility Location Information"),
        ModelFieldItem(FieldName="Billing Provider Info & Ph #", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription="Fetch from Billing Provider Info & Ph #"),
        # ModelFieldItem(FieldName="Insured City", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="If found empty,at least return the field as empty"),
        # ModelFieldItem(FieldName="Insured State", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription="If found empty,at least return the field as empty"),
        ModelFieldItem(FieldName="Insured Zip Code", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription="If found empty,at least return the field as empty")



    ],
        "Tables":[
            ModelTable(
                TableName="LineItemTable",
                Fields=[
                ModelFieldItem(FieldName="Dates of Service From", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription="This instruction is for this child table fetch each row and every rows date"),
                ModelFieldItem(FieldName="Dates of Service To", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription="This instruction is for this child table fetch each row and every rows date"),
                ModelFieldItem(FieldName="Procedures,Services or Supplies", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Diagonsis Partner", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Charges", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Days or Units", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Rendering Provider Id.#", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }

    dictMedicalRecordModelData ={
        "Fields":[
        ModelFieldItem(FieldName="Patient City", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Patient State", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Patient Zip Code", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Occupation", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Date of Injury", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Claim Administrator Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Claim Administrator  Address", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Employer Name", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Claims Administrator City", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Claims Administrator Zip Code", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Claims Administrator State", FieldCategory="Only Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Injury Details(Diagonsis)", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription="Take both Number and Text")

    ],
        "Tables":[ ]

}
        

    dictReconsiderationModelData = {
        "Fields":[
        ModelFieldItem(FieldName="Review Date", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Provider Tax ID", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Jurisdiction", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Date of Injury", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Payee", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="ICD Codes", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Rendering Provider", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription="Fetch From the Provided Data"),
        ModelFieldItem(FieldName="Employer ID", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Rendering Provider NPI", FieldCategory="Only Numbers", FieldFormat="", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Billed", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Fee", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total PPO", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
        ModelFieldItem(FieldName="Total Allowed", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
    ],
        "Tables":[
            ModelTable(
                TableName="LineItemTable",
                Fields=[
                ModelFieldItem(FieldName="Date of Service", FieldCategory="Date", FieldFormat="MM-DD-YYYY", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Code", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Mod", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Qty", FieldCategory="Numbers & Text", FieldFormat="", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Billed", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Fee", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="PPO", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription=""),
                ModelFieldItem(FieldName="Allowed", FieldCategory="Currency", FieldFormat="$ USD", FieldNotes="",FieldDescription="")
                ]
            )
        ]
    }


    dictInitModelData = {
        "Invoice":dictInvoiceModelData,
        "Resume":dictResumeModelData,
        "Job Description":dictJobDescriptionModelData,
        "Receipt":dictReceiptModelData,
        "Account Statement":dictAccountStatementModelData,
        "Insurance EOR":dictInsuranceEORModelData,
        "Medical Invoice":dictMedicalInvoiceModelData,
        "Medical Record":dictMedicalRecordModelData,
        "Reconsideration":dictReconsiderationModelData
    }
    lsDefaultResumeFields = "Remove usage"
    # Parameter for Document OCR
    strProjectId = "************"
    strLocation = "us"
    strProcessorId = "bfac9817b7c2079d"
    strProcessorVersion = "stable"
    strMimeType = {
        "pdf" : "application/pdf",
        "jpg" : "image/jpeg",
        "jpeg": "image/jpeg",
        "png" : "image/png",
        "bmp" : "image/bmp",
        "webp": "image/webp"
    }
    allowed_content_types_for_bug = {
                'application/pdf': 'PDF',
                'text/plain': 'TXT',
                'image/jpeg' : 'JPEG',
                'image/jpg' : 'JPG',  
                'image/png' : "PNG",
                'image/bmp':"BMP", 
                'image/webp':"WEBP",
                'application/vnd.openxmlformats-officedocument.wordprocessingml.document': 'DOCX',
                'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet': 'XLSX',
                'text/csv': 'CSV',
                'application/json': 'JSON',
                'image/svg+xml': 'SVG',
                'image/tiff': 'TIFF',
                'video/mp4': 'MP4',
                'video/x-msvideo': 'AVI',
                'video/mpeg': 'MPEG',
                'video/quicktime': 'MOV',
                'video/webm': 'WEBM'
            }
    # S3 Bucket Configuration
    strS3BucketRegionName = "ap-south-1"
    # Promo Code Constants
    DefaultMaxPrompoCodeUseCount = 20   # Default Max Usage of Any Promo Code
    MaxPromoCodeLength = 20 # Max Length of Promo Code
    lsPromoCodeType = ["PageLimitLeft"]
    lsPromoCodeAction = ["increase","decrease"]
    dictPromoCodeMsg = {lsPromoCodeType[0]: "Referral code applied! Enjoy {promo_value} extra page extractions in your free account."}
    GeneralRateLimitForDocExtractionService = 15
    DefaultFamilyName = "Demo"
    lsContectUsList = ['<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>','<EMAIL>']
    lsDailyStatisticsList = ['<EMAIL>','<EMAIL>']
    # Web Socket TimeOut
    webSocketTimeout = 60.0
    iTrialDays = 10
    MAXContactUSFileSize = 50 * 1024 * 1024  # 50MB in bytes
# Format the time with minutes and seconds but without milliseconds
# formatted_time = current_time.strftime("%Y-%m-%d %H:%M:%S")
