import sys
sys.path.append(".")
from fastapi import APIRouter, Depends
from src.Controllers.StripePaymentController import <PERSON><PERSON><PERSON>App
from src.Schemas.auth_models import CheckoutSessionRequest
from src.Controllers.Logs_Controller import CLogController
from src.middleware.checkAuth import PlanRequest,user_required
import os
from fastapi import HTTP<PERSON>xception
import traceback
import stripe
from os.path import join, dirname
from dotenv import load_dotenv
from src.Controllers.user_Logs_Controller import CUserLogController

# Declaring router
PaymentAppRouter = APIRouter(tags=['PaymentApp'], prefix="/api")

# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

YOUR_DOMAIN = os.getenv('SERVER_DOMAIN')
# determine production or development stripe server secret key
livemode = bool(os.getenv('LIVEMODE') == "true")
print("livemode",livemode)
if bool(livemode):
    stripe.api_key = os.getenv('PAYMENT_INTEGRATION_PROD_SECRET_KEY') 
else:
    stripe.api_key = os.getenv('PAYMENT_INTEGRATION_DEV_SECRET_KEY')

async def update_subscription_end_date(subscription_id: str):
    """
    Purpose: Update the subscription end date to the current date
    Input: subscription_id (str)
    Output: dict containing the updated subscription
    Example: await update_subscription_end_date(subscription_id)
    """
    try:
        from datetime import datetime, timezone
        current_date = datetime.now(timezone.utc)

        subscription = stripe.Subscription.delete(subscription_id)

        await CLogController.MSWriteLog(None, "INFO", f"Subscription end date updated for {subscription_id}")
        print("subscription updated",subscription)
        return subscription
    except stripe.error.StripeError as e:
        await CLogController.MSWriteLog(None, "Error", f"Stripe API error: {e.user_message}")
        return None
    except Exception as e:
        await CLogController.MSWriteLog(None, "Error", f"Error updating subscription end date: {str(e)}")
        await CLogController.MSWriteLog(None, "Debug", f"Traceback: {traceback.format_exc()}")
        return None
    
@PaymentAppRouter.post("/create-checkout-session")
async def create_checkout_session(request: CheckoutSessionRequest = Depends(PlanRequest)):
    try:
        from src.Controllers.auth_controller import CAuthController
        UserData = await CAuthController.MSGetSingleUser(user_id=request.userId)
        # Fetch customer by email
        existing_customers = stripe.Customer.list(email=UserData.get("email")).data
        if existing_customers:
            customer_id = existing_customers[0].id
        else:
            # Create a new customer if not found
            customer = stripe.Customer.create(
                email=UserData.get("email"),
                phone=UserData.get("phoneNumber")
            )
            customer_id = customer.id
        isTopUp = (request.mode).lower() == 'payment'
        # Prepare the invoice_creation parameter based on the mode
        invoice_creation = {}
        if isTopUp:
            invoice_creation = {
                'enabled': True,  # Enable invoice creation for this session
            }
        success_url = f"{YOUR_DOMAIN}/PlanPurchaseSuccessfull/?{'top_up=true' if isTopUp else 'subscription=true'}"
        cancel_url = f"{YOUR_DOMAIN}/PlanPurchaseFailed/?{'top_up=true' if isTopUp else 'subscription=true'}"
        checkout_session = stripe.checkout.Session.create(
            payment_method_types=['card'],
            line_items=[
                {
                    'price': request.price_id,
                    'quantity': request.quantity,
                },
            ],
            mode=request.mode,
            success_url=success_url,
            cancel_url=cancel_url,
            automatic_tax={'enabled': False},
            customer=customer_id,
            consent_collection={
                'terms_of_service': 'required',
            },
            custom_text={
                'terms_of_service_acceptance': {
                    'message': 'I agree to the [Terms of Service](https://app.accuvelocity.com/assets/TermsofService.html)',
                },
            },
            invoice_creation=invoice_creation if invoice_creation else None
        )
        activityLogMsg = f"Top-up payment initiated for {UserData.get('email')}" if isTopUp else f"Subscription payment initiated for {UserData.get('email')}"
 
        await CUserLogController.MSWriteLog(request.userId, "Info",activityLogMsg, "Billing")
    except Exception as e:
        raise HTTPException(status_code=400, detail=str(e))
    print("checkout_session.url", checkout_session)
    return {"url": checkout_session.url}



@PaymentAppRouter.get("/manage-billing-portal")
async def create_checkout_session(userId:int=Depends(user_required)):
    try:
        stripeUserTable =  await CPaymentApp.MSGetStripeUser(userId)
        customerId = stripeUserTable.stripe_customer_id
        billing_portal = stripe.billing_portal.Session.create(customer=customerId,return_url=YOUR_DOMAIN + "/profile" # Replace with your actual redirect URL
            )


        await CUserLogController.MSWriteLog(userId, "Info",f"Your billing portal session has been successfully initiated. You can access it using your customer ID: {customerId}.", "Billing")
    except stripe.error.InvalidRequestError:
        raise HTTPException(status_code=404, detail="Oops! It looks like we can't find your premium account. <NAME_EMAIL> for assistance.")
    except Exception as e:
        # WARN - Need to determine when this case occur means is this occur in 404 not found too or not , need to check first
        # await CUserLogController.MSWriteLog(userId, "Info",f"Sorry, we can't open the billing portal now. Please try again later or contact <NAME_EMAIL>.", "Billing")
        raise HTTPException(status_code=500, detail="Sorry, we can't open the billing portal now. Please try again later or contact <NAME_EMAIL>.")
    print("checkout_session.url",billing_portal)
    return {"url": billing_portal.url}

@PaymentAppRouter.post("/cancel-subscription")
async def cancelSubscription(userId:int=Depends(user_required)):
    try:
        subscriptionTable =  await CPaymentApp.MSGetUserSubscription(userId)
        if subscriptionTable is not None:
            await update_subscription_end_date(subscriptionTable.subscription_id)
        
    except Exception as e:
        raise HTTPException(status_code=404, detail=str(e))
    return {"status": True}

if __name__ == "__main__":
    print("hello")