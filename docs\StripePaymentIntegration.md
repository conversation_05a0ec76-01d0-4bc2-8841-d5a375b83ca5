step 1 : add product id and plan id in resource\ProductionPaymentConfig.json or resource\DevelopmentPaymentConfig.json respectively files
step 2: check LIVEMODE flag in environment .env file 
step 3: check SECRET_KEY flag in environment .env file for STRIPE payment i.e. PAYMENT_INTEGRATION_SECRET_KEY, PAYMENT_INTEGRATION_PUBLISHABLE_KEY,PAYMENT_INTEGRATION_WEBHOOK_SECRET_KEY
step 4: execute this initializeAppPlans.sh or initializeAppPlans.bat file as per your os requirements to initialize Plans in DB
# Production Signing secret KEY for https://dev.accuvelocity.com/api/stripe_webhook url - whsec_HOgO05o6B6TaETOEJQu3afdBOmiBPsuU and 
"""
listen for this events - 	
customer.created,customer.deleted,customer.subscription.created,customer.subscription.deleted,customer.subscription.paused,customer.subscription.pending_update_applied,customer.subscription.pending_update_expired,customer.subscription.resumed,customer.subscription.trial_will_end,customer.subscription.updated,invoice.payment_succeeded,subscription_schedule.aborted,subscription_schedule.canceled,subscription_schedule.completed,subscription_schedule.created,subscription_schedule.expiring,subscription_schedule.released,subscription_schedule.updated
charge.failed
"""