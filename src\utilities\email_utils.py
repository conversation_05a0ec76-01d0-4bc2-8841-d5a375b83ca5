import sys
sys.path.append(".")

from typing import Dict, Any, List
from aiosmtplib import send
from email.mime.multipart import MI<PERSON>Multipart
from email.mime.text import MIMEText
from pydantic import EmailStr
from fastapi_mail import ConnectionConfig
import os
import requests
from dotenv import load_dotenv
from src.Controllers.Logs_Controller import CLogController
from email.mime.image import MIMEImage
import json
from config.constants import Constants
import traceback

class CResetPassword:
    mail_conf = ConnectionConfig(
        MAIL_USERNAME=os.getenv('MAIL_USERNAME'),
        MAIL_PASSWORD=os.getenv('MAIL_PASSWORD'),
        MAIL_FROM=os.getenv('MAIL_FROM'),
        MAIL_PORT=int(os.getenv('MAIL_PORT')),
        MAIL_SERVER=os.getenv('MAIL_SERVER'),
        MAIL_STARTTLS=False,
        MAIL_SSL_TLS=True
    )

    strDailyUsageStatistics = """
                        <!DOCTYPE html>
                                <html>
                                <head>
                                    <title>AccuVelocity Statistic</title>
                                    <style>
                                        body {{
                                            font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                                            margin: 0;
                                            padding: 0;
                                            background-color: #F0F2F5;
                                            color: #333;
                                        }}
                                        .container {{
                                            max-width: 700px;
                                            margin: 40px auto;
                                            padding: 30px;
                                            background-color: #FFFFFF;
                                            border-radius: 12px;
                                            box-shadow: 0 6px 12px rgba(0,0,0,0.1);
                                        }}
                                        .header {{
                                            background-color: #0056b3;
                                            color: #ffffff;
                                            padding: 15px;
                                            text-align: center;
                                            border-radius: 12px 12px 0 0;
                                            margin-bottom: 20px;
                                        }}
                                        .header h1 {{
                                            margin: 0;
                                            font-size: 24px;
                                        }}
                                        .header h2{{
                                            text-align: center;
                                        }}
                                        .content {{
                                            padding: 20px;
                                            line-height: 1.6;
                                        }}
                                        .content h2 {{
                                            font-size: 20px;
                                            border-bottom: 2px solid #0056b3;
                                            padding-bottom: 10px;
                                            margin-bottom: 20px;
                                            color: #333;
                                        }}
                                        table {{
                                            width: 100%;
                                            border-collapse: collapse;
                                            margin-bottom: 20px;
                                        }}
                                        table td, table th {{
                                            padding: 12px;
                                            text-align: center;
                                        }}
                                        table thead th {{
                                            background-color: #0056b3;
                                            color: #ffffff;
                                            font-weight: bold;
                                            border: 1px solid #dddfe1;
                                        }}
                                        table tbody td {{
                                            background-color: #ffffff;
                                            color: #3e3e3e;
                                            border: 1px solid #dddfe1;
                                        }}
                                        table tbody tr:nth-child(even) {{
                                            background-color: #f9f9f9;
                                        }}
                                        .footer {{
                                            font-size: 14px;
                                            color: #555555;
                                            text-align: center;
                                            padding: 20px;
                                            border-top: 1px solid #ddd;
                                            margin-top: 20px;
                                        }}
                                        a.button {{
                                            background-color: #0056b3;
                                            color: #ffffff;
                                            padding: 10px 20px;
                                            text-decoration: none;
                                            border-radius: 5px;
                                            font-weight: bold;
                                            display: inline-block;
                                            margin-top: 20px;
                                        }}
                                    </style>
                                </head>
                                <body>
                                    <div class="container">
                                        <div class="header">
                                            <h1>{CompanyName} Statistic</h1>
                                        </div>
                                        <div class="content">
                                            <h2>User Statistic</h2>
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>Daily User Logins</th>
                                                        <th>New Users</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>{dailyUsers}</td>
                                                        <td>{newUsers}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                            <h2>Page Statistic</h2>
                                            <table>
                                                <thead>
                                                    <tr>
                                                        <th>Lite Pages Processed</th>
                                                        <th>Pro Pages Processed</th>
                                                    </tr>
                                                </thead>
                                                <tbody>
                                                    <tr>
                                                        <td>{litePagesProcessed}</td>
                                                        <td>{proPagesProcessed}</td>
                                                    </tr>
                                                </tbody>
                                            </table>
                                        </div>
                                        <div class="footer">
                                            {CompanyName}<br>
                                            For any questions, please contact our support team.
                                        </div>
                                    </div>
                                </body>
                                </html>
                        """
    strResetPasswordContent = """
                    <!DOCTYPE html>
                    <html>
                    <head>
                        <title>Password Reset Request</title>
                        <style>
                            body {{
                                font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                                margin: 0;
                                padding: 0;
                                background-color: #F0F2F5;
                                color: #333;
                            }}
                            .container {{
                                max-width: 600px;
                                margin: 40px auto;
                                padding: 20px;
                                background-color: #FFFFFF;
                                border-radius: 8px;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                            }}
                            .header {{
                                background-color: #0056b3;
                                color: #ffffff;
                                padding: 10px 20px;
                                text-align: center;
                                border-radius: 8px 8px 0 0;
                            }}
                            .content {{
                                padding: 20px;
                                text-align: left;
                                line-height: 1.6;
                            }}
                            .footer {{
                                font-size: 14px;
                                color: #555555;
                                text-align: center;
                                padding: 20px;
                                border-top: 1px solid #ddd;
                            }}
                            a.button {{
                                background-color: #0056b3;
                                color: #ffffff;
                                padding: 10px 20px;
                                text-decoration: none;
                                border-radius: 5px;
                                font-weight: bold;
                                display: inline-block;
                                margin-top: 20px;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>Password Reset Request</h1>
                            </div>
                            <div class="content">
                                <p>Hi,</p>
                                <p>You recently requested to reset your password for your account. To complete the process, please click the button below. This password reset link will expire in 10 minutes.</p>
                                <a href="{strForgetUrlLink}" target="_blank" class="button">Reset Password</a>
                                <p>If you did not request a password reset, no further action is required on your part. Your account remains secure.</p>
                                <p>Thank you for your attention to this matter.</p>
                            </div>
                            <div class="footer">
                                {CompanyName}<br>
                                For any questions, please contact our support team.
                            </div>
                        </div>
                    </body>
                    </html>
                    """

    strOtpMessageContent = """
                <!DOCTYPE html>
                <html>
                    <head>
                        <title>OTP Verification</title>
                        <style>
                            body {{
                                font-family: 'Helvetica Neue', Helvetica, Arial, sans-serif;
                                margin: 0;
                                padding: 0;
                                background-color: #F0F2F5;
                                color: #333;
                            }}
                            .container {{
                                max-width: 600px;
                                margin: 40px auto;
                                padding: 20px;
                                background-color: #FFFFFF;
                                border-radius: 8px;
                                box-shadow: 0 4px 8px rgba(0,0,0,0.1);
                            }}
                            .header {{
                                background-color: #0056b3;
                                color: #ffffff;
                                padding: 10px 20px;
                                text-align: center;
                                border-radius: 8px 8px 0 0;
                            }}
                            .content {{
                                padding: 20px;
                                text-align: left;
                                line-height: 1.6;
                            }}
                            .footer {{
                                font-size: 14px;
                                color: #555555;
                                text-align: center;
                                padding: 20px;
                                border-top: 1px solid #ddd;
                            }}
                        </style>
                    </head>
                    <body>
                        <div class="container">
                            <div class="header">
                                <h1>OTP Verification</h1>
                            </div>
                            <div class="content">
                                <p>Hi,</p>
                                <p>We have sent you this OTP for verification purposes. Please enter the following OTP in the application:</p>
                                <p style="font-size: 24px; font-weight: bold; text-align: center;">{strOTP}</p>
                                <p>If you did not request this OTP, please ignore this message or contact our support team.</p>
                                <p>Thank you for your attention to this matter.</p>
                            </div>
                            <div class="footer">
                                AccuVelocity<br>
                                For any questions, please contact our support team.
                            </div>
                        </div>
                    </body>
                </html>"""



class CEmailVerifier:
    def __init__(self, email, user_id=None):
        """
        Initializes the CEmailVerifier class.
        
        :param email: Email address to be verified.
        :param user_id: User ID for logging purposes.
        """
        load_dotenv()
        self.api_key = os.getenv('EMAIL_VERIFICATION_API_KEY')
        self.email = email
        self.user_id = user_id
        self.url = f"https://api.bouncify.io/v1/verify?apikey={self.api_key}&email={self.email}"
        self.headers = {
            "Accept": "*/*",
            "Content-Type": "application/json"
        }
        # Call the email verification API and store the response data and status code
        self.response_data, self.status_code = self._verify_email()

    def _verify_email(self):
        """
        Calls the email verification API and handles multiple response codes.

        :return: Tuple containing the response data and status code.
        """
        response = requests.get(self.url, headers=self.headers)
        if response.status_code == 200:
            return response.json(), response.status_code
        elif response.status_code == 401:
            return {"success": "false", "result": "Invalid API Key"}, response.status_code
        elif response.status_code == 402:
            return {"success": "false", "result": "Insufficient verification credits"}, response.status_code
        elif response.status_code == 429:
            return {"success": "false", "result": "Too many requests"}, response.status_code
        else:
            return {"success": "false", "result": "Unknown error"}, response.status_code

    async def isValidEmail(self):
        """
        Validates the email based on the API response.
        
        :return: True if the email is valid, otherwise False.
        """
        await CLogController.MSWriteLog(self.user_id, "INFO", f"Email Verification API Response: {self.response_data}")
        if self.status_code == 200:
            if (self.response_data.get('disposable', 0) == 1) or (self.response_data.get('spamtrap', 0) == 1) or (self.response_data.get('message', "") != "This address can receive emails."  or self.response_data.get('result', "") != "deliverable"):
                await CLogController.MSWriteLog(self.user_id, "Debug", "Email is either disposable or a spamtrap.")
                return False
            await CLogController.MSWriteLog(self.user_id, "Debug", "Email is valid.")
            return True
        else:
            if self.status_code == 401:
                await CLogController.MSWriteLog(self.user_id, "Debug", "Invalid API Key. Please check your API key.")
            elif self.status_code == 402:
                await CLogController.MSWriteLog(self.user_id, "Debug", "Insufficient verification credits. Please top up your credits.")
            elif self.status_code == 429:
                await CLogController.MSWriteLog(self.user_id, "Debug", "Too many requests. Please try again later.")
            else:
                await CLogController.MSWriteLog(self.user_id, "Debug", "An unknown error occurred.")
            return True

    def MGetResponse(self):
        """
        Returns the API response data.
        
        :return: API response data.
        """
        return self.response_data
    
    @staticmethod
    def MSEmailVerifiedAPI(strEmail):
        """
        Verifies an email address using the Bouncify API.

        :param strEmail: The email address to be verified.
        :return: Tuple containing the API response data and a boolean indicating if the email is valid.
        
        Example:
        response_data, isValidEmail = MSEmailVerifiedAPI("<EMAIL>")

        Purpose:
        This method verifies if the provided email address is valid, non-disposable, and not a spamtrap 
        by calling the Bouncify email verification API. It handles multiple response codes and provides
        user-friendly messages.
        """
        isValidEmail = False
        load_dotenv()
        api_key = os.getenv('EMAIL_VERIFICATION_API_KEY')
        url = f"https://api.bouncify.io/v1/verify?apikey={api_key}&email={strEmail}"
        headers = {
            "Accept": "*/*",
            "Content-Type": "application/json"
        }
        objResponse = requests.get(url, headers=headers)
        response_data, status_code = objResponse.json(), objResponse.status_code
        if status_code == 200:
            if (response_data.get('disposable', 0) == 1) or (response_data.get('spamtrap', 0) == 1) or (response_data.get('message', "") != "This address can receive emails."  or response_data.get('result', "") != "deliverable"):
                print("Email is either disposable or a spamtrap.")
                isValidEmail = False
            else:
                print("Email is valid.")
                isValidEmail = True
        else:
            if status_code == 401:
                print("Invalid API Key. Please check your API key.")
            elif status_code == 402:
                print("Insufficient verification credits. Please top up your credits.")
            elif status_code == 429:
                print("Too many requests. Please try again later.")
            else:
                print("An unknown error occurred.")
            # return True if any External Service Raise exception
            isValidEmail = True  
        return response_data, isValidEmail



class CEmailer:
    mail_conf: ConnectionConfig

    def __init__(self, iUserID = None):
        self.iUserID = iUserID
        self.mail_conf = ConnectionConfig(
            MAIL_USERNAME=os.getenv('MAIL_USERNAME'),
            MAIL_PASSWORD=os.getenv('MAIL_PASSWORD'),
            MAIL_FROM=os.getenv('MAIL_FROM'),
            MAIL_PORT=int(os.getenv('MAIL_PORT')),
            MAIL_SERVER=os.getenv('MAIL_SERVER'),
            MAIL_STARTTLS=False,
            MAIL_SSL_TLS=True
        )

    
    async def send_email(
        self, 
        to: List[EmailStr], 
        template_name: str, 
        template_data: Dict[str, Any]
    ) -> None:
        """
        Send an email with the specified template and data to multiple recipients.
        """
        try:
            await CLogController.MSWriteLog(self.iUserID, "INFO", "Sending Email Notification.")

            # Load the JSON file
            with open(Constants.EMAILTEMPLATEPATH, 'r') as json_file:
                email_templates = json.load(json_file)

            # Access the template information
            template_info = email_templates.get(template_name)
            if not template_info:
                raise ValueError(f"Template {template_name} not found in JSON file.")
            
            subject = template_info['Email_Subject']
            email_body = template_info['Email_Body']

            # Replace placeholders in the email body with actual values
            for key, value in template_data.items():
                placeholder = f"{{{{ {key} }}}}"
                email_body = email_body.replace(placeholder, str(value))

            # Create the email content
            email_message = MIMEMultipart()
            email_message['From'] = self.mail_conf.MAIL_FROM
            email_message['To'] = ', '.join(to)  # Add all recipients here
            email_message['Subject'] = subject

            # Attach the HTML content
            email_message.attach(MIMEText(email_body, 'html'))

            strLogoPath = Constants.ACCULOGOPATH
            # Attach the logo image as inline attachment
            with open(strLogoPath, 'rb') as logo_file:
                logo_data = logo_file.read()
                logo_mime = MIMEImage(logo_data)
                logo_mime.add_header('Content-ID', '<logo_image>')
                email_message.attach(logo_mime)

            await send(
                email_message,
                hostname=self.mail_conf.MAIL_SERVER,
                port=self.mail_conf.MAIL_PORT,
                username=self.mail_conf.MAIL_USERNAME,
                password=self.mail_conf.MAIL_PASSWORD,
                start_tls=self.mail_conf.MAIL_STARTTLS,
                use_tls=self.mail_conf.MAIL_SSL_TLS,
                validate_certs=self.mail_conf.VALIDATE_CERTS,
                timeout=self.mail_conf.TIMEOUT,
            )
            await CLogController.MSWriteLog(self.iUserID, "INFO", f"Email Notification Successfully Sent to {to}.")

        except Exception as e:
            await CLogController.MSWriteLog(self.iUserID, "Error", "Failed to send email.")
            await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

if __name__ == "__main__":
    import asyncio
    # ls  = os.getenv("CONTACTUS").split(",")
    # print(ls)
    emailer = CEmailer()
    asyncio.run(emailer.send_email(
        to= Constants.lsContectUsList,
        template_name='BugReportNotification2',
        template_data={
            'Customer_Name': "data",
            'Ticket_ID': "db_bug.BugTrackingID",
            'Description': "db_bug.Description",
            'Date_Submitted': "db_bug.CreatedDateTime",
            'Response_Time_Frame': "24 hours",
            'SupportPhoneNumber': os.getenv("CONTACTNUMBER")
        }
    ))

# if __name__ == "__main__":
#     import asyncio

#     emailer = CEmailer()
#     asyncio.run(emailer.send_email(
#         to=["<EMAIL>"],
#         template_name='NewContactUsNotification',
#         template_data={
#             'customer_name': "Dhruvin Kapadiya",
#             'query_category': f"{Constants.iTrialDays} days",
#             'email':'<EMAIL>',
#             'phone_number': '1234567890',
#             'country': 'India',
#             'designation': 'Software Engineer',
#             'company_name': 'RiverEdge Analytics',
#             'message': 'Thank you for contacting us. We will get back to you as soon as possible.',
#             'status':''
#         }
#     ))

# if __name__ == "__main__":
    
# emailer = CEmailer()
# emailer.send_email(
#     to='<EMAIL>',
#     subject='Welcome to AccuVelocity!',
#     template_name='resource\\Email_Templates\\Check_in.html',
#     template_data={
#         'username': 'JohnDoe',
#         'trial_end_date': '2024-12-31',
#         'support_email': '<EMAIL>'
#     }
# )


# if __name__ == "__main__":
#     pass
    # Example usage
    # lsEmails = ["<EMAIL>"]
    # for email in lsEmails:
    #     email_verifier = CEmailVerifier(email)
    #     print(email_verifier.isValidEmail())
    #     print(email_verifier.MSGetResponse())

# Invalid email detected. Please use a valid, non-disposable email address to register. Contact <EMAIL> for assistance.
