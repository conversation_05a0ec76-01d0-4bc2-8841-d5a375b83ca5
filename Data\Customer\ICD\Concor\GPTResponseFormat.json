{"type": "json_schema", "json_schema": {"name": "concor", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerICD": {"type": "string", "description": "Invoice code or Item code (ICD) of the seller providing the goods or services"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN or GST) of the supplier or seller"}, "SellerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the seller"}, "SellerIRN": {"type": "string", "description": "Full Invoice Reference Number (IRN) of the issuing company"}, "InvoiceNo": {"type": "string", "description": "Unique identification number assigned to the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyy format) on which the invoice was issued."}, "RegisterPartyName": {"type": "string", "description": "The name of the registered receiver."}, "RegisterPartyGSTIN": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN) of the registered receiver."}, "RegisterPartyAddr": {"type": "string", "description": "The address of the registered receiver."}, "RegisterState": {"type": "string", "description": "The State Name of registered receiver."}, "RegisterStateCode": {"type": "string", "description": "The state code of the registered receiver."}, "RegisterCustomerCode": {"type": "string", "description": "A unique customer code assigned to the registered receiver by the seller or service provider."}, "RegisterServiceSegment": {"type": "string", "description": "The category or sector of service for which the invoice is being issued (e.g., IT, Manufacturing, etc.)."}, "UnregisterPartyName": {"type": "string", "description": "The name of the unregistered receiver."}, "UnregisterPartyAddr": {"type": "string", "description": "The address of the unregistered receiver."}, "UnregisterState": {"type": "string", "description": "The State Name of unregistered receiver."}, "UnregisterStateCode": {"type": "string", "description": "The state code of the unregistered receiver."}, "UnregisterAddressOfDelivery": {"type": "string", "description": "The address where the goods or services are being delivered to the unregistered receiver."}, "TotalUpfrontFreightDiscount": {"type": "number", "description": "The total amount of upfront discount applied to the freight charges in the invoice. This is a decimal number  with two digit after the decimal point."}, "TotalInvoiceValueFigure": {"type": "number", "description": "The total value of the invoice, represented numerically, including all products, services, taxes, and discounts. This is a decimal number  with two digit after the decimal point."}, "TotalInvoiceValueWords": {"type": "string", "description": "The total value of the invoice written out in words for clarity and legal purposes."}, "AmountOfTaxSubjectToReverseCharge": {"type": "number", "description": "The total amount of tax that is subject to reverse charge."}, "PlaceOfSupplyStateCode": {"type": "integer", "description": "The state code  where the supply is made."}, "PlaceOfSupplyStateName": {"type": "string", "description": "The name of the state where the supply is made, e.g., 'Madhya Pradesh'."}, "UserName": {"type": "string", "description": "The unique identifier or name used by an individual to log in or be recognized in a system or platform."}, "Designation": {"type": "string", "description": "The official title or position held by an individual with in an organization or system."}, "TotalAmount": {"type": "number", "description": "The total amount of all items in the Table, excluding the TotalInvoiceValue."}, "TotalAbatement": {"type": "number", "description": "The total amount of abatement (discount or reduction) applied in the invoice."}, "TotalAbatedValue": {"type": "number", "description": "The total taxable value of the service after applying any abatements or reductions."}, "TotalDisRate": {"type": "number", "description": "The total rate of the discount or abatement applied to the taxable value, if applicable."}, "TotalWvrAmount": {"type": "number", "description": "The total amount waived off from the taxable value, if applicable."}, "TotalCGSTAmount": {"type": "number", "description": "The total amount of Central Goods and Services Tax (CGST) calculated based on the taxable value. The vaue likes 2605.50,2461.05, ect"}, "TotalSGSTAmount": {"type": "number", "description": "The total amount of State Goods and Services Tax (SGST) calculated based on the taxable value. value from ItemTable1,The vaue likes 2605.50,2461.05, ect"}, "TotalIGSTAmount": {"type": "number", "description": "The total amount of Integrated Goods and Services Tax (IGST) calculated based on the taxable value. value from ItemTable1"}, "ItemTable1": {"type": "array", "items": {"type": "object", "properties": {"SrNo1": {"type": "integer", "description": "The serial number of the item in the invoice"}, "ContainerNumber": {"type": "string", "description": "The unique number identifying the container involved in the transaction, if applicable."}, "ActivityDescOfService": {"type": "string", "description": "A brief description of the service or activity provided in the transaction."}, "AccountingCodeOfService": {"type": "integer", "description": "The unique accounting code used to identify the service in the accounting system."}, "Amount": {"type": "integer", "description": "The total taxable amount for the specific item or service before applying any discounts, abatements, or taxes. This represents the gross value of the goods or services provided in the transaction."}, "Abatement": {"type": "integer", "description": "The specific amount of discount or reduction applied to the taxable value of the item or service, as per applicable tax regulations or contractual agreements. This reduces the taxable amount before calculating applicable taxes."}, "AbatedValue": {"type": "integer", "description": "The taxable value of the service after applying any abatements or reductions."}, "PlaceOfSupplyState": {"type": "integer", "description": "The state where the supply is made, used for determining tax applicability ."}, "DisRate": {"type": "number", "description": "The rate of the discount or abatement applied to the taxable value, if applicable."}, "WvrAmount": {"type": "number", "description": "The amount waived off from the taxable value, if applicable."}, "CGSTRate": {"type": "number", "description": "The rate of Central Goods and Services Tax (CGST) applicable to the service or goods."}, "CGSTAmount": {"type": "number", "description": "The amount of Central Goods and Services Tax (CGST) calculated based on the taxable value."}, "SGSTRate": {"type": "number", "description": "The rate of State Goods and Services Tax (SGST) applicable to the service or goods."}, "SGSTAmount": {"type": "number", "description": "The amount of State Goods and Services Tax (SGST) calculated based on the taxable value."}, "IGSTRate": {"type": "number", "description": "The rate of Integrated Goods and Services Tax (IGST) applicable to the service or goods."}, "IGSTAmount": {"type": "number", "description": "The amount of Integrated Goods and Services Tax (IGST) calculated based on the taxable value."}}, "required": ["SrNo1", "ContainerNumber", "ActivityDescOfService", "AccountingCodeOfService", "Amount", "Abatement", "AbatedValue", "DisRate", "WvrAmount", "PlaceOfSupplyState", "CGSTRate", "CGSTAmount", "SGSTRate", "SGSTAmount", "IGSTRate", "IGSTAmount"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced including duplicate rows"}, "LsContainerNumber/Size": {"type": "array", "description": "Contains container numbers and sizes in the format 'cont_no/size'. This list should contain unique values.", "example_values": ["ESDU2232162 /20", "ESDU1341596/20"], "items": {"type": "string", "description": "Container number and size combined as 'cont_no/size'. Example: 'FFAU3647502/40'."}}, "UserName1": {"type": "string", "description": "Name of the user"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "SellerICD", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerGSTIN", "SellerPANNo", "SellerIRN", "InvoiceNo", "InvoiceDate", "RegisterPartyName", "RegisterPartyGSTIN", "RegisterPartyAddr", "RegisterState", "RegisterStateCode", "RegisterCustomerCode", "RegisterServiceSegment", "UnregisterPartyName", "UnregisterPartyAddr", "UnregisterState", "UnregisterStateCode", "UnregisterAddressOfDelivery", "TotalUpfrontFreightDiscount", "TotalInvoiceValueFigure", "TotalInvoiceValueWords", "AmountOfTaxSubjectToReverseCharge", "PlaceOfSupplyStateCode", "PlaceOfSupplyStateName", "UserName", "Designation", "TotalAmount", "TotalAbatement", "TotalAbatedValue", "TotalDisRate", "TotalWvrAmount", "TotalCGSTAmount", "TotalSGSTAmount", "TotalIGSTAmount", "ItemTable1", "LsContainerNumber/Size", "UserName1"], "additionalProperties": false}}}