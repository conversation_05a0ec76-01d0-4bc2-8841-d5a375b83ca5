import logo from '../assets/accuvelocitylogowhite.svg';
import AVlogo from '../assets/AccuVelocity.svg';

const Footer = () => {
  return (
    <footer className="bg-[#003654] text-white font-inter" style={{
      paddingTop: '5rem',
      paddingBottom: '5rem'
    }}>
      <div className="max-w-7xl py-1 mx-auto flex flex-wrap justify-between p-3">
        <div className="flex items-center space-x-2 mb-7 relative">

          <img src={logo} alt="AccuVelocity" className="h-8" />
          <img src={AVlogo} alt="AccuVelocity" className="h-8" />

        </div>
        <hr className="border-t border-white-400 w-4/5 mt-3.5" />
        <div className="flex justify-between w-full p-4">
          <div className="flex-1 mb-4 mr-8">
            <h4 className="font-bold text-lg mb-2 ">About Us</h4>
            <a href="/company-overview" className="hover:text-blue-300 block mb-1 transition-colors">Company Overview</a>
            <a href="/press-media" className="hover:text-blue-300 block mb-1 transition-colors">Press & Media</a>
            <a href="/testimonials" className="hover:text-blue-300 block mb-1 transition-colors">Testimonials</a>
          </div>
          <div className="flex-1 mb-4 mr-8">
            <h4 className="font-bold text-lg mb-2">Resources</h4>
            <a href="/blog" className="hover:text-blue-300 block mb-1 transition-colors">Blog</a>
            <a href="/help-center" className="hover:text-blue-300 block mb-1 transition-colors">Help Center</a>
            <a href="/case-studies" className="hover:text-blue-300 block mb-1 transition-colors">Case Studies</a>
          </div>
          <div className="flex-1 mb-4 mr-8">
            <h4 className="font-bold text-lg mb-2">Support & Contact</h4>
            <a href="/contact" className="hover:text-blue-300 block mb-1 transition-colors">Contact Us</a>
            <a href="/technical-support" className="hover:text-blue-300 block mb-1 transition-colors">Technical Support</a>
            <a href="/feedback" className="hover:text-blue-300 block mb-1 transition-colors">Feedback</a>
          </div>
          <div className="space-y-1">
            <h4 className="font-bold text-lg">Connect</h4>
            <div className="flex flex-col space-y-1">
              <a href="https://www.facebook.com/AccuVelocity" className="hover:text-blue-300 transition-colors flex items-center font-thin"><svg className="mr-2 ml-0.5" width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M15.0376 8.0188C15.0376 3.86842 11.6692 0.5 7.5188 0.5C3.36842 0.5 0 3.86842 0 8.0188C0 11.6579 2.58647 14.688 6.01504 15.3872V10.2744H4.51128V8.0188H6.01504V6.1391C6.01504 4.68797 7.19549 3.50752 8.64662 3.50752H10.5263V5.76316H9.02256C8.60902 5.76316 8.27068 6.1015 8.27068 6.51504V8.0188H10.5263V10.2744H8.27068V15.5C12.0677 15.1241 15.0376 11.9211 15.0376 8.0188Z" fill="white" />
              </svg>
                Facebook</a>
              <a href="https://www.instagram.com/AccuVelocity" className="hover:text-blue-300 transition-colors flex items-center font-thin"><svg className="mr-2" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" clipRule="evenodd" d="M16.5784 4.08455C16.8013 4.6733 16.9177 5.29782 16.9268 5.92733C16.9732 6.71947 16.9734 6.97854 16.975 8.98365L16.9751 9.01165C16.9751 11.0432 16.9643 11.2968 16.9285 12.0968C16.916 12.7197 16.7988 13.3334 16.5801 13.9155C16.1901 14.92 15.3959 15.715 14.3905 16.105C13.8084 16.3237 13.1939 16.441 12.5727 16.4534C11.7786 16.5 11.5183 16.5 9.48766 16.5H9.48754C7.45682 16.5 7.20319 16.4892 6.40238 16.4534C5.77869 16.4335 5.16416 16.3054 4.58538 16.0792C3.57917 15.6901 2.78501 14.8951 2.395 13.8905C2.17629 13.3076 2.05904 12.6939 2.04657 12.0719C2 11.2769 2 11.0174 2 8.9867C2 6.95599 2.01081 6.70236 2.04657 5.90155C2.05821 5.28036 2.17629 4.66582 2.395 4.08455C2.78418 3.07917 3.57917 2.28418 4.58455 1.895C5.16582 1.67629 5.78036 1.55904 6.40155 1.54574C7.1957 1.5 7.45599 1.5 9.4867 1.5C11.5174 1.5 11.7711 1.50998 12.571 1.54574C13.193 1.55904 13.8076 1.67629 14.3889 1.895C15.3942 2.28418 16.1884 3.07917 16.5784 4.08455ZM5.63733 8.98255C5.63733 11.1064 7.35786 12.8269 9.48171 12.8269C11.6039 12.8269 13.3261 11.1064 13.3261 8.98255C13.3261 6.85869 11.6039 5.13816 9.48171 5.13816C7.35786 5.13816 5.63733 6.85869 5.63733 8.98255ZM12.5827 4.99596C12.5827 5.49158 12.9827 5.8924 13.4791 5.8924C13.9739 5.8924 14.3747 5.49158 14.3747 4.99596C14.3747 4.50034 13.9739 4.09951 13.4791 4.09951C12.9827 4.09951 12.5827 4.50034 12.5827 4.99596ZM11.9788 8.98161C11.9788 10.3608 10.8608 11.4788 9.48161 11.4788C8.10242 11.4788 6.98438 10.3608 6.98438 8.98161C6.98438 7.60242 8.10242 6.48438 9.48161 6.48438C10.8608 6.48438 11.9788 7.60242 11.9788 8.98161Z" fill="white" />
              </svg>
                Instagram</a>
              <a href="https://twitter.com/AccuVelocity" className="hover:text-blue-300 transition-colors flex items-center font-thin"><svg className="mr-2" width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M13.8131 2.25H16.1131L11.0881 7.96913L17 15.75H12.3713L8.74625 11.0303L4.5975 15.75H2.29625L7.67125 9.63259L2 2.25062H6.74625L10.0231 6.56455L13.8131 2.25ZM13.0063 14.3796H14.2806L6.05375 3.54878H4.68625L13.0063 14.3796Z" fill="white" />
              </svg>
                Twitter / X</a>
              <a href="https://www.linkedin.com/company/AccuVelocity" className="hover:text-blue-300 transition-colors flex items-center font-thin"><svg className='ml-0.5 mr-2.5' width="15" height="16" viewBox="0 0 15 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fillRule="evenodd" clipRule="evenodd" d="M12.5 0.5C13.163 0.5 13.7989 0.763392 14.2678 1.23223C14.7366 1.70107 15 2.33696 15 3V13C15 13.663 14.7366 14.2989 14.2678 14.7678C13.7989 15.2366 13.163 15.5 12.5 15.5H2.5C1.83696 15.5 1.20107 15.2366 0.732233 14.7678C0.263392 14.2989 0 13.663 0 13V3C0 2.33696 0.263392 1.70107 0.732233 1.23223C1.20107 0.763392 1.83696 0.5 2.5 0.5H12.5ZM4.16667 6.33333C3.94565 6.33333 3.73369 6.42113 3.57741 6.57741C3.42113 6.73369 3.33333 6.94565 3.33333 7.16667V11.3333C3.33333 11.5543 3.42113 11.7663 3.57741 11.9226C3.73369 12.0789 3.94565 12.1667 4.16667 12.1667C4.38768 12.1667 4.59964 12.0789 4.75592 11.9226C4.9122 11.7663 5 11.5543 5 11.3333V7.16667C5 6.94565 4.9122 6.73369 4.75592 6.57741C4.59964 6.42113 4.38768 6.33333 4.16667 6.33333ZM6.66667 5.5C6.44565 5.5 6.23369 5.5878 6.07741 5.74408C5.92113 5.90036 5.83333 6.11232 5.83333 6.33333V11.3333C5.83333 11.5543 5.92113 11.7663 6.07741 11.9226C6.23369 12.0789 6.44565 12.1667 6.66667 12.1667C6.88768 12.1667 7.09964 12.0789 7.25592 11.9226C7.4122 11.7663 7.5 11.5543 7.5 11.3333V8.28333C7.82511 7.92969 8.2205 7.64784 8.66083 7.45583C8.93833 7.3375 9.35583 7.28917 9.64583 7.38083C9.74204 7.40524 9.82772 7.46022 9.89 7.5375C9.93333 7.59583 10 7.72583 10 8V11.3333C10 11.5543 10.0878 11.7663 10.2441 11.9226C10.4004 12.0789 10.6123 12.1667 10.8333 12.1667C11.0543 12.1667 11.2663 12.0789 11.4226 11.9226C11.5789 11.7663 11.6667 11.5543 11.6667 11.3333V8C11.6667 7.44167 11.525 6.945 11.23 6.54667C10.9585 6.18546 10.5786 5.9206 10.1458 5.79083C9.39417 5.555 8.56167 5.68583 8.00583 5.92417C7.82774 6.00039 7.65437 6.08721 7.48667 6.18417C7.45173 5.99216 7.35052 5.8185 7.20066 5.69347C7.05081 5.56844 6.86183 5.49997 6.66667 5.5ZM4.16667 3.83333C3.94565 3.83333 3.73369 3.92113 3.57741 4.07741C3.42113 4.23369 3.33333 4.44565 3.33333 4.66667C3.33333 4.88768 3.42113 5.09964 3.57741 5.25592C3.73369 5.4122 3.94565 5.5 4.16667 5.5C4.38768 5.5 4.59964 5.4122 4.75592 5.25592C4.9122 5.09964 5 4.88768 5 4.66667C5 4.44565 4.9122 4.23369 4.75592 4.07741C4.59964 3.92113 4.38768 3.83333 4.16667 3.83333Z" fill="white" />
              </svg>
                LinkedIn</a>
            </div>
          </div>
        </div>
        <hr className="border-t border-white w-full my-4" />
      </div>

      <div className="flex justify-between items-center max-w-7xl mx-auto">
        <span className="text-sm ml-4">©2024 AccuVelocity - All rights reserved.</span>
        <div className="flex text-sm">
          <a href="/terms-of-use" className="hover:text-blue-300 transition-colors mr-4 underline">Terms of use</a>
          <a href="/privacy-policy" className="hover:text-blue-300 transition-colors mr-4 underline">Privacy policy</a>
          <a href="/security" className="hover:text-blue-300 transition-colors mr-4 underline">Security</a>
          <a href="/settings" className="hover:text-blue-300 transition-colors mr-4 underline">Cookie Settings</a>
        </div>
      </div>
    </footer>
  );
};

export default Footer;
