import sys

from src.Controllers.GwaliaController_XML import CGwalia
from src.Controllers.PremTextilesController import CPremTextiles
sys.path.append(".")
from src.Controllers.ParagTradersControllers import CParagTraders, CParagTraders_XML
# from src.Controllers.ParagTradersControllers import CParagTraders
from  src.Controllers.ICDController import CICDController
from src.Controllers.AVRequestDetailController import CAVRequestDetail


import json
import asyncio
import os
import sys
import math
sys.path.append(".")
import re
import requests
import traceback
from datetime import datetime, timedelta
from collections import defaultdict
import pytz
from fastapi import HTTPException
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.VedanshSchoolController_XML import CVedanshInternationSchool
from src.Schemas.Tally_Schemas import TallyStockItemObj, TallyStockItemColumn, TallyRoundOffAmountObj, PTAdditionalChargesObj, sanitaryStockItemReqObj, GPTResponseResObj, ParagTradersStockItemCreation, TallyAPIHeaderObj, TallyLedgerName, chemicalStockItemReqObj, TallyPriceListReportResponse
from src.utilities.DBHelper import CExtractionTable
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.utilities.helperFunc import CJSONFileReader, CommonHelper
from src.utilities.PriceListAVREPORT import CPriceListREPORT
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from config.db_config import AsyncSessionLocal
from src.Controllers.AbhinavInfrabuildController import CAbhinavInfrabuild
from src.Models.models import  TallyBackupRecord
from config.constants import Constants
from sqlalchemy import Date
from sqlalchemy.future import select
import pandas as pd
import re

  

class CTallyIndianInvoiceController:

    def __init__(self, user_id, doc_id, dictLsClientDocMetaData,dictExtractedData,lsAllDocInfo, strClientREQID= None,bTestMode=False, strVoucherType = "PURCHASE_WITH_INVENTORY"):
        self.iUserId = user_id
        self.DocId = doc_id
        self.TallyUserConfigObj = None
        self.UploadDocObj = None
        self.ExtractedDocObj = None
        self.tally_body_obj = []
        self.tally_NotExistStockItems = {}
        self.dictProcessData = {}
        self.CompanyName = None
        self.strClientREQID = strClientREQID
        self.dictLsClientDocMetaData=dictLsClientDocMetaData
        self.lsAllDocInfo=lsAllDocInfo
        self.dictExtractedData=dictExtractedData
        self.bTestMode=bTestMode 
        # Append the new row to the grouped DataFrame
        self.dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
        self.strVoucherType = strVoucherType
        dictUsersConfig = CJSONFileReader.read_json_file(Constants.srtTallUserConfig)    
        self.CompanyName = CTallyIndianInvoiceController.MSGetUserConfig(self.iUserId, dictUsersConfig)
        # Remove Space, make incase sensitive
        self.CompanyName = CTallyIndianInvoiceController.MSNormalizeString(self.CompanyName)

    @staticmethod
    def MSGetUserConfig(user_id, config_dict):
             
            user_id_str = str(user_id)
            
            
            if user_id_str in config_dict:
              
                return config_dict[user_id_str]["customerName"]
            else:
                return None  # or you could return a default value or raise an exception

    @staticmethod
    def MSCheckDocModel(model_name, dictAvailableCompany):
        for company, models in dictAvailableCompany.items():
            if model_name in models:
                return company
                

        raise ValueError("Document Company Format Not Found in our Software")
    
    @staticmethod
    async def MSConvertIntToDateFromYYYYMMDD(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats (YYYYMMDD, YYMMDD, etc.)
        to MM.DD.YYYY format.

        Supported formats:
        - 8 digits: YYYYMMDD
        - 7 digits: YYYYMD or YYMMDD
        - 6 digits: YYMMDD or YMMDD
        """

        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 8:
                # Format: YYYYMMDD (e.g., 20250125 -> 01.25.2025)
                year = invoice_str[:4]
                month = invoice_str[4:6]
                day = invoice_str[6:]

            elif length == 7:
                # Format: YYYYMD or YYMMDD (e.g., 2025015 -> 01.05.2025)
                if invoice_str[4] in '0123456789' and len(invoice_str[5:]) == 1:
                    # Case where year is in YYYY and month is a single digit
                    year = invoice_str[:4]
                    month = invoice_str[4:5]
                    day = invoice_str[5:]
                else:
                    # Case where year is in YY
                    year = '20' + invoice_str[:2]
                    month = invoice_str[2:4]
                    day = invoice_str[4:]

            elif length == 6:
                # Format: YYMMDD or YMMDD (e.g., 240825 -> 08.25.2024)
                if invoice_str[2] in '0123456789':
                    # Case where year is in YY
                    year = '20' + invoice_str[:2]
                    month = invoice_str[2:4]
                    day = invoice_str[4:]
                else:
                    # Case where year is in Y and month is a single digit
                    year = '20' + invoice_str[0]
                    month = invoice_str[1:2]
                    day = invoice_str[2:]

            else:
                await CLogController.MSWriteLog(None, "Error", f"Invalid date format received: {invoice_date}")
                return "Invalid date format"

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string in MM.DD.YYYY format
            converted_date = f"{month_int:02}.{day_int:02}.{year_int}"
            await CLogController.MSWriteLog(
                None, "Info", f"Converted integer date {invoice_date} to string date {converted_date}."
            )
            return converted_date
        except ValueError as ve:
            await CLogController.MSWriteLog(None, "Error", f"ValueError in MSConvertIntToDate: {ve}")
            return "Invalid date format"
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Exception in MSConvertIntToDate: {str(e)}")
            return "Invalid date format"



        
    @staticmethod
    async def MSConvertIntToDateFromDDMMYYYY(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats to a standard MM.DD.YYYY format.

        Supported formats:
        - DDMMYYYY
        - DDMMYY
        - DMMYY
        - DMMYYYY

        Note:
            Month: One or two digits
            Day: Two digits
            Year: Two or four digits
        """
        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 6:
                # Format: DDMMYY (e.g., 080524 -> 08.05.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = '20' + invoice_str[4:]
            elif length == 8:
                # Format: DDMMYYYY (e.g., 08102024 -> 08.10.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = invoice_str[4:]
            elif length == 5:
                # Format: DMMYY (e.g., 80524 -> 08.05.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = '20' + invoice_str[3:]
            elif length == 7:
                # Format: DMMYYYY (e.g., 8102024 -> 08.10.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = invoice_str[3:]
            else:
                await CLogController.MSWriteLog(None, "Error", f"Invalid date format received: {invoice_date}")
                return "Invalid date format"

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string in the format MM.DD.YYYY
            converted_date = f"{month_int:02}.{day_int:02}.{year_int}"
            await CLogController.MSWriteLog(
                None, "Info", f"Converted integer date {invoice_date} to string date {converted_date}."
            )
            return converted_date
        except ValueError as ve:
            await CLogController.MSWriteLog(None, "Error", f"ValueError in MSConvertIntToDate: {ve}")
            return "Invalid date format"
        except Exception as e:
            await CLogController.MSWriteLog(0, "Error", f"Exception in MSConvertIntToDate: {str(e)}")
            return "Invalid date format"

    async def MSetPriceListPARAGTRADERS(self, strPriceListReport):
        """
        Input:

            1) strPriceListReport: str
            The file path where the generated price list report should be saved.

        Output:

            None

        Purpose:

            This method checks if the AV XML was successfully or partially generated.
            If so, it creates a price list report using the extracted data and vendor name,
            and then marks the `PriceListVerification` column in the `AVRequestDetail` table as `True`.

            If the XML generation status is not successful, it still updates the
            `PriceListVerification` column as `False` to log the verification attempt.
        """
        try:
            dictGetAVReportData = await CAVRequestDetail.MSGetRecord(
                iUserId=self.iUserId,
                strClientREQID=self.strClientREQID,
                DocID=self.DocId
            )

            if dictGetAVReportData["data"]["AVXMLGeneratedStatus"] in ["Success", "PartialSuccess"]:
                # AVRecordDetail - PriceListVerification Update
                await CPriceListREPORT.MSCreatePriceListReport(
                    iUserId=self.iUserId,
                    dictExtractedData=self.dictExtractedData,
                    strVendorName=self.strVendorName,
                    strReportFilePath=strPriceListReport
                )
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId=self.iUserId,
                    strClientREQID=self.strClientREQID,
                    DocID=self.DocId,
                    PriceListVerification=True
                )
            else:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId=self.iUserId,
                    strClientREQID=self.strClientREQID,
                    DocID=self.DocId,
                    PriceListVerification=False
                )
        except Exception as e:
            await CLogController.MSWriteLog(self.iUserId, "Error", f"Error in MSetPriceListPARAGTRADERS: {str(e)}")

    async def MInitialize(self):
        try:
            await CLogController.MSWriteLog(self.iUserId, "Info", "Initializing the model.")

            # Fetch Tally User Configuration  Use In Only Parag for purchase
            self.TallyUserConfigObj = await CTallyController.MSGetTallyUserConfig(iUserID=self.iUserId)
            await CLogController.MSWriteLog(self.iUserId, "Info", "Fetched Tally user configuration.")

            
            # Validate Tally Enable
            if not self.TallyUserConfigObj.get("TallyEnable"):
                await CLogController.MSWriteLog(self.iUserId, "Error", "Tally is not enabled for the user.")
                raise HTTPException(
                    status_code=400, 
                    detail="Tally is not enabled for you. Please contact the developer team."
                )

            # Get Upload Document Attributes
            self.UploadDocObj = await CDocumentData.MSGetDocById(
                user_id=self.iUserId, 
                docId=self.DocId,
                isBinaryDataRequired=False,
                strVoucherType=self.strVoucherType
            )
            await CLogController.MSWriteLog(self.iUserId, "Info", f"Fetched document details for DocId: {self.DocId}.")

            # Validate Model Name
            model_name = self.UploadDocObj.get("ModelName", "").lower()


            # TODO: Use this vendor name to decide whcih child instance needs to be created
            self.strVendorName = model_name
   
            await CLogController.MSWriteLog(self.iUserId, "Info", "Initialization completed successfully.")
            
            # Check Duplicate Entry Exist
            bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserID = self.iUserId, iDocID=self.DocId,objVoucherType = self.strVoucherType)
            
            if bIsDuplicate and not self.bTestMode: # RAISE ERROR When Duplicat Entry Detected + Ignore Duplicates in Case of Developer MODE
                await CLogController.MSWriteLog(self.iUserId, "Info", f"Duplicate entry found for Document ID '{self.DocId}'.")
                await CAVRequestDetail.MSUpdateRecord(iUserId=self.iUserId, strClientREQID = self.strClientREQID, DocID=self.DocId, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE',CReqServerCompletedAt=datetime.now(), AVXMLGeneratedStatus="Duplicate",TracebackLogs= "WARNING - Duplicate Entry Detected in our AccuVelocity Software", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments="Tally XML: Duplicate Validation Found.")
                self.iTallyDocRecordID = await CTallyController.MSCreateTallyDocRecord(
                    iUserId=self.iUserId,
                    iDocID=self.DocId,  ## In case of Reuse Docid , new row create in Tally Doc Records with Unique Request ID
                    strREQID=self.strClientREQID,
                    av_tally_xml_status="Duplicate",
                    strAVComments="-",
                    voucher_type = self.strVoucherType
                )
                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")
            else:
                # Initialize Tally Doc Record , Upload Document Tally Details
                self.iTallyDocRecordID = await CTallyController.MSCreateTallyDocRecord(
                    iUserId=self.iUserId,
                    iDocID=self.DocId,  ## In case of Reuse Docid , new row create in Tally Doc Records with Unique Request ID
                    strREQID=self.strClientREQID,
                    tally_status="Skipped",  # Client Tally XML Status
                    av_tally_xml_status="Skipped",
                    strAVComments="-",
                    voucher_type = self.strVoucherType
                )

        except ValueError as ve:
            if str(ve) == "Tally XML: Duplicate Entry Found in AccuVelocity.":
                raise ve
            else:
                await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise ve
        except HTTPException as he:
            await CLogController.MSWriteLog(self.iUserId, "Error", f"HTTPException during initialization: {he.detail}")
            await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        except Exception as e:
            await CLogController.MSWriteLog(self.iUserId, "Error", "Failed to initialize the Simpolo model.")
            await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print("Error Occurred - ", traceback.print_exc())
            raise e
        
    @staticmethod
    def MSNormalizeString(text):
        return text.replace(" ", "").lower()
    
    
    
    async def MCallTallyAPI(self, bRaiseError=False,dictLsClientDocMetaData=[{}], strPriceListReport = None, ObjVoucherType = None):
        # TODO: Please Optimize Child Class By Inheriting this Parent Class Properties & Methods 
        dictResult = {
                    "AVComments":"-",
                    "TallyStatus":"-",
                    "XMLFilePath": ""
                }
        
        try:
            await self.MInitialize()
            
            # create developer access dict 
            dictDeveloperDecideVendor = CBusinessIntelligence.MSCheckVendorPresence(self.strVendorName, self.dictAvailableCompany)
            if self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("ParagTraders") or dictDeveloperDecideVendor.get("PARAG TRADERS", False):
                # Additional UDF Related Information Fetching -----------------------------
                lsUDFData = None
                if dictLsClientDocMetaData is None or not dictLsClientDocMetaData:
                    dictLsClientDocMetaData = {}
                try:
                    for key, value in dictLsClientDocMetaData.items():
                        strClientDocCheckSum = key.lower()
                        
                        if strClientDocCheckSum in self.UploadDocObj.get("CheckSum") or strClientDocCheckSum == self.UploadDocObj.get("CheckSum"):
                            lsUDFData = value
                            break
                except Exception as e:
                    print("Error occur - ", e)
                    pass # silently execute remain code , print("Error occur - ", e)
                if lsUDFData is None:
                    lsUDFData = [{}]
                
                #  ------------------------------------------------------------------------

                dictResult = await CParagTraders_XML.MSGenerateTallyXML(iUserId=self.iUserId, iDocId=self.DocId, dictExtractedData=self.dictExtractedData, strVendorName=self.strVendorName, bRaiseError=False, lsUdfData=lsUDFData, strClientREQID=self.strClientREQID, strVoucherType = self.strVoucherType)
                
                # Create PriceList for PARAG TRADER Customer
                # await self.MSetPriceListPARAGTRADERS(strPriceListReport) # NOTE: Embedding taking longer time
            elif self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("ICD") or dictDeveloperDecideVendor.get("ICD", False):
                objProcessDoc = CICDController(user_id=self.iUserId, doc_id=self.DocId)
                # await objProcessDoc.MCallTallyAPI(bRaiseError=bRaiseError, strModelName=self.strVendorName, bTestMode=bTestMode, bVerifyBackup=bVerifyBackup, bSaveTallyReq=bSaveTallyReq)
                dictResult = await objProcessDoc.MGenerateTallyXML(iUserId=self.iUserId, 
                                                                                    iDocId=self.DocId,
                                                                                    dictExtractedData=self.dictExtractedData, 
                                                                                    bRaiseError=False, strClientREQID=self.strClientREQID)
            
            elif self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("Vedansh School") or dictDeveloperDecideVendor.get("VEDANSHSCHOOL", False):
                lsUDFData = None
                if dictLsClientDocMetaData is None or not dictLsClientDocMetaData:
                    dictLsClientDocMetaData = {}
                try:
                    for key, value in dictLsClientDocMetaData.items():
                        strClientDocCheckSum = key.lower()
                        
                        if strClientDocCheckSum in self.UploadDocObj.get("CheckSum") or strClientDocCheckSum == self.UploadDocObj.get("CheckSum"):
                            lsUDFData = value
                            break
                except Exception as e:
                    print("Error occur - ", e)
                    pass # silently execute remain code , print("Error occur - ", e)

                if lsUDFData is None:
                    lsUDFData = [{}]

                objProcessDoc = CVedanshInternationSchool(user_id=self.iUserId, doc_id=self.DocId)
                # await objProcessDoc.MCallTallyAPI(bRaiseError=bRaiseError, strModelName=self.strVendorName, bTestMode=bTestMode, bVerifyBackup=bVerifyBackup, bSaveTallyReq=bSaveTallyReq)
                dictResult = await objProcessDoc.MGenerateTallyXML(iUserId=self.iUserId, 
                                                                                    iDocId=self.DocId,
                                                                                    dictExtractedData=self.dictExtractedData,
                                                                                    strVendorName = self.strVendorName ,
                                                                                    bRaiseError=False, strClientREQID=self.strClientREQID, lsUdfData= lsUDFData, ObjVoucherType = ObjVoucherType, bIsDeveloperMode=self.bTestMode)
                
                
            elif self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("gwalia") or dictDeveloperDecideVendor.get("GWALIA", False):
                lsUDFData = None
                if dictLsClientDocMetaData is None or not dictLsClientDocMetaData:
                    dictLsClientDocMetaData = {}
                try:
                    for key, value in dictLsClientDocMetaData.items():
                        strClientDocCheckSum = key.lower()
                        
                        if strClientDocCheckSum in self.UploadDocObj.get("CheckSum") or strClientDocCheckSum == self.UploadDocObj.get("CheckSum"):
                            lsUDFData = value
                            break
                except Exception as e:
                    print("Error occur - ", e)
                    pass # silently execute remain code , print("Error occur - ", e)

                if lsUDFData is None:
                    lsUDFData = [{}]
                dictResult = await CGwalia.MGenerateTallyXML(iUserId=self.iUserId, 
                                                                iDocId=self.DocId,
                                                                dictExtractedData= self.dictExtractedData, 
                                                                strVendorName=self.strVendorName,
                                                                bRaiseError=False, lsUdfData=lsUDFData, strClientREQID=self.strClientREQID)
            elif self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("abhinav infrabuild") or dictDeveloperDecideVendor.get("abhinav infrabuild", False):
                lsUDFData = None
                if dictLsClientDocMetaData is None or not dictLsClientDocMetaData:
                    dictLsClientDocMetaData = {}
                try:
                    for key, value in dictLsClientDocMetaData.items():
                        strClientDocCheckSum = key.lower()
                        
                        if strClientDocCheckSum in self.UploadDocObj.get("CheckSum") or strClientDocCheckSum == self.UploadDocObj.get("CheckSum"):
                            lsUDFData = value
                            break
                except Exception as e:
                    print("Error occur - ", e)
                    pass # silently execute remain code , print("Error occur - ", e)
                if lsUDFData is None:
                    lsUDFData = [{}]
                dictResult = await CAbhinavInfrabuild.MGenerateTallyXML(iUserId=self.iUserId, 
                                                                iDocId=self.DocId,
                                                                dictExtractedData= self.dictExtractedData, 
                                                                strVendorName=self.strVendorName,
                                                                bRaiseError=False, lsUdfData=lsUDFData, strClientREQID=self.strClientREQID)
        

            elif self.CompanyName == CTallyIndianInvoiceController.MSNormalizeString("prem textiles") or dictDeveloperDecideVendor.get("PREM TEXTILES", False):
                dictResult = await CPremTextiles.MGenerateTallyXML(iUserId=self.iUserId, 
                                                                        iDocId=self.DocId,
                                                                        dictExtractedData= self.dictExtractedData, 
                                                                        strVendorName=self.strVendorName,
                                                                        bRaiseError=False, strClientREQID=self.strClientREQID)

            else:
                raise ValueError("No Customer Information Found in Our Software")

            
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID=self.DocId,
                iUserID=self.iUserId,
                strREQID= self.strClientREQID ,
                tally_api_resp=dictResult,
                resp_date_time = datetime.now()
            )
            # Store XML File Content For Every DocID
            if dictResult['XMLFilePath'] is not None and dictResult['XMLFilePath']:
                with open(dictResult['XMLFilePath'], 'r') as file:
                    strXMLContent = file.read()
                
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId=self.iUserId,
                    strClientREQID = self.strClientREQID,
                    DocID=self.DocId,
                    strXMLResponse = strXMLContent
                )
            else:
                await CLogController.MSWriteLog(self.iUserId, "Info", f"XMLFilePath is None or Empty String for DocId {self.DocId}")

            return dictResult
        except ValueError as ve:
            # NO Need to record this value error as --- In Case of Duplicate Tally Entry Detected We Raise Value ERROR from Initialize method
            if str(ve) == "Tally XML: Duplicate Entry Found in AccuVelocity.":
                raise ve
            else:
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=self.DocId,
                    iUserID=self.iUserId,
                    # invoice_no=None,
                    # invoice_date=None,
                    # tally_status="Skipped", # Client TALLY Status
                    # av_tally_xml_status="Skipped",
                    tally_api_resp=dictResult,
                    tally_api_req = None,
                    DocErrorMsg= str(ve),
                    # strAVComments =dictResult["AVComments"] if dictResult["AVComments"] != "-" else str(ve),
                    strREQID= self.strClientREQID ,
                    resp_date_time = datetime.now()
                )
                await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise ve
        except Exception as GenErr:
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID=self.DocId,
                iUserID=self.iUserId,
                # invoice_no=None,
                # invoice_date=None,
                # tally_status="Skipped", # Client TALLY Status
                # av_tally_xml_status="Skipped",
                tally_api_resp=dictResult,
                tally_api_req = None,
                DocErrorMsg= str(GenErr),
                # strAVComments =dictResult["AVComments"] if dictResult["AVComments"] != "-" else str(GenErr),
                strREQID= self.strClientREQID ,
                resp_date_time = datetime.now()
            )
            raise GenErr
        
        

async def main():
    # From GPT Obj or GPT Dict Json File Insert into Tally
    # objAsian = CParagTraders(user_id=4, doc_id=578)
    # objGPTResponseResObj = GPTResponseResObj(GPTResponseObjFile=r"H:\AI Data\17_ParagTraders\All API Response\14_POWERGRACE\202024_GST0271_gptResponse.json")     # for GPT Object Json File
    # objGPTResponseResObj = GPTResponseResObj(GPTResponseDictFile=r"temp.json")  # for GPT Content Dict Json
    # await CParagTraders.MSInsertGPTResObjIntoTally(objGPTResponseResObj=objGPTResponseResObj, strModelName="powergrace", bRaiseError=False, bSaveTallyReq=True)
    # await CParagTraders.MSSaveToTally

    objNexion = CTallyIndianInvoiceController(user_id=4, doc_id=1216)
    await objNexion.MCallTallyAPI(strModelName="Simpolo")
    # await objNexion.MSaveToTally()

if __name__ == "__main__":
    asyncio.run(main())
    # CTallyIndianInvoiceController()