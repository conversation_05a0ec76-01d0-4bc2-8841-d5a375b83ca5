# What to do with this imports
from dotenv import load_dotenv
import os
from google.cloud import documentai_v1 as documentai
from typing import Optional, Sequence
from google.api_core.client_options import ClientOptions



load_dotenv()
# Get the value of GOOGLE_APPLICATION_CREDENTIALS from the environment
credentials_path = os.getenv("GOOGLE_APPLICATION_CREDENTIALS")

def ProcessDocument(
    strProjectId: str,
    strLocation: str,
    strProcessorId: str,
    strProcessorVersion: str,
    file_binary: bytes,
    strMimeType: str,
    process_options: Optional[documentai.ProcessOptions] = None,
) -> documentai.Document:
    """
        Processes a document using Google Cloud Document AI.

        Input:
            1. project_id (str): Google Cloud project ID.
            2. location (str): The location where the processor is hosted (e.g., "us", "eu").
            3. processor_id (str): The processor's ID used for processing the document.
            4. processor_version (str): The specific processor version to use.
            5. input_file_path (str): Path to the document file to be processed.
            6. mime_type (str): The MIME type of the document (e.g., "application/pdf", "image/jpeg").
            7. process_options (Optional[documentai.ProcessOptions]): Optional processing options for Document AI.

        Output:
            1. documentai.Document in json format will be saved to local folder.
            2. documentai.Document: The processed Document AI object.
            
        Purpose:
            This function reads a document from a specified file path, processes it with the Google Cloud 
            Document AI, converts the result to JSON, and returns the processed `Document` object.
    """
    
    # Initialize the Document AI client with the correct endpoint based on location
    objClient = documentai.DocumentProcessorServiceClient(
        client_options=ClientOptions(api_endpoint=f"{strLocation}-documentai.googleapis.com")
    )

    # Construct the processor version resource name
    objName = objClient.processor_version_path(
        strProjectId, strLocation, strProcessorId, strProcessorVersion
    )

    # Configure the request for Document AI processing
    objRequest = documentai.ProcessRequest(
        name=objName,
        raw_document=documentai.RawDocument(content=file_binary, mime_type=strMimeType),
        process_options = process_options
    )

    # Process the document and retrieve the result
    objResult = objClient.process_document(request=objRequest)
    objDocument = objResult.document  # The processed Document object

    # Convert the processed document to a JSON string
    strDocToJsonString = documentai.Document.to_json(objDocument)

    # Return the processed document for further use or inspection
    return strDocToJsonString
