{"type": "json_schema", "json_schema": {"name": "AQUANT", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller"}, "SellerAddress": {"type": "string", "description": "<PERSON><PERSON>'s full address"}, "SellerGSTIN": {"type": "string", "description": "<PERSON><PERSON>'s GST Identification Number"}, "InvoiceNo": {"type": "string", "description": "Unique invoice number"}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "E-wayBillNo": {"type": "string", "description": "E-way bill Number, if applicable as some documents do not have them"}, "ReferenceNo": {"type": "string", "description": "Purchase Order reference number"}, "ReferenceDate": {"type": "integer", "description": "Purchase Order reference date"}, "Buyer'sOrderNo": {"type": "string", "description": "Buyer's order number"}, "Buyer'sOrderDate": {"type": "integer", "description": "Buyer's order date"}, "DispatchDocNo": {"type": "string", "description": "Dispatch Document Number"}, "DeliveryNoteDate": {"type": "string", "description": "Date of DeliveryDate"}, "DispatchedThrough": {"type": "string", "description": "Name of Dispatched Party, if applicable else empty string"}, "TotalQuantity": {"type": "string", "description": "Total Quantity of item table"}, "TotalAmount": {"type": "number", "description": "The sum of all item amounts in the table. NOTE: Do not include taxes, transport charges, courier charges, or round-off adjustments under any circumstances."}, "CourierCharges": {"type": "number", "description": "Amount of courier charges, if applicable otherwise it would be 0"}, "CourierHSN/SAC": {"type": "string", "description": "HSN or SAC code for the Courier Charges, if applicable otherwise it would be empty string"}, "TransportCharges": {"type": "number", "description": "Amount of transport charges, if applicable otherwise it would be 0"}, "TransportHSN/SAC": {"type": "string", "description": "HSN or SAC code for the transport Charges, if applicable otherwise it would be empty string"}, "IGST(OUTPUT)": {"type": "number", "description": "Total Integrated GST amount"}, "RoundOff": {"type": "number", "description": "Represents the rounded amount for the total, typically used to adjust the final sum to a desired precision (e.g., to the nearest whole number or decimal place)."}, "GrandTotalAmount": {"type": "number", "description": "The total Amount of items ,including tax and round off "}, "Company'sPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the company, which is a unique identifier for tax purposes in India."}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Serial number of the item"}, "DescriptionOfGoods": {"type": "string", "description": "Description of the goods or items"}, "HSN/SAC": {"type": "string", "description": "HSN or SAC code for the item"}, "Quantity": {"type": "string", "description": "Quantity of the item, in pcs formatted as {n} pcs, where n is a number, NOTE: Provide Quantity in specified format only."}, "Rate": {"type": "number", "description": "Rate per unit of the item"}, "Per": {"type": "string", "description": "Unit of measure (e.g., per kg, per piece)"}, "Disc%": {"type": "number", "description": "Discount percentage applied"}, "HighvalueDiscount": {"type": "number", "description": "High-value discount amount"}, "Amount": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "DescriptionOfGoods", "HSN/SAC", "Quantity", "Rate", "Per", "Disc%", "HighvalueDiscount", "Amount"], "additionalProperties": false}, "description": "List of primary items in the invoice without Courier Charges, and IGST (OUTPUT), and Round Off"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerGSTIN", "InvoiceNo", "InvoiceDate", "E-wayBillNo", "ReferenceNo", "ReferenceDate", "Buyer'sOrderNo", "Buyer'sOrderDate", "DispatchDocNo", "DeliveryNoteDate", "DispatchedThrough", "TotalQuantity", "TotalAmount", "CourierCharges", "CourierHSN/SAC", "TransportCharges", "TransportHSN/SAC", "IGST(OUTPUT)", "RoundOff", "GrandTotalAmount", "Company'sPAN", "Table"], "additionalProperties": false}}}