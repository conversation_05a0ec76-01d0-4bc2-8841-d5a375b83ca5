"""
Test Module: test_IndianInvRouteAbhinavPO.py

Purpose:
    This module tests the Indian Invoice Processing API for Abhinav PO (Purchase Order) documents.
    It processes XLS files containing PO data and validates the API response against expected outputs.

Test Flow:
    1. Upload XLS file to the API endpoint
    2. Receive processed ZIP file containing XML and CSV outputs
    3. Extract and validate the outputs against reference files (when BRELEARN=False)
    4. Save outputs for future reference (when BRELEARN=True)

File Types Supported:
    - Input: XLS files (Purchase Order format)
    - Output: ZIP files containing XML and CSV data


Date: 2025
"""

import unittest
import os
import asyncio
import socket
import json
import httpx
from Test.CustomHelper import TestLogger, CLicenseHelper, calculate_checksum, compare_xml, compare_csv, extract_zip

# Learning Mode Configuration
# BRELEARN = True:  Save API outputs as reference files for future testing
# BRELEARN = False: Compare API outputs against existing reference files
BRELEARN = False

# Directory Structure Configuration
TEST_DIR = "Test"  # Root directory for all test-related files

# Dynamic folder structure based on current file and BRELEARN flag
# This ensures each test file has its own organized directory structure
CURRENT_FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]  # Gets filename without .py extension
INPUT_DIR = os.path.join(TEST_DIR, "Input", CURRENT_FILE_NAME)        # Input files directory
REFERENCE_DIR = os.path.join(TEST_DIR, "Reference", CURRENT_FILE_NAME) # Reference files directory
LOG_DIR = os.path.join(TEST_DIR, "Logs")                              # Log files directory

# API Configuration
API_URL = "http://**************:8034/IndianInvTally/process_doc"  # Main API endpoint for document processing
LICENSE_FILE = os.path.join(TEST_DIR, "license.lic")               # License file for API authentication

# Logging Configuration
LOG_FILE = f"{CURRENT_FILE_NAME}.log"  # Dynamic log file name based on test file

# Input File Configuration
INPUT_XLS_FILENAME = "po_register_10-06_PO_REGISTER1764.xls"  # Standard filename for PO XLS files


def get_dynamic_paths(test_method_name):
    """
    Generate dynamic file paths based on BRELEARN flag and test method name.

    This function implements the dynamic folder structure that organizes test data
    based on the current test file and method being executed.

    Directory Structure Created:
        BRELEARN = True:  Test/Input/{current_file_name}/{test_method_name}/
        BRELEARN = False: Test/Reference/{current_file_name}/{test_method_name}/

    Args:
        test_method_name (str): Name of the test method being executed
                               (e.g., "test_AbhinavPO")

    Returns:
        tuple: A tuple containing:
            - data_dir (str): Full path to the test data directory
            - input_file_path (str): Full path to the input XLS file

    Example:
        For test method "test_AbhinavPO" with BRELEARN=False:
        Returns: ("Test/Reference/test_IndianInvRouteAbhinavPO/test_AbhinavPO/",
                 "Test/Reference/test_IndianInvRouteAbhinavPO/test_AbhinavPO/po_register_10-06_PO_REGISTER1764.xls")
    """
    # Determine target directory based on BRELEARN flag
    if BRELEARN:
        # Learning mode: Save new outputs to Input directory
        data_dir = os.path.join(INPUT_DIR, test_method_name)
    else:
        # Testing mode: Use Reference directory for comparison
        data_dir = os.path.join(REFERENCE_DIR, test_method_name)

    # Create directory if it doesn't exist (ensures clean test environment)
    os.makedirs(data_dir, exist_ok=True)

    # Construct full path to input file
    input_xls_path = os.path.join(data_dir, INPUT_XLS_FILENAME)
    return data_dir, input_xls_path


# Initialize logger for test execution tracking
os.makedirs(LOG_DIR, exist_ok=True)  # Ensure log directory exists
test_logger = TestLogger(LOG_DIR, LOG_FILE)
test_logger.MSWriteLog("info", "Test logger initialized")


class TestIndianInvRouteAbhinavPO(unittest.IsolatedAsyncioTestCase):
    """
    Test class for Abhinav PO (Purchase Order) document processing.

    This class contains test methods that validate the Indian Invoice Processing API
    for PO documents. It inherits from IsolatedAsyncioTestCase to support
    asynchronous test execution.

    Test Methods:
        - test_AbhinavPO: Main test method for PO document processing

    Setup/Teardown:
        - asyncSetUp: Initializes test environment and creates necessary directories
        - asyncTearDown: Cleans up after test execution

    Utility Methods:
        - is_server_listening: Checks if the API server is available
    """
    async def asyncSetUp(self):
        """
        Asynchronous setup method executed before each test method.

        This method prepares the test environment by:
        1. Creating dynamic directory structure based on BRELEARN flag
        2. Setting up logging for the current test method
        3. Ensuring all necessary directories exist

        The dynamic path structure ensures that each test method has its own
        isolated directory for input files and outputs.

        Raises:
            Exception: If directory creation or logger setup fails
        """
        try:
            # Get dynamic paths based on BRELEARN flag and current test method
            # This creates either Input or Reference directory structure
            self.strCurTestCaseDirPath, self.input_file_path = get_dynamic_paths(self._testMethodName)

            # Initialize logger for this test instance
            self.logger = test_logger
            test_logger.MSWriteLog("info", f"Setup completed for: {self._testMethodName}")
            test_logger.MSWriteLog("info", f"Using directory: {self.strCurTestCaseDirPath}")
            test_logger.MSWriteLog("info", f"Input file path: {self.input_file_path}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            raise

    async def asyncTearDown(self):
        """
        Asynchronous teardown method executed after each test method.

        This method performs cleanup operations after test execution:
        1. Logs completion status
        2. Handles any cleanup errors gracefully

        Note: Currently minimal cleanup is needed as the test creates
        files that serve as either reference data or test outputs.

        Raises:
            Exception: If teardown operations fail
        """
        try:
            test_logger.MSWriteLog("info", f"Teardown completed for: {self.strCurTestCaseDirPath}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Teardown failed: {str(e)}")
            raise

    def is_server_listening(self, host="**************", port=8034, timeout=2):
        """
        Check if the API server is available and listening on the specified port.

        This utility method verifies server connectivity before attempting
        to make API calls, helping to provide clear error messages when
        the server is unavailable.

        Args:
            host (str): Server hostname or IP address (default: "**************")
            port (int): Server port number (default: 8034)
            timeout (int): Connection timeout in seconds (default: 2)

        Returns:
            bool: True if server is listening and accepting connections,
                  False if server is unreachable or not responding

        Example:
            if self.is_server_listening():
                # Proceed with API call
                response = await client.post(API_URL, ...)
            else:
                self.fail("API server is not available")
        """
        try:
            # Attempt to establish a socket connection to the server
            with socket.create_connection((host, port), timeout=timeout):
                test_logger.MSWriteLog("info", f"Server at {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            test_logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            return False

    async def test_AbhinavPO(self):
        """
        Main test method for Abhinav PO (Purchase Order) document processing.

        This test validates the complete workflow of processing PO documents:

        Test Flow:
        1. License Verification: Validates API license and extracts user token
        2. File Preparation: Sets up input XLS file and output paths
        3. Server Connectivity: Checks if API server is available
        4. Document Upload: Sends XLS file to API endpoint with metadata
        5. Response Processing: Receives and validates ZIP response
        6. Content Extraction: Extracts XML and CSV from ZIP file
        7. File Validation: Compares outputs with reference files (BRELEARN=False)
                           or saves as new reference files (BRELEARN=True)

        API Endpoint: POST /IndianInvTally/process_doc
        User Type: Premium user (iUserid=11)
        Input Format: XLS file containing PO data
        Output Format: ZIP file containing XML and CSV files

        Expected Outputs:
        - {doc_name}.xml: Structured XML representation of PO data
        - Report_{doc_name}.csv: Tabular CSV report of processed data

        Raises:
            AssertionError: If API response is invalid or file comparison fails
            Exception: If license verification, file operations, or API calls fail
        """
        test_logger.MSWriteLog("info", f"Executing test: {self._testMethodName}")
        try:
            # STEP 1: LICENSE VERIFICATION
            test_logger.MSWriteLog("info", "Step 1: Verifying license file")

            # Check if license file exists
            if not os.path.exists(LICENSE_FILE):
                test_logger.MSWriteLog("error", f"License file not found: {LICENSE_FILE}")
                self.fail(f"License file not found: {LICENSE_FILE}")

            # Extract license data and user token
            license_data = CLicenseHelper.MSVerifyLicense(LICENSE_FILE)
            strUserToken = license_data["Token"]
            user_id = license_data.get("uid", "unknown")
            test_logger.MSWriteLog("info", f"License verified, token: {strUserToken[:10]}..., uid: {user_id}")

            # STEP 2: FILE PATH SETUP
            test_logger.MSWriteLog("info", "Step 2: Setting up file paths")

            # Define document name and file paths
            doc_name = "EBRM-124"  # Standardized document name
            strInputXlsPath = self.input_file_path  # Dynamic input file path
            strInputXmlPath = os.path.join(self.strCurTestCaseDirPath, f"{doc_name}.xml")
            strInputCsvPath = os.path.join(self.strCurTestCaseDirPath, f"Report_{doc_name}.csv")
            output_dir = self.strCurTestCaseDirPath  # Dynamic output directory
            strZipPath = os.path.join(output_dir, f"{doc_name}_Content.zip")

            test_logger.MSWriteLog("info", f"Input XLS: {strInputXlsPath}")
            test_logger.MSWriteLog("info", f"Output directory: {output_dir}")
            strOutputXmlPath = os.path.join(output_dir, f"{doc_name}.xml")
            strOutputCsvPath = os.path.join(output_dir, f"Report_{doc_name}.csv")

            # STEP 3: INPUT FILE VERIFICATION
            test_logger.MSWriteLog("info", "Step 3: Verifying input files")

            # Check if input XLS file exists
            if not os.path.exists(strInputXlsPath):
                test_logger.MSWriteLog("error", f"Input XLS file not found: {strInputXlsPath}")
                self.fail(f"Input XLS file not found: {strInputXlsPath}")
            test_logger.MSWriteLog("info", f"Input XLS file verified: {strInputXlsPath}")

            # Verify reference XML and CSV files only in testing mode (BRELEARN=False)
            if not BRELEARN:
                test_logger.MSWriteLog("info", "Testing mode: Verifying reference files")
                for file_path in [strInputXmlPath, strInputCsvPath]:
                    if not os.path.exists(file_path):
                        test_logger.MSWriteLog("error", f"Reference file not found: {file_path}")
                        self.fail(f"Reference file not found: {file_path}")
                test_logger.MSWriteLog("info", "All reference files verified")
            else:
                test_logger.MSWriteLog("info", "Learning mode: Skipping reference file verification")

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)
            test_logger.MSWriteLog("info", f"Output directory prepared: {output_dir}")

            # STEP 4: SERVER CONNECTIVITY CHECK
            test_logger.MSWriteLog("info", "Step 4: Checking server connectivity")
            if not self.is_server_listening():
                test_logger.MSWriteLog("error", f"Server at {API_URL} is not available")
                self.fail(f"Server at {API_URL} is not available")
            test_logger.MSWriteLog("info", "Server connectivity verified")

            # STEP 5: API REQUEST PREPARATION
            test_logger.MSWriteLog("info", "Step 5: Preparing API request")

            # Set up authentication headers
            headers = {"Authorization": f"Bearer {strUserToken}", "iUserid": str(user_id)}
            test_logger.MSWriteLog("info", f"Headers prepared with user ID: {user_id}")

            # Calculate file checksum for integrity verification
            checksum = calculate_checksum(strInputXlsPath, algorithm="md5")
            test_logger.MSWriteLog("info", f"File checksum calculated: {checksum[:10]}...")

            # Prepare document metadata for API
            lsClientDocMetaData = [{
                "filename": INPUT_XLS_FILENAME,
                "Type": ".xls",
                "location": strInputXlsPath,
                "checksum": checksum
            }]

            # Configure API parameters
            params = {
                "bTestMode": True,                                    # Enable test mode
                "strVoucherType": "PURCHASE_ORDER",                  # Document type
                "lsClientDocMetaData": json.dumps(lsClientDocMetaData), # Document metadata
                "bIsMultivendorDoc": False,                          # Single vendor document
                "strSystemName": os.getlogin(),                     # Current user
                "strSerializeUserConfig": json.dumps({              # System configuration
                    "Exe_version": "2.4",
                    "Exe_ReleaseDate": "2025-06-28",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [
                        "http://122.170.3.105:8024/",
                        "http://**************:8034/",
                        "http://122.170.3.105:8034/",
                        "http://**************:8024/"
                    ]
                })
            }
            data = {"checksums": checksum}
            test_logger.MSWriteLog("info", "API request parameters prepared")

            # STEP 6: API CALL EXECUTION
            test_logger.MSWriteLog("info", "Step 6: Executing API call")
            test_logger.MSWriteLog("info", f"Sending POST request to {API_URL}")

            try:
                # Execute API call with timeout for large file processing
                with httpx.Client(timeout=1200) as client:
                    response = client.post(
                        API_URL,
                        headers=headers,
                        params=params,
                        files=[("documents", (INPUT_XLS_FILENAME, open(strInputXlsPath, "rb"), "application/vnd.ms-excel"))],
                        data=data
                    )
                test_logger.MSWriteLog("info", f"API call completed with status: {response.status_code}")
            except Exception as e:
                test_logger.MSWriteLog("error", f"API request failed: {str(e)}")
                self.fail(f"API request failed: {str(e)}")

            # STEP 7: RESPONSE VALIDATION
            test_logger.MSWriteLog("info", "Step 7: Validating API response")

            # Check HTTP status code
            if response.status_code != 200:
                test_logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response.text}")
                self.fail(f"Expected status code 200, got {response.status_code}")
            test_logger.MSWriteLog("info", "HTTP status code validation passed")

            # Verify content type is ZIP
            self.assertEqual(response.headers["Content-Type"], "application/zip",
                           f"Expected Content-Type application/zip, got {response.headers['Content-Type']}")
            test_logger.MSWriteLog("info", "Content-Type validation passed")

            # STEP 8: ZIP FILE PROCESSING
            test_logger.MSWriteLog("info", "Step 8: Processing ZIP response")

            # Extract and verify checksum from response headers
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                test_logger.MSWriteLog("error", "Checksum not received in response headers")
                self.fail("Checksum not received in response headers")
            test_logger.MSWriteLog("info", f"Response checksum received: {response_checksum[:10]}...")

            # Save ZIP file to disk
            with open(strZipPath, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)
            test_logger.MSWriteLog("info", f"ZIP file saved to: {strZipPath}")

            # Verify file integrity using checksum
            saved_checksum = calculate_checksum(strZipPath, algorithm="md5")
            self.assertEqual(saved_checksum, response_checksum, "Checksum mismatch! The downloaded file is corrupted")
            test_logger.MSWriteLog("info", "ZIP file integrity verified")

            # STEP 9: CONTENT EXTRACTION
            test_logger.MSWriteLog("info", "Step 9: Extracting ZIP contents")

            # Extract XML and CSV files from ZIP
            xml_path, csv_path = extract_zip(strZipPath, output_dir, "", "")

            # Verify extracted files exist
            self.assertTrue(os.path.exists(xml_path), f"XML file not found at: {xml_path}")
            test_logger.MSWriteLog("info", f"XML extracted to: {xml_path}")
            self.assertTrue(os.path.exists(csv_path), f"CSV file not found at: {csv_path}")
            test_logger.MSWriteLog("info", f"CSV extracted to: {csv_path}")

            # STEP 10: FILE STANDARDIZATION
            test_logger.MSWriteLog("info", "Step 10: Standardizing file names")

            # Rename extracted XML to standard naming convention
            if os.path.basename(xml_path) != f"{doc_name}.xml":
                os.rename(xml_path, strOutputXmlPath)
                xml_path = strOutputXmlPath
                test_logger.MSWriteLog("info", f"Renamed XML to: {xml_path}")

            # Rename extracted CSV to standard naming convention
            if os.path.basename(csv_path) != f"Report_{doc_name}.csv":
                os.rename(csv_path, strOutputCsvPath)
                csv_path = strOutputCsvPath
                test_logger.MSWriteLog("info", f"Renamed CSV to: {csv_path}")

            # STEP 11: CONTENT VALIDATION
            if not BRELEARN:
                test_logger.MSWriteLog("info", "Step 11: Validating content against reference files")

                # Compare XML content with reference file
                test_logger.MSWriteLog("info", f"Comparing XML: {strInputXmlPath} vs {xml_path}")
                bAreSameXml, strLogMessageXml = compare_xml(self.logger, strInputXmlPath, xml_path, ignore_tags=[])
                if not bAreSameXml:
                    test_logger.MSWriteLog("error", f"XML content mismatch: {strLogMessageXml}")
                    self.fail(f"XML content mismatch: {strLogMessageXml}")
                test_logger.MSWriteLog("info", "XML content validation passed")

                # Compare CSV content with reference file
                test_logger.MSWriteLog("info", f"Comparing CSV: {strInputCsvPath} vs {csv_path}")
                bAreSameCsv, strLogMessageCsv = compare_csv(self.logger, strInputCsvPath, csv_path, ignore_columns=["Received Date"])
                if not bAreSameCsv:
                    test_logger.MSWriteLog("error", f"CSV content mismatch: {strLogMessageCsv}")
                    self.fail(f"CSV content mismatch: {strLogMessageCsv}")
                test_logger.MSWriteLog("info", "CSV content validation passed")

                test_logger.MSWriteLog("info", "All content validation checks passed")
            else:
                test_logger.MSWriteLog("info", "Step 11: Learning mode - Saving outputs as reference files")
                test_logger.MSWriteLog("info", f"XML saved as reference: {xml_path}")
                test_logger.MSWriteLog("info", f"CSV saved as reference: {csv_path}")

            test_logger.MSWriteLog("info", f"Test {self._testMethodName} completed successfully")

        except Exception as e:
            test_logger.MSWriteLog("error", f"Test failed: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        test_logger.MSWriteLog("info", "Starting unittest execution")
        asyncio.run(unittest.main(verbosity=2))
        test_logger.MSWriteLog("info", "Unittest execution completed")
    except Exception as e:
        test_logger.MSWriteLog("error", f"Top-level error: {str(e)}")
        raise