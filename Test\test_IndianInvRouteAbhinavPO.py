import unittest
import os
import asyncio
import socket
import json
import httpx
from Test.CustomHelper import TestLogger, CLicenseHelper, calculate_checksum, compare_xml, compare_csv, extract_zip

# Global configuration variables
BRELEARN = False
TEST_DIR = "Test"
INPUT_DIR = os.path.join(TEST_DIR, "Input", "test_IndianInvRouteAbhinavPO")
REFERENCE_DIR = os.path.join(TEST_DIR, "Reference", "test_IndianInvRouteAbhinavPO")
LOG_DIR = os.path.join(TEST_DIR, "Logs")
API_URL = "http://**************:8034/IndianInvTally/process_doc"
LICENSE_FILE = os.path.join(TEST_DIR, "license.lic")
LOG_FILE = "test_IndianInvRouteAbhinavPO.log"

# Initialize logger
os.makedirs(LOG_DIR, exist_ok=True)
test_logger = TestLogger(LOG_DIR, LOG_FILE)
test_logger.MSWriteLog("info", "Test logger initialized")

class TestIndianInvRouteAbhinavPO(unittest.IsolatedAsyncioTestCase):
    async def asyncSetUp(self):
        """
        Set up the test case by creating directories and initializing logging.
        """
        try:
            self.strCurTestCaseDirPath = os.path.join(INPUT_DIR, self._testMethodName)
            os.makedirs(self.strCurTestCaseDirPath, exist_ok=True)
            os.makedirs(os.path.join(REFERENCE_DIR, self._testMethodName), exist_ok=True)
            self.logger = test_logger
            test_logger.MSWriteLog("info", f"Setup completed for: {self._testMethodName}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            raise

    async def asyncTearDown(self):
        """
        Clean up after the test case.
        """
        try:
            test_logger.MSWriteLog("info", f"Teardown completed for: {self.strCurTestCaseDirPath}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Teardown failed: {str(e)}")
            raise

    def is_server_listening(self, host="**************", port=8034, timeout=2):
        """
        Check if the server is listening on the specified host and port.
        Args:
            host (str): Server host.
            port (int): Server port.
            timeout (int): Timeout for connection attempt.
        Returns:
            bool: True if server is listening, False otherwise.
        """
        try:
            with socket.create_connection((host, port), timeout=timeout):
                test_logger.MSWriteLog("info", f"Server at {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            test_logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            return False

    async def test_AbhinavPO(self):
        """
        Test the /process_doc endpoint for a premium user (iUserid=11).
        Sends an XLS file, receives a ZIP, extracts all contents, renames XML and CSV to standard names,
        and saves to input directory (BRELEARN=True) or reference directory with XML and CSV comparison (BRELEARN=False).
        """
        test_logger.MSWriteLog("info", f"Executing test: {self._testMethodName}")
        try:
            # Verify license
            if not os.path.exists(LICENSE_FILE):
                test_logger.MSWriteLog("error", f"License file not found: {LICENSE_FILE}")
                self.fail(f"License file not found: {LICENSE_FILE}")
            license_data = CLicenseHelper.MSVerifyLicense(LICENSE_FILE)
            strUserToken = license_data["Token"]
            user_id = license_data.get("uid", "unknown")
            test_logger.MSWriteLog("info", f"License verified, token: {strUserToken[:10]}..., uid: {user_id}")

            # Define file paths
            doc_name = "EBRM-124"
            xls_filename = "po_register_10-06_PO_REGISTER1764.xls"
            strInputXlsPath = os.path.join(self.strCurTestCaseDirPath, xls_filename)
            strInputXmlPath = os.path.join(self.strCurTestCaseDirPath, f"{doc_name}.xml")
            strInputCsvPath = os.path.join(self.strCurTestCaseDirPath, f"Report_{doc_name}.csv")
            output_dir = self.strCurTestCaseDirPath if BRELEARN else os.path.join(REFERENCE_DIR, self._testMethodName)
            strZipPath = os.path.join(output_dir, f"{doc_name}_Content.zip")
            strOutputXmlPath = os.path.join(output_dir, f"{doc_name}.xml")
            strOutputCsvPath = os.path.join(output_dir, f"Report_{doc_name}.csv")

            # Verify input XLS file
            if not os.path.exists(strInputXlsPath):
                test_logger.MSWriteLog("error", f"Input XLS file not found: {strInputXlsPath}")
                self.fail(f"Input XLS file not found: {strInputXlsPath}")

            # Verify input XML and CSV files only if BRELEARN=False
            if not BRELEARN:
                for file_path in [strInputXmlPath, strInputCsvPath]:
                    if not os.path.exists(file_path):
                        test_logger.MSWriteLog("error", f"Input file not found: {file_path}")
                        self.fail(f"Input file not found: {file_path}")

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Check server availability
            if not self.is_server_listening():
                test_logger.MSWriteLog("error", f"Server at {API_URL} is not available")
                self.fail(f"Server at {API_URL} is not available")

            # Prepare API request
            headers = {"Authorization": f"Bearer {strUserToken}", "iUserid": str(user_id)}
            checksum = calculate_checksum(strInputXlsPath, algorithm="md5")
            lsClientDocMetaData = [{
                "filename": xls_filename,
                "Type": ".xls",
                "location": strInputXlsPath,
                "checksum": checksum
            }]
            params = {
                "bTestMode": True,
                "strVoucherType": "PURCHASE_ORDER",
                "lsClientDocMetaData": json.dumps(lsClientDocMetaData),
                "bIsMultivendorDoc": False,
                "strSystemName": os.getlogin(),
                "strSerializeUserConfig": json.dumps({
                    "Exe_version": "2.4",
                    "Exe_ReleaseDate": "2025-06-28",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [
                        "http://122.170.3.105:8024/",
                        "http://**************:8034/",
                        "http://122.170.3.105:8034/",
                        "http://**************:8024/"
                    ]
                })
            }
            data = {"checksums": checksum}

            # Send POST request
            test_logger.MSWriteLog("info", f"Sending POST request to {API_URL}")
            try:
                with httpx.Client(timeout=1200) as client:
                    response = client.post(
                        API_URL,
                        headers=headers,
                        params=params,
                        files=[("documents", (xls_filename, open(strInputXlsPath, "rb"), "application/vnd.ms-excel"))],
                        data=data
                    )
            except Exception as e:
                test_logger.MSWriteLog("error", f"API request failed: {str(e)}")
                self.fail(f"API request failed: {str(e)}")

            # Validate response
            if response.status_code != 200:
                test_logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response.text}")
                self.fail(f"Expected status code 200, got {response.status_code}")
            self.assertEqual(response.headers["Content-Type"], "application/zip", f"Expected Content-Type application/zip, got {response.headers['Content-Type']}")

            # Verify and save ZIP file
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                test_logger.MSWriteLog("error", "Checksum not received in response headers")
                self.fail("Checksum not received in response headers")
            with open(strZipPath, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)
            saved_checksum = calculate_checksum(strZipPath, algorithm="md5")
            self.assertEqual(saved_checksum, response_checksum, "Checksum mismatch! The downloaded file is corrupted")
            test_logger.MSWriteLog("info", f"ZIP file saved to: {strZipPath}")

            # Extract all contents from ZIP
            xml_path, csv_path = extract_zip(strZipPath, output_dir, "", "")
            self.assertTrue(os.path.exists(xml_path), f"XML file not found at: {xml_path}")
            test_logger.MSWriteLog("info", f"XML extracted to: {xml_path}")
            self.assertTrue(os.path.exists(csv_path), f"CSV file not found at: {csv_path}")
            test_logger.MSWriteLog("info", f"CSV extracted to: {csv_path}")

            # Rename extracted XML and CSV to standard names
            if os.path.basename(xml_path) != f"{doc_name}.xml":
                os.rename(xml_path, strOutputXmlPath)
                xml_path = strOutputXmlPath
                test_logger.MSWriteLog("info", f"Renamed XML to: {xml_path}")
            if os.path.basename(csv_path) != f"Report_{doc_name}.csv":
                os.rename(csv_path, strOutputCsvPath)
                csv_path = strOutputCsvPath
                test_logger.MSWriteLog("info", f"Renamed CSV to: {csv_path}")

            # Compare files if not in relearn mode
            if not BRELEARN:
                test_logger.MSWriteLog("info", f"Comparing XML: {strInputXmlPath} vs {xml_path}")
                bAreSameXml, strLogMessageXml = compare_xml(self.logger, strInputXmlPath, xml_path, ignore_tags=[])
                if not bAreSameXml:
                    test_logger.MSWriteLog("error", f"XML content mismatch: {strLogMessageXml}")
                    self.fail(f"XML content mismatch: {strLogMessageXml}")
                test_logger.MSWriteLog("info", "XML content matched")

                test_logger.MSWriteLog("info", f"Comparing CSV: {strInputCsvPath} vs {csv_path}")
                bAreSameCsv, strLogMessageCsv = compare_csv(self.logger, strInputCsvPath, csv_path, ignore_columns=["Received Date"])
                if not bAreSameCsv:
                    test_logger.MSWriteLog("error", f"CSV content mismatch: {strLogMessageCsv}")
                    self.fail(f"CSV content mismatch: {strLogMessageCsv}")
                test_logger.MSWriteLog("info", "CSV content matched")

        except Exception as e:
            test_logger.MSWriteLog("error", f"Test failed: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        test_logger.MSWriteLog("info", "Starting unittest execution")
        asyncio.run(unittest.main(verbosity=2))
        test_logger.MSWriteLog("info", "Unittest execution completed")
    except Exception as e:
        test_logger.MSWriteLog("error", f"Top-level error: {str(e)}")
        raise