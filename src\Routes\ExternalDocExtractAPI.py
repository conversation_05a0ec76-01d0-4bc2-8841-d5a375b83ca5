
from src.Controllers.GPTResponse_controller import CGPTResponseData, DocumentProcessorUsingHTTPX
from fastapi import APIRouter, Depends, status, Query, HTTPException, Form
#Importing libraries
from typing import List
from src.middleware.checkAuth import user_required, get_user_token
from src.middleware.checkDocument import DocIdRequiredForExtraction
from src.middleware.checkRateLimit import rate_limiter
from src.utilities.date_helper import CDateHelper
# Declaring router
ExternalExtractDocAPI = APIRouter(tags=['DocExtractionAPI'],  prefix="/doc_exc_api")

from dotenv import load_dotenv
import os
from datetime import datetime
import asyncio
from src.Controllers.GPTResponse_controller import DocumentUploader
import traceback
from src.Controllers.CustomLogger import CLogger
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.TallyIndianInvoiceController import CTallyIndianInvoiceController
from typing import List, Any
from fastapi import APIRouter, Query, Body, HTTPException
from src.Schemas.Doc_Schema import FileSchema
from fastapi import UploadFile, File

CLogger.MCSetupLogging()

# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)

# Read the value of EXTERNAL_API_SERVER
EXTERNAL_API_SERVER = os.getenv('EXTERNAL_API_SERVER')

@ExternalExtractDocAPI.post("/extract-document-id")
async def ProcessFileById(  DocId: int = Query(..., description="The ID of the document to retrieve"),
                            iUserid: int = Query(..., description="The ID of the user performing the extraction"),
                            isTrialPaidDocExtraction: bool = Query(False, description="Trial Paid Doc Extraction Service per user"),
                            strClientREQID :str = Query(..., description="Client Request ID"),
                            strVoucherType: str = Query("Unknown", description="Voucher Type"),
                            bIsDeveloperMode: bool = Query(False, description="Developer Mode")
                            ):
    try:
        return await CGPTResponseData.performDocumentExtractionFromDocumentID(userid=iUserid, 
                                                                            document_id=DocId, 
                                                                            isTrialPaidDocExtraction=isTrialPaidDocExtraction,strClientREQID=strClientREQID, strVoucherType=strVoucherType, bIsDeveloperMode = bIsDeveloperMode)
    except Exception as e:
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")

        
@ExternalExtractDocAPI.post("/ProcessDocuments")
async def process_documents_route(
    doc_ids: List[int] = Query(..., description="List of document IDs to be processed"),
    user_id: int = Query(..., description="Id of user performing the extraction operation"),
    max_tasks: int = Query(50, description="Maximum concurrent tasks"),
    isTrialPaidDocExtraction: bool = Query(False, description="Trial Paid Doc Extraction Service per user"),
    api_base_url: str = Query(EXTERNAL_API_SERVER, description="Backend Service base URL"),

    token = None
):
    """
    Endpoint to process multiple documents based on document IDs.
    Accepts a list of document IDs, a model name, a user ID, and optionally the maximum number of concurrent tasks.
    
    - doc_ids: List of integers representing document IDs to be processed
    - strModelName: String representing the model name, default is 'Invoice'
    - user_id: Integer representing the user ID from the user session
    - max_tasks: Integer representing the maximum number of concurrent tasks allowed
    - isTrialPaidDocExtraction: Boolean indicating if the Trial Paid Doc Extraction Service is being used
    - token: String representing the user's authorization token
    """
    try:
        # Update the ModifiedDateTime for the given document IDs
        await CDateHelper.update_modified_time(doc_ids)
        # Initialize the document processor with the specified maximum concurrent tasks
        processor = DocumentProcessorUsingHTTPX(max_concurrent_tasks=max_tasks, api_base_url=api_base_url)
        result = await processor.process_documents(doc_ids, user_id, isTrialPaidDocExtraction, token)
        
        # Check if the result contains an HTTPException with a specific detail message
        # Check if the result is a list
        if isinstance(result, list):
            for item in result:
                # Check if any item in the result list is an HTTPException with a specific detail message
                if isinstance(item, HTTPException):
                    raise HTTPException(status_code=item.status_code, detail=str(item.detail))
        
        return result
    
    except HTTPException as e:
        raise HTTPException(status_code=e.status_code, detail=str(e.detail))
    except Exception as e:
        raise HTTPException(status_code=500, detail='Something went wrong, please try again later')



async def MDocExecutor(iUserID, dictFileData, dictTDLAttachmentData=None, strClientREQID= None, strVoucherType= None, bIsDeveloperMode=False):
    strCurrentXmlFilePath = None # Initialize to None in case it's not set
    Result = []
    try:
        # async with self.exec_semaphore:
        CLogger.MCWriteLog("info", f"Start MDocExecutor for REQID {strClientREQID} - {datetime.now()}")
        print(f"Start MDocExecutor for REQID {strClientREQID} - {datetime.now()}")
        # --- 2. Extract Document Process ---
        dictExtractAPIResponse = {}
        iDocID = dictFileData.get('DocId') # Safely get DocId

        if dictFileData.get('APIStatusCode') == 200 and iDocID:
            start_extract_time = datetime.now()
            CLogger.MCWriteLog("info", f"[{strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
            print(f"[{strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
            dictExtractAPIResponse = await CGPTResponseData.performDocumentExtractionFromDocumentID(
                userid=iUserID,
                document_id=iDocID,
                isTrialPaidDocExtraction=True,
                strClientREQID=strClientREQID,
                strVoucherType=strVoucherType,
                bIsDeveloperMode=bIsDeveloperMode
            )
            end_extract_time = datetime.now()
            extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
            CLogger.MCWriteLog("info", f"[{strClientREQID}] End Extract Document Process (DocID: {iDocID}). Time taken: {extract_time_taken:.2f} seconds.")
            print(f"[{strClientREQID}] End Extract Document Process (DocID: {iDocID}). Time taken: {extract_time_taken:.2f} seconds.")
        else:
            CLogger.MCWriteLog("warning", f"[{strClientREQID}] Document not uploaded successfully or no DocId. Skipping extraction.")
            print(f"[{strClientREQID}] Document not uploaded successfully or no DocId. Skipping extraction.")
            # Optionally return early or handle this case

        # --- 3. Call Tally API Step ---
        try:
            if dictExtractAPIResponse.get("APIStatusCode") == 200:
                start_tally_time = datetime.now()
                CLogger.MCWriteLog("info", f"[{strClientREQID}] Start Call Tally API Step (DocID: {iDocID}) - {start_tally_time}")
                print(f"[{strClientREQID}] Start Call Tally API Step (DocID: {iDocID}) - {start_tally_time}")
                objTallyIndianController = CTallyIndianInvoiceController(
                    user_id=iUserID,
                    doc_id=iDocID,
                    dictLsClientDocMetaData=dictTDLAttachmentData,
                    dictExtractedData=dictExtractAPIResponse.get("Document"), # Safely get Document
                    lsAllDocInfo=None,
                    strClientREQID=strClientREQID,
                    bTestMode=bIsDeveloperMode,
                    strVoucherType=strVoucherType
                )
                TallyResult = await objTallyIndianController.MCallTallyAPI(
                    bRaiseError=False,
                    dictLsClientDocMetaData=dictTDLAttachmentData,
                    strPriceListReport=None, # Assuming CIndianInvTallyController._mPricelistReportPath is a class attribute Disable from Using
                    ObjVoucherType=strVoucherType
                )
                
                end_tally_time = datetime.now()
                tally_time_taken = (end_tally_time - start_tally_time).total_seconds()
                CLogger.MCWriteLog("info", f"[{strClientREQID}] End Call Tally API Step (DocID: {iDocID}). Time taken: {tally_time_taken:.2f} seconds.")
                print(f"[{strClientREQID}] End Call Tally API Step (DocID: {iDocID}). Time taken: {tally_time_taken:.2f} seconds.")
                strCurrentXmlFilePath = TallyResult.get("XMLFilePath")
                if strCurrentXmlFilePath:
                    Result.append(strCurrentXmlFilePath)
            else:
                await CLogController.MSWriteLog(iUserID, "Debug", f"dictExtractAPIResponse status not 200. Skipping Tally. Response: {dictExtractAPIResponse}")

        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"[{strClientREQID}] Failed to create the XML document, Error:{e}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"[{strClientREQID}] Traceback: {str(traceback.format_exc())}")
            pass # Currently just logs and continues

        final_return_path = strCurrentXmlFilePath if strCurrentXmlFilePath else "No XML file generated"
        CLogger.MCWriteLog("info", f"[{strClientREQID}] End MDocExecutor Process. Final XML Path: {final_return_path} - {datetime.now()}")
        print(f"[{strClientREQID}] End MDocExecutor Process. Final XML Path: {final_return_path} - {datetime.now()}")
        return Result

    except Exception as e:
        # This outer catch block will catch errors from Upload, Extraction, or unexpected issues
        CLogger.MCWriteLog("critical", f"[{strClientREQID}] Error during MDocExecutor. Error: {e}")
        CLogger.MCWriteLog("critical", f"[{strClientREQID}] Traceback: {str(traceback.format_exc())}")
        print(f"[{strClientREQID}] Traceback: {str(traceback.format_exc())}")
        raise e # Re-raise the exception after logging for proper error propagation        
        
@ExternalExtractDocAPI.post("/ProcessDocTallyXML")
async def process_documents_route(
    dfOrStrAdditionalDocData: str = Form(...),
    dictTDLAttachmentDetails: str = Form(...),
    docs: List[UploadFile] = File(...),
    user_id: int = Form(...),
    strVoucherType: str = Form(...),
    strClientREQID: str = Form(...),
    bIsDeveloperMode: str = Form("false"),
    docMetadatas: str = Form(...),  # ✅ new metadata field
):
    """
    Endpoint to process multiple documents based on document IDs.
    Accepts a list of document IDs, a model name, a user ID, and optionally the maximum number of concurrent tasks.
    
    - doc_ids: List of integers representing document IDs to be processed
    - strModelName: String representing the model name, default is 'Invoice'
    - user_id: Integer representing the user ID from the user session
    - max_tasks: Integer representing the maximum number of concurrent tasks allowed
    - isTrialPaidDocExtraction: Boolean indicating if the Trial Paid Doc Extraction Service is being used
    - token: String representing the user's authorization token
    """
    try:
        bIsDeveloperMode = bIsDeveloperMode == "true" if isinstance(bIsDeveloperMode, str) else bIsDeveloperMode
        import json
        ParseddfOrStrAdditionalDocData = json.loads(dfOrStrAdditionalDocData)
        ParseddictTDLAttachmentDetails = json.loads(dictTDLAttachmentDetails)
        # Parse metadata list
        parsedDocMetadatas = json.loads(docMetadatas)

        # Safety check
        if len(parsedDocMetadatas) != len(docs):
            raise HTTPException(status_code=400, detail="Mismatch in number of files and metadata entries.")

        # Build FileSchema objects
        fileSchemas: List[FileSchema] = []
        for file, meta in zip(docs, parsedDocMetadatas):
            fileBytes = await file.read()  # Read file content into memory
            fileSchema = FileSchema(
                file=file,
                filename=file.filename,
                content_type=file.content_type,
                data=fileBytes,
                HashCode=meta.get("HashCode", ""),
                filePath=meta.get("filePath", ""),
                DocVendorName=meta.get("DocVendorName", ""),
                FileType=meta.get("FileType", "pdf"),
                strClientREQID=meta.get("strClientREQID", ""),
                PageCount=meta.get("PageCount", 0)
            )
            fileSchemas.append(fileSchema)
            
        # ------------- Threading Algo ----------------------
        tasks = []
        start_upload_time = datetime.now()
        # two separate semaphores: one for upload, one for executor
        upload_semaphore = asyncio.Semaphore(20)
        exec_semaphore   = asyncio.Semaphore(20)
        uploader = DocumentUploader(strVoucherType=strVoucherType)

        async def process_single_doc(doc):
            # 1) upload under its own semaphore
            async with upload_semaphore:
                dictFileData = await uploader.upload_documentv2(
                    user_id=user_id,
                    document=doc,
                    objAdditionalDocDetails=ParseddfOrStrAdditionalDocData,
                    BReUseDocData=True,
                    bUsePaidModel=True,
                    bAutoSelectModel=False
                )

            # 2) once we have dictFileData (and its DocId), call MDocExecutor
            async with exec_semaphore:
                return await MDocExecutor(
                    iUserID = user_id,
                    dictFileData = dictFileData,
                    dictTDLAttachmentData = ParseddictTDLAttachmentDetails,
                    strClientREQID= strClientREQID,
                    strVoucherType = strVoucherType,
                    bIsDeveloperMode= bIsDeveloperMode
                )

        # spawn one task per doc, each doing upload→execute in order
        tasks = [asyncio.create_task(process_single_doc(doc)) for doc in fileSchemas]
        allDocResponse = await asyncio.gather(*tasks, return_exceptions=True)
        end_upload_time = datetime.now()
        print("End Asyncio Process",end_upload_time)
        upload_time_taken = (end_upload_time - start_upload_time).total_seconds()
        print("Estimated Time : ", upload_time_taken)
        finalResult: List[str] = []

        for res in allDocResponse:
            if isinstance(res, list) and all(isinstance(item, str) for item in res):
                finalResult.extend(res)  # Add all XML paths
            else:
                # Optional: log the error if needed
                print("Skipped response (not valid):", res)
        return finalResult
    except HTTPException as e:
        raise HTTPException(status_code=e.status_code, detail=str(e.detail))
    except Exception as e:
        raise HTTPException(status_code=500, detail='Something went wrong, please try again later')