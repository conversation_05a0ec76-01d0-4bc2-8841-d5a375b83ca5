from fastapi import HTTPException
import json
import traceback
import asyncio
import time
import tik<PERSON>en
import openai
from openai import OpenAI
from ensure import ensure_annotations
import sys
sys.path.append(".")
from src.utilities.helperFunc import saveGPTresponse
from openai._exceptions import RateLimitError , APITimeoutError
from src.utilities.PromptBuilder import MakeModelGPTPrompt
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.Controllers.prompt_controller import CPromptController
from src.Controllers.DocumentData_controller import CDocumentData
from src.utilities.helperFunc import CExtractionHelper
from dotenv import load_dotenv
import socket
import platform
import os
from openai import AsyncOpenAI

# Load environment variables from the .env file
load_dotenv()

# Access the AVOPENAI_API_KEY environment variable
avopenai_api_key = os.getenv('AVOPENAI_API_KEY')
# Access the AVGROKAI_API_KEY environment variable
avgrok_api_key = os.getenv('AVGROKAI_API_KEY')
# Use the API key as needed in your application
print(avopenai_api_key)


class CGPTAPIResponseV2:
    # client = OpenAI(api_key = avopenai_api_key)
    client = AsyncOpenAI(api_key = avopenai_api_key)
    grok_client = AsyncOpenAI(api_key=avgrok_api_key, base_url="https://api.x.ai/v1/")
    # no of pages allow using GPT API
    iNoOfPagesAllowed = 4
    
    def __init__(self, 
                 document_id:int, 
                 user_id: int, 
                 bDebug=False, 
                 strClientName = None,
                 strVoucherType = None,
                 isDevelopmentMode = True):
    
        self.bDebug = bDebug
        self.document_id = document_id
        # Pre-initialize properties to handle later in async method
        self.strDocErrorMsg, self.strDocDebugMsg, self.DocErrorCode = "", "", None
        
        # Open AI Logging
        self.strProjectName = "AccuVelocity"
        self.strResponsibleDeveloper = "Mitul Solanki"
        self.strHostname = socket.gethostname()
        self.strPlatform = platform.platform()
        self.strIPAddress = socket.gethostbyname(self.strHostname)
        self.strUserName = os.getlogin() if hasattr(os, 'getlogin') else "unknown"
        self.strClientName = strClientName
        self.isDevelopmentMode = isDevelopmentMode
        self.strVoucherType = strVoucherType
        self.user_id = user_id
    
    async def log(self, level: str, message: str):
        """
        Helper method to log messages with the specified severity level.

        Args:
            level (str): The severity level of the log ('Info', 'Error', 'Debug').
            message (str): The message to log.
        """
        await CLogController.MSWriteLog(self.user_id, level, message)

        
    async def getGPTResponse(
        strSystemContent: str = "You are a helpful assistant designed to output JSON.",
        strUserContent: str = "Who won the world series in 2020?",
        strModel: str = "gpt-4o-2024-05-13",
        intSeed: int = 33,
        max_tokens=None,
        tempreture=0,
        dictResponseFormat: dict = {"type": "json_object"},
        dictMetadata=None,
        reasoning_effort = "low",
        bIsReasoningModel = False,
        bIsGrokAPI = False,
        **kwargs
    ):
        max_retries = 3
        retry_delay = 15  # seconds

        base_messages = [
            {"role": "system", "content": strSystemContent},
            {"role": "user", "content": strUserContent}
        ]

        # Prepare the base parameters
        params = {
            "model": strModel,
            "messages": base_messages,
            "seed": intSeed,
        }

        
        if bIsReasoningModel:
            # ON Reasoning Model
            params["reasoning_effort"] = reasoning_effort
            params["response_format"] = dictResponseFormat # ON Reasoning Model - Response Format
            if max_tokens is not None:
                params["max_completion_tokens"] = max_tokens
        else:
            # OFF Reasoning Model
            params["temperature"] = tempreture 
            params["response_format"]  = dictResponseFormat # OFF Reasoning Model - Response Format
            if max_tokens is not None: # Max Output Token
                params["max_tokens"] = max_tokens

        
        if dictMetadata is not None:
            params["metadata"] = dictMetadata

        for attempt in range(max_retries):
            try:
                if bIsGrokAPI:
                    objResponse = await CGPTAPIResponseV2.grok_client.chat.completions.create(**params)
                else:
                    objResponse = await CGPTAPIResponseV2.client.chat.completions.create(**params)
                objGPTResponse = json.loads(objResponse.model_dump_json())
                json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])

                return {"json_objects":json_objects, "response":objGPTResponse}
            except (RateLimitError, APITimeoutError) as e:
                await CLogController.MSWriteLog(None, "Error", f"Retryable error in GPTAPI.getGPTResponse: {traceback.format_exc()}")
                await asyncio.sleep(2 ** retry_delay)
            except Exception as e:
                await CLogController.MSWriteLog(None, "Error", f"Unexpected error in GPTAPI.getGPTResponse: {traceback.format_exc()}")
                break  # Unexpected errors should not be retried

        raise HTTPException(status_code=409, detail="Server is under heavy load. Please try again later.")
        
      
    async def runExtractAPI(self, **params):
        # **params :         strSystemContent: str = "You are a helpful assistant designed to output JSON.", strUserContent: str = "Who won the world series in 2020?", strModel: str = "gpt-4o-2024-05-13", intSeed: int = 33, max_tokens=None, tempreture=0, dictResponseFormat: dict = {"type": "json_object"}, dictMetadata=None, reasoning_effort = "low", bIsReasoningModel = False, bIsGrokAPI = False
        try:
            strSystemContent = params.get("strSystemContent", "You are a helpful assistant designed to output JSON.")
            strUserContent = params.get("strUserContent", "Who won the world series in 2020?")
            if not strSystemContent:
                raise HTTPException(status_code=500, detail="An unexpected error occurred. Please try again later.")

            # TODO: Commment due to Temporary Error
            # await self.log("Info", f"Prompt Used: {strSystemContent} \nExtracted Text: {strUserContent}")

            # Add required inputs
            request_kwargs = {}

            # Add optional params (like max_tokens, metadata, etc.)
            request_kwargs.update(params)

            objGPTResponse = await CGPTAPIResponseV2.getGPTResponse(**request_kwargs)
            
            #  TODO: Commment due to Temporary Error
            # await self.log("Debug", f"Prompt Used: {strSystemContent}\nContent: {objGPTResponse.get('json_objects')}\nGPTAPI Response: {objGPTResponse.get('response')}")

            # # Update usage
            # await CUserAPIUsageData.MSUpdateUserPageLimit(
            #     iUserId=self.user_id,
            #     strPlanType="pro",
            #     iPageLimit=self.iDocPageCount,
            #     strOperation="subtract"
            # )

            return objGPTResponse

        except HTTPException as e:
            raise e

        except Exception as e:
            self.strDocErrorMsg = "Server is under heavy load. Please try again later."
            self.DocErrorCode = 409
            self.strDocDebugMsg = f"Traceback: {traceback.format_exc()}"

            await CLogController.MSWriteLog(
                self.user_id, "Error", f"Error in GPTAPI.runExtractAPI: {self.strDocDebugMsg}"
            )

            await CDocumentData.MSUpdateDocumentStatus(
                iUserID=self.user_id,
                iDocId=self.document_id,
                eNewStatus="Error",
                strDocErrorMsg=self.strDocErrorMsg,
                strDocDebugMsg=self.strDocDebugMsg,
                DocExtractionAPIStatusCode=self.DocErrorCode
            )

            raise HTTPException(status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    # async def runExtractAPI(self, objPromptData, dictResponseFormat, bReasoningEnabled=False, **kwargs):
    #     try:
    #         strSystemContent= objPromptData.get("Prompt",None)

    #         if strSystemContent is None:
    #             raise HTTPException(
    #                 status_code=500, detail="An unexpected error occurred. Please try again later.")
            
    #         await self.log("Info", f"Prompt Used: {strSystemContent} \n Extracted Text:{self.docExtractedTxt}")

    #         if bReasoningEnabled:
    #             # Client Specific Parameters
    #             objGPTResponse = await CGPTAPIResponseV2.getGPTResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName, dictResponseFormat=dictResponseFormat, bReasoningEnabled= True, max_tokens = max_tokens, reasoning_effort = reasoning_effort,dictMetadata = dictMetadata, tempreture= tempreture, intSeed = intSeed)

    #         else:
    #             objGPTResponse = await CGPTAPIResponseV2.getGPTResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName, dictResponseFormat=dictResponseFormat, bReasoningEnabled= False, max_tokens = max_tokens, dictMetadata = dictMetadata, tempreture= tempreture, intSeed = intSeed)
    
    #         json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])
            
    #         await self.log("Debug", f"Prompt Used: {strSystemContent} \n Content - {json_objects} \n GPTAPI Response - {objGPTResponse}")
    #         # objGPTResponse = CGPTAPIResponseV2.MSSetPageMetaData(objResponse=objGPTResponse, page_width=page_width, page_height=page_height, bDebug=False)
    #         await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=self.user_id, strPlanType="pro", iPageLimit=self.iDocPageCount, strOperation="subtract")
    #         return {"json_objects":json_objects, "response":objGPTResponse}
    #     except HTTPException as e:
    #         raise e
    #     except Exception as e:
    #         self.strDocErrorMsg = "Server is under heavy load. Please try again later."
    #         self.DocErrorCode = 409
    #         self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
    #         await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runExtractAPI: {str(traceback.format_exc())}")
    #         await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
    #         raise HTTPException(
    #                 status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    @staticmethod
    def MSBuildOpenAIMetadata(
        strClientName="Unknown",
        strvourcherType="Unknown",
        bDevelopmentMode=True,
    ) -> dict:
        try:
            # Nisarg: Added This as Raising Exception local variable 'strHostname' referenced before assignment
            strHostname = ""
            strPlatform = ""
            strIPAddress = ""
            strUserName = ""
            # Dynamic Metadata for system info
            strHostname = strHostname or socket.gethostname()
            strPlatform = strPlatform or platform.platform()
            strIPAddress = strIPAddress or socket.gethostbyname(strHostname)
            strUserName = strUserName or (os.getlogin() if hasattr(os, 'getlogin') else "unknown")

            return {
                "Project": "AccuVelocity", # Project Name
                "ResponsibleDeveloper": "Mitul Solanki", # Developer Name
                "Hostname": strHostname,
                "ipAddress": strIPAddress,
                "Platform": strPlatform,
                "User": strUserName,
                "ClientName": strClientName,
                "VoucherType": strvourcherType,
                "DevelopmentMode": "True" if bDevelopmentMode else "False"
            }
        except Exception as e:
            print(f"ERROR: While building OpenAI Metadata: {str(traceback.format_exc())}")
            {
                "Project": "AccuVelocity", # Project Name
                "ResponsibleDeveloper": "Mitul Solanki", # Developer Name
                "Hostname": "Unknown",
                "ipAddress": "Unknown",
                "Platform": "Unknown",
                "User": "Unknown",
                "ClientName": "Unknown",
                "VoucherType": "Unknown",
                "DevelopmentMode": "True" if bDevelopmentMode else "False"
            }

    @staticmethod
    def MSSetPageMetaData(objResponse, page_width, page_height, bDebug=True):
        """
        Adds metadata for page width and height to the response object.

        Purpose:
        This function updates the 'content' field of the response object by adding 
        metadata for page dimensions (width and height).

        Input:
        - objResponse: dict
            The response object containing the 'choices' and 'message' fields.
        - page_width: int or float
            The width of the page to be added as metadata.
        - page_height: int or float
            The height of the page to be added as metadata.
        - bDebug: bool, optional
            Flag to enable debug printing, default is True.

        Output:
        - dict
            The updated response object with added metadata.

        Example:
        >>> objResponse = {
                'choices': [
                    {
                        'message': {
                            'content': '{"key": "value"}'
                        }
                    }
                ]
            }
        >>> page_width = 8.5
        >>> page_height = 11
        >>> updatedResponse = MSSetPageMetaData(objResponse, page_width, page_height)
        >>> print(updatedResponse)
        {
            'choices': [
                {
                    'message': {
                        'content': '{"key": "value", "meta_data": {"page_width": 8.5, "page_height": 11}}'
                    }
                }
            ]
        }
        """
        try:
            # Extracting the 'content' which is a string representation of a dictionary
            content_string = objResponse['choices'][0]['message']['content']
            
            # Converting the string back into a dictionary
            GPTJsonData = json.loads(content_string)
            
            # Debug printing if enabled
            if bDebug:
                print("GPTJson: ", GPTJsonData)
            
            # Adding meta_data with page dimensions
            GPTJsonData["meta_data"] = {
                "page_width": page_width,
                "page_height": page_height
            }
            
            # Updating the content with the modified dictionary
            objResponse['choices'][0]['message']['content'] = json.dumps(GPTJsonData, ensure_ascii=False)
            return objResponse
        
        except Exception as e:
            # Handling any exceptions that occur during processing
            print(f"ERROR: While reading GPTJsonFile: {objResponse}")
            raise e
    @staticmethod
    def MSSetExtractAPIConfig(strExtractAPIName, dictExtractAPIMetaData = None, **kwargs):
        defaultConfig = {}

        if strExtractAPIName == "GPT4o":
            defaultConfig["strModel"] = "gpt-4o-2024-08-06"
            defaultConfig["max_tokens"] = 16384  # MAX Token Limit = 16384
            defaultConfig["bIsGrokAPI"] = False
            defaultConfig["bIsReasoningModel"] = False
            defaultConfig["ModelNameAlias"] = "NeuraCoreV1"

        elif strExtractAPIName == "GPTo3Reasoning":
            defaultConfig["strModel"] = "o3-mini-2025-01-31"
            defaultConfig["max_tokens"] = 27000  # 100,000 max output tokens
            defaultConfig["bIsGrokAPI"] = False
            defaultConfig["bIsReasoningModel"] = True
            defaultConfig["reasoning_effort"] = "high"
            defaultConfig["ModelNameAlias"] = "NeuraThinkV3-Lite"

        elif strExtractAPIName == "GPTo4Reasoning":
            defaultConfig["strModel"] = "o4-mini-2025-04-16"
            defaultConfig["max_tokens"] = 27000 # 100,000 max output tokens
            defaultConfig["bIsGrokAPI"] = False
            defaultConfig["bIsReasoningModel"] = True
            defaultConfig["reasoning_effort"] = "high"
            defaultConfig["ModelNameAlias"] = "NeuraThinkV4-Pro"


        elif strExtractAPIName == "GrokReasoning":
            defaultConfig["strModel"] = "grok-3-mini" 
            defaultConfig["max_tokens"] = 27000
            defaultConfig["bIsGrokAPI"] = True
            defaultConfig["bIsReasoningModel"] = True
            defaultConfig["reasoning_effort"] = "high"
            defaultConfig["ModelNameAlias"] = "AstraLogicV3-Mini"

        elif strExtractAPIName == "Grok3": # use this for response with same accuracy as Grok-3-fast with lower costing then grok-3-fast  , Context Window 131,072 Tokens
            defaultConfig["strModel"] = "grok-3"
            defaultConfig["max_tokens"] = 27000
            defaultConfig["bIsGrokAPI"] = True
            defaultConfig["bIsReasoningModel"] = False
            defaultConfig["ModelNameAlias"] = "AstraCoreV3"

        elif strExtractAPIName == "Grok3Fast": # use this for fast response with same accuracy as Grok-3 , Context Window 131,072 Tokens
            defaultConfig["strModel"] = "grok-3-fast"
            defaultConfig["max_tokens"] = 27000
            defaultConfig["bIsGrokAPI"] = True
            defaultConfig["bIsReasoningModel"] = False
            defaultConfig["ModelNameAlias"] = "AstraCoreV3-Fast"

        else:
            pass
        
        if dictExtractAPIMetaData is None:
            kwargs["dictMetadata"] = CGPTAPIResponseV2.MSBuildOpenAIMetadata(
                strClientName="AV DEVELOPEMENT",
                strvourcherType="Unknown",
                bDevelopmentMode=True
            )
        return {
            "strSystemContent": kwargs.get("strSystemContent", "You are a helpful assistant designed to output JSON only. Provide concise and structured answers."),
            "strUserContent": kwargs.get("strUserContent", "Who won the world series in 2020?"),
            "strModel": kwargs.get("strModel", defaultConfig.get("strModel", "grok-3-mini")),
            "intSeed": kwargs.get("intSeed", 33),
            "dictResponseFormat": kwargs.get("dictResponseFormat", {"type": "json_object"}),
            "max_tokens": kwargs.get("max_tokens", defaultConfig.get("max_tokens", 16384)),
            "tempreture": kwargs.get("tempreture", 0),
            "reasoning_effort": kwargs.get("reasoning_effort", defaultConfig.get("reasoning_effort", "low")),
            "bIsReasoningModel": kwargs.get("bIsReasoningModel", defaultConfig.get("bIsReasoningModel", True)),
            "bIsGrokAPI": kwargs.get("bIsGrokAPI", defaultConfig.get("bIsGrokAPI", True)),
            "dictMetadata": kwargs.get("dictMetadata", defaultConfig.get("dictMetadata", {"Project": "AccuVelocity", "ResponsibleDeveloper": "Mitul Solanki"})),
            "ModelNameAlias": kwargs.get("ModelNameAlias", defaultConfig.get("ModelNameAlias", "-"))
        }

if __name__ == "__main__":
    pass
