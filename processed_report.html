
    <html>
    <head>
        <style>
            body {
                font-family: Arial, sans-serif;
                background-color: #f4f4f4;
                padding: 20px;
            }
            .container {
                max-width: 800px;
                margin: auto;
                padding: 20px;
                border: 1px solid #ddd;
                border-radius: 8px;
                background-color: white;
                box-shadow: 0px 4px 6px rgba(0, 0, 0, 0.1);
                text-align: left;
            }
            h2 {
                text-align: center;
                color: #2c3e50;
            }
            table {
                width: 100%;
                border-collapse: collapse;
                margin-top: 15px;
            }
            th, td {
                border: 1px solid #ddd;
                padding: 8px;
                text-align: center;
            }
            th {
                background-color: #3498db;
                color: white;
            }
            .footer {
                margin-top: 20px;
                font-size: 14px;
                color: #555;
                text-align: center;
            }
            .date-time {
            display: block;
            white-space: nowrap;
        }
            
        </style>
    </head>
    <body>
        <div class="container">
            <h2>Bank Statements - Tally Posting Report for Gwalia Sweets Pvt Ltd - (24-25)</h2>
            <p><strong>Date:</strong> 08/03/2025</p>
            <p>Hello, Gwalia Sweets Pvt Ltd - (24-25)</p>
            <p>Your file <strong>demo_dup2</strong> has been successfully processed. Below are the details:</p>
            <h3>Performance Summary</h3>
            <table>
                <tr>
                    <th>S.No</th>
                    <th>File Name</th>
                    <th>Start Date and Time</th>
                    <th>End Date and TIme</th>
                    <th>Total Transactions</th>
                    <th>Predicted Transactions</th>
                    <th>Suspense Transactions</th>
                    <th>AccuVelocity Comments</th>
                </tr>
                <tr>
                    <td>1</td>
                    <td>demo_dup2</td>
                    <td>
                        <span class="date-time">08/03/2025</span>
                        <span class="date-time">01:31:24 PM</span>
                    </td>
                    <td>
                        <span class="date-time">08/03/2025</span>
                        <span class="date-time">01:31:30 PM</span>
                    </td>
                    <td>111</td>
                    <td>62</td>
                    <td>49</td>
                    <td>-</td>
                </tr>
            </table>

            <h3>Table Details:</h3>
            <p><strong>Sr. No.:</strong> Serial number of each invoice.</p>
            <p><strong>File Name:</strong> Name of the processed bank statement file.</p>
            <p><strong>Start Date and Time:</strong> The starting date and time of transactions in the bank statement.</p>
            <p><strong>End Date and TIme:</strong> The ending date and time of transactions in the bank statement.</p>
            <p><strong>Total Transactions:</strong> Total number of transactions identified in the bank statement</p>
            <p><strong>Predicted Transactions:</strong> Number of transactions predicted using AI/ML models.</p>
            <p><strong>Suspense Transactions:</strong> Transactions that could not be matched to a known category (classified as suspense).</p>
            <p><strong>AccuVelocity Comments:</strong> Additional remarks or notes related to the statement processing.</p>

            <p class="footer">Regards,<br><strong>AccuVelocity Team</strong></p>

        </div>
    </body>
    </html>
    