Project Requirements:
	1. Python - v3.11.7
	2. Node.js - v20.12.1

Project Structure:

project_root/
├── client/                                  # Frontend application files and resources
│   ├── node_modules/                        # Modules required for frontend development
│   ├── public/                              # Static files like images (and svg, gif) which can be accessed gloabaly in fronted 
│   ├── src/                                 # Source code for the frontend
│   │   ├── assets/                          # Resource files which is used sepecifcally (png and svg)
│   │   ├── components/                      # Reusable components (e.g., Header, Sidebar)
│   │   ├── context/                         # Contexts for managing user state and data
│   │   ├── pages/                           # Individual page components
│   │   │   ├── Auth/                        # Authentication pages (SignUp, Login)
│   │   │   ├── AddModelPage.jsx             # Page to add new vendor models
│   │   │   ├── ForgotPassword.jsx           # Forgot password page
│   │   │   ├── History.jsx                  # User history page (displays list of uploaded and its metadata)
│   │   │   ├── HomePage.jsx                 # Welcome page of the application
│   │   │   ├── MainPage.jsx                 # Main functionality page (Document upload and processing)
│   │   │   ├── ModelListPage.jsx            # Displays list of user-created models
│   │   │   ├── NoMatch.jsx                  # Fallback page for undefined routes
│   │   │   ├── Preview.jsx                  # Specific document preview, approve , and download data in various file formats
│   │   │   ├── Profile.jsx                  # User profile page
│   │   │   ├── ResetPassword.jsx            # Password reset page
│   │   │   ├── UpdateUserPassword.jsx       # Page to update user's current password
│   │   │   └── UserListPage.jsx             # Admin page to display and add users
│   │   ├── App.css                          # Main stylesheet for the app
│   │   ├── App.jsx                          # Main app component with route definitions
│   │   └── Main.jsx                         # Entry point for the frontend application
│   ├── .env                                 # Environment variables for frontend
│   ├── .eslintrc.js                         # This code configures ESLint to enforce coding standards and identify potential issues in your JavaScript project, specifically for a React application using modern features and tools.
│   ├── package-lock.json                    # file would manage each dependencies in detail for that specific frontend application in 
│   ├── package.json                         # Project metadata and list of dependencies for frontent
│   ├── postcss.config.js                    # Defines a basic configuration for a build process that involves Tailwind CSS for utility-based styling and Autoprefixer for ensuring cross-browser compatibility of your CSS styles.
│   ├── tailwind.config.js                   # Integrating Material Tailwind components and defining custom styles and animations for your application's UI.
│   └── vite.config.js                       # Configures Vite for a React project. It includes the React plugin for necessary functionality, enables serving on the local hostname, and specifies a custom port for the development server.
├── config/                                  # Configuration files for the server
│   ├── constants.py                         # Constants used throughout the project
│   └── db_config.py                         # Database configuration for creating url with password and database name to access database(async engine, session maker)
├── docs/                                    # Documentation and guides
│   ├── README.md                            # Project overview and setup instructions
│   ├── SchemaDesign.md                      # Database schema design
│   ├── test.py                              # Temporary file for testing
│   └── UbuntuSteps.md                       # Guide for setting up the project on Ubuntu
├── resources/                               # External resources and configurations
│   ├── GPTConfig.json                       # GPTConfig json file ( contains gpt related configs for example model, tokens and other related data)
│   ├── requirements.txt                     # Python dependencies for the project
│   └── TallyTemplates.json                  # Tally system templates
├── src/                                     # Backend application files and configurations
│   ├── controllers/                         # Controllers for handling business logic
│   │   ├── auth_controller.py               # Controllers for Auth operations (for example login, signup)
│   │   ├── DocumentData_controller.py       # Controllers for Document operations (example : Get all document )
│   │   ├── GPTResponse_controller.py        # Controllers for Document processing with GPT (example : upload doc and fetch gpt result )
│   │   ├── Logs_Controller.py               # Controllers for Logging operations
│   │   ├── Role_Controller.py               # Controllers for Role-related operations
│   │   ├── Tally_Controller.py              # Controllers for Tally system operations
│   │   ├── user_controller.py               # Controllers for User operations (username availability)
│   │   └── UserAPIUsage_Controller.py       # Controllers for API usage tracking and management
│   ├── middleware/                          # Middleware for request processing
│   │   └── checkAuth.py                     # Authentication and authorization checks
│   ├── models/                              # Database models
│   │   └── models.py                        # ORM class definitions for database tables (for example : UploadDoc table, to store all information related to uploaded document ) 
│   ├── routes/                              # Route definitions connecting URLs to controllers
│   │   ├── auth.py                          # Routes for authentication
│   │   ├── DocumentData.py                  # Routes for Document-related operations
│   │   ├── GPTResponse.py                   # Routes for Document processing with GPT
│   │   ├── log_Routes.py                    # Routes for Logging operations
│   │   ├── Role_Routes.py                   # Routes for Role-related operations
│   │   ├── Tally_Routes.py                  # Routes for Tally system operations
│   │   ├── UserAPIUsage.py                  # Routes for API usage management
│   │   └── Vendor_Routes.py                 # Routes for Vendor operations
│   ├── schemas/                             # Schemas for data validation and serialization
│   │   ├── auth_models.py                   # Schemas for auth operations
│   │   ├── base_response_model.py           # Schemas for Base response model for error handling
│   │   ├── logs_model.py                    # Schemas for logging operations
│   │   ├── Role_Schema.py                   # Schemas for role operations
│   │   ├── schemas.py                       # General operation schemas
│   │   ├── Tally_Schemas.py                 # Schemas for tally operations
│   │   ├── token_model.py                   # Schemas for token operations
│   │   └── Vendor_Schema.py                 # Schemas for vendor operations
│   ├── utilities/                           # Utility functions for common operations
│   ├── GPTAPI.py                            # Interface for calling GPT and processing data
│   └── main.py                              # Entry point for the FastAPI application
├── .gitignore                               # Specifies intentionally untracked files to ignore
├── create_env.bat                           # Batch script for setting up environment on Windows
├── createUbuntuEnv.sh                       # Shell script for setting up environment on Ubuntu
└── set_env_variable.sh                      # Script to set environment variables

How to setup mysql :
	1. Download and install MySql workbench
	2. set password for root account 
	3. Create a database name DBVelocity by running this query : create database DBVelocity;
	4. Update your root password in .env or config/db_config.py file 
	5. Then simply run the backend and all the required tables will be created in backend
	
How to activate Backend env:
	1. Run create_env.bat file
	2. cd 5_Env (use bash)
	3. cd Scripts
	4. source activate

Download Postman:
	1. Link to download: https://www.postman.com/downloads/
	
How to start Frontend :
	1. cd client	#paste this in terminal 	#Changes dir to client
	2. npm i 		#paste this in terminal		#installs necessary javascrpt modules			
	3. npm run dev	#paste this in terminal 	#runs frontend on localhost

How to start Backend :
	1. uvicorn src.main:app --host 0.0.0.0 --reload		#directly paste this in terminal
