import { Auth } from "./pages/Authentication";
import UploadPage from "./pages/Upload";
import { ForgotPassword } from "./pages/Authentication";
import { ResetPassword } from "./pages/Authentication";
import PreviewPage from "./pages/PreviewDocument";
import MyDocumentPage from "./pages/MyDocuments";
import ProfilePage from "./pages/UserProfile";
import { UpdatePassword } from "./pages/Admin";
import { UserListPage } from "./pages/Admin";
import { ModelListPage } from "./pages/MyModels";
import { ModelAddPage } from "./pages/MyModels";
import { CompanyDetails } from "./pages/Authentication";
import { PlanPurchaseSuccessfull  } from "./pages/Payment";
import { PlanPurchaseFailed } from "./pages/Payment";
import { AddOnPages } from "./pages/Payment";
import { MaintenancePage } from "./pages/Common";
import UpgradePlanPage from "./pages/UpgradePlans";
import ActivityLog from "./pages/ActivityLogs";
import userDataState from "./context/userData";
import { useRecoilValue } from "recoil";
import { NoMatch } from "./pages/Common";
import { Route } from 'react-router-dom';
import { WelcomePage } from './pages/Authentication'

const Routes = (showAlert, isSidebarOpenLarge) => {
    const userData = useRecoilValue(userDataState);

    if (import.meta.env.VITE_IS_UNDER_MAINTENANCE) {
        return <Route path="*" element={<MaintenancePage />} />;
    }

    return userData.isLoggedIn ? (
        [
            {
                path: "/",
                element: <UploadPage />,
            },
            {
                path: "/profile",
                element: <ProfilePage />,
            },
            {
                path: "/preview",
                element: <PreviewPage showAlert={showAlert} isOpen={isSidebarOpenLarge} />,
            },
            {
                path: "/history",
                element: <MyDocumentPage showAlert={showAlert} isOpen={isSidebarOpenLarge} />,
            },
            {
                path: "/AllUserList",
                element: <UserListPage />,
            },
            {
                path: "/UpdateUserPassword",
                element: <UpdatePassword />,
            },
            {
                path: "/ModelListPage",
                element: <ModelListPage />,
            },
            {
                path: "/Logs",
                element: <ActivityLog showAlert={showAlert} />,
            },
            {
                path: "/AddModelPage",
                element: <ModelAddPage isOpen={isSidebarOpenLarge} />,
            },
            {
                path: "/UpgradePlan",
                element: <UpgradePlanPage />,
            },
            {
                path: "/PlanPurchaseFailed",
                element: <PlanPurchaseFailed />,
            },
            {
                path: "/PlanPurchaseSuccessfull",
                element: <PlanPurchaseSuccessfull  />,
            },
            {
                path: "/AddOnPages",
                element: <AddOnPages showAlert={showAlert} isOpen={isSidebarOpenLarge} />,
            },
            {
                path: "*",
                element: <NoMatch />,
            },
        ]
    ) : (
        [
            {
                path: "/",
                element: <Auth />,
            },
            {
                path: "/recover-password",
                element: <ForgotPassword />,
            },
            {
                path: "/recovery-password/:token",
                element: <ResetPassword />,
            },
            {
                path: "/OrganizationDetails",
                element: <CompanyDetails />,
            },
            {
                path: "/WelcomePage",
                element: <WelcomePage />,
            },
            {
                path: "*",
                element: <NoMatch />,
            },
        ]
    );
};

export default Routes;