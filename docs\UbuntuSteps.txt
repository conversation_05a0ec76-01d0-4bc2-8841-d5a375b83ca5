To download Python 3.11 version on Ubuntu follow below steps:
1. sudo add-apt-repository ppa:deadsnakes/ppa
2. sudo apt update
3. sudo apt install python3.11
4. sudo apt install software-properties-common
5. python3.11 --version
6. sudo update-alternatives --install /usr/bin/python python /usr/bin/python3.11 1
7. python --version
8. sudo apt install python3.11-dev python3.11-venv python3.11-distutils python3.11-gdbm python3.11-tk python3.11-lib2to3


To install mysql in ubuntu follow below steps:
1. sudo apt update
2. sudo apt install pkg-config
3. sudo apt install libmysqlclient-dev
4. sudo apt update
5. sudo apt install build-essential python3-dev
6. pip install mysqlclient

To install node js 
1. sudo snap install node --classic

to setup sql follow steps:
1. sudo mysql -u root -p
2. CREATE USER 'root'@'localhost' IDENTIFIED BY 'Real$321';
GRANT ALL PRIVILEGES ON *.* TO 'root'@'localhost' WITH GRANT OPTION;
3. CREATE DATABASE DBVelocity;
4.FLUSH PRIVILEGES;
5. Exit;

to create new user riveredge for our application:
1. sudo mysql -u root -p
2. CREATE USER 'riveredge'@'localhost' IDENTIFIED BY 'Real$321';
GRANT ALL PRIVILEGES ON *.* TO 'riveredge'@'localhost' WITH GRANT OPTION;
3. FLUSH PRIVILEGES;
4. Exit;


To setup Desktop GUI & XRDP connection
sudo apt-get update
sudo apt install xrdp 
sudo systemctl enable xrdp
sudo add-apt-repository ppa:gnome3-team/gnome3
sudo apt-get install gnome-shell ubuntu-gnome-desktop
sudo passwd ubuntu

To open firefox with keyboard running
sudo firefox --no-remote


Install supervisor
sudo apt-get install supervisor
sudo systemctl start supervisor
sudo systemctl enable supervisor
sudo systemctl status supervisor

Install nginx
sudo apt-get install nginx -y

Install cerbot
sudo apt install certbot python3-certbot-nginx -y


create captcha site key for your registered domain (if not available)
    visit this url https://www.google.com/u/1/recaptcha/admin/create
    & <NAME_EMAIL>

    1. Register a new site
    2. https://your_domain.com
    3. reCAPTCHA type :- Challenge (v2)
    4. Domains :- app.h.accuvelocity.com (your subdomain or domain name)

Setting Environment Variable: Alternatively, you can set the OPENAI_API_KEY environment variable to your API key. This can be done in your terminal or command prompt before running your script or application:

export OPENAI_API_KEY=your_api_key


To install vs code in VPS
sudo snap install code --classic




sudo add-apt-repository ppa:oguzhaninan/stacer
sudo apt-get update
sudo apt-get install stacer
sudo apt-get install mailutils

#!/bin/bash

# Set the CPU threshold
CPU_THRESHOLD=60

# Get the current CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')

# Get the current date and time
CURRENT_DATE=$(date +"%Y-%m-%d %H:%M:%S")

# Check if the CPU usage is greater than the threshold
if (( $(echo "$CPU_USAGE > $CPU_THRESHOLD" | bc -l) )); then
    # Send an email notification
    EMAIL_MESSAGE="CPU usage is above ${CPU_THRESHOLD}%. Current usage: ${CPU_USAGE}%"
    echo "$EMAIL_MESSAGE" | mail -s "CPU Usage Alert" <EMAIL>,<EMAIL>,<EMAIL>
    echo "$CURRENT_DATE - $EMAIL_MESSAGE" >> /var/log/cpu_monitor.log
else
    # Log the current CPU usage without sending an email
    echo "$CURRENT_DATE - CPU usage is below threshold. Current usage: ${CPU_USAGE}%" >> /var/log/cpu_monitor.log
fi

sudo touch /var/log/cpu_monitor.log
sudo chmod 666 /var/log/cpu_monitor.log

crontab -e

*/5 * * * * /path/to/cpu_monitor.sh


sudo apt-get install gnome-software

# For installing the wkhtml for converting the html to pdf in case of doc, excel and csv files
sudo dpkg -i resource/wkhtmltox_0.12.6.1-2.jammy_amd64.deb
sudo apt-get install -f
