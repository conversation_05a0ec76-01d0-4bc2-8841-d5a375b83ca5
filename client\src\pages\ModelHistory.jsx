import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import axios from 'axios';
import { useParams } from 'react-router-dom';
import { BsFillEyeFill, BsDownload } from "react-icons/bs";
import { useNavigate } from 'react-router-dom';
import { RiDownloadLine } from "react-icons/ri";
import { IoTimeOutline } from "react-icons/io5";
import toast, { Toaster } from "react-hot-toast";
import * as ExcelJS from 'exceljs';
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { Button } from "@material-tailwind/react";
import loading from '/animations/loading.svg';
import ModelComponent from '../components/TrialUserBox'; // Import the ModelComponent

export default function ModelHistory() {
    const [activePage, setActivePage] = useState(1);
    const [totalPages, setTotalPages] = useState(0)
    const [historyData, setHistoryData] = useState([]);
    const { modelName } = useParams();
    const [selectedDocs, setSelectedDocs] = useState({});
    const [selectAll, setSelectAll] = useState(false);    // State to track whether all documents are selected
    const navigate = useNavigate();
    const [showDownloadOptions, setShowDownloadOptions] = useState(false);
    const [processingStatus, setProcessingStatus] = useState({});
    const itemsPerPage = 10;
    const [columnNames, setColumnNames] = useState([]);
    const [isLoading, setIsLoading] = useState(true)

    useEffect(() => {
        fetchHistoryData(activePage);
        setSelectedDocs({});
        setSelectAll(false);
    }, [activePage, modelName]); // Depend on modelName to refetch when it changes

    // Calculate the correct index based on current page
    const calculateIndex = (index) => {
        return (activePage - 1) * itemsPerPage + index + 1;
    };

    const toggleDownloadOptions = () => {
        setShowDownloadOptions(!showDownloadOptions);
    };

    const prevPage = () => {
        setActivePage(activePage - 1);
        updateSelectAllState();
    };

    const nextPage = () => {
        setActivePage(activePage + 1);
        updateSelectAllState();
    };

    const fetchHistoryData = async (activePage) => {
        const url = `${import.meta.env.VITE_SERVER}/ModelTable/Processed-Docs/?strModelName=${encodeURIComponent(modelName)}&iLimit=${itemsPerPage}&iPage=${activePage}`;

        try {

            const response = await axios.get(url, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            setHistoryData(response.data.documents); // Assuming the API returns the history data directly

            setColumnNames(getColumnNames(response.data.documents));
            setTotalPages(response.data.pagination.total_pages)
            setSelectAll(false);
            setIsLoading(false)
        } catch (error) {
            setIsLoading(false)
            console.error('Error fetching history data:', error);
            // Optionally, handle the error state here, e.g., set an error message state and display it
        }
    };

    // Function to extract column names from the first item in the data array
    const getColumnNames = (data) => {
        // Start with fixed column names that are outside 'Processed_Data'
        const baseColumns = new Set(["DocumentName", "DocumentStatus", "IsDocApprovedStatus"]);

        // Iterate over each item in the data array
        data?.forEach(item => {

            if (item.Processed_Data && Object.keys(item.Processed_Data).length > 0) {

                // Extract keys from 'Processed_Data' for the current item
                Object.keys(item.Processed_Data).forEach(key => {

                    // Add the key to the set if it's not 'LineItemTable'
                    if (key !== "LineItemTable") {
                        baseColumns.add(key);
                    }
                });
            }

        });

        // Convert the set back to an array and return
        return Array.from(baseColumns);
    };

    const processDocument = async (docIds) => {


        for (const docId of docIds) {
            setProcessingStatus(prevState => ({ ...prevState, [docId]: 'processing' }));
        }


        try {

            // Generate the doc_ids query parameter part
            const docIdsQueryParam = docIds.map(id => `doc_ids=${id}`).join('&');

            const isTrialPaidDocExtraction = JSON.parse(localStorage.getItem("isTrialPaidDocExtraction")); // Retrieve the value from local storage

            const response = await axios.post(`${import.meta.env.EXTERNAL_API_SERVER}/ProcessDocuments?isTrialPaidDocExtraction=${isTrialPaidDocExtraction}&${docIdsQueryParam}`, null, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                for (const docId of docIds) {
                    setProcessingStatus(prevState => ({ ...prevState, [docId]: 'Processed' }));
                }
            }


        } catch (error) {

            if (error.response) {
                // The request was made and the server responded with a status code
                // that falls out of the range of 2xx
                console.error("Error data:", error.response.data);
                console.error("Error status:", error.response.status);

                // Check for status code 500 and specific error message
                if (error.response.status === 500 && error.response.data.detail === "An error occurred: 208: This Doc Is Already Processed") {
                    // Handle the specific error, for example:
                    toast.error("This document has already been processed");
                }
            } else if (error.request) {
                // The request was made but no response was received
                console.error("Error request:", error.request);
            } else {
                // Something happened in setting up the request that triggered an Error
                console.error('Error', error.message);
            }

            for (const docId of docIds) {
                setProcessingStatus(prevState => ({ ...prevState, [docId]: 'Failed' }));
            }
            // setProcessingStatus(prevState => ({ ...prevState, [docId]: 'Failed' }));
        }
    };

    const handleExtractDocuments = async () => {

        // Capture the IDs of documents that were selected at the time the function was called
        const initialSelectedDocIds = Object.entries(selectedDocs)
            .filter(([_, isSelected]) => isSelected)
            .map(([docId, _]) => parseInt(docId, 10));

        if (initialSelectedDocIds.length > 0) {
            // Set all selected documents to 'processing' before starting the loop
            setProcessingStatus(prevState => {
                return initialSelectedDocIds.reduce((acc, docId) => {
                    acc[docId] = 'processing';
                    return acc;
                }, { ...prevState });
            });

            await processDocument(initialSelectedDocIds);

        }
        else {
            // Handle case where no data could be fetched
            toast.error('No document is selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
    };


    // Helper function to check if all documents are selected on the current page
    const checkIfAllSelectedOnPage = (selected, docs) => {
        return docs.every(doc => selected[doc.DocId]);
    };

    const toggleCheckbox = (docId) => {
        setSelectedDocs(prevSelectedDocs => {
            const newSelectedDocs = { ...prevSelectedDocs, [docId]: !prevSelectedDocs[docId] };

            // Update the 'selectAll' state based on the new selection state
            const allSelected = checkIfAllSelectedOnPage(newSelectedDocs, historyData);
            setSelectAll(allSelected);

            return newSelectedDocs;
        });
    };

    const toggleAllCheckboxes = (isChecked) => {
        // Toggle selection for all documents on the current page
        const newSelectedDocs = { ...selectedDocs };
        historyData.forEach(doc => {
            newSelectedDocs[doc.DocId] = isChecked;
        });

        setSelectedDocs(newSelectedDocs);
        setSelectAll(isChecked);
    };

    // Function to update the 'selectAll' state when changing pages
    const updateSelectAllState = () => {
        const allSelected = checkIfAllSelectedOnPage(selectedDocs, historyData);
        setSelectAll(allSelected);
    };

    const filterSuccessfulDocuments = (historyData, selectedDocs) => {
        // Create a list of successful document IDs based on history data and selected docs
        return Object.entries(selectedDocs)
            .filter(([docId, isSelected]) => isSelected && historyData.some(doc => doc.DocId === parseInt(docId) && doc.DocumentStatus === "ToBeApproved"))
            .map(([docId, _]) => parseInt(docId, 10));
    };

    // For Downloading Response Data
    const handleDownloadExcelForSelected = async () => {
        // Get list of successful document IDs
        const successfulDocIds = filterSuccessfulDocuments(historyData, selectedDocs);

        const documentsData = await Promise.all(successfulDocIds.map(async (docId) => {
            try {
                const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/${docId}`, {
                    headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` },
                });

                return response.data.DocExtractedData.Document;
            } catch (error) {
                console.error(`Failed to fetch data for document ${docId}:`, error);
                return null; // Return null for errors to filter out later
            }
        })).then(data => data.filter(item => item !== null)); // Filter out null values

        if (documentsData.length > 0) {
            const excelBlob = await jsonToExcel(documentsData);
            const date = new Date();
            const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const formattedTime = date.toTimeString().split(' ')[0].replace(/:/g, '-').substring(0, 5); // HH-MM
            const fileName = `Data_${formattedDate}-${formattedTime}.xlsx`;
            downloadBlob(excelBlob, fileName);
        } else {
            // Handle case where no data could be fetched
            toast.error('No Processed document is selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
        setShowDownloadOptions(false)
    };

    const jsonToExcel = async (data) => {
        const workbook = new ExcelJS.Workbook(); // Create a new workbook
        const worksheet = workbook.addWorksheet("Documents Data"); // Create a new worksheet

        if (!Array.isArray(data) || data.length === 0) {
            return new Blob(); // Return empty Blob if data is empty or not an array
        }

        const parentKeys = Object.keys(data[0]).filter((key) => key !== 'LineItemTable'); // Parent keys excluding 'LineItemTable'
        let lineItemKeys = [];

        // Determine unique line item keys
        data.forEach((doc) => {
            if (doc.LineItemTable && Array.isArray(doc.LineItemTable) && doc.LineItemTable.length > 0) {
                doc.LineItemTable.forEach((item) => {
                    Object.keys(item).forEach((key) => {
                        if (!lineItemKeys.includes(key)) {
                            lineItemKeys.push(key); // Collect unique keys
                        }
                    });
                });
            }
        });

        // Combine parent and line item headers
        const headers = [...parentKeys, ...lineItemKeys]; // Combine all headers
        worksheet.addRow(headers); // Add headers to the worksheet

        // Add data rows for each document
        data.forEach((doc) => {
            if (doc.LineItemTable && Array.isArray(doc.LineItemTable) && doc.LineItemTable.length > 0) {
                // Process each line item
                doc.LineItemTable.forEach((lineItem) => {
                    const row = [
                        ...parentKeys.map((key) => doc[key] || ''), // Add parent data
                        ...lineItemKeys.map((key) => lineItem[key] || '') // Add line item data
                    ];
                    worksheet.addRow(row); // Add the row to the worksheet
                });
            } else {
                // If there's no LineItemTable, process the document as a whole
                const row = headers.map((header) => doc[header] || ''); // Add row with parent data
                worksheet.addRow(row); // Add row to the worksheet
            }
        });

        // Convert workbook to buffer and return as a Blob
        const buffer = await workbook.xlsx.writeBuffer();
        return new Blob([buffer], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' });
    };



    const downloadCsvForSelected = async () => {

        const successfulDocIds = filterSuccessfulDocuments(historyData, selectedDocs);

        const documentsData = await Promise.all(successfulDocIds.map(async (docId) => {
            try {
                const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/${docId}`, {
                    headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` },
                });
                return response.data.DocExtractedData.Document;
            } catch (error) {
                console.error(`Failed to fetch data for document ${docId}:`, error);
                return null; // Return null for errors to filter out later
            }
        })).then(data => data.filter(item => item !== null)); // Filter out null values

        if (documentsData.length > 0) {
            const csvBlob = await jsonToCsv(documentsData);
            const date = new Date();
            const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const formattedTime = date.toTimeString().split(' ')[0].replace(/:/g, '-').substring(0, 5); // HH-MM
            const fileName = `Data_${formattedDate}-${formattedTime}.csv`;
            downloadBlob(csvBlob, fileName);
        } else {
            // Handle case where no data could be fetched
            toast.error('No Processed document is selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
        setShowDownloadOptions(false)
    };
    const jsonToCsv = async (documentsData) => {
        if (!Array.isArray(documentsData)) return new Blob([], { type: 'text/csv' }); // Ensure documentsData is an array

        const csvRows = [];
        let parentHeaders = [];
        let lineItemHeaders = [];

        // Determine unique parent and line item headers
        documentsData.forEach((data) => {
            const parentKeys = Object.keys(data).filter((key) => key !== 'LineItemTable'); // Exclude LineItemTable

            // Ensure unique parent headers
            parentKeys.forEach((key) => {
                if (!parentHeaders.includes(key)) {
                    parentHeaders.push(key);
                }
            });

            if (data.LineItemTable && Array.isArray(data.LineItemTable) && data.LineItemTable.length > 0) {
                // Get unique line item headers
                data.LineItemTable.forEach((item) => {
                    Object.keys(item).forEach((key) => {
                        if (!lineItemHeaders.includes(key)) {
                            lineItemHeaders.push(key);
                        }
                    });
                });
            }
        });

        // Combine parent and line item headers
        const headers = [...parentHeaders, ...lineItemHeaders]; // All unique headers
        csvRows.push(headers.map((header) => `"${header}"`).join(',')); // Add headers to CSV

        // Add data rows to CSV
        documentsData.forEach((data) => {
            if (data.LineItemTable && Array.isArray(data.LineItemTable) && data.LineItemTable.length > 0) {
                // LineItemTable is present
                data.LineItemTable.forEach((lineItem) => {
                    const row = headers.map((header) => {
                        const value = lineItem[header] || data[header] || ''; // Check line item or parent data
                        return `"${value.toString().replace(/"/g, '""')}"`; // Handle quotes in CSV
                    });
                    csvRows.push(row.join(',')); // Add row to CSV
                });
            } else {
                // No LineItemTable, just parent data
                const row = headers.map((header) => {
                    const value = data[header] || ''; // Check parent data
                    return `"${value.toString().replace(/"/g, '""')}"`; // Handle quotes in CSV
                });
                csvRows.push(row.join(',')); // Add single row
            }
        });

        // Return the CSV as a Blob
        return new Blob([csvRows.join('\n')], { type: 'text/csv' });
    };


    const downloadTextForSelected = async () => {
        const successfulDocIds = filterSuccessfulDocuments(historyData, selectedDocs);

        const documentsData = await Promise.all(successfulDocIds.map(async (docId) => {
            try {
                const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/${docId}`, {
                    headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` },
                });
                // Assuming 'UploadedDoc' and 'GPTProcessData' are at the same response level
                return {
                    ...response.data.DocExtractedData.Document, // Spread the invoice data
                    DocName: response.data.UploadedDoc["DocName"] // Include the document name
                };
            } catch (error) {
                console.error(`Failed to fetch data for document ${docId}:`, error);
                return null; // Return null for errors to filter out later
            }
        })).then(data => data.filter(item => item !== null)); // Filter out null values

        if (documentsData.length > 0) {
            const textBlob = JsonToTextFiles(documentsData);
            const date = new Date();
            const formattedDate = date.toISOString().split('T')[0]; // YYYY-MM-DD
            const formattedTime = date.toTimeString().split(' ')[0].replace(/:/g, '-').substring(0, 5); // HH-MM
            const fileName = `Data_${formattedDate}-${formattedTime}.txt`;
            downloadBlob(textBlob, fileName, 'text/plain');
        } else {
            // Handle case where no data could be fetched
            toast.error('No Processed document is selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });

        }
        setShowDownloadOptions(false)
    };

    const JsonToTextFiles = (documentsData, lineItemsKey = 'LineItemTable') => {
        let content = '';

        documentsData.forEach((invoiceDetails) => {
            // Use the document name instead of the index
            content += `${'-'.repeat(20)} ${invoiceDetails.DocName} ${'-'.repeat(20)}\n\n`;

            // Writing invoice details
            for (const [key, value] of Object.entries(invoiceDetails)) {
                if (key === lineItemsKey || key === "DocName") {
                    continue; // Skip line items and the document name for detail writing
                }
                content += `${key}: ${value}\n`;
            }

            // Check if "LineItemTable" is a key and has content
            if (invoiceDetails.hasOwnProperty(lineItemsKey) && Array.isArray(invoiceDetails[lineItemsKey])) {
                content += "\nLineItemTable\n";
                invoiceDetails[lineItemsKey].forEach((item, index) => {
                    content += `\nRow ${index + 1}\n`;
                    for (const [key, value] of Object.entries(item)) {
                        content += `${key}: ${value}\n`;
                    }
                });
            }

            content += "\n\n"; // Adding some space between documents
        });

        return new Blob([content], { type: 'text/plain' });
    };

    const downloadBlob = (blob, filename) => {
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = filename;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);
    };

    return (
        <>
            <Toaster position="top-center" />
            <div className="flex px-6 py-2 pt-4">
                <div className="flex-grow">
                    <h1 className="text-sm font-semibold text-[#707EAE] hover:underline">
                        <Link to="/ModelListPage">
                            Models / {modelName}
                        </Link>
                    </h1>
                    <h1 className="text-3xl font-semibold text-[#3F3F3F]">{modelName}</h1>
                </div>
                {/* Place the ModelComponent to the right */}
                <ModelComponent />
            </div>

            <div className="p-6 bg-[#ffff] m-7 rounded-xl shadow-lg  style={{ minHeight: 'calc(100vh - 200px)' }}"> {/* Adjusted for dynamic height */}
                <div className="flex justify-end items-center mb-6">

                    <div className="flex-shrink-0 flex items-center space-x-3">
                        <button className="bg-[#003654] hover:bg-[#002744] text-white mx-3 py-2 px-4 rounded-xl cursor-pointer flex items-center" onClick={handleExtractDocuments}>
                            <RiDownloadLine className="mr-2" />Extract Document
                        </button>
                        <div className="relative inline-block">
                            <button className="bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 rounded-xl cursor-pointer flex items-center" onClick={toggleDownloadOptions}>
                                <BsDownload className="mr-2" />Download Data
                            </button>
                            {showDownloadOptions && (
                                <div className="absolute left-0 mt-1 w-48 bg-[#ffff] shadow-lg rounded-lg z-50">
                                    <ul className="py-1">
                                        <li className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" onClick={handleDownloadExcelForSelected}>Download Excel</li>
                                        <li className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" onClick={downloadCsvForSelected}>Download CSV</li>
                                        <li className="block px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left" onClick={downloadTextForSelected}>Download Text</li>
                                    </ul>
                                </div>
                            )}
                        </div>
                    </div>
                </div>

                {isLoading ? (
                    <div className="flex justify-center">
                        <img src={loading} className='h-[20vh]' alt="No data available" />
                    </div>
                ) : (
                    historyData?.length === 0 ? (

                        <div className="flex justify-center">
                            <img src={`/animations/noData.png`} className='h-[50vh]' alt="No data available" />
                        </div>

                    ) : (
                        <>

                            <div className="overflow-x-auto relative m-6" style={{ minHeight: '55vh', maxHeight: '55vh' }}>
                                <table className="min-w-full leading-normal">
                                    <thead>
                                        <tr className="align-middle text-center text-gray-700">
                                            <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">
                                                <input type="checkbox"
                                                    checked={selectAll}
                                                    onChange={(e) => toggleAllCheckboxes(e.target.checked)}
                                                />
                                            </th>
                                            <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Sr No</th>
                                            {columnNames.map((columnName) => (
                                                <th key={columnName} className="px-5 py-3 border-b-2 border-gray-200 font-medium">
                                                    {columnName}
                                                </th>
                                            ))}
                                            <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Preview</th>
                                        </tr>
                                    </thead>

                                    <tbody>
                                        {historyData?.map((doc, index) => (
                                            <tr key={doc.DocId} className={`align-middle text-center odd:bg-gray-50 even:bg-[#ffff] hover:bg-gray-200`}>
                                                <td className="px-5 py-3 border-b border-gray-200">
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedDocs[doc.DocId] || false}
                                                        onChange={() => toggleCheckbox(doc.DocId)}
                                                    />
                                                </td>
                                                <td className="px-5 py-3 border-b border-gray-200">{calculateIndex(index)}</td>
                                                {columnNames.map(columnName => (

                                                    <td key={columnName} className="px-5 py-3 border-b border-gray-200" >
                                                        {/* Check for DocumentStatus and render accordingly */}
                                                        {columnName === 'DocumentStatus' ? (
                                                            <span className={`flex items-center ${doc.DocumentStatus === 'ToBeApproved' || processingStatus[doc.DocId] === 'ToBeApproved' ? 'text-[#4AA785]' : 'text-[#CD0000]'}`}>
                                                                {processingStatus[doc.DocId] === 'processing' ? (
                                                                    <IoTimeOutline className="animate-spin h-5 w-5 text-gray-500 mr-2" />
                                                                ) : (
                                                                    <>
                                                                        <svg className={`fill-current ${doc.DocumentStatus === 'ToBeApproved' || processingStatus[doc.DocId] === 'ToBeApproved' ? 'text-[#4AA785]' : 'text-[#CD0000]'} mr-2`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20">
                                                                            <circle cx="10" cy="10" r="7" />
                                                                        </svg>
                                                                        {doc.DocumentStatus === 'ToBeApproved' || processingStatus[doc.DocId] === 'ToBeApproved' ? 'Completed' : 'Not Processed'}
                                                                    </>
                                                                )}
                                                            </span>
                                                        ) : columnName === 'IsDocApprovedStatus' ? (
                                                            // Check for IsDocApprovedStatus and render Approved or Not Approved
                                                            <span className={`flex items-center ${doc.IsDocApprovedStatus === 0 ? 'text-[#59A8D4]' : 'text-[#FFC555]'}`}>
                                                                <svg className={`fill-current ${doc.IsDocApprovedStatus === 0 ? 'text-[#59A8D4]' : 'text-[#FFC555]'} mr-2`} xmlns="http://www.w3.org/2000/svg" viewBox="0 0 20 20" width="20" height="20">
                                                                    <circle cx="10" cy="10" r="7" />
                                                                </svg>
                                                                {doc.IsDocApprovedStatus === 0 ? 'Pending' : 'Approved'}
                                                            </span>
                                                        ) : (
                                                            // Default case for other columns
                                                            columnName in doc ? doc[columnName] : doc.Processed_Data[columnName] || 'N/A'
                                                        )}
                                                    </td>


                                                ))}
                                                {/* Preview Button */}
                                                <td className="px-5 py-3 border-b border-gray-200">
                                                    {/* // ! Need to Add below state when navigating to preview page for showing model name hyperlink */}
                                                    {/* modelName: doc.ModelName, modelFamilyName: doc.ModelFamilyName, modelId : doc.ModelId */}
                                                    <button onClick={() => navigate('/preview', { state: { docId: doc.DocId, route: "model-history/" + modelName } })} className="bg-[#BCBCBC] hover:bg-[#afafaf] text-white font-bold py-2 px-4 rounded cursor-pointer flex items-center justify-center">
                                                        <BsFillEyeFill className="mr-2" />Preview
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>

                            {/* Pagination buttons */}
                            <div className="flex justify-end items-center mt-6" style={{ position: 'sticky', bottom: '20px', backgroundColor: '#fff', borderTop: '1px solid #e5e7eb', padding: '10px 0', zIndex: 10 }}>
                                <Button
                                    variant="text"
                                    onClick={prevPage}
                                    disabled={activePage === 1}
                                >
                                    <IoIosArrowBack strokeWidth={2} className="h-4 w-4" />
                                </Button>
                                {/* Displaying pagination - dynamically generated based on total pages */}
                                {Array.from({ length: totalPages }, (_, index) => (
                                    <button
                                        key={index}
                                        className={`px-3 py-1 ${activePage === index + 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md`}
                                        onClick={() => setActivePage(index + 1)}
                                    >
                                        {index + 1}
                                    </button>
                                ))}
                                <Button
                                    variant="text"
                                    onClick={nextPage}
                                    disabled={activePage === totalPages}
                                >
                                    <IoIosArrowForward strokeWidth={2} className="h-4 w-4" />
                                </Button>
                            </div>
                        </>
                    ))}
            </div>

        </>
    );
}
