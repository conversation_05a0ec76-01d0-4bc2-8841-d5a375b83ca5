{"CompanyData": {"ComapanyName": "PARAG TRADERS (24-25)", "GstRegistrationType": "Regular", "GstIn": "23AAKFV4306N1ZX", "StateName": "Madhya Pradesh", "CountryName": "India"}, "bisGSTApplied": false, "VendorName": "ROLLIN LOGISTICS", "VendorDetails": {"Address": ["MORBI (GJ)"], "GstRegistrationType": "Regular", "GSTIN": "24GLMPP8896L1ZU", "PinCode": null, "State": "Gujarat", "Country": "India"}, "ConsigneeDetails": {"Address": ["(A UNIT OF VHD DISTRIBUTORS LLP)", "12/4,RACE COURSE ROAD,INDORE", "PH:0731-2535659/2535660", "TIN-***********", "E-Mail : <EMAIL>"], "GSTIN": "23AAKFV4306N1ZX", "MailingName": "PARAG TRADERS", "PinCode": "452003", "State": "Madhya Pradesh", "Country": "India"}, "CostCenterName": null, "VoucherTypeName": "EXPENSES A/C", "VCHEntryMode": "Accounting Invoice", "RoundOffLedger": {"result": "Fail", "comment": "Manual checking required", "LedgerName": "Rounding Off (PURCHASE)"}, "GstConfigData": {"GSTNAME": [{"TaxName": "CGST", "TaxRate": "Not Specified", "TallyLedgerName": "Input CGST"}, {"TaxName": "CGST", "TaxRate": "14.0", "TallyLedgerName": "Input CGST 14%"}, {"TaxName": "CGST", "TaxRate": "1.5", "TallyLedgerName": "Input CGST 1.5%"}, {"TaxName": "CGST", "TaxRate": "2.5", "TallyLedgerName": "Input CGST 2.5%"}, {"TaxName": "CGST", "TaxRate": "6.0", "TallyLedgerName": "Input CGST 6%"}, {"TaxName": "CGST", "TaxRate": "9.0", "TallyLedgerName": "Input CGST 9%"}, {"TaxName": "IGST", "TaxRate": "Not Specified", "TallyLedgerName": "Input IGST"}, {"TaxName": "IGST", "TaxRate": "0.1", "TallyLedgerName": "Input IGST 0.1%"}, {"TaxName": "IGST", "TaxRate": "12.0", "TallyLedgerName": "Input IGST 12%"}, {"TaxName": "IGST", "TaxRate": "18.0", "TallyLedgerName": "Input IGST 18%"}, {"TaxName": "IGST", "TaxRate": "5.0", "TallyLedgerName": "Input IGST 5%"}, {"TaxName": "SGST", "TaxRate": "Not Specified", "TallyLedgerName": "Input SGST"}, {"TaxName": "SGST", "TaxRate": "14.0", "TallyLedgerName": "Input SGST 14%"}, {"TaxName": "SGST", "TaxRate": "1.5", "TallyLedgerName": "Input SGST 1.5%"}, {"TaxName": "SGST", "TaxRate": "2.5", "TallyLedgerName": "Input SGST 2.5%"}, {"TaxName": "SGST", "TaxRate": "6.0", "TallyLedgerName": "Input SGST 6%"}, {"TaxName": "SGST", "TaxRate": "9.0", "TallyLedgerName": "Input SGST 9%"}]}}