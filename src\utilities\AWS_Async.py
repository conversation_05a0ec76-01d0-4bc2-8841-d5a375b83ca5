import asyncio
import aioboto3
import aiofiles
import json
import sys
import logging
from botocore.exceptions import ClientError
import uuid
import os

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[logging.StreamHandler(sys.stdout)]
)

async def upload_file_to_s3(s3_client, file_path, bucket_name, object_name=None):
    """
    Uploads a file to an S3 bucket.
    """
    if object_name is None:
        object_name = os.path.basename(file_path)
    try:
        async with aiofiles.open(file_path, 'rb') as f:
            data = await f.read()
        await s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
        logging.info(f"Uploaded {file_path} to s3://{bucket_name}/{object_name}")
        return object_name
    except ClientError as e:
        logging.error(f"Failed to upload file to S3: {e}")
        sys.exit(1)
    except Exception as e:
        logging.error(f"Unexpected error uploading file to S3: {e}")
        sys.exit(1)

async def start_document_analysis(textract_client, bucket_name, object_name, job_tag="job", notification_channel=None):
    """
    Starts asynchronous document text detection with Textract.
    """
    try:
        params = {
            'DocumentLocation': {
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': object_name
                }
            },
            'JobTag': job_tag
        }
        if notification_channel:
            params['NotificationChannel'] = notification_channel

        response = await textract_client.start_document_text_detection(**params)
        job_id = response['JobId']
        logging.info(f"Started document analysis job with Job ID: {job_id}")
        return job_id
    except ClientError as e:
        logging.error(f"Error starting document analysis: {e}")
        sys.exit(1)

async def get_document_analysis(textract_client, job_id):
    """
    Polls and retrieves the results of an asynchronous document text detection job.
    """
    try:
        while True:
            response = await textract_client.get_document_text_detection(JobId=job_id)
            status = response['JobStatus']
            logging.info(f"Job status: {status}")

            if status in ['SUCCEEDED', 'FAILED']:
                if status == 'SUCCEEDED':
                    logging.info("Document analysis succeeded.")
                    blocks = response.get('Blocks', [])
                    # Handle pagination if NextToken is present
                    next_token = response.get('NextToken', None)
                    while next_token:
                        logging.info("Retrieving next page of results...")
                        response = await textract_client.get_document_text_detection(JobId=job_id, NextToken=next_token)
                        blocks.extend(response.get('Blocks', []))
                        next_token = response.get('NextToken', None)
                    return blocks
                else:
                    logging.error("Document analysis failed.")
                    sys.exit(1)
            await asyncio.sleep(5)
    except ClientError as e:
        logging.error(f"Error getting document analysis: {e}")
        sys.exit(1)

def get_raw_text(blocks):
    """
    Extracts raw text from Textract blocks.
    Groups text by page number and returns a dictionary where the keys are page numbers
    and the values are lists of text lines.
    """
    # Map LINE blocks to their page numbers by checking the PAGE blocks' relationships.
    id_to_page = {}
    page_number = 1
    for block in blocks:
        if block['BlockType'] == 'PAGE':
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        for child_id in relationship['Ids']:
                            id_to_page[child_id] = page_number
            page_number += 1

    pages = {}
    for block in blocks:
        if block['BlockType'] == 'LINE':
            text = block.get('Text', '')
            # If the page is not mapped, default to page 1
            page = id_to_page.get(block['Id'], 1)
            pages.setdefault(page, []).append(text)
    return pages

async def process_document(local_pdf_path, bucket_name, object_name=None, job_tag="ExtractTextJob", notification_channel=None):
    """
    Main function to process the PDF and output raw text.
    """
    session = aioboto3.Session()
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]
    file_dir = os.path.dirname(os.path.abspath(local_pdf_path))
    output_folder = os.path.join(file_dir, base_filename)
    try:
        os.makedirs(output_folder, exist_ok=True)
        logging.info(f"Output folder created at: {output_folder}")
    except Exception as e:
        logging.error(f"Error creating output folder: {e}")
        sys.exit(1)

    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Upload the PDF file to S3
        if not object_name:
            object_name = f"textract-input/{uuid.uuid4()}-{os.path.basename(local_pdf_path)}"
        s3_key = await upload_file_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Start text detection
        logging.info("Starting document analysis with Textract...")
        job_id = await start_document_analysis(textract_client, bucket_name, s3_key, job_tag, notification_channel)

        # Retrieve the analysis results
        logging.info("Polling for document analysis results...")
        blocks = await get_document_analysis(textract_client, job_id)

        # Extract raw text grouped by page
        raw_pages = get_raw_text(blocks)

        # Format the output for each page as specified
        combined_text = ""
        for page in sorted(raw_pages.keys()):
            combined_text += f"----------------------- Page No. {page} Start -------------------------\n"
            combined_text += "\n".join(raw_pages[page]) + "\n"
            combined_text += f"----------------------- Page No. {page} End ---------------------------\n\n"

        # Print to console
        print(combined_text)

        # Optionally, write the output to a text file
        output_txt_filename = os.path.join(output_folder, f"{base_filename}_ExtractedText.txt")
        try:
            async with aiofiles.open(output_txt_filename, 'w', encoding='utf-8') as f:
                await f.write(combined_text)
            logging.info(f"Raw text saved to {output_txt_filename}.")
        except Exception as e:
            logging.error(f"Error writing raw text to file: {e}")
            sys.exit(1)
        return {
            "objAWSResponse":blocks,
            "strAWSUserContent":combined_text}
        

async def extractByAwsTextract(local_pdf_path):
    """
    Wrapper function to process the PDF using AWS Textract.
    """
    bucket_name = os.getenv("s3_bucket_name_for_tally_docs") # 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name, else a unique name is generated
    job_tag = "ExtractTextJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    return await process_document(local_pdf_path, bucket_name, object_name, job_tag, notification_channel)

if __name__ == "__main__":
    # Replace 'your_pdf_file.pdf' with the path to your PDF file.
    pdf_path = 'your_pdf_file.pdf'
    asyncio.run(extractByAwsTextract(pdf_path))
