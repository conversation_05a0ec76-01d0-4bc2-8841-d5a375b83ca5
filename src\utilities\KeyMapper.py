
from fastapi import HTTPException
from fuzzywuzzy import process  # For fuzzy string matching
import traceback

# If you're using timezone awareness (optional)
import pytz
from datetime import datetime

class CKeyValMapper:
    """
        Purpose: This class contains methods that are used to Map User Provided Prompts With Extracted Data. 
    """


    @staticmethod
    def MSFindBestMatch(target, choices):
        """Find the best match for a given target from a list of choices."""
        best_match, score = process.extractOne(target, choices)
        return best_match if score > 80 else None

    @staticmethod
    def MSMapFields(user_fields, doc_fields):
        """Map user fields to document fields using fuzzy matching."""
        mapped_fields = []
        if isinstance(doc_fields,list):
            doc_keys = [list(fields.keys())[0] for fields in doc_fields] # Adapted to handle list of dicts
        elif isinstance(doc_fields,dict):
            doc_keys = list(doc_fields.keys())
        for user_field in user_fields:
            best_match = CKeyValMapper.MSFindBestMatch(user_field['FieldName'], doc_keys)
            if best_match:
                # Extract the value associated with the best match key from doc_fields
                if isinstance(doc_fields, list):
                    matched_field = next(item for item in doc_fields if best_match in item)
                    mapped_fields.append({user_field['FieldName']: matched_field[best_match]})
                elif isinstance(doc_fields, dict):
                    mapped_fields.append({user_field['FieldName']: doc_fields[best_match]})
        return mapped_fields

    @staticmethod
    def MSMapTableFields(user_fields, doc_fields):
        """Map user fields to document fields using fuzzy matching."""
        dictRowData = {}
        doc_keys = list(doc_fields.keys())
        for user_field in user_fields:
            best_match = CKeyValMapper.MSFindBestMatch(user_field['FieldName'], doc_keys)
            if best_match:
                dictRowData[user_field['FieldName']] = doc_fields[best_match]
        return [dictRowData]

    def MSMapTables(user_tables, doc_tables):
        """Map user tables to document tables using fuzzy matching."""
        mapped_tables = []
        doc_table_keys = list(doc_tables.keys())
        if doc_table_keys:
            for user_table in user_tables:
                table_name = user_table['TableName']
                best_table_match = CKeyValMapper.MSFindBestMatch(table_name.replace(" ", ""), doc_table_keys)
                if best_table_match and doc_tables[best_table_match]:
                    doc_table_data = doc_tables[best_table_match]
                    mapped_table_data = [CKeyValMapper.MSMapTableFields(user_table['Fields'], doc_row) for doc_row in doc_table_data]
                    mapped_tables.append({table_name: [item for sublist in mapped_table_data for item in sublist]})
        return mapped_tables

    @staticmethod
    def MSMapUserGivenFields(user_data, extracted_data):
        """Map extracted document data to the user-provided data format using safe access methods."""
        try:
            # Assuming CKeyValMapper.MSMapFields and CKeyValMapper.MSMapTables are defined elsewhere
            mapped_data = {
                'Fields': CKeyValMapper.MSMapFields(user_data.get('Fields', []), extracted_data.get('Fields', [])),
                'Tables': CKeyValMapper.MSMapTables(user_data.get('Tables', []), extracted_data.get('Tables', {})),
                'meta_data': {
                    "page_height": extracted_data.get('meta_data', {}).get("page_height"),
                    "page_width": extracted_data.get('meta_data', {}).get("page_width")
                }
            }

            return mapped_data
        except Exception as e:
            raise HTTPException(status_code=500, detail="There was an issue mapping the extracted data to the specified user model fields.\nThis could be due to a mismatch between the fields you provided and the fields extracted from the document. ")

