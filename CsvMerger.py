import os
import pandas as pd

def merge_pricing_reports(base_directory, output_file):
    """
    Merges all Pricing_Report CSV files from 'Daily_Automation_Reports' folders within
    year_month_day formatted directories into a single CSV file.

    Args:
        base_directory (str): The root directory to search.
        output_file (str): The output path for the merged CSV file.
    """
    merged_data = []
    for root, dirs, files in os.walk(base_directory):
        for fileName in files:
            try:
                file_path = os.path.join(root, fileName)
                print(f"Processing file: {file_path}")
                # Read the CSV file and append it to the merged data
                try:
                    df = pd.read_csv(file_path)
                    merged_data.append(df)
                except Exception as e:
                    print(f"Error reading {file_path}: {e}")
            except Exception as e:
                print(f"Failed to merge file: '{file_path}'")

    # Combine all data and write to the output file
    if merged_data:
        combined_df = pd.concat(merged_data, ignore_index=True)
        combined_df.to_csv(output_file, index=False)
        print(f"Merged file created at: {output_file}")
    else:
        print("No Pricing_Report files found.")

# Example usage
# base_directory = r"PriceListReport"
# output_file = r"merged_pricing_report.csv"
# merge_pricing_reports(base_directory, output_file)


import pandas as pd

def remove_duplicates_from_csv(input_file, output_file, subset_columns=None):
    """
    Removes duplicate rows from a CSV file and saves the result to a new file.

    Args:
        input_file (str): Path to the input CSV file.
        output_file (str): Path to save the cleaned CSV file without duplicates.
        subset_columns (list, optional): List of column names to consider for duplicates.
                                         If None, all columns are considered.
    """
    try:
        # Read the CSV file into a DataFrame
        df = pd.read_csv(input_file)

        # Remove duplicates
        df_cleaned = df.drop_duplicates(subset=subset_columns)

        # Save the cleaned DataFrame to the output file
        df_cleaned.to_csv(output_file, index=False)
        print(f"Duplicates removed. Cleaned file saved at: {output_file}")

    except Exception as e:
        print(f"An error occurred: {e}")

# Example usage
input_csv = r"merged_pricing_report.csv"
output_csv = r"cleansed_merged_pricing_report.csv"

# Remove duplicates based on all columns
remove_duplicates_from_csv(input_csv, output_csv)

# Remove duplicates based on specific columns (e.g., 'Column1' and 'Column2')
# remove_duplicates_from_csv(input_csv, output_csv, subset_columns=['Column1', 'Column2'])
