
from src.Controllers.ParagTradersControllers import CHansgrohe_XML, CNexion_XML, CSimpolo_XML, CKohler_XML, CToto_XML, CGeberit_XML, CAquant_XML, CQuotation
# from src.utilities.helperFunc import  CExcelHelper
from openpyxl.styles import Alignment, Font, Border, Side

from openpyxl import Workbook, load_workbook

class CPriceListREPORT:

    @staticmethod
    def MSFormatExcel(
            sExcelFilePath=None,
            oExcelSheet=None,
            bIsSheetObject=True,
            lsWordWrapColumns=None,
            column_width_mapping=None,
            auto_adjust_width=False,  # New flag to auto-adjust column width
            lsSheetNames=None  # New: List of sheet names to format if an Excel file is passed
        ):
        """
            Input:

                1) sExcelFilePath: String representing the Excel file path

                2) oExcelSheet: Excel sheet object (optional if bIsSheetObject is True)

                3) bIsSheetObject: Boolean indicating if given data is an object (True) or file path (False)

                4) lsWordWrapColumns: List of column names where text should be wrapped

                5) column_width_mapping: Dictionary of column names and their desired widths, e.g., {'Matched PricelistItem Property': 50, 'Qty(Box)': 15}

                6) auto_adjust_width: Boolean flag to enable automatic adjustment of column width based on cell content

                7) lsSheetNames: List of sheet names to apply formatting if an Excel file is provided
                
            Output:

                - Returns the formatted Excel file path if input was a file path.
                
                - Returns the modified Excel sheet object if input was an object.

            Purpose:

                To format specific sheets of an Excel file by setting a bold font for the header row, applying borders to all cells,
                auto-adjusting column widths based on content (if enabled), and wrapping text in specified columns.
        """
        
        # Load the Excel file if bIsSheetObject is False
        if not bIsSheetObject:
            wb = load_workbook(sExcelFilePath)

            # If no specific sheet names are provided, default to all sheets in the workbook
            sheets_to_format = wb.sheetnames if not lsSheetNames else lsSheetNames

            for sheet_name in sheets_to_format:
                if sheet_name in wb.sheetnames:
                    oExcelSheet = wb[sheet_name]
                else:
                    raise ValueError(f"Sheet '{sheet_name}' not found in the workbook.")

                # Define styles
                header_font = Font(name="Arial", size=11, bold=True)  # Bold font only for headers
                border_style = Border(
                    left=Side(style="thin"), right=Side(style="thin"),
                    top=Side(style="thin"), bottom=Side(style="thin")
                )
                word_wrap = Alignment(wrap_text=True)

                # Apply formatting and adjust column widths
                for col in oExcelSheet.iter_cols():
                    col_letter = col[0].column_letter  # Get column letter
                    col_name = oExcelSheet.cell(row=1, column=col[0].column).value  # Get column name

                    # Initialize max length for auto-width adjustment
                    max_length = 0
                    
                    # Apply border to all cells and calculate max length
                    for cell in col:
                        cell.border = border_style
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))
                    
                    # Apply bold font to header row only
                    header_cell = oExcelSheet.cell(row=1, column=col[0].column)
                    header_cell.font = header_font
                    
                    # Apply word wrap to specified columns
                    if lsWordWrapColumns and col_name in lsWordWrapColumns:
                        for cell in col:
                            cell.alignment = word_wrap
                    
                    # Adjust column width based on the mapping
                    if column_width_mapping and col_name in column_width_mapping:
                        oExcelSheet.column_dimensions[col_letter].width = column_width_mapping[col_name]
                    
                    # Auto-adjust column width if enabled and column is not in lsWordWrapColumns
                    if auto_adjust_width and (not lsWordWrapColumns or col_name not in lsWordWrapColumns):
                        oExcelSheet.column_dimensions[col_letter].width = max_length + 2  # Add padding for better spacing

            # Save the workbook
            wb.save(sExcelFilePath)
            return sExcelFilePath
        else:
            if oExcelSheet is None:
                raise ValueError("No valid Excel sheet object provided.")

            # Define styles
            header_font = Font(name="Arial", size=11, bold=True)  # Bold font only for headers
            border_style = Border(
                left=Side(style="thin"), right=Side(style="thin"),
                top=Side(style="thin"), bottom=Side(style="thin")
            )
            word_wrap = Alignment(wrap_text=True)

            # Apply formatting and adjust column widths
            for col in oExcelSheet.iter_cols():
                col_letter = col[0].column_letter  # Get column letter
                col_name = oExcelSheet.cell(row=1, column=col[0].column).value  # Get column name

                # Initialize max length for auto-width adjustment
                max_length = 0
                
                # Apply border to all cells and calculate max length
                for cell in col:
                    cell.border = border_style
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                
                # Apply bold font to header row only
                header_cell = oExcelSheet.cell(row=1, column=col[0].column)
                header_cell.font = header_font
                
                # Apply word wrap to specified columns
                if lsWordWrapColumns and col_name in lsWordWrapColumns:
                    for cell in col:
                        cell.alignment = word_wrap
                
                # Adjust column width based on the mapping
                if column_width_mapping and col_name in column_width_mapping:
                    oExcelSheet.column_dimensions[col_letter].width = column_width_mapping[col_name]
                
                # Auto-adjust column width if enabled and column is not in lsWordWrapColumns
                if auto_adjust_width and (not lsWordWrapColumns or col_name not in lsWordWrapColumns):
                    oExcelSheet.column_dimensions[col_letter].width = max_length + 2  # Add padding for better spacing

            # Return the modified sheet object
            return oExcelSheet
        
    ### ---------------------------------- Price List Creationg--------------------------------------
    @staticmethod
    async def MSCreatePriceListReport(iUserId, dictExtractedData, strVendorName, strReportFilePath):
        """
            Input:

                1) iUserId: int
                The ID of the user for whom the price list report is being generated.

                2) dictExtractedData: dict
                A mapping of vendor names to lists of extracted pricing data entries.  
                Each entry is expected to be a dict containing pricing fields.

                3) strVendorName: str
                The vendor name whose pricing data should be included in this report.

                4) strReportFilePath: str
                The filesystem path where the CSV report should be written.

            Output:

                None:
                    No direct return value; generates or updates a CSV file at `strReportFilePath`.

            Purpose:

                To build and persist a vendor-specific price list report by:
                - Retrieving the list of pricing entries for `strVendorName` from `dictExtractedData`.
                - Formatting these entries into the rows (`lsRows`) required by the CSV schema.
                - Invoking `MSUpdatePricingListReport` to write or append the CSV file if there are rows to report.
        """
        lsRows = []
        if strVendorName.lower() == "hansgrohe":
            lsRows = await CHansgrohe_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)

        elif strVendorName.lower() == "geberit":
            lsRows = await CGeberit_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "nexion":
            lsRows = await CNexion_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "simpolo":
            lsRows = await CSimpolo_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData, strversion="v3")
            
        elif strVendorName.lower() == "toto":
            lsRows = await CToto_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
            
        elif strVendorName.lower() == "kohler":
            lsRows = await CKohler_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        elif strVendorName.lower() == "aquant":
            lsRows = await CAquant_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)    
        else:
            # raise ValueError("No Pricelist found for the given vendor.")
            return False
        
        # Call Function to write the csv report 
        if lsRows:
            try:
                await CPriceListREPORT.update_pricing_list_report(lsReportRows=lsRows, report_pricing_file=strReportFilePath)
            except Exception as e:
                return False
        return True
    
    @staticmethod
    async def update_pricing_list_report(lsReportRows, report_pricing_file):
        """
        Update the pricing list report.
        Enhances formatting for better readability.
        """
        try:
            print(f"Saving Pricing List Report at location: {report_pricing_file}")

            # Define field names
            TilesFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property',
                'Qty(Box)', 'Rate / Box', 'Basic Rate / Sq Ft', 'Discount (%)',
                'Item Amount', 'PriceList Rate / Box', 'Ex-Fac./Sft.', 'Pcs/Box',
                'Sft./Box', 'PriceList Rate / Box Matched', 'PriceList Basic Rate / Sq Ft Matched',
                'Accuvelocity Comments'
            ]
            SanitaryFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property', 'Item Rate',
                'Qty', 'Discount (%)', 'Item Amount', 'PriceList Amount',
                'PriceList Rate Matched', 'Accuvelocity Comments'
            ]

            # Load or create workbook
            try:
                wb = load_workbook(report_pricing_file)
                ws_tiles = wb["Tiles"]
                ws_sanitary = wb["Sanitary"]
            except FileNotFoundError:
                wb = Workbook()
                ws_tiles = wb.active
                ws_tiles.title = "Tiles"
                ws_sanitary = wb.create_sheet(title="Sanitary")

                # Write headers
                ws_tiles.append(TilesFieldNames)
                ws_sanitary.append(SanitaryFieldNames)

            # Process entries and update rows
            def find_and_update_row(sheet, criteria, entry, field_names):
                headers = list(sheet.iter_rows(min_row=1, max_row=1, values_only=True))[0]
                for row_index, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                    if all(row[headers.index(key)] == value for key, value in criteria.items() if key in headers):
                        for col_index, field in enumerate(field_names, start=1):
                            sheet.cell(row=row_index, column=col_index, value=entry.get(field, sheet.cell(row=row_index, column=col_index).value))
                        return True
                return False

            # Vendor categories
            sanitary_vendors = {"Hansgrohe", "Geberit", "Toto", "Kohler", "Aquant"}
            tiles_vendors = {"Ispira", "Nexion", "Simpolo"}

            for report_row in lsReportRows:
                entry = {
                    'VendorName': report_row.strVendorName,
                    'Price List Effective Start Date': report_row.strPriceListEffectiveStartDate,
                    'InvoiceNumber': report_row.strInvoiceNo,
                    'InvoiceDate': report_row.strInvoiceDate,
                    'Unique Item Key': report_row.strItemId,
                    'Item Name': report_row.strItemName,
                    'Matched PricelistItem Property': report_row.strMatchedItemProperty,
                    'Qty(Box)': report_row.strItemQty,
                    'Qty': report_row.strItemQty,
                    'Item Rate': report_row.strItemRate,
                    'Rate / Box': report_row.strItemRatePerBox,
                    'Basic Rate / Sq Ft': report_row.strItemRatePerSqFt,
                    'Discount (%)': report_row.strDiscount,
                    'Item Amount': report_row.strItemAmount,
                    'PriceList Rate / Box': report_row.strPriceListAmount,
                    'PriceList Amount': report_row.strPriceListAmount,
                    'PriceList Rate Matched': report_row.strIsSanitaryItemRateMatch,
                    'Pcs/Box': report_row.strPCSPerBox,
                    'Sft./Box': report_row.strPriceListRatePerSqFt,
                    'Ex-Fac./Sft.': report_row.strExFacAmount,
                    'PriceList Rate / Box Matched': report_row.strIsPriceListRatePerBoxMatch,
                    'PriceList Basic Rate / Sq Ft Matched': report_row.strIsPriceListRatePerSqFtMatch,
                    'Accuvelocity Comments': report_row.strAccuvelocityComments
                }

                if report_row.strVendorName in sanitary_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Item Rate': entry['Item Rate'],
                        'Item Name':  entry['Item Name'],
                    }
                    sheet = ws_sanitary
                    field_names = SanitaryFieldNames
                elif report_row.strVendorName in tiles_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Rate / Box': entry['Rate / Box'],
                        'Item Name':  entry['Item Name']
                    }
                    sheet = ws_tiles
                    field_names = TilesFieldNames
                else:
                    continue

                if not find_and_update_row(sheet, criteria, entry, field_names):
                    sheet.append([entry.get(field, "") for field in field_names])

            # Apply formatting
            ws_tiles= CPriceListREPORT.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_tiles,  lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})
            ws_sanitary = CPriceListREPORT.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_sanitary, lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],  column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})

            # Save workbook
            wb.save(report_pricing_file)
            print(f"Pricing list report successfully updated at location: {report_pricing_file}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print("Error:", e)
            raise e