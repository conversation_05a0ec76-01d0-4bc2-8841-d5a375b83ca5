{"name": "gpt-extraction-frontend", "private": true, "version": "0.0.0", "type": "module", "scripts": {"dev": "vite --host", "build": "vite build", "lint": "eslint . --ext js,jsx --report-unused-disable-directives --max-warnings 0", "preview": "vite preview"}, "dependencies": {"@ajainarayanan/react-pan-zoom": "^0.0.4", "@chakra-ui/icons": "^2.0.18", "@chakra-ui/react": "^2.8.2", "@emotion/react": "^11.13.3", "@emotion/styled": "^11.13.0", "@fontsource/inter": "^5.0.17", "@fontsource/poppins": "^5.0.12", "@fortawesome/fontawesome-svg-core": "^6.5.2", "@fortawesome/free-solid-svg-icons": "^6.5.2", "@fortawesome/react-fontawesome": "^0.2.0", "@googlemaps/react-wrapper": "^1.1.35", "@heroicons/react": "^2.1.1", "@material-tailwind/react": "^2.1.9", "@mui/material": "^5.16.6", "@mui/x-date-pickers": "^7.11.1", "@react-pdf-viewer/core": "^3.12.0", "@react-pdf-viewer/default-layout": "^3.12.0", "@react-pdf-viewer/search": "^3.12.0", "@react-pdf/renderer": "^3.4.4", "@tailwindcss/aspect-ratio": "^0.4.2", "axios": "^1.6.7", "clsx": "^2.1.0", "date-fns": "^3.6.0", "date-fns-tz": "^3.1.3", "dayjs": "^1.11.11", "exceljs": "^4.4.0", "file-saver": "^2.0.5", "flatpickr": "^4.6.13", "formik": "^2.4.5", "framer-motion": "^11.5.4", "html2canvas": "^1.4.1", "jsonwebtoken": "^9.0.2", "jspdf": "^2.5.1", "jszip": "^3.10.1", "jwt-decode": "^4.0.0", "ldrs": "^1.0.1", "libphonenumber-js": "^1.10.60", "mammoth": "^1.7.1", "modern-normalize": "^2.0.0", "moment": "^2.30.1", "mui-tel-input": "^5.1.2", "papaparse": "^5.4.1", "react": "^18.2.0", "react-dom": "^18.2.0", "react-flatpickr": "^3.10.13", "react-google-recaptcha": "^3.1.0", "react-hook-form": "^7.52.1", "react-hot-toast": "^2.4.1", "react-icons": "^5.0.1", "react-indiana-drag-scroll": "^2.2.0", "react-map-interaction": "^2.1.0", "react-modal": "^3.16.1", "react-pdf": "^7.7.1", "react-phone-number-input": "^3.3.9", "react-router-dom": "^6.22.2", "react-scroll": "^1.9.0", "react-select": "^5.8.0", "react-simple-star-rating": "^5.1.7", "react-slick": "^0.30.2", "react-zoom-pan-pinch": "^3.4.4", "recoil": "^0.7.7", "tailwind-merge": "^2.2.2", "yup": "^1.4.0"}, "devDependencies": {"@types/react": "^18.2.56", "@types/react-dom": "^18.2.19", "@vitejs/plugin-react": "^4.2.1", "autoprefixer": "^10.4.18", "eslint": "^8.56.0", "eslint-plugin-react": "^7.33.2", "eslint-plugin-react-hooks": "^4.6.0", "eslint-plugin-react-refresh": "^0.4.5", "postcss": "^8.4.35", "tailwindcss": "^3.4.1", "vite": "^5.1.4"}}