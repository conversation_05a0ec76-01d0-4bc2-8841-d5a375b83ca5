@tailwind base;
@tailwind components;
@tailwind utilities;

/* @import url('https://fonts.googleapis.com/css2?family=Inter:wght@400;600;700&display=swap'); */

body {
  font-family: "Inter", sans-serif !important;
  overflow-x: hidden;
  max-width: 100vw;
  background-color: #f4f7fe;
}

* {
  margin: 0;
  padding: 0;
}

.scrollbar-hide::-webkit-scrollbar {
  display: none;
}

.scrollbar-hide {
  -ms-overflow-style: none;
  scrollbar-width: none;
}

input[type="datetime-local"]::-webkit-calendar-picker-indicator {
  cursor: pointer;
}

.flatpickr-monthDropdown-months {
  font-size: 0.9rem; /* Adjust font size for the dropdown */
  background-color: #f9f9f9; /* Optional: change background color */
  border-radius: 4px; /* Optional: round the corners */
  border: 1px solid #ccc; /* Optional: add a border */
  padding: 4px 8px; /* Optional: adjust padding */
}

.flatpickr-monthDropdown-months .flatpickr-monthDropdown-month {
  font-size: 0.9rem; /* Adjust font size for each option */
  padding: 4px; /* Optional: add padding to each option */
  background-color: #fff; /* Optional: change background color */
  color: #333; /* Optional: change text color */
}

.flatpickr-monthDropdown-months .flatpickr-monthDropdown-month:hover {
  background-color: #eee; /* Optional: change background on hover */
  color: #000; /* Optional: change text color on hover */
}

input[type="search"]::-webkit-search-decoration,
input[type="search"]::-webkit-search-cancel-button,
input[type="search"]::-webkit-search-results-button,
input[type="search"]::-webkit-search-results-decoration {
  display: none;
}

.chakra-input {
  color: #000;
}

input::-ms-reveal,
input::-ms-clear {
  display: none;
}