import sys
sys.path.append(".")

from sqlalchemy.ext.asyncio import AsyncSession, AsyncEngine
from sqlalchemy.future import select
from apscheduler.schedulers.asyncio import AsyncIOScheduler
from apscheduler.triggers.interval import IntervalTrigger
from datetime import datetime, timedelta
import pytz
import asyncio
import traceback
from fastapi import HTTPException
from src.Models.models import Base
from src.Models.models import Role, User, UserAPIUsage
from src.Controllers.auth_controller import CAuthController
from src.Controllers.Tally_Controller import CTallyController
import json
from config.db_config import engine
from config.constants import Constants


class CStartUp:
    """
        Purpose: This Class Contains All Methods Related To App Startup Events
    """

    @staticmethod
    async def MSRun(engine: AsyncEngine):
        """
        Inputs  :   (1) engine : The asynchronous engine instance (AsyncEngine)
        
        Output  : It Performs All the Startup Related Work Required.

        Purpose : This method is used to Perform All Startup Events

        Example : await MSRun(engine)
        """
        try:
            # Create all tables
            await CStartUp.MSCreateTables(engine)
            
            # Create roles
            await CStartUp.MSCreateRoles(engine)

            # Create Admin User
            await CStartUp.MSCreateAdminAndVerifierRoles()
            
            # Create Tally Templates
            await CTallyController.MSInitializeTemplates()
        except Exception as e:
            print(traceback.print_exc())
            print("Error Occur while running startup events ", e)


    @staticmethod
    async def MSCreateTables(engine: AsyncEngine):
        """
        Inputs  :   (1) engine : The asynchronous engine instance (AsyncEngine)
        
        Output  : It creates all the tables in the database.

        Purpose : This method is used to create database tables based on the metadata.

        Example : await create_tables(engine)
        """
        print("**** Tables Creation Started ****")

        try:
            # Create all tables
            async with engine.begin() as conn:
                await conn.run_sync(Base.metadata.create_all)
        except Exception as e:
            print(traceback.print_exc())
            print("Error Occur while create all table ", e)
            
        print("**** Tables Creation Completed ****")


    @staticmethod
    async def MSCreateRoles(engine: AsyncEngine):
        """
            Purpose: This method is used to create the Roles
        """
        print("**** Roles Creation Started ****")

        try:
            async with AsyncSession(engine) as session:
                roles = [
                    Role(RoleName="SuperAdmin", RolePriority=0),
                    Role(RoleName="Admin", RolePriority=1),
                    Role(RoleName="Developer", RolePriority=1),
                    Role(RoleName="Verifier", RolePriority=2),
                    Role(RoleName="PayAlgorithmUser", RolePriority=3),
                    Role(RoleName="General", RolePriority=4)
                ]
                
                for role in roles:
                    # Check if role already exists
                    existing_role = await session.execute(select(Role).filter_by(RoleName=role.RoleName))
                    if not existing_role.scalar():
                        # Role doesn't exist, insert it
                        session.add(role)
                await session.commit()
        except Exception as e:
            print("Error Occur while create roles ", e)
            
        print("**** Roles Creation Completed ****")


    @staticmethod
    async def MSCreateAdminAndVerifierRoles():
        print("**** Initial Account Creation Started ****")

        try:
            # Read the JSON file
            with open(r'resource/userList.json', 'r') as f:
                dictUsersData = json.load(f)
            
            # Extract user attributes
            lsUsers = dictUsersData.get("users", [])
            
            responses = []
            for user_attributes in lsUsers:
                # Create each user and collect responses
                response = await CAuthController.MSUserRegistration(bRaiseError=False, **user_attributes)
                await CAuthController.MSInitializeUserData(iUserID=response.get('uid'))

                responses.append(response)
            
            # Log the successful creation
            print("**** Initial Account Creation Completed ****")
            return responses

        except Exception as e:
            print(f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail=f"An error occurred while creating Initial user accounts: {str(e)}")
        

    
    @staticmethod
    def MSStartScheduler(engine: AsyncEngine):
        """
            Starts the scheduler to reset API usage for users who have surpassed 10 days since account creation.
        """
        scheduler = AsyncIOScheduler()
        
        scheduler.add_job(
            CStartUp.MSResetUserAPIUsage,
            trigger=IntervalTrigger(hours=24),
            args=[engine],
            id='reset_api_usage_task',
            name='Reset API usage task',
            replace_existing=True
        )
        scheduler.start()
    
    @staticmethod
    async def MSResetUserAPIUsage(engine: AsyncEngine):
        """
            This Method checks for users whose account creation date exceeds 10 days and resets their API usage.
        """
        try:
            async with AsyncSession(engine) as session:
                
                ten_days_ago = datetime.now(pytz.timezone("Asia/Kolkata")) - timedelta(days=Constants.iTrialDays)
                
                # Query users who were created more than 10 days ago
                result = await session.execute(
                    select(User).filter(User.created_at < ten_days_ago)
                )
                
                users_to_reset = result.scalars().all()
                
                for user in users_to_reset:
                    # Reset API usage for the user
                    api_usage_result = await session.execute(
                        select(UserAPIUsage).filter(UserAPIUsage.user_id == user.uid)
                    )
                    user_api_usage = api_usage_result.scalars().first()
                    
                    if user_api_usage:
                        user_api_usage.page_limit_left = 0

                await session.commit()

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while Auto Resetting User Api Usage After 10 Days: {str(e)}")
        

if __name__ == "__main__":
    asyncio.run(CStartUp.MSRun(engine))
