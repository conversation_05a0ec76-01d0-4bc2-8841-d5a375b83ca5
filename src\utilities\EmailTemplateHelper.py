class CEmailTemplateCreator:
    
    @staticmethod
    def MSGetEmailTemplate(strTemplateFilePath, dictInputArgs):
        """
            Reads an HTML email template from a file, replaces placeholders with provided values,
            and returns the processed HTML content.

            Parameters:
            -----------
                strTemplateFilePath : str
                    File path of the HTML template file.

                dictInputArgs : dict
                    Dictionary containing key-value pairs where keys are placeholders in the HTML template
                    and values are the data to replace the placeholders.

            Returns:
            --------
                str
                    Processed HTML content with placeholders replaced by actual data.
        """
        # Read your HTML template from a file or use it directly
        with open(strTemplateFilePath, "r") as file:
            html_content = file.read()

        for key, value in dictInputArgs.items():
            # Replace placeholders with actual data
            html_content = html_content.replace(f"{{{{ {key} }}}}", value)

        return html_content

if __name__ == "__main__":
    # Example usage
    template_path = "resource\Email_Templates\Check_in.html"
    template_args = {
        'customer_name': '<PERSON>',
        'trial_end_date': 'July 20, 2024',
        'your_name': 'Support Team'
    }
    htmlContent = CEmailTemplateCreator.MSGetEmailTemplate(template_path, template_args)
    print(htmlContent)