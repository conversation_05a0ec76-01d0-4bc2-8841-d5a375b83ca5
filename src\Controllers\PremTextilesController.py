import sys
sys.path.append("")
import traceback
import os
import pytz
from fastapi import HTTPException
import asyncio

from pydantic import BaseModel, Field, conlist
from typing import List, Optional
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
from datetime import datetime
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, PartyDetailsSchema, ConsigneeDetailsSchema, LedgerEntrySchema, TallyPurchaseVoucherSchema, AdditionalLedgerInfo
from src.utilities.helperFunc import DateHelper, CFileHandler
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.utilities.PathHandler import dictProjectPaths

class CPremTextiles:
    _mStrStoragePath = dictProjectPaths.get("strPremTextiles_StoragePath", r"H:\AI Data\DailyData\PremTextiles")
    _mStrVoucherType =  "AV Tax Journal"

    _mDictCompanyData = {
                            "company_name": "PREM TEXTILES (INTERNATIONAL) PVT.LTD. (2021-22)",
                            "gst_registration_type": "Regular",
                            "gst_in": "23**********1Z1",
                            "state_name": "Gujarat",
                            "country_name": "India"
                    }
    
    @staticmethod
    async def MSBIsDuplicateXML(iDocumentNumber):
        dictMatchedRecords = await CTallyController.MSFetchTallyDocRecords(
                    invoice_no=iDocumentNumber
                )
        if dictMatchedRecords:
            # Iterate through all matched records and check if any contain 'Success' status
            for record in dictMatchedRecords:
                # Safely check if TallyStatus and AVTallyXMLStatus are not None before accessing their values
                tally_status = record.get("TallyStatus")
                avtally_xml_status = record.get("AVTallyXMLStatus")
                
                if (tally_status and tally_status.value == "Success") or (avtally_xml_status and avtally_xml_status.value == "Success"):
                    return True  # If any record has 'Success' status, it's a duplicate
            
        
        return False
    
    @staticmethod
    async def MGenerateTallyXML(iUserId, iDocId, dictExtractedData, strVendorName, bRaiseError=False, strClientREQID=None):
        dictResponse = {
            "AVComments":"",
            "TallyStatus":"",
            "XMLFilePath":""
        }
        iSupplierInvoiceNumber = ""
        try:
            await CLogController.MSWriteLog(iUserId, "Info", "Starting XML Generation for ICD.")
            
            iSupplierInvoiceNumber = dictExtractedData.get("InvoiceNo")
            
            bIsDuplicate = await CPremTextiles.MSBIsDuplicateXML(iDocumentNumber=iSupplierInvoiceNumber)
            
            if bIsDuplicate:
                dictResponse["TallyStatus"] = "Duplicate"
                dictResponse["AVComments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                await CLogController.MSWriteLog(iUserId, "Info", f"Duplicate entry found for invoice numbered '{iSupplierInvoiceNumber}'.")
                # AVRecordDetail -- AVXMLGeneratedStatus, TracebackLogs, strAccuVelocityComments Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE', AVXMLGeneratedStatus=dictResponse["TallyStatus"],TracebackLogs= "WARNING - Duplicate Entry Detected in our AccuVelocity Software", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments=dictResponse["AVComments"])

                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID=iDocId,
                    iUserID=iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"] 
                )
                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")

            # Get Upload Document Attributes
            objUploadedDocs = await CDocumentData.MSGetDocById(
                                                                user_id=iUserId, 
                                                                docId=iDocId,
                                                                isBinaryDataRequired=False
                                                            )
            await CLogController.MSWriteLog(iUserId, "Info", f"Fetched document details for DocId: {iDocId}.")

            try:
                strXmlData = ""
                if "somani brothers" in strVendorName.lower(): 
                    strXmlData = await CSomaniBrothers.MSCreateXML(dictExtractedData=dictExtractedData)
                    
                else:
                    raise ValueError(f"Vendor '{strVendorName}' is not supported, please contact accuvelocity team for more info.")
                if strXmlData is not None:
                    # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                    strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name=strVendorName, no_of_stock_items=0)
                    await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            except ValueError as ve:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = str(ve)
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise ve
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            try:
                # Base directory path
                strDownloadDirPath = CPremTextiles._mStrStoragePath
                today_date = datetime.today().strftime('%Y_%m_%d')
                
                # Create the full directory path with the date-wise folder
                strDownloadDirPath = os.path.join(strDownloadDirPath, today_date)

                # Ensure the directory exists
                os.makedirs(strDownloadDirPath, exist_ok=True)

                strUploadedDocumentName = os.path.splitext(objUploadedDocs.get('DocName', ''))[0]
                strXMLFileName = f"{strClientREQID}_DID{iDocId}_DName{strUploadedDocumentName}.xml"
                strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                bIsFileWritten = CFileHandler.MSWriteFile(  strFilePath=strTodaysXmlFilePath, 
                                                            fileContent=strXmlData, 
                                                            strWriteMode="w", 
                                                            strEncoding=None) 
                if bIsFileWritten:
                    dictResponse["XMLFilePath"] = strTodaysXmlFilePath
                    dictResponse["TallyStatus"] = "Success"
                    dictResponse["AVComments"] = "-"
                    # AVRecordDetail Update  
                    await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                        iUserId=iUserId,
                        dictNewData = {"XMLFilePath": [strTodaysXmlFilePath]},
                        eMode = ENetworkLocationUpdateMode.APPEND,
                        strClientREQID=strClientREQID,
                        docId=iDocId)
                    # Update the status of document processing
                    current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
                    await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                        iDocID = iDocId,
                        iUserID= iUserId,
                        strREQID = strClientREQID,
                        invoice_no=iSupplierInvoiceNumber,
                        av_tally_xml_status="Success",
                        tally_api_resp={"status": f"Successfully created the tally xml at location: {strTodaysXmlFilePath}."},
                        resp_date_time=current_datetime
                    )

                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully stored xml file at location '{strTodaysXmlFilePath}'.")

            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                
                # Update the status of document processing
                current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=iSupplierInvoiceNumber,
                    av_tally_xml_status="Error",
                    tally_api_resp={"status": f"Failed to Create the Tally xml."},
                    DocErrorMsg={str(e)},
                    resp_date_time=current_datetime
                )
                
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

        except HTTPException as he:
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Error",
                tally_api_resp={"status": f"Failed to Create the Tally xml {he.detail}."},
                DocErrorMsg={str(he)},
                resp_date_time=current_datetime
            )
            
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{he}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        
        except ValueError as ve:
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Error",
                tally_api_resp={"status": f"Failed to Create the Tally xml."},
                DocErrorMsg={str(ve)},
                resp_date_time=current_datetime
            )
            raise ve
        
        except Exception as e:
            dictResponse["TallyStatus"] = "Skipped"
            dictResponse["AVComments"] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            
            
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Error",
                tally_api_resp={"status": f"Failed to Create the Tally xml."},
                DocErrorMsg={str(e)},
                resp_date_time=current_datetime
            )
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            print(f"An unexpected error occurred: {e}")

            if bRaiseError:
                raise e
            
        return dictResponse

class CSomaniBrothers:
    """
    A vendor-specific class for SYGNIA BRANDWORKS LLP. 
    It follows a similar structure to CBhavyaSales or CSheebaDairy 
    but adapts the logic to match Sygnia's data format.
    """
    
    # Basic Party Info
    _mStrLedgerName = "Somani Brothers, Indore"  # TODO 1: NEED TO VERIFY THIS LEDGER NAME  
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        # You can add more lines if needed for the address_list 
        "address_list": [
            "470, Jawahar Marg, Siyaganj, Indore" 
        ],
        "gst_registration_type": "Regular",  # TODO 2: NEED TO VERIFY GST TYPE
        "gst_in":"", 
        "state_name": "Madhya Pradesh",
        "country_name": "India",
    }
    _mDictCosigneeData = {
                        "address_list": ["46-A/27-B, SECTOR 'C', INDUSTRIAL AREA,", "SANWER ROAD, INDORE - 452015, CIN:-", "U17113MP2006PTC019140, MSME - UDYAM-MP", "-23-0001849,email:<EMAIL>"],
                        "gst_in": "23**********1Z1",
                        "mailing_name": "PREM TEXTILES (INTERNATIONAL) PVT. LTD. (2021-22)",
                        "state_name": "Madhya Pradesh",
                        "pin_code": "452015",
                        "country_name": "India"
                    }

    # -----------------------------------------------------------------------
    # 1) CREDIT Ledger (Party Ledger) - Sygnia
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Typically, in a Purchase voucher, the vendor (Sygnia) is credited.
        If your code convention is reversed, keep it consistent with your existing logic 
        (e.g. 'is_deemed_positive=False' if that is how your system is set up).
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CSomaniBrothers._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # If you follow the same logic as Sheeba/Bhavya
            "is_party_ledger": True
        }
        try:
            # "TotalChargeable" = 16426 in your sample data 
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            print("Failed to Get Credit Ledger Info:", e)

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Extract debit ledger information based on extracted invoice data,
        summing up taxable amounts for all unique tax rates while avoiding duplication for CGST and SGST.
        """
        main_taxes = dictExtractedData.get("Taxes", {}).get("MainTaxes", [])
        
        # Compute taxable amount for each unique tax rate
        taxable_amounts = {}
        seen_rates = set()
        
        for tax in main_taxes:
            tax_rate = tax.get("TaxRate", 0)
            taxable_amount = tax.get("TaxableAmount", 0)
            tax_name = tax.get("TaxName", "")
            
            # Avoid adding taxable amount twice for CGST and SGST pairs
            if ("CGST" in tax_name or "SGST" in tax_name) and tax_rate in seen_rates:
                continue
            
            if tax_rate:
                taxable_amounts[tax_rate] = taxable_amounts.get(tax_rate, 0) + taxable_amount
                seen_rates.add(tax_rate)
        
        # Compute total taxable amount
        total_taxable_amount = sum(taxable_amounts.values())
        
        # Create ledger entry
        ledger_name = "Mach Spares ( LRD)"
        ledger_details = {
            ledger_name: {
                "ledger_name": ledger_name,
                "amount": -total_taxable_amount,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
        }
        
        return list(ledger_details.values())

    # -----------------------------------------------------------------------
    # 3) GST Ledgers (Input CGST/SGST)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Generate GST ledger information based on extracted invoice data.
        Ensures unique ledger names (CGST (Input), SGST (Input)) with summed amounts.
        """
        gst_ledger_summary = {"CGST (Input)": 0, "SGST (Input)": 0}
        main_taxes = dictExtractedData.get("Taxes", {}).get("MainTaxes", [])
        seen_rates = set()

        for tax in main_taxes:
            tax_rate = tax.get("TaxRate", 0)
            tax_amount = tax.get("TaxAmount", 0)
            tax_name = tax.get("TaxName", "")

            if "CGST" in tax_name:
                gst_ledger_summary["CGST (Input)"] += tax_amount
            elif "SGST" in tax_name:
                gst_ledger_summary["SGST (Input)"] += tax_amount

            seen_rates.add(tax_rate)

        # Convert the summary dictionary into the required ledger format
        gst_ledger_details = [
            {
                "ledger_name": ledger,
                "amount": -amount,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            for ledger, amount in gst_ledger_summary.items() if amount > 0
        ]

        return gst_ledger_details


    # -----------------------------------------------------------------------
    # 4) Round Off Ledger
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        In your sample, there's "RoundingOFF A/c"=0.4 at the top-level,
        or a line in 'Table' describing 'ROUND OFF A/C' with Amount=0.4.

        If you want the top-level key "RoundingOFF A/c", we can fetch it similarly 
        to how you did with Sheeba. Or you can parse from Table's 
        "ROUND OFF A/C" line. 
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            # According to your sample, "RoundingOFF A/c": 0.4
            round_off_amount = dictExtractedData.get("RoundingOFF A/c", 0)

            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # We'll replicate your prior approach:
            if round_off_amount < 0:
                dictRoundoffLedgerInfo["amount"] = abs(round_off_amount)
                # keep is_deemed_positive=True => Tally sees negative
            else:
                dictRoundoffLedgerInfo["amount"] = -round_off_amount
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            print("Failed to Get Roundoff Ledger Info:", e)

        return lsRoundoffLedgers

    # -----------------------------------------------------------------------
    # 5) Consolidated Ledger Info
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to combine:
            - Party Ledger (credit)
            - Purchase Ledger(s)
            - GST Ledger(s)
            - Round Off
        """
        lsLedgersInfo = []

        lsCreditLedgers   = CSomaniBrothers.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers    = CSomaniBrothers.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetail = CSomaniBrothers.MSGetGSTLedgerInfo(dictExtractedData)
        fRoundOff = dictExtractedData.get("RoundingOff") 
        lsRoundoffLedgers = [
                                {
                                    "ledger_name": "Rounded Off A/c",
                                    "amount": abs(fRoundOff)  if fRoundOff < 0 else  -fRoundOff,
                                    "is_deemed_positive": True,
                                    # "is_deemed_positive": True if fRoundOff >= 0 else False,
                                    "is_party_ledger": False
                                }
                            ]

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetail)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    # -----------------------------------------------------------------------
    # 6) XML Creation
    # -----------------------------------------------------------------------
    @staticmethod
    async def MSCreateXML(dictExtractedData):
        """
        Build the Tally Purchase Voucher XML for Sygnia.
        Adjust or rename fields as needed to match your existing pydantic schemas.
        """
        try:
            # 1) Invoice Number & Date
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))    # TODO: Need to extract invoice date in format DDMMYYYY
            iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            objInvDate = DateHelper.MSConvertIntToDateObject(iExtractedInvoiceDate)
            # 2) Narration
            strNarration = f"Being Bill No. {strInvoiceNumber}/{objInvDate.day}.{objInvDate.month}.{objInvDate.year}"

            # 3) Gather ledger entries
            lsLedgerEntries = CSomaniBrothers.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects
            objCompanyInfo = CompanyInfoSchema(**CPremTextiles._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSomaniBrothers._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSomaniBrothers._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objAdditionalInfo = AdditionalLedgerInfo(bDiffActualQTY=False)
            # 5) Build Purchase Voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                voucher_type=CPremTextiles._mStrVoucherType,
                narration=strNarration,
                cost_center_name="",
                ledger_entries=lsLedgerEntries,
                additionalInfo=objAdditionalInfo
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            return xml_str
        except Exception as e:
            raise e
        
    # -----------------------------------------------------------------------
    # 7) Optional Narration
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGenerateNarration(lsLineItems):
        """
        Example of building a textual description from the 'Table' lines, 
        ignoring CGST/SGST OUTPUT or Round Off lines if you want.
        """
        strNarration = ""
        try:
            for iIndex, dictItem in enumerate(lsLineItems):
                desc = str(dictItem.get("DescriptionOfGoods", "")).strip()
                qty = str(dictItem.get("Quantity", "0")).strip()
                rate = str(dictItem.get("Rate", "0")).strip()
                amt = str(dictItem.get("Amount", "0")).strip()

                # Filter out lines that might be filler
                if desc and desc.upper() not in ["CGST OUTPUT", "SGST OUTPUT",  "Less ROUND OFF A/C", "ROUND OFF A/C", "0", "TOTAL"]:
                    strNarration += f"({iIndex+1}). {desc} | Qty: {qty} | Rate: {rate} | Amount: {amt}\n"
        except Exception as e:
            print(f"Error in MSGenerateNarration: {str(e)}")

        return strNarration


if __name__ == "__main__":
    import json
    
    # with open(r"resource\TEMP_TEST\Karnavati_ExtractedData.json", "r") as file:
    #     dictExtractedDataKarnavati = json.load(file)
    
    # with open(r"resource\TEMP_TEST\Sheeba_ExtractedData.json", "r") as file:
    #     dictExtractedDataSheeba = json.load(file)
    
    # with open(r"resource\TEMP_TEST\BhavyaSales_ExtracedData.json", "r") as file:
    #     dictExtractedDataBhavyaSales = json.load(file)
    
    # with open(r"resource\TEMP_TEST\Sygnia_ExtractedData.json", "r") as file:
    #     dictExtractedDataSygnia = json.load(file)
        
    # # dictJson = dictExtractedDataKarnavati
    # dictJson = dictExtractedDataSygnia
    
    # CSygnia.MSCreateXML(dictJson)

#     dictExtractedDataSheeba = {
#     "SellerName": "SHEEBA DAIRY",
#     "SellerAddress": "RAMAYANI KUNJ, B/h. Silver Cloud Hotel, Opp.: Gandhi Ashram",
#     "SellerCity": "Ahmedabad",
#     "SellerPincode": 380027,
#     "SellerFSSAILICNO": "10720026000344",
#     "SellerGSTIN/UIN": "24**********1ZZ",
#     "SellerStateName": "Gujarat",
#     "SellerStateCode": 24,
#     "BuyerName": "GWALIA SWEETS Pvt. Ltd. (TAPOVAN)",
#     "BuyerAddress": "TAPOVAN CIRCLE, GANDHINAGAR ROAD",
#     "BuyerCity": "Ahmedabad",
#     "BuyerGSTIN/UIN": "24**********1ZY",
#     "BuyerPAN/IT": "**********",
#     "BuyerStateName": "Gujarat",
#     "BuyerStateCode": 24,
#     "InvoiceNo": 3792,
#     "InvoiceDate": 210125,
#     "DeliveryNote": "0",
#     "ReferenceNo & Date": "0",
#     "OtherReference": "0",
#     "BuyerOrderNO": "0",
#     "DispatchDocNO": "0",
#     "DeliveryNoteDate": 0,
#     "DispatchedThrough": "0",
#     "Destination": "0",
#     "BillOfLanding/LR-RR No": "dt. 21-Jan-25",
#     "MotorVehicalNO No": "GJ 01 LT 4388",
#     "OutputCGSTRate@2.5": 2.5,
#     "OutputCGSTAmount@2.5": 91.12,
#     "OutputSGSTRate@2.5": 2.5,
#     "OutputSGSTAmount@2.5": 91.12,
#     "OutputCGSTRate@6.0": 6.0,
#     "OutputCGSTAmount@6.0": 0,
#     "OutputSGSTRate@6.0": 6.0,
#     "OutputSGSTAmount@6.0": 0,
#     "RoundingOFF": -0.04,
#     "Table": [
#         {
#             "SlNo.": 1,
#             "DescriptionOfGoods": "STANDARDIZED MILK",
#             "HSN/SAC": "0",
#             "Quantity": 22.0,
#             "Rate": 54.0,
#             "Per": "Ltr",
#             "Amount": 1188.0
#         },
#         {
#             "SlNo.": 2,
#             "DescriptionOfGoods": "LOOSE STANDARD PANEER",
#             "HSN/SAC": "04069000",
#             "Quantity": 5.0,
#             "Rate": 290.0,
#             "Per": "Kg.",
#             "Amount": 1450.0
#         },
#         {
#             "SlNo.": 3,
#             "DescriptionOfGoods": "LOOSE DAHI (CURD) (MADE FROM TONED MILK)",
#             "HSN/SAC": "********",
#             "Quantity": 37.2,
#             "Rate": 59.0,
#             "Per": "Kg.",
#             "Amount": 2194.8
#         },
#         {
#             "SlNo.": 4,
#             "DescriptionOfGoods": "LOOSE Matho(MADE FROM TONED MILK)",
#             "HSN/SAC": "********",
#             "Quantity": 11,
#             "Rate": 19.0,
#             "Per": "Kg.",
#             "Amount": 194.8
#         }
#     ],
#     "TotalInvoiceAmount": 5015.0,
#     "TotalChargeable(In Words)": "INR Five Thousand Fifteen Only",
#     "TotalTaxableValue": 3644.8,
#     "TotalCGST": 91.12,
#     "TotalSGST/UTGST": 91.12,
#     "TotalTaxAmount": 182.24,
#     "TotalTaxAmount(INWORD)": "INR One Hundred Eighty Two and Twenty Four paise Only",
#     "Table1": [
#         {
#             "HSN/SAC": "04069000",
#             "TaxableValue": 1450.0,
#             "CGSTRate": 2.5,
#             "CGSTAmount": 36.25,
#             "SGST/UTGSTRate": 2.5,
#             "SGST/UTGSTAmount": 36.25,
#             "TotalTaxAmount": 72.5
#         },
#         {
#             "HSN/SAC": "********",
#             "TaxableValue": 2194.8,
#             "CGSTRate": 2.5,
#             "CGSTAmount": 54.87,
#             "SGST/UTGSTRate": 2.5,
#             "SGST/UTGSTAmount": 54.87,
#             "TotalTaxAmount": 109.74
#         }
#     ],
#     "SellerPanNO": "**********",
#     "SellerBankName": "HDFC",
#     "SellerA/c No": "**************",
#     "SellerBankBranch": "USMANPURA",
#     "SellerBankIFSCode": "HDFC0001682"
# }
#     # CSheebaDairy.generate_ledger_details(dictExtractedDataSheeba)
    dictExtractedData = {
    "InvoiceNo": "10409",
    "InvoiceDate": "********",
    "InvoiceTime": "160500",
    "SellerDetails": {
        "SellerName": "SOMANI BROTHERS",
        "SellerGST": "23**********1ZU",
        "SellerPAN": "**********",
        "SellerEntityBasedOnPanOrGst": "Firm (Partnership)",
        "SellerContactNumber": "0731-2430519",
        "SellerEmail": "",
        "SellerAddress": "470, Jawahar Marg, Siyaganj, INDORE-452007 (M.P.)",
        "SellerState": "Madhya Pradesh"
    },
    "BuyersDetails": {
        "BuyerName": "PREM TEXTILES INTERNATIONAL P. LTD.-1",
        "BuyerGST": "23**********1Z1",
        "BuyerPAN": "**********",
        "BuyerEntityBasedOnPanOrGst": "Company",
        "BuyerContactNumber": "",
        "BuyerEmail": "",
        "BuyerAddress": "21-C, SECTOR-C, INDUSTRIAL AREA, SANWAR ROAD, INDORE",
        "BuyerState": "Madhya Pradesh"
    },
    "SubTotal": 3435.9,
    "Discounts": [
        {
            "DiscountName": "Trade Discount",
            "DiscountRate": 35,
            "DiscountAmount": 1849.05
        }
    ],
    "Charges": [],
    "Taxes": {
        "ApplicableGstTaxType": "CGST+SGST",
        "ApplicableGstTaxTypeReason": "Both seller and buyer are located in Madhya Pradesh, hence CGST+SGST applies.",
        "MainTaxes": [
            {
                "TaxName": "CGST",
                "TaxRate": 9,
                "TaxableAmount": 3435.9,
                "TaxAmount": 309.23
            },
            {
                "TaxName": "SGST",
                "TaxRate": 9,
                "TaxableAmount": 3435.9,
                "TaxAmount": 309.23
            }
        ],
        "OtherTaxes": {
            "TaxName": "",
            "TaxRate": 0,
            "Hsn/SacCode": 0,
            "TaxAmount": 0
        }
    },
    "RoundingOff": -0.36,
    "TotalAmount": 4054.0
}
    
    strXml =  CSomaniBrothers.MSCreateXML(dictExtractedData=dictExtractedData)
    with open("somanibrothers.xml", "w") as file:
        file.write(strXml)
    # LSdEBITlEDGERS = CSomaniBrothers.MSGetGSTLedgerInfo(dictExtractedData)
    
    
    # async def main():
    #    bIsduplicate =  await CGwalia.MSBIsDuplicateXML("1705")

    # asyncio.run(main())
