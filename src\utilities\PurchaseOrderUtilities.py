import os
from datetime import datetime
from pathlib import Path
import platform

class CPOUtilities:
    @staticmethod
    def MSGetLatestPOFilePath(PODownloadDirectory=Path(r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register")):
        
        # Determine the operating system
        os_name = platform.system() 
        if os_name == "Windows":
            PODownloadDirectory = Path(r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register")
        elif os_name == "Linux":
            PODownloadDirectory = Path(r"/home/<USER>/AV Ahemdabad Server/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register")
        
        
        # Get today's date in "yyyy-MM-dd" format
        today_date = datetime.now().strftime("%Y-%m-%d")
        
        # Construct the path for today's folder
        today_folder_path = os.path.join(PODownloadDirectory, today_date)
        
        # Check if the folder for today's date exists
        if os.path.exists(today_folder_path) and os.path.isdir(today_folder_path):
            # Search for .xls files in the folder
            xls_files = [f for f in os.listdir(today_folder_path) if f.endswith('.xls')]
            
            if xls_files:
                # Find the most recently modified .xls file
                latest_file = max(xls_files, key=lambda f: os.path.getmtime(os.path.join(today_folder_path, f)))
                
                # Return the full path of the last modified .xls file
                return os.path.join(today_folder_path, latest_file)
            else:
                return f"No .xls file found in the folder for {today_date}."
        else:
            return f"No folder found for today's date: {today_date}."