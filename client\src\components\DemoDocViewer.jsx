import PropTypes from 'prop-types';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { AiOutlineZoomIn, AiOutlineZoomOut, AiOutlineLock } from "react-icons/ai";
import { TbZoomReset } from "react-icons/tb";
import { Tooltip } from '@material-tailwind/react';
import React, { useEffect, useRef, useState } from 'react';
import ScrollContainer from 'react-indiana-drag-scroll';
import { Document, Page, pdfjs } from "react-pdf";
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.js`;
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';
import Resume from '../assets/PDF/Vaishnavi popat CV-1.pdf'

const DemoDocViewer = ({ highlightCoords, values, TableData }) => {
    const [isGrabbing, setIsGrabbing] = useState(false);
    const [IteratingForm, setIteratingForm] = useState(true);
    const [numPages, setNumPages] = useState(null);
    const [zoomLevel, setZoomLevel] = useState(1); // Track the current zoom level
    const highlightRef = useRef(null);
    // const pdfContainerRef = useRef(null);
    const [activeIndex, setActiveIndex] = useState(0);
    const [activeTable, setActiveTable] = useState('');

    useEffect(() => {
        if (Object.keys(TableData).length > 0 &&
            Object.values(TableData).some(table => table.length > 0)) {
            setActiveTable(Object.keys(TableData)[0])
        }
    }, [TableData])


    const handleButtonClick = () => {
        setIteratingForm(false);
    }

    const greenTick = (
        <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_344_8400)">
                <circle cx="9.5" cy="8.5" r="8.5" fill="#11AF22" />
            </g>
            <path d="M6.8252 8.78322L8.60891 10.5664L12.1748 7" stroke="white" strokeWidth="0.713287" strokeLinecap="round" strokeLinejoin="round" />
            <defs>
                <filter id="filter0_d_344_8400" x="0.524476" y="0" width="17.951" height="17.951" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feGaussianBlur stdDeviation="0.237762" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_344_8400" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_344_8400" result="shape" />
                </filter>
            </defs>
        </svg>
    )

    const greyTick = (
        <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg">
            <g filter="url(#filter0_d_344_8400)">
                <circle cx="9.5" cy="8.5" r="8.5" fill="#A5A5A5" />
            </g>
            <path d="M6.8252 8.78322L8.60891 10.5664L12.1748 7" stroke="white" strokeWidth="0.713287" strokeLinecap="round" strokeLinejoin="round" />
            <defs>
                <filter id="filter0_d_344_8400" x="0.524476" y="0" width="17.951" height="17.951" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feGaussianBlur stdDeviation="0.237762" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_344_8400" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_344_8400" result="shape" />
                </filter>
            </defs>
        </svg>
    )

    const handleMouseDown = () => {
        if (zoomLevel !== 1) {
            setIsGrabbing(true);
        }
    };

    const handleMouseUp = () => {
        setIsGrabbing(false);
    };

    const onDocumentLoadSuccess = ({ numPages }) => {
        setNumPages(numPages);
    };

    const calculateHighlightStyle = (coords, contentDimensions) => {
        if (!coords || !contentDimensions) return {};

        const { x1, y1, x2, y2 } = coords;
        const { width, height } = contentDimensions;

        const top = (y1 / height) * 100;
        const left = (x1 / width) * 100;
        const highlightWidth = ((x2 - x1) / width) * 100;
        const highlightHeight = ((y2 - y1) / height) * 100;

        return {
            position: 'absolute',
            top: `${top}%`,
            left: `${left}%`,
            width: `${highlightWidth}%`,
            height: `${highlightHeight}%`,
            backgroundColor: 'rgba(255, 255, 0, 0.5)', // Highlight color
            pointerEvents: 'none', // Ensure it doesn't interfere with document interactions
        };
    };

    const tabs = ["Invoice Extractor", "Receipt Extractor", "Purchase Order", "Account Statement"];
    const [activeTab, setActiveTab] = useState(tabs[0]);

    useEffect(() => {
        if (highlightCoords && highlightCoords.length > 0) {
            // Scroll to the highlight element
            const highlightElement = highlightRef.current;
            const viewerContainer = document.getElementById('docView');

            if (highlightElement && viewerContainer) {
                const highlightElementTop = highlightElement.offsetTop;
                const containerScrollTop = viewerContainer.scrollTop;
                const containerClientHeight = viewerContainer.clientHeight;
                const highlightElementHeight = highlightElement.clientHeight;

                // Calculate the offset position to scroll within the container
                const offsetPosition = highlightElementTop - containerClientHeight / 2 + highlightElementHeight / 2;

                console.log(
                    `Highlight Element Top: ${highlightElementTop}`,
                    `Container Scroll Top: ${containerScrollTop}`,
                    `Container Client Height: ${containerClientHeight}`,
                    `Highlight Element Height: ${highlightElementHeight}`,
                    `Offset Position: ${offsetPosition}`
                );

                viewerContainer.scrollTo({
                    top: offsetPosition,
                    behavior: 'smooth'
                });
            }
        }
    }, [highlightCoords, activeIndex]);

    return (
        <div className='bg-white border border-[#0A2B5C] w-[68vw] mx-auto rounded-lg mt-8'>
            <div className="flex justify-around py-2 mt-4">
                {tabs.map((tab) => (
                    <button
                        key={tab}
                        onClick={() => setActiveTab(tab)}
                        className={`font-medium ${activeTab === tab ? 'bg-white text-[#0A2B5C] border rounded-xl py-1 px-2 border-[#0A2B5C]' : 'bg-white py-1 px-2 text-[#0A2B5C]'}`}
                    >
                        {tab}
                    </button>
                ))}
            </div>
            <div className="grid grid-cols-3 gap-4 w-full h-[90vh] pt-4">
                {/* Pdf View */}
                <div id='docView' className="col-span-2 h-full overflow-y-auto relative bg-[#f4f7fe]" >
                    <TransformWrapper
                        wheel={{ disabled: true }}
                        options={{
                            limitToBounds: false,
                            disablePadding: false,
                            centerContent: false,
                            minScale: 1,
                            alignmentAnimation: { sizeX: 0, sizeY: 0 },
                            velocityAnimation: { disabled: true }
                        }}
                        panning={{
                            disabled: zoomLevel === 1,
                            velocityDisabled: true,
                            x: false
                        }}
                        onPanningStart={handleMouseDown}
                        onPanningStop={handleMouseUp}
                        onZoom={(ref) => setZoomLevel(ref.state.scale)} // Track zoom level changes
                        doubleClick={{ disabled: true }}
                    >
                        {({ zoomIn, zoomOut, resetTransform }) => (
                            <React.Fragment>
                                <ScrollContainer className='h-full opacity-90' hideScrollbars="true" activationDistance={20}>
                                    <div className="absolute w-9 h-10 z-20 flex flex-col left-3 mt-6 rounded-md">
                                        <Tooltip content='Read-Only' placement="right">
                                            <button type='button' className="p-2 mb-4 rounded-md shadow-md cursor-not-allowed bg-gray-300">
                                                <AiOutlineLock style={{ fontSize: '19px', cursor: 'not-allowed', color: '#003654' }} />
                                            </button>
                                        </Tooltip>
                                        <Tooltip content='Zoom In'>
                                            <button type='button' onClick={() => {
                                                zoomIn(0.2);
                                                if (zoomLevel <= 2.100000000000001) {
                                                    setZoomLevel((prevLevel) => prevLevel + 0.1);
                                                }
                                            }} className="bg-[#fff] p-2 mb-4 rounded-md shadow-md hover:bg-gray-300">
                                                <AiOutlineZoomIn style={{ fontSize: '19px', cursor: 'pointer', color: '#003654' }} />
                                            </button>
                                        </Tooltip>
                                        <Tooltip content='Zoom Out'>
                                            <button type='button' onClick={() => {
                                                zoomOut(0.2);
                                                if (zoomLevel >= 1) {
                                                    setZoomLevel((prevLevel) => Math.max(prevLevel - 0.1, 1));
                                                }
                                            }} className="bg-[#fff] p-2 mb-4 rounded-md shadow-md hover:bg-gray-300">
                                                <AiOutlineZoomOut style={{ fontSize: '19px', cursor: 'pointer', color: '#003654' }} />
                                            </button>
                                        </Tooltip>
                                        <Tooltip content='Zoom Reset'>
                                            <button type='button' onClick={() => {
                                                resetTransform();
                                                setZoomLevel(1);
                                            }} className="bg-[#fff] p-2 rounded-md shadow-md hover:bg-gray-300">
                                                <TbZoomReset style={{ fontSize: '19px', cursor: 'pointer', color: '#003654' }} />
                                            </button>
                                        </Tooltip>
                                    </div>
                                    <div
                                        className={`mx-[3vw] h-[95vh] -mt-14`}
                                        onMouseDown={handleMouseDown}
                                        onMouseUp={handleMouseUp}
                                        onMouseLeave={handleMouseUp}
                                    >
                                        <div
                                            className={`${zoomLevel === 1 ? 'cursor-default' : isGrabbing ? "cursor-grabbing" : "cursor-grab"}`}
                                            style={{ width: 'max-content', cursor: isGrabbing ? 'grabbing' : 'grab' }}
                                        >
                                            <TransformComponent>
                                                <Document
                                                    file={Resume}
                                                    onLoadSuccess={onDocumentLoadSuccess}
                                                    className="w-full mt-20"
                                                >
                                                    {Array.from(new Array(numPages), (el, index) => (
                                                        <div
                                                            key={`page_${index + 1}`}
                                                            className="mb-5 flex justify-center w-full"
                                                        >
                                                            <Page
                                                                pageNumber={index + 1}
                                                                width={740} // Adjust width as necessary
                                                                className="shadow-md relative" // Add relative positioning
                                                                renderTextLayer={false}
                                                            >
                                                                {activeIndex !== null && highlightCoords[activeIndex]?.page === index + 1 && (
                                                                    <div ref={highlightRef}
                                                                        style={calculateHighlightStyle(highlightCoords[activeIndex], { width: 594.9599609375, height: 842.25 })}
                                                                    />
                                                                )}
                                                            </Page>
                                                        </div>
                                                    ))}
                                                </Document>
                                            </TransformComponent>
                                        </div>
                                    </div>
                                </ScrollContainer>
                            </React.Fragment>
                        )}
                    </TransformWrapper>
                </div>
                {/* Extraction Fields */}
                <div className="col-span-1 ml-3 mx-2.5 bg-white " style={{ height: '100%', overflowY: 'auto', borderTopRightRadius: '20px', borderTopLeftRadius: '20px', borderBottomRightRadius: '20px', borderBottomLeftRadius: '20px' }}>
                    <div className="flex items-center sticky top-0 z-10 bg-[#ffff]" style={{ borderTopRightRadius: '20px', borderTopLeftRadius: '20px' }}>
                        {/* <svg className='mt-4 ml-5' width="22" height="21" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg"></svg> */}
                        <label className="text-md mt-4 ml-1 font-bold text-[#003654]">Extraction Fields:</label>
                    </div>
                    <div id='formFields' className="bg-white max-h-[85vh]" style={{ borderBottomRightRadius: '20px', borderBottomLeftRadius: '20px' }}>
                        <div className="flex flex-col m-3 gap-1 mb-4">
                            {Object.entries(values).map(([key, value], index) => {
                                // Uncomment and use these variables as needed
                                // const isApproved = approvedFields.has(key);
                                const isApproved = true;
                                const isActiveField = index === activeIndex;
                                {/* const isActiveField = index === 0; */ }

                                return (
                                    <div
                                        key={key}
                                        id={isActiveField && IteratingForm ? 'active' : undefined}
                                        className={`relative p-2 rounded-lg flex items-center border-[#003654] ${isActiveField && IteratingForm ? 'border-dashed border-2 border-[#003654]' : 'border-[#003654]'}`}
                                    >
                                        <label htmlFor={key} className="block text-sm font-medium tracking-tight text-gray-700 w-1/3">
                                            {key.replace(/([a-z])([A-Z])/g, '$1 $2').trim()}
                                        </label>
                                        <div className="flex items-center w-3/4">
                                            <div
                                                id={key}
                                                name={key}
                                                onClick={() => {
                                                    setActiveIndex(index);
                                                    setIteratingForm(true);
                                                }}
                                                className="flex-1 sm:text-sm p-1 cursor-pointer focus:outline-none focus:ring-0 rounded-md resize-none bg-gray-200"
                                                style={{ overflowX: "auto", height: `${value.length < 40 ? '2rem' : `${Math.min(Math.ceil((value.length / 40) + 2), 4)}rem`}` }}
                                            >
                                                {value}
                                            </div>
                                        </div>
                                        <Tooltip content={isApproved ? 'Unapprove This Field' : 'Approve This Field'} placement="left">
                                            <span
                                                className="absolute right-2 cursor-pointer mt-1 mr-1"
                                            // onClick={() => {
                                            //     approveFormField(Object.keys(formData)[index]);
                                            //     setActiveFieldIndex(index);
                                            // }}
                                            >
                                                {isApproved ? greenTick : greyTick}
                                            </span>
                                        </Tooltip>
                                    </div>
                                );
                            })}
                        </div>
                    </div>
                </div>
                {/* Independent Table Field */}
                <div className={`${Object.values(TableData).some(table => table.length > 0) ? '' : 'hidden'} mx-2 col-span-3 max-h-full flex flex-col mb-4 border rounded-lg`}>
                    <div
                        style={{ maxHeight: '30vh' }}
                        // className={`w-full flex flex-col mb-4 ${isTableActive ? 'border-dashed border-2 border-[#003654]' : ''} bg-[#ffff]  rounded-lg`}
                        className={`w-full flex flex-col mb-4 ${!IteratingForm ? 'border-dashed border-2 border-[#003654]' : ''} bg-[#ffff]  rounded-lg`}
                    >
                        <div className="flex justify-between p-3">
                            <div className="flex">
                                <label className="text-md ml-1 font-bold text-[#003654]">Extraction Table:</label>
                                {/* {(allFieldsApproved || allTablesApproved) ? <span className="ml-1 mt-1 cursor-not-allowed">{greenTick}</span> : <span className="ml-1 mt-1 cursor-not-allowed">{greyTick}</span>} */}
                                {<span className="ml-1 mt-1 cursor-not-allowed">{greenTick}</span>}
                            </div>
                            <div className="flex">
                                {Object.keys(TableData).filter(tableName => TableData[tableName].length > 0).map((tableName) => (
                                    <div
                                        key={tableName}
                                        type='button'
                                        onClick={() => {
                                            setActiveTable(tableName);
                                            setIteratingForm(false);
                                        }}
                                        className={`px-4 py-1 text-sm text-[#fff] ${activeTable === tableName ? 'bg-[#003654]' : 'bg-[#A9A9A9] border'} rounded-lg mr-2`}
                                        style={{ borderRadius: "13.56px" }}
                                    >
                                        <span className="flex items-center">
                                            {tableName}
                                            {/* {TableData[tableName].every((row, rowIndex) =>
                                                Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                            ) ? <span className="ml-1">{greenTick}</span> : <span className="ml-1">{greyTick}</span>} */}
                                            {<span className="ml-1">{greenTick}</span>}
                                        </span>
                                    </div>
                                ))}
                            </div>
                        </div>
                        {Object.keys(TableData).map((tableName, index) => (
                            <div
                                key={index}
                                className={`w-full flex flex-col mb-1 overflow-auto ${activeTable === tableName ? '' : 'hidden'} bg-[#ffff] border rounded-lg`}
                            // className={`w-full flex flex-col mb-1 overflow-auto ${activeTable === tableName ? '' : 'hidden'} bg-[#ffff] border rounded-lg`}
                            >
                                <div className={`overflow-auto m-3 border border-gray-300 rounded-md ${TableData[tableName].length > 3 ? 'max-h-[55vh]' : 'h-auto'}`} style={{ boxShadow: '0px 2.1px 8.4px 0px #0000001F' }}>
                                    <table className="min-w-full divide-y divide-gray-200 table-auto">
                                        <thead className="bg-[#F1F3F9] sticky top-0 z-5">
                                            <tr>
                                                <th className="px-2 py-3 text-left text-xs font-semibold text-gray-600 uppercase border-r-4 tracking-wider whitespace-nowrap">
                                                    <Tooltip
                                                        content={`Unapprove ${tableName}`}
                                                    // content={TableData[tableName].every((row, rowIndex) =>
                                                    //     Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                                    // ) ? `Unapprove ${tableName}` : `Approve ${tableName}`}
                                                    >
                                                        <span
                                                            className="flex justify-center mr-3 cursor-pointer"
                                                        >
                                                            {/* {TableData[tableName].every((row, rowIndex) =>
                                                                Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                                            ) ? greenTick : greyTick} */}
                                                            {greenTick}
                                                        </span>
                                                    </Tooltip>
                                                </th>
                                                {TableData[tableName].length > 0 && Object.keys(TableData[tableName][0]).map(header => (
                                                    <th key={header} className="px-3 py-3 text-left text-xs font-semibold text-gray-600 border-r-4 uppercase tracking-wider whitespace-nowrap">
                                                        <div className="flex justify-between items-center">
                                                            {header.replace(/([A-Z])/g, ' $1').trim()}
                                                            <Tooltip
                                                                // content={TableData[tableName].every((row, rowIndex) =>
                                                                //     approvedTableFields.has(`${tableName}[${rowIndex}][${header}]`)
                                                                // ) ? `Unapprove Column` : `Approve Column`}
                                                                content={`Unapprove Column`}
                                                            >
                                                                <span
                                                                    className="cursor-pointer"
                                                                // onClick={() => {
                                                                //     approveColumnFields(tableName, header);
                                                                //     setIteratingFormData(false);
                                                                //     setActiveFieldIndex(index);
                                                                //     setIsTableActive(true);
                                                                //     setActiveTable(tableName);
                                                                //     setHighlightCoords({})
                                                                // }}
                                                                >
                                                                    {/* {TableData[tableName].every((row, rowIndex) =>
                                                                        approvedTableFields.has(`${tableName}[${rowIndex}][${header}]`)
                                                                    ) ? greenTick : greyTick} */}
                                                                    {greenTick}
                                                                </span>
                                                            </Tooltip>
                                                        </div>
                                                    </th>
                                                ))}
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y divide-gray-200" style={{ maxHeight: "30vh", overflow: 'auto' }}>
                                            {TableData[tableName].map((row, rowIndex) => (
                                                <tr key={rowIndex}
                                                    className={`${rowIndex % 2 === 0 ? 'bg-white' : 'bg-[#F8F9FC]'}`}
                                                >
                                                    <td className="flex justify-center items-center">
                                                        <Tooltip
                                                            // content={Object.keys(row).every(key =>
                                                            //     approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`)
                                                            // ) ? `Unapprove Row` : `Approve Row`}
                                                            content={`Approve Row`}
                                                        >
                                                            <span
                                                                className="mr-3 mt-4 cursor-pointer"
                                                            >
                                                                {/* {Object.keys(row).every(key =>
                                                                    approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`)
                                                                ) ? greenTick : greyTick} */}
                                                                {greenTick}
                                                            </span>
                                                        </Tooltip>
                                                    </td>
                                                    {Object.entries(row).map(([key, value]) => (
                                                        <td key={`${rowIndex}-${key}`} className="text-gray-700 whitespace-normal break-words border border-gray-300">
                                                            {/* Replace this with your actual input field */}
                                                            <div className="flex items-center">
                                                                <div
                                                                    onClick={handleButtonClick}
                                                                    className={`text-left min-h-[2em] max-h-[3em] min-w-[3em] ${rowIndex % 2 === 0 ? 'bg-white' : 'bg-[#F8F9FC]'} w-full px-3 overflow-auto flex items-center`}
                                                                    style={{
                                                                        display: 'flex',
                                                                        alignItems: 'center',
                                                                        justifyContent: 'center',
                                                                        textAlign: 'left',
                                                                        padding: '0.5em 1em', // Add padding here (adjust as needed)
                                                                    }}
                                                                >
                                                                    {value}
                                                                </div>

                                                                <Tooltip
                                                                    content={`Unapprove Cell`}
                                                                // : `Approve Cell`}
                                                                >
                                                                    <span
                                                                        className="mr-4 cursor-pointer"
                                                                        style={{
                                                                            display: 'flex',
                                                                            alignItems: 'center',
                                                                            justifyContent: 'center',
                                                                            textAlign: 'left',
                                                                            padding: '0.5em 0', // Add padding here (adjust as needed)
                                                                        }}
                                                                    >
                                                                        {/* {approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`) ? greenTick : greyTick} */}
                                                                        {greenTick}
                                                                    </span>
                                                                </Tooltip>
                                                            </div>
                                                        </td>
                                                    ))}
                                                </tr>
                                            ))}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        ))}
                    </div>
                </div>
            </div >
        </div>
    );
};

DemoDocViewer.propTypes = {
    highlightCoords: PropTypes.array.isRequired,
    values: PropTypes.object.isRequired,
    TableData: PropTypes.object.isRequired,
    // contentUrl: PropTypes.string.isRequired,
    // contentType: PropTypes.string.isRequired,
    // docData: PropTypes.string.isRequired,
    // fileName: PropTypes.string.isRequired,
    // pageHeight: PropTypes.number.isRequired,
    // pageWidth: PropTypes.number.isRequired
};

export default DemoDocViewer;