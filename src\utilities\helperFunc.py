import socket
import sys

sys.path.append(r".")
import PyPDF2
from ensure import ensure_annotations
import json
import aiofiles
import os
from datetime import datetime, date
import re
import base64
from pathlib import Path
import hashlib
import random
import string
import tiktoken
import traceback
from fastapi import FastAPI
import xml.etree.ElementTree as ET
from src.Controllers.myXMLParser import CXmlParser
import os
from fastapi.middleware.cors import CORSMiddleware
from typing import Any, Optional
from fastapi.responses import JSONResponse
from src.Schemas.base_response_model import BaseResponseModel
from PyPDF2 import PdfReader, PdfWriter
import pdfplumber
import io
import i18n
import re
from docx import Document
from fastapi import HTTPException
import i18n
from openpyxl import load_workbook
from openpyxl.utils import get_column_letter
import pdfkit
import mammoth
import numpy as np
from config.constants import Constants
from passlib.context import CryptContext
from src.Controllers.Logs_Controller import CLogController
from fuzzywuzzy import process
import pandas as pd
from src.Schemas.Vendor_Schema import ModelFieldItem, ModelTable
from typing import List, Dict, Union
import zipfile
from openpyxl.styles import Alignment, Font, Border, Side
import math
from fuzzywuzzy import fuzz
from io import StringIO
import chardet      # For Auto Detecting File encoding
import pandas as pd
from io import StringIO
import chardet
import magic  # pip install python-magic
import os
from babel.numbers import format_currency
from datetime import datetime
from dotenv import load_dotenv
from google.cloud import translate_v3 as translate
from google.api_core import exceptions

from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy import MetaData, Table, update, and_, text

from concurrent.futures import ProcessPoolExecutor, ThreadPoolExecutor # We'll use ProcessPoolExecutor
import logging
from src.Controllers.CustomLogger import CLogger
CLogger.MCSetupLogging(strLogsDirPath=r"Logs\SplitPDFAlgo")
# Configure logging
# logging.basicConfig(level=logging.INFO, format='%(asctime)s - %(processName)s - %(threadName)s - %(levelname)s - %(message)s')

class CRegexMethod:

    @staticmethod
    def MSExtractTallyResDocDetails(filename):
        dictDocDetails = {
                "Doc_Details": {},
                "Other_GRN_Details": {},
                "Machinery_GRN_Details": {},
                "PO_Details": {},
                "Delivery_Note_Details": {},  # Added the new data type
                "Imprest_Journal_Details":{}
            }

        patterns = [
            {
                "name": "Doc_Details",
                "pattern": r"TallyXMLResponse-(REQ_[A-Za-z0-9_]+)_DID(\d+)_DName([^<>:\"/\\|?*]+)\.xml",
                "extract": lambda m: {
                    "REQID": m.group(1),
                    "DID": int(m.group(2)),
                    "DName": m.group(3)
                }
            },
            {
                "name": "PO_Details",
                "pattern": r"TallyXMLResponse-REQID_(\d+)_UID_(\d+)_PONUM_([^<>:\"/\\|?*]+)\.xml",
                "extract": lambda m: {
                    "REQID": int(m.group(1)),
                    "UID": int(m.group(2)),
                    "PONUM": m.group(3)
                }
            },
            {
                "name": "Other_GRN_Details",
                "pattern": r"TallyXMLResponse-REQID_(\d+)_UID_(\d+)_GRNNUM_([^<>:\"/\\|?*]+)\.xml",
                "extract": lambda m: {
                    "REQID": int(m.group(1)),
                    "UID": int(m.group(2)),
                    "GRNNUM": m.group(3)
                }
            },
            {
                "name": "Machinery_GRN_Details",
                "pattern": r"TallyXMLResponse-REQID_(\d+)_UID_(\d+)_COSTCENTER_([^_]+)_VENDOR_([^_]+)_(.+)_DATE_(\d{8})\.xml",
                "extract": lambda m: {
                    "REQID": int(m.group(1)),
                    "UID": int(m.group(2)),
                    "COSTCENTER": m.group(3),
                    "VENDOR": m.group(4).replace("_", " "),
                    "MachineNumber": m.group(5).replace("_", " "),
                    "DATE": m.group(6)
                }
            },
            {
                "name": "Delivery_Note_Details",
                "pattern": re.compile(
                    r"^TallyXMLResponse-"                # literal prefix
                    r"[A-Za-z0-9_]+_"                    # vendor name (or 'Quotation') plus underscore
                    r"(?:Delivery_Note)-"                 # literal 'Delivery_Note'
                    r"(REQ_(?:[^_]+_){2}[^_]+)"           # group1: REQ_<vendor>_<timestamp>_<session>
                    r"_(.+)\.xml$"                        # group2: everything else up to “.xml”
                ),
                "extract": lambda m: {
                    "REQID": m.group(1),
                    "DeliveryNoteDocName": m.group(2),
                }
            },
            {
                "name": "Imprest_Journal_Details",
                "pattern": re.compile(r"^TallyXMLResponse-Imprest_Journal_ClientRequestID_.*_JournalID_(\d+)_JID_(\d+)\.xml$"),
                "extract": lambda m: {
                    "iJournalID": int(m.group(1)),
                    "iJID": int(m.group(2))
                }
            }

        ]

        for entry in patterns:
            match = re.match(entry["pattern"], filename)
            if match:
                dictDocDetails[entry["name"]] = entry["extract"](match)
                return dictDocDetails

        raise ValueError("Filename does not match any expected pattern.")
    
    @staticmethod
    def MSExtractTransactionDetailsFromFileName(file_name: str) -> dict:
        """
        Extracts transaction ID and statement ID from a well-formed filename string.

        Args:
            file_name (str): The full filename string, e.g.
                'TallyXMLResponse-TallyXML_Bank_Statement_..._169_52355.xml'

        Returns:
            dict: {
                'iTransactionId': int,
                'iStatementId': int
            }

        Raises:
            ValueError: If the filename does not contain at least two numeric parts at the end.
        """
        import os

        try:
            # Extract filename without path or extension
            base_name = os.path.splitext(os.path.basename(file_name))[0]

            # Split by underscores
            parts = base_name.split('_')

            if len(parts) < 2:
                raise ValueError("Filename format is invalid: not enough parts")

            # Get last and second-to-last parts
            iTransactionId = int(parts[-1])
            iStatementId = int(parts[-2])

            return iStatementId, iTransactionId

        except (IndexError, ValueError) as e:
            raise ValueError(f"Failed to extract IDs from filename: {file_name}. Error: {e}")

class CStringFormat:

    @staticmethod
    def MSFormatINRAmount(amount) -> str:
        """
        Formats a number into Indian Rupee format using Babel.
        Removes .00 if the amount is a whole number.
        """
        try:
            amount_float = float(amount)
            formatted = format_currency(amount_float, 'INR', locale='en_IN')
            # Remove decimal part if .00
            if formatted.endswith('.00'):
                formatted = formatted[:-3]
            return formatted.strip()  # Remove ₹ symbol if not needed
        except (ValueError, TypeError):
            return "₹" + str(amount)

class CModelsHelper:
    
    @staticmethod
    def MSConvertDictToModels(data: Dict) -> Dict[str, Union[List[ModelFieldItem], List[ModelTable]]]:
        field_items = [ModelFieldItem(**field) for field in data['Fields']]
        table_items = [ModelTable(TableName=table['TableName'], 
                                Fields=[ModelFieldItem(**field) for field in table['Fields']]) for table in data['Tables']]
        return {'Fields': field_items, 'Tables': table_items}

class CExcelHelper:

    def read_file(strFilePath, table_index=1):
        """
        Reads a file and returns a pandas DataFrame, supporting both Excel and HTML formats.
        
        Parameters:
        - strFilePath (str): Path to the file.
        - table_index (int): Index of the table/sheet to extract (default is 1).
        
        Returns:
        - pd.DataFrame: A DataFrame containing the table data, or None if reading fails.
        """
        try:
            # Step 1: Try reading as Excel with different engines
            for engine in ['xlrd', 'openpyxl']:
                try:
                    df = pd.read_excel(strFilePath, engine=engine, sheet_name=table_index)
                    df.dropna(how='all', inplace=True)  # Remove fully empty rows
                    df.reset_index(drop=True, inplace=True)  # Reset index
                    print(f"Successfully read as Excel file using {engine} engine.")
                    return df
                except Exception as e:
                    print(f"Failed to read as Excel with {engine}: {e}. Trying next engine...")
            
            print("Excel reading failed. Attempting to read as HTML...")
            
            # chardet TAKES AROUND 10 SECONDS TO FIGUREOUT THE ENCODING,
            # FOR SPECIFIC SCENARIOS WE CAN REPLACE THE ENCODING WITH FIXED FORMAT,
            # USE CAN RUN THE BELOW CODE ONCE AND GET THE ENCODING AND SPECIFY THE ENCODING BY DEFAULT

            # Step 2: Detect encoding for potential HTML file
            with open(strFilePath, 'rb') as file:
                result = chardet.detect(file.read())
                encoding = result.get('encoding', 'utf-8')
                print(f"Detected encoding: {encoding}")
            
            # Step 3: Read file content with detected encoding
            try:
                with open(strFilePath, encoding=encoding, errors='replace') as file:
                    html_content = file.read()
            except UnicodeDecodeError as ue:
                print(f"Decoding error with detected encoding '{encoding}': {ue}")
                raise
            
            # Step 4: Parse HTML content
            html_io = StringIO(html_content)
            tables = pd.read_html(html_io, flavor='html5lib')  # <-- specify html5lib
            if len(tables) <= table_index:
                raise ValueError(f"Table index {table_index} not found. Only {len(tables)} tables detected.")

            df = tables[table_index]
            df.dropna(how='all', inplace=True)
            df.reset_index(drop=True, inplace=True)
            print("Successfully read as HTML file.")
            return df
        
        except UnicodeDecodeError:
            # Step 5: Handle encoding errors by trying common alternatives
            print(f"Encoding {encoding} failed. Trying alternative encodings...")
            for enc in ['utf-8', 'windows-1252', 'latin1']:
                try:
                    with open(strFilePath, encoding=enc) as file:
                        html_content = file.read()
                    html_io = StringIO(html_content)
                    tables = pd.read_html(html_io)
                    if len(tables) <= table_index:
                        raise ValueError(f"Table index {table_index} not found. Only {len(tables)} tables detected.")
                    df = tables[table_index]
                    df.dropna(how='all', inplace=True)
                    df.reset_index(drop=True, inplace=True)
                    print(f"Successfully read as HTML file with {enc} encoding.")
                    return df
                except UnicodeDecodeError:
                    continue
            raise ValueError("Unable to decode file with available encodings.")
        
        except Exception as e:
            print(f"Error processing file: {e}")
            return None

    def read_GRN_Updated_Format(strFilePath, table_index=1, grn_date_as_datetime=False):
            """
            Reads a file and returns a pandas DataFrame with specified columns, supporting both Excel and HTML formats.
            Ensures consistent value capture by parsing as strings, stripping whitespace, and converting to appropriate types.
            Converts 'Party Bill Date' to datetime, and 'GRN Date' to date (or datetime if grn_date_as_datetime=True).
            Handles GRN Date' formats DD/MM/YYYY and DD-MM-YYYY.
            
            Parameters:
            - strFilePath (str): Path to the file.
            - table_index (int): Index of the table/sheet to extract (default is 1).
            - grn_date_as_datetime (bool): If True, GRN Date is datetime64[ns]; if False, object with datetime.date (default).
            
            Returns:
            - pd.DataFrame or None
        """
            try:
                mime_type = magic.from_file(strFilePath, mime=True)
                
                # Define desired columns mapping (input column name -> output column name)
                # UNIT.1 this naming convention used in python when two columns have same name
                new_columns = {
                    "GRN NO.": "GRN No",
                    "GRN DATE": "GRN Date",
                    "PARTY BILL NO": "Party Bill No",
                    "PARTY BILL DATE": "Party Bill Date",
                    "CARRYING VEHICLE NO.": "Veh No",
                    "CHALLAN NO.": "Ch No",
                    "PO NO.": "PO No",
                    "INDENT NO.": "Indent No",
                    "PARTY": "Party",
                    "GRN REMARK": "Remarks",
                    "ITEM NAME": "Item",
                    "QTY": "Stock Qty",
                    "UNIT.1": "Unit",
                    "WEIGHT": "Weight",
                    "RATE": "Rate",
                    "AMOUNT": "Amount",
                    "RST NO.": "RST NO"
                }
                
                if "html" in mime_type:
                    
                    print("File detected as HTML. Reading as HTML...")
                    with open(strFilePath, 'rb') as file:
                        result = chardet.detect(file.read())
                        encoding = result['encoding']
                        print(f"Detected encoding: {encoding}")

                    with open(strFilePath, encoding=encoding) as file:
                        html_content = file.read()

                    html_io = StringIO(html_content)
                    tables = pd.read_html(html_io)
                    if table_index >= len(tables):
                        raise ValueError(f"Table index {table_index} out of range. Found {len(tables)} tables.")
                    df = tables[table_index]
                    df.dropna(how='all', inplace=True)
                    df.reset_index(drop=True, inplace=True)
                    

                elif "excel" in mime_type or os.path.splitext(strFilePath)[1] in ['.xls', '.xlsx']:
                    print("File detected as Excel. Attempting to read...")
                    for engine in ['openpyxl', 'xlrd']:
                        try:
                            df = pd.read_excel(strFilePath, engine=engine, sheet_name=table_index)
                            df.dropna(how='all', inplace=True)
                            df.reset_index(drop=True, inplace=True)
                            print(f"Successfully read as Excel using {engine}")
                            # return df
                        except Exception as e:
                            print(f"Failed with {engine}: {e}")
                    
                    raise ValueError("Failed to read Excel file with available engines.")
                else:
                    raise ValueError("Unsupported file type.")
                
                # Strip whitespace from all string columns
                for col in df.columns:
                    if df[col].dtype == 'string' or df[col].dtype == object:
                        df[col] = df[col].str.strip()
                
                
                
                # Create column mapping (case-insensitive)
                column_mapping = {}
                unit_count = 0
                available_columns = [col.strip() for col in df.columns]  # Strip whitespace from column names
                for idx, col in enumerate(available_columns):
                    # Find matching column in new_columns (case-insensitive)
                    for input_col, output_col in new_columns.items():
                        if col.upper() == input_col.upper():
                            column_mapping[col] = output_col
                            break

                # Check if all required columns (except second UNIT) are found
                expected_columns = set(new_columns.values()) 
                found_columns = set(column_mapping.values())
                missing_columns = expected_columns - found_columns
                if missing_columns:
                    print(f"Warning: Missing columns in input file: {missing_columns}")

                # Select and rename columns
                if not column_mapping:
                    raise ValueError("No matching columns found in the input file.")
                
                selected_columns = list(column_mapping.keys())
                df = df[selected_columns]
                df.columns = [column_mapping[col] for col in selected_columns]
                
                # Convert date columns
                try:
                    if 'Party Bill Date' in df.columns:
                        df['Party Bill Date'] = pd.to_datetime(df['Party Bill Date'], errors='coerce')
                        if df['Party Bill Date'].isna().all():
                            print("Warning: All 'Party Bill Date' values are invalid and converted to NaT.")
                    else:
                        print("Warning: 'Party Bill Date' column not found in DataFrame.")

                    if 'GRN Date' in df.columns:
                        # Try DD/MM/YYYY and DD-MM-YYYY formats
                        grn_date_series = df['GRN Date']
                        converted_dates = pd.to_datetime(
                            grn_date_series,
                            format='%d/%m/%Y',  # DD/MM/YYYY (e.g., 20/06/2025)
                            errors='coerce'
                        ).combine_first(
                            pd.to_datetime(
                                grn_date_series,
                                format='%d-%m-%Y',  # DD-MM-YYYY (e.g., 20-06-2025)
                                errors='coerce'
                            )
                        )
                        
                        df['GRN Date'] = converted_dates
                        if grn_date_as_datetime:
                            # Keep as datetime64[ns]
                            if df['GRN Date'].isna().all():
                                print("Warning: All 'GRN Date' values are invalid and converted to NaT.")
                        else:
                            # Convert to datetime.date (object dtype)
                            df['GRN Date'] = df['GRN Date'].dt.date
                            if df['GRN Date'].isna().all():
                                print("Warning: All 'GRN Date' values are invalid and converted to NaT.")
                            
                    # Numeric columns
                    numeric_columns = ['Stock Qty', 'Weight', 'Rate', 'Amount']
                    for col in numeric_columns:
                        if col in df.columns:
                            df[col] = pd.to_numeric(df[col], errors='coerce')
                            if df[col].isna().all():
                                print(f"Warning: All '{col}' values are invalid and converted to NaN.")
                except Exception as e:
                    print(f"Error converting date columns: {e}")

                return df
                

            except Exception as e:
                print(f"Error: {e}")
                return None
        

    def read_file_temp(strFilePath, table_index=1):
        """
        Reads a file and returns a pandas DataFrame, supporting both Excel and HTML formats.
        
        Parameters:
        - strFilePath (str): Path to the file.
        - table_index (int): Index of the table/sheet to extract (default is 1).
        
        Returns:
        - pd.DataFrame or None
        """
        try:
            mime_type = magic.from_file(strFilePath, mime=True)
            print(f"Detected MIME type: {mime_type}")

            if "html" in mime_type:
                print("File detected as HTML. Reading as HTML...")
                with open(strFilePath, 'rb') as file:
                    result = chardet.detect(file.read())
                    encoding = result['encoding']
                    print(f"Detected encoding: {encoding}")

                with open(strFilePath, encoding=encoding) as file:
                    html_content = file.read()

                html_io = StringIO(html_content)
                tables = pd.read_html(html_io)
                if table_index >= len(tables):
                    raise ValueError(f"Table index {table_index} out of range. Found {len(tables)} tables.")
                df = tables[table_index]
                df.dropna(how='all', inplace=True)
                df.reset_index(drop=True, inplace=True)
                return df

            elif "excel" in mime_type or os.path.splitext(strFilePath)[1] in ['.xls', '.xlsx']:
                print("File detected as Excel. Attempting to read...")
                for engine in ['openpyxl', 'xlrd']:
                    try:
                        df = pd.read_excel(strFilePath, engine=engine, sheet_name=table_index)
                        df.dropna(how='all', inplace=True)
                        df.reset_index(drop=True, inplace=True)
                        print(f"Successfully read as Excel using {engine}")
                        return df
                    except Exception as e:
                        print(f"Failed with {engine}: {e}")
                raise ValueError("Failed to read Excel file with available engines.")
            else:
                raise ValueError("Unsupported file type.")

        except Exception as e:
            print(f"Error: {e}")
            return None

    def read_excel_sheets(file_path, sheet_names):
        # If a single sheet name is passed as a string, convert it to a list
        if isinstance(sheet_names, str):
            sheet_names = [sheet_names]
        
        # Read the Excel file for the specified sheets
        data = pd.read_excel(file_path, sheet_name=sheet_names)
        
        # Return a list of dataframes in the order of sheet_names
        return [data[sheet] for sheet in sheet_names]

    @staticmethod
    def MSFormatExcel(
            sExcelFilePath=None,
            oExcelSheet=None,
            bIsSheetObject=True,
            lsWordWrapColumns=None,
            column_width_mapping=None,
            auto_adjust_width=False,  # New flag to auto-adjust column width
            lsSheetNames=None  # New: List of sheet names to format if an Excel file is passed
        ):
        """
            Input:

                1) sExcelFilePath: String representing the Excel file path

                2) oExcelSheet: Excel sheet object (optional if bIsSheetObject is True)

                3) bIsSheetObject: Boolean indicating if given data is an object (True) or file path (False)

                4) lsWordWrapColumns: List of column names where text should be wrapped

                5) column_width_mapping: Dictionary of column names and their desired widths, e.g., {'Matched PricelistItem Property': 50, 'Qty(Box)': 15}

                6) auto_adjust_width: Boolean flag to enable automatic adjustment of column width based on cell content

                7) lsSheetNames: List of sheet names to apply formatting if an Excel file is provided
                
            Output:

                - Returns the formatted Excel file path if input was a file path.
                
                - Returns the modified Excel sheet object if input was an object.

            Purpose:

                To format specific sheets of an Excel file by setting a bold font for the header row, applying borders to all cells,
                auto-adjusting column widths based on content (if enabled), and wrapping text in specified columns.
        """
        
        # Load the Excel file if bIsSheetObject is False
        if not bIsSheetObject:
            wb = load_workbook(sExcelFilePath)

            # If no specific sheet names are provided, default to all sheets in the workbook
            sheets_to_format = wb.sheetnames if not lsSheetNames else lsSheetNames

            for sheet_name in sheets_to_format:
                if sheet_name in wb.sheetnames:
                    oExcelSheet = wb[sheet_name]
                else:
                    raise ValueError(f"Sheet '{sheet_name}' not found in the workbook.")

                # Define styles
                header_font = Font(name="Arial", size=11, bold=True)  # Bold font only for headers
                border_style = Border(
                    left=Side(style="thin"), right=Side(style="thin"),
                    top=Side(style="thin"), bottom=Side(style="thin")
                )
                word_wrap = Alignment(wrap_text=True)

                # Apply formatting and adjust column widths
                for col in oExcelSheet.iter_cols():
                    col_letter = col[0].column_letter  # Get column letter
                    col_name = oExcelSheet.cell(row=1, column=col[0].column).value  # Get column name

                    # Initialize max length for auto-width adjustment
                    max_length = 0
                    
                    # Apply border to all cells and calculate max length
                    for cell in col:
                        cell.border = border_style
                        if cell.value:
                            max_length = max(max_length, len(str(cell.value)))
                    
                    # Apply bold font to header row only
                    header_cell = oExcelSheet.cell(row=1, column=col[0].column)
                    header_cell.font = header_font
                    
                    # Apply word wrap to specified columns
                    if lsWordWrapColumns and col_name in lsWordWrapColumns:
                        for cell in col:
                            cell.alignment = word_wrap
                    
                    # Adjust column width based on the mapping
                    if column_width_mapping and col_name in column_width_mapping:
                        oExcelSheet.column_dimensions[col_letter].width = column_width_mapping[col_name]
                    
                    # Auto-adjust column width if enabled and column is not in lsWordWrapColumns
                    if auto_adjust_width and (not lsWordWrapColumns or col_name not in lsWordWrapColumns):
                        oExcelSheet.column_dimensions[col_letter].width = max_length + 2  # Add padding for better spacing

            # Save the workbook
            wb.save(sExcelFilePath)
            return sExcelFilePath
        else:
            if oExcelSheet is None:
                raise ValueError("No valid Excel sheet object provided.")

            # Define styles
            header_font = Font(name="Arial", size=11, bold=True)  # Bold font only for headers
            border_style = Border(
                left=Side(style="thin"), right=Side(style="thin"),
                top=Side(style="thin"), bottom=Side(style="thin")
            )
            word_wrap = Alignment(wrap_text=True)

            # Apply formatting and adjust column widths
            for col in oExcelSheet.iter_cols():
                col_letter = col[0].column_letter  # Get column letter
                col_name = oExcelSheet.cell(row=1, column=col[0].column).value  # Get column name

                # Initialize max length for auto-width adjustment
                max_length = 0
                
                # Apply border to all cells and calculate max length
                for cell in col:
                    cell.border = border_style
                    if cell.value:
                        max_length = max(max_length, len(str(cell.value)))
                
                # Apply bold font to header row only
                header_cell = oExcelSheet.cell(row=1, column=col[0].column)
                header_cell.font = header_font
                
                # Apply word wrap to specified columns
                if lsWordWrapColumns and col_name in lsWordWrapColumns:
                    for cell in col:
                        cell.alignment = word_wrap
                
                # Adjust column width based on the mapping
                if column_width_mapping and col_name in column_width_mapping:
                    oExcelSheet.column_dimensions[col_letter].width = column_width_mapping[col_name]
                
                # Auto-adjust column width if enabled and column is not in lsWordWrapColumns
                if auto_adjust_width and (not lsWordWrapColumns or col_name not in lsWordWrapColumns):
                    oExcelSheet.column_dimensions[col_letter].width = max_length + 2  # Add padding for better spacing

            # Return the modified sheet object
            return oExcelSheet

    @staticmethod
    def MSWriteToXLSX(file_path, data, column, sheet_name=None):
        """
        Append data to a specific column in an Excel file using pandas.

        Parameters:
            file_path (str): Path to the .xlsx file.
            data (list): List of data to append to the column.
            column (str): Column name where data should be written.
            sheet_name (str, optional): Name of the sheet to write to. Default is 'Sheet1'.

        Returns:
            None
        """
        try:
            if not data:
                print("No data to be added to the Excel sheet.")
                return

            # Default to "Sheet1" if no sheet_name is provided
            if not sheet_name:
                sheet_name = "Sheet1"

            # Load the workbook or create a new one if it doesn't exist
            try:
                workbook = load_workbook(file_path)
                if sheet_name in workbook.sheetnames:
                    # Read the specific sheet into a DataFrame
                    excel_data = pd.read_excel(file_path, sheet_name=sheet_name, engine='openpyxl')
                else:
                    excel_data = pd.DataFrame()  # Start with an empty DataFrame if the sheet doesn't exist
            except FileNotFoundError:
                workbook = None
                excel_data = pd.DataFrame()

            # Ensure the column exists or initialize a new one
            if column not in excel_data.columns:
                excel_data[column] = pd.Series(dtype='str')

            # Combine existing data with new data
            existing_data = excel_data[column].dropna().tolist()
            combined_data = existing_data + data

            # Create a DataFrame for the new column data
            combined_df = pd.DataFrame({column: combined_data})

            # Update the DataFrame with the new data
            for col in excel_data.columns:
                if col != column:
                    combined_df[col] = excel_data[col]

            # Save the updated DataFrame back to the file
            with pd.ExcelWriter(file_path, engine='openpyxl', mode='a', if_sheet_exists='replace') as writer:
                combined_df.to_excel(writer, index=False, sheet_name=sheet_name)

            print(f"Data successfully written to '{file_path}' in sheet '{sheet_name}', column '{column}'.")

        except Exception as e:
            print(f"An error occurred: {e}")

    @staticmethod
    def MSReadExcel(file_path: str, columns_to_verify: list[str] = [], bVerify: bool = False) -> pd.DataFrame:
        """
        Reads an Excel file and verifies specified columns.

        Args:
            file_path (str): The path to the Excel file.
            columns_to_verify (list[str]): A list of column names to verify in the Excel file.
            bVerify (bool): If True, verify that the specified columns exist in the Excel file.

        Returns:
            pd.DataFrame: A DataFrame containing the data from the Excel file.

        Raises:
            ValueError: If any of the specified columns are missing in the Excel file.
        """
        try:
            # Read the Excel file into a DataFrame
            df = pd.read_excel(file_path)

            if bVerify:
                # Find missing columns
                missing_columns = [col for col in columns_to_verify if col not in df.columns]

                # Raise an error if there are missing columns
                if missing_columns:
                    raise HTTPException(status_code=422, detail=f"The following columns are missing in the file: {', '.join(missing_columns)}")

            return df
        except ValueError as ve:
            raise ve
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail=f"The file at path '{file_path}' does not exist.")
        except Exception as e:
            if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            else:
                raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")


    def MSExcelToJSON(df: pd.DataFrame = None, file_path: str = None) -> str:
        """
        Converts a DataFrame or Excel file content to a JSON string.

        Args:
            df (pd.DataFrame, optional): The DataFrame to convert to JSON.
            file_path (str, optional): The path to the Excel file to convert to JSON.

        Returns:
            str: A JSON string representation of the data.

        Raises:
            ValueError: If both df and file_path are provided or neither is provided.
        """
        if (df is not None and file_path is not None) or (df is None and file_path is None):
            raise ValueError("Either a DataFrame or a file path must be provided, but not both.")

        if file_path:
            try:
                df = pd.read_excel(file_path)
            except FileNotFoundError:
                raise FileNotFoundError(f"The file at path '{file_path}' does not exist.")
            except Exception as e:
                raise RuntimeError(f"An error occurred while reading the file: {e}")

        try:
            json_string = df.to_json(orient='records')
            return json.loads(json_string)
        except Exception as e:
            raise RuntimeError(f"An error occurred while converting the data to JSON: {e}")




class CExtractedDataHelper:
    """
        This class contains all the helper methods related to te data extraction process
    """
    
    @staticmethod
    async def MSInitializeVerificationStatus(data):
        """
            Purpose: Used to Initialize User Approved Fields and Individual Table Cells.
        """
        # Initialize the output dictionary
        output = {'Fields': [], 'Tables': []}

        # Process fields, setting all field values to '0'
        for field in data['Fields']:
            for key in field:
                output['Fields'].append({key : 0})

        # Process tables, setting all cells in all rows to '0'
        for table in data['Tables']:
            table_init = {}
            for table_name, rows in table.items():
                if isinstance(rows, list):  # Check if the value is a list of rows
                    initialized_rows = []
                    for row in rows:
                        initialized_row = {}
                        for cell_name in row:
                            initialized_row[cell_name] = 0
                        initialized_rows.append(initialized_row)
                    table_init[table_name] = initialized_rows
                else:
                    # If no detailed rows are specified, just initialize the table to '0'
                    table_init[table_name] = 0
            output['Tables'].append(table_init)

        return output


    @staticmethod
    async def MSGetVerifiedData(verification_data, data_to_verify, bApprove):
        """
            Update the verification status of fields and tables based on user input.
            Now supports individual cell verification within tables.
        """
        # Verify fields
        if 'Fields' in data_to_verify:
            for field in data_to_verify['Fields']:
                for key, value in field.items():
                    for idx, verified_field in enumerate(verification_data['Fields']):
                        if key in verified_field:
                            if bApprove:
                                verification_data['Fields'][idx][key] = 1
                            else:
                                verification_data['Fields'][idx][key] = 0

        # Verify tables and individual cells within tables
        if 'Tables' in data_to_verify:
            for update_table in data_to_verify['Tables']:
                update_table_name = list(update_table.keys())[0]
                for update_row in update_table[update_table_name]:
                    row_index = update_row["rowIndex"]
                    update_values = update_row["values"]
                    # Find the corresponding table in verification_data
                    for verified_table in verification_data['Tables']:
                        if update_table_name in verified_table:
                            # Access the specific row by index
                            if row_index < len(verified_table[update_table_name]):
                                verified_row = verified_table[update_table_name][row_index]
                                # Update the cell values to 1 if the key matches
                                for cell_key in update_values.keys():
                                    if cell_key in verified_row:
                                        if bApprove:
                                            verified_row[cell_key] = 1
                                        else:
                                            verified_row[cell_key] = 0
        return verification_data
    

    @staticmethod
    def MSConvertToFile(dictJsonData, fileType):
        try:
            # Process fields into a single dictionary
            fields = dictJsonData['Fields']
            tables = dictJsonData.get('Tables', [])

            fields_data = {list(field.keys())[0]: list(field.values())[0] for field in fields}

            # Initialize a list to hold all rows of data
            all_rows = []

            # Create a base dictionary for fields
            base_fields_dict = {key: value for key, value in fields_data.items()}

            if tables:
                # Combine all table rows for each entry into a single dictionary
                combined_table_data = {}
                for table in tables:
                    for table_name, rows in table.items():
                        for i, row in enumerate(rows):
                            if i not in combined_table_data:
                                combined_table_data[i] = {**base_fields_dict}
                            for key, value in row.items():
                                combined_table_data[i][f"{table_name}_{key}"] = value
                # Convert the combined data into rows
                for key in combined_table_data:
                    all_rows.append(combined_table_data[key])
            else:
                # If no tables, just use the fields data
                all_rows.append(base_fields_dict)

            # Create DataFrame from all rows
            final_df = pd.DataFrame(all_rows)

            output = io.BytesIO()
            if fileType.lower() == "excel":
                # Save to Excel in memory
                final_df.to_excel(output, index=False)
                output.seek(0)
                media_type = 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet'
                
            elif fileType.lower() == "csv":
                # Save to CSV in memory
                final_df.to_csv(output, index=False)
                output.seek(0)
                media_type = 'text/csv'
            else:
                raise HTTPException(status_code=400, detail="Invalid file type requested")
            
            file_data_base64 = base64.b64encode(output.getvalue()).decode('utf-8')

            return {
                    'FileData': file_data_base64,
                    'MediaType': media_type
                }
            
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
        
        
class CExtractionHelper():
    
    @staticmethod
    def MSConvertDictToListOfDict(input_data):
        # Initialize the list of dictionaries
        output_list = []

        # Loop through each key in the input dictionary
        if isinstance(input_data,dict):
            for key, value in input_data.items():
                # Create a dictionary with the key and its corresponding value
                output_dict = {key: value}
                # Append the dictionary to the output list
                output_list.append(output_dict)

            return output_list
        else:
            raise ValueError(f"Invalid Data type passed to MSConvertDictToListOfDict : {type(input_data)}")


class CDictHelper:

    @staticmethod
    def merge_dicts(dictOriginal, dictUpdates):
        for key, value in dictUpdates.items():
            if key in dictOriginal:
                if key == "Tables":
                    original_tables = {list(item.keys())[0]: item for item in dictOriginal[key]}
                    update_tables = {list(item.keys())[0]: item for item in value}
                    for table_name, table_data in update_tables.items():
                        if table_name in original_tables:
                            original_table_data = original_tables[table_name][table_name]
                            update_table_data = table_data[table_name]
                            update_rows = {item['rowIndex']: item for item in update_table_data}
                            for row_index, row_data in update_rows.items():
                                for cellKey, cellValue in row_data['values'].items():
                                    if original_table_data[row_index][cellKey] != cellValue:
                                        original_table_data[row_index][cellKey] = cellValue
                        else:
                            original_tables[table_name] = table_data
                    dictOriginal[key] = list(original_tables.values())

                elif key == "Fields":
                    original_fields = {list(item.keys())[0]: item for item in dictOriginal[key] if isinstance(item, dict)}
                    for item in value:
                        field_name = list(item.keys())[0]
                        field_value = item[field_name]
                        if field_name in original_fields:
                            original_field_value = original_fields[field_name][field_name]
                            if original_field_value[0] != field_value:
                                original_fields[field_name] = {
                                    field_name: (
                                        field_value if isinstance(field_value, list) and len(field_value) == 6
                                        else [field_value, 0, 0, 0, 0, 0]
                                    )
                                }
                        else:
                            original_fields[field_name] = {
                                field_name: (
                                    field_value if isinstance(field_value, list) and len(field_value) == 6
                                    else [field_value, 0, 0, 0, 0, 0]
                                )
                            }
                    dictOriginal[key] = list(original_fields.values())

                elif isinstance(dictOriginal[key], dict) and isinstance(value, dict):
                    CDictHelper.merge_dicts(dictOriginal[key], value)
                else:
                    if dictOriginal[key] != value:
                        dictOriginal[key] = {key: [value, 0, 0, 0, 0, 0]}
            else:
                dictOriginal[key] = {key: [value, 0, 0, 0, 0, 0]}

# Convert string to date
class DateHelper:

    # Date Formats
    db_date_format = "%Y-%m-%d"

    def string_to_date(date: str, date_format: str = db_date_format):
        return datetime.strptime(date, date_format).date()
    
    def get_current_timestamp(format_str="%Y-%m-%d %H:%M:%S"):
        """
        Returns the current timestamp as a string based on the provided format.

        :param format_str: A string representing the desired format.
        :return: Formatted current timestamp string.
        """
        return datetime.now().strftime(format_str)
    
    @staticmethod
    def MSConvertIntToDateFromDDMMYYYY(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats to a standard MM.DD.YYYY format.

        Supported formats:
        - DDMMYYYY
        - DDMMYY
        - DMMYY
        - DMMYYYY

        Note:
            Month: One or two digits
            Day: Two digits
            Year: Two or four digits
        """
        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 6:
                # Format: DDMMYY (e.g., 080524 -> 08.05.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = '20' + invoice_str[4:]
            elif length == 8:
                # Format: DDMMYYYY (e.g., 08102024 -> 08.10.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = invoice_str[4:]
            elif length == 5:
                # Format: DMMYY (e.g., 80524 -> 08.05.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = '20' + invoice_str[3:]
            elif length == 7:
                # Format: DMMYYYY (e.g., 8102024 -> 08.10.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = invoice_str[3:]
            else:
                raise ValueError(f"Invalid Date Format Provided '{invoice_date}'.")

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string based on the day of the month
            if day_int <= 12:
                converted_date = f"{year_int}{month_int:02}{day_int:02}"  # YYYY.MM.DD.
            else:
                converted_date = f"{year_int}{month_int:02}{day_int:02}" # YYYY.MM.DD.
            
            return converted_date
        except Exception as e:
            raise ValueError(f"Invalid Date Format Provided '{invoice_date}'.")

    @staticmethod
    def MSConvertIntToDateObject(invoice_date: int) -> date:
        """
        Convert an integer representing a date in various formats to a standard date object.

        Supported formats:
        - DDMMYYYY
        - DDMMYY
        - DMMYY
        - DMMYYYY

        Returns:
            date: A valid Python `date` object.

        Raises:
            ValueError: If the provided date is invalid.
        """
        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 6:
                # Format: DDMMYY (e.g., 080524 -> 08-May-2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = '20' + invoice_str[4:]
            elif length == 8:
                # Format: DDMMYYYY (e.g., 08102024 -> 08-Oct-2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = invoice_str[4:]
            elif length == 5:
                # Format: DMMYY (e.g., 80524 -> 08-May-2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = '20' + invoice_str[3:]
            elif length == 7:
                # Format: DMMYYYY (e.g., 8102024 -> 08-Oct-2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = invoice_str[3:]
            else:
                raise ValueError(f"Invalid Date Format Provided '{invoice_date}'.")

            # Convert extracted strings to integers
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate and return a date object
            return date(year_int, month_int, day_int)
            
        except Exception as e:
            print("Date Conversion failed, taking todays date.")
            return date(datetime.now().year, datetime.now().month, datetime.now().day)
       
    @staticmethod
    def convert_date(date_str):
        formats = [
            "%d/%b/%Y",  # 02/Mar/2025,
            "%d-%b-%Y",  # 30-Mar-2025
            "%d-%b-%y",  # 30-Mar-25
            "%d/%m/%Y",  # 02/04/2025
            "%d/%m/%y",  # 02/04/25
            "%d-%m-%Y",  # 30-03-2025
            "%d-%m-%y",  # 30-03-25
            "%d-%B-%Y",  # 30-March-2025
            "%Y-%m-%d",  # 2025-03-30
        ]
        
        for fmt in formats:
            try:
                return datetime.strptime(date_str.strip(), fmt).strftime("%Y%m%d")
            except ValueError:
                continue
        raise ValueError(f"Unsupported date format: {date_str}")


    @staticmethod
    def get_time_difference(record_dict):
        """
        Input:
            record_dict: dict
            A dictionary containing 'created_at' and 'ResponseAt' keys which can be either
            datetime objects or strings in the format "%Y-%m-%d %H:%M:%S" or "%Y-%m-%d %H:%M:%S.%f".

        Output:
            str: Time difference between 'created_at' and 'ResponseAt' in the format
                "X min:Y sec", or "-" if inputs are missing, invalid, or if the difference is negative.

        Purpose:
            To compute and display the time difference between creation and response
            timestamps of a record, handling both datetime and string formats with or without microseconds.

        Example:
            Input: {
                "created_at": "2025-04-01 23:28:15",
                "ResponseAt": "2025-04-01 23:30:45"
            }
            Output: "2 min:30 sec"
        """
        if record_dict is None or "created_at" not in record_dict or "ResponseAt" not in record_dict:
            return "-"
        
        created_at = record_dict["created_at"]
        response_at = record_dict["ResponseAt"]

        if created_at is None or response_at is None:
            return "-"

        try:
            # Convert strings to datetime if necessary
            if isinstance(created_at, str):
                try:
                    created_at = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    created_at = datetime.strptime(created_at, "%Y-%m-%d %H:%M:%S")
            if isinstance(response_at, str):
                try:
                    response_at = datetime.strptime(response_at, "%Y-%m-%d %H:%M:%S.%f")
                except ValueError:
                    response_at = datetime.strptime(response_at, "%Y-%m-%d %H:%M:%S")

            time_diff = response_at - created_at
            total_seconds = time_diff.total_seconds()

            if total_seconds < 0:
                return "-"

            minutes = int(total_seconds // 60)
            seconds = int(total_seconds % 60)

            # Pad seconds with leading zero if needed
            return f"{minutes} min:{seconds:02d} sec"
        
        except Exception:
            return "-"

    @staticmethod
    async def MSConvertIntToDateFromYYYYMMDD(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats (YYYYMMDD, YYMMDD, etc.)
        to MM.DD.YYYY format.

        Supported formats:
        - 8 digits: YYYYMMDD
        - 7 digits: YYYYMD or YYMMDD
        - 6 digits: YYMMDD or YMMDD
        """

        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 8:
                # Format: YYYYMMDD (e.g., 20250125 -> 01.25.2025)
                year = invoice_str[:4]
                month = invoice_str[4:6]
                day = invoice_str[6:]

            elif length == 7:
                # Format: YYYYMD or YYMMDD (e.g., 2025015 -> 01.05.2025)
                if invoice_str[4] in '0123456789' and len(invoice_str[5:]) == 1:
                    # Case where year is in YYYY and month is a single digit
                    year = invoice_str[:4]
                    month = invoice_str[4:5]
                    day = invoice_str[5:]
                else:
                    # Case where year is in YY
                    year = '20' + invoice_str[:2]
                    month = invoice_str[2:4]
                    day = invoice_str[4:]

            elif length == 6:
                # Format: YYMMDD or YMMDD (e.g., 240825 -> 08.25.2024)
                if invoice_str[2] in '0123456789':
                    # Case where year is in YY
                    year = '20' + invoice_str[:2]
                    month = invoice_str[2:4]
                    day = invoice_str[4:]
                else:
                    # Case where year is in Y and month is a single digit
                    year = '20' + invoice_str[0]
                    month = invoice_str[1:2]
                    day = invoice_str[2:]

            else:
                await CLogController.MSWriteLog(None, "Error", f"Invalid date format received: {invoice_date}")
                return "Invalid date format"

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string in MM.DD.YYYY format
            converted_date = f"{month_int:02}.{day_int:02}.{year_int}"
            await CLogController.MSWriteLog(
                None, "Info", f"Converted integer date {invoice_date} to string date {converted_date}."
            )
            return converted_date
        except ValueError as ve:
            await CLogController.MSWriteLog(None, "Error", f"ValueError in MSConvertIntToDate: {ve}")
            return "Invalid date format"
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Exception in MSConvertIntToDate: {str(e)}")
            return "Invalid date format"



        
    @staticmethod
    async def MSUserReadableDate(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats to a standard MM.DD.YYYY format.

        Supported formats:
        - DDMMYYYY
        - DDMMYY
        - DMMYY
        - DMMYYYY

        Note:
            Month: One or two digits
            Day: Two digits
            Year: Two or four digits
        """
        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 6:
                # Format: DDMMYY (e.g., 080524 -> 08.05.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = '20' + invoice_str[4:]
            elif length == 8:
                # Format: DDMMYYYY (e.g., 08102024 -> 08.10.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = invoice_str[4:]
            elif length == 5:
                # Format: DMMYY (e.g., 80524 -> 08.05.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = '20' + invoice_str[3:]
            elif length == 7:
                # Format: DMMYYYY (e.g., 8102024 -> 08.10.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = invoice_str[3:]
            else:
                await CLogController.MSWriteLog(None, "Error", f"Invalid date format received: {invoice_date}")
                return "Invalid date format"

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string in the format MM.DD.YYYY
            converted_date = f"{month_int:02}.{day_int:02}.{year_int}"
            await CLogController.MSWriteLog(
                None, "Info", f"Converted integer date {invoice_date} to string date {converted_date}."
            )
            return converted_date
        except ValueError as ve:
            await CLogController.MSWriteLog(None, "Error", f"ValueError in MSConvertIntToDate: {ve}")
            return "Invalid date format"
        except Exception as e:
            await CLogController.MSWriteLog(0, "Error", f"Exception in MSConvertIntToDate: {str(e)}")
            return "Invalid date format"
        
class CComparator:

    @staticmethod
    def MSAreNumbersClose(val1, val2, allowed_variation=3.0):
        """
        Compares two floating-point numbers with a small margin of variation.

        Args:
            val1 (float): The first number to compare.
            val2 (float): The second number to compare.
            variation (float): The allowed margin of variation as a percentage (default is 0.5%, or 0.005).

        Returns:
            bool: True if the numbers are considered equal within the margin of variation, False otherwise.
        """
        try:
            # Check if both values are numbers
            if not isinstance(val1, (int, float)) or not isinstance(val2, (int, float)):
                raise ValueError("Both inputs must be integers or floats.")
            
            # Check if the absolute difference is within the allowed range
            if math.isclose(val1, val2, abs_tol=allowed_variation):
                return True
            
            return False

        except Exception as e:
            print(f"Error comparing floats: {e}")
            return False
        
class CGPTJsonHelper:
    
    @staticmethod
    def MSSaveExtractedDataFromResponseObject(directory_path, output_directory, invoice_keys):
        # Ensure output directory exists
        os.makedirs(output_directory, exist_ok=True)

        # Traverse all JSON files in the directory
        for filename in os.listdir(directory_path):
            if filename.endswith(".json"):
                input_file_path = os.path.join(directory_path, filename)

                with open(input_file_path, "r") as file:
                    try:
                        data = json.load(file)
                        # Extract the required field
                        content_json_str = data.get("choices", [{}])[0].get("message", {}).get("content", "{}")
                        content_json = json.loads(content_json_str)  # Convert string to JSON

                        # Determine invoice number from available keys
                        invoice_number = None
                        for key in invoice_keys:
                            if key in content_json:
                                invoice_number = str(content_json[key]).replace("\\","").replace("/", "")
                                break

                        if not invoice_number:
                            print(f"No invoice number found for {filename}, skipping...")
                            continue

                        # Construct new filename based on invoice number
                        output_file_path = os.path.join(output_directory, f"{invoice_number}.json")
                        
                        # Save extracted JSON
                        with open(output_file_path, "w") as output_file:
                            json.dump(content_json, output_file, indent=4)
                        print(f"Saved {output_file_path}")
                    
                    except (json.JSONDecodeError, IndexError, KeyError) as e:
                        print(f"Error processing {filename}: {e}")
class CSimilarityHelper:

    @staticmethod
    def MSFindMostSimilarMatch(input_string, items):
        # Extract the part inside the parentheses
        bracketed_text = re.search(r"\((.*?)\)", input_string)
        
        # If there's a match inside parentheses, try to match it exactly in the list
        if bracketed_text:
            bracketed_text = bracketed_text.group(1).strip()
            
            # Exact match of the bracketed part
            exact_match = [item for item in items if bracketed_text.lower() in item.lower()]
            
            if exact_match:
                return exact_match[0]  # Return the first match found
        
        # If no exact match, use fuzzy matching to find the closest match for the entire input string
        best_match = None
        best_score = 0
        
        for item in items:
            score = fuzz.ratio(input_string.lower(), item.lower())
            if score > best_score:
                best_score = score
                best_match = item
        
        return best_match

    @staticmethod
    def MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = {}, strInputText = "shyam traders"):
        try:
            # Open the JSON file and load the data
            if strLedgerConfigFile is not None:
                with open(strLedgerConfigFile, "r") as file:
                    dictLedgerConfig = json.load(file)
            
            inp = strInputText.lower().strip()
            
            if not inp:
                return " "
            else:
                # Naive search: Simple substring search
                lsNaiveSearch = []
                for key in dictLedgerConfig.keys():
                    if inp in key.lower():
                        lsNaiveSearch.append(key)
                print("Naive Search Results:", lsNaiveSearch)
            
                # Fuzzy search using fuzzywuzzy with token_sort_ratio for better matching
                lsFuzzySearch = process.extract(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                threshold = 70
                filtered_fuzzy_results = [match for match in lsFuzzySearch if match[1] >= threshold]
                print("Filtered Fuzzy Search Results:", filtered_fuzzy_results)
            
                # Best match using extractOne
                best_match = process.extractOne(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                print("Best Fuzzy Match:", best_match[0])
                return best_match[0]
        except Exception as e:
            return " "

class ValidationHelper:

    def is_mobile(cls, v):
        if not re.fullmatch(Constants.NUMBER_REGEX, str(v)):
            message = i18n.t(key='Please enter valid mobile number')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_password(cls, v):
        if not re.fullmatch(Constants.PASSWORD_REGEX, v):
            message = i18n.t(key='The password must be at least six characters long')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_email(cls, v):
        if not re.fullmatch(Constants.EMAIL_REGEX, v):
            message = i18n.t(key= "Please enter valid email.")
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_lang(cls, v):
        if not re.fullmatch(Constants.LANG_REGEX, v):
            message = i18n.t(key='INVALID_LANG')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_comment_type(cls, v):
        if not re.fullmatch(Constants.COMMENT_TYPE, v):
            message = i18n.t(key='INVALID_COMMENT_TYPE')
            raise HTTPException(status_code=400, detail=message)
        return v


class Hashing:
    """
    A utility class for hashing and checksum operations, including password hashing
    and checksum calculation for files and content.
    """
    _pwd_context = CryptContext(schemes=["bcrypt"], deprecated="auto")

    @staticmethod
    def calculate_checksum_from_file(file_path):
        """
        Input:

            1) file_path: str
               The path to the file for which the checksum needs to be calculated.

        Output:

            str: The MD5 checksum of the file content.

        Purpose:

            To calculate and return the MD5 checksum of a file's content.

        Example:

            checksum = Hashing.calculate_checksum_from_file("path/to/file.txt")
            print(checksum)
        """
        with open(file_path, "rb") as f:
            file_content = f.read()
        return hashlib.md5(file_content).hexdigest()

    @staticmethod
    def calculate_checksum(file_content):
        """
        Input:

            1) file_content: bytes
               The content of the file or data for which the checksum needs to be calculated.

        Output:

            str: The MD5 checksum of the provided content.

        Purpose:

            To calculate and return the MD5 checksum of the given data.

        Example:

            content = b"Sample content"
            checksum = Hashing.calculate_checksum(content)
            print(checksum)
        """
        return hashlib.md5(file_content).hexdigest()

    @staticmethod
    def get_hash(text):
        """
        Input:

            1) text: str
               The plain text string that needs to be hashed.

        Output:

            str: The hashed representation of the input text.

        Purpose:

            To hash a plain text string using the bcrypt algorithm for secure storage.

        Example:

            hashed_password = Hashing.get_hash("mypassword")
            print(hashed_password)
        """
        return Hashing._pwd_context.hash(text)

    @staticmethod
    def verify(plain_text, hashed_text):
        """
        Input:

            1) plain_text: str
               The plain text string to be verified.

            2) hashed_text: str
               The previously hashed string to compare against.

        Output:

            bool: True if the plain text matches the hashed text, False otherwise.

        Purpose:

            To verify if the provided plain text matches the hashed text, ensuring password validation.

        Example:

            is_valid = Hashing.verify("mypassword", hashed_password)
            print(is_valid)
        """
        return Hashing._pwd_context.verify(plain_text, hashed_text)

    # Indian Documents Process - ROUTES 
    async def VerifyChecksum(checksums, documents):
        # Verify Checksum of the files received
        received_checksums = checksums.split(",")
        
        for idx, doc in enumerate(documents):
            content = await doc.read()
            computed_checksum = Hashing.calculate_checksum(content)
            
            # Reset file pointer before re-reading
            doc.file.seek(0)
            
            if computed_checksum != received_checksums[idx]:
                raise HTTPException(status_code=500, detail=f"Checksum mismatch for file: {doc.filename}")


MODEL_TO_PATH_MAPPING = {
    "simpolo": Path(r"Data/Customer/17_ParagTraders/1_Simpolo/GPTResponseFormat.json"),
    "nexion": Path(r"Data/Customer/17_ParagTraders/2_Nexion/GPTResponseFormat.json"),
    "hansgrohe": Path(r"Data/Customer/17_ParagTraders/3_Hansgrohe/GPTResponseFormat.json"),
    "kohler": Path(r"Data/Customer/17_ParagTraders/4_Kohler/GPTResponseFormat.json"),
    "geberit": Path(r"Data/Customer/17_ParagTraders/12_Geberit/GPTResponseFormat.json"),
    "toto": Path(r"Data/Customer/17_ParagTraders/5_Toto/GPTResponseFormat.json"),
    "powergrace": Path(r"Data/Customer/17_ParagTraders/6_Powergrace/GPTResponseFormat.json"),
    "aquant": Path(r"Data/Customer/17_ParagTraders/15_Aquant/GPTResponseFormat.json"),
    "icon": Path(r"Data/Customer/17_ParagTraders/ICON/GPTResponseFormat.json"),
    "pegasus": Path(r"Data/Customer/ICD/1_Pegasus/GPTResponseFormat.json"),
    "sygnia brandworks llp": Path(r"Data/Customer/Gwalia/Sygnia/GPTResponseFormat.json"),
    "sheeba dairy": Path(r"Data/Customer/Gwalia/SheebaDairy/GPTResponseFormat.json"),
    "bhavya sales company": Path(r"Data/Customer/Gwalia/BhavyaSales/GPTResponseFormat.json"),
    "container corporation of india ltd.": Path(r"Data/Customer/ICD/Concor/GPTResponseFormat.json"),
    "goodrich maritime private limited": Path(r"Data/Customer/ICD/GoodRich/GPTResponseFormat.json"),
    "kotak global logistics pvt ltd": Path(r"Data/Customer/ICD/Kotak global/GPTResponseFormat.json"),
    "msc agency": Path(r"Data/Customer/ICD/MSC/GPTResponseFormat.json"),
    "agarwal suppliers": Path(r"Data/Customer/Gwalia/AgarwalSuppliers/GPTResponseFormat.json"),
    "shri ram enterprises": Path(r"Data/Customer/Gwalia/ShriRamEnterprises/GPTResponseFormat.json"),
    "the ahmedabad coop dept stories ltd": Path(r"Data/Customer/Gwalia/TheAhmedabadCoopDeptStoriesLtd/GPTResponseFormat.json"),
    "shree foods": Path(r"Data/Customer/Gwalia/ShreeFoods/GPTResponseFormat.json"),
    "nakoda trading": Path(r"Data/Customer/Gwalia/NakodaTrading/GPTResponseFormat.json"),
    "shree dutt krupa lamination": Path(r"Data/Customer/Gwalia/ShreeDuttkrupaLamination/GPTResponseFormat.json"),
    "shivambica sales corporation": Path(r"Data/Customer/Gwalia/ShivambicaSalesCorporation/GPTResponseFormat.json"),
    "sachi products": Path(r"Data/Customer/Gwalia/SachiProducts/GPTResponseFormat.json"),
    "govind tours and travels": Path(r"Data/Customer/Gwalia/GovindToursAndTravels/GPTResponseFormat.json"),
    "i i traders": Path(r"Data/Customer/Gwalia/IITraders/GPTResponseFormat.json"),
    "11 traders": Path(r"Data/Customer/Gwalia/IITraders/GPTResponseFormat.json"),
    "sovereign sales": Path(r"Data/Customer/Gwalia/SovereignSales/GPTResponseFormat.json"),
    "dharm sales company": Path(r"Data/Customer/Gwalia/DharmSalesCompany/GPTResponseFormat.json"),
    "satyam traders": Path(r"Data/Customer/Satyam/GPTResponseFormat.json"),
    "ketan engineering works": Path(r"Data/Customer/Ketan/GPTResponseFormat.json"),
    "hind terminals": Path(r"Data/Customer/ICD/Hind Terminals/GPTResponseFormat.json"),
    "adani hazira": Path(r"Data/Customer/ICD/Adani Hazira/GPTResponseFormat.json"),
    "somani brothers": Path(r"Data/Customer/PremTaxtiles/SomaniBrothers/GPTResponseFormat.json"),
    "the vedansh international school": Path(r"Data/Customer/VedanshSchool/VedanshInternationalSchool/GPTResponseFormat.json"),
    "r k trading company": Path(r"Data/Customer/Gwalia/RKTradingCompany/GPTResponseFormat.json"),
    "r k plast (india)": Path(r"Data/Customer/Gwalia/RKPlastIndia/GPTResponseFormat.json"),
    "uma converter ltd": Path(r"Data/Customer/Gwalia/UmaConverterLtd/GPTResponseFormat.json"),
    "radhe agency": Path(r"Data/Customer/Gwalia/RadheAgency/GPTResponseFormat.json"),
    "amar traders": Path(r"Data/Customer/Gwalia/AmarTraders/GPTResponseFormat.json"),
    "mahavir international": Path(r"Data/Customer/Gwalia/MahavirInternational/GPTResponseFormat.json"),
    "gas guys": Path(r"Data/Customer/Gwalia/GasGuys/GPTResponseFormat.json"),
    "zeel pest solution llp": Path(r"Data/Customer/Gwalia/ZeelPestSolutionLlp/GPTResponseFormat.json"),
    "shri arihant sales agency": Path(r"Data/Customer/Gwalia/ShreeArihantSalesAgency/GPTResponseFormat.json"),
    "diamond private security investigation services": Path(r"Data/Customer/Gwalia/DiamondPrivateSecurityInvestigationServices/GPTResponseFormat.json"),
    "savnath enterprise llp": Path(r"Data/Customer/Gwalia/SavnathEnterpriseLLP/gptResponseFormat.json"),
    "unicorn enterprise": Path(r"Data/Customer/Gwalia/UnicornEnterprise/gptResponseFormat.json"),
    "gurukrupa traders": Path(r"Data/Customer/Gwalia/GurukrupaTraders/gptResponseFormat.json"),
    "grains and more": Path(r"Data/Customer/Gwalia/GrainsAndMore/gptResponseFormat.json"),
    "shivay enterprise": Path(r"Data/Customer/Gwalia/ShivayEnterprise/gptResponseFormat.json"),
    "yash tradelink": Path(r"Data/Customer/Gwalia/YashTradelink/gptResponseFormat.json"),
    "palladium": Path(r"Data/Customer/Gwalia/SwiggyDineoutPalladium/gptResponseFormat.json"),
    "satyam steel house": Path(r"Data/Customer/Gwalia/SatyamSteelHouse/gptResponseFormat.json"),
    "south asia fm ltd": Path(r"Data/Customer/VedanshSchool/South Asia FM Limited/GPTResponseFormat.json"),
    "guru kripa petroleum": Path(r"Data/Customer/VedanshSchool/GuruKripaPetroleum/GPTResponseFormat.json"),
    "trophy wala": Path(r"Data/Customer/VedanshSchool/Trophy Wala/GPTResponseFormat.json"),
    "r k enterprises": Path(r"Data/Customer/VedanshSchool/RKEnterprises/GPTResponseFormat.json"),
    "sanghi brothers": Path(r"Data/Customer/VedanshSchool/SanghiBrothers/GPTResponseFormat.json"),
    "mahavir kirana stores": Path(r"Data/Customer/VedanshSchool/MahaveerKiranaStores/GPTResponseFormat.json"),
    "ronak rudra security & management services": Path(r"Data/Customer/VedanshSchool/RonakRudraSecurity/GPTResponseFormat.json"),
    "ghanshyam bahiniya": Path(r"Data/Customer/VedanshSchool/GhansyamBahiniya/GPTResponseFormat.json"),
    "inifinty cars pvt ltd": Path(r"Data/Customer/VedanshSchool/InfinityCars/GPTResponseFormat.json"),
    "mendwell agencies": Path(r"Data/Customer/VedanshSchool/MendwellAgencies/GPTResponseFormat.json"),
    "airtel": Path(r"Data/Customer/17_ParagTraders/21_Airtel/gptResponseFormat.json"),
    "bharat sanchar nigam limited": Path(r"Data/Customer/17_ParagTraders/22_Bsnl/gptResponseFormat.json"),
    "kiron electricals": Path(r"Data/Customer/17_ParagTraders/23_Kiron/gptResponseFormat.json"),
    "rollin logistics": Path(r"Data/Customer/17_ParagTraders/24_RollinLogistics/gptResponseFormat.json"),
    "karnavati": Path(r"Data/Customer/Gwalia/Karnavati/GPTResponseFormat.json"),
    "regenta m foods": Path(r"Data/Customer/Gwalia/Regenta M Foods/GPTResponseFormat.json"),
    "bhagwati trading co": Path(r"Data/Customer/Abhinav InfraBuild/Bhagwati Trading Co/GPTResponseFormat.json"),
    "a.a.khambati & sons": Path(r"Data/Customer/Abhinav InfraBuild/AAKhambati&Son/GPTResponseFormat.json"),
    "narayan marketing": Path(r"Data/Customer/Abhinav InfraBuild/Narayan Marketing/GPTResponseFormat.json"),
    "ashkelon enterprises": Path(r"Data/Customer/Abhinav InfraBuild/Ashkelon Enterprises/GPTResponseFormat.json"),
    "v.s.agencies": Path(r"Data/Customer/Abhinav InfraBuild/VSAgencies/GPTResponseFormat.json"),
    "united liner shipping services llp": Path(r"Data/Customer/ICD/United Liner Shipping/GPTResponseFormat.json"),
    "anl singapore pte. ltd. c/o ccai": Path(r"Data/Customer/ICD/ANL/GPTResponseFormat.json"),
    "anl singapore pte. ltd":Path(r"Data/Customer/ICD/ANL/GPTResponseFormat.json"),
    "anl singapore pte. ltd.":Path(r"Data/Customer/ICD/ANL/GPTResponseFormat.json") 
}

class CommonHelper:
    def random_string(length: int = 6):
        return ''.join(random.choices(string.digits, k=length))

    @staticmethod
    def normalize_string(s):
        """
        Normalize the string by removing symbols, spaces, and making it lowercase.
        
        Args:
            s (str): Input string to normalize.
        
        Returns:
            str: Normalized string.
        """
        return re.sub(r'[^a-z0-9]', '', s.lower())  # Remove all non-alphanumeric characters and convert to lowercase

    @staticmethod
    def MSParseTimeString(time_str):
        # Remove the leading '~' and extra spaces if present
        time_str = time_str.strip('~').strip()

        # Regular expression to match minutes and seconds
        time_pattern = r"(?:(\d+)\s*min(?:s)?)?\s*(?:(\d+)\s*sec(?:s)?)?"

        # Match the time pattern
        match = re.match(time_pattern, time_str)

        # Initialize hours, minutes, and seconds as 0
        hours = 0
        minutes = 0
        seconds = 0

        if match:
            # Extract minutes and seconds from the match groups
            minutes = int(match.group(1) or 0)  # Group 1 is minutes
            seconds = int(match.group(2) or 0)  # Group 2 is seconds

        return hours, minutes, seconds
    
    @staticmethod
    def MSGetTotalTimeSaved(time_list):
        """
            Calculate the total time saved from a list of time strings and return formatted output.

            This method takes a list of time strings, parses each one into hours, minutes, and seconds,
            accumulates the total time, normalizes it, and returns a formatted string representing the
            total time saved. It also returns the total minutes (including seconds converted to minutes)
            for additional calculations if needed.

            Args:
                time_list (list): A list of time strings in a format that can be parsed by
                                `CommonHelper.MSParseTimeString`. Each string represents a duration.

            Returns:
                tuple: A tuple containing:
                    - str: A formatted string representing the total time saved (e.g., "2hrs 30mins 15secs").
                        Returns "-" if the input list is empty, contains only invalid/empty strings,
                        or the total time is zero.
                    - int: The total number of minutes (including seconds converted to minutes).
                        Returns 0 if the input list is empty, contains only invalid/empty strings,
                        or the total time is zero.
        """
        total_hours = total_minutes = total_seconds = 0

        # Early return if list is empty or contains only empty/invalid strings
        if not time_list or all(not t.strip() for t in time_list):
            return "-", 0

        # Parse each time string and accumulate
        for time_str in time_list:
            hours, minutes, seconds = CommonHelper.MSParseTimeString(time_str)
            total_hours += hours
            total_minutes += minutes
            total_seconds += seconds

        # Normalize
        total_minutes += total_seconds // 60
        total_seconds = total_seconds % 60

        total_hours += total_minutes // 60
        ftotal_minutes = total_minutes % 60

        # Format result
        result = []
        if total_hours > 0:
            result.append(f"{total_hours}hr" if total_hours == 1 else f"{total_hours}hrs")
        if ftotal_minutes > 0:
            result.append(f"{ftotal_minutes}min" if ftotal_minutes == 1 else f"{ftotal_minutes}mins")
        if total_seconds > 0:
            result.append(f"{total_seconds}sec" if total_seconds == 1 else f"{total_seconds}secs")

        # If result is still empty (all components were 0), return dash
        if not result:
            return "-", 0

        return " ".join(result), (total_minutes + total_seconds // 60)

    @staticmethod
    def MSGetResponseFormat(model_name: str) -> dict:
        """
        Purpose : Retrieve the response format dictionary for a given model name from a corresponding JSON file.

        Inputs  :   (1) model_name : The name of the model to find the response format for (e.g., "Hansgrohe").

        Outputs : A dictionary containing the response format loaded from the JSON file.

        Example : 
            response_format = MSGetResponseFormat(model_name="Hansgrohe")
            print(response_format)  # Output: {'key': 'value', ...} (content of the JSON file)
        """
        try:
            model_name_lower = model_name.lower()
            for key in MODEL_TO_PATH_MAPPING:
                if key in model_name_lower:
                    path = MODEL_TO_PATH_MAPPING[key]
                    with open(path, 'r') as f:
                        return json.load(f)
            raise HTTPException(status_code=404, detail=f"No valid response format found for model '{model_name}'")
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail=f"Response format file not found for model '{model_name}'")
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail=f"Invalid JSON format in response file for model '{model_name}'")
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while retrieving response format for model '{model_name}': {str(e)}")
    @staticmethod
    async def MSGetFormattedTime(iMinutes: int) -> str:
        """
        Format the given minutes into a human-readable string.

        Args:
            iMinutes (int): The number of minutes to format. Must be a non-negative integer.

        Returns:
            str: A formatted string representing the time in hours and minutes.

        Raises:
            TypeError: If iMinutes is not an integer.
            ValueError: If iMinutes is negative.

        Examples:
            >>> await MSGetFormattedTime(30)
            '30 Minutes'
            >>> await MSGetFormattedTime(60)
            '1 Hour'
            >>> await MSGetFormattedTime(61)
            '1 Hour 1 Minute'
            >>> await MSGetFormattedTime(120)
            '2 Hours'
            >>> await MSGetFormattedTime(121)
            '2 Hours 1 Minute'
            >>> await MSGetFormattedTime(1)
            '1 Minute'
            >>> await MSGetFormattedTime(0)
            '0 Minutes'
        """
        if not isinstance(iMinutes, int):
            iMinutes = int(iMinutes)
        if iMinutes < 0:
            raise ValueError("iMinutes must be non-negative")
        
        if iMinutes < 60:
            if iMinutes == 1:
                return "1 Minute"
            else:
                return f"{iMinutes} Minutes"
        else:
            hours = iMinutes // 60
            minutes = iMinutes % 60
            hour_str = "Hour" if hours == 1 else "Hours"
            if minutes == 0:
                return f"{hours} {hour_str}"
            else:
                minute_str = "Minute" if minutes == 1 else "Minutes"
                return f"{hours} {hour_str} {minutes} {minute_str}"
              
    @staticmethod
    def MSTotalTimeSaved(po_list: list) -> str:
        """
        Calculate the total time saved from a list of PO dictionaries and format it as a readable string.
        
        Args:
            po_list (list): List of dictionaries containing "Time_Statistics" with hours, minutes, and seconds.
        
        Returns:
            str: Formatted time string (e.g., "2 hr 30 min 10 sec" or "45 sec").
        """
        total_hours = sum(po.get("Time_Statistics", {}).get("hours", 0) for po in po_list)
        total_minutes = sum(po.get("Time_Statistics", {}).get("minutes", 0) for po in po_list)
        total_seconds = sum(po.get("Time_Statistics", {}).get("seconds", 0) for po in po_list)

        # Normalize minutes and seconds
        total_minutes += total_seconds // 60
        total_seconds = total_seconds % 60
        total_hours += total_minutes // 60
        total_minutes = total_minutes % 60

        # Create a formatted string
        formatted_time = []
        if total_hours > 0:
            formatted_time.append(f"{total_hours} hr")
        if total_minutes > 0:
            formatted_time.append(f"{total_minutes} min")
        if total_seconds > 0 or not formatted_time:
            formatted_time.append(f"{total_seconds} sec")

        return " ".join(formatted_time)


class APIHelper:
    # Send success response with custom message
    def send_error_response(errorMessageKey: Optional[str] = None):
        error_message = i18n.t(key=errorMessageKey or 'failure')
        response_model = BaseResponseModel(error=error_message)
        return JSONResponse(
            status_code=400,
            content=response_model.model_dump(exclude_none=True),
        )

    # Send unauthorized response with custom message
    def send_unauthorized_error(locale: Optional[str] = "en"):
        return JSONResponse(
            status_code=401,
            content=(BaseResponseModel(error=i18n.t(
                'You are unauthorized', locale=locale))),
        )

    # Send error response with custom message
    def send_success_response(
        data: Optional[Any] = None, successMessageKey: Optional[str] = None, locale: Optional[str] = "en"
    ):
        return BaseResponseModel(data=data, message=i18n.t(key=successMessageKey or 'success', locale=locale))


    @staticmethod
    def MSDecideServiceURL():
        import getpass
        DEVICE_NAME = socket.gethostname()  # Fetch the device name
        IP_ADDRESS = socket.gethostbyname(DEVICE_NAME) 
        # Fetch system user name
        USER_NAME = getpass.getuser()
        print("USER_NAME : ", USER_NAME)
        # Debugging Config - DO NOT REMOVE 
        if IP_ADDRESS.split('.')[-1] == '20' or DEVICE_NAME == 'RIVER-DEV8':
            return "http://************:9003/IndianInvTally/process_doc"
            
        elif IP_ADDRESS.split('.')[-1] == '19' or DEVICE_NAME == 'TALLY-SER-2':
            return "http://************:9002/IndianInvTally/process_doc"

        elif IP_ADDRESS.split('.')[-1] == '23' or DEVICE_NAME == 'RIVER-DEV2':
            return "http://************:9010/IndianInvTally/process_doc"

        elif IP_ADDRESS.split('.')[-1] == '13' or DEVICE_NAME == 'TALLY-SERVER':
            return "http://************:9001/IndianInvTally/process_doc"

        elif IP_ADDRESS.split('.')[-1] == '15' or USER_NAME == 'Harshil':
            return "http://************:9002/IndianInvTally/process_doc"
        
        elif IP_ADDRESS.split('.')[-1] == '15' or DEVICE_NAME == 'DEV':
            return "http://************:9010/IndianInvTally/process_doc"

        else:
            return "http://************:9001/IndianInvTally/process_doc"
         

class CORSHelper:
    #CORS setup
    def setup_cors(app: FastAPI):
        origins = ["*"]
        
        # if os.getenv("ENV") == "development":
        #      origins = ["*"]          
        # else:
        #     origins = os.getenv("CORS_DOMAIN").split(",")
            
        app.add_middleware(
            CORSMiddleware,
            allow_origins=origins,
            allow_credentials=True,
            allow_methods=["*"],
            allow_headers=["*"],
        )

class GPTHelper:
    
    @staticmethod
    def MSCountGPTToken(input_text, model_name="gpt-4-0125-preview"):
        # Get the tokenizer corresponding to the model
        tokenizer = tiktoken.encoding_for_model(model_name)

        # Tokenize the input text
        tokens = tokenizer.encode(str(input_text))

        # Return the number of tokens
        return len(tokens)

    @staticmethod
    def MSCountPage(token_count:int):
        # Determine the number of pages by dividing the token count by 4000
        # and rounding up to the nearest whole number
        page_count = (token_count // 4000) + 1

        return int(page_count)

    @staticmethod
    def MSKeepSpecifiedPages(input_path, pages_to_keep_str, output_path):
        """
        Purpose : Creates a new PDF containing only the specified pages from the input PDF and saves it to the output path.

        Inputs  :   (1) input_path       : Path to the input PDF file (e.g., "C:\\path\\to\\input.pdf").
                    (2) pages_to_keep_str: A string specifying pages to keep, e.g., "1,2,3,4-6,8-15,20-32".
                    (3) output_path      : Path where the output PDF will be saved (e.g., "C:\\path\\to\\output.pdf").

        Outputs : None. Saves the new PDF with selected pages to the specified output path.

        Example : 
            MSKeepSpecifiedPages(
                input_path="C:\\input.pdf",
                pages_to_keep_str="1,3,5-7",
                output_path="C:\\output.pdf"
            )
        """
        try:
            # Open the PDF using PdfReader
            pdf = PdfReader(input_path)
            
            # Parse the pages to keep into a set of 1-based page numbers
            pages_to_keep = GPTHelper.MSParsePagesToKeep(pages_to_keep_str)
            
            # Create a PdfWriter object to build the new PDF
            pdf_writer = PdfWriter()
            
            # Loop through all pages (0-based index) and add only the specified ones
            for i in range(len(pdf.pages)):
                if (i + 1) in pages_to_keep:  # Convert to 1-based index for comparison
                    pdf_writer.add_page(pdf.pages[i])
            
            # Write the new PDF to the output file
            with open(output_path, 'wb') as out_file:
                pdf_writer.write(out_file)
                
        except FileNotFoundError:
            raise FileNotFoundError(f"Input PDF file not found: {input_path}")
        except ValueError as e:
            raise ValueError(f"Invalid page specification or PDF processing error: {str(e)}")
        except Exception as e:
            raise Exception(f"An error occurred while processing the PDF: {str(e)}")

    @staticmethod
    def MSParsePagesToKeep(pages_str):
        """
        Purpose : Parses a string of page specifications into a set of 1-based page numbers.

        Inputs  :   (1) pages_str : A string specifying pages, e.g., "1,2,3,4-6,8-15,20-32".

        Outputs : A set of integers representing the pages to keep.

        Example : 
            pages = MSParsePagesToKeep("1,3,5-7")
            print(pages)  # Output: {1, 3, 5, 6, 7}
        """
        try:
            pages = set()
            for part in pages_str.split(','):
                part = part.strip()
                if '-' in part:
                    start, end = map(int, part.split('-'))
                    if start > end:
                        raise ValueError(f"Invalid range: start page {start} is greater than end page {end}")
                    pages.update(range(start, end + 1))  # Add all pages in the range
                else:
                    page_num = int(part)
                    if page_num <= 0:
                        raise ValueError(f"Invalid page number: {page_num}. Page numbers must be positive.")
                    pages.add(page_num)  # Add single page
            return pages
        except ValueError as e:
            raise ValueError(f"Error parsing page specification: {str(e)}")
        except Exception as e:
            raise Exception(f"An unexpected error occurred while parsing pages: {str(e)}")

class CExtractCoordinatesHelper:
    def transformForAppLayerWithCordinates(GeminiDictResponse:dict):
        """
        Transforms the input dictionary `GeminiDictResponse` according to specific requirements.
    
        Args:
            GeminiDictResponse (dict): Input dictionary containing fields and tables.
    
        Returns:
            dict: Transformed dictionary with the following structure:
                {
                    'Fields': [
                        {field1: [value1, 0, 0, 0, 0, 0]},
                        {field2: [value2, 0, 0, 0, 0, 0]},
                        ...
                    ],
                    'Tables': {table1: table_data1, table2: table_data2,...}
                }
    
        Example:
            input_dict = {
                'Fields': {
                    'field1': 'value1',
                    'field2': 'value2'
                },
                'Tables': {
                    'table1': [data1, data2, data3],
                    'table2': [data4, data5, data6]
                }
            }
    
            transformed_dict = transformForAppLayerWithCordinates(input_dict)
            print(transformed_dict)
            # Output:
            # {
            #     'Fields': [
            #         {'field1': ['value1', 0, 0, 0, 0, 0]},
            #         {'field2': ['value2', 0, 0, 0, 0, 0]}
            #     ],
            #     'Tables': {
            #         'table1': [data1, data2, data3],
            #         'table2': [data4, data5, data6]
            #     }
            # }
        """
        transformed_dict = {}

        for field in GeminiDictResponse['Fields']:
            if isinstance(field, dict):
                for key, value in field.items():
                    transformed_value = [value, 0, 0, 0, 0, 0]
                    transformed_dict.setdefault('Fields', []).append({key: transformed_value})
            elif isinstance(field, str):
                transformed_value = [field, 0, 0, 0, 0, 0]
                transformed_dict.setdefault('Fields', []).append({field: transformed_value})

        transformed_dict['Tables'] = GeminiDictResponse['Tables']

        return transformed_dict
    
    def transformAndRemoveCoordinatesFromDocExtractedData(Response: dict) -> dict:
        """
        Transforms the input dictionary `Response` according to specific requirements.

        Args:
            Response (dict): Input dictionary containing fields and tables.

        Returns:
            dict: Transformed dictionary with the following structure:
                {
                    'Fields': [
                        {field1: value1},
                        {field2: value2},
                        ...
                    ],
                    'Tables': {table1: table_data1, table2: table_data2,...}
                }
        """
        transformed_dict = {}

        for field in Response['Fields']:
            if isinstance(field, dict):
                for key, value in field.items():
                    if isinstance(value, list) and len(value) > 0:
                        transformed_value = value[0]  # Extract the first element if it is a list
                    else:
                        transformed_value = value  # Use the value directly if it's not a list
                    transformed_dict.setdefault('Fields', []).append({key: transformed_value})

        transformed_dict['Tables'] = Response['Tables']

        return transformed_dict

    def transformForExtractedFormat(CordinatesExtractedData:dict):
        ExtractedDictDataResponse = {}

        ExtractedDictDataResponse['Fields'] = []
        for field in CordinatesExtractedData['Fields']:
            if isinstance(field, dict):
                for key, value in field.items():
                    if isinstance(value, list):
                        if len(value) == 0:
                            ExtractedDictDataResponse['Fields'].append({key: ""})
                        else:
                            ExtractedDictDataResponse['Fields'].append({key: value[0]})
                    elif isinstance(value, str):
                        ExtractedDictDataResponse['Fields'].append({key: value})

        ExtractedDictDataResponse['Tables'] = CordinatesExtractedData['Tables']

        if 'meta_data' in CordinatesExtractedData:
            meta_data = CordinatesExtractedData['meta_data']
            if isinstance(meta_data, dict):
                ExtractedDictDataResponse["meta_data"] = {}
                if "page_width" in meta_data:
                    ExtractedDictDataResponse["meta_data"]["page_width"] = meta_data['page_width']
                if "page_height" in meta_data:
                    ExtractedDictDataResponse["meta_data"]["page_height"] = meta_data['page_height']

        return ExtractedDictDataResponse
    
    def EnsureExtractedDataCordinates(CordinatesExtractedData: dict):
        ExtractedDictDataResponse = {}

        ExtractedDictDataResponse['Fields'] = []
        for field in CordinatesExtractedData['Fields']:
            if isinstance(field, dict):
                for key, value in field.items():
                    if isinstance(value, list):
                        ExtractedDictDataResponse['Fields'].append({key: value})
                    elif isinstance(value, str):
                        transformed_value = [value, 0, 0, 0, 0, 0]
                        # Ensure if no coordinates receive from GPT then provide default values 0 
                        ExtractedDictDataResponse['Fields'].append({key: transformed_value})

        ExtractedDictDataResponse['Tables'] = CordinatesExtractedData['Tables']

        if 'meta_data' in CordinatesExtractedData:
            meta_data = CordinatesExtractedData['meta_data']
            if isinstance(meta_data, dict):
                ExtractedDictDataResponse["meta_data"] = {}
                if "page_width" in meta_data:
                    ExtractedDictDataResponse["meta_data"]["page_width"] = meta_data['page_width']
                if "page_height" in meta_data:
                    ExtractedDictDataResponse["meta_data"]["page_height"] = meta_data['page_height']

        return ExtractedDictDataResponse

def GetFiletypeFromFileName(strFileName):
    try:
        # Corrected regex pattern with accurate extensions and no double pipes
        match = re.search(r'\.(pdf|txt|jpeg|jpg|bmp|webp|png)$', strFileName, re.IGNORECASE)

        if match:
            # Return the matched extension as lowercase to avoid case mismatches
            return match.group(1).lower()
        else:
            raise HTTPException(status_code=500, detail="Invalid file format found")

    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error processing file: {str(e)}")
        
    

def replace_sample_with_zero(data):
    """
    Replaces 'Sample' values in the dictionary with 0.
    
    Args:
        data (dict): Input dictionary containing nested fields and tables with 'Sample' values.
    
    Returns:
        dict: Modified dictionary with 'Sample' replaced by 0.
    """
    if isinstance(data, dict):
        return {key: replace_sample_with_zero(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [replace_sample_with_zero(item) for item in data]
    else:
        return 0 if data == 'Sample' else data


def validValue(value: str, colType: str, bDebug: bool = False):
    """
    Purpose : This method is used to validate the column type in the output.

    Inputs  :   (1) value   : Response of GPT for a specific column (string)
                (2) colType : Column type (string)
                (3) bDebug : Debug flag (boolean)

    Output  : It returns the validated value as a string or "Not Found" in case of an error.

    Example : validValue(value='01/01/20', colType='Date')
    """
    try:
        if colType == "Date":
            match1 = re.match(r'(\d{1,2})/(\d{1,2})/(\d{2,4})', str(value))
            match2 = re.match(r'(\d{1,2})-(\d{1,2})-(\d{2,4})', str(value))
            if match1 or match2:
                match = match1 if match1 else match2
                month, day, year = match.groups()
                if len(year) == 2:
                    year = '20' + year
                date_obj = datetime.strptime(f"{month}/{day}/{year}", "%m/%d/%Y")
                year = date_obj.strftime("%y")
                return (day, month, year)
        elif colType == "Int":
            return int(''.join(filter(str.isdigit, value))) if value else "Not Found"
        elif colType == "Float":
            value = re.sub(r'[^\d.]', '', str(value)) if value else "Not Found"
            try:
                return round(float(value), 2)
            except ValueError:
                return "Not Found"
        else:
            return (str(re.sub(r'[^A-Za-z0-9\s]', '', str(value)))).strip() if value else "Not Found"
    except Exception as e:
        if bDebug:
            print(f"Unable to execute process for: {value, colType}")
            print(traceback.format_exc())
        return "Not Found"
        
@ensure_annotations
def saveGPTresponse(strOutputFilePath: str, dictResponse: object) -> dict:
    """
    Purpose : This method is used to save the GPT response into a file.

    Inputs  :   (1) strOutputFilePath : Path to the output file (string)

    Output  : It returns the GPT response as a dictionary.

    Example : CGetGPTJson.saveGPTresponse(strOutputFilePath='path/to/output')
    """
    try:
        dictNewResponse = {'Response': dictResponse}
        with open(strOutputFilePath, 'w') as file:
            json.dump(dictNewResponse, file, indent=4)
        return dictResponse
    except Exception as e:
        print(f"ERROR: While saving GPT response to file: {strOutputFilePath}")
        return {}

def getGPTConfigData():
    file_path = "resource" + os.sep + "GPTConfig,json"
    with open(r"resource/GPTConfig.json") as f:
        data = json.load(f)
    return data

def RemoveConfidenceFromGptResponse(objResponse):
    jsonData = json.loads(objResponse["choices"][0]["message"]["content"])
    jsonData = jsonData['Invoice']
    jsonData = RemoveConfidenceScore(jsonData)
    return jsonData




def RemoveConfidenceScore(jsonData):
    try:

        # Initialize a new dictionary to store the processed jsonData
        processed_jsonData = {}

        # Iterate over each key-value pair in the input dictionary

        for key, value in jsonData.items():
            
            # Check if the value is a list
            if isinstance(value, list):
                # If the list contains a dictionary as its first element, process each dictionary in the list
                if value and isinstance(value[0], dict):
                    processed_list = []
                    for item in value:
                        processed_list.append(RemoveConfidenceScore(item))
                    processed_jsonData[key] = processed_list
                else:
                    # Otherwise, store only the first element (actual value) in the processed dictionary
                    processed_jsonData[key] = value[0]
            elif isinstance(value, dict):
                # If the value is a dictionary, recursively process it
                processed_jsonData[key] = RemoveConfidenceScore(value)
            else:
                # If the value is not a list, copy it as is
                processed_jsonData[key] = value
        
        return processed_jsonData
    except Exception as e:
        raise HTTPException(
                    status_code=500,
                    detail="We're sorry, but there was an unexpected issue processing your document. Please try again later."
                )

async def IsAllFieldApprovedCheck(user_id, objGPTResponse,lsApprovedFields, isGemini = False) :
    try:
        if isGemini:
            jsonGPTResponse = objGPTResponse
        else:
            jsonGPTResponse = json.loads(objGPTResponse["choices"][0]["message"]["content"])

        return len(lsApprovedFields) == len(jsonGPTResponse.get("Document", {}).keys())
    except Exception as e:
        await CLogController.MSWriteLog(user_id, "Error", f"Failed to Insert Json Updates to database")
        await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
        return False


async def getAPIJsonStructureResponse(gpt_data, objPromptData):
    try:
        objDocExtractionResponse = None
        strAPIModelSeries = objPromptData.get("ModelSeries",None)
        if strAPIModelSeries is None:
            raise HTTPException(status_code=404, detail=f"gpt_data.DocExtractionPromptID Not found in ModelTable of value {gpt_data.DocExtractionPromptID}")

        if(( "_" in strAPIModelSeries )and (len(strAPIModelSeries.split("_")) >= 1)):
            strAPIModelName = strAPIModelSeries.split("_")[-2]
        else:
            raise HTTPException(status_code=500,detail="Unable to fetch API Model Name")
        if strAPIModelName ==Constants.GeminiAPIModelName:
            if isinstance(gpt_data.Response, object):
                objDocExtractionResponse = gpt_data.Response.text
            elif isinstance(gpt_data.Response, dict):
                objDocExtractionResponse = gpt_data.Response
            else:
                objDocExtractionResponse = {}
        elif strAPIModelName == Constants.GPTAPIModelName:
            objDocExtractionResponse = json.loads(gpt_data.Response["choices"][0]["message"]["content"])
        
        if objDocExtractionResponse:
            objDocExtractionResponse = objDocExtractionResponse
        return objDocExtractionResponse
    except Exception as e:
        raise e

class CJSONFileReader:
    @staticmethod
    def read_json_file(file_path):
        """
        Read JSON data from a file.

        Args:
        - file_path (str): Path to the JSON file.

        Returns:
        - dict: Parsed JSON data as a dictionary.
        """
        try:
            with open(file_path, 'r', encoding='utf-8') as file:
                data = json.load(file)
                return data
        except FileNotFoundError:
            print(f"Error: File '{file_path}' not found.")
            return None
        except json.JSONDecodeError as e:
            print(f"Error decoding JSON from '{file_path}': {str(e)}")
            return None

    @staticmethod
    def get_supplier_details_from_ledger(party_name, ledger_json_path):
            # Load JSON
            with open(ledger_json_path, "r", encoding="utf-8") as f:
                ledgers = json.load(f)

            # Search for matching ledger
            for ledger in ledgers:
                if ledger.get("Name", "").strip().lower() == party_name.strip().lower():
                    return {
                        "party_name": party_name,
                        "gst_in": ledger.get("GSTIN", ""),
                        "state_name": ledger.get("PriorStateName", ""),
                        "country_name": ledger.get("CountryOfResidence", ""),
                        "address_list": ledger.get("Addresses", []),
                    }

            # Default if no match
            return {
                "party_name": party_name,
                "gst_in": "",
                "state_name": "",
                "country_name": "",
                "address_list": [],
            }


    

class CFileHandler:
    
    @staticmethod
    def MSWriteFile(strFilePath, fileContent, strWriteMode="w", strEncoding=None):
        """
        Writes data to the specified file.

        :param strFilePath: Path to the file.
        :param strWriteMode: File opening mode (default is 'w').
        :param strEncoding: Encoding to use for writing (default is None).
        :return: True if successful, False otherwise.
        """
        try:
            with open(strFilePath, strWriteMode, encoding=strEncoding) as file:
                file.write(fileContent)  
            print(f"File {strFilePath} Created.")
            return True
        except Exception as e:
            print(f"Error writing to file {strFilePath}: {e}")
            return False


class CDocument:
    def __init__(self, file_path):
        self.file_path = file_path
        self.filename = os.path.basename(file_path)
        self.content_type = self.get_content_type(self.filename)

    async def read(self):
        if not os.path.exists(self.file_path):
            raise FileNotFoundError(f"File not found at path: {self.file_path}")

        async with aiofiles.open(self.file_path, "rb") as file:
            binary_data = await file.read()

        return binary_data

    def get_content_type(self, filename):
        """
        Determine the content type based on the file extension.

        Args:
        - filename (str): Name of the file.

        Returns:
        - str: Content type of the file.
        """
        content_type_mapping = Constants.strMimeType
        file_extension = os.path.splitext(filename.lower())[1][1:]
        return content_type_mapping.get(file_extension, "application/octet-stream")
    
class CDocxExcelCsv2PDF:
    """
    Purpose : This class provides methods to convert Excel, CSV, and DOCX files to PDF format. It also includes functionalities
              to automatically adjust dimensions of Excel spreadsheets and handle conversion from CSV to Excel before PDF conversion.
    """
    def MSExcelCsvAutoAdjustDimensions(ws):
        """
        Purpose : Automatically adjusts the width and height of cells in an Excel worksheet based on their content.

        Inputs  :   (1) ws : The Excel worksheet instance from openpyxl (Worksheet)

        Output  : None. The adjustments are made directly on the worksheet provided.

        Example : CDocxExcelCsv2PDF.MSExcelCsvAutoAdjustDimensions(ws=worksheet)
        """
        try:
            for col in ws.columns:
                max_length = 0
                column = get_column_letter(col[0].column)  # Get the column name
                for cell in col:
                    if cell.value:
                        cell.alignment = Alignment(wrap_text=True)
                        max_length = max(max_length, len(str(cell.value)))
                adjusted_width = (max_length + 2)
                ws.column_dimensions[column].width = adjusted_width

            for row in ws.iter_rows():
                max_height = 0
                for cell in row:
                    if cell.value:
                        lines = str(cell.value).split('\n')
                        max_height = max(max_height, len(lines))
                for cell in row:
                    ws.row_dimensions[cell.row].height = max_height * 15
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>")

    def MSCsvToExcel(csv_data):
        """
        Purpose : Converts CSV data to Excel format and automatically adjusts the dimensions.

        Inputs  :   (1) csv_data : CSV data in binary format (bytes)

        Output  : Returns a tuple containing the Excel data in binary format and the file extension 'xlsx'.

        Example : excel_data, file_extension = CDocxExcelCsv2PDF.MSCsvToExcel(csv_data=binary_csv_data)
        """
        try:
            objDataFrame = pd.read_csv(io.BytesIO(csv_data))
            objExcelBuffer = io.BytesIO()
            objDataFrame.to_excel(objExcelBuffer, index=False)

            objWorkBook = load_workbook(objExcelBuffer)
            ws = objWorkBook.active

            CDocxExcelCsv2PDF.MSExcelCsvAutoAdjustDimensions(ws)

            objExcelBuffer.seek(0)
            strFileExtension = 'xlsx'
            return objExcelBuffer.getvalue(), strFileExtension
        
        except HTTPException as e:
            raise e
        
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>")
        
    def MSExcelToPdf(excel_data):
        """
        Purpose : Converts Excel data to PDF format.

        Inputs  :   (1) excel_data : Excel data in binary format (bytes)

        Output  : Returns the PDF data in binary format.

        Example : pdf_data = CDocxExcelCsv2PDF.MSExcelToPdf(excel_data=binary_excel_data)
        """
        try:
            with io.BytesIO(excel_data) as excel_file:
                objDataFrame = pd.read_excel(excel_file)
                objHtmlBuffer = io.StringIO()
                objDataFrame.to_html(objHtmlBuffer, index=False)

            # Determine the operating system and configure the path to wkhtmltopdf
            if os.name == 'nt':  # Windows
                wkhtmltopdf_path = r"Y:\Interns\Pavan\Projects\All Programs\15. WKHTML to PDF\wkhtmltox\bin\wkhtmltopdf.exe"
            else:  # Assume Linux/Ubuntu
                wkhtmltopdf_path = '/usr/local/bin/wkhtmltopdf'

            # Specify the configuration for pdfkit
            objConfigOfPdfkit = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)

            # Convert HTML to PDF
            objPdfBuffer = io.BytesIO()
            objPdfBuffer = pdfkit.from_string(objHtmlBuffer.getvalue(), configuration=objConfigOfPdfkit)
            return objPdfBuffer
        
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>")
            
    def MSDocToPdf(docx_data):
        """
        Purpose : Converts DOCX data to PDF format.

        Inputs  :   (1) docx_data : DOCX data in binary format (bytes)

        Output  : Returns the PDF data in binary format.

        Example : pdf_data = CDocxExcelCsv2PDF.MSDocToPdf(docx_data=binary_docx_data)
        """
        try:
            with io.BytesIO(docx_data) as docx_file:
                objConvertedHtmlResult = mammoth.convert_to_html(docx_file)
                objHtmlData = objConvertedHtmlResult.value

            # Determine the operating system and configure the path to wkhtmltopdf
            if os.name == 'nt':  # Windows
                wkhtmltopdf_path = r"Y:\Interns\Pavan\Projects\All Programs\15. WKHTML to PDF\wkhtmltox\bin\wkhtmltopdf.exe"
            else:  # Assume Linux/Ubuntu
                wkhtmltopdf_path = '/usr/local/bin/wkhtmltopdf'

            # Specify the configuration for pdfkit
            objConfigOfPdfkit = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)

            objPdfBuffer = io.BytesIO()
            objPdfBuffer = pdfkit.from_string(objHtmlData, configuration=objConfigOfPdfkit)

            return objPdfBuffer
        
        except zipfile.BadZipFile as e:
            raise HTTPException(status_code=422, detail="We encountered an issue processing your document. It appears the file may be corrupted. Please try uploading a different file.")
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>")
    
    def MSConvertFileToPdf(InputBinaryData, strFileExtension):
        """
        Purpose : Converts various file formats (CSV, XLSX, DOCX) to PDF by determining the file type and handling the conversion accordingly.

        Inputs  :   (1) InputBinaryData : Binary data of the file to be converted (bytes)
                    (2) strFileExtension : File extension indicating the format of the input file (string)

        Output  : Returns the PDF data in binary format, or raises an HTTP exception if the conversion fails.

        Example : pdf_data = CDocxExcelCsv2PDF.MSConvertFileToPdf(InputBinaryData=binary_data, strFileExtension='xlsx')
        """
        try:
            if strFileExtension.lower() == 'csv':
                temp_excel_file, strFileExtension = CDocxExcelCsv2PDF.MSCsvToExcel(InputBinaryData)
            else:
                temp_excel_file = InputBinaryData
            
            if temp_excel_file:
                try:
                    if strFileExtension.lower() in ['xlsx']:
                        return CDocxExcelCsv2PDF.MSExcelToPdf(temp_excel_file)
                    elif strFileExtension.lower() in ['docx']:
                        return CDocxExcelCsv2PDF.MSDocToPdff(temp_excel_file)
                    elif strFileExtension.lower() == 'txt':
                        return CDocxExcelCsv2PDF.MSTxtToPdf(temp_excel_file)
                except HTTPException as e:
                    raise e
                except Exception as e:
                    raise HTTPException(status_code=400, detail=f"Unsupported file format: '{strFileExtension}'. Please upload supported Document!!!")
            
        except HTTPException as e:
            raise e
        
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>")
    
    def MSTxtToPdf(txt_data):
        try:
            # Decode the bytes to string assuming UTF-8 encoding
            txt_content = txt_data.decode('utf-8')
            # Wrap the text in HTML <pre> tag for formatting
            html_content = f"<pre>{txt_content}</pre>"
            
            if os.name == 'nt':  # Windows
                wkhtmltopdf_path = r"Y:\Interns\Pavan\Projects\All Programs\15. WKHTML to PDF\wkhtmltox\bin\wkhtmltopdf.exe"
            else:  # Assume Linux/Ubuntu
                wkhtmltopdf_path = '/usr/local/bin/wkhtmltopdf'

            objConfigOfPdfkit = pdfkit.configuration(wkhtmltopdf=wkhtmltopdf_path)

            # Generate the PDF from the HTML content
            pdf_data = pdfkit.from_string(html_content, False, configuration=objConfigOfPdfkit)

            return pdf_data
        except UnicodeDecodeError:
            raise HTTPException(status_code=400, detail="Invalid text file content")
            
        except Exception as e:
            raise HTTPException(status_code=500, detail="Something went wrong while processing your request!!! <NAME_EMAIL>.")


class CDirectoryHelper:

    @staticmethod
    def MSGetLatestFile(directory: str, extension: Optional[str] = None, prefix: str = "FilteredStockItemExport") -> Optional[str]:
        """
        Get the latest file in the given directory, optionally filtered by file extension and filename prefix.
        
        Args:
            directory (str): The directory path to search for files.
            extension (Optional[str]): The file extension to filter by (e.g., '.txt'). Defaults to None.
            prefix (str): The filename prefix to filter by (e.g., 'FilteredStockItemExport'). Defaults to "FilteredStockItemExport".
        
        Returns:
            Optional[str]: The path to the latest file matching the criteria or None if no files are found.
        """
        try:
            dir_path = Path(directory)

            if not dir_path.is_dir():
                print(f"Error: The directory '{directory}' does not exist or is not a directory.")
                return None

            # List all files in the directory
            files = [f for f in dir_path.iterdir() if f.is_file()]

            # Debug: List all files found
            # print(f"All files in directory '{directory}': {[str(f) for f in files]}")

            # Filter by extension if provided
            if extension:
                files = [f for f in files if f.suffix.lower() == extension.lower()]
                # Debug: Files after extension filter
                # print(f"Files after filtering by extension '{extension}': {[str(f) for f in files]}")

            # Filter by prefix
            if prefix:
                files = [f for f in files if f.name.startswith(prefix)]
                # Debug: Files after prefix filter
                # print(f"Files after filtering by prefix '{prefix}': {[str(f) for f in files]}")

            if files:
                latest_file = max(files, key=lambda f: f.stat().st_mtime)
                # Debug: Latest file found
                # print(f"Latest file: {latest_file}")
                return str(latest_file)
            else:
                print("No files found matching the given criteria.")
                return None
        except Exception as e:
            print(f"Error: {e}")
            return None

    @staticmethod   
    def MSGetLatestFileFromNestedFolder(directory: str, extension: Optional[str] = None) -> Optional[str]:
        """
        Get the latest Excel file from the most recently created subfolder in the given directory,
        optionally filtered by filename prefix.
        
        Args:
            directory (str): The directory path to search for subfolders.
            extension (Optional[str]): The file extension to filter by (e.g., '.xlsx'). 
                Defaults to None, which will filter for '.xlsx' or '.xls'.
            prefix (str): The filename prefix to filter by (e.g., 'FilteredStockItemExport'). 
                Defaults to "FilteredStockItemExport".
        
        Returns:
            Optional[str]: The path to the latest Excel file matching the criteria or None if no files are found.
        """
        try:
            dir_path = Path(directory)

            if not dir_path.is_dir():
                print(f"Error: The directory '{directory}' does not exist or is not a directory.")
                return None

            # Get all subfolders in the directory
            subfolders = [f for f in dir_path.iterdir() if f.is_dir()]

            if not subfolders:
                print(f"No subfolders found in directory '{directory}'.")
                return None

            # Find the most recently created subfolder
            latest_folder = max(subfolders, key=lambda f: f.stat().st_ctime)

            # Set default Excel extensions if none provided
            excel_extensions = ['.xlsx', '.xls'] if extension is None else extension

            # List all files in the latest subfolder
            files = [f for f in latest_folder.iterdir() if f.is_file()]

            # Filter by Excel extensions
            files = [f for f in files if f.suffix.lower() in excel_extensions]

            if files:
                latest_file = max(files, key=lambda f: f.stat().st_mtime)
                return str(latest_file)
            else:
                print(f"No Excel files found in subfolder '{latest_folder}' matching the given criteria.")
                return None

        except Exception as e:
            print(f"Error: {e}")
            return None
   

    def create_zip_file(file_paths, destination_path):
        """
        Input:

            1) file_paths: list
            A list of file paths to be included in the zip file. Each file path should point to an existing file.

            2) destination_path: str
            The full path where the zip file will be created and saved. The directory will be created if it doesn't exist.

        Output:

            str: The full path to the created zip file.

        Purpose:

            To create a zip file containing all the files provided in the file_paths list and save it to the specified destination.
            Files that do not exist are skipped, and a warning is logged for each.

        Example:

            file_paths = ["C:/files/file1.txt", "C:/files/file2.txt"]
            destination_path = "C:/zipped_files/my_archive.zip"
            zip_path = create_zip_file(file_paths, destination_path)
            print(f"Zip file created at: {zip_path}")

        """
        if not file_paths:
            raise ValueError("file_paths cannot be empty.")

        # Ensure the destination directory exists
        destination_dir = os.path.dirname(destination_path)
        if not os.path.exists(destination_dir):
            os.makedirs(destination_dir)

        # Create the zip file
        with zipfile.ZipFile(destination_path, 'w') as zipf:
            for file_path in file_paths:
                if not os.path.exists(file_path):
                    print(f"Warning: File not found and skipped: {file_path}")
                    continue
                arcname = os.path.basename(file_path)  # Use only the file name in the zip
                zipf.write(file_path, arcname=arcname)

        return destination_path

class CAVXMLParser:
    # "Purpose: Conversion of XML tags specific to AccuVelocity."

    @staticmethod
    def isClientVoucherImported200(xmlContent: str) -> bool:
        """
        Returns True if any <EXCEPTIONS> or <CREATED> tag has value 1 in the XML content.
        Returns False in all other cases, including empty or malformed XML.
        The function is dynamic and doesn't require specific tag positions.
        """
        if not xmlContent.strip():
            return False  # Empty content

        try:
            # Parse the XML content
            root = ET.fromstring(xmlContent)

            # Traverse the entire XML tree and check for <EXCEPTIONS> and <CREATED> tags
            for elem in root.iter():
                # Check if the tag is EXCEPTIONS or CREATED
                if elem.tag in ["EXCEPTIONS", "CREATED"]:
                    # Check if the value of the tag is "1"
                    if elem.text == "1":
                        return True

            return False  # If no matching tag found with value "1"
        except ET.ParseError:
            return False  # Malformed XML


    @staticmethod
    def MSConvertSpecificXMLTagsToExcel(input_xml_path, output_excel_path, start_path, target_tags, nested_tags=None):
        """
        Converts specified XML data to an Excel file.

        :param input_xml_path: Path to the input XML file.
        :param output_excel_path: Path where the Excel file will be saved.
        :param start_path: The XML path from where to start extracting data (e.g., 'ENVELOPE/BODY/DATA/COLLECTION').
        :param target_tags: List of flat tags to extract from each target element.
        :param nested_tags: Dictionary where keys are parent tags and values are lists of nested tags to extract.
        """
        # Check if the input XML file exists
        if not os.path.exists(input_xml_path):
            print(f"Error: The file {input_xml_path} does not exist.")
            return

        # Parse the XML file
        try:
            # Read and sanitize XML content
            with open(input_xml_path, "r", encoding="utf-8") as file:
                raw_xml = file.read()
            
            sanitized_xml = CXmlParser.MSSanitizeXML(raw_xml)

            # Parse sanitized XML content
            root = ET.fromstring(sanitized_xml)
            # tree = ET.parse(input_xml_path)
            # root = tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing XML file: {e}")
            return

        # Navigate to the starting path
        current_element = root
        for tag in start_path.split('/'):
            found = current_element.find(tag)
            if found is not None:
                current_element = found
            else:
                print(f"Error: Tag '{tag}' not found in the XML structure.")
                return

        # Collect data
        data = []
        for item in current_element.findall('STOCKITEM'):
            item_data = {}
            
            # Extract flat tags
            for tag in target_tags:
                if tag == 'STOCKITEM':
                    # 'STOCKITEM' corresponds to the 'NAME' attribute
                    item_data['STOCKITEM'] = item.get('NAME', '').replace('&amp;', '&')  # Handle XML entity
                else:
                    element = item.find(tag)
                    if element is not None and element.text is not None:
                        item_data[tag] = element.text.strip()
                    else:
                        item_data[tag] = ''

            # Extract nested tags
            if nested_tags:
                for parent_tag, child_tags in nested_tags.items():
                    parent_element = item.find(parent_tag)
                    if parent_element is not None:
                        for child_tag in child_tags:
                            # Create a unique key for nested tags, e.g., 'HSN_APPLICABLEFROM'
                            unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                            child_element = parent_element.find(child_tag)
                            if child_element is not None and child_element.text is not None:
                                item_data[unique_key] = child_element.text.strip()
                            else:
                                item_data[unique_key] = ''
                    else:
                        # If the parent tag is missing, set all child tags as empty
                        for child_tag in child_tags:
                            unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                            item_data[unique_key] = ''

            data.append(item_data)

        # Define the column order
        columns = target_tags.copy()
        if nested_tags:
            for parent_tag, child_tags in nested_tags.items():
                for child_tag in child_tags:
                    unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                    columns.append(unique_key)

        # Create DataFrame
        df = pd.DataFrame(data, columns=columns)

        # Write to Excel
        try:
            df.to_excel(output_excel_path, index=False)
            print(f"Successfully wrote data to {output_excel_path}")
            return output_excel_path
        except Exception as e:
            print(f"Error writing to Excel: {e}")
          
def ReadExcelToDict(filepath):
    """
    Reads an Excel file and stores its content as a list of dictionaries.

    Args:
        filepath (str): The path to the Excel file.

    Returns:
        list: A list of dictionaries representing the rows in the Excel file.

    Raises:
        ValueError: If the file cannot be read.
    """
    try:
        # Read the Excel file into a Pandas DataFrame
        df = pd.read_excel(filepath)

        # Replace NaN values with empty strings
        df.replace({np.nan: ''}, inplace=True)

        # Convert DataFrame to a list of dictionaries
        data_list = df.to_dict(orient="records")
        # TODO: Use Pegasus Object to store important column details only
        return data_list
    except Exception as e:
        raise ValueError(f"Invalid Excel format")


def _split_pages_task(input_pdf_path, output_dir, input_filename, start_page_index, end_page_index, timestamp):
    """
    Helper function to split and save a range of pages.
    This function will be executed by each process in the pool.
    """
    try:
        reader = PdfReader(input_pdf_path)
        writer = PdfWriter()
        
        # Add the specific range of pages to the writer
        for j in range(start_page_index, end_page_index):
            writer.add_page(reader.pages[j])

        # Generate a meaningful filename with start and end page numbers
        # Adjusting page numbers for 1-based indexing for display
        output_filename = f"{input_filename}_PG{start_page_index + 1}To{end_page_index}_TS{timestamp}.pdf"
        output_filepath = os.path.join(output_dir, output_filename)
        
        with open(output_filepath, 'wb') as output_pdf:
            writer.write(output_pdf)
        CLogger.MCWriteLog("info",f"Successfully split and saved: {output_filename}")

        return output_filename, output_filepath
    except Exception as e:
        CLogger.MCWriteLog("error",f"Failed to split page {start_page_index + 1} to {end_page_index} from {input_pdf_path}: {e}")
        # logging.error(f"Failed to split page {page_index + 1} from {input_pdf_path}: {e}")
        return None, None

# def SplitMergedPDF_Parallel(input_pdf_path, output_dir, split_size=1):
#     """
#     Purpose : Splits a PDF into multiple single-page PDFs using multiprocessing for parallelism.

#     Inputs  : (1) input_pdf_path : Path to the input PDF file (str)
#               (2) output_dir     : Directory path where the split PDF files will be saved (str)

#     Output  : Returns a dictionary where keys are filenames and values are full paths to the split PDF files.

#     Example : split_pdfs = SplitMergedPDF_Parallel(input_pdf_path='/path/to/input.pdf',
#                                                    output_dir='/path/to/output')
#     """
#     if not os.path.exists(output_dir):
#         os.makedirs(output_dir)

#     input_filename = os.path.splitext(os.path.basename(input_pdf_path))[0]
    
#     try:
#         reader = PdfReader(input_pdf_path)
#         total_pages = len(reader.pages)
#     except Exception as e:
#         CLogger.MCWriteLog("error",f"Failed to read input PDF {input_pdf_path}: {e}")
#         # logging.error(f"Failed to read input PDF {input_pdf_path}: {e}")
#         raise RuntimeError(f"Could not read input PDF: {e}")

#     split_pdfs = {}
#     timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") # More comprehensive timestamp

#     # Determine the number of workers.
#     # A good starting point is os.cpu_count() or os.cpu_count() - 1 to leave one core free.
#     # You have 16 cores, 28 logical processors.
#     # For CPU-bound tasks, stick closer to physical cores or slightly more.
#     # Let's use `min(os.cpu_count(), 20)` as a reasonable upper bound if you have many logical cores.
#     # For a 16-core CPU, 16-20 workers is a good range.
#     max_workers = min(os.cpu_count() or 1, 20) 
#     CLogger.MCWriteLog("info",f"Using {max_workers} processes for PDF splitting.")
#     # logging.info(f"Using {max_workers} processes for PDF splitting.")

#     # Using ProcessPoolExecutor for CPU-bound tasks
#     with ProcessPoolExecutor(max_workers=max_workers) as executor:
#         futures = []
#         # Iterate to define page ranges for each task
#         for i in range(0, total_pages, split_size):
#             start_page = i
#             end_page = min(i + split_size, total_pages)
#             # Submit a task for each defined page range
#             futures.append(executor.submit(_split_pages_task, 
#                                            input_pdf_path, 
#                                            output_dir, 
#                                            input_filename, 
#                                            start_page, 
#                                            end_page, 
#                                            timestamp))
#         # Collect results as they complete
#         for future in futures:
#             filename, filepath = future.result()
#             if filename and filepath:
#                 split_pdfs[filename] = filepath
#             else:
#                 pass # Error logged in _split_pages_task
#     CLogger.MCWriteLog("info",f"Finished splitting {total_pages} pages from {input_pdf_path}")
#     # logging.info(f"Finished splitting {total_pages} pages from {input_pdf_path}")
#     return split_pdfs

# --- Original sequential function for comparison ---
def SplitMergedPDF_Sequential(input_pdf_path, output_dir, split_size=1):
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    input_filename = os.path.splitext(os.path.basename(input_pdf_path))[0]
    reader = PdfReader(input_pdf_path)
    total_pages = len(reader.pages)
    
    split_pdfs = {}
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S") # Use consistent timestamp format
    for i in range(0, total_pages, split_size):
        writer = PdfWriter()
        start_page = i
        end_page = min(i + split_size, total_pages)
        
        for j in range(start_page, end_page):
            writer.add_page(reader.pages[j])

        output_filename = f"{input_filename}_PG{start_page + 1}To{end_page}_TS{timestamp}.pdf"
        output_filepath = os.path.join(output_dir, output_filename)
        
        try:
            with open(output_filepath, 'wb') as output_pdf:
                writer.write(output_pdf)
            split_pdfs[output_filename] = output_filepath
            CLogger.MCWriteLog("info",f"Sequential: Successfully split and saved: {output_filename}")
            # logging.info(f"Sequential: Successfully split and saved: {output_filename}")
        except Exception as e:
            CLogger.MCWriteLog("error",f"Sequential: Failed to write PDF file {output_filename}: {str(e)}")
            # logging.error(f"Sequential: Failed to write PDF file {output_filename}: {str(e)}")
            raise RuntimeError(f"Failed to write PDF file {output_filename}: {str(e)}")

    return split_pdfs

def SplitMergedPDF(input_pdf_path, output_dir, split_size=1):
    """
    Purpose : Splits a PDF into multiple PDFs based on the number of pages per split.
    
    Inputs  :   (1) input_pdf_path : Path to the input PDF file (str)
               (2) output_dir     : Directory path where the split PDF files will be saved (str)
               (3) split_size     : Number of pages per split PDF (int, default is 1)
    
    Output  : Returns a dictionary where keys are filenames and values are full paths to the split PDF files.
    
    Example : split_pdfs = SplitMergedPDF(input_pdf_path='/path/to/input.pdf', 
                                               output_dir='/path/to/output', 
                                               split_size=2)
    
    """
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    input_filename = os.path.splitext(os.path.basename(input_pdf_path))[0]
    reader = PdfReader(input_pdf_path)
    total_pages = len(reader.pages)
    
    split_pdfs = {}
    # Get current timestamp for suffix
    timestamp = datetime.now().strftime("%H%M%S")
    for i in range(0, total_pages, split_size):
        writer = PdfWriter()
        start_page = i
        end_page = min(i + split_size, total_pages)
        
        for j in range(start_page, end_page):
            writer.add_page(reader.pages[j])

        # Generate a meaningful filename with timestamp
        output_filename = f"{input_filename}_PG{start_page + 1}To{end_page}_TS{timestamp}.pdf"
        output_filepath = os.path.join(output_dir, output_filename)
        
        try:
            with open(output_filepath, 'wb') as output_pdf:
                writer.write(output_pdf)
            split_pdfs[output_filename] = output_filepath
        except Exception as e:
            raise RuntimeError(f"Failed to write PDF file {output_filename}: {str(e)}")

    return split_pdfs

def split_pdf_MultipleVendor(pdf_path, split_info, output_dir):
    # Create the output directory if it doesn't exist
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)

    # Open the original PDF
    with open(pdf_path, 'rb') as file:
        pdf_reader = PyPDF2.PdfReader(file)
        total_pages = len(pdf_reader.pages)
        
        # List to store the paths of the split PDFs
        split_paths = []

        # Iterate through the vendor names to create new PDFs
        vendor_names = split_info.get("VendorNames", [])
        
        for i, vendor_info in enumerate(vendor_names):
            vendor_name = vendor_info["VendorName"]
            start_page = int(vendor_info["PageNumber"])
            end_page = int(vendor_names[i + 1]["PageNumber"]) - 1 if i + 1 < len(vendor_names) else total_pages

            # Create a PDF writer object for each split PDF
            pdf_writer = PyPDF2.PdfWriter()

            # Add pages to the writer object
            for page_num in range(start_page - 1, end_page):  # pages are zero-indexed
                pdf_writer.add_page(pdf_reader.pages[page_num])

            # Get the current timestamp
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            
            # Generate a unique UUID
            unique_id = ''.join(random.choices(string.ascii_letters + string.digits, k=4))

            # Define the new output file path with vendor name, timestamp, and UUID
            output_pdf_name = f"{vendor_name}_{timestamp}_{unique_id}.pdf"
            output_pdf_path = os.path.join(output_dir, output_pdf_name)

            # Write the pages to the new PDF
            with open(output_pdf_path, 'wb') as output_file:
                pdf_writer.write(output_file)

            # Add the output path to the list of split PDF paths
            split_paths.append(output_pdf_path)

        return split_paths

class TransactionUpdater:
    @staticmethod
    async def MSUpdateTransactionTable(
        intTxnId: int,
        intStatementId: int,
        strClientREQID: str,
        ClientImportedXMLStatusCode: int,
        CReqIMPORTEDXMLTimeAt: datetime,
        CImportedXML_Type: str,
        xml_content: str = None,
        engine=None
    ) -> bool:
        """
        Static method to update XML import metadata for a transaction row in 'transactions' table.

        Args:
            intTxnId (int): Transaction ID
            intStatementId (int): Statement ID
            strClientREQID (str): Client request ID
            ClientImportedXMLStatusCode (int): Status code to store
            CReqIMPORTEDXMLTimeAt (datetime): Time of import
            CImportedXML_Type (str): Type of import
            xml_content (str, optional): XML content to store (can be None)
            engine (AsyncEngine, optional): SQLAlchemy async engine. Created from env vars if None.

        Returns:
            bool: True if update succeeded, False otherwise.

        Raises:
            RuntimeError: If update fails.
        """
        try:
            if engine is None:
                driver = os.getenv("BS_DB_DRIVER").replace("+pymysql", "+aiomysql")
                engine = create_async_engine(
                    f"{driver}://{os.getenv('BS_DB_USERNAME')}:{os.getenv('BS_DB_PASSWORD')}@"
                    f"{os.getenv('BS_DB_HOST')}:{os.getenv('BS_DB_PORT')}/{os.getenv('BS_DB_NAME')}",
                    echo=False,
                    future=True
                )

            metadata = MetaData()
            async with engine.begin() as conn:
                    await conn.run_sync(lambda sync_conn: metadata.reflect(sync_conn, only=["transactions"]))

            transactions = metadata.tables["transactions"]

            # Prepare the update values
            update_values = {
                "ClientImportedXMLResponse": ClientImportedXMLStatusCode,
                "CReqIMPORTEDXMLTimeAt": CReqIMPORTEDXMLTimeAt,
                "CImportedXML_Type": CImportedXML_Type
            }

            if xml_content is not None:
                update_values["ClientImportedXMLResponse"] = xml_content

            # Perform the update
            async with AsyncSession(engine) as session:
                stmt = (
                    update(transactions)
                    .where(
                        and_(
                            transactions.c.intTxnId == intTxnId,
                            transactions.c.intStatementId == intStatementId,
                            transactions.c.strClientREQID == strClientREQID
                        )
                    )
                    .values(**update_values)
                )

                result = await session.execute(stmt)
                await session.commit()
                await engine.dispose()
                return result.rowcount > 0

        except Exception as e:
            raise RuntimeError(f"Failed to update transaction XML import fields: {e}")

    @staticmethod
    async def MSUpdateTransactionXMLResponse(
        intTxnId: int,
        intStatementId: int,
        strXMLResponse: str,
        engine=None
    ) -> bool:
        """
        Updates the 'strXMLResponse' field for a specific transaction.

        Args:
            intTxnId (int): Transaction ID.
            intStatementId (int): Statement ID.
            strXMLResponse (str): XML response content to store.
            engine (AsyncEngine, optional): SQLAlchemy async engine. Created from env vars if None.

        Returns:
            bool: True if the update was successful, False otherwise.

        Raises:
            RuntimeError: If the update fails.
        """
        try:
            if engine is None:
                driver = os.getenv("BS_DB_DRIVER").replace("+pymysql", "+aiomysql")
                engine = create_async_engine(
                    f"{driver}://{os.getenv('BS_DB_USERNAME')}:{os.getenv('BS_DB_PASSWORD')}@"
                    f"{os.getenv('BS_DB_HOST')}:{os.getenv('BS_DB_PORT')}/{os.getenv('BS_DB_NAME')}",
                    echo=False,
                    future=True
                )

            metadata = MetaData()
            async with engine.begin() as conn:
                await conn.run_sync(lambda sync_conn: metadata.reflect(sync_conn, only=["transactions"]))

            transactions = metadata.tables["transactions"]

            async with AsyncSession(engine) as session:
                stmt = (
                    update(transactions)
                    .where(
                        and_(
                            transactions.c.intTxnId == intTxnId,
                            transactions.c.intStatementId == intStatementId
                        )
                    )
                    .values(strXMLResponse=strXMLResponse)
                )

                result = await session.execute(stmt)
                await session.commit()
                await engine.dispose()
                return result.rowcount > 0

        except Exception as e:
            raise RuntimeError(f"Failed to update strXMLResponse: {e}")

class CRemarkTranslator:
    """
    A class to translate and rephrase Hindi remarks from an Excel file using Google Cloud Translation API.

    This class reads remarks from an Excel file, translates them from Hindi to English, applies a custom
    dictionary for specific terms, rephrases the translated text for clarity, and logs the input and output.
    It uses the Google Cloud Translation API for translation and handles errors robustly.

    Attributes:
        project_id (str): Google Cloud project ID from environment variables.
        client (TranslationServiceClient): Google Cloud Translation client.
        parent (str): Parent resource path for translation API.
        custom_dict (dict): Dictionary mapping Hindi terms to English equivalents.
        log_file (str): Path to the JSON file for storing translation records.
        excel_file (str): Path to the Excel file containing remarks.
    """
    
    def __init__(self):
        """Initialize the RemarkTranslator with environment variables and Google Cloud client."""
        load_dotenv()
        
        self.project_id = os.getenv("GOOGLE_CLOUD_PROJECT")
        if not self.project_id:
            raise ValueError("GOOGLE_CLOUD_PROJECT environment variable not set")
        
        try:
            self.client = translate.TranslationServiceClient()
            self.location = "global"
            self.parent = f"projects/{self.project_id}/locations/{self.location}"
        except Exception as e:
            raise RuntimeError(f"Error initializing Translation client: {e}")
        
        self.custom_dict = {
            "nasta": "breakfast",
            "kharchi": "purchase",
            "bartan": "utensils"
        }
        
        # self.log_file = "translation_records_google_api.json"
        # self.excel_file = r"\\192.168.1.109\user_data\MITUL\Downloads\AI Imprest.xlsx"

    async def MTranslateAndRephrase(self, remark: str) -> str:
        """
        Translate a Hindi remark to English, apply custom terms, and rephrase for clarity.

        Args:
            remark (str): The Hindi remark to translate.

        Returns:
            str: The rephrased English text or the original remark as string if translation fails.
        """
        try:
            # Step 0: Ensure remark is a string and not nan/float
            if remark is None or not isinstance(remark, str) or remark.strip().lower() in {"nan", "none"}:
                return ""

            # Step 1: Translate Hindi to English
            mime_type = "text/plain"
            request = translate.TranslateTextRequest(
                contents=[remark],
                mime_type=mime_type,
                source_language_code="hi",
                target_language_code="en",
                parent=self.parent
            )
            response = self.client.translate_text(request=request)
            translated = response.translations[0].translated_text

            # Step 2: Replace specific terms using custom dictionary
            for hindi, english in self.custom_dict.items():
                translated = translated.replace(hindi.lower(), english)

            # Step 3: Rephrase for clarity and conciseness
            parts = translated.split()
            quantity = parts[0] if parts and parts[0].isdigit() else "1"
            item = " ".join(parts[1:-2]) if len(parts) > 3 else " ".join(parts[1:-1]) or "item"
            location = " ".join(parts[-2:]) if len(parts) >= 2 else ""

            rephrased = f"{quantity} {item.capitalize()} for {location.capitalize()}"
            translated = translated.replace("&", "&amp;")

            return str(translated)

        except exceptions.GoogleAPIError as e:
            return str(remark) if isinstance(remark, str) else ""

        except Exception as e:
            return str(remark) if isinstance(remark, str) else ""


    async def process_remarks(self) -> None:
        """
        Read remarks from the Excel file and process them for translation and rephrasing.
        """
        try:
            # Read Excel file
            df = pd.read_excel(self.excel_file, sheet_name="DATA")
            
            # Check if 'Remarks' column exists
            if 'Remarks' not in df.columns:
                print("Error: 'Remarks' column not found in the Excel file.")
                return
            
            # Extract remarks, convert to string, and filter out NaN/empty values
            remarks = df['Remarks'].dropna().astype(str).tolist()
            
            if not remarks:
                print("No valid remarks found in the 'Remarks' column.")
                return
            
            # Process each remark
            for remark in remarks:
                result = await self.translate_and_rephrase(remark)
                print(f"Original: {remark} -> Rephrased: {result}")
        
        except FileNotFoundError:
            print(f"Error: Excel file '{self.excel_file}' not found.")
        except Exception as e:
            print(f"Error reading Excel file: {e}")

class ImprestReportGenerator:
    @staticmethod
    def create_csv_report(data: list[dict], file_path:str) -> str:
        """
        Creates a CSV report from a list of dictionaries with specified columns, 
        using '-' for missing values, and returns the file path.

        Parameters
        ----------
        data : list[dict]
            List of dictionaries containing data for the report. Each dictionary
            may contain keys matching the required columns.

        Returns
        -------
        str
            The file path of the generated CSV report.

        Examples
        --------
        >>> data = [
        ...     {'JVSheetNo': 12345, 'EmprestHolder': 'John Doe', 'SiteName': 'Main Campus', 
        ...      'TotalAmount': 5000.0, 'TotalEntries': 3, 'RecievedDate': '2025-06-05'},
        ...     {'JVSheetNo': 67890, 'EmprestHolder': 'Jane Doe', 'SiteName': 'Downtown'}
        ... ]
        >>> file_path = create_csv_report(data)
        >>> print(file_path)
        'report_20250605_2203.csv'
        """
        # Define required columns
        columns = [
            'Recieved Date', 'JV Sheet No', 'Site Name', 'Emprest Holder',  'Email Address', 
            'Date Of Expense Period', 'Total Amount', 'Total Entries', 'Est Accountant Time Saved', 
            'AV XML Generated Status', 'AV Comments' 
        ]
        
        # Create a list of dictionaries with all columns, filling missing values with '-'
        processed_data = []
        for index, item in enumerate(data, start=1):
            row = {'Sr No.':index,
                **{col: CStringFormat.MSFormatINRAmount(item.get('TotalAmount', 0)) 
                   if col == 'Total Amount' 
                   else item.get(col.replace(" ",""), '-') 
                   for col in columns}}
            processed_data.append(row)
        
        # Create DataFrame
        df = pd.DataFrame(processed_data)
        # Save to CSV
        df.to_csv(file_path, index=False)
        
        # Return absolute file path
        return os.path.abspath(file_path)


def FormatAmount(strTotalAmount):
    # Convert the string to an integer, format it with commas, and return as string
    return "{:,}".format(int(strTotalAmount))


# # --- Example Usage ---
# if __name__ == "__main__":
#     test_pdf_path = r"C:\Users\<USER>\Downloads\ilovepdf_merged (2).pdf" # Make sure this PDF exists for testing
#     output_directory_parallel = r"C:\Users\<USER>\Documents\AV_bVersion1.4\TestSplitParallel"
#     output_directory_sequential = r"C:\Users\<USER>\Documents\AV_bVersion1.4\TestSplitSequential"
    
#     # Define a split size for demonstration (e.g., 2 pages per document)
#     desired_split_size = 1

#     # # Create a dummy PDF for testing if it doesn't exist
#     # if not os.path.exists(test_pdf_path):
#     #     print(f"Creating a dummy PDF: {test_pdf_path} for testing...")
#     #     writer = PdfWriter()
#     #     for i in range(150): # Create a 150-page dummy PDF
#     #         writer.add_blank_page(width=72, height=72)
#     #     with open(test_pdf_path, "wb") as f:
#     #         writer.write(f)
#     #     print("Dummy PDF created.")

#     print(f"\n--- Running Parallel PDF Split (split_size={desired_split_size}) ---")
#     start_time_parallel = datetime.now()
#     try:
#         parallel_split_files = SplitMergedPDF(test_pdf_path, output_directory_parallel, split_size=desired_split_size)
#         end_time_parallel = datetime.now()
#         print(f"Parallel split completed in {(end_time_parallel - start_time_parallel).total_seconds():.2f} seconds.")
#         print(f"Generated {len(parallel_split_files)} files in {output_directory_parallel}")
#     except Exception as e:
#         print(f"Parallel split failed: {e}")

#     print(f"\n--- Running Sequential PDF Split (split_size={desired_split_size}, for comparison) ---")
#     start_time_sequential = datetime.now()
#     try:
#         sequential_split_files = SplitMergedPDF_Sequential(test_pdf_path, output_directory_sequential, split_size=desired_split_size)
#         end_time_sequential = datetime.now()
#         print(f"Sequential split completed in {(end_time_sequential - start_time_sequential).total_seconds():.2f} seconds.")
#         print(f"Generated {len(sequential_split_files)} files in {output_directory_sequential}")
#     except Exception as e:
#         print(f"Sequential split failed: {e}")
        
# if __name__ == "__main__":
    # Example usage
    # lsEstimatedCurrentRequestTimeSaved = ["~ 2 min", "~ 4 min 10 sec", "~ 3 min 50 sec"]
    # output = CommonHelper.MSGetTotalTimeSaved(lsEstimatedCurrentRequestTimeSaved)
    # print("Output",output)  # Output: "~ 9 min 40 sec"
    # pass
    # CExcelHelper.MSFormatExcel(
    #     sExcelFilePath=r"C:\Users\<USER>\Desktop\customer\REAL\AccuVelocity\GitIgnore\PricelistTest\Simpolo_doc_2429_pricelist_report.xlsx",
    #     bIsSheetObject=False,
    #     lsWordWrapColumns=["Matched PricelistItem Property", "Qty(Box)"],
    #     column_width_mapping={
    #         "Matched PricelistItem Property": 50,
    #         "Qty(Box)": 15
    #     },
    #     auto_adjust_width=True  # Enable auto-adjust column widths
    # )

    # pass
    # print(CDirectoryHelper.MSGetLatestFile(directory=r"H:\AI Data\17_ParagTraders\StockItemsDB", extension=".xlsx", prefix= "FilteredStockItemExport"))
    # dfExcel = CExcelHelper.MSReadExcel(file_path=r"D:\USER_DATA\user\Downloads\AV_Template_Quotation_Details.xlsx", columns_to_verify= ['ItemSRNO', 'ClientName', 'OrderTerms1', 'OrderTerms2', 'ClientAddress1', 'ClientAddress2', 'ClientAddress3', 'ClientAddress4', 'ClientState', 'ClientCountry', 'ClientPinCode', 'ClientGSTRegistrationType', 'ClientGSTIN', 'CartageAmount'], bVerify= True)
    # dictJsonData = CExcelHelper.MSExcelToJSON(df=dfExcel)
    # print(dictJsonData)
    
    # CGPTJsonHelper.MSSaveExtractedDataFromResponseObject(r"H:\AI Data\26_Gwalia\16_Sygnia\gptResponse", r"H:\AI Data\26_Gwalia\16_Sygnia\gptResponse\ContentOnly", ["InvoiceNo"])

    # ------------ split pdf test -------------------- 
    # input_pdf_path = r"H:\Customers\26_Gwalia\Manual Testing\AV-Multiple-Vendor-Single-Doc.pdf"
    # output_dir = r"H:\Customers\26_Gwalia\Manual Testing"
    # split_size = 1  # Split every 2 pages

    # split_files = SplitMergedPDF(input_pdf_path, output_dir, split_size)
    # print(split_files)
    # print(split_files.values())

    # -------------------- end of split pdf test -------------------- 

    # GPTHelper.MSKeepSpecifiedPages(r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\Scan1.pdf","1,2,3,4-6,8-15,20-32",r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\removed unwanted pages in pdf")
    # GPTHelper.MSKeepSpecifiedPages(
    #     r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\Scan1.pdf",
    #     "1,2,3,4-6,8-15,20-32",
    #     r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\removed unwanted pages in pdf\output.pdf"
    # )
    # CRegexMethod.MSExtractTallyResDocDetails(r"TallyXMLResponse-Quotation_Delivery_Note-REQ_CSAVDEVELOPER_TS175223_UIDTHWIR5_Quotation_Pty_MR_SANJAY_CHOUDHRY_JI_No_241_dt_10_Jan_2025_dbsPar20250110172831-468-1-638721269118449737.xml")