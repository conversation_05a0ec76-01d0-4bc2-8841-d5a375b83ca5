import { useEffect, useState } from 'react';
import Header from '../components/Header';
import Footer from '../components/footer';
import stockImage from '../assets/FileHistoryStock.svg';
import stockGridImage from "../assets/image.png";
import starSymbol from "../assets/starSymbol.svg";
import { FaPlay, FaChevronRight, FaChevronDown } from 'react-icons/fa';
import caseStudyStock from "../assets/casestudystock.svg";
import smartCheckLogo from "../assets/smartCheckLogo.svg";
import smartCheckLogoW from "../assets/smartCheckLogoW.svg";
import rpaIntegrationLogo from "../assets/rpaIntegrationLogo.svg";
import rpaIntegrationLogoW from "../assets/rpaIntegrationLogoW.svg";
import stockVideoIntro from "../assets/stockVideoIntro.mp4"
import crt1 from "../assets/Certifications/IAF.png"
import crt2 from "../assets/Certifications/ISO.png"
import { Typography } from "@material-tailwind/react";
import { TypewriterEffectSmooth } from '../components/Home/TypeWrite';
import DemoDocViewer from '../components/DemoDocViewer';
// import AnimatedBorderButton from '../components/Home/MovingBorderButton';

const MainAV = () => {

  const [index, setIndex] = useState(0);
  const [activeTab, setActiveTab] = useState('Accounts Payable');
  const [showSecondTypewriter, setShowSecondTypewriter] = useState(false);

  const tabs = ['Accounts Payable', 'Recruitment', 'Compliance'];

  const [hoveredCard, setHoveredCard] = useState(null);

  const handleMouseEnter = (card) => {
    setHoveredCard(card);
  };

  const handleMouseLeave = () => {
    setHoveredCard(null);
  };

  const currentValue = {
    "Full Name": "VAISHNAVI POPAT",
    "Gender": "",
    "Birth Date": "19 September 2000",
    "Email Address": "<EMAIL>",
    "Address": "B306 , spectrum tower , police stadium , shahibaug, ahmedabad",
    "Contact Number": "**********",
    "Linked In Profile URL": "(23) Vaishnavi Popat | LinkedIn",
    "Objective Statement": "An accounting and financial aspirant, done my bachelors with the specialization in finance and marketing, along with that an ACCA affiliate. Highly self-motivated, focused, hard worker and fast learner. Can do multi-tasking performance.",
    "Skills": "Excel, Microsoft office, Data Entry, Data Analysis, tally, Accounting and finance, Auditing, financial modelling, financial reporting, business analysis, powerpoint, forecasting, investment, research skill, leadership skill, communication skill, Analytical skill",
    "Certifications": "Tally ., udamey excel, google digital marketing, financial modelling",
    "Achievements": "",
    "Languages Known": "English, Hindi, Gujarati",
    "Technologies Used": ""
  }

  const TableData = {
    "Education Details": [
      {
        "Degree": "ACCA AFFILIATE",
        "Institution Name": "",
        "Graduation Year": "",
        "GPA/Marks/%": ""
      },
      {
        "Degree": "PG DIPLOMA IN FINANCIAL ANALYSIS",
        "Institution Name": "",
        "Graduation Year": "2022-23",
        "GPA/Marks/%": ""
      },
      {
        "Degree": "BBA",
        "Institution Name": "AHMEDABAD UNIVERSITY",
        "Graduation Year": "2018-2021",
        "GPA/Marks/%": "2.76 / 4.00"
      },
      {
        "Degree": "HSC",
        "Institution Name": "GUJARAT BOARD",
        "Graduation Year": "2018",
        "GPA/Marks/%": "81.73%"
      },
      {
        "Degree": "SSC",
        "Institution Name": "GUJARAT BOARD",
        "Graduation Year": "2016",
        "GPA/Marks/%": "74.83%"
      }
    ],
    "Work Experience": [
      {
        "Company Name": "PRIVATE CA FIRM",
        "Role": "INTERN",
        "Start Year": "2021",
        "End Year": "2022",
        "Description / Responsibility": "work in file return, gst return, tally, accounting, data entry"
      },
      {
        "Company Name": "QX OUTSOURCING CO",
        "Role": "recuritment trainee",
        "Start Year": "2021",
        "End Year": "2021",
        "Description / Responsibility": "learn about the sourcing, screening"
      },
      {
        "Company Name": "VIRAL FISSION",
        "Role": "student ambassador and team leader",
        "Start Year": "2020",
        "End Year": "2021",
        "Description / Responsibility": "marketing various brand like savan, spotify, oneplus as an intern"
      },
      {
        "Company Name": "IIFL GOLD LOAN",
        "Role": "student intern",
        "Start Year": "2019",
        "End Year": "2019",
        "Description / Responsibility": "analysis of the customer investment towards gold loan at iifl company"
      },
      {
        "Company Name": "TATA CHEMICAL LTD",
        "Role": "student trainee",
        "Start Year": "2019",
        "End Year": "2020",
        "Description / Responsibility": "work on project of an overview of the tata chemical ltd. learn about the SAP software, gst software, TDS, pyroll"
      }
    ],
    "Projects": [
      {
        "Project Name": "how country of origin manipulate the mindset of the customer",
        "Description": "due consideration of sex appeal in indian advertisement",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "how covid-19 impacted the compensation structure in the banking sector",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "foreign exchange market in India",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "IPL analysis using business statics",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "consumer behaviour towards the times of india app",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "financial analysis of the company using ratio",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "logo created and the process of getting the IPR logo",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      },
      {
        "Project Name": "Business plan - tucks and stitches",
        "Description": "",
        "Technologies Used": "",
        "Role": ""
      }
    ]
  }

  const sentences = [
    "Capture, Connect, Conquer your",
    "data with Precision and Ease.",
    "Seamlessly integrating data from manual, paper-based processes to legacy systems, boosting efficiency and collaboration."
  ];

  useEffect(() => {
    // Function to add the script tags
    const addChatbotScripts = () => {
      const injectScript = document.createElement('script');
      injectScript.src = "https://cdn.botpress.cloud/webchat/v1/inject.js";
      injectScript.async = true;
      document.body.appendChild(injectScript);

      const configScript = document.createElement('script');
      configScript.src = "https://mediafiles.botpress.cloud/f4d98133-6b91-452e-960c-bfc50e5c1847/webchat/config.js";
      configScript.defer = true;
      document.body.appendChild(configScript);

      // Clean up by removing the scripts when the component is unmounted
      return () => {
        document.body.removeChild(injectScript);
        document.body.removeChild(configScript);
      };
    };

    // Add chatbot scripts
    const cleanupChatbot = addChatbotScripts();

    // // Set timeout for showing the second typewriter
    const timer = setTimeout(() => {
      setShowSecondTypewriter(true);
    }, 3000); // 3000 milliseconds = 3 seconds

    // Cleanup function for both the timer and the scripts
    return () => {
      cleanupChatbot();
      clearTimeout(timer);
    };
  }, []);

  useEffect(() => {
    if (index < sentences.length) {
      // Set a timeout to change the index, effectively changing the sentence after the animation
      const timeoutId = setTimeout(() => {
        setIndex((prevIndex) => prevIndex + 1);
      }, 3000); // Assuming the animation takes around 3000ms (3 seconds) for each sentence

      return () => clearTimeout(timeoutId);
    }
  }, [index]);

  const caseStudies = [
    {
      date: "19 Jan 2023",
      title: "The Rise of AI in Business Analytics: What You Need to Know",
      image: caseStudyStock
    },
    {
      date: "20 Jan 2023",
      title: "Customizing Your AccuVelocity Dashboard: A Step-by-Step Guide",
      image: caseStudyStock
    },
    {
      date: "20 Jan 2023",
      title: "Optimizing Your Workflow with AccuVelocity's Latest Features",
      image: caseStudyStock
    }
  ];

  const highlightCoordinates = [
    {
      "page": 1,
      "x1": 260,
      "y1": 53,
      "x2": 571,
      "y2": 97
    },
    {
      "page": 0,
      "x1": 0,
      "y1": 0,
      "x2": 0,
      "y2": 0
    },
    {
      "page": 1,
      "x1": 63,
      "y1": 356,
      "x2": 165,
      "y2": 369
    },
    {
      "page": 1,
      "x1": 63,
      "y1": 278,
      "x2": 199,
      "y2": 291
    },
    {
      "page": 1,
      "x1": 63,
      "y1": 304,
      "x2": 204,
      "y2": 349
    },
    {
      "page": 1,
      "x1": 63,
      "y1": 245,
      "x2": 127,
      "y2": 258
    },
    {
      "page": 1,
      "x1": 63,
      "y1": 382,
      "x2": 172,
      "y2": 411
    },
    {
      "page": 1,
      "x1": 234,
      "y1": 210,
      "x2": 509,
      "y2": 271
    },
    {
      "page": 1,
      "x1": 21,
      "y1": 660,
      "x2": 85,
      "y2": 837
    },
    {
      "page": 2,
      "x1": 26,
      "y1": 420,
      "x2": 187,
      "y2": 559
    },
    {
      "page": 0,
      "x1": 0,
      "y1": 0,
      "x2": 0,
      "y2": 0
    },
    {
      "page": 0,
      "x1": 0,
      "y1": 0,
      "x2": 0,
      "y2": 0
    },
    {
      "page": 0,
      "x1": 0,
      "y1": 0,
      "x2": 0,
      "y2": 0
    }
  ]

  const faqs = [
    {
      question: 'What is Webflow and why is it the best website builder?',
      answer: 'Webflow is a powerful design and website building tool that provides users with a robust platform for creating responsive websites without the need to write code.',
      isOpen: true,
    },
    {
      question: 'What is your favorite template from BRIX Templates?',
      answer: 'Webflow is a powerful design and website building tool that provides users with a robust platform for creating responsive websites without the need to write code.',
      isOpen: false,
    },
    {
      question: 'How do you clone a Webflow Template from the Showcase?',
      answer: 'Webflow is a powerful design and website building tool that provides users with a robust platform for creating responsive websites without the need to write code.',
      isOpen: false,
    },
    {
      question: 'Why is BRIX Templates the best Webflow agency out there?',
      answer: 'Webflow is a powerful design and website building tool that provides users with a robust platform for creating responsive websites without the need to write code.',
      isOpen: false,
    },
    {
      question: 'How can I customize my Webflow template?',
      answer: 'Webflow is a powerful design and website building tool that provides users with a robust platform for creating responsive websites without the need to write code.',
      isOpen: false,
    },
  ];

  const [faqsState, setFaqsState] = useState(faqs);

  const toggleFaq = (index) => {
    setFaqsState(
      faqsState.map((faq, i) => {
        if (i === index) {
          faq.isOpen = !faq.isOpen;
        } else {
          faq.isOpen = false;
        }
        return faq;
      })
    );
  };

  const [isAnnual, setIsAnnual] = useState(false);
  const [showMore, setShowMore] = useState(false);

  const togglePricing = () => {
    setIsAnnual(!isAnnual);
  };

  const tick = (
    <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M11.1125 0.905959C10.1812 0.119888 8.8188 0.119889 7.88749 0.905959L7.03932 1.62185C6.64349 1.95595 6.15365 2.15886 5.6375 2.20251L4.53155 2.29603C3.31717 2.39873 2.35381 3.3621 2.25111 4.57647L2.15759 5.68242C2.11394 6.19857 1.91102 6.68841 1.57692 7.08426L0.861037 7.93241C0.0749661 8.86372 0.0749673 10.2261 0.861037 11.1574L1.57692 12.0056C1.91102 12.4014 2.11394 12.8913 2.15759 13.4074L2.25111 14.5134C2.35381 15.7278 3.31717 16.6912 4.53155 16.7938L5.6375 16.8873C6.15365 16.931 6.64349 17.1339 7.03934 17.468L7.88749 18.1839C8.8188 18.9699 10.1812 18.9699 11.1125 18.1839L11.9607 17.468C12.3565 17.1339 12.8464 16.931 13.3625 16.8873L14.4685 16.7938C15.6829 16.6912 16.6462 15.7278 16.7489 14.5134L16.8424 13.4074C16.8861 12.8913 17.089 12.4014 17.4231 12.0056L18.139 11.1574C18.925 10.2261 18.925 8.86372 18.139 7.93241L17.4231 7.08425C17.089 6.68841 16.8861 6.19857 16.8424 5.68242L16.7489 4.57647C16.6462 3.3621 15.6829 2.39873 14.4685 2.29603L13.3625 2.20251C12.8464 2.15886 12.3565 1.95595 11.9607 1.62185L11.1125 0.905959ZM14.046 7.84047C14.4854 7.40113 14.4854 6.68882 14.046 6.24948C13.6067 5.81013 12.8944 5.81013 12.455 6.24948L8.25054 10.454L6.54604 8.74948C6.1067 8.31013 5.39439 8.31013 4.95505 8.74948C4.5157 9.18882 4.5157 9.90113 4.95505 10.3405L7.45505 12.8404C7.89439 13.2798 8.6067 13.2798 9.04604 12.8404L14.046 7.84047Z" fill="#003654" />
    </svg>

  )

  const pricingRows = [
    { feature: 'Page Limit', free: 'Upto 12 Pages', starter: isAnnual ? 'Upto 900 Pages' : 'Upto 75 Pages', professional: isAnnual ? 'Upto 9000 Pages' : 'Upto 750 Pages', business: 'Custom' },
    { feature: 'User Accounts', free: 'Upto 1 User', starter: isAnnual ? 'Upto 4 Users' : 'Upto 3 Users', professional: isAnnual ? 'Upto 8 Users' : 'Upto 6 Users', business: 'Custom' },
    { feature: '24/7 Customer Support', free: <div className="flex items-center justify-center  text-center">{tick}</div>, starter: <div className="flex items-center justify-center  text-center">{tick}</div>, professional: <div className="flex items-center justify-center text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
    { feature: 'Unlimited Custom Models', free: <div className="flex items-center justify-center  text-center">{tick}</div>, starter: <div className="flex items-center justify-center  text-center">{tick}</div>, professional: <div className="flex items-center justify-center text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
    { feature: 'Smart Check', free: <div className="flex items-center justify-center  text-center">{tick}</div>, starter: <div className="flex items-center justify-center  text-center">{tick}</div>, professional: <div className="flex items-center justify-center  text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
    { feature: 'Bulk Processing', free: '', starter: <div className="flex items-center justify-center  text-center">{tick}</div>, professional: <div className="flex items-center justify-center  text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
    { feature: 'Dedicated Account Manager', free: '', starter: '', professional: <div className="flex items-center justify-center  text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
    { feature: 'API Access', free: '', starter: '', professional: <div className="flex items-center justify-center  text-center">{tick}</div>, business: <div className="flex items-center justify-center  text-center">{tick}</div> },
  ];

  const additionalRows = [
    { feature: 'Custom RPA Access', free: '', starter: '', professional: '', business: <div className="flex items-center justify-center  text-center">{tick}</div> }
  ];

  return (
    <>
      <div className="flex flex-col min-h-screen">
        <Header className="sticky top-0 w-full" style={{ top: '0', zIndex: '50' }} />
        <main className="flex-grow bg-[#ffff] min-h-screen" style={{ paddingTop: '0.75rem' }}>
          <section className="text-center pb-8 px-4">
            <div className="text-center mt-16 mb-8 px-4">

              <div className="text-center mt-16 mb-8 px-8">
                <div className="mx-auto bg-[#ffff] border-2 border-[#003654] rounded-full shadow-md p-1 inline-flex items-center justify-center pl-4 pr-4" style={{ boxShadow: '0 4px 6px -1px rgba(0, 53, 84, 0.1), 0 2px 4px -1px rgba(0, 53, 84, 0.06)' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm text-[#003654]">
                    Generative AI for Meaningful Extraction
                  </span>
                </div>
              </div>

              <div className="flex flex-col justify-center mx-auto items-center text-[#003654]">
                <TypewriterEffectSmooth
                  words={[
                    { text: "Data Extraction Simplified!", className: "font-semibold text-center" },
                  ]}
                  className="font-semibold text-center mx-auto"
                />
                {showSecondTypewriter && (
                <TypewriterEffectSmooth
                  words={[
                    { text: "Fast, Accurate and Reliable.", className: "font-semibold text-center" },
                  ]}
                  className="font-semibold text-center mx-auto mt-2"
                />
                )}
              </div>

              <p className="mb-8 text-[#626981] mx-[10vw]" >
                Say goodbye to manual data entry and human errors. AccuVelocity’s Intelligent Document Processing technology efficiently <br></br> handles even the most unstructured documents, turning prolonged manual data extraction into quick reviews.
              </p>

              <div className="inline-flex space-x-4 "> {/* Added mb-8 for spacing */}
                <button className="bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 rounded-lg" >
                  Start your free trial
                </button>
                <button className="bg-transparent hover:bg-gray-100 text-[#003654] py-2 px-4 border border-[#003654] hover:border-[#002744] rounded-lg flex items-center justify-center" onClick={() => document.getElementById('videoPlayer').play()}>
                  <FaPlay className="mr-2" /> {/* Include the FaPlay icon */}
                  Watch video
                </button>
              </div>
            </div>

            {/* Placeholder for the large central card */}
            <div className="max-w-4xl mx-auto bg-[#ffff] rounded-lg shadow-md mb-8">
              <video id="videoPlayer" className="w-full h-auto rounded-lg shadow-md" controls>
                <source src={stockVideoIntro} type="video/mp4" />
                {/* Your browser does not support the video tag. */}
              </video>
            </div>

            <p className="text-sm mb-4" >
              160,000+ customers in over 120 countries grow their businesses with AccuVelocity
            </p>
          </section>

          <div className="flex justify-center items-center space-x-4 bg-[#ffff] p-4">
            {/* Logos of certifications */}
            <img src={crt1} alt="Certification 1" className="h-[20vh] bg-[#ffff]" />
            <img src={crt2} alt="Certification 2" className="h-[20vh] bg-[#ffff]" />
          </div>

          <section className="flex justify-center items-center text-center mt-0 px-4">
            <div className="max-w-4xl mx-auto">
              <div className="text-center mt-12 px-8">
                <div className="w-auto bg-white border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    Smart Add-ons
                  </span>
                </div>
              </div>

              <h1 className="text-4xl font-bold mb-4 text-[#003654]" >
                Next-Level Capabilities
              </h1>

              <p className="mb-8" >
                Enhance Your Experience With OUR add-ons.
              </p>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-10">
                {/* Smart Check Card */}
                <div
                  className="flex flex-col items-start p-10 border-2 border-gray-300 rounded-lg transition duration-300 ease-in-out hover:scale-110"
                  onMouseEnter={() => handleMouseEnter('smartCheck')}
                  onMouseLeave={handleMouseLeave}
                  style={{
                    backgroundColor: hoveredCard === 'smartCheck' ? '#003654' : 'transparent',
                    color: hoveredCard === 'smartCheck' ? 'white' : 'inherit',
                    width: '394.63px',
                    height: '299.91px',
                    padding: '37.73px 35.32px',
                    gap: '8.03px',
                    border: '0.85px 0px 0px 0px',
                    opacity: '0px',
                  }}
                >
                  <img
                    src={hoveredCard === 'smartCheck' ? smartCheckLogoW : smartCheckLogo}
                    alt="Smart Check Icon"
                    className="h-10"
                    style={{ marginBottom: '8.03px' }}
                  />
                  <h3 className={`font-semibold mb-1 ${hoveredCard === 'smartCheck' ? 'text-white' : 'text-[#003654]'}`}
                    style={{
                      fontFamily: 'Inter, sans-serif',
                      fontSize: '23.73px',
                      fontWeight: 600,
                      lineHeight: '31.6px',
                      textAlign: 'left',
                      width: '178px',
                      height: '32px',
                      gap: '0px',
                      opacity: 1,
                      marginTop: '0px',
                    }}>
                    Smart Check
                  </h3>

                  <p className={`"text-left" ${hoveredCard === 'smartCheck' ? 'text-white' : 'text-[#003654]'}`}
                    style={{
                      width: '279.98px',
                      height: '108px',
                      gap: '0px',
                      opacity: 1,
                      fontFamily: 'Inter, sans-serif',
                      fontSize: '17.08px',
                      fontWeight: 400,
                      lineHeight: '27.33px',
                      textAlign: 'left',
                      marginBottom: '0px'
                    }}>
                    Smart Check delivers the confidence of meticulously verified data, ensuring unparalleled accuracy.
                  </p>

                </div>

                {/* RPA Integration Card */}
                <div
                  className="flex flex-col items-start border-2 border-gray-300 rounded-lg transition duration-300 ease-in-out hover:scale-110"
                  onMouseEnter={() => handleMouseEnter('rpaIntegration')}
                  onMouseLeave={handleMouseLeave}
                  style={{
                    backgroundColor: hoveredCard === 'rpaIntegration' ? '#003654' : 'transparent',
                    color: hoveredCard === 'rpaIntegration' ? 'white' : 'inherit',
                    width: '394.63px',
                    height: '299.91px',
                    padding: '37.73px 35.32px',
                    gap: '8.03px',
                    border: '0.85px 0px 0px 0px',
                    opacity: '0px',
                  }}
                >
                  <img
                    src={hoveredCard === 'rpaIntegration' ? rpaIntegrationLogoW : rpaIntegrationLogo}
                    alt="RPA Integration Icon"
                    className="h-10 mb-2"
                    style={{ marginBottom: '8.03px' }}
                  />
                  <h3 className={`text-lg font-bold mb-1 ${hoveredCard === 'rpaIntegration' ? 'text-white' : 'text-[#003654]'}`} style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '23.73px',
                    fontWeight: 600,
                    lineHeight: '31.6px',
                    textAlign: 'left',
                    width: '178px',
                    height: '32px',
                    gap: '0px',
                    opacity: 1,
                    marginTop: '0px',
                  }}>
                    RPA Integration
                  </h3>
                  <p className={`"text-left" ${hoveredCard === 'rpaIntegration' ? 'text-white' : 'text-[#003654]'}`}
                    style={{
                      width: '279.98px',
                      height: '108px',
                      gap: '0px',
                      opacity: 1,
                      fontFamily: 'Inter, sans-serif',
                      fontSize: '17.08px',
                      fontWeight: 400,
                      lineHeight: '27.33px',
                      textAlign: 'left',
                      marginBottom: '0px'
                    }}>
                    Streamline and connect diverse systems with automation. Boost efficiency and reduce errors at the same time.
                  </p>

                </div>

              </div>
            </div>
          </section>

          <section className="px-4 py-8 bg-white">
            <div className="text-center mt-16 px-8">
              <div className="w-auto bg-white border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                <span className="text-sm font text-[#003654]">
                  Experience the Real App
                </span>
              </div>
            </div>
            <div className="text-center mb-8">
              <Typography variant="h1" className="text-4xl font-bold text-[#003654] mb-4">
                See It For Yourself!
              </Typography>
            </div>

            <div className="flex justify-center space-x-4 mt-8">
              {tabs.map((tab) => (
                <button
                  key={tab}
                  onClick={() => setActiveTab(tab)}
                  className={`w-49 h-19 px-6 py-4 font-medium rounded-full border ${activeTab === tab ? 'bg-[#0A2B5C] text-white' : 'bg-white text-[#0A2B5C]'
                    } border-[#0B2B5C]`}
                >
                  {tab}
                </button>
              ))}
            </div>

            <DemoDocViewer highlightCoords={highlightCoordinates} values={currentValue} TableData={TableData} />
            <div className="flex justify-center space-x-4 mt-8">

              <button
                className={`w-49 h-19 px-6 py-4 font-medium rounded-full border ${'bg-[#0A2B5C] text-white'} border-[#0B2B5C]`}
              >
                BOOK A DEMO
              </button>
            </div>
          </section>

          <section className="px-4 py-8 bg-[#ffff]">
            <div className="max-w-7xl mx-auto">
              <div className="text-center px-8">
                <div className="w-auto bg-white border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    Tutorial
                  </span>
                </div>
              </div>
              <div className="text-center">
                <h2 className="text-3xl font-bold text-[#003654] mb-4">
                  Learn How To Use It?
                </h2>
              </div>
              <div className="flex flex-col md:flex-row justify-between items-center mb-8">
                <div className="w-full md:w-1/2 space-y-6 mb-8 md:mb-0 md:mr-8">
                  {/* Card 1 */}
                  <div className="flex items-start p-6 bg-white rounded-[26.83px] shadow-lg"
                    style={{
                      width: '541.94px',
                      height: '177.71px',
                      padding: '26.83px 12.52px',
                      gap: '8.94px',
                    }}>
                    <div className="text-[#003654] font-bold text-4xl mr-4">01</div>
                    <div>
                      <h3 className="font-bold text-2xl text-[#003654] mb-1">Access The Platform</h3>
                      <p className="text-gray-600">
                        Easily log into our all-encompassing suite from any device to manage your data processing and auditing tasks.
                      </p>
                    </div>
                  </div>

                  {/* Card 2 */}
                  <div className="flex items-start p-6 bg-white rounded-[26.83px] shadow-lg"
                    style={{
                      width: '541.94px',
                      height: '177.71px',
                      padding: '26.83px 12.52px',
                      gap: '8.94px',
                    }}>
                    <div className=" text-[#003654] font-bold text-4xl mr-4">02</div>
                    <div>
                      <h3 className="font-bold text-2xl text-[#003654] mb-1">Upload And Organize Data</h3>
                      <p className="text-gray-600">
                        Upload your data effortlessly, using drag-and-drop or direct upload. Our Smart Scan technology then sorts and readies your data for analysis, human review, or RPA Integration.
                      </p>
                    </div>
                  </div>

                  {/* Card 3 */}
                  <div className="flex items-start p-6 bg-white rounded-[26.83px] shadow-lg"
                    style={{
                      width: '541.94px',
                      height: '177.71px',
                      padding: '26.83px 12.52px',
                      gap: '8.94px',
                    }}>
                    <div className="text-[#003654] font-bold text-4xl mr-4">03</div>
                    <div>
                      <h3 className="font-bold text-2xl text-[#003654] mb-1">Analyze And Optimize</h3>
                      <p className="text-gray-600">
                        Track your data&apos;s journey in real-time, verify outcomes, and delve into analytics for strategic decision-making via a single dashboard.
                      </p>
                    </div>
                  </div>
                </div>
                <div className="w-full md:ml-8">
                  <img src={stockImage} alt="Dashboard"
                    // style={{
                    //   height: '501.04px',
                    //   width: '879.6px'
                    // }} 
                    className="mx-auto h-auto rounded-lg shadow-md" />
                </div>
              </div>
            </div>
          </section>

          <section className="px-4 py-8">
            <div className="max-w-7xl mx-auto">
              <div className="text-center px-8">
                <div className="w-auto bg-white border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    Why Choose Us?
                  </span>
                </div>
              </div>
              <div className="text-center mb-8">
                <h2 className="text-3xl font-bold text-[#003654] mb-4">
                  There&apos;s a reason why organizations love AccuVelocity!
                </h2>
              </div>
              <div className="flex justify-center">
                <div className="mb-8">
                  <table className="ml-22 mr-22"
                    style={{
                      width: '1242.44px',
                      height: '545.83px',
                      borderRadius: '4.58px 4.58px 4.58px 4.58px',
                    }}>
                    <thead>
                      <tr>
                        <th className="px-3 pr-36 py-2 border border-[#B9B9B9] text-bold text-md font-medium text-[#003654] uppercase tracking-wider bg-[#f2f2f2]"></th>
                        <th className="px-3 pr-36 py-2 border border-[#B9B9B9] font-semibold text-md text-left text-[#4B4B4B] bg-[#f2f2f2] tracking-wider"
                          style={{
                            fontFamily: 'Inter, sans-serif',
                            fontSize: '22.9px',
                            lineHeight: '27.72px',
                            textAlign: 'left',
                          }}>
                          Manual Data Entry
                        </th>
                        <th className="px-3 pr-36 py-2 border border-[#B9B9B9] text-left text-md font-semibold text-[#fff] tracking-wider bg-[#003654]" style={{
                          fontFamily: 'Inter, sans-serif',
                          fontSize: '22.9px',
                          lineHeight: '27.72px',
                          textAlign: 'left',
                        }}>AccuVelocity</th>
                      </tr>
                    </thead>
                    <tbody className="bg-[#ffff] divide-y divide-gray-300 ">
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Time to Extract</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Several minutes</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>
                          Less than 1 minute
                        </td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Accuracy</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Differs from person to person</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>99%+</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Doc. Format</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Structured</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Structured and unstructured</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Security & Compliance</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>None</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>ISO 9001</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>High-Volume Doc. Processing</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Upto hundreds per day</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Upto thousands per day</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Pricing</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Hourly</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Per Page</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Approval Process</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Tendency to overlook</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Structured and Tracked</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Document Activity Timeline (History)</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>None</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>From upload to approved</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Average time per 100 pages</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Approx. 6 hr & 40 min (efficiency 1x)</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Approx. 1 hr & 15 min (efficiency 5x)</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Smart Check (Auditing)</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Requires more people and time</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Available</td>
                      </tr>
                      <tr>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm font-bold" style={{
                          fontSize: '17.41px',
                          fontWeight: 600,
                          lineHeight: '21.06px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>RPA Integration (Connect Diverse Systems)</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] text-sm " style={{
                          fontSize: '18.32px',
                          lineHeight: '22.17px',
                          textAlign: 'left',
                          width: '386.67px',
                          height: '22px',
                        }}>Needs specialist</td>
                        <td className="px-3 pr-36 py-2 whitespace-nowrap border border-[#B9B9B9] font-thin text-white text-left"
                          style={{
                            backgroundColor: 'rgba(7, 88, 134, 0.76)',
                            fontSize: '18.32px',
                            lineHeight: '22.17px',
                            textAlign: 'left',
                            width: '386.67px',
                            height: '22px',
                          }}>Available as additional feature</td>
                      </tr>
                    </tbody>
                  </table>
                </div>
              </div>
            </div>
          </section>

          {/* <section className="bg-[#ffff] py-8 px-4">
          <div className="container mx-auto text-center">
            <h2 className="text-2xl font-bold mb-4 text-[#003654]">Our Certifications and Accreditations</h2>
            <p className="text-[#575757] text-sm mb-6">We are proud to be recognized by leading industry authorities. Explore our certifications and accreditations that stand as a testament to our commitment to excellence.</p>

            <div className="flex justify-center items-center gap-4">
              <img src={crt1} alt="Certification 1" className="w-80 mr- h-auto transform transition duration-500 hover:scale-110" />
              <img src={crt2} alt="Certification 2" className="w-80 h-auto transform transition duration-500 hover:scale-110" />
            </div>

            <p className="text-[#575757] text-xs mt-4">Our dedication to quality and service is reflected in our industry certifications and the trust we have earned from our clients.</p>
          </div>
        </section> */}

          <section className="bg-[#ffff] py-8 px-4">
            <div className="container mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4 text-[#003654]">Transparent Pricing for Exceptional Value</h2>
              <div className="flex justify-center items-center my-6">
                <span className="text-sm font-medium text-[#575757] mx-2">Monthly</span>
                <button
                  className={`w-14 h-7 flex items-center  rounded-full p-1 duration-300 ease-in-out ${isAnnual ? 'bg-[#003654]' : 'bg-[#CCCCCC]'
                    }`}
                  onClick={togglePricing}
                >
                  <div
                    className={`bg-[#ffff] w-5 h-5 rounded-full shadow-md ${isAnnual ? 'translate-x-7' : ''
                      }`}
                  />
                </button>
                <span className="text-sm font-medium text-[#575757] mx-2">Annually</span>
              </div>

              <p className="text-[#575757] text-sm">Save Upto 30% on Annual Plans</p>

              <div className="overflow-x-auto mt-6">
                <table className="min-w-full">
                  <thead className="bg-[#FFF] text-[#003654]">
                    <tr>
                      <th className="py-4 px-6  border-t border-l border-b text-left align-center border-[#E6E9F5] w-1/5">
                        <div className="font-bold text-xl">Compare plans</div>
                        <div className="mt-3 font-light text-sm text-[#858BA0]">
                          Choose your workspace plan according to your organisational need
                        </div>
                        <div className="mt-3 inline-block text-[#003654] bg-[#FFF] border-2 border-[#E6E9F5] py-2 px-4 rounded-full text-xs">
                          Upto 30% Off on Annual Plans
                        </div>
                      </th>
                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Free</p>
                        <div className="mt-4 font-light text-sm text-[#646464]">
                          Best for personal use
                        </div>
                        <div className="mt-16 text-[#003654]">
                          <span className="font-bold text-4xl">$0</span>
                          <span className="text-lg font-light">{isAnnual ? '/year' : '/month'}</span>
                        </div>
                        <button className="mt-10 w-full bg-[#003654] text-white py-2 px-6 rounded font-light">
                          Choose This Plan
                        </button>
                      </th>
                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Starter</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">
                          For individuals or teams looking to try out the platform
                        </div>
                        <div className="mt-5 flex flex-col items-start">
                          <div className='flex items-center'>
                            <span className="text-xl line-through text-[#003654] opacity-50">{isAnnual ? '$588' : '$49'}</span>
                            <span className='text-xs text-[#003654] opacity-50'>{isAnnual ? '/year' : '/month'}</span>
                          </div>
                          <span className="font-bold text-3xl text-[#003654]">
                            <span className="font-bold text-4xl">{isAnnual ? '$228' : '$19'}</span>
                            <span className="text-sm">.99</span>
                            <span className="text-lg font-light">{isAnnual ? '/year' : '/month'}</span>
                          </span>
                        </div>
                        <button className="mt-10 w-full bg-[#003654] text-white py-2 px-6 rounded font-light">
                          Choose This Plan
                        </button>
                      </th>
                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Professional</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">
                          For teams looking to automate time-draining tasks.
                        </div>
                        <div className="mt-5 flex flex-col items-start">
                          <div className='flex items-center'>
                            <span className="text-xl line-through text-[#003654] opacity-50">{isAnnual ? '$5988' : '$499'}</span>
                            <span className='text-xs text-[#003654] opacity-50'>{isAnnual ? '/year' : '/month'}</span>
                          </div>
                          <span className="font-bold text-3xl text-[#003654]">
                            <span className="font-bold text-4xl">{isAnnual ? '$2388' : '$199'}</span>
                            <span className="text-sm">.99</span>
                            <span className="text-lg font-light">{isAnnual ? '/year' : '/month'}</span>
                          </span>
                        </div>
                        <button className="mt-10 w-full bg-[#003654] text-white py-2 px-6 rounded font-light">
                          Choose This Plan
                        </button>
                      </th>
                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Business</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">
                          For businesses looking for custom workflows to automate business processes for measurable ROI
                        </div>
                        <div className="mt-3">
                          <span className="font-bold text-3xl">Custom</span>
                        </div>
                        <button className="mt-10 w-full bg-[#003654] text-white py-2 px-6 rounded font-light">
                          Talk to Sales
                        </button>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {pricingRows.map((row, index) => (
                      <tr key={index} className={`border-t ${index >= pricingRows.length - 2 && !showMore ? 'blur-sm' : ''}`}>
                        <td
                          className="py-2 px-6 border-l border-r border-b border-[#E6E9F5] text-center text-[#003654]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "17.17px",
                            fontWeight: 600,
                            lineHeight: '24.8px'
                          }}
                        >
                          {row.feature}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.free}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.starter}
                          {index < 2 && <div className="text-[#858BA0] text-sm font-light">Pages Add-ons on Demand</div>}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.professional}
                          {index < 2 && <div className="text-[#858BA0] text-sm font-light">Pages Add-ons on Demand</div>}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.business}
                        </td>
                      </tr>
                    ))}
                    {showMore && additionalRows.map((row, index) => (
                      <tr key={`additional-${index}`} className="border-t text-[#003654]">
                        <td
                          className="py-2 px-6 border-r text-center border-l border-b border-[#E6E9F5] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                          }}
                        >
                          {row.feature}
                        </td>
                        <td
                          className="py-2 px-6 text-left border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.free}
                        </td>
                        <td
                          className="py-2 px-6 text-left border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.starter}
                        </td>
                        <td
                          className="py-2 px-6 text-left border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.professional}
                        </td>
                        <td
                          className="py-2 px-6 text-left border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.business}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>

              {showMore ? (
                <button onClick={() => setShowMore(false)} className="text-[#003654] mt-4">See Less</button>
              ) : (
                <button onClick={() => setShowMore(true)} className="text-[#003654] mt-4">See More</button>
              )}
            </div>
          </section>

          <section className="features-section py-12">
            <div className="container mx-auto px-4">
              <div className="text-center px-8">
                <div className="w-auto bg-[#ffff] border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    Extraordinary Features
                  </span>
                </div>
              </div>
              <div className="text-center mb-8">
                <Typography variant="h1" className="text-4xl font-bold text-[#003654] mb-4">
                  Enhance your experience with our Custom Features!
                </Typography>
                <p className="mb-8 text-xl text-[#003654]">
                  Get Custom features on the horizon, designed to enrich your experience.
                </p>
              </div>

              <div className="mx-16 grid grid-cols-1 md:grid-cols-2 gap-8">
                {/* Feature 1 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant="h3" style={{
                    color: '#003654',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">
                    Transaction Categorization
                  </Typography>
                  <p style={{
                    color: '#4B5162',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '16px',
                    fontWeight: 400,
                    lineHeight: '25.6px',
                    textAlign: 'left',
                  }}>
                    Automatically sort your transactions into categories for simplified financial tracking and analysis.
                  </p>
                </div>

                {/* Feature 2 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant="h3" style={{
                    color: '#003654',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">Detailed Reports and Analysis</Typography>
                  <p>Gain deeper insights with comprehensive reports and analysis tailored to your financial activities.</p>
                </div>

                {/* Feature 3 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant='h3' style={{
                    color: '#003654',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">Queue Management System</Typography>
                  <p>Streamline your service flow with an efficient queue management system, reducing wait times and ensuring customer satisfaction.</p>
                </div>

                {/* Feature 4 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant='h3' style={{
                    color: '#003654',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">Accounting Software Integration</Typography>
                  <p>Seamlessly integrate leading accounting software for a unified view of your finances, enhancing efficiency and accuracy.</p>
                </div>

                {/* Feature 5 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant='h3' style={{
                    color: '#003654',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">Smart Scan for Handwritten Docs</Typography>
                  <p>Utilize smart scanning technology to quickly process handwritten documents, converting them into digital format.</p>
                </div>

                {/* Feature 6 */}
                <div className="feature-card bg-[#ffff] p-6  rounded-2xl text-left" style={{ border: '1px solid #D2D5E2' }}>
                  <img src={stockGridImage} alt="User" className="h-[160px] w-full" />
                  <Typography variant='h3' style={{
                    color: '#003654',
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 600,
                    lineHeight: '28.4px',
                    letterSpacing: '-0.02em',
                    textAlign: 'left'
                  }} className="font-bold mb-2 mt-3">Signature and Mark Verification</Typography>
                  <p>Ensure security with advanced signature and mark verification tools that authenticate identity with precision.</p>
                </div>
              </div>
              <div className="text-center mt-16 px-8">
                <button type="submit" style={{ borderRadius: "12px" }} className="mx-auto bg-white border border-[#D2D5E2] rounded-lg p-1 inline-flex items-center justify-center  px-5 py-3">
                  <span className="text-[#003654] font-inter text-base font-semibold leading-[19.52px] text-left">
                    Explore more features
                  </span>
                </button>
              </div>
            </div>
          </section>

          <section className="faq-section py-12 bg-[#ffff]">
            <div className="container mx-auto px-4">
              <div className="text-center px-8">
                <div className="w-auto bg-[#ffff] border border-[#003654] rounded-full p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    FAQ
                  </span>
                </div>
              </div>

              <div className="text-center mb-8">
                <h2 className="font-semibold text-[#003654] mb-4"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '35px',
                    fontWeight: 600,
                    lineHeight: '40.6px',
                    letterSpacing: '-0.02em',
                    textAlign: 'center',
                  }}>
                  Your Questions, Answered
                </h2>
                <p className="text-[#4B5162]"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 400,
                    lineHeight: '32px',
                    textAlign: 'center',
                  }}>
                  Find quick answers and clear insights in our streamlined FAQ section,  designed to help
                </p>
                <p className="mb-8 text-[#4B5162]"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '20px',
                    fontWeight: 400,
                    lineHeight: '32px',
                    textAlign: 'center',
                  }}>
                  you make the most of our services effortlessly
                </p>
              </div>

              <div className="max-w-4xl mx-auto">
                {faqsState.map((faq, index) => (
                  <div
                    key={index}
                    className={`bg-white mb-4 ${faq.isOpen ? 'border-[#003B5D] shadow-lg' : 'shadow-lg'}`}
                    style={faq.isOpen ? {
                      boxShadow: '0px 6px 16px 0px #4A3AFF30',
                      border: '2px solid #003B5D',
                      width: '896px',
                      height: '230px',
                      borderRadius: '18px 18px 18px 18px',
                      opacity: 1,
                    } : {
                      boxShadow: '0px 5px 16px 0px #080F340F',
                      width: '896px',
                      height: '132px',
                      borderRadius: '18px 18px 18px 18px',
                      opacity: 1,
                    }}
                    onClick={() => toggleFaq(index)}
                  >
                    <div className="p-4 flex justify-between items-center select-none cursor-pointer">
                      <div className={`flex justify-between w-full ${faq.isOpen ? '' : 'mt-8'}`}>
                        <h3 className="font-semibold text-[#170F49]" style={{
                          fontSize: '20px',
                          fontWeight: 600,
                          lineHeight: '28.4px',
                        }}>{faq.question}</h3>
                        <div
                          className={`rounded-full p-3 ${faq.isOpen ? 'bg-[#003654] text-white' : 'bg-white text-[#003654] shadow-lg'}`}
                        >
                          {faq.isOpen ? <FaChevronDown /> : <FaChevronRight />}
                        </div>
                      </div>
                    </div>
                    {faq.isOpen && <div className="p-4 text-[#6F6C90]" style={{
                      fontSize: '18px',
                      fontWeight: 400,
                      lineHeight: '30px',
                      width: '715.3px'
                    }}>{faq.answer}</div>}
                  </div>
                ))}
              </div>
            </div>
          </section>

          <div className="mt-10 text-center">
            <h2 className="font-semibold text-[#003654] mb-4"
              style={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '35px',
                fontWeight: 600,
                lineHeight: '40.6px',
                letterSpacing: '-0.02em',
                textAlign: 'center',
              }}>
              Still have a question?
            </h2>

            <p className="text-[#4B5162]"
              style={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '20px',
                fontWeight: 400,
                lineHeight: '32px',
                textAlign: 'center',
              }}>
              If you cannot find answer what you were looking in our FAQs.
            </p>
            <span className="mb-8 text-[#4B5162]"
              style={{
                fontFamily: 'Inter, sans-serif',
                fontSize: '20px',
                fontWeight: 400,
                lineHeight: '32px',
                textAlign: 'center',
              }}>
              Write to us at <a href="mailto:<EMAIL>" className="text-[#0096FF]"><EMAIL></a>
            </span>
          </div>

          <section className="case-studies-section py-12 bg-[#ffff]">
            <div className="container mx-auto px-4">
              <div className="text-center px-8">
                <div className="w-auto bg-[#ffff] border border-[#003654] rounded-full shadow-md p-1 mb-8 inline-flex items-center justify-center pl-4 pr-4" style={{ fontWeight: '500' }}>
                  <img src={starSymbol} alt="Star" className="h-4 mr-2" />
                  <span className="text-sm font text-[#003654]">
                    Case Studies
                  </span>
                </div>
                <h2 className="font-bold text-[#003654] mb-4"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '39px',
                    fontWeight: 'bolder',
                    lineHeight: '45.24px',
                    textAlign: 'center',
                  }}>
                  Dive into the World of Real Use Case Examples
                </h2>

                <p className="text-[#4B5162]"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '16px',
                    fontWeight: 400,
                    lineHeight: '25.6px',
                    textAlign: 'center',
                    color: 'var(--Neutral-800, #4B5162)',
                  }}>
                  Stay updated with the latest trends, tips, and insights in business analytics. Explore our
                </p>
                <p className="mb-8 text-[#4B5162]"
                  style={{
                    fontFamily: 'Inter, sans-serif',
                    fontSize: '16px',
                    fontWeight: 400,
                    lineHeight: '25.6px',
                    textAlign: 'center',
                    color: 'var(--Neutral-800, #4B5162)',
                  }}>
                  curated articles designed to empower your data-driven journey.
                </p>

              </div>

              <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                {caseStudies.map((caseStudy, index) => (
                  <div key={index} className="feature-card bg-[#ffff] p-6 rounded-lg text-left">
                    <img src={caseStudy.image} alt="Case Study" className="mx-auto mb-2" />
                    <div className="text-sm text-gray-500 mb-2 ml-3 mr-3">{caseStudy.date}</div>
                    <div className="font-bold text-lg mb-2 ml-3 mr-3">{caseStudy.title}</div>
                  </div>
                ))}
              </div>
            </div>
          </section>
        </main>
        <Footer />
      </div>
    </>
  );
};

export default MainAV;
