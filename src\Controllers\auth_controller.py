# Importing libraries
import copy
from datetime import datetime,timedelta 
import os
from typing import Optional
from sqlalchemy import func
from fastapi_mail import FastMail, MessageSchema
from starlette.background import BackgroundTasks
from fastapi import HTTPException
from sqlalchemy.exc import IntegrityError
from fastapi import File as FastAPIFile
import base64
from sqlalchemy import select
from fastapi import HTTPException, UploadFile
import traceback
from config.db_config import AsyncSessionLocal
from src.Schemas.auth_models import LoginRequest, ForgotPasswordModel, ResetPassword, UpdatePassword, EmailValidation
from src.Schemas.Tally_Schemas import AddTallyHeader

from src.utilities.helperFunc import Hashing
from src.utilities.email_utils import CResetPassword, CEmailVerifier, CEmailer
from src.utilities.token_helper import TokenHelper
from config.constants import Constants
from src.Models.models import User, PasswordReset, Role,UploadedDoc, ModelTable, UserAP<PERSON>Usage,OrganizationDetails, PromoCode, IntegrationConfig  
from sqlalchemy.future import select
import os
import filetype
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.referralCodeController import CReferralCode
from src.Controllers.StripePaymentController import CPaymentApp
from src.Controllers.InitializeUserInbuiltData import CInitializeUserInbuiltData
from src.Controllers.user_Logs_Controller import CUserLogController
from random import randint

import pytz
from fastapi import HTTPException
from sqlalchemy.exc import SQLAlchemyError


class CAuthController:

    @staticmethod 
    async def MSLogin(request: LoginRequest) :
        """
        Purpose : This method is used to authenticate a user and provide a JWT token if successful.

        Inputs  :   (1) request   :   LoginRequest object containing user credentials.

        Output  : It returns a dictionary with JWT token and user details or raises an HTTPException.

        Example : await CAuthController.MSLogin(request=LoginRequest(email="<EMAIL>", password="password"))
        """
        objUser = None
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User).filter(User.email == request.email.lower()))
                objUser = result.scalars().first()
                if objUser is None:
                    raise HTTPException(
                        status_code=404, detail="No account exists with this email. Try creating a new one!")
            
                if objUser and Hashing.verify(request.password, objUser.password):
                    
                    roleResult = await session.execute(select(Role).filter(Role.Id == objUser.roleID))
                    objRole = roleResult.scalars().first()
                    
                    # Password is correct, generate access token
                    strAccessToken = TokenHelper.create_access_token(
                        data={"id":objUser.uid,"email": objUser.email, "role": objRole.RoleName,"name":objUser.name})
                    
                    await CLogController.MSWriteLog(objUser.uid, "Info", f"User has successfully logged in.")
                    await CUserLogController.MSWriteLog(objUser.uid, "Info", f"You successfully logged in to your profile. ", "Profile")

                    return {
                        "jwt_token": strAccessToken,
                        "role":objRole.RoleName,
                        "uid": objUser.uid,
                        "email": objUser.email,
                        "name": objUser.name,
                        "created_at":objUser.created_at
                    }
                else:
                    # Either the user does not exist or password is incorrect
                    if objUser:
                        await CLogController.MSWriteLog(objUser.uid, "Error", f"Your login failed. Please check your email and password.")
                        await CUserLogController.MSWriteLog(objUser.uid, "Error", f"Login failed: Incorrect password. Please try again.", "Profile")
                    raise HTTPException(
                        status_code=401, detail="Your login failed. Please check your email and password.")
                
        except HTTPException as http_exc:
            # Re-raise the HTTPException to be handled by FastAPI
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{request.email.lower()}' Unable to Authenticate User, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc
        
        except Exception as e:
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail=f"An error occurred during the login process: {str(e)}")

    @staticmethod 
    async def MSUserRegistration(strEmail: str , strPassword: str , strName: str ,  profilePicture , strPhoneNumber:  Optional[str] = None,Country:str = None, roleName: str = "General", bUsePaidOCR:bool = False, bUsePaidDocExtractor:bool = False, promoCodeStr:[str,None]=None, bRaiseError:bool = True, total_allowed_page_limit:int = Constants.MaxTrialPaidDocExtractionPerUser, bEmailValidation=False):
        """
        Purpose : This method is used to register a new user, verify the phone number, store the profile image in binary format,
                and provide a JWT token upon successful registration.

        Inputs  :   (1) request: UserRegistrationInput object containing user registration details, 
                        including phone number and profile image.

        Output  : It returns a dictionary with JWT token and user details or raises an HTTPException.

        Example : await CAuthController.MSRegistration(request=UserRegistrationInput(strEmail="<EMAIL>", 
                    password="password", name="New User", phone_number="1234567890", profile_image="<base64_encoded_string>"))
        """
        async with AsyncSessionLocal() as session:
            iUserID = None
            UserRegistrationResMsg = Constants.UserRegistrationSuccessMessage
            try:
                # For Verification of valid promo code 
                if promoCodeStr is not None and promoCodeStr:
                    promoCodeData = await CReferralCode.MSGetReferralCodeByName(promoCodeStr)
                
                # Perform Email Validation
                if bEmailValidation:
                    objExistingUser = await CAuthController.CheckEmailExists(email=strEmail)
                    if objExistingUser:
                        if bRaiseError:
                            raise HTTPException(status_code=400, detail="Email already exists")
                        else:
                            # By pass when account exists
                            return None
                if roleName:
                    roleResult = await session.execute(select(Role).filter(Role.RoleName == roleName))
                else:
                    roleResult = await session.execute(select(Role).filter(Role.RoleName == "General"))

                objrole = roleResult.scalars().first()
            
                if not objrole:
                    raise HTTPException(
                        status_code=400, detail=f"Role Named {roleName} not found.")
                
                # Create a new user with additional fields for phone number and profile image
                strHashedPassword = Hashing.get_hash(strPassword)
                
                objNewUser = User(
                    email=strEmail.lower(),
                    password=strHashedPassword,
                    name=strName,
                    roleID=objrole.Id,
                    phoneNumber=strPhoneNumber,
                    Country = Country,
                    usePaidOCR = bUsePaidOCR,
                    usePaidDocExtractor = bUsePaidDocExtractor
                )
                session.add(objNewUser)
                await session.commit()
                await session.refresh(objNewUser)

                            
                # Generate access token
                iUserID = objNewUser.uid
                strAccessToken = TokenHelper.create_access_token(
                    data={"email": objNewUser.email, "role": objrole.RoleName, "id": objNewUser.uid,"name":strName})

                try:
                    objInitializeAppData = CInitializeUserInbuiltData(iUserID)
                    await objInitializeAppData.MSetUserApplicationUsage(total_allowed_page_limit)
                    
                    # Fetch PromoCode Id if Valid PromoCode
                    if promoCodeStr is not None and promoCodeStr:
                        await CReferralCode.MSUsePromoCode(userid=iUserID, strPromocode=promoCodeStr)
                        
                except Exception as e:
                    if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                        raise HTTPException(status_code=e.status_code, detail=e.detail)
                    else:
                        raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")
                    
                await CLogController.MSWriteLog( objNewUser.uid, "Info", f"User with email: {objNewUser.email} and name: {objNewUser.name} was successfully created.")
                await CUserLogController.MSWriteLog(objNewUser.uid, "Info", f"Account created sucessfully", "Profile")
                # -------------------------------------------------------------------------
                return {
                    "jwt_token": strAccessToken,
                    "uid": objNewUser.uid,
                    "email": objNewUser.email,
                    "name": objNewUser.name,
                    "role": objrole.RoleName,
                    "phoneNumber" : objNewUser.phoneNumber,
                    "Country" : objNewUser.Country,
                    "created_at":objNewUser.created_at.astimezone(pytz.timezone("Asia/Kolkata")),
                    "message":UserRegistrationResMsg
                }
            
            except HTTPException as http_exc:
                # Re-raise the HTTPException to be handled by FastAPI
                if iUserID is not None:
                    await CAuthController.MSDeleteUser(user_id=iUserID)
                if strName or strEmail:
                    await CLogController.MSWriteLog(None, "Debug", f"Information: 'Name:{strName if strName else ''}, Email:{strEmail.lower() if strEmail else ''}' Failed to Register User!!!, \n\nTraceback: {str(traceback.format_exc())}")
                raise http_exc
        
            except IntegrityError as e:
                await session.rollback()
                if iUserID is not None:
                    await CAuthController.MSDeleteUser(user_id=iUserID)
                if strName or strEmail:
                    await CLogController.MSWriteLog(None, "Debug", f"Information: 'Name:{strName if strName else ''}, Email:{strEmail.lower() if strEmail else ''}' Failed to Register User!!!, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")
        
            except Exception as e:
                await session.rollback()
                if iUserID is not None:
                    await CAuthController.MSDeleteUser(user_id=iUserID)
                if strName or strEmail:
                    await CLogController.MSWriteLog(None, "Debug", f"Information: 'Name:{strName if strName else ''}, Email:{strEmail.lower() if strEmail else ''}' Failed to Register User!!!, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(
                status_code=500, detail="An error was encountered during User Registration.")
    
    @staticmethod
    async def MSInitializeUserData(iUserID:int):
        try:
            objInitializeAppData = CInitializeUserInbuiltData(iUserID)
            await objInitializeAppData.MSSetInbuiltModelData()
            await objInitializeAppData.MSSetDocument()
            userData = await CAuthController.MSGetUserMetaData(user_id=iUserID)
            emailer = CEmailer(iUserID=iUserID)
            await emailer.send_email(
                to=[userData.get("email")],
                template_name='TrialOnboarding',
                template_data={
                    'customer_name': userData.get("name"),
                    'trial_duration': f"{Constants.iTrialDays} days",
                }
            )
            return {"status":True}
        except Exception as e:
            if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            else:
                raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")
            
    @staticmethod
    async def MSUpdateUserAPIUsage(user_id: int, pro_page_limit: Optional[int] = None, page_limit_left: Optional[int] = None, bOverwrite: bool = True):
        """
        Purpose : This method is used to update the API usage details of an existing user.

        Inputs  :   (1) user_id           :   User ID (integer)
                    (2) pro_page_limit    :   New page limit (integer, optional)
                    (3) page_limit_left   :   Remaining page limit (integer, optional)
                    (4) bOverwrite        :   Flag to indicate whether to overwrite or increment the limits (boolean, optional)

        Output  : It returns a dictionary with the updated API usage details or raises an HTTPException.

        Example : await CAuthController.MSUpdateUserAPIUsage(user_id=123, pro_page_limit=100, page_limit_left=50, bOverwrite=True)
        """
        objUser = None
        try:
            updateAPIUsageResponseMsg = "Successfully Updated User API Usage"
            await CLogController.MSWriteLog(user_id, "Info", f"User API Usage Update Started.")
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User).filter(User.uid == user_id))
                objUser = result.scalars().first()
                if not objUser:
                    raise HTTPException(status_code=404, detail="User not found")

                api_usage_result = await session.execute(select(UserAPIUsage).filter(UserAPIUsage.user_id == user_id))
                api_usage = api_usage_result.scalars().first()

                if pro_page_limit is not None:
                    if bOverwrite:
                        api_usage.total_allowed_page_limit = int(pro_page_limit)
                        if page_limit_left is not None:
                            api_usage.page_limit_left = int(page_limit_left) 
                    else:
                        api_usage.total_allowed_page_limit += int(pro_page_limit)
                        api_usage.page_limit_left += int(page_limit_left) if page_limit_left is not None else 0
                
                await session.commit()

                await CLogController.MSWriteLog(user_id, "Info", f"User API Usage For User {objUser.name} Successfully Updated.")
                await CUserLogController.MSWriteLog(objUser.uid, "Info", f"Page usage limit updated : New Pro pages Limit = {api_usage.page_limit_left}, New Lite pages Limit = ", "Profile")       # pending
                return {
                    "msg": updateAPIUsageResponseMsg,
                    "total_allowed_page_limit": api_usage.total_allowed_page_limit,
                    "page_limit_left": api_usage.page_limit_left
                }
        except HTTPException as http_exc:
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Unable to Update User API Usage, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc

        except IntegrityError as e:
            await session.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to update User API Usage.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Failed to update User API Usage, Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail="Update failed due to conflicting data entries. Please adjust your input and retry.")
        except Exception as e:
            await session.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to update User API Usage.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Failed to update User API Usage, Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred during updating User API Usage.")
    @staticmethod
    async def MSUpdateUserDetails(user_id: int, current_user_id:int,  name: Optional[str] = None, email: Optional[str] = None, phoneNumber: Optional[str] = None, profilePicture: Optional[UploadFile] = FastAPIFile(None), rolename: Optional[str] = None, Country: Optional[str] = None, usePaidOCR: Optional[bool] = None, usePaidDocExtractor: Optional[bool] = None, used_tokens: Optional[int] = None, api_requested: Optional[int] = None, page_limit_left: Optional[int] = None, pro_page_limit: Optional[int] = None, standard_page_limit_usage: Optional[int] = None, standard_page_limit: Optional[int] = None, promoCodeStr: Optional[str] = None, is_subscription_active: bool = None, active_plan_name: str = None, active_plan_type: str = None, bOverwrite: bool = True):
        """
        Purpose : This method is used to update the details of an existing user.

        Inputs  :   (1) user_id   :   User ID (integer)
                    (2) request   :   Request object containing the new user details.

        Output  : It returns a dictionary with the updated user details or raises an HTTPException.

        Example : await CAuthController.MSUpdateUserDetails(user_id=123, request=request)
        """
        objUser = None
        try:
            updateUserResponseMsg = "Succesfully Update User Field"
            await CLogController.MSWriteLog(user_id, "Info", f"User Data Update Started.")
            async with AsyncSessionLocal() as session:
                totalPageLimit = None
                if promoCodeStr is not None and promoCodeStr:
                    await CReferralCode.MSUsePromoCode(userid=user_id, strPromocode=promoCodeStr)
                    await CUserLogController.MSWriteLog(user_id, "Info", f"Promo code {promoCodeStr} applied Sucessfully", "Profile")   # pending counts
                
                # Define roles allowed to update other users' details
                allowed_roles = ["SuperAdmin", "Admin", "Developer", "Verifier"]
                # Check if current user have rights to update given user
                curUserResult = await session.execute(select(User, Role).join(Role, User.roleID == Role.Id).filter(User.uid == current_user_id))
                objCurUser = curUserResult.first()
                if not objCurUser:
                    raise HTTPException(status_code=404, detail="User not found")
                
                if objCurUser[1].RoleName not in allowed_roles and current_user_id != user_id:
                    raise HTTPException(status_code=403, detail="You are not authorized to update this user's details")

                result = await session.execute(select(User).filter(User.uid == user_id))
                objUser = result.scalars().first()
                if not objUser:
                    raise HTTPException(status_code=404, detail="User not found")

                api_usage_result = await session.execute(select(UserAPIUsage).filter(UserAPIUsage.user_id == user_id))
                api_usage = api_usage_result.scalars().first()
            
                # Update User Table fields
                update_fields = {"name": name, "email": email, "phoneNumber": phoneNumber, "rolename": rolename, "Country": Country, "usePaidOCR": usePaidOCR, "usePaidDocExtractor": usePaidDocExtractor}
                
                for field, value in update_fields.items():
                    if field == "rolename":
                        field = "roleID"
                    if hasattr(objUser, field) and value is not None:
                        if field == "roleID":
                            lsObjRoles = await session.execute(select(Role).filter(Role.RoleName == value))
                            objRole = lsObjRoles.scalars().first()                            
                            
                            if not objRole:
                                await CLogController.MSWriteLog(user_id, "Error", f"Role {value} Not Found.")
                                raise HTTPException(status_code=404, detail=f"Role Not Found.") 
                            
                            value = objRole.Id

                        if getattr(objUser, field) != value:
                            strTempObjUserField = copy.deepcopy(getattr(objUser, field))
                            setattr(objUser, field, value)
                            await CUserLogController.MSWriteLog(user_id, "Info", f"Value of {field} updated sucessfully from {strTempObjUserField} to {value}", "Profile")                            


                        

                
                if pro_page_limit is not None:      # pending
                    if bOverwrite:
                        api_usage.total_allowed_page_limit = pro_page_limit
                        if  page_limit_left is not None:
                            api_usage.page_limit_left = page_limit_left 
                    else:
                        api_usage.total_allowed_page_limit += pro_page_limit
                        api_usage.page_limit_left += page_limit_left if page_limit_left is not None else 0
                
                # Update API Usage fields
                api_usage_fields = {
                    "used_tokens": used_tokens, 
                    "api_requested": api_requested,
                    "free_page_limit_usage": standard_page_limit_usage,
                    "total_allowed_free_page_limit": standard_page_limit,"is_subscription_active": is_subscription_active, "active_plan_name": active_plan_name,"active_plan_type": active_plan_type
                }
                for field, value in api_usage_fields.items():
                    if value is not None:
                        setattr(api_usage, field, value)
                
                # Handle profile picture update
                if profilePicture and profilePicture is not None:
                    file_content = await profilePicture.read()
                    await profilePicture.seek(0)  # Reset file pointer if needed later

                    # Validate file size
                    if len(file_content) > 5 * 1024 * 1024:  # 5MB limit
                        await CLogController.MSWriteLog(user_id, "Error", f"Profile picture size must not exceed 5MB.")
                        raise HTTPException(status_code=400, detail="Profile picture size must not exceed 5MB.")

                    # Guess the file type
                    file_info = filetype.guess(file_content)

                    if file_info is None:
                        await CLogController.MSWriteLog(user_id, "Error", f"Unable to determine the file type. Please upload a valid image in JPEG, PNG, HEIC, or HEIF format.")
                        raise HTTPException(status_code=400, detail="Unable to determine the file type. Please upload a valid image in JPEG, PNG, HEIC, or HEIF format.")

                    file_type = file_info.mime
                    
                    accepted_mime_types = [
                        'image/jpeg',
                        'image/png',
                        'image/heic',
                        'image/heif',
                        'image/jpg',
                    ]

                    # Validate file type with clear error message
                    if file_type not in accepted_mime_types:
                        await CLogController.MSWriteLog(user_id, "Error", f"Invalid file type: {file_type}. Please use JPEG, PNG, HEIC, or HEIF format.")
                        raise HTTPException(status_code=400, detail=f"Invalid file type: {file_type}. Please use JPEG, PNG, HEIC, or HEIF format.")


                    # Assuming objUser.profilePicture stores the binary content
                    objUser.profilePicture = file_content
                    await CUserLogController.MSWriteLog(user_id, "Info", f"Prodile picture updated sucessfully", "Profile")
                
                await session.commit()
                # Construct the log message with all parameters and their values
                log_message = (
                    f"user_id={user_id}, current_user_id={current_user_id}, name={name}, email={email}, "
                    f"phoneNumber={phoneNumber}, profilePicture={profilePicture}, rolename={rolename}, "
                    f"Country={Country}, usePaidOCR={usePaidOCR}, usePaidDocExtractor={usePaidDocExtractor}, "
                    f"used_tokens={used_tokens}, api_requested={api_requested}, page_limit_left={page_limit_left}, "
                    f"pro_page_limit={pro_page_limit}, standard_page_limit_usage={standard_page_limit_usage}, "
                    f"standard_page_limit={standard_page_limit}, promoCodeStr={promoCodeStr}, "
                    f"is_subscription_active={is_subscription_active}, active_plan_name={active_plan_name}, "
                    f"active_plan_type={active_plan_type}, bOverwrite={bOverwrite}"
                )
                modified_log_message = (
                    f"used_tokens={api_usage.used_tokens}, api_requested={api_usage.api_requested}, free_page_limit_usage(STANDARD)={api_usage.free_page_limit_usage}, "
                    f"total_allowed_free_page_limit(STANDARD)={api_usage.total_allowed_free_page_limit}, is_subscription_active={api_usage.is_subscription_active}, "
                    f"active_plan_name={api_usage.active_plan_name}, active_plan_type={api_usage.active_plan_type}",
                    f"page_limit_left(PRO)={api_usage.page_limit_left},total_allowed_page_limit(PRO)={api_usage.total_allowed_page_limit}"
                )
                
                await CLogController.MSWriteLog(user_id, "Debug", f"Given User Data Updated Successfully | Given Input: {log_message} | Modifed Values: {modified_log_message}")
                return {
                    "msg":updateUserResponseMsg,
                    "profilePicture": "Updated" if profilePicture else "Not updated or unchanged"
                }
        except HTTPException as http_exc:
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Unable to Update User Details, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc
    
        except IntegrityError as e:
            await session.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to update User Data.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Failed to update User Data, Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail="Update failed due to conflicting data entries. Please adjust your input and retry.")
        except Exception as e:
            await session.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to update User Data.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Failed to update User Data, Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred during updating User Details.")
        
    @staticmethod
    async def MSGetAllUsers(iUserID, page: int = 1, per_page: int = 10):
        """
        Retrieves a list of all users with pagination, returning 10 users per page.
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Fetching of All User Started.")
            async with AsyncSessionLocal() as session:
                # Calculate offset for pagination
                offset = (page - 1) * per_page

                # Get total number of users for pagination metadata
                total_users = await session.scalar(select(func.count(User.uid)))

                # Execute query with limit and offset for pagination
                result = await session.execute(
                                                select(User, Role, UserAPIUsage)
                                                .join(Role, User.roleID == Role.Id)
                                                .join(UserAPIUsage, User.uid == UserAPIUsage.user_id)
                                                .offset(offset)
                                                .limit(per_page)
                                            )

    
                # lsUsersNRoles = result.all()
                lsUsersNRolesNApiUsage = result.all()
                
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched All Users.")

                # Convert user data and profile picture to Base64 (if exists)
                users_data = [
                    {
                        "uid": objUser.uid,
                        "email": objUser.email,
                        "name": objUser.name,
                        "role": objRole.RoleName,
                        "phoneNumber": objUser.phoneNumber,
                        "Country" : objUser.Country,
                        "profilePicture": base64.b64encode(objUser.profilePicture).decode('utf-8') if objUser.profilePicture else None,
                        "usePaidOCR": "Yes" if objUser.usePaidOCR else "No",
                        "usePaidDocExtractor": "Yes" if objUser.usePaidDocExtractor else "No",
                        "used_tokens": objAPIUsage.used_tokens,
                        "api_requested": objAPIUsage.api_requested,
                        "page_limit_left": objAPIUsage.page_limit_left,
                        "total_allowed_page_limit": objAPIUsage.total_allowed_page_limit,
                        "free_page_limit_usage" : objAPIUsage.free_page_limit_usage,
                        "total_allowed_free_page_limit" :  objAPIUsage.total_allowed_free_page_limit,
                        "promocodeUsage": await CReferralCode.MSGetUserPromoCodeUsage(userid=objUser.uid)


                    } for objUser, objRole, objAPIUsage  in lsUsersNRolesNApiUsage
                ]

                # Retrieving Roles Data
                role_result = await session.execute(select(Role))
                lsObjRoles = role_result.scalars().all()
                roles_data =  [
                                {
                                    "Id": objRole.Id,
                                    "RoleName": objRole.RoleName,
                                    "RolePriority": objRole.RolePriority
                                } for objRole in lsObjRoles
                            ]
                
                # Pagination metadata
                total_pages = (total_users + per_page - 1) // per_page  # Round up division
                pagination_info = {
                    "total_users": total_users,
                    "total_pages": total_pages,
                    "current_page": page,
                    "users_per_page": per_page,
                    "users": users_data,
                    "roles": roles_data
                }

                return pagination_info
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Retrieve All Users.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Failed to Retrieve All User Data , Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail=f"A technical error occurred while trying to fetch user details. Our team is investigating. Please retry in a few moments.")

    @staticmethod
    async def MSGetSingleUser(user_id: int) -> dict:
        """
        Retrieve details of a single user by their user ID, including the first integration configuration.

        Args:
            user_id (int): User ID.

        Returns:
            dict: Dictionary containing the details of the user and their first integration configuration.

        Raises:
            HTTPException: If an error occurs while retrieving the user details.
        """
        objUser = None
        try:
            # Log the start of user data fetching
            await CLogController.MSWriteLog(user_id, "Info", "Fetching user data started.")
            statuses_to_exclude = ["NotProcess", "Processing", "Error"]
            
            # Establish a database session
            async with AsyncSessionLocal() as session:
                # Execute the query to retrieve user details along with the first IntegrationConfig
                result = await session.execute(
                    select(User, Role, UserAPIUsage, IntegrationConfig)
                    .join(Role, User.roleID == Role.Id)
                    .join(UserAPIUsage, User.uid == UserAPIUsage.user_id)
                    .outerjoin(IntegrationConfig, User.uid == IntegrationConfig.UserID)
                    .where(User.uid == user_id)
                    .order_by(IntegrationConfig.Id.asc())  # Optional: Order to ensure consistency
                    .limit(1)  # Limit to fetch only the first IntegrationConfig
                )
                
                # Fetch the first result
                user_data = result.first()

                # Check if user details are found
                if user_data:
                    objUser, objRole, objAPIUsage, objIntegrationConfig = user_data
                    
                    # Extract the first IntegrationConfig if it exists
                    if objIntegrationConfig:
                        integration_config = {
                            "tallConfigId": objIntegrationConfig.Id,
                            "isTallyConfigured": objIntegrationConfig.Tally,
                            "created_datetime": objIntegrationConfig.CreatedDateTime,
                            "modified_datetime": objIntegrationConfig.ModifiedDateTime
                        }
                    else:
                        integration_config = None
                else:
                    # Log the error and raise an HTTPException
                    raise HTTPException(status_code=404, detail="User data not found.")

                # Fetch promo code usage
                lsPromocodeUsage = await CReferralCode.MSGetUserPromoCodeUsage(userid=user_id)

                # Execute additional queries to fetch related data
                total_docs_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(UploadedDoc)
                    .filter(UploadedDoc.UserId == user_id)
                )

                total_processed_status_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(UploadedDoc)
                    .filter(UploadedDoc.Status.notin_(statuses_to_exclude), UploadedDoc.UserId == user_id)
                )

                total_TallyStatus_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(UploadedDoc)
                    .filter(UploadedDoc.TallyStatus == "ToBeApproved", UploadedDoc.UserId == user_id)
                )

                total_IsDocApprovedStatus_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(UploadedDoc)
                    .filter(UploadedDoc.Status == 'Approved', UploadedDoc.UserId == user_id)
                )

                organization_name_result = await session.execute(
                    select(OrganizationDetails.CompanyName).where(OrganizationDetails.User_Id == user_id)
                )

                organization_name_row = organization_name_result.one_or_none()
                organization_name = organization_name_row[0] if organization_name_row else None

                # For Fetching Total Models --------------------------
                total_CreatedModels_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(ModelTable)
                    .filter(ModelTable.UserID == user_id)
                )
                
                # For Fetching Total Pages Processed --------------------------
                total_ProcessedPages_query = await session.scalar(
                    select(func.coalesce(func.sum(UploadedDoc.PageCount), 0))
                    .select_from(UploadedDoc)
                    .filter(
                        UploadedDoc.UserId == user_id,
                        UploadedDoc.Status.notin_(statuses_to_exclude)
                    )
                )
                
                # --------------------------
                
                # Convert objUser.created_at to a timezone-aware datetime object
                user_created_at_aware = pytz.timezone("Asia/Kolkata").localize(objUser.created_at)

                # Determine if the trial period has expired
                # bIsTrialExpired = (datetime.now(pytz.timezone("Asia/Kolkata")) - user_created_at_aware) > timedelta(days=Constants.iTrialDays)

                return {
                    "uid": objUser.uid,
                    "email": objUser.email,
                    "name": objUser.name,
                    "phoneNumber": objUser.phoneNumber,
                    "role": objRole.RoleName,
                    "usePaidOCR": "Yes" if objUser.usePaidOCR else "No",
                    "usePaidDocExtractor": "Yes" if objUser.usePaidDocExtractor else "No",
                    "promoCodeHistory": lsPromocodeUsage,
                    "used_tokens": objAPIUsage.used_tokens,
                    "api_requested": objAPIUsage.api_requested,
                    "page_limit_left": objAPIUsage.page_limit_left,
                    "total_allowed_page_limit": objAPIUsage.total_allowed_page_limit,
                    "free_page_limit_usage": objAPIUsage.free_page_limit_usage,
                    "total_allowed_free_page_limit": objAPIUsage.total_allowed_free_page_limit,
                    "isTrialPaidDocExtraction": bool(
                        objAPIUsage.page_limit_left > 0 and 
                        objAPIUsage.page_limit_left <= objAPIUsage.total_allowed_page_limit
                    ),
                    "created_at": objUser.created_at,
                    "updated_at": objUser.updated_at,
                    "profilePicture": base64.b64encode(objUser.profilePicture).decode('utf-8') if objUser.profilePicture else None,
                    "Country": objUser.Country,
                    "total_processed_pages": total_ProcessedPages_query,
                    "total_models_created": total_CreatedModels_query,
                    "total_documents": total_docs_query,
                    "total_processed_status": total_processed_status_query,
                    "total_TallyStatus": total_TallyStatus_query,
                    "total_IsDocApprovedStatus": total_IsDocApprovedStatus_query,
                    "organization_name": organization_name,
                    # "trialExpired": bIsTrialExpired,
                    "plan_active_status": objAPIUsage.is_subscription_active,
                    "active_plan": objAPIUsage.active_plan_name,
                    "active_plan_type": objAPIUsage.active_plan_type,
                    "integration_config": integration_config  # Changed to single integration config
                }

        except HTTPException as http_exc:
            # Re-raise HTTPException to propagate it
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Unable to Authenticate User, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc

        except Exception as e:
            # Log the error and raise an HTTPException
            await CLogController.MSWriteLog(user_id, "Error", "Failed to fetch user details.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the user details")
        
    @staticmethod
    async def MSGetUserMetaData(user_id: int) -> dict:
        """
        Retrieve details, metadata, and list of models for a single user by their user ID.

        Args:
            user_id (int): User ID.

        Returns:
            dict: Dictionary containing the details, metadata, and list of models of the user.

        Raises:
            HTTPException: If an error occurs while retrieving the user details or models.
        """
        objUser = None
        try:
            # Log the start of user data fetching
            await CLogController.MSWriteLog(user_id, "Info", "Fetching user data, metadata, and models started.")
            # await CUserLogController.MSWriteLog(user_id, "Info", f"Fetching user data, metadata, and model started ", "Profile")

            # Establish a database session
            async with AsyncSessionLocal() as session:
                # Execute the query to retrieve user details
                result = await session.execute(
                    select(User, Role, UserAPIUsage)
                    .join(Role, User.roleID == Role.Id)
                    .join(UserAPIUsage, User.uid == UserAPIUsage.user_id)
                    .where(User.uid == user_id)
                )
                # Fetch the result
                objUser, objRole, objAPIUsage = result.first()

                # Check if user details are found
                if not objUser or not objRole or not objAPIUsage:
                    # Log the error and raise an HTTPException
                    # await CLogController.MSWriteLog(user_id, "Error", "User data not found.")
                    raise HTTPException(status_code=404, detail="User data not found.")

                lsPromocodeUsage = await CReferralCode.MSGetUserPromoCodeUsage(userid=user_id)

                # Additional queries to fetch related data
                total_created_models_query = await session.scalar(
                    select(func.coalesce(func.count(), 0))
                    .select_from(ModelTable)
                    .filter(ModelTable.UserID == user_id)
                )

                # Query to fetch list of models for the user
                user_models_query = await session.execute(
                    select(ModelTable)
                    .filter(ModelTable.UserID == user_id)
                )
                user_models = user_models_query.scalars().all()

                # Construct metadata dictionary
                metadata = {
                    "uid": objUser.uid,
                    "email": objUser.email,
                    "name": objUser.name,
                    "role": objRole.RoleName,
                    "usePaidOCR": "Yes" if objUser.usePaidOCR else "No",
                    "usePaidDocExtractor": "Yes" if objUser.usePaidDocExtractor else "No",
                    "promoCodeUsage": lsPromocodeUsage,
                    "plan_active_status":objAPIUsage.is_subscription_active,
                    "active_plan":objAPIUsage.active_plan_name,
                    "active_plan_type":objAPIUsage.active_plan_type,
                    "page_limit_left": objAPIUsage.page_limit_left,
                    "total_allowed_page_limit":objAPIUsage.total_allowed_page_limit,
                    "free_page_limit_usage" : objAPIUsage.free_page_limit_usage,
                    "total_allowed_free_page_limit" :  objAPIUsage.total_allowed_free_page_limit,
                    "total_models_created": total_created_models_query,
                    "models": [model.Name for model in user_models]  # List of model names
                    # Add more metadata fields here as needed
                }

                return metadata

        except HTTPException as http_exc:
            # Re-raise HTTPException to propagate it
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Unable to Get User Meta Data, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc
            
        except Exception as e:
            # Log the error and raise an HTTPException
            await CLogController.MSWriteLog(user_id, "Error", "Failed to fetch user details, metadata, and models.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the user details, metadata, and models")
    
    @staticmethod
    async def MSGetUserOnEmailId(EmailID: int) -> dict:
        """
        Retrieve details, metadata, and list of models for a single user by their email ID.

        Args:
            EmailID (int): User's email ID.

        Returns:
            dict: Dictionary containing the details, metadata, and list of models of the user.

        Raises:
            HTTPException: If an error occurs while retrieving the user details or models.
        """
        objUser = None
        try:
            # Log the start of user data fetching
            await CLogController.MSWriteLog(None, "Info", "Fetching user data, metadata, and models started.")

            # Establish a database session
            async with AsyncSessionLocal() as session:
                # Execute the query to retrieve user details
                result = await session.execute(
                    select(User)
                    .where(User.email == EmailID)
                )
                # Fetch the result
                objUser = result.scalar_one_or_none()

                # Check if user details are found
                if not objUser:
                    # Log the error and raise an HTTPException
                    raise HTTPException(status_code=404, detail="User data not found.")

                # Construct metadata dictionary
                metadata = {
                    "uid": objUser.uid,  # Ensure objUser has 'uid' attribute
                    "email": objUser.email,
                    "name": objUser.name,
                }

                return metadata

        except SQLAlchemyError as e:
            # Log the database error
            await CLogController.MSWriteLog(None, "Error", "Database error while fetching user details.")
            await CLogController.MSWriteLog(None, "Debug", f"SQLAlchemy Error: {str(e)}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the user details")

        except HTTPException as http_exc:
            # Re-raise HTTPException to propagate it
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{EmailID}' Unable to Get User Meta Data, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc
        
        except Exception as e:
            # Log the error and raise an HTTPException
            await CLogController.MSWriteLog(None, "Error", "Failed to fetch user details.")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the user details")
    
    @staticmethod
    async def MSGetAllUsersMetaData(iUserID, page: int = 1, per_page: int = 50, userFilter: str = None):
        """
        Retrieves a list of all users with pagination, returning 10 users per page.
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Fetching of All User Started.")
            async with AsyncSessionLocal() as session:
                # Calculate offset for pagination
                offset = (page - 1) * per_page

                # Get total number of users for pagination metadata
                total_users = await session.scalar(select(func.count(User.uid)))
                query = select(User, Role, UserAPIUsage).join(Role, User.roleID == Role.Id).join(UserAPIUsage, User.uid == UserAPIUsage.user_id)

                if userFilter:
                    query = query.filter(User.name.ilike(f'%{userFilter}%'))

                query = query.offset(offset).limit(per_page)
                
                result = await session.execute(query)
    
                # lsUsersNRoles = result.all()
                lsUsersNRolesNApiUsage = result.all()
                
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched All Users.")

                # Convert user data and profile picture to Base64 (if exists)
                users_data = {
                    objUser.uid: {
                        "id":objUser.uid,
                        "email": objUser.email,
                        "name": objUser.name,
                        "role": objRole.RoleName,
                        "phoneNumber": objUser.phoneNumber,
                        "Country": objUser.Country,
                        "profilePicture": base64.b64encode(objUser.profilePicture).decode('utf-8') if objUser.profilePicture else None,
                        "usePaidOCR": "Yes" if objUser.usePaidOCR else "No",
                        "usePaidDocExtractor": "Yes" if objUser.usePaidDocExtractor else "No",
                        "used_tokens": objAPIUsage.used_tokens,
                        "api_requested": objAPIUsage.api_requested,
                        "plan_active_status":objAPIUsage.is_subscription_active,
                        "active_plan":objAPIUsage.active_plan_name,
                        "active_plan_type":objAPIUsage.active_plan_type,
                        "page_limit_left": objAPIUsage.page_limit_left,
                        "free_page_limit_usage" : objAPIUsage.free_page_limit_usage,
                        "total_allowed_free_page_limit" :  objAPIUsage.total_allowed_free_page_limit,
                    } for objUser, objRole, objAPIUsage in lsUsersNRolesNApiUsage
                }

                # Pagination metadata
                total_pages = (total_users + per_page - 1) // per_page  # Round up division
                pagination_info = {
                    "total_users": total_users,
                    "total_pages": total_pages,
                    "current_page": page,
                    "users_per_page": per_page,
                    "users": users_data
                }

                return pagination_info
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Retrieve All Users.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Failed to Retrieve All User Data , Traceback : {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail=f"An error occurred while retrieving data.")
    
    @staticmethod 
    async def MSDeleteUser(user_id: int):
        """
        Purpose : This method is used to delete a user by their user ID.

        Inputs  :   (1) user_id   :   User ID (integer)

        Output  : It returns a confirmation message of the deletion or raises an HTTPException.

        Example : await CAuthController.MSDeleteUser(user_id=123)
        """
        try:
            objUser = None
            await CLogController.MSWriteLog(user_id, "Info", f"User Deletion Process Started")
            # await CUserLogController.MSWriteLog(user_id, "Info", f"User Deletion Process Started", "Profile")
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User).filter(User.uid == user_id))
                objUser = result.scalars().first()
                if not objUser:
                    raise HTTPException(status_code=404, detail="User not found")
                
                await session.delete(objUser)
                await session.commit()
                return {"detail": "User deleted successfully"}

        except HTTPException as http_exc:
            # Re-raise HTTPException to propagate it
            if objUser:
                await CLogController.MSWriteLog(objUser.uid, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                # await CLogController.MSWriteLog(None, "Error", f"Information: '{request.email.lower()}' Failed to Login!!!")
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Unable to Delete User, \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc
        
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"There was some error deleting the user.")
            await CLogController.MSWriteLog(user_id, "Debug", f"There was some error deleting the user with userID{user_id}. Traceback : {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while deleting the user")


    @staticmethod 
    async def MSForgotPassword(myBackgroundTasks: BackgroundTasks, objFpr: ForgotPasswordModel):
        """
        Purpose : This method is used to initiate a password reset process by sending a reset link to the user's email.

        Inputs  :   (1) myBackgroundTasks : BackgroundTasks object for background execution.
                    (2) objFpr          : ForgotPasswordModel object containing the user's email.

        Output  : It returns a message indicating that a password reset link has been sent.

        Example : await CAuthController.MSForgotPassword(myBackgroundTasks=BackgroundTasks(), objFpr=ForgotPasswordModel(email="<EMAIL>"))
        """
        iUserID = None
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User.uid).filter(User.email == objFpr.email.lower()))
                iUserID = result.scalars().first()

                if iUserID is None:
                    # await CLogController.MSWriteLog(iUserID, "Error", f"User does not exist.")
                    raise HTTPException(status_code=404, detail="User with this email does not exist.")

                strSecretToken = TokenHelper.create_access_token(data={"email": objFpr.email,"uid":iUserID}, expires_delta=timedelta(minutes=int(os.getenv("ForgetEmailExpiryTime"))))

                strForgetUrlLink = f"{os.getenv('FORGET_PASSWORD_URL')}{strSecretToken}"

                # Check if a PasswordReset record already exists for the email
                existing_reset = await session.execute(select(PasswordReset).filter(PasswordReset.email == objFpr.email.lower()))
                objExistingReset = existing_reset.scalars().first()

                # If a record exists, update the token and created_at fields
                if objExistingReset:
                    objExistingReset.token = strSecretToken
                    objExistingReset.created_at = datetime.utcnow()
                else:
                    # If no record exists, create a new one
                    objResetUser = PasswordReset(
                        email=objFpr.email.lower(),
                        token=strSecretToken,
                        created_at=datetime.utcnow(),
                    )
                    session.add(objResetUser)

                await session.commit()


                strEmailBody = CResetPassword.strResetPasswordContent.format(strForgetUrlLink=strForgetUrlLink, CompanyName = os.getenv('MAIL_FROM_NAME'))

                objMessage = MessageSchema(
                    subject="Password Reset Instructions",
                    recipients=[objFpr.email],
                    body=strEmailBody,
                    subtype="html"
                )

                fm = FastMail(CResetPassword.mail_conf)
                myBackgroundTasks.add_task(fm.send_message, message=objMessage)
                
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Sent Password Resetting Link.")
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Forget Password : Password reset link sucessfully send to registered email address", "Profile")
                
                return {"message": "A password reset link has been sent to this email."}
        except HTTPException as http_exc:
            if iUserID:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{objFpr.email.lower()}' Error while processing forgot password., \n\nTraceback: {str(traceback.format_exc())}")
            raise http_exc

        except Exception as e:
            await session.rollback()
            if iUserID:
                await CLogController.MSWriteLog(iUserID, "Error", f"There was some error while processing forgot password .")
                await CLogController.MSWriteLog(iUserID, "Debug", f"There was some error while processing forgot password. Traceback : {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Error", f"There was some error while processing forgot password .")
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{objFpr.email.lower()}' There was some error while processing forgot password., \n\nTraceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred during the password reset process")


    @staticmethod 
    async def MSResetPassword( objRequest: ResetPassword, strToken: str) :
        """
        Purpose : This method is used to reset the password of a user using a token.

        Inputs  :   (1) objRequest : ResetPassword object containing the new password.
                    (2) strToken   : Token string used for verification.

        Output  : It returns a message indicating that the password has been successfully reset.

        Example : await CAuthController.MSResetPassword(objRequest=ResetPasswordModel(password="newpassword"), strToken="token")
        """
        iUserID = None
        try:

            strEmail,iUserID, bIsExpired = TokenHelper.verify_token(strToken)

            if (not strEmail) or (bIsExpired):
                async with AsyncSessionLocal() as session:
                    result = await session.execute(select(PasswordReset).filter(PasswordReset.token == strToken))
                    objResetRecord = await result.scalars().first()
                    if objResetRecord:
                        await session.delete(objResetRecord)
                        await session.commit()
                # await CLogController.MSWriteLog(iUserID, "Error", f"The password reset link has expired.")
                await CLogController.MSWriteLog(iUserID, "Error", f"Your password reset link has expired. Please request a new one.")
                raise HTTPException(status_code=401, detail="Your password reset link has expired. Please request a new one.")

            async with AsyncSessionLocal() as session:
                result = await session.execute(select(PasswordReset).filter(PasswordReset.token == strToken))
                objResetRecord = result.scalars().first()
                if not objResetRecord:
                    # await CLogController.MSWriteLog(iUserID, "Error", f"Token not found or invalid.")
                    await CLogController.MSWriteLog(iUserID, "Error", f"The password reset link you provided is not valid. Please check the link and try again.")
                    raise HTTPException(status_code=404, detail="The password reset link you provided is not valid. Please check the link and try again.")

                userResult = await session.execute(select(User).filter(User.email == strEmail))
                objUser = userResult.scalars().first()
                if not objUser or objUser.email != strEmail:
                    # await CLogController.MSWriteLog(iUserID, "Error", f"User not found or email does not match token.")
                    await CLogController.MSWriteLog(iUserID, "Error", f"We couldn't find a user with that email address associated with the provided reset link. Please ensure the email and link are correct.")
                    raise HTTPException(status_code=404, detail="We couldn't find a user with that email address associated with the provided reset link. Please ensure the email and link are correct.")

                strHashedPassword = Hashing.get_hash(objRequest.password)
                objUser.password = strHashedPassword
                await session.commit()

                await session.delete(objResetRecord)
                await session.commit()
                
                # await CLogController.MSWriteLog(iUserID, "Info", f"Password Reset Was Successful.")

            await CUserLogController.MSWriteLog(objUser.uid, "Info", f"Password sucessfully reset", "Profile")
            return {"message": "Password successfully reset."}
        
        except HTTPException as http_exc:
            if iUserID:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback : {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Error", f"There was some error while password Resetting.")
                await CLogController.MSWriteLog(None, "Debug", f"There was some error while password Resetting with Request:{objRequest}. Traceback : {str(traceback.format_exc())}")
            # Re-raise HTTPException to propagate it
            raise http_exc

        
        except Exception as e:
            if iUserID:         
                await CLogController.MSWriteLog(iUserID, "Error", f"There was some error while password Resetting .")
                await CLogController.MSWriteLog(iUserID, "Debug", f"There was some error while password Resetting with Request:{objRequest}. Traceback : {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Error", f"There was some error while password Resetting.")
                await CLogController.MSWriteLog(None, "Debug", f"There was some error while password Resetting with Request:{objRequest}. Traceback : {str(traceback.format_exc())}")     
            raise HTTPException(status_code=500, detail="An error occurred during the password reset process")
        

    @staticmethod
    async def MSUpdateUserPassword(user_id: int, objRequest : UpdatePassword):
        async with AsyncSessionLocal() as session:
            objUser = None
            try:
                # Retrieve the user from the database
                result = await session.execute(select(User).filter(User.uid == user_id))
                objUser = result.scalars().first()
                if not objUser:
                    await CLogController.MSWriteLog(user_id, "Error", "User Not Found.")
                    raise HTTPException(status_code=404, detail="User not found")
                

                # Verify the current password
                if not Hashing.verify(objRequest.CurrentPassword, objUser.password):
                    await CLogController.MSWriteLog(user_id, "Error", "Incorrect current password.")
                    raise HTTPException(status_code=400, detail="Incorrect current password")

                # Update the password with the new hashed password
                objUser.password = Hashing.get_hash(objRequest.UpdatedPassword)
                session.add(objUser)
                await session.commit()

                await CLogController.MSWriteLog(user_id, "Info", f"Password for User {objUser.name} Successfully Updated.")
                await CUserLogController.MSWriteLog(user_id, "Info", f"Password Successfully Updated", "Profile")
                return {"msg": "Password updated successfully"}
            
            except HTTPException as http_exc:
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise http_exc

            except IntegrityError as e:
                await session.rollback()
                await CLogController.MSWriteLog(user_id, "Error", "Failed to update password.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Error updating password")
            except Exception as e:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to update password.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Error during updating password")
            

    @staticmethod 
    async def sendOTPToEmail(myBackgroundTasks: BackgroundTasks, request_body: ForgotPasswordModel):
        """
        This method sends an OTP to the user's email and returns the OTP for frontend verification.

        Note: Returning OTP to the frontend is for demonstration purposes only and should not be used in production due to security concerns.
        """
        user_id = None
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User.uid).filter(User.email == request_body.email.lower()))
                user_id = result.scalars().first()

                if user_id is not None:
                    raise HTTPException(status_code=404, detail="User with this email already exist. Please Login")

                # Generate a 6-digit OTP
                otp = randint(100000, 999999)
                strOTP = f"{otp:06d}" # Ensures the OTP is 6 digits
                

                strEmailBody = CResetPassword.strOtpMessageContent.format(strOTP=strOTP)
                

                objMessage = MessageSchema(
                    subject="AccuVelocity : Sign Up OTP",
                    recipients=[request_body.email],
                    body=strEmailBody,
                    subtype="html"
                )

                fm = FastMail(CResetPassword.mail_conf)
                myBackgroundTasks.add_task(fm.send_message, message=objMessage)
                
                
                return {"otp": strOTP, "message": "An OTP has been sent to your email."}
        except HTTPException as http_exc:
            if user_id:
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Error while Sending OTP., \n\nTraceback: {str(traceback.format_exc())}")
            # Re-raise HTTPException to propagate it
            raise http_exc
            
        except Exception as e:
            await session.rollback()
            if user_id:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to Send OTP.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            else:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to Send OTP.")
                await CLogController.MSWriteLog(None, "Debug", f"Information: '{user_id}' Error while Sending OTP., \n\nTraceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while sending the OTP.")
        
    async def CheckEmailExists(email: str) -> bool:
        """
        Purpose : This method checks if an email exists in the database.

        Inputs  :   (1) email   :   A string containing the user's email to be checked.

        Output  : It returns True if the email exists in the database, otherwise False.

        Example : exists = await CAuthController.CheckEmailExists(email="<EMAIL>")
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(User).filter(User.email == email.lower()))
                user = result.scalars().first()
                
                # If a user is found with the provided email, return True, otherwise False
                return user is not None
            
        except Exception as e:
            raise HTTPException(status_code=500, detail="An error occurred while processing your request.")
    
    @staticmethod    
    async def MSEmailValidation(email: str) -> bool:
        """
        Purpose : This method checks if an email exists in the database and checks if the email is valid,
                meaning it is not a disposable or spamtrap email.

        Inputs  :  
            email : str : The email address to be validated.

        Output  : 
            EmailValidation : An instance of EmailValidation with isEmailExist and isValidEmail fields.

        Example : 
            email_validation = await MSEmailValidation("<EMAIL>")
            print(email_validation)

        """
        try:
            isEmailExist, isValidEmail = None, None 
            isEmailExist = await CAuthController.CheckEmailExists(email=email)
            if isEmailExist:
                emailValidationResponse  = EmailValidation(isEmailExist=True, isValidEmail=True)   # As it will be valid only if user is registered
            else:
                ObjEmailVerifier = CEmailVerifier(email=email)
                isValidEmail = await ObjEmailVerifier.isValidEmail()
                emailValidationResponse  = EmailValidation(isEmailExist=isEmailExist, isValidEmail=isValidEmail)
            return emailValidationResponse
        except Exception as e:
            raise HTTPException(status_code=500, detail="An error occurred while processing your request.")
    
    @staticmethod
    async def register_organization(user_id: int, company_name: str, team_strength: str, invoices_processed_per_day: str, strDesignation:str, company_url: str = ""):
        """
        Registers or updates an organization for a user.

        :param user_id: The ID of the user registering the organization.
        :param company_name: The name of the company.
        :param company_url: The url of the company.
        :param team_strength: The number of people in the company.
        :param invoices_processed_per_day: The number of invoices processed per day.
        :return: A dictionary with the details of the registered or updated organization or raises an HTTPException.
        """
        try:        # pending
            async with AsyncSessionLocal() as session:
                # Check if the organization with the given user_id already exists
                existing_organization = await session.get(OrganizationDetails, user_id)
                
                if existing_organization:
                    # Update the existing organization
                    existing_organization.CompanyName = company_name
                    existing_organization.CompanyURL = company_url
                    existing_organization.Designation=strDesignation,
                    existing_organization.TeamStrength = team_strength
                    existing_organization.InvoicesProcessedPerDay = invoices_processed_per_day
                else:
                    # Create a new organization record if not found
                    new_organization = OrganizationDetails(
                        User_Id=user_id,
                        CompanyName=company_name,
                        CompanyURL=company_url,
                        Designation=strDesignation,
                        TeamStrength=team_strength,
                        InvoicesProcessedPerDay=invoices_processed_per_day
                    )
                    session.add(new_organization)

                # Commit the changes and refresh the object
                await session.commit()
                if existing_organization:
                    await session.refresh(existing_organization)
                    organization = existing_organization
                else:
                    await session.refresh(new_organization)
                    organization = new_organization

                # Return the details of the registered or updated organization
                return {
                    "organization_id": organization.Id,
                    "company_name": organization.CompanyName,
                    "company_url": organization.CompanyURL,
                    "designation": organization.Designation,
                    "team_strength": organization.TeamStrength,
                    "invoices_processed_per_day": organization.InvoicesProcessedPerDay,
                    "created_at": organization.CreatedDateTime.isoformat(),
                    "updated_at": organization.UpdatedDateTime.isoformat()
                }
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", "Failed to register Organization")
            await CLogController.MSWriteLog(user_id, "Debug", f"IntegrityError: {str(e)}")
            raise HTTPException(status_code=500, detail="An error occurred during the organization registration or update process")

if __name__ == '__main__':
    objCAuthController = CAuthController()
    objCAuthController.test_temp()
