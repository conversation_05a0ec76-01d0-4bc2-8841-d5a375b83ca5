import { useEffect, useState } from 'react';
import axios from 'axios';
import { isActivePaidPlanAtom } from '../context/TrailUsageUserData'
import { useRecoilState } from 'recoil';
import { useLocation } from 'react-router-dom';
import toast, { Toaster } from 'react-hot-toast';

const UpgradePlanPage = () => {
  // Initialize state variables with default values from cookies
  const [IsActivePlan, _] = useRecoilState(isActivePaidPlanAtom);

  const stripe_livemode = `${import.meta.env.VITE_STRIPE_LIVEMODE}`
  let planPrices;
  if (stripe_livemode.toLowerCase() == "true") {
    planPrices = {
      "plans": {
        "monthly": [
          {
            "Starter Plan": {
              "ProductID": "prod_QX9srG3ZsoUaFp",
              "PriceID": "price_1Pg5YgD9i0KVpTcaScROSkTy",
              "description": "$25.99 / month",
              "currency": "usd",
              "amount": 25.99
            }
          },
          {
            "Professional Plan": {
              "ProductID": "prod_QX9srhH7ENOLQa",
              "PriceID": "price_1Pg5YdD9i0KVpTcamg4KFJ4L",
              "description": "$259.99 / month",
              "currency": "usd",
              "amount": 259.99
            }
          }
        ],
        "yearly": [
          {
            "Starter Plan": {
              "ProductID": "prod_QX9srG3ZsoUaFp",
              "PriceID": "price_1Pg5YgD9i0KVpTcaiN6HjyT7",
              "description": "$239.88 / year",
              "currency": "usd",
              "amount": 239.88
            }
          },
          {
            "Professional Plan": {
              "ProductID": "prod_QX9srhH7ENOLQa",
              "PriceID": "price_1Pg5YdD9i0KVpTca2xL68AYm",
              "description": "$2,399.88 / year",
              "currency": "usd",
              "amount": 2399.88
            }
          }
        ]
      }
    };
  }
  else {
    planPrices = {
      "plans": {
        "monthly": [
          {
            "Starter Plan": {
              "ProductID": "prod_QWnxFG4C89Dzc1",
              "PriceID": "price_1PfkLdD9i0KVpTcad0ne7AV0",
              "description": "$25.99 / month",
              "currency": "usd",
              "amount": 25.99
            }
          },
          {
            "Professional Plan": {
              "ProductID": "prod_QWnzow5dZX39pm",
              "PriceID": "price_1PfkNnD9i0KVpTcay3uFTXow",
              "description": "$259.99 / month",
              "currency": "usd",
              "amount": 259.99

            }
          }
        ],
        "yearly": [
          {
            "Starter Plan": {
              "ProductID": "prod_QWnxFG4C89Dzc1",
              "PriceID": "price_1PfkLdD9i0KVpTcaA0Kqx19m",
              "description": "$239.88 / month",
              "currency": "usd",
              "amount": 239.88
            }
          },
          {
            "Professional Plan": {
              "ProductID": "prod_QWnzow5dZX39pm",
              "PriceID": "price_1PfkNnD9i0KVpTcabfUFR96q",
              "description": "$2,399.88 / month",
              "currency": "usd",
              "amount": 2399.88
            }
          }
        ]
      }
    };

  }

  const location = useLocation();

  useEffect(() => {
    if (location.state?.showMessage) {
      toast.error(
        <div>
          It seems you don&apos;t have an AccuVelocity premium subscription yet. Please click{' '}
          <a href="/upgradeplan" style={{ color: '#003654', textDecoration: 'underline', fontWeight: 500 }}>
            <strong>Upgrade</strong>
          </a>{' '}
          to get started with a premium subscription.
        </div>,
        {
          type: 'error',
          icon: '⚠️',
          duration: 6000
        }
      );
    }
  }, []);

  const [isAnnual, setIsAnnual] = useState(true);

  const togglePricing = () => {
    setIsAnnual(!isAnnual);
  };
  const handlePlanSelection = async (plan, isAnnual) => {
    const planType = isAnnual ? 'yearly' : 'monthly';
    const selectedPlan = planPrices.plans[planType].find(p => Object.keys(p)[0] === `${plan} Plan`);
    const priceID = selectedPlan ? selectedPlan[`${plan} Plan`].PriceID : null;
    console.log("priceID", priceID, selectedPlan, planType)
    if (priceID) {
      try {
        const response = await axios.post(`${import.meta.env.VITE_SERVER}/create-checkout-session`, {
          price_id: priceID
        }, {
          headers: {
            "Authorization": `Bearer ${localStorage.getItem('token')}`
          }
        });
        if (response.data) {
          // Handle successful response, e.g., redirect to checkout page
          console.log('Checkout session created:', response.data);
          // Redirect to the checkout session URL
          window.location.href = response.data.url;
        }
      } catch (error) {
        console.error("Oops! We couldn't create the checkout session right now. Please try again later.", error);
      }
    } else {
      console.error('Price ID not found for the selected plan and billing period.');
    }
    // }
  };

  const handleContactUsQuery = () => {
    window.location.href = "https://calendly.com/accuvelocity/accuvelocity-insights-a-detailed-exploration?month=2024-07";
  };

  const tick = (
    <svg width="19" height="19" viewBox="0 0 19 19" fill="none" xmlns="http://www.w3.org/2000/svg">
      <path fillRule="evenodd" clipRule="evenodd" d="M11.1125 0.905959C10.1812 0.119888 8.8188 0.119889 7.88749 0.905959L7.03932 1.62185C6.64349 1.95595 6.15365 2.15886 5.6375 2.20251L4.53155 2.29603C3.31717 2.39873 2.35381 3.3621 2.25111 4.57647L2.15759 5.68242C2.11394 6.19857 1.91102 6.68841 1.57692 7.08426L0.861037 7.93241C0.0749661 8.86372 0.0749673 10.2261 0.861037 11.1574L1.57692 12.0056C1.91102 12.4014 2.11394 12.8913 2.15759 13.4074L2.25111 14.5134C2.35381 15.7278 3.31717 16.6912 4.53155 16.7938L5.6375 16.8873C6.15365 16.931 6.64349 17.1339 7.03934 17.468L7.88749 18.1839C8.8188 18.9699 10.1812 18.9699 11.1125 18.1839L11.9607 17.468C12.3565 17.1339 12.8464 16.931 13.3625 16.8873L14.4685 16.7938C15.6829 16.6912 16.6462 15.7278 16.7489 14.5134L16.8424 13.4074C16.8861 12.8913 17.089 12.4014 17.4231 12.0056L18.139 11.1574C18.925 10.2261 18.925 8.86372 18.139 7.93241L17.4231 7.08425C17.089 6.68841 16.8861 6.19857 16.8424 5.68242L16.7489 4.57647C16.6462 3.3621 15.6829 2.39873 14.4685 2.29603L13.3625 2.20251C12.8464 2.15886 12.3565 1.95595 11.9607 1.62185L11.1125 0.905959ZM14.046 7.84047C14.4854 7.40113 14.4854 6.68882 14.046 6.24948C13.6067 5.81013 12.8944 5.81013 12.455 6.24948L8.25054 10.454L6.54604 8.74948C6.1067 8.31013 5.39439 8.31013 4.95505 8.74948C4.5157 9.18882 4.5157 9.90113 4.95505 10.3405L7.45505 12.8404C7.89439 13.2798 8.6067 13.2798 9.04604 12.8404L14.046 7.84047Z" fill="#003654" />
    </svg>
  );

  const pricingRows = [
    { feature: 'Page Limit', starter: isAnnual ? 'Upto 900 Pages' : 'Upto 75 Pages', professional: isAnnual ? 'Upto 12000 Pages' : 'Upto 1000 Pages', bussiness: 'Custom' },

    { feature: '24/7 Customer Support', starter: <div className="flex items-center justify-center text-center">{tick}</div>, professional: <div className="flex items-center justify-center text-center">{tick}</div>, bussiness: <div className="flex items-center justify-center text-center">{tick}</div> },
    { feature: 'Unlimited Custom Models', starter: <div className="flex items-center justify-center text-center">{tick}</div>, professional: <div className="flex items-center justify-center text-center">{tick}</div>, bussiness: <div className="flex items-center justify-center text-center">{tick}</div> },
    { feature: 'Data Retention', starter: 'Upto 6 months', professional: 'Upto 12 months', bussiness: 'Custom' },
    { feature: 'Priority Processing', starter: '', professional: <div className="flex items-center justify-center text-center">{tick}</div>, bussiness: 'Custom' }


  ];

  const additionalRows = [
    { feature: 'Dedicated Account Manager', starter: '', professional: <div className="flex items-center justify-center text-center">{tick}</div>, bussiness: <div className="flex items-center justify-center text-center">{tick}</div> },
    {
      feature: 'Custom RPA Access',
      starter: <a className='underline' target='_blank'
        href='https://calendly.com/accuvelocity/accuvelocity-insights-a-detailed-exploration?month=2024-07'>Add-on Feature - Contact sales</a>,
      professional: <a className='underline' target='_blank'
        href='https://calendly.com/accuvelocity/accuvelocity-insights-a-detailed-exploration?month=2024-07'>Add-on Feature - Contact sales</a>,
      bussiness: <a className='underline' target='_blank'
        href='https://calendly.com/accuvelocity/accuvelocity-insights-a-detailed-exploration?month=2024-07'>Add-on Feature - Contact sales</a>
    }
  ];
  return (
    <>
      <Toaster position="top-center" />
      <div className="flex flex-col min-h-screen">
        <main className="flex-grow bg-[#ffff] min-h-screen" style={{ paddingTop: '0.75rem' }}>
          <section className="bg-[#ffff] py-8 px-4">
            <div className="container mx-auto text-center">
              <h2 className="text-3xl font-bold mb-4 text-[#003654]">Transparent Pricing for Exceptional Value</h2>
              <div className="flex justify-center items-center my-6">
                <span className="text-sm font-medium text-[#575757] mx-2">Monthly</span>
                <button
                  className={`w-14 h-7 flex items-center rounded-full p-1 duration-300 ease-in-out ${isAnnual ? 'bg-[#003654]' : 'bg-[#CCCCCC]'}`}
                  onClick={togglePricing}
                >
                  <div className={`bg-[#ffff] w-5 h-5 rounded-full shadow-md ${isAnnual ? 'translate-x-7' : ''}`} />
                </button>
                <span className="text-sm font-medium text-[#575757] mx-2">Annual</span>
              </div>
              <p className="text-[#575757] text-sm">Save Upto 30% on Annual Plans</p>
              <div className="overflow-x-auto mt-6">
                <table className="min-w-full">
                  <thead className="bg-[#FFF] text-[#003654]">
                    <tr>
                      <th className="py-4 px-6 border-t border-l border-b text-left align-center border-[#E6E9F5] w-1/5">
                        <div className="font-bold text-xl">Compare plans</div>
                        <div className="mt-3 font-light text-sm text-[#858BA0]">
                          Choose your workspace plan according to your organisational need
                        </div>
                        <div className="mt-3 inline-block text-[#003654] bg-[#FFF] border-2 border-[#E6E9F5] py-2 px-4 rounded-full text-xs">
                          Upto 30% Off on Annual Plans
                        </div>
                      </th>

                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Starter</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">For individuals or teams looking to try out the platform</div>
                        <div className="mt-5 flex flex-col items-start">
                          <div className='flex items-center'>
                            <span className="text-xl line-through text-[#003654] opacity-50">{isAnnual ? '$26.99' : '$35.09'}</span>
                            <span className='text-xs text-[#003654] opacity-50'>{isAnnual ? '/month' : '/month'}</span>
                          </div>
                          <span className="font-bold text-3xl text-[#003654]">
                            <span className="font-bold text-4xl">{isAnnual ? '$19' : '$25'}</span>
                            <span className="text-sm">{isAnnual ? '.99' : '.99'}</span>
                            <span className="text-lg font-light">{isAnnual ? '/month' : '/month'}</span>
                          </span>
                        </div>
                        <button
                          className={`mt-8 w-full bg-[#003654] text-white py-2 px-6 rounded-xl font-bold ${IsActivePlan == true ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={IsActivePlan === true ? null : () => handlePlanSelection('Starter', isAnnual)}
                          disabled={IsActivePlan === true} // Adding the disabled attribute for better accessibility
                        >
                          Choose This Plan
                        </button>
                      </th>

                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Professional</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">For teams looking to automate time-draining tasks.</div>
                        <div className="mt-5 flex flex-col items-start">
                          <div className='flex items-center'>
                            <span className="text-xl line-through text-[#003654] opacity-50">{isAnnual ? '$269.99' : '$350.99'}</span>
                            <span className='text-xs text-[#003654] opacity-50'>{isAnnual ? '/month' : '/month'}</span>
                          </div>
                          <span className="font-bold text-3xl text-[#003654]">
                            <span className="font-bold text-4xl">{isAnnual ? '$199' : '$259'}</span>
                            <span className="text-sm">{isAnnual ? '.99' : '.99'}</span>
                            <span className="text-lg font-light">{isAnnual ? '/month' : '/month'}</span>
                          </span>
                        </div>
                        <button
                          className={`mt-8 w-full bg-[#003654] text-white py-2 px-6 rounded-xl font-bold ${IsActivePlan == true ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={IsActivePlan === true ? null : () => handlePlanSelection('Professional', isAnnual)}
                          disabled={IsActivePlan === true} // Adding the disabled attribute for better accessibility
                        >
                          Choose This Plan
                        </button>
                      </th>
                      <th className="py-4 px-6 text-start align-top border-b border-t border-l border-r border-[#E6E9F5] w-1/5">
                        <p className='text-3xl'>Business</p>
                        <div className="mt-3 font-light text-sm text-[#646464]">For businesses looking for custom workflows to automate business processes for measurable ROl</div>
                        <div className="mt-5 flex flex-col items-start">
                          <span className="font-bold text-3xl text-[#003654]">
                            <span className="font-bold text-4xl">Custom</span>
                          </span>
                        </div>
                        <button
                          className={`mt-10 w-full bg-[#003654] text-white py-2 px-6 rounded-xl font-bold ${IsActivePlan == true ? 'opacity-50 cursor-not-allowed' : ''}`}
                          onClick={IsActivePlan === true ? null : () => handleContactUsQuery()}
                          disabled={IsActivePlan === true} // Adding the disabled attribute for better accessibility
                        >
                          Talk to Sales
                        </button>
                      </th>
                    </tr>
                  </thead>
                  <tbody>
                    {pricingRows.map((row, index) => (
                      <tr key={index} className="border-t">
                        <td
                          className="py-2 px-6 border-l border-r border-b border-[#E6E9F5] text-center text-[#003654]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "17.17px",
                            fontWeight: 600,
                            lineHeight: '24.8px'
                          }}
                        >
                          {row.feature}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.starter}
                          {index < 2 && <div className="text-[#858BA0] text-sm font-light">Pages Add-ons on Demand</div>}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.professional}
                          {index < 2 && <div className="text-[#858BA0] text-sm font-light">Pages Add-ons on Demand</div>}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5] text-[#003654] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.bussiness}
                          {index < 2 && <div className="text-[#858BA0] text-sm font-light">Pages Add-ons on Demand</div>}
                        </td>
                      </tr>
                    ))}
                    {additionalRows.map((row, index) => (
                      <tr key={`additional-${index}`} className="border-t text-[#003654]">
                        <td
                          className="py-2 px-6 border-r text-center border-l border-b border-[#E6E9F5] font-semibold"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                          }}
                        >
                          {row.feature}
                        </td>

                        <td
                          className="py-2 px-6 text-center  border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.starter}
                        </td>
                        <td
                          className="py-2 px-6 text-center  border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.professional}
                        </td>
                        <td
                          className="py-2 px-6 text-center border-r border-b border-[#E6E9F5]"
                          style={{
                            width: '297.45px',
                            height: '76.32px',
                            padding: '19.08px 0',
                            gap: '3.82px',
                            opacity: 1,
                            fontSize: "13.36px",
                            fontWeight: 600,
                            lineHeight: '19.08px'
                          }}
                        >
                          {row.bussiness}
                        </td>
                      </tr>
                    ))}
                  </tbody>
                </table>
              </div>
            </div>
          </section>
        </main>
      </div>
    </>
  );
};

export default UpgradePlanPage;



