import {
  Box,
  Button,
  Flex,
  Table,
  Text,
  Thead,
  Tr,
  Tbody,
  Td,
  Image,
} from "@chakra-ui/react";
import { useState } from "react";
import RPAIcon from "../assets/RPAIcon.svg";
import SectionTitle from "../components/Home/SectionTitle";

const tick = (
  <svg
    width="19"
    height="19"
    viewBox="0 0 19 19"
    fill="none"
    xmlns="http://www.w3.org/2000/svg"
  >
    <path
      fillRule="evenodd"
      clipRule="evenodd"
      d="M11.1125 0.905959C10.1812 0.119888 8.8188 0.119889 7.88749 0.905959L7.03932 1.62185C6.64349 1.95595 6.15365 2.15886 5.6375 2.20251L4.53155 2.29603C3.31717 2.39873 2.35381 3.3621 2.25111 4.57647L2.15759 5.68242C2.11394 6.19857 1.91102 6.68841 1.57692 7.08426L0.861037 7.93241C0.0749661 8.86372 0.0749673 10.2261 0.861037 11.1574L1.57692 12.0056C1.91102 12.4014 2.11394 12.8913 2.15759 13.4074L2.25111 14.5134C2.35381 15.7278 3.31717 16.6912 4.53155 16.7938L5.6375 16.8873C6.15365 16.931 6.64349 17.1339 7.03934 17.468L7.88749 18.1839C8.8188 18.9699 10.1812 18.9699 11.1125 18.1839L11.9607 17.468C12.3565 17.1339 12.8464 16.931 13.3625 16.8873L14.4685 16.7938C15.6829 16.6912 16.6462 15.7278 16.7489 14.5134L16.8424 13.4074C16.8861 12.8913 17.089 12.4014 17.4231 12.0056L18.139 11.1574C18.925 10.2261 18.925 8.86372 18.139 7.93241L17.4231 7.08425C17.089 6.68841 16.8861 6.19857 16.8424 5.68242L16.7489 4.57647C16.6462 3.3621 15.6829 2.39873 14.4685 2.29603L13.3625 2.20251C12.8464 2.15886 12.3565 1.95595 11.9607 1.62185L11.1125 0.905959ZM14.046 7.84047C14.4854 7.40113 14.4854 6.68882 14.046 6.24948C13.6067 5.81013 12.8944 5.81013 12.455 6.24948L8.25054 10.454L6.54604 8.74948C6.1067 8.31013 5.39439 8.31013 4.95505 8.74948C4.5157 9.18882 4.5157 9.90113 4.95505 10.3405L7.45505 12.8404C7.89439 13.2798 8.6067 13.2798 9.04604 12.8404L14.046 7.84047Z"
      fill="#003654"
    />
  </svg>
);

const PLANS_DATA = [
  {
    id: 2,
    planName: "Starter",
    tagline: "For individuals or teams looking to try out the platform",
    oldPrice: "49",
    duration: "month",
    newPrice: "19.99",
    monthlyOldPrice: "49",
    monthlyNewPrice: "19.99",
    annualOldPrice: "588",
    annualNewPrice: "228.99",
    isCustom: false,
  },
  {
    id: 3,
    planName: "Professional",
    tagline: "For teams looking to automate time-draining tasks",
    oldPrice: "499",
    duration: "month",
    newPrice: "199.99",
    monthlyOldPrice: "499",
    monthlyNewPrice: "199.99",
    annualOldPrice: "5988",
    annualNewPrice: "2388.99",
    isCustom: false,
  },
  {
    id: 4,
    planName: "Business",
    tagline:
      "For businesses looking for custom workflows to automate business processes for measurable ROl",
    oldPrice: "",
    duration: "",
    newPrice: "",
    monthlyOldPrice: "",
    monthlyNewPrice: "",
    annualOldPrice: "",
    annualNewPrice: "",
    isCustom: true,
  },
];

const TickBox = () => {
  return (
    <Box display={"flex"} alignItems={"center"} justifyContent={"center"}>
      {tick}
    </Box>
  );
};
const RPABox = () => {
  return (
    <Box
      padding={"10px"}
      textAlign={"left"}
      backgroundColor={"#003654E3"}
      borderRadius={"8px"}
    >
      <Box
        display={"flex"}
        alignItems={"flex-start"}
        justifyContent={"space-between"}
      >
        <Text
          fontSize={"14px"}
          fontWeight={500}
          textAlign={"start"}
          color={"#ffffff"}
        >
          Automate your Tasks Seamlessly with Robotic Process Automation
        </Text>
        <Image minW={"50px"} boxSize={"50px"} src={RPAIcon}></Image>
      </Box>
      <Button
        mt={"10px"}
        w="45%"
        bg="#ffffff"
        color="#003654E3"
        height={"28px"}
        fontWeight={500}
        fontSize={"10px"}
        borderRadius={"6px"}
      >
        Add
      </Button>
    </Box>
  );
};
// eslint-disable-next-line react/prop-types
const CustomTableBox = ({ item, textAlign }) => {
  return (
    <Td
      height={"76px"}
      px={6}
      textAlign={textAlign ? textAlign : "center"}
      border="1px solid #E6E9F5"
      fontSize={"18px"}
      fontWeight={500}
    >
      {item}
    </Td>
  );
};

const Pricing = () => {
  const [isAnnual, setIsAnnual] = useState(true);
  const [showMore, setShowMore] = useState(false);
  const pricingRows = [
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            Page Limit
          </Text>
        </>
      ),
      starter: (
        <>
          <Text color="#003654" fontWeight={500} fontSize={"14px"}>
            {isAnnual ? "Upto 900 Pages" : "Upto 75 Pages"}
          </Text>
          <Text color="#858BA0" fontSize="sm" fontWeight="light">
            Pages Add-ons on Demand
          </Text>
        </>
      ),
      professional: (
        <>
          <Text color="#003654" fontWeight={500} fontSize={"14px"}>
            {isAnnual ? "Upto 9000 Pages" : "Upto 750 Pages"}
          </Text>
          <Text color="#858BA0" fontSize="sm" fontWeight="light">
            Pages Add-ons on Demand
          </Text>
        </>
      ),
      business: (
        <>
          <Text color="#003654" fontWeight={500} fontSize={"14px"}>
            Custom
          </Text>
        </>
      ),
    },
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            24/7 Customer Support
          </Text>
        </>
      ),
      
      starter: <TickBox />,
      professional: <TickBox />,
      business: <TickBox />,
    },
    {
      feature:
        (
          <Text color="#003654" fontWeight={600}>
            Unlimited Custom Models
          </Text>
        ),
      
      starter: <TickBox />,
      professional: <TickBox />,
      business: <TickBox />,
    },
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            Data Retention
          </Text>
        </>
      ),
      starter: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          {isAnnual ? "Upto 6 months" : "Upto 6 months"}
        </Text>
      ),
      professional: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          {isAnnual ? "Upto 12 months" : "Upto 12 months"}
        </Text>
      ),
      business: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          {isAnnual ? "Custom" : "Custom"}
        </Text>
      ),
    },
    {
      feature: (
        <Text color="#003654" fontWeight={600}>
          Bulk Processing
        </Text>
      ),
      starter: 
      (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Upto 50 Docs
        </Text>
      ),
      professional: 
      (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Upto 500 Docs
        </Text>
      ),
      business: 
      (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Unlimited
        </Text>
      )
    },
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            Priority Processing
          </Text>
        </>
      ),
      starter: "",
      professional: <TickBox />,
      business: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          {isAnnual ? "Custom" : "Custom"}
        </Text>
      ),
    },
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            Dedicated Account Manager
          </Text>
        </>
      ),
      starter: "",
      professional: <TickBox />,
      business: <TickBox />,
    },
    {
      feature: (
        <>
          <Text color="#003654" fontWeight={600}>
            Custom RPA Access
          </Text>
        </>
      ),
      starter: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Add-on Feature - Contact sales
        </Text>
      ),
      professional: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Add-on Feature - Contact sales
        </Text>
      ),
      business: (
        <Text color="#003654" fontWeight={500} fontSize={"14px"}>
          Add-on Feature - Contact sales
        </Text>
      )
      // Remove - Cards in 3 paid plans and add text "Add-on feature - Contact sales"
    },
  ];
  const additionalRows = [
    
    // {
    //   feature: "API Access",
    //   free: "",
    //   starter: "",
    //   professional: "",
    //   business: <TickBox />,
    // },
  ];

  return (
    <>
      <Box
        id="pricing"
        pt={"10px"}
        // px={{ base: "0px", md: "20px", lg: "30px", xl: "40px" }}
        mb={{ base: "0px", md: "40px", lg: "50px", xl: "90px" }}
      >
        <SectionTitle title={"Transparent Pricing for Exceptional Value"} />
        <Flex justify="center" align="center" my={6} mb={"5px"}>
          <Text fontSize={"18px"} fontWeight="medium" color="#575757" mx={2}>
            Monthly
          </Text>
          <Button
            w="14"
            h="7"
            display="flex"
            alignItems="center"
            justifyContent={"flex-start"}
            rounded="full"
            p={1}
            transition="background-color 300ms ease-in-out"
            bg={isAnnual ? "#003654" : "#CCCCCC"}
            onClick={() => setIsAnnual(!isAnnual)}
          >
            <Flex
              bg="#ffffff"
              w="5"
              h="5"
              rounded="full"
              boxShadow="md"
              transform={isAnnual ? "translateX(1.75rem)" : "translateX(0)"}
              transition="transform 300ms ease-in-out"
            />
          </Button>
          <Text fontSize={"18px"} fontWeight="medium" color="#575757" mx={2}>
            Annually
          </Text>
        </Flex>
        <Text
          fontWeight={400}
          textAlign={"center"}
          color={"#676767"}
          mx={"auto"}
          maxW={{ base: "90vw", md: "60vw" }}
          fontSize={{ base: "14px", md: "16px" }}
          lineHeight={{ base: "20px", md: "22px", lg: "24px", xl: "26px" }}
        >
          {"Save Upto 30% on Annual Plans"}
        </Text>

        <Box className="scrollBar" overflowX={"hidden"} mt={"50px"}>
          <Table minW={"900px"}>
            <Thead bg="#FFF" color="#003654">
              <Tr>
                <Td
                  py={4}
                  px={6}
                  border="1px solid #E6E9F5"
                  textAlign="left"
                  width="20%"
                  minW={"250px"}
                >
                  <Text fontWeight={700} color={"#003654"} fontSize={"22px"}>
                    Compare plans
                  </Text>
                  <Text mt={3} fontWeight="light" fontSize="sm" color="#858BA0">
                    Choose your workspace plan according to your organisational
                    need
                  </Text>
                  <Text
                    mt={3}
                    display="inline-block"
                    color="#003654"
                    bg="#FFF"
                    border="1px solid #003654"
                    p={"8px 16px"}
                    rounded="full"
                    fontSize={"14px"}
                    fontWeight={600}
                  >
                    Up to 30% Off on Annual Plans
                  </Text>
                </Td>
                {PLANS_DATA.map((item) => (
                  <Td
                    key={item.id}
                    py={4}
                    px={6}
                    border="1px solid #E6E9F5"
                    textAlign="start"
                    width="19%"
                    minW={"250px"}
                  >
                    <Text fontWeight={700} fontSize={"24px"} color={"#003654"}>
                      {item.planName}
                    </Text>

                    <Text
                      height={"60px"}
                      mt={"8px"}
                      fontSize={"14px"}
                      fontWeight={400}
                      color="#646464"
                    >
                      {item.tagline}
                    </Text>

                    <Box
                      display="flex"
                      flexDirection="column"
                      alignItems="start"
                    >
                      <Box height={"40px"}>
                        <Box display="flex" alignItems="center">
                          <Text
                            fontSize="24px"
                            fontWeight={700}
                            textDecoration="line-through"
                            color="#003654"
                            opacity={0.5}
                            lineHeight={"24px"}
                          >
                            {isAnnual
                              ? item.annualOldPrice !== ""
                                ? "$" + item.annualOldPrice
                                : ""
                              : item.monthlyOldPrice !== ""
                              ? "$" + item.monthlyOldPrice
                              : ""}
                          </Text>
                          <Text fontSize="10px" color="#003654" opacity={0.5}>
                            {isAnnual
                              ? item.annualOldPrice !== ""
                                ? "/year"
                                : ""
                              : item.annualOldPrice !== ""
                              ? "/month"
                              : ""}
                          </Text>
                        </Box>
                      </Box>
                      <Box display="flex" alignItems="flex-end">
                        <Box display="flex" alignItems="flex-start">
                          <Text
                            lineHeight={"38px"}
                            as="span"
                            fontWeight={700}
                            fontSize={"38px"}
                          >
                            {isAnnual
                              ? item.annualNewPrice !== ""
                                ? "$" + item.annualNewPrice.split(".")[0]
                                : "Custom"
                              : item.monthlyNewPrice !== ""
                              ? "$" + item.monthlyNewPrice.split(".")[0]
                              : "Custom"}
                          </Text>
                          <Text as="span" fontSize="sm">
                            {isAnnual
                              ? item.annualNewPrice !== ""
                                ? "." + item.annualNewPrice.split(".")[1]
                                : ""
                              : item.monthlyNewPrice !== ""
                              ? "." + item.monthlyNewPrice.split(".")[1]
                              : ""}
                          </Text>
                        </Box>
                        <Text as="span" fontSize="lg" fontWeight="light">
                          {isAnnual
                            ? item.annualNewPrice !== ""
                              ? "/year"
                              : ""
                            : item.monthlyNewPrice !== ""
                            ? "/month"
                            : ""}
                        </Text>
                      </Box>
                    </Box>

                    {item.isCustom ? (
                      <Button
                        as="a"
                        href={"#StillHaveAQuestion"}
                        mt={"18px"}
                        w="full"
                        bg="#003654"
                        color="white"
                        height={{
                          base: "30px",
                          md: "40px",
                          lg: "50px",
                          xl: "50px",
                        }}
                        px={6}
                        rounded="md"
                      >
                        {"Talk to Sales"}
                      </Button>
                    ) : (
                      <Button
                        mt={"18px"}
                        w="full"
                        bg="#003654"
                        color="white"
                        height={{
                          base: "30px",
                          md: "40px",
                          lg: "50px",
                          xl: "50px",
                        }}
                        px={6}
                        rounded="md"
                        onClick={() =>
                          window.open("https://app.accuvelocity.com/", "_blank")
                        }
                      >
                        Choose This Plan
                      </Button>
                    )}
                  </Td>
                ))}
              </Tr>
            </Thead>
            <Tbody>
              {pricingRows.map((row, index) => (
                <Tr
                  key={index}
                  className={
                    index >= pricingRows.length - 2 && !showMore
                      ? "blur-sm"
                      : ""
                  }
                >
                  <CustomTableBox item={row.feature} textAlign="left" />
                  <CustomTableBox item={row.starter} />
                  <CustomTableBox item={row.professional} />
                  <CustomTableBox item={row.business} />
                </Tr>
              ))}
              {showMore &&
                additionalRows.map((row, index) => (
                  <Tr
                    key={index}
                    className={
                      index >= pricingRows.length - 2 && !showMore
                        ? "blur-sm"
                        : ""
                    }
                  >
                    <CustomTableBox item={row.feature} textAlign="left" />
                    <CustomTableBox item={row.starter} />
                    <CustomTableBox item={row.professional} />
                    <CustomTableBox item={row.business} />
                  </Tr>
                ))}
            </Tbody>
          </Table>
        </Box>

        <Box mt={"50px"} display={"flex"} justifyContent={"center"}>
          <Button
            onClick={() => setShowMore(!showMore)}
            color="#ffffff"
            borderRadius={"20px"}
            backgroundColor={"#003654"}
            height={{
              base: "40px",
              md: "40px",
              lg: "50px",
              xl: "50px",
            }}
          >
            {showMore ? "See less" : "Explore more features"}
          </Button>
        </Box>
      </Box>
    </>
  );
};

export default Pricing;
