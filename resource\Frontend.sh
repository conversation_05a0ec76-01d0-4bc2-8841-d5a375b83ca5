sudo apt-get install nginx -y
sudo systemctl status nginx
npm i
npm run build
sudo chgrp -R www-data /home/<USER>/Desktop/AccuVelocity/AccuVelocity-master/client/dist
sudo find /home/<USER>/Desktop/AccuVelocity/AccuVelocity-master/client/dist -type d -exec chmod 750 {} \;
sudo find /home/<USER>/Desktop/AccuVelocity/AccuVelocity-master/client/dist -type f -exec chmod 640 {} \;
sudo chmod +x /home/<USER>/home/<USER>/Desktop /home/<USER>/Desktop/AccuVelocity /home/<USER>/Desktop/AccuVelocity/AccuVelocity-master /home/<USER>/Desktop/AccuVelocity/AccuVelocity-master/client

sudo nano /etc/nginx/sites-available/app.accuvelocity.com
server {
    listen 443 ssl;
    server_name app.accuvelocity.com;

    root /root/Desktop/AccuVelocity/client/dist; # Path to your dist directory
    index index.html index.htm;

    location / {
        try_files $uri $uri/ /index.html =404;
    }
}

    ssl_certificate /etc/letsencrypt/live/app.accuvelocity.com/fullchain.pem; # managed by Certbot
    ssl_certificate_key /etc/letsencrypt/live/app.accuvelocity.com/privkey.pem; # managed by Certbot
    include /etc/letsencrypt/options-ssl-nginx.conf; # managed by Certbot
    ssl_dhparam /etc/letsencrypt/ssl-dhparams.pem; # managed by Certbot

server {
    if ($host = app.accuvelocity.com) {
        return 301 https://$host$request_uri;
    } # managed by Certbot

    listen 80;
    server_name app.accuvelocity.com;
    return 404; # managed by Certbot
}

sudo ln -s /etc/nginx/sites-available/app.accuvelocity.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

sudo apt-get install certbot python3-certbot-nginx
sudo certbot --nginx -d app.accuvelocity.com
