import logo from '../assets/logoW.svg'; // Adjust the path to your logo

const Header = () => {
  return (
    <header className="bg-[#003654] p-5 sticky top-0 z-20">
      <div className="max-w-7xl mx-auto flex items-center justify-between">
        {/* Logo on the left */}
        <div className="flex items-start justify-start">
          <img src={logo} alt="AccuVelocity" className="h-9" />
        </div>
        
        {/* Navigation links centered */}
        <nav className="hidden smo:block">
          <ul className="flex justify-center items-center space-x-8">
            <li><a href="/solutions" className="text-white hover:text-gray-300 transition-colors">Solutions</a></li>
            <li><a href="/pricing" className="text-white hover:text-gray-300 transition-colors">Pricing</a></li>
            <li><a href="/resources" className="text-white hover:text-gray-300 transition-colors">Resources</a></li>
            <li><a href="/contact" className="text-white hover:text-gray-300 transition-colors">Contact Us</a></li>
          </ul>
        </nav>
        
        {/* Buttons aligned to the right */}
        <div className="flex items-center justify-end space-x-4">
          <button className="text-[#002744] bg-[#ffff] px-6 py-2 rounded-lg transition-colors hover:bg-gray-200">Request a demo</button>
          <button className="bg-[#003654] border text-white px-6 py-2 rounded-lg transition-colors ">Sign up / Login</button>
        </div>
      </div>
    </header>
  );
};


export default Header;
