from fastapi import <PERSON><PERSON><PERSON>, HTTPException
from fastapi.responses import FileResponse
import os
import json
from fastapi import APIRouter
from config.constants import Constants


AdvertisementApi = APIRouter(tags=['Advertisement'],  prefix="/ad")

# Path to the images directory on the server
# IMAGES_DIR = r"H:\AI Data\Advertisements"
IMAGES_DIR = Constants.Advertisement_path

@AdvertisementApi.get("/get_images")
async def get_images():
    """
    Purpose : This method retrieves a list of all image files from the server's image directory.

    Inputs  : None

    Output  : A dictionary containing the list of image filenames with valid image extensions (e.g., .jpg, .jpeg, .png, .gif, .bmp).

    Example : await get_images()
    """
    try:
        # Get the list of all image files from the server's image directory
        image_files = [
            f for f in os.listdir(IMAGES_DIR) 
            if os.path.splitext(f)[1].lower() in ['.jpg', '.jpeg', '.png', '.gif', '.bmp']
        ]
        return {"image_files": image_files}
    except Exception as e:
        raise HTTPException(status_code=500, detail=f"Error fetching images: {str(e)}")


@AdvertisementApi.get("/download_image/{image_name}")
async def download_image(image_name: str):
    """
    Purpose : This method allows the user to download a specific image file from the server.

    Inputs  :   (1) image_name :   The name of the image file to be downloaded.

    Output  : It returns the image file as a downloadable response if the file exists or raises an HTTPException if not found.

    Example : await download_image(image_name="example.jpg")
    """
    image_path = os.path.join(IMAGES_DIR, image_name)
    if os.path.exists(image_path):
        return FileResponse(image_path)
    else:
        raise HTTPException(status_code=404, detail="Image not found")

