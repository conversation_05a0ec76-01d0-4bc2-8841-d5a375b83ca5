{"cells": [{"cell_type": "markdown", "id": "0f8d8305", "metadata": {}, "source": ["### Purpose : to create pricelist excel for PARAG Trader Customer\n", "\n", "### Auther : <PERSON><PERSON>\n", "\n", "### Created Date: 22-04-2025"]}, {"cell_type": "markdown", "id": "2f6fc1a4", "metadata": {}, "source": ["## Generate api_responses.json from MySQL Database:\n", "This notebook block connects to a MySQL database, runs a dynamic SQL query to fetch the latest uploaded document for each document type (based on UploadedDateTime), and exports the results as a JSON file"]}, {"cell_type": "markdown", "id": "6c2cd4c7", "metadata": {}, "source": ["## Import Modules"]}, {"cell_type": "code", "execution_count": 2, "id": "fcabf451", "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "from sqlalchemy import create_engine, text\n", "from urllib.parse import quote_plus\n", "import sys\n", "import asyncio\n", "import json\n", "from pathlib import Path\n", "import os\n", "from src.utilities.helperFunc import CExcelHelper"]}, {"cell_type": "markdown", "id": "385ef704", "metadata": {}, "source": ["# Generate `api_responses.json` from MySQL Database\n", "\n", "This Jupyter Notebook extracts the latest uploaded document for each document type from a MySQL database and exports the data as a JSON file.\n", "\n", "1. **Connect to Database**: Uses SQLAlchemy with secure credentials.\n", "2. **Execute SQL Query**: Fetches the most recent document per `document_type` based on `UploadedDateTime`.\n", "3. **Export Data**: Saves the retrieved records into `api_responses.json`.\n", "\n", "A streamlined approach for structured data retrieval and storage!\n"]}, {"cell_type": "code", "execution_count": 6, "id": "80e9325c", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Export complete: 111 records written to 'GitIgnore/api_responses.json'\n"]}], "source": ["# === PARAMETERS ===\n", "MODEL_ID   = None       # e.g. 42 or None\n", "USER_ID    = 2          # mandatory\n", "START_DATE = '2025-04-01 00:00:00'\n", "END_DATE   = '2025-04-24 00:00:00'\n", "\n", "# === DATABASE CREDENTIALS ===\n", "DB_USER     = 'root'\n", "DB_PASSWORD = 'real@123#'\n", "DB_HOST     = 'localhost'\n", "DB_PORT     = 3306\n", "DB_NAME     = 'DBVelocity'\n", "\n", "\n", "encoded_password = quote_plus(DB_PASSWORD)\n", "connection_url   = (\n", "    f\"mysql+pymysql://{DB_USER}:{encoded_password}@\"\n", "    f\"{DB_HOST}:{DB_PORT}/{DB_NAME}\"\n", ")\n", "engine = create_engine(connection_url)\n", "\n", "# === DYNAMIC SQL QUERY ===\n", "query = \"\"\"\n", "WITH RankedData AS (\n", "    SELECT \n", "        ded.Name,\n", "        ud.<PERSON>,\n", "        ud.DocId,\n", "        ROW_NUMBER() OVER (\n", "            PARTITION BY ud.DocName \n", "            ORDER BY ud.UploadedDateTime DESC\n", "        ) AS RowNum\n", "    FROM \n", "        modeltable ded\n", "    JOIN \n", "        uploaded_docs ud\n", "    ON \n", "        ded.Id = ud.ModelId\n", "    WHERE \n", "        ud.UploadedDateTime >= :start_date \n", "        AND ud.UploadedDateTime <= :end_date \n", "        AND ud.DocExtractionAPIStatusCode = 200 \n", "        AND ud.UserId = :user_id\n", "        AND (ud.ModelId = :model_id OR :model_id IS NULL)\n", ")\n", "SELECT \n", "    <PERSON><PERSON><PERSON>, DocId, Name\n", "FROM \n", "    RankedData\n", "WHERE \n", "    RowNum = 1;\n", "\"\"\"\n", "\n", "# === SET PARAMETERS ===\n", "params = {\n", "    'user_id':    USER_ID,    # Integer, e.g., 2\n", "    'start_date': START_DATE, # String, e.g., '2025-02-01 00:00:00'\n", "    'end_date':   <PERSON><PERSON>_DATE,   # String, e.g., '2025-03-31 00:00:00'\n", "    'model_id':   MODEL_ID    # Integer or None\n", "}\n", "\n", "# === EXECUTE AND DUMP TO JSON ===\n", "with engine.connect() as conn:\n", "    df = pd.read_sql_query(text(query), conn, params=params)\n", "\n", "output_file = 'GitIgnore/api_responses.json'\n", "df.to_json(output_file, orient='records', force_ascii=False, date_format='iso')\n", "\n", "print(f\"Export complete: {len(df)} records written to '{output_file}'\")"]}, {"cell_type": "markdown", "id": "192157d1", "metadata": {}, "source": ["# Generate Price List Reports from JSON Data\n", "\n", "- Loads JSON data from a specified file.\n", "- Locates and imports the helper module `CTallyHelper`.\n", "- Prepares an output directory for storing reports.\n", "- Executes an asynchronous function to generate price list reports.\n", "- Handles errors efficiently and confirms successful report generation."]}, {"cell_type": "code", "execution_count": 8, "id": "b0b62851", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["No Pricelist found for the given vendor. - PowerGrace\n", "Saving Pricing List Report at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Aquant_doc_3729_pricelist_report.xlsx\n", "Pricing list report successfully updated at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Aquant_doc_3729_pricelist_report.xlsx\n", "Saving Pricing List Report at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Geberit_doc_3990_pricelist_report.xlsx\n", "Pricing list report successfully updated at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Geberit_doc_3990_pricelist_report.xlsx\n", "Saving Pricing List Report at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Toto_doc_3920_pricelist_report.xlsx\n", "Pricing list report successfully updated at location: C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\\Toto_doc_3920_pricelist_report.xlsx\n", "Price list reports generated successfully for model 'Toto' and document.\n", "Price‐list reports generated successfully in WindowsPath('C:/Users/<USER>/Sanket Gohil/Customers/Real/Accuvelocity/AccuVelocity/GitIgnore/ParagTraders Price List/April Pricelist')\n"]}], "source": ["# 1. Define your absolute file paths as input parameters.\n", "json_file_path = Path(r\"C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\api_responses.json\")\n", "output_dir_path = Path(r\"C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\")\n", "\n", "\n", "# 2. Ensure Python can locate src/utilities/TallyHelper.py relative to your notebook root.\n", "notebook_root = Path().resolve()\n", "tally_helper_path = notebook_root / \"src\" / \"utilities\"\n", "if str(tally_helper_path) not in sys.path:\n", "    sys.path.append(str(tally_helper_path))\n", "\n", "# 3. Import your helper module.\n", "from src.utilities.TallyHelper import CTallyHelper\n", "\n", "# 4. Load the JSON using the absolute file path.\n", "try:\n", "    with json_file_path.open(\"r\", encoding=\"utf-8\") as f:\n", "        lsDocInfo = json.load(f)\n", "except FileNotFoundError:\n", "    print(f\"Error: The JSON file was not found at {json_file_path}\")\n", "    lsDocInfo = None\n", "except json.JSONDecodeError as je:\n", "    print(f\"Error decoding JSON from {json_file_path}: {je}\")\n", "    lsDocInfo = None\n", "except Exception as e:\n", "    print(f\"An unexpected error occurred while loading the JSON file: {e}\")\n", "    lsDocInfo = None\n", "\n", "# 5. Proceed only if the JSON was successfully loaded.\n", "if lsDocInfo is not None:\n", "    # Prepare the output directory.\n", "    try:\n", "        output_dir_path.mkdir(parents=True, exist_ok=True)\n", "    except Exception as e:\n", "        print(f\"An error occurred while creating the output directory at {output_dir_path}: {e}\")\n", "        output_dir_path = None\n", "\n", "    # Ensure the output directory was created.\n", "    if output_dir_path is not None:\n", "        # Define the user ID.\n", "        USER_ID = 2\n", "\n", "        # 6. Call the asynchronous function with error handling.\n", "        try:\n", "            asyncio.run(\n", "                CTallyHelper.MSCreatePriceListReportFromDocIds(\n", "                    iUserID=USER_ID,\n", "                    json_data=lsDocInfo,\n", "                    strReportDirPath=str(output_dir_path)\n", "                )\n", "            )\n", "        except Exception as e:\n", "            print(f\"An error occurred during price‐list report generation: {e}\")\n", "        else:\n", "            print(f\"Price‐list reports generated successfully in {output_dir_path!r}\")\n"]}, {"cell_type": "markdown", "id": "fe0f9f64", "metadata": {}, "source": ["# Merge Excel Files and Apply Formatting\n", "\n", "This notebook merges multiple `.xlsx` files from a specified directory into a single Excel file while maintaining two sheets (`Tiles` and `Sanitary`).\n", "\n", "1. **Collect Data**: Reads all `.xlsx` files in the directory, extracting data from two sheets.\n", "2. **Merge Data**: Combines content across sheets while preserving structure.\n", "3. **Save Merged File**: Writes the consolidated data into a new Excel file.\n", "4. **Apply Formatting**: Uses `CExcelHelper.MSFormatExcel` to adjust column widths and apply word wrapping.\n", "5. **Completion Check**: Confirms successful file creation and formatting.\n", "\n", "This ensures efficient data integration and standardized output for price list reports.\n"]}, {"cell_type": "code", "execution_count": 5, "id": "f499072b", "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Merged file saved as: GitIgnore\\\\Price-List-Version-V4 1-4-2025 to 24-4-25 Merged Price List for Your Received Documents.xlsx\n"]}], "source": ["directory = r\"C:\\Users\\<USER>\\Sanket Gohil\\Customers\\Real\\Accuvelocity\\AccuVelocity\\GitIgnore\\ParagTraders Price List\\April Pricelist\"\n", "output_file = r\"GitIgnore\\\\Price-List-Version-V4 1-4-2025 to 24-4-25 Merged Price List for Your Received Documents.xlsx\"\n", "\n", "def merge_excel_files(directory, output_file):\n", "    \"\"\"\n", "    Merges all `.xlsx` files in a directory into a single Excel file.\n", "    The files must contain two sheets, and the data is appended sheet-wise.\n", "    \n", "    Args:\n", "        directory (str): Path to the directory containing the `.xlsx` files.\n", "        output_file (str): Path to save the merged Excel file.\n", "    \"\"\"\n", "    # Initialize lists to collect data for each sheet\n", "    sheet1_data = []\n", "    sheet2_data = []\n", "    \n", "    # Iterate over all files in the directory\n", "    for filename in os.listdir(directory):\n", "        if filename.endswith(\".xlsx\"):\n", "            file_path = os.path.join(directory, filename)\n", "            try:\n", "                # Read the Excel file\n", "                xls = pd.ExcelFile(file_path)\n", "                # Append data from each sheet to the corresponding list\n", "                sheet1_data.append(pd.read_excel(xls, sheet_name=xls.sheet_names[0]))\n", "                sheet2_data.append(pd.read_excel(xls, sheet_name=xls.sheet_names[1]))\n", "            except Exception as e:\n", "                print(f\"Error processing file {filename}: {e}\")\n", "    \n", "    # Concatenate the data from all files for each sheet\n", "    merged_sheet1 = pd.concat(sheet1_data, ignore_index=True)\n", "    merged_sheet2 = pd.concat(sheet2_data, ignore_index=True)\n", "    \n", "    # Write the merged data into a new Excel file\n", "    with pd.ExcelWriter(output_file) as writer:\n", "        merged_sheet1.to_excel(writer, sheet_name=\"Tiles\", index=False)\n", "        merged_sheet2.to_excel(writer, sheet_name=\"Sanitary\", index=False)\n", "    \n", "    # Apply formatting to the merged Excel file\n", "    CExcelHelper.MSFormatExcel(\n", "        bIsSheetObject=False,\n", "        sExcelFilePath=output_file,\n", "        lsWordWrapColumns=['Item Name', \"Matched PricelistItem Property\", \"Accuvelocity Comments\"],\n", "        column_width_mapping={\n", "            \"Matched PricelistItem Property\": 45,\n", "            'Item Name': 45,\n", "            'Accuvelocity Comments': 90\n", "        },\n", "        lsSheetNames=[\"Tiles\", \"Sanitary\"],\n", "        auto_adjust_width=True\n", "    )\n", "    print(f\"Merged file saved as: {output_file}\")\n", "\n", "merge_excel_files(directory, output_file)"]}], "metadata": {"kernelspec": {"display_name": "5_Env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.13.2"}}, "nbformat": 4, "nbformat_minor": 5}