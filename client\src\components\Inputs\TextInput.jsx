/* eslint-disable react/prop-types */
import { Box, FormLabel, Input, InputGroup } from "@chakra-ui/react";

const TextInput = (props) => {
  return (
    <Box w={"100%"}>
      <InputGroup display={"flex"} flexDir={"column"}>
        {props.label && <FormLabel fontWeight={400}>{props.label}</FormLabel>}
        <Input
          type="text"
          fontWeight={500}
          borderBottom={"2px solid #CDCDCD"}
          _focusVisible={{ borderBottom: "2px solid #003654" }}
          borderRadius={"0px"}
          height={"44px"}
          {...props}
        ></Input>
      </InputGroup>
    </Box>
  );
};

export default TextInput;
