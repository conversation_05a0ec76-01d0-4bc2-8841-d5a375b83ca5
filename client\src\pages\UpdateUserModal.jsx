import { useState } from "react";
import axios from 'axios';
import toast, { Toaster } from 'react-hot-toast';
import PropTypes from 'prop-types';
import countryCodes from '../assets/phoneCountries.json'

export default function UpdateUserModal({ isOpen, onClose, user, roles }) {

    const [userData, setUserData] = useState({
        id: user.uid,
        name: user.name,
        email: user.email,
        role: user.role,
        phoneNumber: user.phoneNumber,
        country:  user.Country,
        usePaidOCR: user.usePaidOCR === "Yes",
        usePaidDocExtractor: user.usePaidDocExtractor === "Yes",
        apiUsage: user.api_requested,
        usedTokens: user.used_tokens,
        pageLimitLeft: user.page_limit_left,
        pageLimit: user.total_allowed_page_limit,
        standardPageLimitUsage: user.free_page_limit_usage,
        standardPageLimit: user.total_allowed_free_page_limit,
        promoCodeStr: user.promocodeUsage && user.promocodeUsage.length > 0 ? user.promocodeUsage[0].promo_code_name : ''
    });

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;

        setUserData(prev => ({
            ...prev,
            [name]: type === 'checkbox' ? checked : value
        }));
    };

    const handleCountryChange = (e) => {
        setUserData((prevUserData) => ({
            ...prevUserData,
            country: e.target.value
        }));
    };

    const handleSubmit = async () => {

        if (userData.pageLimit < (user.total_allowed_page_limit -  user.page_limit_left)) {
            toast.error("Total PRO Page limit cannot be less than PRO Page limit usage.");
            return;
        }

        if (userData.standardPageLimit < user.free_page_limit_usage) {
            toast.error("Total Standard Page limit cannot be less than Standard Page limit usage.");
            return;
        }
        
        const apiUrl = `${import.meta.env.VITE_SERVER}/update/user/${userData.id}`;
        try {

            const formData = new FormData()
            formData.append("email", userData.email)
            formData.append("name", userData.name)
            formData.append("Country", userData.country || '')
            formData.append("phoneNumber", userData.phoneNumber || '')
            formData.append("rolename", userData.role)
            
            formData.append("usePaidOCR", userData.usePaidOCR )
            formData.append("usePaidDocExtractor", userData.usePaidDocExtractor)
            formData.append("used_tokens", userData.usedTokens)
            formData.append("api_requested", userData.apiUsage)
            formData.append("pro_page_limit", userData.pageLimit)
            formData.append("standard_page_limit", userData.standardPageLimit)
            formData.append("promoCodeStr", userData.promoCodeStr)

            const response = await axios.put(apiUrl, formData, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                toast.success("User Details Updated successfully!");
            }
            else {  
                toast.error("Failed to Update User Details.");
            }
            
            setTimeout(() => {
                onClose(); // Close modal after successful submission
                window.location.reload();    // Refresh the page
            }, 2000); // 2000 milliseconds = 2 seconds
            
        } catch (error) {
            setUserData({ // Reset userData state after update
                id: user.uid,
                name: user.name,
                email: user.email,
                role: user.role,
                phoneNumber: user.phoneNumber,
                country:  user.Country,
                usePaidOCR: user.usePaidOCR,
                usePaidDocExtractor: user.usePaidDocExtractor,
                apiUsage: user.api_requested,
                usedTokens: user.used_tokens,
                pageLimitLeft: user.page_limit_left,
                pageLimit: user.total_allowed_page_limit,
                standardPageLimitUsage: user.free_page_limit_usage,
                standardPageLimit: user.total_allowed_free_page_limit,
                promoCodeStr: user.promocodeUsage && user.promocodeUsage.length > 0 ? user.promocodeUsage[0].promo_code_name : ''
            });
            toast.error(error.response.data.detail);
            
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center  bg-opacity-90 backdrop-brightness-90 backdrop-blur-xs transition-opacity">
            <Toaster position="top-center"></Toaster>
            <div className="relative mx-auto w-full max-w-md rounded-xl bg-[#ffff] p-6 ">
                <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between">
                        <h4 className="text-2xl font-semibold text-gray-900">Update User</h4>
                        <button
                            onClick={onClose}
                            className="rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 transition-color focus:outline-none focus:ring focus:ring-blue-500"
                        >
                            Close
                        </button>
                    </div>
                    <label className="block">
                        Name:
                        <input
                            type="text"
                            name="name"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                            value={userData.name}
                            onChange={handleInputChange}
                        />
                    </label>
                    <label className="block">
                        Email:
                        <input
                            type="email"
                            name="email"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                            value={userData.email}
                            onChange={handleInputChange}
                        />
                    </label>
                    <label className="block">
                        Role:
                        <select
                            name="role"
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                            value={userData.role}
                            onChange={handleInputChange}
                        >
                            {roles.map((role, index) => (
                                    <option key={index} value={role.RoleName}>{role.RoleName}</option>
                            ))}
                        </select>
                    </label>
                    <div className="flex">
                        
                        <label className="place-content-start">
                            Country:
                            <div className="">
                                
                                <select
                                    id="country"
                                    defaultValue={userData.country}
                                    onChange={handleCountryChange}
                                    className="w-[12vh] mr-2 flex-1 px-3 py-2 border rounded-md focus:outline-none focus:ring focus:border-blue-300"
                                >
                                    {countryCodes.map(country => (
                                        <option key={country.name} value={country.name}>{country.dial_code} {country.name}</option>
                                    ))}
                                </select>
                            </div>
                        </label>

                        <label className="">
                            Phone No.:
                            <input
                                type="tel"
                                name="phoneNumber"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                value={userData.phoneNumber}
                                onChange={handleInputChange}
                            />
                        </label>
                    </div>
                    
                    <label className="block">
                        API Usage:
                        <input
                            disabled="true"
                            type="text"
                            name="apiUsage"
                            className="cursor-not-allowed w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 bg-gray-200 focus:border-blue-500 focus:outline-none"
                            value={userData.apiUsage}
                            onChange={handleInputChange}
                        />
                    </label>
                    <label className="block">
                        Used Tokens:
                        <input
                            disabled="true"
                            type="text"
                            name="usedTokens"
                            className="cursor-not-allowed w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 bg-gray-200 focus:border-blue-500 focus:outline-none"
                            value={userData.usedTokens}
                            onChange={handleInputChange}
                        />
                    </label>
                    <div className="flex"> 
                        <label className="mr-2">
                            PRO Page Limit Usage:
                            <input
                                disabled="true"
                                type="text"
                                name="pageLimitLeft"
                                className="cursor-not-allowed w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 bg-gray-200 focus:border-blue-500 focus:outline-none"
                                value={user.total_allowed_page_limit - user.page_limit_left}
                                onChange={handleInputChange}
                            />
                        </label>

                        <label className="">
                            Total PRO Page Limit:
                            <input
                                type="number"
                                name="pageLimit"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                value={userData.pageLimit}
                                onChange={handleInputChange}
                            />
                        </label>
                    </div>
                    
                    <div className="flex"> 
                        <label className="mr-2">
                            Standard Page Limit Usage:
                            <input
                                disabled="true"
                                type="text"
                                name="standardPageLimitUsage"
                                className="cursor-not-allowed w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 bg-gray-200 focus:border-blue-500 focus:outline-none"
                                value={userData.standardPageLimitUsage}
                                onChange={handleInputChange}
                            />
                        </label>

                        <label className="">
                            Total Standard Page Limit:
                            <input
                                type="number"
                                name="standardPageLimit"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                value={userData.standardPageLimit}
                                onChange={handleInputChange}
                            />
                        </label>
                    </div>

                    <label className="block">Promo Code</label>
                    <input
                        type="text"
                        name="promoCodeStr"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        placeholder="Promo Code"
                        value={userData.promoCodeStr}
                        onChange={handleInputChange}
                    />
                    
                    <div className="flex justify-end mt-4">
                        <button
                            onClick={handleSubmit}
                            className={`rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 w-full transition-color focus:outline-none focus:ring focus:ring-blue-500 ${!(userData.email && userData.name) && 'opacity-50 cursor-not-allowed'}`}
                            disabled={!(userData.email && userData.name)}
                        >
                            Update
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

UpdateUserModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    user: PropTypes.object.isRequired,
    roles: PropTypes.array.isRequired,
};
