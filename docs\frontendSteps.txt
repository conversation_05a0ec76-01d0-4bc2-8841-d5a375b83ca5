/home/<USER>/Desktop/AccuVelocity/client

sudo chgrp -R www-data /home/<USER>/AV_WebApp_IN
sudo find /home/<USER>/AV_WebApp_IN/dist -type d -exec chmod 750 {} \;
sudo find /home/<USER>/AV_WebApp_IN/dist -type f -exec chmod 640 {} \;
sudo chmod +x /home/<USER>/home/<USER>/AV_WebApp_IN /home/<USER>/AV_WebApp_IN/dist
sudo nano /etc/nginx/sites-available/portal.accuvelocity.com

sudo chgrp -R www-data /home/<USER>/Desktop/AccuVelocity/client/dist
sudo find /home/<USER>/Desktop/AccuVelocity/client/dist -type d -exec chmod 750 {} \;
sudo find /home/<USER>/Desktop/AccuVelocity/client/dist -type f -exec chmod 640 {} \;
sudo chmod +x /home/<USER>/home/<USER>/Desktop /home/<USER>/Desktop/AccuVelocity /home/<USER>/Desktop/AccuVelocity/client /home/<USER>/Desktop/AccuVelocity/client/dist
sudo nano /etc/nginx/sites-available/app.accuvelocity.com
server {
    listen 80;
    server_name portal.accuvelocity.com;

    root /home/<USER>/AV_WebApp_IN/dist;
    index index.html;

    location / {
        try_files $uri $uri/ /index.html;
    }

    # Handle Gzip Compression
    gzip on;
    gzip_vary on;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

    # Security Headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "no-referrer-when-downgrade" always;
    add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Disable logging for favicon
    location = /favicon.ico {
        log_not_found off;
        access_log off;
    }

    # Disable logging for robots.txt
    location = /robots.txt {
        log_not_found off;
        access_log off;
    }
}

sudo ln -s /etc/nginx/sites-available/portal.accuvelocity.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

sudo certbot --nginx -d portal.accuvelocity.com