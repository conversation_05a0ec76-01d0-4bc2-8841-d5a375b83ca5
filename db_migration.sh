#!/bin/bash

# Debugging: Print the current directory
echo "Current directory: $(pwd)"

# Activate the virtual environment
echo "Activating virtual environment..."
source ./5_Env/bin/activate

# Debugging: Check the Python executable in the virtual environment
echo "Python executable:"
PYTHON_EXECUTABLE=./5_Env/bin/python
echo "$PYTHON_EXECUTABLE"
$PYTHON_EXECUTABLE --version

# Run the database migration script
echo "Running database migration script..."
$PYTHON_EXECUTABLE ./config/db_migration.py

echo "Database migration completed."
read -p "Press [Enter] key to continue..."
