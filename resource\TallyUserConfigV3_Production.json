{"<EMAIL>": {"metadata": {"provider": "zoho", "password": "BGMmjqSCjVPY"}, "clients": {"Parag Traders": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "Parag Traders", "dataDirectory": "H:\\AI Data\\DailyData\\ParagTraders", "userId": 2, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "company_name": "ParagTraders", "bank_name": "boi_1"}, "AV Dev": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "AV Dev", "dataDirectory": "H:\\AI Data\\DailyData\\DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "company_name": "airen_construction", "bank_name": "boi_1"}, "ICD": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "ICD", "dataDirectory": "H:\\AI Data\\DailyData\\ICD", "userId": 5, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "company_name": "ICD"}, "Gwalia": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "Gwalia", "dataDirectory": "H:\\AI Data\\DailyData\\Gwalia", "userId": 7, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "company_name": "gwalia", "bank_name": "BOB"}, "Prem Textiles": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "Prem Textiles", "dataDirectory": "H:\\AI Data\\DailyData\\PremTextiles", "userId": 9, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "company_name": "Prem Textiles"}, "Vedansh School": {"senderList": [], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "Vedansh School", "dataDirectory": "H:\\AI Data\\DailyData\\VedanshSchool", "userId": 10, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "company_name": "test_vis", "bank_name": "NO NEED"}, "Abhinav Infrabuild": {"senderList": [], "attachmentsTypes": ["pdf", "jpg", "png", "xls", "xlsx", "zip", "rar", "7z", "xml", "csv"], "customerName": "<PERSON><PERSON><PERSON><PERSON>frabuild", "dataDirectory": "H:\\AI Data\\DailyData\\AbhinavInfrabuild", "userId": 11, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "company_name": "test_abhinavInfra", "bank_name": "ICICI"}}}}