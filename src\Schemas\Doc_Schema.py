from pydantic import BaseModel
from typing import Optional, Union
from typing import Any
from fastapi import APIRouter, Depends, UploadFile

class GetAllDocFilterQuery(BaseModel):
    bFileNameAsc : Optional[bool] = None
    bUploadDateAsc : Optional[bool] = None
    strDocStatus : Optional[str] = None 
    strSearchInTable : Optional[str] = None
    strStartdate : Optional[str] = None
    strEnddate : Optional[str] = None
    bDateAsc: Optional[bool] = None
    bStatusAsc: Optional[bool] = None
    bDocTypeAsc: Optional[bool] = None
    bModelNameAsc: Optional[bool] = None
    iUserId : Optional[Union[int, str]] = None
    bUserIdAsc:  Optional[bool] = None


class AdditionalDocDetails(BaseModel):
    strICDPegasusExcelFile: Optional[str] = None

class FileSchema(BaseModel):
    file: Any  # This should be a file-like object (BinaryIO)
    filename: str
    content_type: str
    data:Any
    HashCode: str
    filePath:str=""
    DocVendorName: str = ""
    FileType: str = "pdf"
    strClientREQID: str = ""
    PageCount: int= 0

    async def read(self):
        return self.data