from fastapi import HTTPException
import json
import traceback
import asyncio
import time
import tik<PERSON>en
import openai
from openai import OpenAI
from ensure import ensure_annotations
import sys
sys.path.append(".")
from src.utilities.helperFunc import saveGPTresponse
from openai._exceptions import RateLimitError , APITimeoutError
from src.utilities.PromptBuilder import MakeModelGPTPrompt
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.Controllers.prompt_controller import CPromptController
from src.Controllers.DocumentData_controller import CDocumentData
from src.utilities.helperFunc import CExtractionHelper
from dotenv import load_dotenv
import socket
from pathlib import Path
import platform
import os

# Load environment variables from the .env file
load_dotenv()

# Access the AVOPENAI_API_KEY environment variable
avopenai_api_key = os.getenv('AVOPENAI_API_KEY')

# Use the API key as needed in your application
print(avopenai_api_key)


class CGPTAPIResponse:
    client = OpenAI(api_key = avopenai_api_key)
    # no of pages allow using GPT API
    iNoOfPagesAllowed = 4
    
    def __init__(self, 
                 document_id:int, 
                 UserData: dict, 
                 docExtractedTxt: 
                 str, iDocPageCount: int, 
                 strGPTConfigPath=Path(r"resource/GPTConfig.json"), 
                 bDebug=False, 
                 bReasoningEnabled=False,
                 strVendorName = None,
                 strClientName = None,
                 strVoucherType = None,
                 isDevelopmentMode = True):
        
        self.UserData = UserData
        self.docExtractedTxt = docExtractedTxt
        self.iDocPageCount = iDocPageCount
        self.strGPTConfigPath = strGPTConfigPath
        self.bDebug = bDebug
        self.document_id = document_id
        # Pre-initialize properties to handle later in async method
        self.strDocErrorMsg, self.strDocDebugMsg, self.DocErrorCode = "", "", None
        self.DocValidationStatus = True
        self.bReasoningEnabled=bReasoningEnabled
        
        # Open AI Logging
        self.strProjectName = "AccuVelocity"
        self.strResponsibleDeveloper = "Mitul Solanki"
        self.strHostname = socket.gethostname()
        self.strPlatform = platform.platform()
        self.strIPAddress = socket.gethostbyname(self.strHostname)
        self.strUserName = os.getlogin() if hasattr(os, 'getlogin') else "unknown"
        self.strVendorName = strVendorName
        self.strClientName = strClientName
        self.isDevelopmentMode = isDevelopmentMode
        self.strVoucherType = strVoucherType
        self.user_id = self.UserData.get('uid', None)

    async def initialize(self):
        try:
            self.user_id = self.UserData.get('uid', None)
            self.isPaidUser = (self.UserData.get("usePaidDocExtractor") == "Yes")
            if self.user_id is None:
                self.strDocErrorMsg = "Error User ID Not found"
                self.DocErrorCode = 404
                self.strDocDebugMsg = "user_id not found in UserData"
                raise HTTPException(status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

            # Asynchronous file reading or configuration loading
            self.GPTConfigData = await MakeModelGPTPrompt.ReadConfigFile(UserID=self.user_id, StrPath=self.strGPTConfigPath)
            await self.log("Info", "Preprocess Validation Started")

            if self.bReasoningEnabled:
                self.strGPTModelName = self.GPTConfigData.get("gpt-reasoning-model", None)
            else:
                self.strGPTModelName = self.GPTConfigData.get("gpt-model", None)
                
            if self.strGPTModelName is None:
                self.strDocErrorMsg = "Internal Server Error"
                self.DocErrorCode = 500
                self.strDocDebugMsg = "strGPTModelName not found in GPTConfig"
                await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
                raise HTTPException(status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

            # Increment page credit limit
            await CUserAPIUsageData.MSUpdateUserApiRequest(self.user_id, 1)
                

            await self.log("Info", "GPT Validation Completed")

        except HTTPException as e:
            await self.log("Error", self.strDocErrorMsg)
            await self.log("Debug", self.strDocDebugMsg)
            raise e
        except Exception as e:
            self.strDocErrorMsg = "Server is under heavy load. Unable to process this Document, Please try again later."
            self.DocErrorCode = 500
            await self.log("Error", self.strDocErrorMsg)
            await self.log("Debug", self.strDocDebugMsg)
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            raise HTTPException(status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    
    async def log(self, level: str, message: str):
        """
        Helper method to log messages with the specified severity level.

        Args:
            level (str): The severity level of the log ('Info', 'Error', 'Debug').
            message (str): The message to log.
        """
        await CLogController.MSWriteLog(self.user_id, level, message)

        
    async def getGPTResponse(self,strSystemContent: str = "You are a helpful assistant designed to output JSON.",
                        strUserContent: str = "Who won the world series in 2020?",
                        strModel: str = "gpt-4o-2024-05-13", intSeed: int = 33,
                        dictResponseFormat: dict = {"type": "json_object"}):
        max_retries = 3
        retry_delay = 15  # seconds

        for attempt in range(max_retries):
            try:
                if self.user_id == 10:
                    objResponse = CGPTAPIResponse.client.chat.completions.create(
                        model=strModel,
                        messages=[
                            {"role": "system", "content": f"{strSystemContent}"},
                            {"role": "user", "content": f"{strUserContent}"}],
                        seed=intSeed,
                        response_format=dictResponseFormat,
                        # max_tokens=16384,
                        # temperature=0
                        # metadata = {
                        #         "Project": self.strProjectName,
                        #         "ResponsibleDeveloper": self.strResponsibleDeveloper,
                        #         "Hostname": self.strHostname,
                        #         "ipAddress": self.strIPAddress,
                        #         "Platform": self.strPlatform,
                        #         "User": self.strUserName,
                        #         "FunctionKey": self.strVendorName,
                        #         "ClientName": self.strClientName,
                        #         "VoucherType": self.strVoucherType,
                        #         "DevelopmentMode": "True" if self.isDevelopmentMode else "False"
                        #     }
                    )
                else:
                    objResponse = CGPTAPIResponse.client.chat.completions.create(
                    model=strModel,
                    messages=[
                        {"role": "system", "content": f"{strSystemContent}"},
                        {"role": "user", "content": f"{strUserContent}"}],
                    seed=intSeed,
                    response_format=dictResponseFormat,
                    max_tokens=16384,
                    temperature=0
                    # metadata = {
                    #         "Project": self.strProjectName,
                    #         "ResponsibleDeveloper": self.strResponsibleDeveloper,
                    #         "Hostname": self.strHostname,
                    #         "ipAddress": self.strIPAddress,
                    #         "Platform": self.strPlatform,
                    #         "User": self.strUserName,
                    #         "FunctionKey": self.strVendorName,
                    #         "ClientName": self.strClientName,
                    #         "VoucherType": self.strVoucherType,
                    #         "DevelopmentMode": "True" if self.isDevelopmentMode else "False"
                    #     }
                )
                objGPTResponse = json.loads(objResponse.model_dump_json())
                return objGPTResponse

            except RateLimitError as e:
                await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.getGPTResponse: {str(traceback.format_exc())}")
                await asyncio.sleep(2 ** retry_delay)
            except APITimeoutError as e:
                await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.getGPTResponse: {str(traceback.format_exc())}")
                await asyncio.sleep(2 ** retry_delay)  # Exponential backoff(retry_delay)
            except Exception as e:
                await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.getGPTResponse: {str(traceback.format_exc())}")
                break  # Stop retrying after an unexpected error

        self.strDocErrorMsg = "Server is under heavy load. Please try again later."
        self.DocErrorCode = 409
        self.strDocDebugMsg = f"Unable to Get GPT Response"
        raise HTTPException(
                status_code=self.DocErrorCode, detail=self.strDocErrorMsg)
    
    async def getGPTReasoningResponse(self,strSystemContent: str = "You are a helpful assistant designed to output JSON.",
                        strUserContent: str = "Who won the world series in 2020?",
                        strModel: str = "o3-mini-2025-01-31", intSeed: int = 33,
                        dictResponseFormat: dict = {"type": "json_object"}):
        max_retries = 3
        retry_delay = 15  # seconds

        for attempt in range(max_retries):
            try:
                try:
                    objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                        model=strModel,
                        messages=[
                            {"role": "system", "content": f"{strSystemContent}"},
                            {"role": "user", "content": f"{strUserContent}"}],
                        response_format=dictResponseFormat,
                        reasoning_effort="high",
                        max_completion_tokens=27000,
                        seed=intSeed
                        # metadata = {
                        #     "Project": self.strProjectName,
                        #     "ResponsibleDeveloper": self.strResponsibleDeveloper,
                        #     "Hostname": self.strHostname,
                        #     "ipAddress": self.strIPAddress,
                        #     "Platform": self.strPlatform,
                        #     "User": self.strUserName,
                        #     "FunctionKey": self.strVendorName,
                        #     "ClientName": self.strClientName,
                        #     "VoucherType": self.strVoucherType,
                        #     "DevelopmentMode": "True" if self.isDevelopmentMode else "False"
                        # }
                    )
                    objGPTResponse = json.loads(objResponse.model_dump_json())
                    return objGPTResponse
                except:
                    objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                        model=strModel,
                        messages=[
                            {"role": "system", "content": f"{strSystemContent}"},
                            {"role": "user", "content": f"{strUserContent}"}],
                        response_format=dictResponseFormat,
                        reasoning_effort="high",
                        max_completion_tokens=27000,
                        seed=intSeed
                    )
                    objGPTResponse = json.loads(objResponse.model_dump_json())
                    return objGPTResponse
                
            except RateLimitError as e:
                await asyncio.sleep(2 ** retry_delay)
            except APITimeoutError as e:
                await asyncio.sleep(2 ** retry_delay)  # Exponential backoff(retry_delay)
            except Exception as e:
                await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.getGPTReasoningResponse: {str(traceback.format_exc())}")
                break  # Stop retrying after an unexpected error

        self.strDocErrorMsg = f"traceback : Server is under heavy load. Please try again later. {str(traceback.format_exc())}"
        self.DocErrorCode = 409
        self.strDocDebugMsg = f"traceback: Unable to Get GPT Response : {str(traceback.format_exc())}"
        raise HTTPException(
                status_code=self.DocErrorCode, detail=self.strDocErrorMsg)
      
      
    @staticmethod
    async def MSCallGPTAPI(strSystemContent: str = "You are a helpful assistant designed to output JSON.",
                        strUserContent: str = "Who won the world series in 2020?",
                        strModel: str = "o3-mini-2025-01-31", intSeed: int = 33,
                        strVendorName: str = None, strClientName: str = None, strVoucherType: str = None, bDevelopmentMode: bool = True,
                        dictResponseFormat: dict = {"type": "json_object"},reasoning_effort="low",max_completion_tokens=16384, 
            fTemperature=0, bIsReasoningModel=False):
        max_retries = 2
        retry_delay = 15  # seconds

        for attempt in range(max_retries):
            try:
                if bIsReasoningModel:
                    try:
                        dictMetadata = CGPTAPIResponse.MSBuildOpenAIMetadata(
                            strVendorName=strVendorName,
                            strClientName=strClientName,
                            strvourcherType=strVoucherType,
                            bDevelopmentMode=bDevelopmentMode
                        )
                        objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                            model=strModel,
                            messages=[
                                {"role": "system", "content": f"{strSystemContent}"},
                                {"role": "user", "content": f"{strUserContent}"}],
                            response_format=dictResponseFormat,
                            reasoning_effort=reasoning_effort,
                            max_completion_tokens=max_completion_tokens,
                            seed=intSeed
                            # metadata = dictMetadata
                        )
                    except:
                        objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                            model=strModel,
                            messages=[
                                {"role": "system", "content": f"{strSystemContent}"},
                                {"role": "user", "content": f"{strUserContent}"}],
                            response_format=dictResponseFormat,
                            reasoning_effort=reasoning_effort,
                            max_completion_tokens=max_completion_tokens,
                            seed=intSeed
                        )
                else:
                    dictMetadata = CGPTAPIResponse.MSBuildOpenAIMetadata(
                            strVendorName=strVendorName,
                            strClientName=strClientName,
                            strvourcherType=strVoucherType,
                            bDevelopmentMode=bDevelopmentMode
                        )
                    objResponse = CGPTAPIResponse.client.chat.completions.create(
                        model=strModel,
                        messages=[
                            {"role": "system", "content": f"{strSystemContent}"},
                            {"role": "user", "content": f"{strUserContent}"}],
                        seed=intSeed,
                        response_format=dictResponseFormat,
                        max_tokens=max_completion_tokens,
                        temperature=fTemperature
                        # metadata = dictMetadata
                    )
                objGPTResponse = json.loads(objResponse.model_dump_json())
                return objGPTResponse

            except RateLimitError as e:
                await asyncio.sleep(2 ** retry_delay)
            except APITimeoutError as e:
                await asyncio.sleep(2 ** retry_delay)  # Exponential backoff(retry_delay)
            except Exception as e:
                await CLogController.MSWriteLog(None, "Error", f"Error in GPTAPI.MSCallGPTAPI: {str(traceback.format_exc())}")
                  # Stop retrying after an unexpected error
                error_message = f"Unexpected error occurred: {str(e)}"
                error_traceback = traceback.format_exc()
                return {"error": error_message, "traceback": error_traceback}

        error_message = "Server is under heavy load. Please try again later."
        error_traceback = traceback.format_exc()
        raise HTTPException(
            status_code=409,
            detail=error_message,
            headers={"X-Debug-Message": error_traceback}
        )
      
  
      
      
      
        
    async def run(self, objPromptData, page_width, page_height, is_scanned_document=False, bReasoningEnabled=False):
        try:
            strSystemContent= objPromptData.get("Prompt",None)

            if strSystemContent is None:
                raise HTTPException(
                    status_code=500, detail="An unexpected error occurred. Please try again later.")
            
            await self.log("Info", f"Prompt Used: {strSystemContent} \n Extracted Text:{self.docExtractedTxt}")
            if bReasoningEnabled:
                objGPTResponse = await self.getGPTReasoningResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName)
            else:
                objGPTResponse = await self.getGPTResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName)
            
            json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])
            for item,value in json_objects.items():
                # Update Fields Key Dict Data value
                if isinstance(value, dict) and str(item).lower() != 'tables':
                    updatedItem  = CExtractionHelper.MSConvertDictToListOfDict(value)
                    json_objects[item] = updatedItem
            else:
                await self.log("Debug", f"Prompt Used: {strSystemContent} \n Content - {json_objects} \n GPTAPI Response - {objGPTResponse}")
                objGPTResponse = CGPTAPIResponse.MSSetPageMetaData(objResponse=objGPTResponse, page_width=page_width, page_height=page_height, bDebug=False)
                # await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=self.user_id, strPlanType="pro", iPageLimit=self.iDocPageCount, strOperation="subtract")
                return {"json_objects":json_objects, "response":objGPTResponse}
        except HTTPException as e:
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.run: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.run: {str(traceback.format_exc())}")
            self.strDocErrorMsg = "Server is under heavy load. Please try again later."
            self.DocErrorCode = 409
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            raise HTTPException(
                    status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    async def runForAWS(self, objPromptData, dictResponseFormat, bReasoningEnabled=False):
        try:
            strSystemContent= objPromptData.get("Prompt",None)

            if strSystemContent is None:
                raise HTTPException(
                    status_code=500, detail="An unexpected error occurred. Please try again later.")
            
            await self.log("Info", f"Prompt Used: {strSystemContent} \n Extracted Text:{self.docExtractedTxt}")
            if bReasoningEnabled:
                objGPTResponse = await self.getGPTReasoningResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName, dictResponseFormat=dictResponseFormat)

            else:
                objGPTResponse = await self.getGPTResponse(strSystemContent=strSystemContent,strUserContent=self.docExtractedTxt, strModel=self.strGPTModelName, dictResponseFormat=dictResponseFormat)
    
            json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])
            
            await self.log("Debug", f"Prompt Used: {strSystemContent} \n Content - {json_objects} \n GPTAPI Response - {objGPTResponse}")
            # objGPTResponse = CGPTAPIResponse.MSSetPageMetaData(objResponse=objGPTResponse, page_width=page_width, page_height=page_height, bDebug=False)
            await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=self.user_id, strPlanType="pro", iPageLimit=self.iDocPageCount, strOperation="subtract")
            return {"json_objects":json_objects, "response":objGPTResponse}
        except HTTPException as e:
            raise e
        except Exception as e:
            self.strDocErrorMsg = "Server is under heavy load. Please try again later."
            self.DocErrorCode = 409
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runForAWS: {str(traceback.format_exc())}")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            raise HTTPException(
                    status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    async def runGPT4o(self,strSystemContent , strUserContent,dictResponseFormat):
        try:
            
            await self.log("Info", f"Prompt Used: {strSystemContent} \n Extracted Text:{strUserContent}")

            objGPTResponse = await self.getGPTResponse(strSystemContent=strSystemContent,strUserContent=strUserContent, strModel=self.strGPTModelName, dictResponseFormat=dictResponseFormat)
    
            json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])
            
            await self.log("Debug", f"Prompt Used: {strSystemContent} \n Content - {json_objects} \n GPTAPI Response - {objGPTResponse}")
            # objGPTResponse = CGPTAPIResponse.MSSetPageMetaData(objResponse=objGPTResponse, page_width=page_width, page_height=page_height, bDebug=False)
            await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=self.user_id, strPlanType="pro", iPageLimit=self.iDocPageCount, strOperation="subtract")
            return {"json_objects":json_objects, "response":objGPTResponse}
        except HTTPException as e:
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runGPT4o: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            self.strDocErrorMsg = "Server is under heavy load. Please try again later."
            self.DocErrorCode = 409
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runForAWS: {str(traceback.format_exc())}")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            raise HTTPException(
                    status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    async def runReasoningModelAPI(self, strSystemContent, dictResponseFormat):
        try:
            if strSystemContent is None:
                raise HTTPException(
                    status_code=500, detail="An unexpected error occurred. Please try again later.")
            
            await self.log("Info", f"Prompt Used: {strSystemContent} \n Extracted Text:{self.docExtractedTxt}")
            objResponse = CGPTAPIResponse.client.responses.create(
                    model="o3-mini-2025-01-31",
                    input=[
                        {"role": "system", "content": f"{strSystemContent}"},
                        {"role": "user", "content": f"{self.docExtractedTxt}"}],
                    text=dictResponseFormat, 
                    reasoning={"effort":"high"}
                    # metadata = {
                    #         "Project": self.strProjectName,
                    #         "ResponsibleDeveloper": self.strResponsibleDeveloper,
                    #         "Hostname": self.strHostname,
                    #         "ipAddress": self.strIPAddress,
                    #         "Platform": self.strPlatform,
                    #         "User": self.strUserName,
                    #         "FunctionKey": self.strVendorName,
                    #         "ClientName": self.strClientName,
                    #         "VoucherType": self.strVoucherType,
                    #         "DevelopmentMode": "True" if self.isDevelopmentMode else "False"
                    #     }
                )
            
            objGPTResponse = json.loads(objResponse.model_dump_json())

            json_objects = json.loads(objGPTResponse["output"][1]["content"][0]["text"])
            
            await self.log("Debug", f"Prompt Used: {strSystemContent} \n Content - {json_objects} \n GPTAPI Response - {objGPTResponse}")
            # objGPTResponse = CGPTAPIResponse.MSSetPageMetaData(objResponse=objGPTResponse, page_width=page_width, page_height=page_height, bDebug=False)
            await CUserAPIUsageData.MSUpdateUserPageLimit(iUserId=self.user_id, strPlanType="pro", iPageLimit=self.iDocPageCount, strOperation="subtract")
            return {"json_objects":json_objects, "response":objGPTResponse}
        except HTTPException as e:
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runReasoningModelAPI: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            self.strDocErrorMsg = "Server is under heavy load. Please try again later."
            self.DocErrorCode = 409
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CLogController.MSWriteLog(self.user_id, "Error", f"Error in GPTAPI.runReasoningModelAPI: {str(traceback.format_exc())}")
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            raise HTTPException(
                    status_code=self.DocErrorCode, detail=self.strDocErrorMsg)

    @staticmethod
    def MSBuildOpenAIMetadata(
        strVendorName="Unknown",
        strClientName="Unknown",
        strvourcherType="Unknown",
        bDevelopmentMode=True,
    ) -> dict:

        # Nisarg: Added This as Raising Exception local variable 'strHostname' referenced before assignment
        strHostname = ""
        strPlatform = ""
        strIPAddress = ""
        strUserName = ""
        # Dynamic Metadata for system info
        strHostname = strHostname or socket.gethostname()
        strPlatform = strPlatform or platform.platform()
        strIPAddress = strIPAddress or socket.gethostbyname(strHostname)
        strUserName = strUserName or (os.getlogin() if hasattr(os, 'getlogin') else "unknown")

        return {
            "Project": "AccuVelocity", # Project Name
            "ResponsibleDeveloper": "Mitul Solanki", # Developer Name
            "Hostname": strHostname,
            "ipAddress": strIPAddress,
            "Platform": strPlatform,
            "User": strUserName,
            "FunctionKey": strVendorName,
            "ClientName": strClientName,
            "VoucherType": strvourcherType,
            "DevelopmentMode": "True" if bDevelopmentMode else "False"
        }
    
    @staticmethod
    async def MSRunForPWOIGeneralize(strSystemContent=None,strUserContent=None, dictResponseFormat = None, strClientName=None, strVendorName=None, strVoucherType=None,bDevelopmentMode=True, strModel: str = "o3-mini-2025-01-31", intSeed: int = 33):
        try:
            objGPTResponse = None
            try:
                try:
                    dictMetadata = CGPTAPIResponse.MSBuildOpenAIMetadata(
                            strVendorName=strVendorName,
                            strClientName=strClientName,
                            strvourcherType=strVoucherType,
                            bDevelopmentMode=bDevelopmentMode
                        )
                    objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                    model=strModel,
                    messages=[
                        {"role": "system", "content": f"{strSystemContent}"},
                        {"role": "user", "content": f"{strUserContent}"}],
                    response_format=dictResponseFormat,
                    reasoning_effort="high",
                    max_completion_tokens=27000,
                    seed=intSeed,
                    # metadata = dictMetadata
                    )
                    objGPTResponse = json.loads(objResponse.model_dump_json())
                except:
                    objResponse = CGPTAPIResponse.client.beta.chat.completions.parse(
                        model=strModel,
                        messages=[
                            {"role": "system", "content": f"{strSystemContent}"},
                            {"role": "user", "content": f"{strUserContent}"}],
                        response_format=dictResponseFormat,
                        reasoning_effort="high",
                        max_completion_tokens=27000,
                        seed=intSeed
                    )
                    objGPTResponse = json.loads(objResponse.model_dump_json())
            except Exception as e:
                await CLogController.MSWriteLog(None, "Error", f"Error in GPTAPI.getGPTReasoningResponse: {str(traceback.format_exc())}")
                # break  # Stop retrying after an unexpected error

            json_objects = json.loads(objGPTResponse["choices"][0]["message"]["content"])
            return {"json_objects":json_objects, "response":objGPTResponse}
        except HTTPException as e:
            raise e
        except Exception as e:
            raise
        
    @staticmethod
    def MSSetPageMetaData(objResponse, page_width, page_height, bDebug=True):
        """
        Adds metadata for page width and height to the response object.

        Purpose:
        This function updates the 'content' field of the response object by adding 
        metadata for page dimensions (width and height).

        Input:
        - objResponse: dict
            The response object containing the 'choices' and 'message' fields.
        - page_width: int or float
            The width of the page to be added as metadata.
        - page_height: int or float
            The height of the page to be added as metadata.
        - bDebug: bool, optional
            Flag to enable debug printing, default is True.

        Output:
        - dict
            The updated response object with added metadata.

        Example:
        >>> objResponse = {
                'choices': [
                    {
                        'message': {
                            'content': '{"key": "value"}'
                        }
                    }
                ]
            }
        >>> page_width = 8.5
        >>> page_height = 11
        >>> updatedResponse = MSSetPageMetaData(objResponse, page_width, page_height)
        >>> print(updatedResponse)
        {
            'choices': [
                {
                    'message': {
                        'content': '{"key": "value", "meta_data": {"page_width": 8.5, "page_height": 11}}'
                    }
                }
            ]
        }
        """
        try:
            # Extracting the 'content' which is a string representation of a dictionary
            content_string = objResponse['choices'][0]['message']['content']
            
            # Converting the string back into a dictionary
            GPTJsonData = json.loads(content_string)
            
            # Debug printing if enabled
            if bDebug:
                print("GPTJson: ", GPTJsonData)
            
            # Adding meta_data with page dimensions
            GPTJsonData["meta_data"] = {
                "page_width": page_width,
                "page_height": page_height
            }
            
            # Updating the content with the modified dictionary
            objResponse['choices'][0]['message']['content'] = json.dumps(GPTJsonData, ensure_ascii=False)
            return objResponse
        
        except Exception as e:
            # Handling any exceptions that occur during processing
            print(f"ERROR: While reading GPTJsonFile: {objResponse}")
            raise e

import asyncio

# Assuming the CGPTAPIResponse class is defined and imported correctly

async def fetch_api_response():
    # Assign the variables needed for the call
    m_strSystemPrompt = '''You are a completely and perfectly obedient Indian accountant who is an expert at extracting vendor names from Indian goods or services invoices or tax journal vouchers pages. Follow the below steps to perform the complete task:\n\nStep 1:\nYou are provided with raw invoice text (converted from PDF to text) in the following format in triple quotes:\n\n\'\'\'\n----------------------- Page No. 1 Start --------------------------\n\nall text content of page no. 1\n\n----------------------- Page No. 1 End ----------------------------\n\n----------------------- Page No. 2 Start --------------------------\n\nall text content of page no. 2\n\n----------------------- Page No. 2 End ----------------------------\n\n\'\'\'\n\nEach pageâ€™s content appears between the "Start" and "End" markers.\n\nStep 2:\nAnalyze the text thoroughly to find the vendor or seller name, document type, document number or invoice number. Although the text is unstructured, pay attention to each section, where relevant details are typically displayed in Indian goods or services invoices or tax journal vouchers.\n\nStep 3:\nIn the text content, there may be multiple invoices or tax journal vouchers of different vendors, as well as multiple invoices or tax journal vouchers for the same vendor. Invoices may be of single or multiple pages. Give all details for each document or invoice.\n\nStep 4:\nAccount for possible typos and inconsistent formatting. If a vendor name cannot be confidently identified for invoice, assign an "Unknown" vendor for that page. \n\nStep 5:\nDo not assume anything. There isn\'t any pattern that is followed. It is extremely important that you re-check the given text again page by page (more important) and if invoice number changes on subsequent page (even though seller is same), strickly include that invoice details in output. Correct mistakes, if there are any.'''

    m_strUserContentStructured = '''----------------------- Page No. 1 Start -------------------------\nTOTO\nTAX INVOICE/COMMERCIALCNVOICE\nORIGINAL\nFor Buyer\nTOTO INDIA INDUSTRIES PVT.LTD.\nInvoice No.:\nF25240001027\nUnit 1002. 10th Flr, Kamla Executive Park, Off. Andheri Kurla Road, MIDC Lane,\nSAP Doc.No.:\n7015169311\nAndheri (E), Mumbai 400059\nDraft Invoice:\nMaharashtra,India\nInvoice Date:\n30/04/2024\nGSTIN:\n27AADCT7270M1ZT\nOrder No.:\n1000207014\nPreparation Date/Time: 01/05/2024 03:39:06\nDo No.:\n3000916400\nRemoval Date/Time:\nTransporter Name: SSK LOGISTICS\n2113 m\nSO NO.:\n2001054095\nVehicle No.:\nMP09GI4993\nProject Name/PO NO.: Retail Order\nLR No/LR Date:\n875/30/04/2024\nIRN:8bbde0883b7b6d46e996f5178d6a94f292299b02fe64ac176a0e2f23f66f1e2c\nPayment Terms:\n100% IN ADVANCE\nE-way Bill NO.:\nIncoterms:\nEXW-\nSite Add:\nBilled o:1000001181 VHD DISTRIBUTORS LLP\nShipped to: ********** VHD DISTRIBUTORS LLP\n12/4, Race Course Road. Indore,\n45, Panchal Compound Lasudiya Mori, Dewasnaka, Indore, Madhya Pradesh,\nMadhya Pradesh, 452003\nIndore 453771\nState name:Madhva Pradesh.India\nMadhya Pradesh,India\nGSTIN/ Unique ID: 23AAKFV4306N1ZX\nGSTIN/ Unique ID:23AAKFV4306N1ZX\nPlace of supply:Bhiwandi, Maharashtra\nTax is payable on reverse charge basis:-Yes/No\nDescription of goods: TOTO SANITARY WARES & FITTINGS\nState code: 23\nS.No.\nItem Code\nDescription of Goods/\nHSN/SAC\nQty\nRate/ Price\nTotal Amount\nDiscount\nFreight\nInsurance\nTaxable\nIGST(INR)\nTotal Amount\nServices\n(PC)\n(INR)\n(selling price)\n(INR)\n(INR)\n(INR)\nAmount\n(incl. tax)\n(INR)\n(INR)\nRate(%)\nAmount\n(INR)\n0010\nCW822RA#W\n"Avante" Wall Hung Rimless\n69101000\n20\n14,310.00\n286,200.00\n0,00\n0,00\n1,431.00\n287,631.00\n18.00\n51,774.00\n339,405.00\nToilet\nGrand Total\n20\n286,200.00\n0.00\n1,431.00\n287,631.00\n51,774.00\n339,405.00\nTCS0%\n0.00\nTOTAL VALUE\n339,405.00\nTotal Invoice value (in words)\nINR THREE HUNDRED THIRTY-NINE THOUSAND FOUR HUNDRED FIVE ONLY\nTerms & Condition\n1 We hereby declare that the shipment shipped against this Invoice is affixed with MRP Label as per the Legal Metrology (Packaged Commodities) Rules, 2011.\n..Any items (Packages) found without MRP Label should be reported to the company representative immediately at the time of accepting delivery of the shipment.\nGoods once sold will not be taken back.\n4. All Disputes subject to Mumbai/Vadodara Juris...'''

    strModel = 'o3-mini-2025-01-31'
    intSeed = 33
    m_strResponseFormat = {
        "type": "json_schema",
        "json_schema": {
            "name": "Parag Traders",
            "strict": True,
            "schema": {
                "type": "object",
                "properties": {
                    "VendorNames": {
                        "type": "array",
                        "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page",
                        "items": {
                            "type": "object",
                            "properties": {
                                "PageNumber": {
                                    "type": "string",
                                    "description": "The page number where the vendor name or seller was identified."
                                },
                                "VendorName": {
                                    "type": "string",
                                    "description": "The vendor name or seller name extracted from the page.",
                                    "enum": [
                                        "simpolo",
                                        "hansgrohe",
                                        "kohler",
                                        "nexion",
                                        "toto",
                                        "icon",
                                        "geberit",
                                        "powergrace",
                                        "aquant",
                                        "Unknown"
                                    ]
                                },
                                "DocumentType": {
                                    "type": "string",
                                    "description": "The Type of Document, Identify from content",
                                    "enum": [
                                        "Tax Invoice",
                                        "Tax Journal Voucher"
                                    ]
                                },
                                "InvoiceOrDocumentNumber": {
                                    "type": "integer",
                                    "description": "Unique identification number of Invoice or Document"
                                }
                            },
                            "required": [
                                "PageNumber",
                                "VendorName",
                                "DocumentType",
                                "InvoiceOrDocumentNumber"
                            ],
                            "additionalProperties": False
                        }
                    }
                },
                "required": [
                    "VendorNames"
                ],
                "additionalProperties": False,
                "description": "DO NOT ASSUME ANYTHING."
            }
        }
    }
    reasoning_effort = 'low'
    max_completion_tokens = 16384

    # Perform the async API call
    objResponse = await CGPTAPIResponse.MSCallGPTAPI(
        strSystemContent=m_strSystemPrompt,
        strUserContent=m_strUserContentStructured,
        strModel=strModel,
        intSeed=intSeed,
        dictResponseFormat=m_strResponseFormat,
        reasoning_effort=reasoning_effort,
        max_completion_tokens=max_completion_tokens
    )

    # Print or process the response as needed
    print(objResponse)


if __name__ == "__main__":
    # Run the asynchronous function
    asyncio.run(fetch_api_response())
