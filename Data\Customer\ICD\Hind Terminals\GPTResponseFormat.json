{"type": "json_schema", "json_schema": {"name": "TAXINVOICE", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "The name of the company or seller to whom the invoice is addressed."}, "SellerCorporateIdentityNo": {"type": "string", "description": "<PERSON><PERSON>'s Corporate Identity Number"}, "SellerGSTIN": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN) of the seller."}, "SellerAddress": {"type": "string", "description": "The complete address of the seller to whom the invoice is addressed."}, "SellerStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the seller issuing the invoice"}, "SellerCountry": {"type": "string", "description": "The country where the seller is based. For example, India"}, "Contact No": {"type": "string", "description": "Contact number of the seller"}, "BillingPartyName": {"type": "string", "description": "Name of the Billing party"}, "BillingPartyPanNo": {"type": "string", "description": "PAN number of the Billing Party"}, "BillingPartyAddress": {"type": "string", "description": "Address of the Billing party"}, "BillingPartyGSTNo": {"type": "string", "description": "GST number of the Billing Party"}, "BillingPartyStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the billing party issuing the invoice"}, "BillingPartyStateCode": {"type": "number", "description": "State code of the billing party issuing the invoice"}, "BillingPartyCustomerCinNo": {"type": "string", "description": "Billing Party's Customer CIN number"}, "Cha": {"type": "string", "description": "CHA number of the Billing Party"}, "Consignee": {"type": "string", "description": "The name of the Consignee"}, "ConsigneeAddress": {"type": "string", "description": "The complete address of the Consignee to whom the invoice is addressed."}, "ConsigneeIRNNo": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN) of the consignee."}, "paymentMode": {"type": "string", "description": "Mode of payment like PDA, etc."}, "InvoiceNo": {"type": "string", "description": "Unique invoice number"}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "InvoiceValidityDate": {"type": "integer", "description": "Last Date of the invoice validity in ddmmyy format"}, "PlaceofSupply": {"type": "string", "description": "Place of supply"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Unique serial number for each item."}, "ItemDescription": {"type": "string", "description": "Detailed description of the item or goods."}, "Amount(IN_INR)": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "ItemDescription", "Amount(IN_INR)"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice."}}, "Amount(Before Tax)": {"type": "number", "description": "Total Amount before applying taxes"}, "Amount(In Word)": {"type": "string", "description": "Total amount in words"}, "ItemTable1": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Unique serial number for each item. This should be a sequential integer (e.g., 1, 2, 3)."}, "SAC": {"type": "number", "description": "SAC of the company"}, "Classification Of Service": {"type": "string", "description": "Classification of the service"}, "Amount(Inr)": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "SAC", "Classification Of Service", "Amount(Inr)"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice."}}, "TotalInvoiceAmount(RoundOff)": {"type": "number", "description": "Total invoice amount rounded off"}, "BL No / BE No": {"type": "string", "description": "BL No / BE No of the company"}, "Vessel": {"type": "string", "description": "Vessel name of the company"}, "IGM No": {"type": "number", "description": "IGM number of the company"}, "Line No": {"type": "number", "description": "Line number of the company"}, "Cargo Wt": {"type": "number", "description": "Cargo weight of the company"}, "Liner/Agent": {"type": "string", "description": "Liner/Agent of the company"}, "BL Date": {"type": "number", "description": "BL Date (ddmmyy format)"}, "BE Date": {"type": "number", "description": "BE Date (ddmmyy format)"}, "Movement": {"type": "string", "description": "Name of the movement (e.g., RMS)"}, "Area": {"type": "number", "description": "Area of the product"}, "PreviousInvoice": {"type": "string", "description": "Details of the previous invoice"}, "Cargo": {"type": "string", "description": "Cargo details"}, "LsContainerNumber/Size": {"type": "array", "description": "Contains container numbers and sizes in the format 'cont_no/size'. This list should contain unique values.", "example_values": ["MSMU5118538 /40", "MSDU7919041/40"], "items": {"type": "string", "description": "Container number and size combined as 'cont_no/size'. Example: 'FFAU3647502/40'."}}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "SellerCorporateIdentityNo", "SellerGSTIN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerStateName", "SellerCountry", "Contact No", "BillingPartyName", "BillingPartyPanNo", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BillingPartyGSTNo", "BillingPartyStateName", "BillingPartyStateCode", "BillingPartyCustomerCinNo", "Cha", "Consignee", "Consign<PERSON><PERSON><PERSON><PERSON>", "ConsigneeIRNNo", "paymentMode", "InvoiceNo", "InvoiceDate", "InvoiceValidityDate", "PlaceofSupply", "Table", "Amount(Before Tax)", "Amount(In Word)", "ItemTable1", "TotalInvoiceAmount(RoundOff)", "BL No / BE No", "<PERSON><PERSON><PERSON>", "IGM No", "Line No", "Cargo Wt", "Liner/Agent", "BL Date", "BE Date", "Movement", "Area", "PreviousInvoice", "Cargo", "LsContainerNumber/Size"], "additionalProperties": false}}}