/* style.css */

body {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    margin: 20px;
}

.styled-table {
    border-collapse: collapse;
    width: 100%;
    box-shadow: 0 2px 5px rgba(0,0,0,0.1);
}

.styled-table th, .styled-table td {
    padding: 12px 15px;
    text-align: center;
}

.styled-table th {
    background-color: #009879;
    color: #ffffff;
    text-transform: uppercase;
    font-size: 14px;
}

.styled-table tr {
    border-bottom: 1px solid #dddddd;
}

.styled-table tr:nth-of-type(even) {
    background-color: #f3f3f3;
}

.styled-table tr:last-of-type {
    border-bottom: 2px solid #009879;
}

.header {
    text-align: center;
    padding-bottom: 20px;
}

.footer {
    margin-top: 20px;
    font-size: 14px;
}
