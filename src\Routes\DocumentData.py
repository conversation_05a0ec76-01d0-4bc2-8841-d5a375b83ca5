# from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.DocumentData_controller import CDocumentData
from fastapi import APIRouter, Depends, UploadFile, File, Path, Body, Query, HTTPException, Form, WebSocket
# Importing libraries
from typing import Optional
from starlette.websockets import WebSocketDisconnect
from starlette.websockets import WebSocketState
import traceback
import asyncio
from src.Schemas.Doc_Schema import GetAllDocFilter<PERSON><PERSON>y
from typing import List
# Import your SQLAlchemy UploadedDoc model
from src.Models.models import UploadedDoc, StatusEnum
from src.utilities.ExtractTextHelper import ExtractTextFromDoc
from src.middleware.checkAuth import user_required, UserAndRoleCheck
from src.middleware.checkModel import ModelIdAndDocIdRequired
from src.middleware.checkDocument import DocIdRequired
from src.Controllers.auth_controller import CAuthController
from src.Controllers.GPTResponse_controller import DocumentUploader
from src.Controllers.Logs_Controller import C<PERSON>og<PERSON>ontroller
from src.utilities.DBHelper import CDocumentTable
from config.constants import Constants
import json
from fastapi import WebSocket, WebSocketDisconnect
from src.Schemas.Doc_Schema import  AdditionalDocDetails
# Declaring router
DocumentRouter = APIRouter(tags=['DocumentData'],  prefix="/api")

@DocumentRouter.post("/multi-threading-upload-documents/")
async def upload_documents_route(
    user_id: int = Query(description="Id of current user calling this api"),
    documents: List[UploadFile] = File(...),
    strModelName: str = Query("Invoice", description="Model Name, default is Invoice"),
    strFamilyName: str = Query("Demo Finance", description="Model Family Name, default is Demo Finance"),
    bAutoSelectModel:bool = Query(False, description="To Auto Extract the model from document."),
    bUsePaidModel:bool = Query(True, description="Weather paid model is used or not."),
    max_concurrent_uploads: int = Query(10, description="Maximum concurrent uploads"),
    objAdditionalDocDetails: str = Query("", description="Additional file path")
):
    """
    Endpoint to upload multiple documents.
    Accepts a list of documents, a model name, a user ID, and optionally the maximum number of concurrent uploads.

    - user_id: integer representing the user ID from the user session
    - documents: list of UploadFile objects representing the documents to upload
    - strModelName: string representing the model name, default is 'Invoice'
    - max_concurrent_uploads: integer representing the maximum number of concurrent uploads allowed

    Returns a list of responses for each document upload.
    """
    try:
        # Initialize the document uploader with the specified maximum concurrent uploads
        uploader = DocumentUploader(max_concurrent_uploads=max_concurrent_uploads)
        return await uploader.upload_documents(user_id= user_id, documents = documents, strFamilyName=strFamilyName,model_name = strModelName, bAutoSelectModel=bAutoSelectModel, bUsePaidModel = bUsePaidModel, objAdditionalDocDetails= objAdditionalDocDetails)
    except Exception as e:
        print(traceback.print_exc())
        raise HTTPException(status_code=e.status_code, detail=e.detail)

@DocumentRouter.websocket("/ws/upload")
async def websocket_endpoint(websocket: WebSocket):
    await websocket.accept()
    try:
        while True:
            try:
                # Wait for a message with a timeout of 60 seconds
                data = await asyncio.wait_for(websocket.receive_json(), timeout=Constants.webSocketTimeout)
            except asyncio.TimeoutError:
                if websocket.application_state == WebSocketState.CONNECTED:
                    await websocket.close(code=1000)
                break
            user_id = data['user_id']
            documents = data['documents']
            strModelName = data.get('strModelName', 'Invoice')
            
            if not user_id or not documents:
                await websocket.send_json({"error": "Invalid data received"})
                continue

            uploader = DocumentUploader(max_concurrent_uploads=10)  # Example value
            await uploader.upload_documents(user_id, documents, strModelName, websocket)
    except WebSocketDisconnect:
        # The client disconnected, clean up any resources related to this WebSocket connection here
        print("WebSocket connection closed")
    except Exception as e:
        if websocket.application_state == WebSocketState.CONNECTED:
            await websocket.close(code=1011)

@DocumentRouter.post("/delete-documents/")
async def delete_documents_route(
    lsDocumentIds: List[int],
    user_id: int = Depends(user_required)
):
    try:
        return await CDocumentData.MSDeleteDocuments(user_id = user_id, document_ids = lsDocumentIds)
    except Exception as e:
        print(traceback.print_exc())
        raise HTTPException(status_code=e.status_code, detail=e.detail)


@DocumentRouter.post("/UpdateModel/doc/")
async def UpdateDocModelBaseOnModelId(
    iModelId : int  = Query(...,description="Model Id required to fetch model data"),
    dictRequestData: dict= Depends(ModelIdAndDocIdRequired),
    DocId: int= Query(None, description="Document Id"),
    strModelName: str= Query(None, description="Model Name, default is Invoice")
    
):
    """
    Endpoint to update the model for a specific document.
    
    Parameters:
        user_id (int): The ID of the user.
        doc_id (int): The ID of the document.
        model_name (str): The name of the model.

    Returns:
        bool: True if update successful, False otherwise.
    """
    try: 
        result = await CDocumentTable.MSUpdateModelForDocID( user_id=dictRequestData.get("UserId"), doc_id=dictRequestData.get("DocId"),strModelName= dictRequestData.get("ModelData").get("ModelName"), model_id =  dictRequestData.get("ModelData").get("ModelId"))
        return {"success": result}
    except Exception as e:
        raise HTTPException(status_code=e.status_code, detail="Failed to update model for document.")

@DocumentRouter.post("/UpdateModel/")
async def UpdateDocModelBaseOnModelId(
    iModelId : int,
    DocId: int,
    user_id: int = Depends(user_required),
    modelFamily: str = Constants.DefaultFamilyName

):
    try: 
        return await CDocumentData.MSUpdateModel( user_id=user_id, docId =DocId , modelId = iModelId, modelFamily = modelFamily)
    except Exception as e:
        raise HTTPException(status_code=e.status_code, detail="Failed to update model for document.")
# @DocumentRouter.post("/doc")
# async def upload_pdf_route(user_id: int = Depends(user_required), pdf_file: UploadFile = File(...), strModelName: str = Query("Invoice", description="ModelName default is Invoice")):
#     """
#     Endpoint to upload a PDF document. Requires authentication to determine user_id.
#     """
#     try:
#         userData = await CAuthController.MSGetSingleUser(user_id=user_id)

#         # Read binary data of document
#         file_data = await pdf_file.read()
#         file_name = pdf_file.filename
#         file_type = Constants.allowed_content_types.get(pdf_file.content_type)

#         # Create Document Meta Data object
#         isOCRPDF = await ExtractTextFromDoc.isScannedDoc(file_data=file_data, file_type =file_type)

#         return await CDocumentData.upload_pdf_to_db(user_id=user_id, strModelName=strModelName, file_data=file_data, file_name=file_name, file_type=file_type, is_scanned_doc=int(isOCRPDF))
    
#     except HTTPException as e:
#         raise e
    
#     except Exception as e:
#         raise e

#     finally:
#         await pdf_file.close()

@DocumentRouter.get("/doc/")
async def get_document_by_id_route(DocId: int= Query(None, description="Document Id"), dictRequestData: dict = Depends(DocIdRequired), isBinaryDataRequired = Query(False, description="Boolean Flag to Get Document Binary Data")):
    return await CDocumentData.MSGetDocById(user_id=dictRequestData.get("UserId"), docId=dictRequestData.get("DocId"), isBinaryDataRequired=isBinaryDataRequired)

@DocumentRouter.websocket("/ws/get-status-docs")
async def get_documents_by_ids_route(websocket: WebSocket):
    await websocket.accept()

    try:
        while True:
            # Wait for a message to get doc_ids and user_id
            try:
                # Wait for a message with a timeout of 60 seconds
                data = await asyncio.wait_for(websocket.receive_text(), timeout=Constants.webSocketTimeout)
            except asyncio.TimeoutError:
                if websocket.application_state == WebSocketState.CONNECTED:
                    await websocket.close(code=1000)
                break
            payload = json.loads(data)
            doc_ids = payload["doc_ids"]
            user_id = payload["user_id"]

            # Set binary data flag
            isBinaryDataRequired = False

            results = []

            for docId in doc_ids:
                try:
                    # Fetch document data
                    response_data = await CDocumentData.MSGetDocById(user_id=user_id, docId=docId, isBinaryDataRequired=isBinaryDataRequired)
                    
                    # Add data to results list
                    results.append({"docId": docId, "data": response_data})
                except HTTPException as e:
                    results.append({"docId": docId, "error": str(e.detail)})
                except Exception as e:
                    await CLogController.MSWriteLog(user_id, "Error", f"Failed to Retrieve Document for DocID {docId}")
                    results.append({"docId": docId, "error": "An error occurred during document retrieval"})

            # Send all results together
            await websocket.send_text(json.dumps(results))
    
    except WebSocketDisconnect:
        # The client disconnected, clean up any resources related to this WebSocket connection here
        print("WebSocket connection closed")

    except Exception as e:
        if websocket.application_state == WebSocketState.CONNECTED:
            await websocket.close(code=1011)


@DocumentRouter.get("/doc/download/")
async def download_pdf_route(DocId: int= Query(None, description="Document Id"), dictRequestData: dict = Depends(DocIdRequired)):
    """
    Route to download a PDF document by its ID. Requires user authentication.

    Parameters:
    - iDocId (int): The ID of the document to download, passed in the URL path.
    - user_id (int): User ID, extracted from the request through dependency injection (assumed to be provided by the `user_required` dependency).

    Returns:
    - A StreamingResponse containing the PDF data if found, otherwise raises an HTTPException.
    """
    return await CDocumentData.MSDownloadPdf(iUserID=dictRequestData.get("UserId"), iDocId=dictRequestData.get("DocId"))

@DocumentRouter.post("/doc/update-status")
async def updateDocStatus(iDocId: int = Form(...), bIsPaidModelUsed: bool = Form(...), iUserID: int = Depends(user_required)):
    """
    This function updates the status of a document based on the provided parameters.
    """
    return await CDocumentData.MSUpdateDoc(iUserID=iUserID, bUsedPaidModel=bIsPaidModelUsed, iDocId=iDocId)

@DocumentRouter.get("/docs/all")
async def get_all_documents_route(page: Optional[int] = Query(1, description="Page number starting from 1"),
                                  per_page: Optional[int] = Query(
                                      None, description="Number of documents per page"),
                                  dictRequestData: dict = Depends(UserAndRoleCheck),
                                  filterQuery :GetAllDocFilterQuery= Depends()):
    """
    Endpoint to retrieve all documents with optional user ID filtering and pagination.
    Accepts query parameters 'page', 'per_page', and optionally 'user_id' for document filtering.
    """
    try:
        await CLogController.MSWriteLog(None, "DEBUG", f"MyDocuments Requested UserData-> {dictRequestData}, filterQuery -> {filterQuery}")
        return await CDocumentData.MSGetAllDocument(page=page, per_page=per_page, user_id=dictRequestData.get("UserId"),filterQuery=filterQuery, hasAdminRights=bool((dictRequestData.get("RoleData")).get("hasAdminRights")))
    except Exception as e:
        print("Error ", e)


@DocumentRouter.delete("/doc/delete/")
async def DeleteDoc(DocId: int= Query(None, description="Document Id"), dictRequestData: dict = Depends(DocIdRequired)):

    return await CDocumentData.delete_document(iUserID=dictRequestData.get("UserId"), docId=dictRequestData.get("DocId"))


@DocumentRouter.put("/doc/")
async def UpdateDoc(DocId: int= Query(None, description="Document Id"), dictRequestData: dict = Depends(DocIdRequired), eNewStatus: StatusEnum = Query(..., description="The new status to set for the document")):
    return await CDocumentData.MSUpdateDocumentStatus(iUserID=dictRequestData.get("UserId"), iDocId=dictRequestData.get("DocId"), eNewStatus=eNewStatus)

@DocumentRouter.put("/doc/comment")
async def SetDocumentComment(
    DocId: int = Query(..., description="The ID of the document to update"), 
    strDocComment: str = Query(..., description="The comment to set for the document"),
    dictRequestData:dict=Depends(DocIdRequired)
):
    # Call the MSSetDocumentComment method
    try:
        response = await CDocumentData.MSSetDocumentComment(iUserID=dictRequestData.get("UserId"), iDocId=dictRequestData.get("DocId"), strDocComment=strDocComment, hasAdminRights=bool((dictRequestData.get("RoleData")).get("hasAdminRights")))
        return response
    except Exception as e:
        # Handle any errors and return a user-friendly error message
        return  HTTPException(status_code=e.status_code, detail=e.detail)
