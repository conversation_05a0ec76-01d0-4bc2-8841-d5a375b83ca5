import sys
sys.path.append(".")

import re
import faiss
import asyncio
import nest_asyncio
nest_asyncio.apply()
import sys
# sys.path.append("..")
import warnings
import numpy as np
import pandas as pd
import faiss
import aiohttp
from openai import OpenAIError
from difflib import SequenceMatcher

# Local imports
import src.utilities.EmbeddingHelper as hfa

nest_asyncio.apply()
warnings.filterwarnings("ignore")

class NexionItemMatcher:
    """
    A class to match a single description of goods to a price list based on size and textual similarity.
    """

    @staticmethod
    def parseSize(strInput: str) -> str:
        """
        Extract the size from the input string.

        Parameters:
        -----------
        strInput : str
            The input string containing the description of goods.

        Returns:
        --------
        str
            The extracted size in uppercase (e.g., '600X1200').
            Returns an empty string if no valid size is found.
        """
        if not isinstance(strInput, str):
            return ""

        lstTokens = strInput.split()
        for strToken in lstTokens:
            if 'x' in strToken.lower():
                strCleanedSize = re.sub(r'[^0-9xX\.]', '', strToken)
                if re.match(r'^\d+X\d+(\.\d+)?$', strCleanedSize.upper()):
                    return strCleanedSize.upper()
        return ""

    @staticmethod
    def cleanAndSplitText(strText: str) -> set:
        """
        Clean and split input text into a set of words (order-independent).

        Parameters:
        -----------
        strText : str
            The input text to clean and split.

        Returns:
        --------
        set
            A set of cleaned words in uppercase.
        """
        if not isinstance(strText, str):
            return set()
        strCleanedText = re.sub(r'[^A-Za-z0-9]+', ' ', strText).upper()
        return set(strCleanedText.split())

    @staticmethod
    def calculateJaccardSimilarity(setA: set, setB: set) -> float:
        """
        Calculate Jaccard similarity between two sets of words.

        Parameters:
        -----------
        setA : set
            First set of words.
        setB : set
            Second set of words.

        Returns:
        --------
        float
            Jaccard similarity as a percentage (0-100).
        """
        intIntersection = len(setA.intersection(setB))
        intUnion = len(setA.union(setB))
        return (intIntersection / intUnion) * 100 if intUnion > 0 else 0.0

    def findBestMatch(
        self,
        strInput: str,
        dfPriceList: pd.DataFrame,
        strSizeCol: str,
        lstColumnsToConcat: list,
    ) -> dict:
        """
        Find the best match for a single input string in the price list and return as a dictionary.

        Parameters:
        -----------
        strInput : str
            The description of goods to match.
        dfPriceList : pd.DataFrame
            The price list DataFrame containing reference data.
        strSizeCol : str
            The column name in the price list that contains sizes.
        lstColumnsToConcat : list
            List of columns in the price list to concatenate for matching.

        Returns:
        --------
        dict
            A dictionary with the best match details. Returns an error message if no match is found.
        """
        strExtractedSize = self.parseSize(strInput)
        if not strExtractedSize:
            return {
                "Error": "No valid size found in input."
            }

        dfPriceList[strSizeCol] = dfPriceList[strSizeCol].astype(str).str.upper().str.replace(" ", "")
        dfFiltered = dfPriceList[dfPriceList[strSizeCol] == strExtractedSize]

        if dfFiltered.empty:
            return {
                "Error": f"No matching size found in '{strSizeCol}' column."
            }

        setInputWords = self.cleanAndSplitText(strInput)
        fltBestScore = 0.0
        intBestIndex = None

        for intIdx, row in dfFiltered.iterrows():
            strMergedText = " ".join(
                str(row[strCol]) for strCol in lstColumnsToConcat if strCol in dfFiltered.columns
            )
            setMergedWords = self.cleanAndSplitText(strMergedText)
            fltScore = self.calculateJaccardSimilarity(setInputWords, setMergedWords)

            if fltScore > fltBestScore:
                fltBestScore = fltScore
                intBestIndex = intIdx

        if intBestIndex is not None:
            dictBestRow = dfPriceList.loc[intBestIndex].to_dict()
            dictBestRow["Confidence_Score"] = fltBestScore
            return dictBestRow
        else:
            return {
                "Error": "No match found after filtering."
            }

    def processSingleInput(
        self,
        strInput: str,
        dfPriceList: pd.DataFrame,
        strSizeCol: str,
        lstColumnsToConcat: list,
    ) -> dict:
        """
        Process a single input string and return matching details as a dictionary.

        Parameters:
        -----------
        strInput : str
            A single description of goods to match.
        dfPriceList : pd.DataFrame
            The price list DataFrame containing reference data.
        strSizeCol : str
            The column name in the price list that contains sizes.
        lstColumnsToConcat : list
            List of columns in the price list to concatenate for matching.

        Returns:
        --------
        dict
            A dictionary containing the best match details or an error message.
        """
        return self.findBestMatch(strInput, dfPriceList, strSizeCol, lstColumnsToConcat)



class SimpoloItemMatcher:
    """
    A refactored class that processes a *single* product description string
    against a Simpolo price list. Extracts dimensions, product name, performs
    dimension/name matching, fetches embeddings via OpenAI, and uses FAISS
    to find the most similar matched entity.
    """

    def __init__(self, simpolo_price_lst: pd.DataFrame):
        """
        :param simpolo_price_lst: A DataFrame with at least columns:
                                  ['Size', 'Product Name', 'Product Category', 'Surface', ...]
        """
        self.simpolo_price_lst = simpolo_price_lst

    # -------------------------------------------------------------------------
    # 1) EXTRACTION METHODS
    # -------------------------------------------------------------------------

    def extract_dimensions(self, description: str) -> str:
        """
        Extracts dimensions from the given product description using regex
        and returns a formatted string, e.g. "300 X 300MM" or None if no match.
        """
        if description.startswith("FLORA"):
            # e.g. "FLORA SKID 63030 300 X 300"
            match_flora = re.search(r'(\d+)\s*X\s*(\d+)', description)
            if match_flora:
                return f"{match_flora.group(1)} X {match_flora.group(2)}MM"

        # If it starts with a number (e.g. "63030 300 X 300")
        match_number_start = re.match(r'(\d+)\s+(\d+)\s*X\s*(\d+)', description)
        if match_number_start:
            return f"{match_number_start.group(2)} X {match_number_start.group(3)}MM"

        # If it starts with "EL" or "GL" (e.g. "EL DECOR 600 X 300")
        match_el_gl = re.search(r'(EL|GL)\s+\w+\s+(\d+)\s*X\s*(\d+)', description)
        if match_el_gl:
            return f"{match_el_gl.group(2)} X {match_el_gl.group(3)}MM"

        # Pattern for "PG1200X1800X9" or "GV1200X1800X9"
        match_pg_gv_gt = re.search(r'(PG|GV|GT)(\d+)(X)(\d+)(X)(\d+(\.\d+)?)', description)
        if match_pg_gv_gt:
            return f"{match_pg_gv_gt.group(2)} X {match_pg_gv_gt.group(4)} X {match_pg_gv_gt.group(6)}MM"

        # Pattern for "EL 724 450 X 300" or "GL DECOR 600 X 300"
        match_three_part = re.search(r'(\d+)\s*(\d+)\s*X\s*(\d+)', description)
        if match_three_part:
            return f"{match_three_part.group(1)} X {match_three_part.group(2)} X {match_three_part.group(3)}MM"

        return None

    def extract_product_name(self, description: str) -> str:
        """
        Extracts product name from the description by checking the first few words.
        Skips '2P', removes patterns like 'FB', 'SCS', or <digits>PC.
        """
        # if description and description.lower().strip() == "PG PROZZO ONYX WHITE PG798X1598X8.5 PROZZO".lower():
        #     # Remove "prozzo" (case-insensitive) before splitting the description if 4 words before dimension
        #     description = re.sub(r'prozzo', '', description, flags=re.IGNORECASE).strip()


        words = description.split()
        if not words:
            return None

        # If description starts with "FLORA"
        if description.startswith("FLORA"):
            product_name = " ".join(words[:3])

        # If the first token is numeric
        elif words[0].isdigit():
            product_name = words[0]

        # If description starts with "EL" or "GL"
        elif description.startswith("EL") or description.startswith("GL"):
            product_name = " ".join(words[:2])

        else:
            if len(words) >= 3:
                third_word = words[2]
                if third_word == "2P":
                    product_name = " ".join(words[1:2])
                elif re.search(r'X\\d+', third_word):
                    product_name = " ".join(words[1:2])
                else:
                    product_name = " ".join(words[1:3])
            else:
                product_name = None

        # Remove unwanted patterns "FB", "SCS", or <digits>PC
        if product_name:
            tokens = product_name.split()
            cleaned = [
                token for token in tokens
                if token not in ["FB", "SCS"] and not re.search(r'\\d+PC', token)
            ]
            product_name = " ".join(cleaned) if cleaned else None

        return product_name

    # -------------------------------------------------------------------------
    # 2) MATCHING LOGIC
    # -------------------------------------------------------------------------

    def match_dimension_to_size(self, dimension: str) -> bool:
        """
        Returns True if the dimension is found in the price list's 'Size' column.
        """
        if not dimension:
            return False
        size_column_trimmed = self.simpolo_price_lst['Size'].str.lstrip()
        return dimension in size_column_trimmed.values

    def match_product_name(self, product_name: str) -> bool:
        """
        Returns True if product_name is found (case-insensitive) in the price list's 'Product Name' column
        after cleaning the string (removing symbols and spaces).
        """
        if not product_name:
            return False
        
        # Clean the input product name: remove non-alphanumeric characters and spaces, then convert to lowercase
        cleaned_product_name = re.sub(r'[^a-zA-Z0-9]', '', product_name).replace(" ", "").lower()

        # Get the product name column from the price list and clean each entry similarly
        product_name_column = self.simpolo_price_lst['Product Name']
        
        # Check if any cleaned product name matches the cleaned product names in the column
        return any(cleaned_product_name == (re.sub(r'[^a-zA-Z0-9]', '', str(name)).replace(" ", "").lower()) for name in product_name_column)

    def match_alternate_dimension(self, dimension: str) -> str:
        """
        Tries alternate dimension formats (e.g., '30 X 60' -> '300 X 600').
        Returns the new dimension if found in 'Size', otherwise None.
        """
        if not dimension or " X " not in dimension:
            return None

        size_column_trimmed = self.simpolo_price_lst['Size'].str.strip()
        parts = dimension.split(" X ")

        # Three-part dimension
        if len(parts) == 3:
            try:
                # Create the alternative dimension by multiplying the first two parts by 10
                alt_dimension = f"{int(parts[0]) * 10} X {int(parts[1]) * 10} X {parts[2]}"
                
                # If the exact alternate dimension exists in the size column, return it
                if alt_dimension in size_column_trimmed.values:
                    return alt_dimension

                # If the exact dimension is not found, check for approximate matches within a 0.5 variation range for the third part
                # Extract the numeric value from the thickness part and convert to float
                base_thickness_str = parts[2].replace("MM", "")  # Remove 'MM' or other non-numeric suffix
                base_thickness = float(base_thickness_str)

                # Generate a list of thickness variations in the order you want (e.g., 11.9, 12.1, 11.8, 12.2, ...)
                thickness_variations = []
                for i in range(-5, 6):
                    if i == 0:
                        continue  # Skip the exact thickness as it's already checked
                    variation = round(base_thickness + i * 0.1, 1)  # Generate variations like 12.0, 11.9, 12.1, etc.
                    thickness_variations.append(variation)

                for variation in thickness_variations:
                    alt_variation_dimension = f"{int(parts[0])} X {int(parts[1])} X {variation}MM"
                    
                    if alt_variation_dimension in size_column_trimmed.values:
                        return alt_variation_dimension  # Return the found dimension with variation
            except ValueError:
                pass

        # Two-part dimension
        elif len(parts) == 2:
            part1, part2 = parts[0].strip(), parts[1].strip()
            part2_trimmed = part2.replace("MM", "").strip()

            alt_formats = [
                f"{part1} X {part2}",
                f"{part2} X {part1}",
                f"{part1} x {part2}",
                f"{part2} x {part1}",
                f"{part1} X {part2_trimmed}",
                f"{part2_trimmed} X {part1}",
                f"{part1} x {part2_trimmed}",
                f"{part2_trimmed} x {part1}",
                f"{part1} X {part2}MM",
                f"{part2} X {part1}MM",
                f"{part1} x {part2}MM",
                f"{part2} x {part1}MM"
            ]
            for alt_format in alt_formats:
                if alt_format in size_column_trimmed.values:
                    return alt_format

        return None

    
    def get_matched_indices(
            self,
            dimension: str,
            product_name: str,
            dimension_matched: bool,
            product_matched: bool
        ) -> list:
        """
        Builds a list of matched indices from self.simpolo_price_lst
        based on dimension and product_name matches.
        """
        if not dimension_matched:
            return []

        # Clean the product name parameter: remove symbols, spaces, and convert to lowercase
        cleaned_product_name = re.sub(r'[^a-zA-Z0-9]', '', product_name).replace(" ", "").lower()

        # If dimension matched AND product matched, filter on both
        if product_matched:
            # Clean the 'Product Name' column similarly by applying the cleaning function to each product name
            matched_indices = self.simpolo_price_lst[
                (self.simpolo_price_lst['Size'].str.strip() == dimension) &
                (self.simpolo_price_lst['Product Name'].apply(
                    lambda x: re.sub(r'[^a-zA-Z0-9]', '', str(x)).replace(" ", "").lower()
                ) == cleaned_product_name)
            ].index.tolist()

            # If exact match for dimension and product name is found, proceed with checking variations
            # if matched_indices:
            # Extract the thickness (third part) of the dimension to generate variations
            parts = dimension.split(" X ")
            if len(parts) == 3:
                try:
                    base_thickness_str = parts[2].replace("MM", "")  # Remove 'MM' or other non-numeric suffix
                    base_thickness = float(base_thickness_str)

                    # Generate a list of thickness variations in the order you want (e.g., 11.9, 12.1, 11.8, 12.2, ...)
                    thickness_variations = []
                    for i in range(-5, 6):
                        if i == 0:
                            continue  # Skip the exact thickness as it's already checked
                        variation = round(base_thickness + i * 0.1, 1)  # Generate variations like 12.0, 11.9, 12.1, etc.

                        # If the precision is .0, convert to int
                        if variation.is_integer():
                            variation = int(variation)

                        thickness_variations.append(variation)

                    # For each thickness variation, check if the dimension exists in the 'Size' column
                    for variation in thickness_variations:
                        alt_dimension = f"{int(parts[0])} X {int(parts[1])} X {variation}MM"
                        # Check if this alternate dimension matches any record in the 'Size' column
                        matched_variation = self.simpolo_price_lst[
                            (self.simpolo_price_lst['Size'].str.strip() == alt_dimension) &
                            (self.simpolo_price_lst['Product Name'].apply(
                                lambda x: re.sub(r'[^a-zA-Z0-9]', '', str(x)).replace(" ", "").lower()
                            ) == cleaned_product_name)
                        ].index.tolist()

                        # Append any matched variations to matched_indices
                        matched_indices.extend(matched_variation)

                except ValueError:
                    pass  # In case of any errors while parsing the dimension

        # If product matched is not true, or no matches found, fallback to dimension-only matching
        if not matched_indices:
            matched_indices = self.simpolo_price_lst[
                self.simpolo_price_lst['Size'].str.strip() == dimension
            ].index.tolist()

        return matched_indices

    def get_category_abbreviation(self, category: str) -> str:
        abbreviations = {
            'FULLBODY': 'FB',
            'FULLBODY STEP': 'FB',
            'SCS': 'SCS',
            'SCS MARBLE': 'SCS',
            'STRONG X': 'STX',
            'PROZZO PGVT': 'PG',
            'POSH': 'PSH',
            'PAPER MATT': 'PM',
            'PAPPER MATT': 'PM',
            'TERRADURO': 'TD',
            'ROCKDECK': 'RDK',
            'HIGH GLOSS': 'HG',
            'EVERMATT': 'EVM',
            'GLYPHTECH': 'GL',
            'ANTIFALL': 'AF',
            'CARVING': 'CRV',
            'SHINY': 'SNY',
            # New categories added
            'SCS MATT': 'CO',
            'DOUBLE CHARGE': 'DC',
            'GVT': 'GV',
            'SCS METAL': 'ME',
            'PGVT': 'PG',
            'ROCK DECK/OUTDOOR': 'RD',
            'SUGAR': 'SG',
            'SMART MARBLE': 'SM',
            'SNOW SALT': 'SN',
            'SAMPLE': 'SP',
            'SOLUBLE SALT': 'SS',
            'TERRA DURO': 'TD',
            'SCS WOODS': 'WO',
            'PLATINUM': 'PT',
            'MATT SATIN': 'MS',
            'ELEVATION': 'EV',
            'GLOSSY': 'GS',
            'RUSTIC': 'RS',
            'HARD MATT': 'HM',
            'THIRDFIRING': 'TF',
            'GLUE': 'GL',
            'SSS': '3S',
            'HIGH ELEVATION': 'HE',
            'REACTIVE': 'RA',
            'GLUE GRANULA': 'GG',
            'COOL ROOF': 'CL',
            'FEATHER TOUCH': 'FT',
            'NEO FORTE': 'NF',
            'SOFTMATT': 'SO',
            'NEO SENSO': 'NS',
            'SABBIA': 'SB',
            'METALIC': 'MT',
        }
        # if not run:
        return abbreviations.get(category, category)
        # else:
        #     return "NEO FORTE" if category == "NEOTRA" else ""

    def get_surface_abbreviation(self, surface: str) -> str:
        abbreviations = {
            'MATT(GROOVE Texture)(GLIDE texture)(VERTIG Texture)(VIBE Texture)(HORIZON Texture)(FORT Texture)': 'GV',
            'POLISHED': 'GV',
            'MATT/POLISHED': 'GV',
            'GEO TEXTURE': 'GT',
            'FULLBODY': 'FB',
            
            'FULLBODY STEP': 'FB',
            'SCS': 'SCS',
            'SCS MARBLE': 'SCS',
            'STRONG X': 'STX',
            'PROZZO PGVT': 'PG',
            'POSH': 'PSH',
            'PAPER MATT': 'PM',
            'PAPPER MATT': 'PM',
            'TERRADURO': 'TD',
            'ROCKDECK': 'RDK',
            'HIGH GLOSS': 'HG',
            'EVERMATT': 'EVM',
            'GLYPHTECH': 'GL',
            'ANTIFALL': 'AF',
            'CARVING': 'CRV',
            'SHINY': 'SNY',
            # New categories added
            'SCS MATT': 'CO',
            'DOUBLE CHARGE': 'DC',
            'GVT': 'GV',
            'SCS METAL': 'ME',
            'PGVT': 'PG',
            'ROCK DECK/OUTDOOR': 'RD',
            'SUGAR': 'SG',
            'SMART MARBLE': 'SM',
            'SNOW SALT': 'SN',
            'SAMPLE': 'SP',
            'SOLUBLE SALT': 'SS',
            'TERRA DURO': 'TD',
            'SCS WOODS': 'WO',
            'PLATINUM': 'PT',
            'MATT SATIN': 'MS',
            'ELEVATION': 'EV',
            'GLOSSY': 'GS',
            'RUSTIC': 'RS',
            'HARD MATT': 'HM',
            'THIRDFIRING': 'TF',
            'GLUE': 'GL',
            'SSS': '3S',
            'HIGH ELEVATION': 'HE',
            'REACTIVE': 'RA',
            'GLUE GRANULA': 'GG',
            'COOL ROOF': 'CL',
            'FEATHER TOUCH': 'FT',
            'NEO FORTE': 'NF',
            'SOFTMATT': 'SO',
            'NEO SENSO': 'NS',
            'SABBIA': 'SB',
            'METALIC': 'MT',
        }
        return abbreviations.get(surface, '')

    def build_matched_entities_list(self, matched_indices: list) -> list:
        """
        Returns a list of combined matched entities from matched_indices in the price list.
        Format: "<CategoryAbbrev> <ProductName> <SurfaceAbbrev><Size>"
        """
        if not matched_indices:
            return []

        sub_df = self.simpolo_price_lst.loc[
            matched_indices,
            ['Product Category', 'Product Name', 'Surface', 'Size']
        ]

        combined_values = []
        for _, row in sub_df.iterrows():
            # if "NEOTRA" in row['Product Category']:
            #     # NEO SENSO
            #     cat_abbr = self.get_category_abbreviation(row['Product Category'])
            #     surf_abbr = self.get_surface_abbreviation(row['Surface'])
            #     combined_values.append(
            #         f"{cat_abbr} {row['Product Name']} {surf_abbr}{row['Size']}"
            #     )
            #     # NEO FORTE
            #     cat_abbr = self.get_category_abbreviation(row['Product Category'], run=1)
            #     surf_abbr = self.get_surface_abbreviation(row['Surface'])
            #     combined_values.append(
            #         f"{cat_abbr} {row['Product Name']} {surf_abbr}{row['Size']}"
            #     )
            # else:
            cat_abbr = self.get_category_abbreviation(row['Product Category'])
            surf_abbr = self.get_surface_abbreviation(row['Surface'])
            combined_values.append(
                f"{cat_abbr} {row['Product Name']} {surf_abbr}{row['Size']}"
            )
        return combined_values

    # -------------------------------------------------------------------------
    # 3) EMBEDDINGS & FAISS
    # -------------------------------------------------------------------------

    async def get_embeddings_from_openai(self, description: str, entities: list) -> dict:
        """
        Gets embeddings for the single description + each matched entity.
        Returns:
          {
            'desc_embed': <512-dim vector (list)>,
            'entity_embeds': [<vector>, <vector>, ...]
          }
        """
        async with aiohttp.ClientSession() as session:
            tasks = []
            tasks.append(hfa.getEmbeddingsFromOpenAI(description, session))
            for e in entities:
                tasks.append(hfa.getEmbeddingsFromOpenAI(e, session))

            results = await asyncio.gather(*tasks)

        if not results:
            return {'desc_embed': None, 'entity_embeds': []}

        # First result => description
        desc_embed = results[0]['data'][0]['embedding'] if 'data' in results[0] else None

        # Remaining => entity embeddings
        entity_embeds = []
        for r in results[1:]:
            if 'data' in r:
                entity_embeds.append(r['data'][0]['embedding'])
            else:
                entity_embeds.append(None)

        return {
            'desc_embed': desc_embed,
            'entity_embeds': entity_embeds
        }

    def calculate_faiss_similarity(
        self,
        desc_embed: list,
        entity_embeds: list,
        matched_indices: list
    ) -> int:
        """
        Uses FAISS to find the closest entity embedding to `desc_embed`.
        Returns the final index from matched_indices or None if there's an issue.
        """
        if (desc_embed is None) or (not entity_embeds) or (not matched_indices):
            return None

        query_vector = np.array(desc_embed, dtype=np.float32).reshape(1, -1)
        entity_vectors = np.array(entity_embeds, dtype=np.float32)

        # Ensure shape is correct
        if (
            len(entity_vectors.shape) != 2
            or entity_vectors.shape[1] != query_vector.shape[1]
        ):
            return None

        index = faiss.IndexFlatL2(query_vector.shape[1])
        index.add(entity_vectors)
        distances, faiss_indices = index.search(query_vector, 1)
        most_similar_idx = int(faiss_indices[0][0])

        if most_similar_idx < len(matched_indices):
            return matched_indices[most_similar_idx]
        return None

    # -------------------------------------------------------------------------
    # 4) MAIN METHOD: process_single_description
    # -------------------------------------------------------------------------

    async def process_single_description(self, description: str) -> dict:
        """
        Processes a single description from start to finish:
          1) Dimension & product name extraction
          2) Attempt dimension match
          3) If dimension not matched => check alternate dimension
          4) Build matched indices from dimension + product
          5) If matched indices exist => get embeddings, do FAISS similarity
          6) Return the matched row as a dict (or {} if no match).
        """
        # 1) Extract dimension & product
        dimension = self.extract_dimensions(description)
        product_name = self.extract_product_name(description)

        # 2) Match dimension
        dimension_matched = self.match_dimension_to_size(dimension)
        product_matched = False
        if dimension_matched:
            product_matched = self.match_product_name(product_name)
            
        # 3) If dimension not matched, check alternate dimension
        if not dimension_matched:
            alt_dimension = self.match_alternate_dimension(dimension)
            if alt_dimension and self.match_dimension_to_size(alt_dimension):
                # Update dimension if alt found
                dimension = alt_dimension
                dimension_matched = True
                # Re-check product name with updated dimension
                product_matched = self.match_product_name(product_name)

        # 4) Build matched indices
        matched_indices = self.get_matched_indices(dimension, product_name, dimension_matched, product_matched)
        if not matched_indices:
            # No match => return empty
            return {}

        # 5) Get embeddings & do FAISS if matched
        all_matched_entities = self.build_matched_entities_list(matched_indices)
        embed_results = await self.get_embeddings_from_openai(description, all_matched_entities)

        desc_embed = embed_results['desc_embed']
        entity_embeds = embed_results['entity_embeds']

        final_idx = self.calculate_faiss_similarity(desc_embed, entity_embeds, matched_indices)

        # 6) Return matched row
        if final_idx is not None:
            return self.simpolo_price_lst.loc[final_idx].to_dict()

        return {}

class SimpoloItemMatcherV3:
    """
    A refactored class that processes a *single* product description string
    against a Simpolo price list. Extracts dimensions, product name, performs
    dimension/name matching, fetches embeddings via OpenAI, and uses FAISS
    to find the most similar matched entity.
    """

    def __init__(self, simpolo_price_lst: pd.DataFrame):
        """
        :param simpolo_price_lst: A DataFrame with at least columns:
                                  ['Size', 'Product Name', 'Product Category', 'Surface', ...]
        """
        self.simpolo_price_lst = simpolo_price_lst

    # -------------------------------------------------------------------------
    # 1) EXTRACTION METHODS
    # -------------------------------------------------------------------------

    def extract_dimensions(self, description: str) -> str:
        """
        Extracts dimensions from the given product description using regex
        and returns a formatted string, e.g. "300 X 300MM" or None if no match.
        """
        if description.startswith("FLORA"):
            # e.g. "FLORA SKID 63030 300 X 300"
            match_flora = re.search(r'(\d+)\s*X\s*(\d+)', description)
            if match_flora:
                return f"{match_flora.group(1)} X {match_flora.group(2)}MM"

        # If it starts with a number (e.g. "63030 300 X 300")
        match_number_start = re.match(r'(\d+)\s+(\d+)\s*X\s*(\d+)', description)
        if match_number_start:
            return f"{match_number_start.group(2)} X {match_number_start.group(3)}MM"

        # If it starts with "EL" or "GL" (e.g. "EL DECOR 600 X 300")
        match_el_gl = re.search(r'(EL|GL)\s+\w+\s+(\d+)\s*X\s*(\d+)', description)
        if match_el_gl:
            return f"{match_el_gl.group(2)} X {match_el_gl.group(3)}MM"

        # Pattern for "PG1200X1800X9" or "GV1200X1800X9"
        match_pg_gv_gt = re.search(r'(PG|GV|GT)(\d+)(X)(\d+)(X)(\d+(\.\d+)?)', description)
        if match_pg_gv_gt:
            return f"{match_pg_gv_gt.group(2)} X {match_pg_gv_gt.group(4)} X {match_pg_gv_gt.group(6)}MM"

        # Pattern for "EL 724 450 X 300" or "GL DECOR 600 X 300"
        match_three_part = re.search(r'(\d+)\s*(\d+)\s*X\s*(\d+)', description)
        if match_three_part:
            return f"{match_three_part.group(1)} X {match_three_part.group(2)} X {match_three_part.group(3)}MM"

        return None

    def extract_product_name(self, description: str) -> str:
        """
        Extracts product name from the description by checking the first few words.
        Skips '2P', removes patterns like 'FB', 'SCS', or <digits>PC.
        """
        # if description and description.lower().strip() == "PG PROZZO ONYX WHITE PG798X1598X8.5 PROZZO".lower():
        #     # Remove "prozzo" (case-insensitive) before splitting the description if 4 words before dimension
        #     description = re.sub(r'prozzo', '', description, flags=re.IGNORECASE).strip()


        words = description.split()
        if not words:
            return None

        # If description starts with "FLORA"
        if description.startswith("FLORA"):
            product_name = " ".join(words[:3])

        # If the first token is numeric
        elif words[0].isdigit():
            product_name = words[0]

        # If description starts with "EL" or "GL"
        elif description.startswith("EL") or description.startswith("GL"):
            product_name = " ".join(words[:2])

        else:
            if len(words) >= 3:
                third_word = words[2]
                if third_word == "2P":
                    product_name = " ".join(words[1:2])
                elif re.search(r'X\\d+', third_word):
                    product_name = " ".join(words[1:2])
                else:
                    product_name = " ".join(words[1:3])
            else:
                product_name = None

        # Remove unwanted patterns "FB", "SCS", or <digits>PC
        if product_name:
            tokens = product_name.split()
            cleaned = [
                token for token in tokens
                if token not in ["FB", "SCS"] and not re.search(r'\\d+PC', token)
            ]
            product_name = " ".join(cleaned) if cleaned else None

        return product_name

    # -------------------------------------------------------------------------
    # 2) MATCHING LOGIC
    # -------------------------------------------------------------------------

    def match_dimension_to_size(self, dimension: str) -> bool:
        """
        Returns True if the dimension is found in the price list's 'Size' column.
        """
        if not dimension:
            return False
        size_column_trimmed = self.simpolo_price_lst['Size'].str.lstrip()
        return dimension in size_column_trimmed.values

    def match_product_name(self, product_name: str) -> bool:
        """
        Returns True if product_name is found (case-insensitive) in the price list's 'Product Name' column
        after cleaning the string (removing symbols and spaces).
        """
        if not product_name:
            return False
        
        # Clean the input product name: remove non-alphanumeric characters and spaces, then convert to lowercase
        cleaned_product_name = re.sub(r'[^a-zA-Z0-9]', '', product_name).replace(" ", "").lower()

        # Get the product name column from the price list and clean each entry similarly
        product_name_column = self.simpolo_price_lst['Product Name']
        
        # Check if any cleaned product name matches the cleaned product names in the column
        return any(cleaned_product_name == (re.sub(r'[^a-zA-Z0-9]', '', str(name)).replace(" ", "").lower()) for name in product_name_column)

    def match_alternate_dimension(self, dimension: str) -> str:
        """
        Matches alternate dimensions by considering different orderings, casing, suffixes like 'MM',
        and checking ranges for both the mm and size parts. The function handles:
        
        - Three-part dimensions (e.g., "800 X 1800 X 9MM"):
        • Adjusts the mm part by ±0.5 (in 0.1 increments).
        • Checks for size variations by altering the first two dimensions by ±2.
        • Attempts an alternative format by multiplying the first two parts by 10.
        
        - Two-part dimensions (e.g., "800 X 1800" or "1200 X 2780"):
        • Tests various formulations (order reversal, different casing, with/without "MM").
        
        Args:
            dimension (str): The original dimension string from the row.
        
        Returns:
            str or None: The alternate dimension if found in the trimmed size list, otherwise None.
        """
        if not dimension or " X " not in dimension:
            return None

        # Get the trimmed size column from the simpolo_price_lst dataframe
        size_column_trimmed = self.simpolo_price_lst['Size'].str.strip()
        parts = dimension.split(" X ")

        if len(parts) == 3:
            # Extract parts for three-part dimension, e.g., "800 X 1800 X 9MM"
            size_part_1 = parts[0].strip()
            size_part_2 = parts[1].strip()
            mm_part_raw = parts[2].strip()
            # Remove the 'MM' suffix if present
            mm_part = mm_part_raw.replace("MM", "").strip()

            try:
                mm_value = float(mm_part)
            except ValueError:
                return None

            # Check variations for mm: ±0.5 range in 0.1 increments
            for delta in range(-5, 6):  # delta from -5 to +5
                adjusted_mm_value = round(mm_value + delta / 10, 1)
                alt_dimension = f"{size_part_1} X {size_part_2} X {adjusted_mm_value}MM"
                if alt_dimension in size_column_trimmed.values:
                    return alt_dimension

            # Check variations for the size dimensions by adjusting each by ±2
            for delta_1 in range(-2, 3):
                for delta_2 in range(-2, 3):
                    try:
                        alt_size_1 = int(size_part_1) + delta_1
                        alt_size_2 = int(size_part_2) + delta_2
                        alt_dimension_size = f"{alt_size_1} X {alt_size_2} X {mm_part}MM"
                        if alt_dimension_size in size_column_trimmed.values:
                            return alt_dimension_size
                    except ValueError:
                        continue

            # Finally, try an alternative dimension by multiplying the first two parts by 10
            try:
                alt_dimension = f"{int(size_part_1) * 10} X {int(size_part_2) * 10} X {parts[2].strip()}"
                if alt_dimension in size_column_trimmed.values:
                    return alt_dimension
            except ValueError:
                pass

        elif len(parts) == 2:
            # For two-part dimensions such as "800 X 1800"
            part1, part2 = parts[0].strip(), parts[1].strip()
            part2_trimmed = part2.replace("MM", "").strip()

            # Generate a list of alternate formats with various ordering and suffix options
            alt_formats = [
                f"{part1} X {part2}",
                f"{part2} X {part1}",
                f"{part1} x {part2}",
                f"{part2} x {part1}",
                f"{part1} X {part2_trimmed}",
                f"{part2_trimmed} X {part1}",
                f"{part1} x {part2_trimmed}",
                f"{part2_trimmed} x {part1}",
                f"{part1} X {part2}MM",
                f"{part2} X {part1}MM",
                f"{part1} x {part2}MM",
                f"{part2} x {part1}MM"
            ]

            for alt_format in alt_formats:
                if alt_format in size_column_trimmed.values:
                    return alt_format

        return None



    def get_matched_indices(
            self,
            dimension: str,
            product_name: str,
            dimension_matched: bool,
            product_matched: bool
        ) -> list:
        """
        Builds a list of matched indices from self.simpolo_price_lst
        based on dimension and product_name matches.
        """
        if not dimension_matched:
            return []

        # Clean the product name parameter: remove symbols, spaces, and convert to lowercase
        cleaned_product_name = re.sub(r'[^a-zA-Z0-9]', '', product_name).replace(" ", "").lower()

        matched_indices = []


        # If dimension matched AND product matched, filter on both
        if product_matched:
            # Clean the 'Product Name' column similarly by applying the cleaning function to each product name
            matched_indices = self.simpolo_price_lst[
                (self.simpolo_price_lst['Size'].str.strip() == dimension) 

            ].index.tolist()

            # If exact match for dimension and product name is found, proceed with checking variations
            # if matched_indices:
            # Extract the thickness (third part) of the dimension to generate variations
            parts = dimension.split(" X ")
            if len(parts) == 3:
                try:
                    base_thickness_str = parts[2].replace("MM", "")  # Remove 'MM' or other non-numeric suffix
                    base_thickness = float(base_thickness_str)

                    # Generate a list of thickness variations in the order you want (e.g., 11.9, 12.1, 11.8, 12.2, ...)
                    thickness_variations = []
                    for i in range(-5, 6):
                        if i == 0:
                            continue  # Skip the exact thickness as it's already checked
                        variation = round(base_thickness + i * 0.1, 1)  # Generate variations like 12.0, 11.9, 12.1, etc.

                        # If the precision is .0, convert to int
                        if variation.is_integer():
                            variation = int(variation)

                        thickness_variations.append(variation)

                    # For each thickness variation, check if the dimension exists in the 'Size' column
                    for variation in thickness_variations:
                        alt_dimension = f"{int(parts[0])} X {int(parts[1])} X {variation}MM"
                        # Check if this alternate dimension matches any record in the 'Size' column
                        matched_variation = self.simpolo_price_lst[
                            (self.simpolo_price_lst['Size'].str.strip() == alt_dimension) 
                            
                        ].index.tolist()

                        # Append any matched variations to matched_indices
                        matched_indices.extend(matched_variation)

                except ValueError:
                    pass  # In case of any errors while parsing the dimension

        # If product matched is not true, or no matches found, fallback to dimension-only matching
        if not matched_indices:
            matched_indices = self.simpolo_price_lst[
                self.simpolo_price_lst['Size'].str.strip() == dimension
            ].index.tolist()

        return matched_indices

    def get_category_abbreviation(self, category: str) -> str:
        abbreviations = {
            'FULLBODY': 'FB',
            'FULLBODY STEP': 'FB',
            'SCS': 'SCS',
            'SCS MARBLE': 'SCS',
            'STRONG X': 'STX',
            'PROZZO PGVT': 'PG',
            'POSH': 'PSH',
            'PAPER MATT': 'PM',
            'PAPPER MATT': 'PM',
            'TERRADURO': 'TD',
            'ROCKDECK': 'RDK',
            'HIGH GLOSS': 'HG',
            'EVERMATT': 'EVM',
            'GLYPHTECH': 'GL',
            'ANTIFALL': 'AF',
            'CARVING': 'CRV',
            'SHINY': 'SNY',
            # New categories added
            'SCS MATT': 'CO',
            'DOUBLE CHARGE': 'DC',
            'GVT': 'GV',
            'SCS METAL': 'ME',
            'PGVT': 'PG',
            'ROCK DECK/OUTDOOR': 'RD',
            'SUGAR': 'SG',
            'SMART MARBLE': 'SM',
            'SNOW SALT': 'SN',
            'SAMPLE': 'SP',
            'SOLUBLE SALT': 'SS',
            'TERRA DURO': 'TD',
            'SCS WOODS': 'WO',
            'PLATINUM': 'PT',
            'MATT SATIN': 'MS',
            'ELEVATION': 'EV',
            'GLOSSY': 'GS',
            'RUSTIC': 'RS',
            'HARD MATT': 'HM',
            'THIRDFIRING': 'TF',
            'GLUE': 'GL',
            'SSS': '3S',
            'HIGH ELEVATION': 'HE',
            'REACTIVE': 'RA',
            'GLUE GRANULA': 'GG',
            'COOL ROOF': 'CL',
            'FEATHER TOUCH': 'FT',
            'NEO FORTE': 'NF',
            'SOFTMATT': 'SO',
            'NEO SENSO': 'NS',
            'SABBIA': 'SB',
            'METALIC': 'MT',
        }
        # if not run:
        return abbreviations.get(category, category)
        # else:
        #     return "NEO FORTE" if category == "NEOTRA" else ""

    def get_surface_abbreviation(self, surface: str) -> str:
        abbreviations = {
            'MATT(GROOVE Texture)(GLIDE texture)(VERTIG Texture)(VIBE Texture)(HORIZON Texture)(FORT Texture)': 'GV',
            'POLISHED': 'GV',
            'MATT/POLISHED': 'GV',
            'GEO TEXTURE': 'GT',
            'FULLBODY': 'FB',
            
            'FULLBODY STEP': 'FB',
            'SCS': 'SCS',
            'SCS MARBLE': 'SCS',
            'STRONG X': 'STX',
            'PROZZO PGVT': 'PG',
            'POSH': 'PSH',
            'PAPER MATT': 'PM',
            'PAPPER MATT': 'PM',
            'TERRADURO': 'TD',
            'ROCKDECK': 'RDK',
            'HIGH GLOSS': 'HG',
            'EVERMATT': 'EVM',
            'GLYPHTECH': 'GL',
            'ANTIFALL': 'AF',
            'CARVING': 'CRV',
            'SHINY': 'SNY',
            # New categories added
            'SCS MATT': 'CO',
            'DOUBLE CHARGE': 'DC',
            'GVT': 'GV',
            'SCS METAL': 'ME',
            'PGVT': 'PG',
            'ROCK DECK/OUTDOOR': 'RD',
            'SUGAR': 'SG',
            'SMART MARBLE': 'SM',
            'SNOW SALT': 'SN',
            'SAMPLE': 'SP',
            'SOLUBLE SALT': 'SS',
            'TERRA DURO': 'TD',
            'SCS WOODS': 'WO',
            'PLATINUM': 'PT',
            'MATT SATIN': 'MS',
            'ELEVATION': 'EV',
            'GLOSSY': 'GS',
            'RUSTIC': 'RS',
            'HARD MATT': 'HM',
            'THIRDFIRING': 'TF',
            'GLUE': 'GL',
            'SSS': '3S',
            'HIGH ELEVATION': 'HE',
            'REACTIVE': 'RA',
            'GLUE GRANULA': 'GG',
            'COOL ROOF': 'CL',
            'FEATHER TOUCH': 'FT',
            'NEO FORTE': 'NF',
            'SOFTMATT': 'SO',
            'NEO SENSO': 'NS',
            'SABBIA': 'SB',
            'METALIC': 'MT',
        }
        return abbreviations.get(surface, '')

    def build_matched_entities_list(self, matched_indices: list) -> list:
        """
        Returns a list of combined matched entities from matched_indices in the price list.
        Format: "<CategoryAbbrev> <ProductName> <SurfaceAbbrev><Size>"
        """
        if not matched_indices:
            return []

        sub_df = self.simpolo_price_lst.loc[
            matched_indices,
            ['Product Category', 'Product Name','Size']
        ]

        combined_values = []
        for _, row in sub_df.iterrows():
            # if "NEOTRA" in row['Product Category']:
            #     # NEO SENSO
            #     cat_abbr = self.get_category_abbreviation(row['Product Category'])
            #     surf_abbr = self.get_surface_abbreviation(row['Surface'])
            #     combined_values.append(
            #         f"{cat_abbr} {row['Product Name']} {surf_abbr}{row['Size']}"
            #     )
            #     # NEO FORTE
            #     cat_abbr = self.get_category_abbreviation(row['Product Category'], run=1)
            #     surf_abbr = self.get_surface_abbreviation(row['Surface'])
            #     combined_values.append(
            #         f"{cat_abbr} {row['Product Name']} {surf_abbr}{row['Size']}"
            #     )
            # else:
            cat_abbr = self.get_category_abbreviation(row['Product Category'])

            combined_values.append(
                f"{cat_abbr} {row['Product Name']} {row['Size']}"
            )
        return combined_values

    

    # -------------------------------------------------------------------------
    # 3) EMBEDDINGS & FAISS
    # -------------------------------------------------------------------------

    async def get_embeddings_from_openai(self, description: str, entities: list) -> dict:
        """
        Gets embeddings for the single description + each matched entity.
        Returns:
          {
            'desc_embed': <512-dim vector (list)>,
            'entity_embeds': [<vector>, <vector>, ...]
          }
        """
        async with aiohttp.ClientSession() as session:
            tasks = []
            tasks.append(hfa.getEmbeddingsFromOpenAI(description, session))
            for e in entities:
                tasks.append(hfa.getEmbeddingsFromOpenAI(e, session))

            results = await asyncio.gather(*tasks)

        if not results:
            return {'desc_embed': None, 'entity_embeds': []}

        # First result => description
        desc_embed = results[0]['data'][0]['embedding'] if 'data' in results[0] else None

        # Remaining => entity embeddings
        entity_embeds = []
        for r in results[1:]:
            if 'data' in r:
                entity_embeds.append(r['data'][0]['embedding'])
            else:
                entity_embeds.append(None)

        return {
            'desc_embed': desc_embed,
            'entity_embeds': entity_embeds
        }

    def calculate_faiss_similarity(
        self,
        desc_embed: list,
        entity_embeds: list,
        matched_indices: list
    ) -> list:
        """
        Uses FAISS to find the top 3 closest entity embeddings to `desc_embed`.
        Returns the list of indices from `matched_indices` corresponding to the top 3 closest embeddings,
        or None if there's an issue.
        """
        if (desc_embed is None) or (not entity_embeds) or (not matched_indices):
            return None

        query_vector = np.array(desc_embed, dtype=np.float32).reshape(1, -1)
        entity_vectors = np.array(entity_embeds, dtype=np.float32)

        # Ensure shape is correct
        if (
            len(entity_vectors.shape) != 2
            or entity_vectors.shape[1] != query_vector.shape[1]
        ):
            return None

        index = faiss.IndexFlatL2(query_vector.shape[1])
        index.add(entity_vectors)

        # Get top 3 closest vectors
        distances, faiss_indices = index.search(query_vector, 3)  # Searching for top 3 matches

        most_similar_indices = []
        for i in range(3):
            most_similar_idx = int(faiss_indices[0][i])
            if most_similar_idx < len(matched_indices):
                most_similar_indices.append(matched_indices[most_similar_idx])

        # Return the top 3 indices or None if there are issues
        return most_similar_indices if len(most_similar_indices) == 3 else None


    # -------------------------------------------------------------------------
    # 4) MAIN METHOD: process_single_description
    # -------------------------------------------------------------------------

    async def process_single_description(self, description: str) -> dict:
        """
        Processes a single description from start to finish:
        1) Dimension & product name extraction
        2) Attempt dimension match
        3) If dimension not matched => check alternate dimension
        4) Build matched indices from dimension + product
        5) If matched indices exist => get embeddings, do FAISS similarity
        6) Return the matched row as a dict (or {} if no match).
        """
        # 1) Extract dimension & product
        dimension = self.extract_dimensions(description)
        product_name = self.extract_product_name(description)
        dictmatchedinfofinal={}
        # 2) Match dimension
        dimension_matched = self.match_dimension_to_size(dimension)
        product_matched = dimension_matched

        # 3) If dimension not matched, check alternate dimension
        if not dimension_matched:
            alt_dimension = self.match_alternate_dimension(dimension)
            if alt_dimension and self.match_dimension_to_size(alt_dimension):
                # Update dimension if alt found
                dimension = alt_dimension
                dimension_matched = True
                product_matched = dimension_matched

        # 4) Build matched indices
        matched_indices = self.get_matched_indices(dimension, product_name, dimension_matched, product_matched)
        if not matched_indices:
            # No match => return empty
            return {}

        # 5) Get embeddings & do FAISS if matched
        all_matched_entities = self.build_matched_entities_list(matched_indices)
        embed_results = await self.get_embeddings_from_openai(description, all_matched_entities)

        desc_embed = embed_results['desc_embed']
        entity_embeds = embed_results['entity_embeds']

        final_idx = self.calculate_faiss_similarity(desc_embed, entity_embeds, matched_indices)
        dictmatchedinfo = None

        # 6) Normalize and match product_name (with spaces removed) with Product Name in dictmatchedinfo (spaces removed)
        if final_idx is not None:
            dictmatchedinfo = self.simpolo_price_lst.loc[final_idx].to_dict()

            # Normalize product_name: Remove spaces, normalize case, and handle special characters
            product_name_no_spaces = product_name.replace(" ", "").lower()
            
            # Remove dimension-like parts (e.g., 'GV790X3000X15', '3PC', 'X3000', 'MM', etc.)
            normalized_product_name = re.sub(r'(\d+[xX]\d+[xX]\d+|\d+PC|\d+MM)', '', product_name_no_spaces)

            product_name_match = None
            for idx, product in dictmatchedinfo['Product Name'].items():
                # Remove spaces and normalize case for the product name in dictmatchedinfo
                product_no_spaces = product.replace(" ", "").lower()

                # Remove dimension-like parts (e.g., 'GV790X3000X15', '3PC', 'X3000', 'MM', etc.)
                normalized_dict_product = re.sub(r'(\d+[xX]\d+[xX]\d+|\d+PC|\d+MM)', '', product_no_spaces)

                # Match if normalized product_name_no_spaces is a substring of normalized_dict_product
                if normalized_product_name in normalized_dict_product:
                    product_name_match = idx
                    break  # Assuming you want the first match

            # If a match is found, use that index to fetch the final row
            if product_name_match is not None:
                dictmatchedinfofinal = self.simpolo_price_lst.loc[product_name_match].to_dict()

        # Return the final matched dictionary or empty if no match
        return dictmatchedinfofinal if dictmatchedinfofinal else {}

class AquantItemMatcher:
    """
    A class to find the best match for a product description based on a given price list.
    
    Methods:
    --------
    preprocess_string(strText: str) -> str:
        Remove spaces and special characters from a string and convert to uppercase.

    similarity_score(strA: str, strB: str) -> float:
        Calculate similarity score between two strings using SequenceMatcher.

    find_best_match(
        strInput: str, 
        dfPriceList: pd.DataFrame, 
        lstColumns: list, 
        strFilterColumn: str,
        strMRPColumnName: str
    ) -> dict:
        Find the best matching row based on the first filter column.

    Example:
    --------
    dfPriceList = pd.read_excel("Aquant Product Database Vol 13 Jan 2024 (1).xlsx")
    strInput = " A-2123 BRG-BRASS THERMOSTATIC DIVERTER WITH FIVE O"
    lstColumns = ["PRODUCT CODE", "PRODUCT DESCRIPTION"]
    strFilterColumn = "PRODUCT CODE"
    strMRPColumnName = "MRP"

    matcher = AquantItemMatcher()
    dictBestMatch = matcher.find_best_match(strInput, dfPriceList, lstColumns, strFilterColumn, strMRPColumnName)
    print(f"Final Best Match: {dictBestMatch}")
    """

    @staticmethod
    def preprocess_string(strText: str) -> str:
        """
        Remove spaces and special characters from a string and convert to uppercase.

        Parameters:
        -----------
        strText : str
            The input string to preprocess.

        Returns:
        --------
        str
            The preprocessed string in uppercase without spaces and special characters.
        """
        return re.sub(r'[^a-zA-Z0-9]', '', strText).upper()

    @staticmethod
    def similarity_score(strA: str, strB: str) -> float:
        """
        Calculate similarity score between two strings using SequenceMatcher.

        Parameters:
        -----------
        strA : str
            The first input string.
        strB : str
            The second input string.

        Returns:
        --------
        float
            Similarity score between the two input strings (0 to 1).
        """
        return SequenceMatcher(None, strA, strB).ratio()

    def find_best_match(
        self, 
        strInput: str, 
        dfPriceList: pd.DataFrame, 
        lstColumns: list, 
        strFilterColumn: str, 
        strMRPColumnName:str
    ) -> dict:
        """
        Find the best matching row based on the first filter column.

        Parameters:
        -----------
        strInput : str
            The input string to match.
        dfPriceList : pd.DataFrame
            The dataframe to search in.
        lstColumns : list
            List of column names to consider.
        strFilterColumn : str
            The primary column for filtering.

        Returns:
        --------
        dict
            A dictionary with column names as keys, corresponding row values, and confidence score.
        """
        
        # Preprocess the input string
        strProcessed = self.preprocess_string(strInput)

        # Preprocess the first filter column in the dataframe
        dfPriceList[strFilterColumn + '_processed'] = dfPriceList[strFilterColumn].astype(str).apply(self.preprocess_string)

        # First, check for exact substring matches
        dfPriceList['exact_match'] = dfPriceList[strFilterColumn + '_processed'].apply(lambda strX: strProcessed in strX or strX in strProcessed)

        if dfPriceList['exact_match'].any():
            rowBestMatch = dfPriceList[dfPriceList['exact_match']].iloc[0]
            fltConfidenceScore = 1.0  # Exact match gets full confidence

        else:
            # Compute similarity scores for all entries
            dfPriceList['similarity_score'] = dfPriceList[strFilterColumn + '_processed'].apply(lambda strX: self.similarity_score(strProcessed, strX))

            # Sort by highest similarity score
            rowBestMatch = dfPriceList.loc[dfPriceList['similarity_score'].idxmax()]
            fltConfidenceScore = round(rowBestMatch["similarity_score"], 2)

        # Construct result dictionary
        dictResult = {strCol: rowBestMatch[strCol] for strCol in lstColumns}
        dictResult[strMRPColumnName] = rowBestMatch[strMRPColumnName]
        dictResult["confidence_score"] = fltConfidenceScore
        # Check if the value is a valid numeric string
        strMRPValue = str(dictResult[strMRPColumnName]).strip()

        # Remove non-numeric characters (if any)
        mrp_value_clean = ''.join(filter(str.isdigit, strMRPValue))

        # Convert to integer if it's a valid numeric string
        if mrp_value_clean:
            dictResult[strMRPColumnName] = float(mrp_value_clean)

        return dictResult

    # def find_best_match_aquant(self, strInput: str, dfPriceList: pd.DataFrame, lstColumns: list, 
    #                             strFilterColumn: str, strMRPColumnName: str) -> dict:
    #     """
    #     Find the best matching row for Aquant products by prioritizing exact product code prefix matching 
    #     with specifier validation.
        
    #     If the input includes an explicit specifier (e.g., "CP"), then only rows that include that specifier 
    #     in the product code (e.g., "4006-CP") are valid. This prevents a fallback to a numeric-only match.
        
    #     Parameters:
    #     -----------
    #     strInput : str
    #         The input string to match (e.g., "4006 CP-Brass Rain Shower").
    #     dfPriceList : pd.DataFrame
    #         The dataframe containing the price list.
    #     lstColumns : list
    #         List of column names to include in the result (e.g., ["PRODUCT CODE", "PRODUCT DESCRIPTION"]).
    #     strFilterColumn : str
    #         The column containing product codes (e.g., "PRODUCT CODE").
    #     strMRPColumnName : str
    #         The column containing MRP values (e.g., "MRP").
        
    #     Returns:
    #     --------
    #     dict
    #         A dictionary with column values and a confidence score.
    #         Returns {"confidence_score": 0.0} if no match.
    #     """
    #     import re
    #     import pandas as pd

    #     # --- Step 0. Extract the numeric prefix from the input string ---
    #     # We use this to require an exact numeric match.
    #     num_prefix_match = re.match(r'\d+', strInput)
    #     if not num_prefix_match:
    #         return {strMRPColumnName: "-", "confidence_score": 0.0}
    #     input_numeric_prefix = num_prefix_match.group(0)

    #     # --- Step 1. Extract an explicit specifier from the input string, if present ---
    #     # The regex looks for a numeric part followed by a space or hyphen and at least 2 uppercase letters.
    #     product_code_pattern = re.compile(r'\b(\d+)[\s-]+([A-Z]{2,})\b')
    #     code_match = product_code_pattern.search(strInput)
    #     explicit_specifier = None
    #     if code_match:
    #         # For example, from "4006 CP-Brass Rain Shower", we capture "CP"
    #         explicit_specifier = code_match.group(2).lower()  # normalize to lower case for matching

    #     # --- Step 2. Perform phrase replacements when no explicit specifier is available ---
    #     # Using descending-length ordering to prioritize longer phrases.
    #     specifier_mapping = {
    #         "brushed rose gold": "brg",   # e.g., mapping "brushed rose gold" to "brg"
    #         "rose gold white": "rgw",
    #         "brushed gold": "bg",
    #         "graphite grey": "gg",
    #         "white": "wht",
    #         "matt black": "mb",
    #         # Add more mappings as needed.
    #     }
        
    #     strInput_lower = strInput.lower()
    #     if not explicit_specifier:
    #         for key in sorted(specifier_mapping, key=len, reverse=True):
    #             pattern = r'\b' + re.escape(key) + r'\b'
    #             if re.search(pattern, strInput_lower):
    #                 strInput_lower = re.sub(pattern, specifier_mapping[key], strInput_lower)
    #     # If an explicit specifier exists, we leave that part intact.
    #     strInput = strInput_lower

    #     # --- Step 3. Define normalization helper functions ---
    #     def normalize_for_prefix(text):
    #         # Remove "mm" and any spaces or hyphens, then lowercase.
    #         text = re.sub(r'(\d+)mm', r'\1', text)
    #         text = re.sub(r'[- ]', '', text)
    #         return text.lower()

    #     def normalize_for_words(text):
    #         # Remove "mm", replace hyphens with spaces, then split into tokens.
    #         text = re.sub(r'(\d+)mm', r'\1', text)
    #         text = re.sub(r'-', ' ', text)
    #         return text.lower().split()

    #     input_prefix = normalize_for_prefix(strInput)
    #     input_words = normalize_for_words(strInput)

    #     # --- Step 4. Tokenize and normalize the price list product codes ---
    #     dfPriceList['parts'] = dfPriceList[strFilterColumn].astype(str).apply(
    #         lambda x: [normalize_for_prefix(part)
    #                 for part in re.split(r'[ -]', x) if part]
    #     )

    #     # --- Step 5. Find matching rows with strict criteria ---
    #     # If an explicit specifier is present in the input, only consider rows that contain that specifier.
    #     matches = []
    #     for idx, row in dfPriceList.iterrows():
    #         parts = row['parts']
    #         # Check that the first part (numeric prefix) exactly equals the input numeric prefix.
    #         if len(parts) > 0 and parts[0] == input_numeric_prefix:
    #             # If the input has an explicit specifier, only match rows that have a specifier and where it exactly matches.
    #             if explicit_specifier:
    #                 if len(parts) > 1 and parts[1] == explicit_specifier:
    #                     matches.append((idx, row))
    #             else:
    #                 # No explicit specifier in the input: if the row does not provide a specifier, it is acceptable.
    #                 if len(parts) == 1:
    #                     matches.append((idx, row))
    #                 else:
    #                     # Otherwise, check if at least one of the additional tokens is present among the input words.
    #                     if any(parts[1] == word for word in input_words):
    #                         matches.append((idx, row))
        
    #     # If no proper matches are found, then return "no match".
    #     if not matches:
    #         return {strMRPColumnName: "-", "confidence_score": 0.0}

    #     # --- Step 6. Choose the best match ---
    #     # We choose the row with the most parts (most specific) among the valid matches.
    #     best_match_idx, best_match_row = max(matches, key=lambda x: len(x[1]['parts']))
    #     confidence_score = 1.0

    #     result = {col: best_match_row[col] for col in lstColumns}
    #     result[strMRPColumnName] = best_match_row[strMRPColumnName]
    #     result["confidence_score"] = confidence_score

    #     # Clean and convert the MRP value by removing non-digit characters.
    #     mrp_value = str(result[strMRPColumnName]).strip()
    #     mrp_clean = ''.join(filter(str.isdigit, mrp_value))
    #     result[strMRPColumnName] = float(mrp_clean) if mrp_clean else "-"

    #     return result

    def find_best_match_aquant(self, strInput: str, dfPriceList: pd.DataFrame, lstColumns: list, 
                                strFilterColumn: str, strMRPColumnName: str) -> dict:
        """
        Find the best matching row for Aquant products by prioritizing exact product code prefix matching 
        with specifier validation.
        
        If the input includes an explicit specifier (e.g., "CP"), then only rows that include that specifier 
        in the product code (e.g., "4006-CP") are valid. This prevents a fallback to a numeric-only match.
        
        Parameters:
        -----------
        strInput : str
            The input string to match (e.g., "4006 CP-Brass Rain Shower").
        dfPriceList : pd.DataFrame
            The dataframe containing the price list.
        lstColumns : list
            List of column names to include in the result (e.g., ["PRODUCT CODE", "PRODUCT DESCRIPTION"]).
        strFilterColumn : str
            The column containing product codes (e.g., "PRODUCT CODE").
        strMRPColumnName : str
            The column containing MRP values (e.g., "MRP").
        
        Returns:
        --------
        dict
            A dictionary with column values and a confidence score.
            Returns {"confidence_score": 0.0} if no match.
        """
        import re
        import pandas as pd

        # --- Step 0. Extract the numeric prefix from the input string ---
        num_prefix_match = re.match(r'\d+', strInput)
        if not num_prefix_match:
            return {strMRPColumnName: "-", "confidence_score": 0.0}
        input_numeric_prefix = num_prefix_match.group(0)

        # --- Step 1. Extract an explicit specifier from the input string, if present ---
        # The regex looks for a numeric part followed by a space or hyphen and at least 2 uppercase letters.
        product_code_pattern = re.compile(r'\b(\d+)[\s-]+([A-Z]{2,})\b')
        code_match = product_code_pattern.search(strInput)
        explicit_specifier = None
        if code_match:
            # Capture the specifier (normalize it to lower case for matching)
            explicit_specifier = code_match.group(2).lower()

        # --- Step 2. Perform phrase replacements when no explicit specifier is available ---
        specifier_mapping = {
            "brushed rose gold": "brg",   # e.g., mapping "brushed rose gold" to "brg"
            "rose gold white": "rgw",
            "brushed gold": "bg",
            "graphite grey": "gg",
            "white": "wht",
            "matt black": "mb",
            # Add more mappings as needed.
        }
        strInput_lower = strInput.lower()
        if not explicit_specifier:
            for key in sorted(specifier_mapping, key=len, reverse=True):
                pattern = r'\b' + re.escape(key) + r'\b'
                if re.search(pattern, strInput_lower):
                    # Replace longer phrases with their mapped abbreviations.
                    strInput_lower = re.sub(pattern, specifier_mapping[key], strInput_lower)
        # Update the input string with modifications (if any).
        strInput = strInput_lower

        # --- Step 3. Define normalization helper functions ---
        def normalize_for_prefix(text):
            # Remove "mm" and any spaces or hyphens, then lowercase.
            text = re.sub(r'(\d+)mm', r'\1', text)
            text = re.sub(r'[- ]', '', text)
            return text.lower()

        def normalize_for_words(text):
            # Remove "mm", replace hyphens with spaces, then split into tokens.
            text = re.sub(r'(\d+)mm', r'\1', text)
            text = re.sub(r'-', ' ', text)
            return text.lower().split()

        input_prefix = normalize_for_prefix(strInput)
        input_words = normalize_for_words(strInput)

        # --- Step 4. Tokenize and normalize the price list product codes ---
        dfPriceList['parts'] = dfPriceList[strFilterColumn].astype(str).apply(
            lambda x: [normalize_for_prefix(part)
                    for part in re.split(r'[ -]', x) if part]
        )

        # --- Step 5. Find matching rows with refined criteria ---
        matches = []
        for idx, row in dfPriceList.iterrows():
            parts = row['parts']
            # First, ensure the numeric prefix (first token) matches exactly.
            if len(parts) > 0 and parts[0] == input_numeric_prefix:
                # If an explicit specifier was detected in the input,
                # check if it appears in any of the tokens after the numeric prefix
                # (ignoring tokens that are purely numeric).
                if explicit_specifier:
                    if any(token == explicit_specifier for token in parts[1:] if not token.isdigit()):
                        matches.append((idx, row))
                else:
                    # No explicit specifier in the input.
                    # If there are no non-numeric tokens (i.e., no specifier provided in product code), accept the row.
                    if not any(not token.isdigit() for token in parts[1:]):
                        matches.append((idx, row))
                    else:
                        # Otherwise, require that at least one non-numeric token in the product code
                        # appears in the normalized input words.
                        if any(token in input_words for token in parts[1:] if not token.isdigit()):
                            matches.append((idx, row))

        # If no proper matches are found, return no match.
        if not matches:
            return {strMRPColumnName: "-", "confidence_score": 0.0}

        # --- Step 6. Choose the best match ---
        # Select the row with the most tokens (i.e., the most specific product code).
        best_match_idx, best_match_row = max(matches, key=lambda x: len(x[1]['parts']))
        confidence_score = 1.0

        result = {col: best_match_row[col] for col in lstColumns}
        result[strMRPColumnName] = best_match_row[strMRPColumnName]
        result["confidence_score"] = confidence_score

        # Clean and convert the MRP value by removing non-digit characters.
        mrp_value = str(result[strMRPColumnName]).strip()
        mrp_clean = ''.join(filter(str.isdigit, mrp_value))
        result[strMRPColumnName] = float(mrp_clean) if mrp_clean else "-"

        return result


if __name__ == "__main__":
    # # Example Usage
    # objMatcher = NexionItemMatcher()

    # # Example price list
    # dfPriceList = pd.read_excel(r"Data\Customer\17_ParagTraders\2_Nexion\NEXION PRICE LIST.xlsx")
    # # Example single input string
    # strInput = "TEXTURE PULPIS GRIGIO RIGATO 600x1200"


    # # Process input string
    # dictResult = objMatcher.processSingleInput(
    #     strInput=strInput,
    #     dfPriceList=dfPriceList,
    #     strSizeCol="Size(mm)",
    #     lstColumnsToConcat=["Design", "Category", "Size(mm)"],
    # )

    # # Output result as a dictionary
    # print(dictResult)

    # ------------------------------------- Simpolo Pricelist ---------------------------
    # 1) Load the price list
    simpolo_price_lst = pd.read_excel(r"Data\Customer\17_ParagTraders\1_Simpolo\Simpolo Price List.xlsx")

    # 2) Create the matcher object
    matcher = SimpoloItemMatcher(simpolo_price_lst)

    # 3) Define a single description
    single_description = "SCS SPECTRA WINE 3P GV598X1198X8.5"

    # 4) Run the pipeline for this single description
    final_dict = asyncio.run(matcher.process_single_description(single_description))

    # 5) Inspect the result
    print(final_dict)