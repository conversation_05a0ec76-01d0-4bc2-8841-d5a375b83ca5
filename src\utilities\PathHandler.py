import os
import platform
import json
from typing import Dict, <PERSON>, Optional
from pathlib import Path

# Global path dictionary
dictProjectPaths = {
    # Constant Path
    "strAdvertisementPath": (r"H:/AI Data/Advertisements"),
    
    # Abhinav Imprest
    "GRN_FILE_PATH": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/grn_register"),
    "PO_FOLDERPATH": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register"),
    "TALLY_MAP_PATH": (r"Data/Customer/Abhinav InfraBuild/Mappings/Tally_ERP_MappingsV5.xlsx"),
    "STORAGE_PATH_PRODUCTION": (r"H:/AI Data/DailyData/AbhinavInfrabuild"),
    "STORAGE_PATH_DEVELOPMENT": (r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild"),
    "Abhinav_Vendor_Path": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Resources/PartyDetails.json"),
    
    # Abhinav Infrabuild Controller
    "strAbhinavInfrabuild_ProdStoragePath": (r"H:/AI Data/DailyData/AbhinavInfrabuild"),
    "strAbhinavInfrabuild_DevStoragePath": (r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild"),
    "strAbhinavInfrabuild_VendorDetailsFilePath": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Resources/PartyDetails.json"),
    "strAbhinavInfrabuild_MappingExcelPath": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Mappings/Tally_ERP_MappingsV2.xlsx"),

    # Abhinav Infrabuild GRN
    "strAbhinavGRN_DevStoragePath": (r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild"),
    "strAbhinavGRN_ProdStoragePath": (r"H:/AI Data/DailyData/AbhinavInfrabuild"),
    "strAbhinavGRN_MappingExcelPath": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Mappings/Tally_ERP_MappingsV2.xlsx"),
    "strAbhinavPODownloadDirectory": (r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register"),

    # Gwalia
    "strGwalia_StoragePath": (r"H:/AI Data/DailyData/Gwalia"),

    # ICD Controller
    "strICD_TallyReqObjectFilePath": (r"H:/AI Data/18_FairdealInternational/TallyRequestsObject"),

    # Pegasis
    "strPegasis_DocStoragePath": (r"H:/AI Data/DailyData/ICD"),

    # Parag Traders
    "strParagTraders_StockItemDBDir": (r"H:/AI Data/17_ParagTraders/StockItemsDB"),
    "strParagTraders_StockItemDevDBDir": (r"H:/AI Data/17_ParagTraders/StockItemsDB/Developer"),
    "strParagXMLdir": (r"H:/AI Data/DailyData/ParagTraders"),
    "strDevParagXMLdir": (r"H:/AI Data/DailyData/AV Dev"),
    
    # ICON
    "strICONStockItemFilePath": (r"Data/Customer/17_ParagTraders/ICON/StockItemGroup.json"),
    
    # Quotation Paths
    "strQuotation_StockInventoryDBDir": (r"H:/AI Data/17_ParagTraders/StockItemsDB"),
    "strQuotation_DevStockInventoryDBDir": (r"H:/AI Data/17_ParagTraders/StockItemsDB/Developer"),
    "strQuotation_SaveXMLDir": (r"H:/AI Data/DailyData/ParagTraders"),
    "strQuotation_DevSaveXMLDir": (r"H:/AI Data/DailyData/AV Dev"),

    # ParagTraders_XML Class
    "strParagTradersXML_ProdStoragePath": (r"H:/AI Data/DailyData/ParagTraders"),
    "strParagTradersXML_DevStoragePath": (r"H:/AI Data/DailyData/AV Dev"),

    # Prem Textiles
    "strPremTextiles_StoragePath": (r"H:/AI Data/DailyData/PremTextiles"),

    # Tally Controller
    "strTallyController_ExportBaseDir": (r"H:/AI Data/TallyExports"),

    # Vedansh International School
    "strVedanshSchool_StoragePath": (r"H:/AI Data/DailyData/Vedansh"),

    # Advertisement 
    "strAdvertismentPath": (r"H:/AI Data/Advertisements"),
}

class CPathUtility:
    @staticmethod
    def MSValidateAndCreatePath(
        dictPathMap: Dict[str, Union[str, int]],
        strNetworkDriveLetter: str,
        strTallyConfigPath: Optional[str] = None,
        strTDLConfigPath: Optional[str] = None
    ) -> Dict[str, str]:
        """
        Validates and updates file/directory paths from a dictionary and two JSON config files.
        Automatically adjusts paths based on OS (Windows or Ubuntu).
        """

        # Detect OS and define path prefixes
        is_windows = platform.system().lower() == "windows"
        windows_prefix = r"H"
        ubuntu_prefix = "/home/<USER>/AV Ahemdabad Server"
        print(f"🖥️ OS Detected: {'Windows' if is_windows else 'Linux/Ubuntu'}")

        # Always resolve paths from current script directory
        script_dir = os.path.dirname(os.path.abspath(__file__))
        project_root = os.path.dirname(os.path.dirname(script_dir))

        if strTallyConfigPath is None:
            strTallyConfigPath = os.path.join(project_root, "resource", "TallyUserConfig.json")
        if strTDLConfigPath is None:
            strTDLConfigPath = os.path.join(project_root, "resource", "TDLUserConfig.json")
            
        # ✅ Normalize if Windows
        if is_windows:
            strTallyConfigPath = os.path.normpath(strTallyConfigPath)
            strTDLConfigPath = os.path.normpath(strTDLConfigPath)

        def _IsLikelyDirectory(strPath: str) -> bool:
            return not os.path.splitext(strPath)[1]

        def _UpdatePath(strPath: str) -> str:
            if is_windows:
                # Ensure all paths are normalized in Windows format
                if strPath.replace("/", "\\").startswith(windows_prefix):
                    return strPath  # Already correct
                else:
                    try:
                        suffix = strPath.split(":", 1)[-1].lstrip("\\/")
                        return f"{windows_prefix}:\{suffix}"
                    except:
                        return f"❌ Error accessing path: {os.path.join(windows_prefix, os.path.relpath(strPath, windows_prefix))}"
            else:
                # We're on Ubuntu — convert Windows path to Ubuntu path
                # Only convert if it starts with the windows prefix
                norm_path = strPath.replace("\\", "/")
                if norm_path.startswith(windows_prefix.replace("\\", "/")):
                    # Here used len(windows_prefix)+ 1 as we only provide letter and current dict path contains ':' so to remove it we use len + 1.
                    relative_path = norm_path[len(windows_prefix)+1:].lstrip("/")
                    return f"{ubuntu_prefix}/{relative_path}"
                else:
                    return strPath  # leave it unchanged if it doesn't match

        def _ValidateAndCreate(strKey: str, strPath: str) -> str:
            try:
                if _IsLikelyDirectory(strPath):
                    if not os.path.exists(strPath):
                        os.makedirs(strPath, exist_ok=True)
                        return f"✅ Created directory: {strPath}"
                    return f"✅ Directory exists: {strPath}"
                else:
                    return f"✅ File exists: {strPath}" if os.path.exists(strPath) else f"⚠️ File missing: {strPath}"
            except Exception as e:
                return f"❌ Error accessing path: {strPath} — {e}"

        dictPathStatusReport: Dict[str, str] = {}

        # Validate project paths
        for strPathKey, objPath in dictPathMap.items():
            if not isinstance(objPath, str):
                dictPathStatusReport[strPathKey] = f"ℹ️ Skipped (non-string value): {objPath}"
                continue
            updatedPath = _UpdatePath(objPath)
            dictPathMap[strPathKey] = updatedPath
            dictPathStatusReport[strPathKey] = _ValidateAndCreate(strPathKey, updatedPath)

        # TallyUserConfig.json
        if os.path.exists(strTallyConfigPath):
            try:
                with open(strTallyConfigPath, "r") as f:
                    tallyConfig = json.load(f)
                for email, user_data in tallyConfig.items():
                    if "userDirectory" in user_data:
                        originalPath = user_data["userDirectory"]
                        updatedPath = _UpdatePath(originalPath)
                        user_data["userDirectory"] = updatedPath

                        dictPathStatusReport[f"TallyUserConfig_userDirectory_{email}"] = _ValidateAndCreate(
                            f"TallyUserConfig_userDirectory_{email}", updatedPath
                        )
                with open(strTallyConfigPath, "w") as f:
                    json.dump(tallyConfig, f, indent=4)
                print(f"✅ Directory exists: {strTallyConfigPath} and Updated")
            except Exception as e:
                dictPathStatusReport["TallyUserConfig_userDirectory"] = f"❌ Failed to process TallyUserConfig.json — {e}"
                raise
        else:
            print(f"❌ Failed to process TallyUserConfig.json — {e}")

        # TDLUserConfig.json
        if os.path.exists(strTDLConfigPath):
            try:
                with open(strTDLConfigPath, "r") as f:
                    tdlConfig = json.load(f)
                for user_id, user_data in tdlConfig.items():
                    if "dataDirectory" in user_data:
                        original_path = user_data["dataDirectory"]
                        path_parts = original_path.split(":\\", 1)
                        if len(path_parts) > 1:
                            if is_windows:
                                user_data["dataDirectory"] = f"{strNetworkDriveLetter}:\\{path_parts[1]}"
                            else:
                                user_data["dataDirectory"] = os.path.join(
                                    ubuntu_prefix, path_parts[1].replace("\\", "/")
                                )
                        
                with open(strTDLConfigPath, 'w') as file:
                    json.dump(tdlConfig, file, indent=4)
                print(f"✅ Directory exists: {strTDLConfigPath} and Updated")
            except Exception as e:
                dictPathStatusReport["TDLUserConfig_dataDirectory"] = f"❌ Failed to process TDLUserConfig.json — {e}"
                raise
        else:
            print(f"❌ Failed to process TDLUserConfig— {e}")

        return dictPathStatusReport


# Run the utility
if __name__ == "__main__":
    dictStatusReport = CPathUtility.MSValidateAndCreatePath(dictProjectPaths, "K")
    for key, value in dictStatusReport.items():
        print(f"{key}: {value}")
