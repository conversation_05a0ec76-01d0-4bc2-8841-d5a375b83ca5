import logo from '../../assets/logo.svg'; // Ensure the logo is correctly located
import RegistrationTick from '../../assets/registrationtick.svg'; // Import check icon SVG as a component

export default function WelcomePage() {
    // const navigate = useNavigate();


    const handleStart = () => {
        localStorage.setItem("token", localStorage.getItem("Authtoken"))
        localStorage.removeItem("Authtoken");
        window.location.href = '/'; // Change to where you want to redirect after start
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#ffff]">
            <div className="flex justify-center">
                <img src={logo} alt="AccuVelocity" className="h-12 w-auto mb-6" />
            </div>
            <div className="text-center">
                <div className="flex justify-center mb-6 my-10">
                    <img src={RegistrationTick} alt="Account created successfully" className="h-50 w-auto" />
                </div>
                <h3 className="text-xl font-semibold mb-2 mt-12" >Account created successfully!</h3>
                <p className="text-gray-400 mb-8" >Welcome aboard! Start your success journey with AccuVelocity!</p>
                <button 
                    onClick={handleStart} 
                    className="py-2 px-8 mt-10 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                >
                    Let&apos;s Start!
                </button>
            </div>
        </div>
    );
}