import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { But<PERSON> } from "@material-tailwind/react";
import AddVendorsModal from '../components/Models/addVendors';
import DuplicateVendorsModal from '../components/Models/duplicateVendor';

import { useNavigate } from 'react-router-dom';
import { MdDelete} from "react-icons/md";
import toast, { Toaster } from "react-hot-toast";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import loading from '/animations/loading.svg';
import modelEditImage from '../assets/contract_editmodify_model.png';
import { MdOutlineContentCopy } from "react-icons/md";
import { IoMdAdd } from "react-icons/io";
import { CiCircleInfo } from "react-icons/ci";
import { Tooltip } from "@material-tailwind/react";
import { setCookie, getCookie } from '../utils/cookieUtils';
import filterSVG from "../assets/SVGs/filterSvg.svg"

const AllModelList = () => {
    const [AllVendors, setVendors] = useState([]);
    const [AllModelFamilies, setModelFamilies] = useState([]);
    const [activePage, setActivePage] = useState(() => {
        const savedPage = getCookie('activePageModel');
        return savedPage ? Number(savedPage) : 1;
    });

    const [totalPages, setTotalPages] = useState(0);
    const [isModalOpen, setModalOpen] = useState(false);
    const [isDuplicateModalOpen, setDuplicateModalOpen] = useState(false);
    const [isLoading, setIsLoading] = useState(true)
    const itemsPerPage = 10;
    const navigate = useNavigate()
    const [selectedModels, setSelectedModels] = useState({});
    const [IsActionBtnAllow, setIsActionBtnAllow] = useState(false);
    const [SelectedModelForDuplicate, setSelectedModelForDuplicate] = useState("");
    const [SelectedModelFamilyForDuplicate, setSelectedModelFamilyForDuplicate] = useState("");
    const [SelectedModelIDForDuplicate, setSelectedModelIDForDuplicate] = useState("");
    const [sorting, setSorting] = useState({
        modelNameAsc: null,
        modelFamilyNameAsc: null,
        totalDocs: null,
    });

    useEffect(() => {
        setIsActionBtnAllow(Object.keys(selectedModels).length > 0);
    }, [selectedModels, IsActionBtnAllow]);

    const calculateIndex = (index) => {
        return (activePage - 1) * itemsPerPage + index + 1;
    };

    const toggleModal = () => setModalOpen((prev) => !prev);

    // const toggleAddDuplicateModal = () => 
    const toggleAddDuplicateModal = () => {
        const selectedVendorKeys = Object.keys(selectedModels).filter(key => selectedModels[key]);

        if (selectedVendorKeys.length === 0) {
            toast.error("Please select one model to duplicate.");
            return;
        }
        else if (selectedVendorKeys.length > 1) {
            toast.error("You can only duplicate single model at a time.");
            return;
        }

        selectedVendorKeys.forEach(key => {
            const [Id, name, family] = key.split('-');
            setSelectedModelIDForDuplicate(Id);
            setSelectedModelForDuplicate(name);
            setSelectedModelFamilyForDuplicate(family);
            setDuplicateModalOpen((prev) => !prev);
        });
    };


    const fetchData = useCallback ( async () => {
        try {
            let queryString = `?iLimit=${itemsPerPage}&iPage=${activePage}`;

            if (sorting.modelNameAsc !== null) {
                queryString += `&bModelNameAsc=${sorting.modelNameAsc ? '1' : '0'}`;
            }
            if (sorting.modelFamilyNameAsc !== null) {
                queryString += `&bModelFamilyNameAsc=${sorting.modelFamilyNameAsc ? '1' : '0'}`;
            }
            if (sorting.totalDocs !== null) {
                queryString += `&bTotalDocsAsc=${sorting.totalDocs ? '1' : '0'}`;
            }

            const response = await axios.get(`${import.meta.env.VITE_SERVER}/Model/Model/${queryString}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            const filteredVendors = response.data.Models.filter(vendor => vendor.Name !== 'General');
            setIsLoading(false)
            setVendors(filteredVendors);
            setCookie('activePageModel', activePage, 1);
            const filteredModelFamilies = response.data.Model_Families.filter(family => family !== "Default");
            setModelFamilies(filteredModelFamilies);
            setTotalPages(response.data.pagination.total_pages);

        } catch (error) {
            setIsLoading(false)
            console.error('Failed to fetch users', error);
        }
    },[activePage, sorting]); 
    
    useEffect(() => {
        // Only call fetchData if isDone is true
        setIsLoading(true)
        fetchData();
        setTimeout(() => {
            setIsLoading(false)
        }, 1000)
        // }
    }, [fetchData]);

    const toggleSort = (field) => {
        // Set the current field to the toggled value and others to null
        setSorting({
            modelNameAsc: field === 'modelName' ? !sorting.modelNameAsc : null,
            modelFamilyNameAsc: field === 'modelFamilyName' ? !sorting.modelFamilyNameAsc : null,
            totalDocs: field === 'totalDocuments' ? !sorting.totalDocs : null
        });
    };

    const toggleCheckbox = (model) => {
        const key = `${model.Id}-${model.Name}-${model.FamilyName}`;
        setSelectedModels(prev => {
            const newSelectedModels = { ...prev };

            if (newSelectedModels[key]) {
                delete newSelectedModels[key];
            } else {
                newSelectedModels[key] = true;
            }

            return newSelectedModels;
        });
    };

    const handleDeleteSelectedVendors = () => {
        const selectedVendorKeys = Object.keys(selectedModels).filter(key => selectedModels[key]);
        if (selectedVendorKeys.length === 0) {
            toast.error("Please select at least one model to delete.");
            return;
        }
        if (window.confirm("Are you sure you want to delete all selected models?")) {
            selectedVendorKeys.forEach(key => {
                const [Id, name, family] = key.split('-');
                handleDeleteVendor(Id, name, family);
            });
        }
    };

    const prevPage = () => {
        setActivePage(activePage - 1);
        
    };

    const nextPage = () => {
        setActivePage(activePage + 1);
        
    };

    const handleDeleteVendor = async (iModelId, vendorName, vendorFamily) => {

        try {
            const forbiddenModels = ["demo finance", "demo hr", "demo medical insurance"];

            // Do not allow to delete demo models
            if (forbiddenModels.includes(vendorFamily.trim().toLowerCase())) {
                // Display a toast message indicating that deletion is not allowed
                toast.error("You cannot delete the built-in 'Demo' Models.");
                return;
            }

            const response = await axios.delete(`${import.meta.env.VITE_SERVER}/Model/Model?iModelId=${iModelId}&strModelName=${vendorName}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                toast.success(`Successfully Deleted Model ${vendorName}`)

                setTimeout(() => {
                    window.location.reload();
                }, 1000);  // 1000 milliseconds = 1 seconds

            } else {
                toast.error(`Failed to Delete Model ${vendorName}!`)
            }

        } catch (error) {
            toast.error("Error Occured during Deletion!");
        }

    };

    return (
        <>

            <div className="px-6 py-2 pt-4">
                <h1 className="text-3xl font-semibold text-[#3F3F3F]">All Models</h1>
            </div>

            <div className="p-6 bg-[#ffff] m-7 rounded-xl shadow-lg  style={{ minHeight: 'calc(100vh - 200px)' }}">
                <Toaster position="top-center"></Toaster>

                <DuplicateVendorsModal isOpen={isDuplicateModalOpen} onClose={toggleAddDuplicateModal} modelFamilyList={AllModelFamilies} selectedModelID={SelectedModelIDForDuplicate} selectedModel={SelectedModelForDuplicate} selectedModelFamily={SelectedModelFamilyForDuplicate} />

                <AddVendorsModal isOpen={isModalOpen} onClose={toggleModal} modelFamilyList={AllModelFamilies} />
                <div className="px-6 py-2 pt-4">

                    <div className="flex justify-between items-center mb-6 w-full">
                        <span className='text-3xl font-bold text-[#003654]'>My Models</span>

                        <div className="flex-shrink-0 flex items-center space-x-3">
                            <Tooltip content={IsActionBtnAllow ? "Click to delete the selected model." : "Select a model to enable delete action."}>
                                <button
                                    className={` text-sm xl:text-xs py-2 px-4 rounded-2xl inline-flex items-center h-full  font-bold ${IsActionBtnAllow ? 'bg-red-600 text-white hover:bg-red-700 cursor-pointer' : 'bg-gray-400 text-gray-700 cursor-not-allowed'}`}
                                    onClick={handleDeleteSelectedVendors}
                                    disabled={!IsActionBtnAllow}
                                >
                                    <MdDelete className="h-5 w-6" />
                                    Delete Model
                                </button>
                            </Tooltip>

                            <Tooltip content={IsActionBtnAllow ? "Click to duplicate the selected model." : "Select a model to enable duplicate action."}>
                                <button
                                    className={`text-sm xl:text-xs py-2 px-4 rounded-2xl inline-flex items-center h-full font-bold ${IsActionBtnAllow ? 'bg-[#003654] text-white hover:bg-[#002744] cursor-pointer' : 'bg-gray-400 text-gray-700 cursor-not-allowed'}`}
                                    onClick={toggleAddDuplicateModal}
                                    disabled={!IsActionBtnAllow}
                                >
                                    <MdOutlineContentCopy className="h-5 w-6" />
                                    Duplicate Model
                                </button>
                            </Tooltip>

                         <Tooltip content="Click to Add New model.">
                            <button
                                type="button"
                                className={`text-sm mx-3 py-2 px-4 rounded-2xl inline-flex items-center h-full font-bold  bg-[#003654] text-white hover:bg-[#002744]`}
                                onClick={toggleModal}
                            >
                                <IoMdAdd className="h-5 w-6" />
                                Add New Model
                            </button>
                            </Tooltip>
                        </div>
                    </div>

                    {isLoading ? (
                        <div className="flex justify-center">
                            <img src={loading} className='h-[20vh]' alt="No data available" />
                        </div>
                    ) : (
                        AllVendors.length === 0 ? (

                            <div className="flex justify-center">
                                <img src={`/animations/noData.png`} className='h-[50vh]' alt="No data available" />
                            </div>
                        ) : (
                            <div className="overflow-x-auto relative m-6" style={{ minHeight: '55vh', maxHeight: '55vh' }}>
                                <table className="min-w-full leading-normal">
                                    <thead className='sticky top-0 bg-white'>

                                        <tr className="items-center text-left text-gray-700">
                                            <th className="px-5 py-3  w-[1vw] border-b-2 border-gray-200 font-medium">

                                            </th>
                                            <th className="px-5 py-3 w-[2vw] border-b-2 border-gray-200 font-medium">
                                                Sr No
                                            </th>
                                            <th className="px-5 py-3 w-[6vw] border-b-2 border-gray-200 font-medium cursor-pointer" onClick={() => toggleSort('modelFamilyName')}>
                                                <div className='flex'>
                                                    Document Model Family
                                                    <span className=''><img className='mt-1.5 ml-1' src={filterSVG} /></span> 
                                                    <Tooltip content={
                                                            <div>
                                                                The category that best fits your document(e.g., Demo_Accounting, Demo_HR).<br />
                                                                Organizing your models into families makes it easier to manage and identify them.
                                                            </div>
                                                        }>
                                                        <div>
                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>

                                            </th>
                                            <th className="px-5 py-3 w-[6vw] border-b-2 border-gray-200 font-medium cursor-pointer" onClick={() => toggleSort('modelName')}>
                                                <div className='flex'>
                                                    Document Model 
                                                    <span className=''><img className='mt-1.5 ml-1' src={filterSVG} /></span> 
                                                    <Tooltip content={
                                                <div>
                                                    The specific type of document you are uploading (e.g., Invoice, Receipt). <br />This helps AccuVelocity apply the correct extraction model for accurate data processing.
                                                                </div>
                                                                    }>
                                                            <div>
                                                                <CiCircleInfo className="ml-2 text-xl" />
                                                            </div>
                                                        </Tooltip>
                                                </div>
                                            </th>

                                            <th className="px-5 py-3 w-[8vw] border-b-2 border-gray-200 font-medium">
                                                <div className='flex'>
                                                    Description
                                                    <Tooltip content={ <div style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                    This section provides detailed information about the model,including its architecture, parameters, performance metrics, and usage guidelines.
                                                    </div> }>
                                                        <div>
                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            </th>

                                            <th className="px-5 py-3 w-[2vw] border-b-2 border-gray-200 font-medium cursor-pointer" onClick={() => toggleSort('totalDocuments')}>
                                                <div className='flex'>
                                                    Total Documents
                                                    <span className=''><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    <Tooltip content="Total Documents processed using each models">
                                                        <div>
                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            </th>

                                            <th className="px-5 py-3 w-[2vw] border-b-2 border-gray-200 font-medium">
                                                <div className='flex'>
                                                    Action
                                                    <Tooltip content="To edit your Models details">
                                                        <div>
                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                        </div>
                                                    </Tooltip>
                                                </div>
                                            </th>
                                        </tr>
                                    </thead>

                                    <tbody className="divide-y divide-gray-200 overflow-y-auto">
                                        {AllVendors?.map((vendor, index) => (


                                            <tr key={index} className={`odd:bg-gray-50 even:bg-[#ffff] hover:bg-gray-200 h-10`}>
                                                <td className="px-5 py-3 w-[2vw] border-b border-gray-200">
                                                    <input
                                                        type="checkbox"
                                                        checked={selectedModels[`${vendor.Id}-${vendor.Name}-${vendor.FamilyName}`] || false}
                                                        onChange={() => toggleCheckbox(vendor)}
                                                    />
                                                </td>
                                                <td className="px-5 py-3 w-[2vw] border-b border-gray-200">{calculateIndex(index)}</td>

                                                {vendor?.FamilyName?.length > 15 ? (
                                                    <td
                                                        className="px-5 py-3 w-[6vw] border-b border-gray-200 cursor-pointer "
                                                        style={{
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis'
                                                        }}
                                                    >
                                                        <Tooltip content={vendor.FamilyName}>
                                                            <div>
                                                                {vendor?.FamilyName?.substring(0, 15)}...
                                                            </div>
                                                        </Tooltip>
                                                    </td>
                                                ) : (
                                                    <td
                                                        className="px-5 py-3 w-[6vw] border-b border-gray-200 cursor-pointer "
                                                    >
                                                        {vendor.FamilyName}
                                                    </td>
                                                )}
                                                
                                                {vendor?.Name?.length > 15 ? (
                                                <td
                                                    className="px-5 py-3 w-[6vw] border-b border-gray-200 cursor-pointer truncate" onClick={() => navigate('/AddModelPage', { state: { vName: vendor.Name, vFamilyName: vendor.FamilyName, iModelId: vendor.Id } })}
                                                    style={{
                                                        whiteSpace: 'nowrap',
                                                        overflow: 'hidden',
                                                        textOverflow: 'ellipsis'
                                                    }}
                                                >
                                                    <Tooltip content={vendor.Name}>
                                                        <div>
                                                            {vendor?.Name?.substring(0, 15)}...
                                                        </div>
                                                    </Tooltip>
                                                </td>
                                                ) : (
                                                <td
                                                    className="px-5 py-3 w-[6vw] border-b border-gray-200 cursor-pointer truncate"
                                                    onClick={() => navigate('/AddModelPage', { state: { vName: vendor.Name, vFamilyName: vendor.FamilyName, iModelId: vendor.Id } })}
                                                >
                                                    {vendor.Name}
                                                </td>
                                                )}
                                                {vendor?.Description?.length > 65 ? (
                                                    <td
                                                        className="px-5 py-3 w-[8vw] border-b border-gray-200  truncate"
                                                        style={{
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis'
                                                        }}
                                                    >
                                                        <Tooltip content={ <div style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                        {vendor?.Description ? vendor.Description : 'N/A'}
                                                        </div>  }  >
                                                            <div className='w-full'>
                                                                {vendor?.Description?.substring(0, 65)}...
                                                            </div>
                                                        </Tooltip>
                                                    </td>
                                                ) : (
                                                    <td
                                                        className="px-5 py-3 w-[8vw] border-b border-gray-200  truncate"
                                                    >
                                                        {vendor.Description ? vendor.Description : 'N/A'}
                                                    </td>
                                                )}

                                                <td className="px-5 py-3 w-[2vw] border-b border-gray-200">{vendor.Summary["Total Invoices"]}</td>
                                                <td className="px-5 py-3 w-[2vw] border-b border-gray-200 text-center">
                                                    <button
                                                        onClick={() => navigate('/AddModelPage', { state: { vName: vendor.Name, vFamilyName: vendor.FamilyName, iModelId: vendor.Id } })}
                                                        className="mx-auto hover:bg-gray-300 text-white rounded-xl cursor-pointer flex items-center justify-center"
                                                        style={{ display: 'flex', alignItems: 'center', justifyContent: 'center', width: '100%', height: '100%' }}
                                                    >
                                                        <img src={modelEditImage} alt="Modify" className="mx-auto h-auto rounded-xl shadow-md" />
                                                    </button>
                                                </td>
                                            </tr>
                                        ))}
                                    </tbody>
                                </table>
                            </div>
                        ))}

                    {/* Pagination buttons */}
                    <div className="flex justify-end items-center mt-6" style={{ position: 'sticky', bottom: '20px', backgroundColor: '#fff', borderTop: '1px solid #e5e7eb', padding: '10px 0', zIndex: 10 }}>
                        <Button
                            variant="text"
                            onClick={prevPage}
                            disabled={activePage === 1}
                        >
                            <IoIosArrowBack strokeWidth={2} className="h-4 w-4" />
                        </Button>

                        {totalPages <= 3 ? (
                            Array.from({ length: totalPages }, (_, index) => (
                                <button
                                    key={index}
                                    className={`px-3 py-1 ${activePage == index + 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md xl:text-xs`}
                                    onClick={() => setActivePage(index + 1)}
                                >
                                    {index + 1}
                                </button>
                            ))
                        ) : (
                            <>
                                {/* Always show the first page button */}
                                <button
                                    key={1}
                                    className={`px-3 py-1 ${activePage === 2 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md`}
                                    onClick={() => setActivePage(1)}
                                >
                                    1
                                </button>

                                {/* Ellipsis if there are pages between 1 and current page - 1 */}
                                {activePage > 3 && <span className="px-2">...</span>}

                                {/* Show current page and its neighbors */}
                                {Array.from({ length: 3 }, (_, idx) => activePage - 1 + idx)
                                    .filter(page => page > 1 && page < totalPages)
                                    .map(page => (
                                        <button
                                            key={page}
                                            className={`px-3 py-1 ${activePage === page ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md`}
                                            onClick={() => setActivePage(page)}
                                        >
                                            {page}
                                        </button>
                                    ))
                                }

                                {/* Ellipsis if there are pages between current page + 1 and last page */}
                                {activePage < totalPages - 2 && <span className="px-2">...</span>}

                                {/* Always show the last page button */}
                                <button
                                    key={totalPages}
                                    className={`px-3 py-1 ${activePage === totalPages ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md`}
                                    onClick={() => setActivePage(totalPages)}
                                >
                                    {totalPages}
                                </button>
                            </>
                        )}

                        <Button
                            variant="text"
                            onClick={nextPage}
                            disabled={activePage === totalPages}
                        >
                            <IoIosArrowForward strokeWidth={2} className="h-4 w-4" />
                        </Button>
                    </div>

                </div>
            </div>
        </>
    );
};


export default AllModelList;