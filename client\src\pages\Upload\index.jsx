import { useEffect, useState } from "react";
import { MdDelete } from "react-icons/md";
import toast, { Toaster } from 'react-hot-toast';
// import { trefoil } from 'ldrs'
import axios from "axios";
import { useNavigate } from "react-router-dom";
import { IoCheckmarkDoneCircle } from "react-icons/io5";
// import { TbLoader2 } from "react-icons/tb";
import { Tooltip } from "@material-tailwind/react";
import { ModelComponent } from '../../components/App'; // Import the ModelComponent
import { jwtDecode } from "jwt-decode";
import { Button } from "@material-tailwind/react";
import { IoMdArrowUp } from "react-icons/io";
import { useRecoilValue } from 'recoil';
import { usePageLimitLeft, useTotalPageLimitLeft, useFreePageLimitUsage, useTotalFreePageLimit, isTrialPaidDocExtractionAtom } from '../../context/TrailUsageUserData';
import { VscError } from "react-icons/vsc";
import { CiCircleInfo } from "react-icons/ci";
import { useUserNameSetter } from "../../context/userData";
import { WatchVideo } from "../../components/App";
import { getCookie, setCookieExpireInMin } from '../../utils/cookieUtils';
import UploadIcon from '../../assets/SVGs/UploadDocsIcon.svg';
import cloudeSVG from '../../assets/SVGs/Cloude.svg';
import fetchData from "../../utils/fetchData";
const VALID_FILE_TYPES = [
    'application/pdf',
    'text/plain',
    'image/jpeg',
    'image/png',
    'image/bmp',
    'image/webp',
    'application/vnd.openxmlformats-officedocument.wordprocessingml.document', // docx
    'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet', // xlsx
    'text/csv', // csv
];

const MAX_FILE_SIZE_MB = 20; // Maximum file size in MB
const MAX_FILE_SIZE_BYTES = MAX_FILE_SIZE_MB * 1024 * 1024; // Convert to bytes
const MAX_FILES_ALLOWED = 5; // Maximum number of files allowed
const MAX_FILE_NAME_SIZE = 190; // Maximum number of characters allowed

export default function MainPage() {

    const setUserName = useUserNameSetter()
    const [dragging, setDragging] = useState(false);
    const [files, setFiles] = useState([]);
    const [isUploading, setIsUploading] = useState(false);
    const navigate = useNavigate()
    const [vendors, setVendors] = useState([]);
    const [filterModels, setFilterModels] = useState([]);
    const [modelFamily, setModelFamily] = useState([]);
    const [selectedModelFamily, setSelectedModelFamily] = useState();
    const [selectedVendor, setSelectedVendor] = useState();
    const [selectedVendorID, setSelectedVendorID] = useState();
    const [selectedModelFamilyForFile, setSelectedModelFamilyForFile] = useState({});
    const [selectedModelNameForFile, setSelectedModelNameForFile] = useState({});
    const [uploadedDocIds, setUploadedDocIds] = useState([]);
    const [isInitialFetchDocPageCount, setInitialFetchPageCount] = useState(true);
    const token = localStorage.getItem('token'); // Assuming the token is stored in localStorage
    const decodedToken = jwtDecode(token);
    const userId = decodedToken.id;
    const setPageLimitLeft = usePageLimitLeft();
    const setTotalPageLimitLeft = useTotalPageLimitLeft();
    const setFreePageLimitUsage = useFreePageLimitUsage();
    const setTotalFreePageLimit = useTotalFreePageLimit();
    const [pageLimitLeft] = useState(() => {
        const cookieValue = getCookie("pageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalPageLimitLeft] = useState(() => {
        const cookieValue = getCookie("totalPageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [freePageLimitUsage] = useState(() => {
        const cookieValue = getCookie("freePageLimitUsage");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalFreePageLimit] = useState(() => {
        const cookieValue = getCookie("totalFreePageLimit");
        return cookieValue !== undefined ? cookieValue : undefined;
    });
    const isTrialPaidDocExtraction = useRecoilValue(isTrialPaidDocExtractionAtom);
    const [selectedModelDescription, setSelectedModelDescription] = useState('');

    const handleVendorChange = (event) => {
        const vendorId = event.target.value;
        const vendorName = event.target.options[event.target.selectedIndex].text;

        if (vendorId === '') {
            setSelectedVendor('Invoice');
            setSelectedVendorID(null);
        } else {
            setSelectedVendor(vendorName);
            setSelectedVendorID(vendorId);

            const selectedVendor = vendors.find(vendor => vendor.Id == vendorId);
            if (selectedVendor) {
                setSelectedModelDescription(selectedVendor.Description);
            } else {
                setSelectedModelDescription('');
            }
        }
    };

    const handleModelFamilyChange = (event) => {
        const modelFamily = event.target.value;
        if (modelFamily === '') {
            setSelectedModelFamily('Demo Finance');
        } else {
            setSelectedModelFamily(modelFamily);
        }
        const FilterModels = filterByFamilyName(vendors, modelFamily)
        setFilterModels(FilterModels)

        // Set the first vendor from the filtered models if available
        if (FilterModels.length > 0) {
            const firstVendor = FilterModels[0];
            setSelectedVendor(firstVendor.Name);
            setSelectedVendorID(firstVendor.Id);
            setSelectedModelDescription(firstVendor.Description);
        } else {
            setSelectedVendor('Invoice');
            setSelectedVendorID(null);
            setSelectedModelDescription('');
        }
    };
    const filterByFamilyName = (models, familyName) => {
        return models.filter(model => model.FamilyName === familyName);
    };


    useEffect(() => {

        if (isInitialFetchDocPageCount) {
            const fetchUserData = async () => {
                try {
                    const userResponse = await fetchData(userId)
                    const pageLimitLeft = userResponse.page_limit_left;
                    const totalPageLimitLeft = userResponse.total_allowed_page_limit;
                    const freePageLimitUsage = userResponse.free_page_limit_usage;
                    const totalFreePageLimit = userResponse.total_allowed_free_page_limit;

                    setCookieExpireInMin("pageLimitLeft", pageLimitLeft, 3);
                    setCookieExpireInMin("totalPageLimitLeft", totalPageLimitLeft, 3);
                    setCookieExpireInMin("freePageLimitUsage", freePageLimitUsage, 3);
                    setCookieExpireInMin("totalFreePageLimit", totalFreePageLimit, 3);

                    const userName = userResponse.name;
                    setUserName(userName);
                    setPageLimitLeft(pageLimitLeft)
                    setTotalPageLimitLeft(totalPageLimitLeft)
                    setFreePageLimitUsage(freePageLimitUsage)
                    setTotalFreePageLimit(totalFreePageLimit)

                } catch (error) {
                    if (error.response && error.response.data && error.response.data.detail) {
                        toast.error(error.response.data.detail);
                    } else {
                        toast.error("An unexpected error occurred"); // Fallback message
                    }
                    console.error('Error fetching user data:', error);
                }
            };

            fetchUserData();
            setInitialFetchPageCount(false);
        }
    }, [isInitialFetchDocPageCount]);

    useEffect(() => {
        fetchModelData();
    }, []);

    const fetchModelData = async () => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/Model/Model?iLimit=200`, {
                headers: {
                    "Authorization": `Bearer ${token}`
                },
            });
            setVendors(response.data.Models);
            setModelFamily(response.data.Model_Families)
            const model = response.data.Models.find(m => m.Name === 'Invoice' && m.FamilyName === 'Demo Finance');
            setSelectedModelFamily('Demo Finance')
            const FilterModels = filterByFamilyName(response.data.Models, 'Demo Finance')
            setFilterModels(FilterModels)
            setSelectedVendor(model?.Name);
            setSelectedVendorID(model?.Id);
            setSelectedModelDescription(model?.Description);


        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Failed to fetch Models', error);
        }
    };

    const addFilesWithStatus = (newFiles) => {
        const validFilesCount = files.filter(file => !file.error).length;
        if (validFilesCount + newFiles.length > MAX_FILES_ALLOWED) {
            toast.error(`You can upload a maximum of ${MAX_FILES_ALLOWED} valid files.`);
            return;
        }

        const filesWithStatus = newFiles.map((file) => ({
            name: file.name,
            type: file.type,
            size: file.size,
            uploading: false,
            uploaded: false,
            isDone: false,
            error: false,
            docId: null,
            errorMsg: null,
            timestamp: new Date(),
            selectedVendor: {
                name: file.selectedVendor?.name ? file.selectedVendor.name : selectedVendor,
                family: file.selectedVendor?.name ? file.selectedVendor.name : selectedModelFamily,
                id: file.selectedVendor?.id ? file.selectedVendor.id : selectedVendorID
            },
        }));

        setFiles((prevFiles) => [...prevFiles, ...filesWithStatus]);
    };

    const filterValidFiles = (files) => {
        // Early return if no files
        if (!files) {
            return [];
        }

        const validFiles = []

        for (const file of Array.from(files)) {

            if (!VALID_FILE_TYPES.includes(file.type)) {
                toast.error('Unsupported file type! Please upload one of the following formats: TXT, PDF, JPG, JPEG, PNG, WEBP, BMP, DOCX, XLSX, or CSV.');
            } else if (file.size > MAX_FILE_SIZE_BYTES) {
                toast.error(`${file.name} exceeds the maximum size of ${MAX_FILE_SIZE_MB} MB.`);
            } else if (file?.name?.length > MAX_FILE_NAME_SIZE) {
                toast.error(`The file name ${file.name} exceeds the maximum length of ${MAX_FILE_NAME_SIZE} characters.`);
            } else {
                validFiles.push(file);
            }
        }

        return validFiles;
    };

    const initializeModelDetails = () => {
        files.forEach((file, index) => {
            const docId = file.docId ? file.docId : null;

            if (
                docId &&
                docId !== null &&
                (!(docId in selectedModelFamilyForFile) || !(docId in selectedModelNameForFile))
            ) {
                const modelFamilyName = file.ModelFamilyFile ? file.ModelFamilyFile : selectedModelFamily;

                setSelectedModelFamilyForFile(prevState => ({
                    ...prevState,
                    [docId]: modelFamilyName
                }));
                const filterModels = filterByFamilyName(vendors, modelFamilyName)
                setSelectedModelNameForFile(prevState => ({
                    ...prevState,
                    [docId]: filterModels
                }));
            }
        });
    };
    useEffect(() => {
        initializeModelDetails();
    }, [files]);


    const handleModelFamilyChangeForFile = async (index, event) => {
        const docId = files[index].docId;
        const modelFamilyName = event.target.value;

        // Update the selectedVendor.family directly in the files array
        const updatedFiles = [...files];
        updatedFiles[index].selectedVendor.family = modelFamilyName;
        setFiles(updatedFiles);
        // Update the selected model family for the specific docId
        setSelectedModelFamilyForFile(prevState => ({
            ...prevState,
            [docId]: modelFamilyName
        }));

        // Get model names filtered by family name and update the state

        const modelNames = filterByFamilyName(vendors, modelFamilyName);
        // Set the first vendor from the filtered models if available
        if (modelNames.length > 0) {
            const firstVendor = modelNames[0];
            updatedFiles[index].selectedVendor.name = firstVendor.Name;
            updatedFiles[index].selectedVendor.id = firstVendor.Id;
            setFiles(updatedFiles);
        } else {
            updatedFiles[index].selectedVendor.name = 'Invoice';
            updatedFiles[index].selectedVendor.id = null;
            setFiles(updatedFiles);
        }
        // Replace the entire selectedModelNameForFile state with the new value
        setSelectedModelNameForFile(prevState => ({
            ...prevState,
            [docId]: modelNames
        }));

        const docID = files[index].docId;
        const vendorName = files[index].selectedVendor.name;
        const modelFamily = files[index].selectedVendor.family;
        const vendorId = files[index].selectedVendor.id;

        // Call API to Update the model id
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/UpdateModel/?iModelId=${vendorId}&DocId=${docID}&modelFamily=${modelFamily}`,
                null,
                {
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                    },
                }
            );

            if (response.status === 200) {
                const updatedFiles = [...files];
                updatedFiles[index].selectedVendor.name = vendorName;
                updatedFiles[index].selectedVendor.id = vendorId;
                updatedFiles[index].selectedVendor.family = modelFamily;
                setFiles(updatedFiles);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            toast.error("Failed to update the model name, please try again later.");
        }
    };

    const UpdateModelDetails = async (index, event) => {
        const docID = files[index].docId;
        const vendorName = event.target.options[event.target.selectedIndex].text;
        const modelFamily = files[index].selectedVendor.family;
        const vendorId = event.target.value;

        // Call API to Update the model id
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/UpdateModel/?iModelId=${vendorId}&DocId=${docID}&modelFamily=${modelFamily}`,
                null,
                {
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                    },
                }
            );

            if (response.status === 200) {
                const updatedFiles = [...files];
                updatedFiles[index].selectedVendor.name = vendorName;
                updatedFiles[index].selectedVendor.id = vendorId;
                updatedFiles[index].selectedVendor.family = modelFamily;
                setFiles(updatedFiles);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            toast.error("Failed to update the model name, please try again later.");
        }
    };


    const handleFileInputChange = async (e) => {
        e.preventDefault();
        if (!selectedModelFamily) {
            toast.error("No Document Model Family Selected  ,Please select Document Model Family and Document Model.");
            return;
        }
        else if (!selectedVendor) {
            toast.error("No Document Model Selected, Please select Document Model.");
            return;
        }
        const validFiles = filterValidFiles(e.target.files);

        if (validFiles.length === 0) {
            return;
        }

        const validFilesCount = files.filter(file => !file.error).length;
        if (validFilesCount + validFiles.length > MAX_FILES_ALLOWED) {
            toast.error(`You can upload a maximum of ${MAX_FILES_ALLOWED} valid files.`);
            return;
        }

        addFilesWithStatus(validFiles);
        await uploadFiles(validFiles);
        initializeModelDetails();
    };

    const handleDrop = async (e) => {
        e.preventDefault();
        if (!selectedModelFamily) {
            toast.error("No Document Model Family Selected  ,Please select Document Model Family and Document Model.");
            return;
        }
        else if (!selectedVendor) {
            toast.error("No Document Model Selected, Please select Document Model.");
            return;
        }
        setDragging(false);

        const validFiles = filterValidFiles(e.dataTransfer.files);

        if (validFiles.length === 0) {
            return; // Exit if no valid files
        }

        const validFilesCount = files.filter(file => !file.error).length;
        if (validFilesCount + validFiles.length > MAX_FILES_ALLOWED) {
            toast.error(`You can upload a maximum of ${MAX_FILES_ALLOWED} valid files.`);
            return;
        }

        addFilesWithStatus(validFiles);
        await uploadFiles(validFiles); // Upload only valid files
        initializeModelDetails()
    };

    const uploadFiles = async (files) => {
        setIsUploading(true);

        const validFiles = filterValidFiles(files);

        // Set the uploading status to true for each file
        setFiles((prevFiles) =>
            prevFiles.map((file) => {
                if (validFiles.some(newFile => newFile.name === file.name)) {
                    return { ...file, uploading: true };
                }
                return file;
            })
        );

        initializeModelDetails();

        for (const [index, file] of validFiles.entries()) {
            const formData = new FormData();
            formData.append('documents', file);
            try {
                const response = await axios.post(
                    `${import.meta.env.VITE_SERVER}/multi-threading-upload-documents?strFamilyName=${selectedModelFamily}&strModelName=${selectedVendor}&bUsePaidModel=${isTrialPaidDocExtraction}`,
                    formData,
                    {
                        headers: {
                            Authorization: `Bearer ${localStorage.getItem('token')}`,
                        },
                    }
                );

                if (response?.data[0]?.DocId) {
                    setFiles((prevFiles) => {
                        return prevFiles?.map((prevFile) => {
                            if (prevFile?.name === file.name) {
                                return { ...prevFile, docId: response.data[0].DocId, uploading: false, uploaded: true };
                            }
                            return prevFile;
                        });
                    });

                    setUploadedDocIds((prevIds) => [...prevIds, response?.data[0]?.DocId]);
                }
            } catch (error) {
                if (error.response && (error.response.status === 400 || error.response.status === 422)) {
                    const errorMessage = error.response.data.detail || "Bad Request";
                    console.error('Error uploading file:', errorMessage);
                    toast.error(errorMessage);
                } else {
                    console.error('Error uploading file:', error);
                    toast.error("Something Went Wrong! Please Try again Later");
                }
                setFiles((prevFiles) =>
                    prevFiles.map((prevFile) => {
                        if (prevFile?.name === file.name) {
                            return { ...prevFile, uploading: false, error: true };
                        }
                        return prevFile;
                    })
                );
            } finally {
                setFiles((prevFiles) =>
                    prevFiles.map((prevFile) => {
                        if (prevFile?.name === file.name) {
                            return { ...prevFile, uploading: false };
                        }
                        return prevFile;
                    })
                );
            }
        }

        initializeModelDetails();
        setIsUploading(false);
    };



    const handleFileDeleteFromFrontend = async (index) => {
        const deletedFile = files[index];

        // For Removing Doc From Frontend
        const updatedFiles = [...files];
        updatedFiles.splice(index, 1);
        setFiles(updatedFiles);

        // Remove the corresponding docId from uploadedDocIds
        if (deletedFile?.docId) {
            setUploadedDocIds(prevIds => prevIds.filter(id => id !== deletedFile.docId));
        }

    }

    // Function to handle file deletion
    const handleFileDelete = async (docId) => {
        if (docId === null || !docId) return;
        const fileIndex = files.findIndex(file => file.docId === docId);

        // For Removing Doc From Backend
        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/delete-documents`,
                [docId],
                { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
            );

            if (response.status === 200) {
                // For Removing Doc From Frontend
                await handleFileDeleteFromFrontend(fileIndex);
            }
            else {
                toast.error('Failed to deleted Document!');
            }
        } catch (error) {
            console.error('Error processing files:', error);
            toast.error('Failed to deleted Document!');
        }
    };

    const handleDeleteAll = async (docId = null) => {

        if (window.confirm("Are you sure you want to delete uploaded document?")) {
            let docIdsToDelete;

            if (docId) {
                docIdsToDelete = [docId];
            } else {
                docIdsToDelete = files.map(file => file.docId).filter(docId => docId !== null);
            }

            if (docIdsToDelete.length > 0) {
                for (const docId of docIdsToDelete) {
                    await handleFileDelete(docId);
                }
            }

            if (!docId) {
                setFiles([]);
                setUploadedDocIds([]);
            }
        }
    };


    function formatDate(timeZone) {
        const now = new Date();
        const options = { year: 'numeric', month: 'long', day: 'numeric', hour: '2-digit', minute: '2-digit', timeZoneName: 'short', timeZone: timeZone };
        return `${now.toLocaleDateString('en-US', options)}`;
    }

    const updateDocumentStatus = async (docId, isTrialPaidDocExtraction) => {
        try {
            // Create a FormData object to send as form data
            const formData = new FormData();
            formData.append('iDocId', docId);
            formData.append('bIsPaidModelUsed', isTrialPaidDocExtraction);

            await axios.post(
                `${import.meta.env.VITE_SERVER}/doc/update-status`,
                formData,  // Sending data as form data
                {
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                    },
                }
            );
        } catch (error) {
            toast.error(`Failed to update document status, documents will get extracted using lite mode.`);
        }
    };

    const handleSubmit = async (e) => {
        e.preventDefault();
        const validFiles = files.filter(file => !file.error);

        await Promise.all(validFiles.map(file => updateDocumentStatus(file.docId, isTrialPaidDocExtraction))); // Update Doc Extraction model as currently select mode of extraction

        navigate('/history', { state: { docIds: uploadedDocIds } });
    };

    return (
        <>
            <Toaster position="top-center" />
            <div className="flex pl-[1.5rem] py-2 pt-4">
                <div className="flex-grow">
                    <h1 className="text-sm xl:text-xs font-semibold text-[#707EAE]">Home</h1>
                    <h1 className="text-3xl xl:text-2xl font-semibold text-[#3F3F3F]">Upload Document</h1>
                </div>

                {/* Place the ModelComponent to the right */}
                <WatchVideo />
                <ModelComponent
                    initialPageLimitLeft={pageLimitLeft}
                    initialTotalPageLimitLeft={totalPageLimitLeft}
                    initialFreePageLimitUsage={freePageLimitUsage}
                    initialTotalFreePageLimit={totalFreePageLimit}
                />
            </div>
            <div className="flex flex-col items-center bg-[#ffff] rounded-2xl m-7 justify-start">
                <div className="relative w-full p-4">
                    <div className="w-full mb-8">
                        <div className="flex items-center mb-8">  {/* This creates a flex container and aligns items to the center */}
                            <img src={UploadIcon} className="xl:w-[21px]" />
                            <label htmlFor="vendorSelect" className="ml-1 text-xl font-bold text-[#003654] xl:text-sm">  {/* The label */}
                                Upload Document
                            </label>
                        </div>
                        <div className="flex items-start">
                            <div style={{ marginRight: "10px" }}>
                                <label htmlFor="vendorSelect" className="block mb-2 text-sm font-medium text-gray-700" >
                                    <div className="flex">
                                        Document model category<span className="text-red-500">*
                                        </span>
                                        <Tooltip content={<div>The category that best fits your document (e.g., Demo_Accounting, Demo_HR).<br /> Organizing your models into families makes it easier to manage and identify them.</div>}>
                                            <div>
                                                <CiCircleInfo className="ml-1 text-xl" />
                                            </div>
                                        </Tooltip>
                                    </div>
                                </label>
                                <select
                                    id="vendorSelect"
                                    className="block xl:text-sm p-2 text-base focus:outline-none text-gray-700 bg-[#ffff] border border-gray-300 rounded-md shadow-sm focus:border-blue-600"
                                    style={{ width: "300px" }}
                                    value={selectedModelFamily}
                                    onChange={handleModelFamilyChange}
                                >
                                    {!selectedModelFamily && <option value="">Select a Model Family</option>}
                                    {modelFamily.map((vendor, index) => (
                                        <option key={index} value={vendor}>
                                            {vendor}
                                        </option>
                                    ))}
                                </select>
                            </div>
                            <div style={{ marginLeft: "10px" }}>
                                <label htmlFor="vendorSelect" className="block mb-2 text-sm font-medium text-gray-700">
                                    <div className='flex'>
                                        Document model<span className="text-red-500">*</span>
                                        <Tooltip content={
                                            <div>
                                                The specific type of document you are uploading (e.g., Invoice, Receipt).<br /> This helps AccuVelocity apply the correct extraction model for accurate data processing.
                                            </div>
                                        }>
                                            <div>
                                                <CiCircleInfo className="ml-1 text-xl" />
                                            </div>
                                        </Tooltip>
                                    </div>
                                </label>

                                <select
                                    id="vendorSelect"
                                    className="block p-2 xl:text-sm text-base focus:outline-none text-gray-700 bg-[#ffff] border border-gray-300 rounded-md shadow-sm focus:border-blue-600"
                                    style={{ width: "300px" }}
                                    value={selectedVendorID}
                                    onChange={handleVendorChange}
                                >
                                    {!selectedVendorID && <option value="">Select a Model</option>}
                                    {filterModels.map((vendor, index) => (
                                        <option key={vendor.Id} value={vendor.Id}>
                                            {vendor.Name}
                                        </option>
                                    ))}

                                </select>
                            </div>
                            <div>
                                <div className="content-center ml-5"  >
                                    <label className="block mb-1 text-sm font-medium text-gray-700" >
                                        <div className="flex">
                                            Model Description
                                        </div>
                                    </label>
                                    <div>

                                        <Tooltip
                                            content={<div style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                {selectedModelDescription}
                                            </div>
                                            }>

                                            <label className=" text-sm font-medium text-gray-700">
                                                {selectedModelDescription}
                                            </label>
                                        </Tooltip>
                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                    {/* Invisible file input */}
                    <input
                        type="file"
                        id="fileInput"
                        className="hidden"
                        multiple
                        onChange={handleFileInputChange}
                        accept=".txt, .pdf, .jpg, .jpeg, .png, .bmp, .webp, .heic, .heif, .xlsx, .csv, .docx"
                        disabled={vendors.length < 1} // Disable input if there are no vendors
                    />
                    {/* Drag and drop box */}
                    
                    <div 
                        className={`w-full smo:w-10vw h-[40vh] p-10 smo:h-auto border-2 bg-[#F4F4F4] border-dashed rounded-lg flex flex-col justify-center items-center cursor-pointer mb-8 ${vendors.length < 1 ? 'opacity-50' : ''}`} // Dimmed effect if disabled
                        onDrop={vendors.length >= 1 ? handleDrop : null} // Only allow drop if enabled
                        onDragOver={(e) => {
                            if (vendors.length >= 1) { // Prevent drag if disabled
                                e.preventDefault();
                                setDragging(true);
                            }
                        }}
                        onDragEnter={() => vendors.length >= 1 && setDragging(true)}
                        onDragLeave={() => vendors.length >= 1 && setDragging(false)}
                        onClick={() => vendors.length >= 1 && document.getElementById('fileInput').click()} // Allow click only if vendors are available
                        style={{
                            borderColor: dragging ? '#003654' : '#e2e8f0',
                            backgroundColor: dragging ? '#ebf8ff' : '',
                            cursor: vendors.length < 1 ? 'not-allowed' : 'pointer' // Change cursor to indicate disabled
                        }}
                    >
                        {/* Cloud icon */}
                        <img src={cloudeSVG} className="xl:w-[140px]" />
                        <div className={`${dragging ? 'text-[#003654]' : 'text-gray-500'} text-lg smo:text-2xl`}>
                            {dragging ? 'Drop files here' : (
                                <div style={{ textDecoration: 'underline', textUnderlineOffset: '5px' }}>
                                    <span>Click or </span>
                                    <span>Drag</span>
                                    <span> to upload your docs</span>
                                </div>
                            )}
                        </div>
                        <p className="text-gray-500 text-sm smo:text-base mt-1 xl:text-xs">Supported: TXT, PDF, JPG, JPEG, PNG, WEBP, BMP, DOCX, XLSX, CSV</p>
                        <p className="text-gray-500 text-sm smo:text-base xl:text-xs">
                            File should not exceed 4 pages, must not be password-protected, and should preferably be in English. The maximum file size allowed is 20MB.
                        </p>
                    </div>
                   

                    {(files.length > 0) && (
                        <div className="w-full overflow-x-auto shadow-lg">
                            <table className="min-w-full leading-normal ">
                                <thead>
                                    <tr>
                                        <th className="w-[10vh] px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Sr No
                                        </th>
                                        <th className="w-[40vh] px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            File Name
                                        </th>
                                        <th className="w-[30vh]  px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Model Family
                                        </th>
                                        <th className="w-[30vh]  px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Model Name
                                        </th>
                                        <th className="w-[30vh]  px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Date Time
                                        </th>
                                        <th className="w-[20vh]  px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Status
                                        </th>
                                        <th className="w-[10vh]  px-2 py-3 border-b-2 border-gray-300 text-left text-xs font-semibold text-gray-600 uppercase tracking-wider smo:px-6 smo:py-3">
                                            Action
                                        </th>
                                    </tr>
                                </thead>
                                <tbody className="bg-[#ffff]">
                                    {
                                        files.map((file, index) => {
                                            const docId = file.docId ? file.docId : undefined;
                                            return (
                                                <tr key={index} className={`odd:bg-gray-50 even:bg-[#ffff] hover:bg-gray-200 ${file.error ? 'bg-red-100' : ''}`}>
                                                    <td className="px-2 py-4 border-b xl:text-xs border-gray-300 text-sm smo:px-6 smo:py-4">{index + 1}</td>
                                                    {file.name.length > 20 ? (
                                                        <Tooltip content={file.name}><td className="xl:text-xs px-2 py-4 border-b border-gray-300 text-sm smo:px-6 smo:py-4" style={{
                                                            maxWidth: '100px',
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis'
                                                        }}>{file.name}</td></Tooltip>
                                                    ) : (
                                                        <td className="xl:text-xs px-2 py-4 border-b border-gray-300 text-sm smo:px-6 smo:py-4" style={{
                                                            maxWidth: '100px',
                                                            whiteSpace: 'nowrap',
                                                            overflow: 'hidden',
                                                            textOverflow: 'ellipsis'
                                                        }}>{file.name}</td>
                                                    )}
                                                    <td className="px-2 py-4 border-b border-gray-300 text-sm smo:px-6 smo:py-4">
                                                        <select
                                                            disabled={file.uploading || file.error}
                                                            className="block p-2 xl:text-xs text-base focus:outline-none text-gray-700 bg-[#ffff] border border-gray-300 rounded-md shadow-sm focus:border-blue-600"
                                                            style={{
                                                                width: "100%",
                                                                maxHeight: "8rem",
                                                                overflowY: "auto"
                                                            }}
                                                            value={file.selectedVendor?.family ? file.selectedVendor.family : selectedModelFamily}
                                                            onChange={(e) => handleModelFamilyChangeForFile(index, e)}
                                                        >
                                                            {modelFamily.map((vendor, index) => (
                                                                <option key={index} value={vendor}>
                                                                    {vendor}
                                                                </option>
                                                            ))}
                                                        </select>
                                                    </td>
                                                    <td className="px-2 py-4 border-b border-gray-300 text-sm smo:px-6 smo:py-4">
                                                        <select
                                                            disabled={file.uploading || file.error}
                                                            className="block p-2 xl:text-xs text-base focus:outline-none text-gray-700 bg-[#ffff] border border-gray-300 rounded-md shadow-sm focus:border-blue-600"
                                                            style={{
                                                                width: "100%",
                                                                maxHeight: "8rem",
                                                                overflowY: "auto"
                                                            }}
                                                            value={file.selectedVendor?.id ? file.selectedVendor.id : selectedVendorID}
                                                            onChange={(e) => UpdateModelDetails(index, e)}
                                                        >
                                                            {docId && selectedModelNameForFile[docId] ? (
                                                                selectedModelNameForFile[docId].map((vendor, index) => (
                                                                    <option key={vendor.Id || index} value={vendor.Id}>
                                                                        {vendor.Name}
                                                                    </option>
                                                                ))
                                                            ) : (
                                                                vendors.map((vendor, index) => (
                                                                    <option key={vendor.Id || index} value={vendor.Id}>
                                                                        {vendor.Name}
                                                                    </option>
                                                                ))
                                                            )}
                                                        </select>
                                                    </td>
                                                    <td className="px-2 py-4 xl:text-xs border-b border-gray-300 text-sm smo:px-6 smo:py-4">{formatDate('America/New_York')}</td>
                                                    <td className="px-2 py-4 border-b xl:text-xs border-gray-300 text-sm smo:px-6 smo:py-4">
                                                        {file.uploading ? (
                                                            <Tooltip content="The Document is uploading..">
                                                            <div className="inline-flex items-center px-3 py-1 rounded-full">
                                                                <IoMdArrowUp className="animate-bounce h-5 w-5 text-gray-500" />
                                                            </div></Tooltip>
                                                        ) : file.uploaded ? (
                                                            
                                                            <div className="inline-flex items-center px-3 py-1 border rounded-full text-green-600">
                                                                <IoCheckmarkDoneCircle className="h-5 w-5 mr-2" />
                                                                <span>Uploaded</span>
                                                            </div>
                                                        ) : file.error ? (
                                                            <div className="inline-flex items-center px-3 py-1 rounded-full text-red-600">
                                                                <VscError className="h-5 w-5 mr-2" />
                                                                <span>Failed</span>
                                                            </div>
                                                        ) : null}
                                                    </td>
                                                    <td className="px-2 py-4 border-b border-gray-300 text-sm smo:px-6 smo:py-4">
                                                        <div className="">
                                                            <div
                                                                style={{
                                                                    border: 'none',
                                                                    backgroundColor: 'transparent'
                                                                }}
                                                                disabled={vendors.length < 1 || file.uploading || file.error}
                                                                onClick={() => !file.uploading && !file.error && handleDeleteAll(files[index]?.docId)}
                                                            >
                                                                <MdDelete
                                                                    className={`h-6 w-6 xl:h-5 xl:w-5 text-red-600 hover:text-red-800 cursor-pointer ${(file.uploading || file.error) ? 'opacity-20' : ''}`}
                                                                />
                                                            </div>
                                                        </div>
                                                    </td>
                                                </tr>
                                            )
                                        })
                                    }
                                </tbody>
                            </table>
                        </div>
                    )}
                    {files.length > 0 && (
                        <div className="flex items-center justify-between sticky bottom-0 mb-4 mt-4">
                            <div className="flex justify-start">
                                {files.some(file => file.uploaded) && (
                                    <Button
                                        disabled={vendors.length < 1 || isUploading}
                                        onClick={handleSubmit}
                                        className="px-5 py-3 bg-[#003654] text-white xl:text-[0.700rem] hover:bg-[#002744] rounded-lg cursor-pointer"
                                    >
                                        Extract Data
                                    </Button>
                                )}
                            </div>
                            <div className="flex justify-end ">

                                <Button
                                    disabled={vendors.length < 1 || isUploading}
                                    onClick={() => handleDeleteAll()}
                                    className="px-5 py-3 bg-red-600 text-white xl:text-[0.700rem] hover:bg-red-700 rounded-lg cursor-pointer"
                                >
                                    Delete All
                                </Button>
                            </div>
                        </div>
                    )}
                </div>
            </div>
        </>
    );
}
