import {
    Card,
    Typography,
    List,
    ListItem,
    <PERSON><PERSON>temPrefix,
    Accordion,
    Accordion<PERSON>eader,
    AccordionBody
} from "@material-tailwind/react";
import { useRecoilValue } from "recoil";
import { FaUser } from "react-icons/fa6";
import { FaUsers } from "react-icons/fa";
import userDataState from "../context/userData";
import { IoIosPower } from "react-icons/io";
import { IoDocumentTextOutline } from "react-icons/io5";
import { Link, useNavigate } from "react-router-dom";
import toast, { Toaster } from 'react-hot-toast';
import logo from '../assets/logo.svg';
import { BiCollapseAlt } from "react-icons/bi";
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
// import { VscAdd } from "react-icons/vsc";
import { AiOutlineCloudUpload } from "react-icons/ai";
import MyModelImage from '../assets/IconMyModels.png';
import { useState, useEffect } from "react";
import { VscHubot } from "react-icons/vsc";
import { useRecoilState } from 'recoil';
import { isActivePaidPlanAtom } from '../context/TrailUsageUserData'
import { Tooltip } from '@material-tailwind/react';
import BugDetailsModal from "./BugDetails";
import { jwtDecode } from "jwt-decode";
import companyLogoSVG from "../assets/Logo/CompanyLogo.svg"
import activityLogo from "../assets/SVGs/Sidebar/activityLogo.svg"
import bugIcon from "../assets/SVGs/Sidebar/BugIcon.svg"
import DropDownIcon from "../assets/SVGs/Sidebar/DropDownIcon.svg"
import HelpCenterIcon from "../assets/SVGs/Sidebar/HelpCenter.svg"
import FaqIcon from "../assets/SVGs/Sidebar/FaqIcon.svg"
import SupportIcon from "../assets/SVGs/Sidebar/SupportIcon.svg"
import UpgradePlan from "../assets/SVGs/Sidebar/UpgradePlanIcon.svg"

export default function Sidebar({ isOpen, toggleSidebar, showAlert }) {
    const userData = useRecoilValue(userDataState);
    const isLoggedIn = userData.userData !== null;
    const Version = '2.0.20240808'
    const [currentToastId, setCurrentToastId] = useState();
    const navigate = useNavigate()
    const [hasAdminRights, setAdminRights] = useState(false);
    const [isScriptAdded, setIsScriptAdded] = useState(false);
    const [isActivePaidPlan, _] = useRecoilState(isActivePaidPlanAtom);

    const [isModalOpen, setIsModalOpen] = useState(false);
    const [userName, setUserName] = useState(" ");
    const [open, setOpen] = useState(0);

    const handleOpen = (value) => {
        setOpen(open === value ? 0 : value);
    };

    const handleBugReportClick = () => {
        setIsModalOpen(true);
    };

    const closeModal = () => {
        setIsModalOpen(false);
    };

    useEffect(() => {
        // Retrieve role from localStorage
        const role = localStorage.getItem('Role');
        const token = localStorage.getItem('token');
        const decoded = jwtDecode(token)
        //  userName = decoded.name
        setUserName(decoded.name)
        if (role && ["admin", "super admin", "verifier"].includes(role.toLowerCase())) {

            setAdminRights(true);
        } else {
            setAdminRights(false);
        }

    }, []); // Empty dependency array ensures the effect runs only once after initial render
    function resetAllCookies() {
        // Get all cookies
        let cookies = document.cookie.split(';');

        // Iterate over each cookie
        cookies.forEach(function (cookie) {
            let parts = cookie.split('=');
            let name = parts[0].trim();

            // Set expiry date to a past date
            document.cookie = name + "=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;";
        });
    }

    const handleLogout = () => {
        localStorage.removeItem('token');

        // Clear location state and navigate to home
        navigate("/", { replace: true, state: null });
        // Get showAlert value from localStorage and update if it's false
        const showAlert = localStorage.getItem('showAlert');
        if (showAlert === 'false') {
            localStorage.setItem('showAlert', 'true');
        }
        toast.success("Logged Out!");
        window.location.href = '/';
        // Call the function to reset all cookies
        resetAllCookies();
        localStorage.clear();
    };

    const location = useLocation();

    const isActive = (path) => {
        // Special handling for root path to avoid false positives
        if (path === '/') {
            return location.pathname === '/';
        }
        // General case for all other paths
        return location.pathname.includes(path);
    }

    const toggleScripts = () => {
        // Dismiss any existing toast when a new click happens
        if (currentToastId !== null) {
            toast.remove(currentToastId);
            setCurrentToastId(null);
        }

        if (isScriptAdded) {
            // Remove scripts
            const script1 = document.querySelector('script[src="https://cdn.botpress.cloud/webchat/v1/inject.js"]');
            const script2 = document.querySelector('script[src="https://mediafiles.botpress.cloud/c936a838-2a44-434b-a667-7bf48e9c96a8/webchat/config.js"]');
            const widgetContainer = document.getElementById('bp-web-widget-container');
            if (script1) {
                script1.parentNode.removeChild(script1);
            }
            if (script2) {
                script2.parentNode.removeChild(script2);
            }
            if (widgetContainer) {
                widgetContainer.parentNode.removeChild(widgetContainer);
            }
            toast.success("The chat widget has been removed.", { autoClose: 2000 });
        } else {
            // Function to reload scripts if the widget doesn't load
            const reloadScriptsIfNeeded = () => {
                const widgetContainer = document.getElementById('bp-web-widget-container');
                if (!widgetContainer) {
                    // Remove scripts if they exist
                    const existingScript1 = document.querySelector('script[src="https://cdn.botpress.cloud/webchat/v1/inject.js"]');
                    const existingScript2 = document.querySelector('script[src="https://mediafiles.botpress.cloud/c936a838-2a44-434b-a667-7bf48e9c96a8/webchat/config.js"]');
                    if (existingScript1) {
                        existingScript1.parentNode.removeChild(existingScript1);
                    }
                    if (existingScript2) {
                        existingScript2.parentNode.removeChild(existingScript2);
                    }

                    // Re-add scripts
                    const newScript1 = document.createElement('script');
                    newScript1.src = 'https://cdn.botpress.cloud/webchat/v1/inject.js';
                    document.body.appendChild(newScript1);

                    const newScript2 = document.createElement('script');
                    newScript2.src = 'https://mediafiles.botpress.cloud/c936a838-2a44-434b-a667-7bf48e9c96a8/webchat/config.js';
                    newScript2.defer = true;
                    document.body.appendChild(newScript2);
                }
            };

            // Add scripts
            const script1 = document.createElement('script');
            script1.src = 'https://cdn.botpress.cloud/webchat/v1/inject.js';
            document.body.appendChild(script1);

            const script2 = document.createElement('script');
            script2.src = 'https://mediafiles.botpress.cloud/c936a838-2a44-434b-a667-7bf48e9c96a8/webchat/config.js';
            script2.defer = true;
            document.body.appendChild(script2);

            // Check for the widget container after 5 seconds and reload if not found
            setTimeout(reloadScriptsIfNeeded, 10000);

            // Display the toast message and store the new toast ID
            const newToastId = toast.success(
                "For assistance with Accuvelocity, please click the chat icon.\nIf you have any further questions, feel free to contact <NAME_EMAIL>.",
                {
                    style: { textAlign: 'center' },
                    duration: 10000, // Duration in milliseconds (10 seconds)
                    autoClose: true
                }
            );
            setCurrentToastId(newToastId);
        }

        setIsScriptAdded(!isScriptAdded);
    };

    return (
        <>
            <BugDetailsModal isOpen={isModalOpen} onClose={closeModal} Version={Version} />
            <Toaster position="top-center"></Toaster>
            <div className={`fixed inset-y-0 left-0 z-40 overflow-hidden transition-all duration-300 ease-in-out ${showAlert ? 'mt-10' : ''} ${isOpen ? '2xlo:w-64 xlo:w-[12rem]' : 'w-16'}`}>
                <Card className={`h-screen ${isOpen ? '2xlo:w-64 xlo:w-[12rem]' : 'w-16'} p-0.9 shadow-xl shadow-blue-gray-900/5`}>
                    {/* Logo */}
                    <Link to="/" className="cursor-pointer font-medium  bg-[#ffff]">
                        <div className={`flex items-center justify-center p-4 border-b w-full`} onClick={() => {
                            if (currentToastId) {
                                toast.remove(currentToastId);
                            }
                        }}>
                            {isOpen ? (
                                <img src={logo} alt="Full Logo" className="w-full transition-opacity duration-300 opacity-100" />
                            ) : (
                                <div className="w-full transition-opacity duration-300 opacity-100">
                                    <img src={companyLogoSVG} />
                                </div>
                            )}
                        </div>
                    </Link>
                    <List className="text-xs xlo:text-sm 2xlo:text-[1rem]">
                        {/* Dashboard */}
                        <Link to="/">
                            <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <ListItemPrefix>
                                    <AiOutlineCloudUpload className="h-5 w-5" />
                                </ListItemPrefix>
                                {isOpen && <span>Upload Document</span>}
                            </ListItem>
                        </Link>

                        {/* Documents */}
                        <Link to="/history">
                            <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/history') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <ListItemPrefix>
                                    <IoDocumentTextOutline className="h-5 w-5" />
                                </ListItemPrefix>
                                {isOpen && <span>My Documents</span>}
                            </ListItem>
                        </Link>

                        {/* My Models */}
                        <Link to="/ModelListPage">
                            <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/ModelListPage') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <ListItemPrefix>
                                    <img src={MyModelImage} alt="Modify" className="h-5 w-5" />
                                </ListItemPrefix>
                                {isOpen && <span>My Models</span>}
                            </ListItem>
                        </Link>

                        {/* Activity Log */}
                        <Link to="/Logs">
                            <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/Logs') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <ListItemPrefix>
                                    <img src={activityLogo} alt="Modify" />
                                </ListItemPrefix>
                                {isOpen && <span>Activity Log</span>}
                            </ListItem>
                        </Link>

                        {!isActivePaidPlan || isActivePaidPlan === "false" ? (
                            <div>
                                <Link to="/UpgradePlan">
                                    <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/UpgradePlan') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                        if (currentToastId) {
                                            toast.remove(currentToastId);
                                        }
                                    }}>
                                        <ListItemPrefix>
                                            <img src={UpgradePlan} />
                                        </ListItemPrefix>
                                        {isOpen && <span>Upgrade</span>}
                                    </ListItem>
                                </Link>
                            </div>
                        ) : null}

                        {/* Users List (Admin Only) */}
                        {hasAdminRights && (
                            <Link to="/AllUserList">
                                <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/AllUserList') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                    if (currentToastId) {
                                        toast.remove(currentToastId);
                                    }
                                }}>
                                    <ListItemPrefix>
                                        <FaUsers className="h-5 w-5" />
                                    </ListItemPrefix>
                                    {isOpen && <span>Users List</span>}
                                </ListItem>
                            </Link>
                        )}

                        {/* Support */}

                        <div className={`${open && 'bg-[#F4F7FE]'}`}>
                            <ListItem className={`2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] hover:bg-[#F4F7FE]`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <Accordion
                                    open={open === 1}
                                    icon={
                                        <div className={`transition-transform ${open === 1 ? "rotate-180" : ""}`}>
                                            <img src={DropDownIcon} />
                                        </div>
                                    }
                                >
                                    <div className="ml-3 ">
                                        <AccordionHeader onClick={() => handleOpen(1)} className="border-b-0 -m-3">
                                            <ListItemPrefix>
                                                <img src={SupportIcon} />
                                            </ListItemPrefix>
                                            <Typography color="blue-gray" className="mr-auto font-normal xlo:text-sm 2xlo:text-[1rem]">
                                                {isOpen && <span className="-ml-1">Support</span>}
                                            </Typography>
                                        </AccordionHeader>
                                    </div>
                                    <AccordionBody className={`${isOpen ? '' : '-m-3'}`}>
                                        <a href="https://youtu.be/JmmzXVfHjV4?si=kJ_guWesodJ1GPxJ" target="_blank">
                                            <ListItem className={`flex items-center transition-all duration-300 text-[#003654] hover:bg-gray-300`} onClick={() => {
                                                if (currentToastId) {
                                                    toast.remove(currentToastId);
                                                }
                                            }}>
                                                <ListItemPrefix>
                                                    <img src={HelpCenterIcon} />
                                                </ListItemPrefix>
                                                {isOpen && <span className="text-[#003654] font-normal -ml-1">
                                                    Help Center
                                                </span>}
                                            </ListItem>
                                        </a>
                                        <a href="https://accuvelocity.com/#faqs" target="_blank">
                                            <ListItem onClick={() => {
                                                if (currentToastId) {
                                                    toast.remove(currentToastId);
                                                }
                                            }}>
                                                <ListItemPrefix>
                                                    <img src={FaqIcon} />
                                                </ListItemPrefix>
                                                {isOpen && <span className="text-[#003654] font-normal">
                                                    FAQ&apos;S
                                                </span>}
                                            </ListItem>
                                        </a>
                                        <ListItem
                                            className={`flex items-center transition-all duration-300 text-[#003654] hover:bg-gray-300`}
                                            onClick={toggleScripts}
                                            style={{ cursor: 'pointer' }}
                                        >
                                            <ListItemPrefix>
                                                <VscHubot className="h-5 w-5" style={{ transform: 'scale(1.3)' }} />
                                            </ListItemPrefix>
                                            {isOpen && <span className="text-[#003654] font-normal">AI ChatBot</span>}
                                        </ListItem>
                                    </AccordionBody>
                                </Accordion>
                            </ListItem>
                        </div>

                        {/* Profile */}
                        <Link to="/profile">
                            <ListItem className={`flex items-center 2xlo:w-full xlo:w-[177px] transition-all duration-300 text-[#003654] ${isActive('/profile') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`} onClick={() => {
                                if (currentToastId) {
                                    toast.remove(currentToastId);
                                }
                            }}>
                                <ListItemPrefix>
                                    <FaUser className="h-4 w-5" />
                                </ListItemPrefix>
                                {isOpen && <span>Profile</span>}
                            </ListItem>
                        </Link>

                        {/* Log Out */}
                        {isLoggedIn && (
                            <ListItem
                                onClick={(e) => {
                                    e.preventDefault(); // This will prevent the default link action
                                    handleLogout(); // Call your logout handler
                                }}
                                className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/logout') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`}
                            >
                                <ListItemPrefix>
                                    <IoIosPower className="h-5 w-5" />
                                </ListItemPrefix>
                                {isOpen && <span>Log Out</span>}
                            </ListItem>
                        )}
                    </List>
                </Card>

                {/* Sidebar Toggle Button */}
                <button onClick={toggleSidebar} className="absolute top-1/2 right-0 transform -translate-y-1/2 translate-x-1/2 z-50 transition-transform duration-300 ease-in-out bg-gray-200 hover:bg-gray-300 rounded-full h-10 w-10 flex items-center justify-center">
                    <BiCollapseAlt className="text-gray-800 mr-3" />
                </button>

                {/* Version Information */}
                <div className="absolute bottom-0 w-full flex flex-col justify-center items-center px-4 py-6 ">
                    {isOpen ? (
                        <>
                            {userName && (
                                <Link to="/profile">
                                    <Tooltip content={userName}>
                                        <div
                                            style={{
                                                fontFamily: 'inherit',
                                                fontSize: 'inherit',
                                                color: 'grey', // Grey color for the username
                                                fontWeight: 'bold', // Make username bold
                                                cursor: 'pointer', // Pointer cursor on hover
                                                marginBottom: '20%' // Add margin-bottom
                                            }}
                                        >
                                            {userName.length > 15 ? `${userName.substring(0, 15)}...` : userName}
                                        </div>
                                    </Tooltip>
                                </Link>
                            )}

                            {
                                <div>
                                    <button
                                        onClick={() => {
                                            handleBugReportClick();
                                            if (currentToastId) {
                                                toast.remove(currentToastId); // Remove the toast if it exists
                                            }
                                        }}
                                        className="mr-2 text-sm bg-[#003654] hover:bg-[#002744] xl:text-[0.7rem] xl:m-2 text-white font-semibold py-2 px-4 rounded-3xl focus:outline-none focus:shadow-outline"
                                    >
                                        <Tooltip content="Click to report bug details.">
                                            <div className="flex">
                                                <span className="mr-2">
                                                    <img src={bugIcon} alt="Modify" />
                                                </span>
                                                Report a Bug!
                                            </div>
                                        </Tooltip>
                                    </button>
                                </div>
                            }

                            <div
                                className={`flex items-center justify-center text-[#3F3F3F] bg-[#ffff] p-3 rounded-3xl xl:text-[0.7rem] drop-shadow-md ${isOpen ? 'text-sm h-10' : 'text-xs h-6 w-16 ml-2'}`}
                                style={{ marginTop: '5%' }} // Add margin-top
                            >
                                <span>Version {Version}</span>
                            </div>
                        </>
                    ) : (
                        <span></span>
                    )}
                </div>
            </div >
        </>
    );

}

Sidebar.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    toggleSidebar: PropTypes.func.isRequired,
    showAlert: PropTypes.bool.isRequired
};
