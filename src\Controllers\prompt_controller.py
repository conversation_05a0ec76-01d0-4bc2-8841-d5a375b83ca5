from fastapi import FastAP<PERSON>, HTTPException
import traceback
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from config.db_config import AsyncSessionLocal
from src.Models.models import Prompt
from src.Controllers.Logs_Controller import <PERSON><PERSON>ogController

from datetime import datetime
import pytz

app = FastAPI()

class CPromptController:
    @staticmethod
    async def create_prompt(iUserID: int, ModelId: int, prompt_text: str, ModelSeries: str):
        async with AsyncSessionLocal() as db:
            try:
                # Check for existing prompt with the same ModelSeries and UserID
                existing_prompt = await db.execute(
                    select(Prompt).filter(
                        Prompt.ModelId == ModelId,
                        Prompt.ModelSeries == ModelSeries,
                        Prompt.UserID == iUserID
                    )
                )
                existing_prompt = existing_prompt.scalar()

                if existing_prompt is not None:
                    raise HTTPException(
                        status_code=400, detail=f"You already have this model with identical settings in your account."
                    )

                new_prompt = Prompt(
                    UserID=iUserID,
                    ModelId=ModelId,
                    ModelSeries=ModelSeries,
                    prompt=prompt_text
                )

                db.add(new_prompt)
                await db.commit()
                await db.refresh(new_prompt)

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully added new Prompt with ID {new_prompt.Id}.")

                return {
                    "Message": "Successfully added new Prompt.",
                    "Prompt Details": {
                        "Id": new_prompt.Id,
                        "ModelId": new_prompt.ModelId,
                        "ModelSeries": new_prompt.ModelSeries,
                        "Prompt": new_prompt.prompt,
                        "CreatedDateTime": str(new_prompt.CreatedDateTime),
                        "UpdatedDateTime": str(new_prompt.UpdatedDateTime)
                    }
                }
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to add Prompt.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to add Prompt for ModelSeries {ModelSeries}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500, detail=f"Error occurred while Configuring the Model for you."
                )
                
    async def MSGetModelNameFromPromptID(iUserID, iPromptID):
        """
            Returns : (str) --> "GPT" OR "Gemini"
            Purpose: To Get Model Name from Prompt Model Series 
        """
        try:

            dictDocPromptData = await CPromptController.MSGetPromptByID(iUserID=iUserID, promptId=iPromptID)
            strAPIModelSeries = dictDocPromptData.get("ModelSeries",None)
            if strAPIModelSeries is None:
                raise HTTPException(status_code=404, detail=f"The requested model could not be found.")

            if(( "_" in strAPIModelSeries )and (len(strAPIModelSeries.split("_")) >= 1)):
                strAPIModelName = strAPIModelSeries.split("_")[-2]
            else:
                raise HTTPException(status_code=500,detail="Error occurred while Fetching the Model for you.")
            return strAPIModelName
        
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to get Prompt using Model ID {iPromptID}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to get Prompt using Model ID {iPromptID}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            raise HTTPException(status_code=500, detail="Error occurred while Fetching the Model for you.")
        
        
    @staticmethod
    async def update_prompt(iUserID: int, ModelId: int, prompt_text: str, ModelSeries: str):
        async with AsyncSessionLocal() as db:
            try:
                # Find existing prompt by ModelSeries and UserID
                existing_prompt = await db.execute(
                    select(Prompt).filter(
                        Prompt.ModelId == ModelId,
                        Prompt.ModelSeries == ModelSeries,
                        Prompt.UserID == iUserID
                    )
                )
                existing_prompt = existing_prompt.scalar()

                if existing_prompt is None:
                    return await CPromptController.create_prompt(iUserID=iUserID, ModelId=ModelId, prompt_text=prompt_text, ModelSeries=ModelSeries)

                existing_prompt.prompt = prompt_text
                await db.commit()

                await CLogController.MSWriteLog(iUserID, "Info", f"Updated existing Prompt with ID {existing_prompt.Id}.")

                return {
                    "Message": "Existing Prompt updated successfully.",
                    "Prompt Details": {
                        "Id": existing_prompt.Id,
                        "ModelId": existing_prompt.ModelId,
                        "ModelSeries": existing_prompt.ModelSeries,
                        "Prompt": existing_prompt.prompt,
                        "CreatedDateTime": str(existing_prompt.CreatedDateTime),
                        "UpdatedDateTime": str(existing_prompt.UpdatedDateTime)
                    }
                }
            except HTTPException as e:
                raise e
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to update Prompt for ModelSeries {ModelSeries}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code= 500, detail=f"Error occurred while updating Model."
                )

    @staticmethod
    async def get_prompt_by_model_series(iUserID: int, ModelId: int, ModelSeries: str):
        async with AsyncSessionLocal() as db:
            try:
                # Execute the query to find the prompt with the given ModelSeries and UserID
                query_result = await db.execute(
                    select(Prompt).filter(
                        Prompt.ModelSeries == ModelSeries,
                        Prompt.ModelId == ModelId,
                        Prompt.UserID == iUserID
                    )
                )
                prompt = query_result.scalar()

                # If no prompt is found, log the event and raise an HTTPException
                if prompt is None:
                    await CLogController.MSWriteLog(iUserID, "Info", f"No prompt found with ModelSeries {ModelSeries} for this user.")
                    raise HTTPException(
                        status_code= 404,
                        detail=f"The requested model could not be found."
                    )

                # Log successful retrieval
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully retrieved prompt with ID {prompt.Id}.")

                # If a prompt is found, return its details
                return {
                    "Id": prompt.Id,
                    "ModelId": prompt.ModelId,
                    "ModelSeries": prompt.ModelSeries,
                    "Prompt": prompt.prompt,
                    "CreatedDateTime": str(prompt.CreatedDateTime),
                    "UpdatedDateTime": str(prompt.UpdatedDateTime)
                }
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt for ModelSeries {ModelSeries}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e

            except SQLAlchemyError as e:
                # Log the SQLAlchemy error
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt for ModelSeries {ModelSeries}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500,
                    detail="An error occurred while retrieving the Model."
                )
    
    @staticmethod
    async def get_all_prompts_by_model(iUserID: int, ModelId: int):
        async with AsyncSessionLocal() as db:
            try:
                # Execute the query to find the prompt with the given ModelSeries and UserID
                query_result = await db.execute(
                    select(Prompt).filter(
                        Prompt.ModelId == ModelId,
                        Prompt.UserID == iUserID
                    )
                )
                objPrompts = query_result.scalars().all()

                # If no prompt is found, log the event and raise an HTTPException
                if objPrompts is None:
                    await CLogController.MSWriteLog(iUserID, "Info", f"No prompts found with ModelId {ModelId} for this user.")
                    raise HTTPException(
                        status_code= 404,
                        detail=f"The requested model could not be found."
                    )

                # Log successful retrieval
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully retrieved prompt with ID {ModelId}.")

                # If a prompt is found, return its details
                return [{
                    "Id": prompt.Id,
                    "ModelId": prompt.ModelId,
                    "ModelSeries": prompt.ModelSeries,
                    "Prompt": prompt.prompt,
                    "CreatedDateTime": str(prompt.CreatedDateTime),
                    "UpdatedDateTime": str(prompt.UpdatedDateTime)
                }for prompt in objPrompts]
            
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt for ModelId {ModelId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e

            except SQLAlchemyError as e:
                # Log the SQLAlchemy error
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt for ModelId {ModelId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500,
                    detail="An error occurred while retrieving the Model."
                )
    
    @staticmethod
    async def MSGetPromptByID(iUserID: int, promptId: int):
        async with AsyncSessionLocal() as db:
            try:
                # Execute the query to find the prompt by ID and UserID
                query_result = await db.execute(
                    select(Prompt).filter(
                        Prompt.Id == promptId
                    )
                )
                prompt = query_result.scalars().one_or_none()

                if prompt is None:
                    await CLogController.MSWriteLog(iUserID, "Info", f"No prompt found with ID {promptId}.")
                    raise HTTPException(
                        status_code=404,
                        detail=f"The requested model could not be found."
                    )

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully retrieved prompt with ID {promptId}.")

                return {
                    "Id": prompt.Id,
                    "UserID": prompt.UserID,
                    "ModelId": prompt.ModelId,
                    "ModelSeries": prompt.ModelSeries,
                    "Prompt": prompt.prompt,
                    "CreatedDateTime": str(prompt.CreatedDateTime),
                    "UpdatedDateTime": str(prompt.UpdatedDateTime)
                }
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt ID {promptId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt ID {promptId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500,
                    detail="An error occurred while retrieving the Model."
                )