import { useState, useEffect, useCallback } from 'react';
import axios from 'axios';
import { Button } from "@material-tailwind/react";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import toast, { Toaster } from "react-hot-toast";
import _ from 'lodash';
import { BsPlus } from "react-icons/bs";
import AddUserModal from './AddUserPage';
import UpdateUserModal from './UpdateUserModal'

const UserListPage = () => {
    const [columns, setColumns] = useState([]);
    const [users, setUsers] = useState([]);
    const [roles, setRoles] = useState([]);
    const [activePage, setActivePage] = useState(1);
    const [totalPages, setTotalPages] = useState(0);
    const itemsPerPage = 10;
    const [isAddUserModalOpen, setIsAddUserModalOpen] = useState(false);
    const [isUpdateUserModalOpen, setIsUpdateUserModalOpen] = useState(false);
    const [currentUser, setCurrentUser] = useState(null);

    const fetchData = async (page) => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/users?page=${page}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
            
            if (response.data.users.length > 0) {
                setColumns(Object.keys(response.data.users[0])); // Set columns from keys of the first user object
            }
            
            setUsers(response.data.users);
            setRoles(response.data.roles);
            // Set columns from the first user object keys or predefined
            setColumns(response.data.users[0] ? Object.keys(response.data.users[0]) : ['uid', 'name', 'email', 'phoneNumber', 'Country']);
            setTotalPages(response.data.total_pages);
        } catch (error) {
            console.error('Failed to fetch users', error);
            toast.error("Failed to fetch users.");
        }
    };

    const handleEditUserClick = (user) => {
        setCurrentUser(user); // Set the user to be edited
        setIsUpdateUserModalOpen(true); // Open the modal
    };
    useEffect(() => {
        fetchData(activePage);
    }, [activePage]);

    const prevPage = () => {
        setActivePage(Math.max(1, activePage - 1));
    };

    const nextPage = () => {
        setActivePage(Math.min(totalPages, activePage + 1));
    };

    const calculateIndex = (index) => {
        return (activePage - 1) * itemsPerPage + index + 1;
    };
    const handleAddUserClick = () => {
        setIsAddUserModalOpen(true);
    };

    const handleCloseModal = () => {
        setIsAddUserModalOpen(false);
    };

    const handleEditUserCloseModal = () => {
        setIsUpdateUserModalOpen(false);
    };

    return (
        <>
            <Toaster position="top-center" />
            <div className="px-6 py-2 pt-4">
                <h1 className="text-sm font-semibold text-[#707EAE]">Users List</h1>
                <h1 className="text-3xl font-semibold text-[#3F3F3F]">All Users</h1>
            </div>
            <div className="p-6 bg-[#ffff] m-7 rounded-xl shadow-lg">
                <div className="flex items-center justify-end mb-6 space-x-3 ">
                    <button 
                            onClick={handleAddUserClick}
                            className="bg-[#003654] hover:bg-[#002744] text-white mx-3 py-2 px-4 rounded-xl cursor-pointer flex items-center"
                            >
                            <BsPlus className="h-5 w-5 mr-2" /> 
                            Add User
                    </button>
                    
                </div>
                <AddUserModal isOpen={isAddUserModalOpen} onClose={handleCloseModal} roles={roles} />

                {
                
                users.length == 0 ? (
                        <div className="flex justify-center">
                            <img src={`animations/noData.png`} className='h-[50vh]' alt="No data available" />
                        </div>
                    ):
                    (
                    <div className="overflow-x-auto">
                        <table className="min-w-full leading-normal">
                            <thead>
                                <tr className="text-left text-gray-700">
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Sr No</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Name</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Email</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Role</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Phone No.</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Country</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">API Usage</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Used Tokens</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Pro Page Limit Usage</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Standard Page Limit Usage</th>
                                    <th className="px-5 py-3 border-b-2 border-gray-200 font-medium">Edit</th>
                                </tr>
                            </thead>
                            <tbody>
                                {users.map((user, index) => (
                                    <tr key={user.id}>
                                        <td className="px-5 py-3 border-b border-gray-200">{calculateIndex(index)}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.name}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.email}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.role}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.phoneNumber}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.Country}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.api_requested}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.used_tokens}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.total_allowed_page_limit - user.page_limit_left}/{user.total_allowed_page_limit}</td>
                                        <td className="px-5 py-3 border-b border-gray-200">{user.free_page_limit_usage}/{user.total_allowed_free_page_limit}</td>

                                        <td className="px-5 py-3 border-b border-gray-200">
                                            <button onClick={() => handleEditUserClick(user)} className="bg-[#BCBCBC] hover:bg-[#afafaf] text-white font-bold py-2 px-4 rounded cursor-pointer flex items-center justify-center">
                                                Edit
                                            </button>
                                        </td>
                                        {
                                            isUpdateUserModalOpen && currentUser && (
                                                <UpdateUserModal isOpen={isUpdateUserModalOpen} onClose={handleEditUserCloseModal} user={currentUser} roles={roles} />
                                            )
                                        }
                                        
                                    </tr>
                                ))}
                            </tbody>
                        </table>
                    </div>
                )}
                

                <div className="flex justify-end items-center mt-6">
                    <Button variant="text" onClick={prevPage} disabled={activePage === 1}>
                        <IoIosArrowBack className="h-4 w-4" />
                    </Button>
                    {Array.from({ length: totalPages }, (_, index) => (
                        <button
                            key={index}
                            className={`px-3 py-1 ${activePage === index + 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md`}
                            onClick={() => setActivePage(index + 1)}
                        >
                            {index + 1}
                        </button>
                    ))}
                    <Button variant="text" onClick={nextPage} disabled={activePage === totalPages}>
                        <IoIosArrowForward className="h-4 w-4" />
                    </Button>
                </div>
            </div>
        </>
    );
};

export default UserListPage;
