import PropTypes from 'prop-types';
import { TransformWrapper, TransformComponent } from 'react-zoom-pan-pinch';
import { AiOutlineZoomIn, AiOutlineZoomOut, AiOutlineCloudDownload } from "react-icons/ai";
import { TbZoomReset } from "react-icons/tb";
import { Tooltip } from '@material-tailwind/react';
import React, { useEffect, useRef, useState } from 'react';
import ScrollContainer from 'react-indiana-drag-scroll';
import { Document, Page } from 'react-pdf';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import 'react-pdf/dist/esm/Page/TextLayer.css';

// Function to get MIME type from file extension
const getMimeType = (fileName) => {
    const extension = fileName.split('.').pop().toLowerCase();
    switch (extension) {
        case 'png': return 'image/png';
        case 'jpg':
        case 'jpeg': return 'image/jpeg';
        case 'webp': return 'image/webp';
        case 'bmp': return 'image/bmp';
        case 'pdf': return 'application/pdf';
        default: return 'application/octet-stream';
    }
};

// Function to download the document
const downloadDocument = (docData, fileName) => {
    const mimeType = getMimeType(fileName);
    const byteCharacters = atob(docData);
    const byteNumbers = new Array(byteCharacters.length);
    for (let i = 0; i < byteCharacters.length; i++) {
        byteNumbers[i] = byteCharacters.charCodeAt(i);
    }
    const byteArray = new Uint8Array(byteNumbers);
    const blob = new Blob([byteArray], { type: mimeType });
    const url = URL.createObjectURL(blob);
    const a = document.createElement('a');
    a.href = url;
    a.download = fileName;
    document.body.appendChild(a);
    a.click();
    document.body.removeChild(a);
    URL.revokeObjectURL(url);
};

const iconSize = window.innerWidth >= 1280 && window.innerWidth < 1600 ? '15px' : '19px';

// Controls component for zooming
const Controls = ({ zoomIn, zoomOut, resetTransform, docData, fileName }) => {
    return (
        <div className="fixed mb-4 grid grid-cols-1 gap-5 z-10 rounded-md">
            <Tooltip content='Zoom In'>
                <button type='button' onClick={zoomIn} className="bg-[#fff] p-2 rounded-md shadow-md hover:bg-gray-300">
                    <AiOutlineZoomIn style={{ fontSize: iconSize, cursor: 'pointer', color: '#003654' }} />
                </button>
            </Tooltip>
            <Tooltip content='Zoom Out'>
                <button type='button' onClick={zoomOut} className="bg-[#fff] p-2 rounded-md shadow-md hover:bg-gray-300">
                    <AiOutlineZoomOut style={{ fontSize: iconSize, cursor: 'pointer', color: '#003654' }} />
                </button>
            </Tooltip>
            <Tooltip content='Zoom Reset'>
                <button type='button' onClick={resetTransform} className="bg-[#fff] p-2 rounded-md shadow-md hover:bg-gray-300">
                    <TbZoomReset style={{ fontSize: iconSize, cursor: 'pointer', color: '#003654' }} />
                </button>
            </Tooltip>
            <Tooltip content='Download file'>
                <button type='button' onClick={() => downloadDocument(docData, fileName)} className="bg-[#fff] p-2 rounded-md shadow-md hover:bg-gray-300">
                    <AiOutlineCloudDownload style={{ fontSize: iconSize, cursor: 'pointer', color: '#003654' }} />
                </button>
            </Tooltip>
        </div>
    );
};

Controls.propTypes = {
    zoomIn: PropTypes.func.isRequired,
    zoomOut: PropTypes.func.isRequired,
    resetTransform: PropTypes.func.isRequired,
    docData: PropTypes.string.isRequired,
    fileName: PropTypes.string.isRequired
};

const DocViewer = ({ highlightCoords, contentUrl, contentType, isTable, docData, fileName, isOpen, pageHeight, pageWidth }) => {
    const [isGrabbing, setIsGrabbing] = useState(false);
    const [numPages, setNumPages] = useState(null);
    const [zoomLevel, setZoomLevel] = useState(1);
    const [pageW, setPageW] = useState(0);

    const [top, setTop] = useState(0);
    const [left, setleft] = useState(0);
    const [highlightWidth, setHighlightWidth] = useState(0);
    const [highlightHeight, setHighlightHeight] = useState(0);
    const highlightRef = useRef(null);
    const containerRef = useRef(null);

    const handleMouseDown = () => {
        if (zoomLevel !== 1) {
            setIsGrabbing(true);
        }
    };

    const getWidthClass = () => {
        const screenWidth = window.innerWidth;
        if (screenWidth >= 1699) {
            return isOpen ? 'w-[52vw]' : 'w-[57vw]';
        } else if (screenWidth >= 1280 && screenWidth < 1600) {
            return isOpen ? 'w-[49vw]' : 'w-[58vw]';
        }
    };

    useEffect(() => {
        const handleResize = () => {
            const screenWidth = window.innerWidth;
            if (screenWidth >= 1280 && screenWidth < 1600) {
                setPageW(isOpen ? 630 : 720);
            } else if (screenWidth >= 1600) {
                setPageW(isOpen ? 975 : 1158);
            }
        };

        handleResize();
        window.addEventListener('resize', handleResize);
        return () => {
            window.removeEventListener('resize', handleResize);
        };
    }, [isOpen]);

    const handleMouseUp = () => {
        setIsGrabbing(false);
    };

    const onDocumentLoadSuccess = ({ numPages }) => {
        setNumPages(numPages);
    };

    const calculateHighlightStyle = (coords, contentDimensions) => {
        if (!coords || !contentDimensions) return {};

        const { x1, y1, x2, y2 } = coords;
        const { width, height } = contentDimensions;

        setTop((y1 / height) * 100)
    
        setleft((x1 / width) * 100)
    
        setHighlightWidth(((x2 - x1) / width) * 100)
    
        setHighlightHeight(((y2 - y1) / height) * 100)

        return {
            position: 'absolute',
            top: `${top}%`,
            left: `${left}%`,
            width: `${highlightWidth}%`,
            height: `${highlightHeight}%`,
            backgroundColor: 'rgba(171, 216, 20, 0.4)',
            pointerEvents: 'none'
        };
    };

    const renderContent = () => {
        switch (contentType) {
            case 'image':
                return (
                    <div className="relative">
                        <img
                            src={contentUrl}
                            alt="Document"
                            className={`max-w-[100vw] ${getWidthClass()} max-h-full`}
                        />
                        <div ref={highlightRef} className={`${Object.keys(highlightCoords).length > 0 ? '' : 'hidden'}`}
                            style={calculateHighlightStyle(highlightCoords, { width: pageWidth, height: pageHeight })}
                        />
                    </div>
                );
            case 'pdf':
                return (
                    <>
                        <Document
                            file={contentUrl}
                            onLoadSuccess={onDocumentLoadSuccess}
                            className="w-full"
                        >
                            {Array.from(new Array(numPages), (el, index) => {


                                return (
                                    <div
                                        key={`page_${index + 1}`}
                                        className="mb-5 flex justify-center w-full"
                                    >
                                        <Page
                                            pageNumber={index + 1}
                                            width={pageW}
                                            className="shadow-md relative"
                                            renderTextLayer={false}
                                        >
                                            {Object.keys(highlightCoords).length > 0 && highlightCoords.page === index + 1 && (
                                                <div
                                                    ref={highlightRef}
                                                    style={calculateHighlightStyle(highlightCoords, { width: pageWidth, height: pageHeight })}
                                                />
                                            )}
                                        </Page>
                                    </div>
                                );
                            })}
                        </Document>
                    </>
                );
            case 'text': {
                const decodedText = atob(docData);
                return (
                    <div className={`text-content p-4 bg-white overflow-auto h-full ${isOpen ? 'w-[52vw]' : 'w-[57.5vw]'} relative cursor-default`}>
                        <pre className="whitespace-pre-wrap">{decodedText}</pre>
                        <div ref={highlightRef} className={`${Object.keys(highlightCoords).length > 0 ? '' : 'hidden'}`}
                            style={calculateHighlightStyle(highlightCoords, { width: pageWidth, height: pageHeight })}
                        />
                    </div>
                );
            }
            default:
                return <div>Unsupported content type</div>;
        }
    };

    useEffect(() => {
        if (highlightCoords) {
            const highlightElement = highlightRef.current;
            if (highlightElement) {
                highlightElement.scrollIntoView({ behavior: 'smooth', block: 'center' });
            }
        }
        
    } ,[highlightCoords, top, left, highlightWidth, highlightHeight]);

    useEffect(() => {
        if (containerRef.current) {
            containerRef.current.style.transform = `scale(${zoomLevel})`;
            containerRef.current.style.overflow = zoomLevel === 1 ? 'hidden' : 'auto';
        }
    }, [zoomLevel]);

    return (
        <div className="w-full h-full pt-4">
            <TransformWrapper
                wheel={{ disabled: true }}
                options={{
                    limitToBounds: false,
                    disablePadding: false,
                    centerContent: false,
                    minScale: 1,
                    alignmentAnimation: { sizeX: 0, sizeY: 0 },
                    velocityAnimation: { disabled: true }
                }}
                panning={{
                    disabled: zoomLevel === 1,
                    velocityDisabled: true,
                    x: false
                }}
                onPanningStart={handleMouseDown}
                onPanningStop={handleMouseUp}
                onZoom={(ref) => setZoomLevel(ref.state.scale)}
                doubleClick={{ disabled: true }}
            >
                {({ zoomIn, zoomOut, resetTransform }) => (
                    <React.Fragment>
                        <ScrollContainer className='h-full opacity-90' hideScrollbars="false" activationDistance={1}>
                            <Controls
                                zoomIn={() => {
                                    zoomIn(0.2);
                                    if (zoomLevel <= 2.100000000000001) {
                                        setZoomLevel((prevLevel) => prevLevel + 0.1);
                                    }
                                }}
                                zoomOut={() => {
                                    zoomOut(0.2);
                                    if (zoomLevel >= 1) {
                                        setZoomLevel((prevLevel) => Math.max(prevLevel - 0.1, 1));
                                    }
                                }}
                                resetTransform={() => {
                                    resetTransform();
                                    setZoomLevel(1);
                                }}
                                docData={docData}
                                fileName={fileName}
                            />
                            <div
                                className={`${isOpen ? 'mx-[3vw]' : 'mx-[3.3vw]'} ${isTable ? 'h-[50vh]' : 'h-[80vh]'}`}
                                onMouseDown={handleMouseDown}
                                onMouseUp={handleMouseUp}
                                onMouseLeave={handleMouseUp}
                            >
                                <div
                                    ref={containerRef}
                                    className={`${zoomLevel === 1 ? 'cursor-default' : isGrabbing ? "cursor-grabbing" : "cursor-grab"}`}
                                    style={{ width: 'max-content', cursor: isGrabbing ? 'grabbing' : 'grab' }}
                                >
                                    <TransformComponent>
                                        {renderContent()}
                                    </TransformComponent>
                                </div>
                            </div>
                        </ScrollContainer>
                    </React.Fragment>
                )}
            </TransformWrapper>
        </div>
    );
};

DocViewer.propTypes = {
    highlightCoords: PropTypes.object.isRequired,
    contentUrl: PropTypes.string.isRequired,
    contentType: PropTypes.string.isRequired,
    isTable: PropTypes.bool.isRequired,
    docData: PropTypes.string.isRequired,
    fileName: PropTypes.string.isRequired,
    isOpen: PropTypes.bool.isRequired,
    pageHeight: PropTypes.number.isRequired,
    pageWidth: PropTypes.number.isRequired
};

export default DocViewer;