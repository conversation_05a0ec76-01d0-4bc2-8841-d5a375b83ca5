from dotenv import load_dotenv
from os.path import join, dirname
import os
from fastapi import FastAPI
from config.constants import Constants
from src.Routes.ExternalDocExtractAPI import ExternalExtractDocAPI
from src.utilities.helperFunc import CORSHelper

# Load environment variables
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

# Initialize the FastAPI application for GPTResponseRouter on port 9000
app_gpt = FastAPI(
    title="GptExt app - ExternalExtractDocAPI",
    version=Constants.APP_VERSION,
)
CORSHelper.setup_cors(app_gpt)
app_gpt.include_router(ExternalExtractDocAPI)

@app_gpt.get("/")
def read_root():
    return {"health-check": "Good"}

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app_gpt, host="0.0.0.0", port=9000)
