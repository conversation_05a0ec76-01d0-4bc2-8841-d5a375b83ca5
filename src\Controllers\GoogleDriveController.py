import logging
import os
import io
import pandas as pd
from io import Bytes<PERSON>
from datetime import datetime
from pathlib import Path
from typing import Optional, Dict, Any
from dotenv import load_dotenv
from google.oauth2.service_account import Credentials
from googleapiclient.discovery import build
from googleapiclient.http import MediaIoBaseDownload
from googleapiclient.errors import HttpError

from tenacity import retry, stop_after_attempt, wait_exponential, retry_if_exception_type

# Specify the custom path for the log file
today = datetime.now().strftime('%Y-%m-%d')
log_directory = os.path.join('Logs', 'Google Drive Logs', today)  # Replace with your desired path
log_file = os.path.join(log_directory, 'google_drive_service.log')

# Ensure the directory exists
os.makedirs(log_directory, exist_ok=True)

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler(log_file),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)

# Configuration
load_dotenv()
SCOPES = ['https://www.googleapis.com/auth/drive.readonly']
TALLY_PREFIX = "AI MASTER SHEET"
DOWNLOAD_PATH = "Data\Customer\Abhinav InfraBuild\Mappings"


class GoogleDriveService:
    """Service for interacting with Google Drive API using a service account."""

    def __init__(self):
        """Initialize the Google Drive service with authenticated credentials."""
        self.service = self._build_service()

    @staticmethod
    def _authenticate() -> Credentials:
        """Authenticate using a service account and return credentials.

        Returns:
            Credentials: Google API credentials for the service account.

        Raises:
            FileNotFoundError: If the credentials file is not found.
            ValueError: If the credentials file is invalid.
            HttpError: If authentication fails due to API errors.
        """
        credentials_path = os.getenv('GOOGLE_APPLICATION_CREDENTIALS')
        if not credentials_path or not os.path.exists(credentials_path):
            logger.error("Credentials file not found at %s", credentials_path)
            raise FileNotFoundError(f"Credentials file not found at {credentials_path}")
        
        try:
            creds = Credentials.from_service_account_file(credentials_path, scopes=SCOPES)
            logger.info("Successfully authenticated with service account")
            return creds
        except ValueError as e:
            logger.error("Invalid service account credentials: %s", e)
            raise
        except HttpError as e:
            logger.error("Authentication failed: %s", e)
            raise

    def _build_service(self) -> Any:
        """Build and return the Google Drive API service.

        Returns:
            Any: Google Drive API service object.

        Raises:
            HttpError: If service creation fails.
        """
        try:
            creds = self._authenticate()
            service = build('drive', 'v3', credentials=creds, cache_discovery=False)
            return service
        except HttpError as e:
            logger.error("Failed to build Drive service: %s", e)
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(HttpError),
        before_sleep=lambda retry_state: logger.warning("Retrying API call due to error: %s", retry_state.outcome.exception())
    )
    def get_folder_id(self, folder_name: str, parent_folder_id: Optional[str] = None) -> Optional[str]:
        """Find a folder by name and return its ID.

        Args:
            folder_name: Name of the folder to search for.
            parent_folder_id: Optional ID of the parent folder to scope the search.

        Returns:
            Optional[str]: The folder ID if found, else None.

        Raises:
            HttpError: If the API request fails.
            ValueError: If folder_name is empty or invalid.
        """
        if not folder_name or not isinstance(folder_name, str):
            logger.error("Invalid folder name: %s", folder_name)
            raise ValueError("Folder name must be a non-empty string")

        try:
            query = f"name='{folder_name}' and mimeType='application/vnd.google-apps.folder' and trashed=false"
            if parent_folder_id:
                query += f" and '{parent_folder_id}' in parents"
            results = self.service.files().list(
                q=query,
                pageSize=10,
                fields="files(id, name)",
                supportsAllDrives=True
            ).execute()
            folders = results.get('files', [])
            if not folders:
                logger.warning("No folder named '%s' found", folder_name)
                return None
            folder_id = folders[0]['id']
            logger.info("Found folder '%s' with ID: %s", folder_name, folder_id)
            return folder_id
        except HttpError as e:
            logger.error("Error searching for folder '%s': %s", folder_name, e)
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(HttpError),
        before_sleep=lambda retry_state: logger.warning("Retrying API call due to error: %s", retry_state.outcome.exception())
    )
    def get_latest_file(self, folder_id: Optional[str] = None) -> Optional[Dict[str, Any]]:
        """Get the latest file with 'Tally_ERP_Mapping' in its name from the specified folder.

        Args:
            folder_id: Optional ID of the folder to search in. If None, searches the root.

        Returns:
            Optional[Dict[str, Any]]: Metadata of the latest matching file, or None if none found.

        Raises:
            HttpError: If the API request fails.
        """
        try:
            query = f"'{folder_id}' in parents and trashed=false" if folder_id else "trashed=false"
            logger.debug("No files matched '%s', trying fallback query: %s", TALLY_PREFIX, query)
            results = self.service.files().list(
                q=query,
                pageSize=100,
                fields="files(id, name, createdTime, mimeType)",
                orderBy="createdTime desc",
                supportsAllDrives=True
            ).execute()
            files = results.get('files', [])
            
            # Filter files with 'Tally' (case-insensitive)
            matching_files = [f for f in files if TALLY_PREFIX.lower() in f['name'].lower()]
            if not matching_files:
                logger.warning("No files with '%s' in name found in folder ID %s", TALLY_PREFIX, folder_id)
                logger.debug("All files in folder: %s", [{'name': f['name'], 'id': f['id'], 'mimeType': f['mimeType']} for f in files])
                return None
            
            latest_file = matching_files[0]
            logger.info("Found %d files with '%s' (case-insensitive), returning latest: %s", len(matching_files), TALLY_PREFIX, latest_file['name'])
            return latest_file
        except HttpError as e:
            logger.error("Error listing files: %s", e)
            raise

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(HttpError),
        before_sleep=lambda retry_state: logger.warning("Retrying API call due to error: %s", retry_state.outcome.exception())
    )
    def download_file(self, file_id: str, file_name: str, download_path: str) -> Optional[str]:
        """Download a file from Google Drive to the specified path.

        Args:
            file_id: ID of the file to download.
            file_name: Name of the file to save.
            download_path: Directory path to save the file.

        Returns:
            Optional[str]: Path to the downloaded file, or None if the download fails.

        Raises:
            HttpError: If the API request fails.
            ValueError: If file_id or download_path is invalid.
        """
        if not file_id or not isinstance(file_id, str):
            logger.error("Invalid file ID: %s", file_id)
            raise ValueError("File ID must be a non-empty string")
        if not download_path or not isinstance(download_path, str):
            logger.error("Invalid download path: %s", download_path)
            raise ValueError("Download path must be a non-empty string")

        try:
            mime_type = self.service.files().get(fileId=file_id, fields='mimeType').execute().get('mimeType')
            if mime_type == 'application/vnd.google-apps.spreadsheet':
                request = self.service.files().export_media(fileId=file_id, mimeType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
                file_name = file_name if file_name.endswith('.xlsx') else file_name + '.xlsx'
            else:
                request = self.service.files().get_media(fileId=file_id)
            
            file_path = os.path.join(download_path, file_name)
            Path(download_path).mkdir(parents=True, exist_ok=True)
            with io.FileIO(file_path, 'wb') as fh:
                downloader = MediaIoBaseDownload(fh, request)
                done = False
                while not done:
                    status, done = downloader.next_chunk()
                    logger.debug("Download progress: %d%%", int(status.progress() * 100))
            logger.info("Downloaded file '%s' to %s", file_name, file_path)
            return file_path
        except HttpError as e:
            logger.error("Error downloading file ID %s: %s", file_id, e)
            return None

    @retry(
        stop=stop_after_attempt(3),
        wait=wait_exponential(multiplier=1, min=4, max=10),
        retry=retry_if_exception_type(HttpError),
        before_sleep=lambda retry_state: logger.warning("Retrying API call due to error: %s", retry_state.outcome.exception())
    )
    def read_file(self, file_id: str, bisMultipleSheet = False) -> Optional[pd.DataFrame]:
        """Read the content of a file from Google Drive as a pandas DataFrame.

        Args:
            file_id: ID of the file to read (Excel or Google Sheets).

        Returns:
            Optional[pd.DataFrame]: DataFrame containing the file content, or None if the read or parsing fails.

        Raises:
            ValueError: If the file_id is invalid.
            HttpError: If the API request fails.
            pd.errors.EmptyDataError: If the file is empty or cannot be parsed as a DataFrame.
            Exception: For other file processing errors.
        """
        if not file_id or not isinstance(file_id, str):
            logger.error("Invalid file ID: %s", file_id)
            raise ValueError("File ID must be a non-empty string")

        try:
            mime_type = self.service.files().get(fileId=file_id, fields='mimeType').execute().get('mimeType')
            if mime_type == 'application/vnd.google-apps.spreadsheet':
                request = self.service.files().export_media(fileId=file_id, mimeType='application/vnd.openxmlformats-officedocument.spreadsheetml.sheet')
            else:
                request = self.service.files().get_media(fileId=file_id)

            fh = BytesIO()
            downloader = MediaIoBaseDownload(fh, request)
            done = False
            while not done:
                status, done = downloader.next_chunk()
                logger.debug("Download progress for reading file %s: %d%%", file_id, int(status.progress() * 100))
            fh.seek(0)

            # Read into DataFrame
            if bisMultipleSheet:
                df = pd.read_excel(fh, sheet_name=None)
            else:
                df = pd.read_excel(fh)
            if (isinstance(df, dict) and all(d.empty for d in df.values())) or (isinstance(df, pd.DataFrame) and df.empty):
                logger.warning("Empty DataFrame for file ID %s", file_id)
                return None
            logger.info("Successfully read file ID %s as DataFrame with %d rows", file_id, len(df))
            return df
        except HttpError as e:
            logger.error("Error reading file ID %s: %s", file_id, e)
            return None
        except pd.errors.EmptyDataError:
            logger.error("File ID %s is empty or cannot be parsed as a DataFrame", file_id)
            return None
        except Exception as e:
            logger.error("Error processing file ID %s as DataFrame: %s", file_id, e)
            return None

    def handle_file(
        self,
        download: bool = False,
        path: str = DOWNLOAD_PATH,
        folder_name: Optional[str] = None,
        folder_id: Optional[str] = '1tNEv6IGMJdVz_UL3jxCcgcvS4Lo38k1Q',
        bisMultipleSheet= False
    ) -> Optional[str]:
        """Handle file operations: download or read the latest file with 'Tally_ERP_Mapping'.

        Args:
            download: If True, download the file; otherwise, read its content.
            path: Directory path to save downloaded files.
            folder_name: Optional name of the folder to search for.
            folder_id: Optional ID of the folder to search in.

        Returns:
            Optional[str]: Path to the downloaded file if download=True, else file content as bytes, or None if operation fails.

        Raises:
            ValueError: If both folder_name and folder_id are None, or if path is invalid.
        """
        if not folder_name and not folder_id:
            logger.error("Either folder_name or folder_id must be provided")
            raise ValueError("Either folder_name or folder_id must be provided")
        if download and (not path or not isinstance(path, str)):
            logger.error("Invalid download path: %s", path)
            raise ValueError("Download path must be a non-empty string")

        try:
            target_folder_id = folder_id
            if folder_name and not folder_id:
                target_folder_id = self.get_folder_id(folder_name)
                if not target_folder_id:
                    raise Exception("There was problem in getting Item ERP Matching File.") 

            latest_file = self.get_latest_file(target_folder_id)
            if not latest_file:
                raise Exception("There was problem in getting Item ERP Matching File.")

            logger.info("Processing latest file: %s (created at %s)", latest_file['name'], latest_file['createdTime'])

            if download:
                file_path = self.download_file(latest_file['id'], latest_file['name'], path)
                if file_path:
                    print("File Stored at: "+file_path)

            content = self.read_file(latest_file['id'],bisMultipleSheet)
            if content is not None:
                # logger.debug("File content preview (first 500 bytes): %s", content[:500])

                return content
            else:
                raise Exception("There was problem in getting Item ERP Matching File.")
        except Exception as e:
            logger.error("Error handling file: %s", e)
            raise e

if __name__ == '__main__':
    try:
        drive_service = GoogleDriveService()
        result = drive_service.handle_file(bisMultipleSheet=True)
        print(result)
        if not result.empty:
            logger.info("Operation completed successfully: %s", result)
        else:
            logger.warning("Operation failed or no file was processed")
    except Exception as e:
        logger.error("Main execution failed: %s", e)
        