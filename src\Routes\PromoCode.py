from fastapi import APIRouter, Depends, HTTPException, Form, Query, Path
from datetime import datetime, timedelta
from typing import Optional

from src.middleware.checkAuth import admin_required, user_required
from src.Controllers.referralCodeController import CReferralCode

# Declaring the API router with a specific tag and prefix
PromoCodeRouter = APIRouter(tags=['PromoCode'], prefix="/api")

# API endpoint to set a new promo code
@PromoCodeRouter.post("/PromoCode/")
async def set_promo_code(
    promo_name: str = Query(...),
    promo_code_type: str = Query(...),
    action: str = Query(...),
    value: str = Query(...),
    expire_time: Optional[datetime] = None
    ,admin: bool = Depends(admin_required)
):
    # Set default expiration time to 30 days ahead if not provided
    if expire_time is None:
        expire_time = datetime.now() + timedelta(days=30)
    result = await CReferralCode.MSSetReferralCode(userid=admin, promo_name=str(promo_name).upper(), promo_code_type=promo_code_type, action=action, value=value, expire_time=expire_time)
    if result:
        return {"message": "Promo code created successfully."}
    else:
        raise HTTPException(status_code=400, detail="Failed to create promo code.")

# API endpoint to get a promo code by name
@PromoCodeRouter.get("/PromoCode/{promo_name}")
async def get_promo_code_by_name(promo_name: str):
    promo_code = await CReferralCode.MSGetReferralCodeByName(promo_name)
    if promo_code:
        return promo_code
    else:
        raise HTTPException(status_code=404, detail="Promo code not found.")
    
# API endpoint to get all promo code by name
@PromoCodeRouter.get("/PromoCode/")
async def get_all_promo_codes(include_expired: bool = False,admin: bool = Depends(admin_required), user_id :int = Depends(user_required) ):
    try:
        # Call the controller method to retrieve all promo codes
        promo_codes = await CReferralCode.MSGetAllReferralCodes(user_id=user_id, includeExpiredPromoCode=include_expired)
        # Return the retrieved promo codes
        return promo_codes
    except HTTPException as e:
        # If an HTTPException is raised in the controller method, re-raise it
        raise e
    except Exception as e:
        # If any other exception occurs, raise a 500 Internal Server Error with a generic error message
        raise HTTPException(status_code=500, detail="An error occurred while processing the request.")
    
# API endpoint to check if a promo code is valid
@PromoCodeRouter.get("/PromoCode/valid/{promo_name}")
async def is_valid_promo_code(promo_name: str):
    is_valid = await CReferralCode.MSIsValidReferralCode(promo_name)
    if is_valid.get("status"):
        return is_valid
    else:
        raise HTTPException(status_code=404, detail="Promo code is invalid or expired.")
    
@PromoCodeRouter.put("/PromoCode/update-usage-count/{promo_id}")
async def updateRefferalCodeCount(
    promo_id: int = Path(..., description="The ID of the promo code to update"),
    action: str = Query("subtract", description="The action to perform, either 'add' or 'subtract'"),
    value: int = Query(1, description="The value to add or subtract from the MaxPromptCodeUseCount"),admin: bool = Depends(admin_required)
):
    try:
        # Call the static method to update the MaxPromptCodeUseCount
        result = await CReferralCode.MSUpdateRefferalCodeCount(promoId=promo_id, action=action, value=value)
        return result
    except HTTPException as e:
        raise e
