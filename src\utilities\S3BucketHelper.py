import boto3
import hashlib
import os
import uuid
import io  # Add this import at the beginning of your file
from io import BytesIO
import sys
from fastapi import HTTPException
sys.path.append(".")
from botocore.exceptions import NoCredentialsError, PartialCredentialsError, ClientError
from config.constants import Constants
from dotenv import load_dotenv
from src.Controllers.Logs_Controller import CLogController

# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)


class CAWSS3Storage:
    # Read the value of S3 configuration file
    access_key = os.getenv('s3_bucket_access_key')
    secret_key = os.getenv('S3_bucket_secret_key')
    region_name = Constants.strS3BucketRegionName
    bucket_name = os.getenv('s3_bucket_name')
    bugs_bucket_name = os.getenv('s3_bucket_name_for_bugs')
    
    def __init__(self, object_name, user_id=None, bucket_name = None):
        self.object_name = object_name
        self.user_id = user_id
        self.s3_client = CAWSS3Storage.MSConfigureS3Session(bucket_name = bucket_name)
    
    @staticmethod
    def MSConfigureS3Session(bucket_name=None):
        try:
            # Take Default Bucket Name accuvelocity
            if bucket_name is None:
                bucket_name = CAWSS3Storage.bucket_name
            else:
                bucket_name = bucket_name
                CAWSS3Storage.bucket_name = bucket_name
            # Check if access_key, secret_key, and bucket_name are not None or empty
            if all([CAWSS3Storage.access_key, CAWSS3Storage.secret_key, bucket_name, CAWSS3Storage.region_name]):
                s3_client = boto3.client('s3', aws_access_key_id=CAWSS3Storage.access_key,
                                        aws_secret_access_key=CAWSS3Storage.secret_key,
                                        region_name=CAWSS3Storage.region_name)
                return s3_client
            else:
                raise ValueError("Access key, secret key, or bucket name is not provided or empty.")
        except (NoCredentialsError, PartialCredentialsError, ValueError) as e:
            error_message = f"Error configuring S3 client: {str(e)}"
            raise HTTPException(status_code=500, detail="Internal Server Error")

    async def MSetS3Object(self, file_data, cache_control="max-age=86400", metadata=None):
        try:
            if self.object_name and  self.object_name is not None:
                extra_args = {'CacheControl': cache_control}  # Cache control for 1 day
                if metadata:
                    extra_args['Metadata'] = metadata #, each metadata key must be prefixed with x-amz-meta- as required by AWS S3, and you can pass any string as the value
                
                file_stream = BytesIO(file_data)
                response = self.s3_client.upload_fileobj(file_stream, Bucket=CAWSS3Storage.bucket_name, Key=self.object_name,ExtraArgs=extra_args)
                # Check if the file exists in the S3 bucket after upload
                response = self.s3_client.head_object(Bucket=CAWSS3Storage.bucket_name, Key=self.object_name)
                if 'ResponseMetadata' in response and 'HTTPStatusCode' in response['ResponseMetadata']:
                    http_status_code = response['ResponseMetadata']['HTTPStatusCode']
                    if http_status_code == 200:
                        return {"message": "File uploaded successfully to S3.", "status_code": http_status_code}
                    else:
                        raise HTTPException(status_code=500, detail=f"Failed to verify file upload to S3. HTTP Status Code: {http_status_code}")
                else:
                    raise HTTPException(status_code=500, detail="Failed to verify file upload to S3.")
            else:
                raise ValueError("object Name not found")
        except ClientError as e:
            error_message = f"Failed to upload object: {str(e)}"
            await CLogController.MSWriteLog(self.user_id, "Error", error_message)
            raise HTTPException(status_code=500, detail="Internal Server Error")
    
    async def MGetS3Object(self, object_name):
        try:
            response = self.s3_client.get_object(Bucket=CAWSS3Storage.bucket_name, Key=object_name)
            data = response['Body'].read()
            metadata = response['Metadata']
            return data, metadata
        except ClientError as e:
            error_message = f"Failed to retrieve object: {object_name}, ERROR: {str(e)}"
            await CLogController.MSWriteLog(self.user_id, "Error", error_message)
            raise HTTPException(status_code=500, detail="Internal Server Error")
    
    @staticmethod
    def MSSetS3Object(object_name, file_data, fileType="object",cache_control="max-age=86400", metadata=None,bucket_name=None):
        try:
            # fileType is Enum [object, filePath]
            if object_name and  object_name is not None:
                # Take Default Bucket Name accuvelocity
                if bucket_name is None:
                    bucket_name = CAWSS3Storage.bucket_name
                extra_args = {'CacheControl': cache_control}  # Cache control for 1 day
                if metadata:
                    extra_args['Metadata'] = metadata #, each metadata key must be prefixed with x-amz-meta- as required by AWS S3, and you can pass any string as the value
                s3_client = CAWSS3Storage.MSConfigureS3Session(bucket_name)
                if fileType == "object":
                    file_stream = BytesIO(file_data)
                    response = s3_client.upload_fileobj(file_stream, Bucket=bucket_name, Key=object_name,ExtraArgs=extra_args)
                else:
                    # File Path Given
                    strFilePath = file_data
                    response = s3_client.upload_file(strFilePath, Bucket=bucket_name, Key=object_name,ExtraArgs=extra_args)
                # Check if the file exists in the S3 bucket after upload
                response = s3_client.head_object(Bucket=bucket_name, Key=object_name)
                if 'ResponseMetadata' in response and 'HTTPStatusCode' in response['ResponseMetadata']:
                    http_status_code = response['ResponseMetadata']['HTTPStatusCode']
                    if http_status_code == 200:
                        return {"message": "File uploaded successfully to S3.", "status_code": http_status_code}
                    else:
                        raise HTTPException(status_code=500, detail=f"Failed to verify file upload to S3. HTTP Status Code: {http_status_code}")
                else:
                    raise HTTPException(status_code=500, detail="Failed to verify file upload to S3.")
            else:
                raise ValueError("object Name not found")
        except ClientError as e:
            error_message = f"Failed to upload object: {str(e)}"
            raise HTTPException(status_code=500, detail="Internal Server Error")
        
    
    @staticmethod
    def MSGetS3Object(object_name, bucket_name = None):
        try:
            # Take Default Bucket Name accuvelocity
            if bucket_name is None:
                bucket_name = CAWSS3Storage.bucket_name
            s3_client = CAWSS3Storage.MSConfigureS3Session(bucket_name)
            response = s3_client.get_object(Bucket=bucket_name, Key=object_name)
            data = response['Body'].read()
            metadata = response['Metadata']
            return {"data":data, "metadata":metadata}
        except ClientError as e:
            error_message = f"Failed to retrieve object: {object_name}, ERROR: {str(e)}"
            raise HTTPException(status_code=500, detail="Internal Server Error")

    @staticmethod
    def MSGenerateUniqueObjecID():
        unique_id = str(uuid.uuid4())
        hash_digest = hashlib.sha256(unique_id.encode()).hexdigest()
        return hash_digest

# Example Usage:
if __name__ == "__main__":
    pass