import stripe
import os
from os.path import join, dirname
from dotenv import load_dotenv
# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")

load_dotenv(dotenv_path)

stripe.api_key = os.getenv('PAYMENT_INTEGRATION_SECRET_KEY')


class StripePayment:
    
    
    def __init__(self,bDebug=True):
        self.bDebug = bDebug
    
    @staticmethod    
    def MSSSetProduct(strProductName, strProductDesc, bDebug=True):
        starter_subscription = stripe.Product.create(
        name=strProductName,
        description=strProductDesc,
        )
        if bDebug:
            print("-----starter_subscription-----",starter_subscription)
        return {
            "ProductID":starter_subscription.id,
            "ProductDescription":starter_subscription.description,
            "livemode":starter_subscription.livemode,
            "active":starter_subscription.active
            }
    
    @staticmethod    
    def MSSSetProductPrice(strProductID, price:int, subscriptionInterval:str="month",currency:str = "usd",bDebug=True):
        starter_subscription_price = stripe.Price.create(
            unit_amount=price,
            currency=currency.lower(),
            recurring={"interval": subscriptionInterval},
            product=strProductID,
        )
        if bDebug:
            print("-----starter_subscription-----",starter_subscription_price)
        return {
            "ProductID":strProductID,
            "PriceID":starter_subscription_price.id,
            "type":starter_subscription_price.type,
            "recurring":starter_subscription_price.recurring,
            "livemode":starter_subscription.livemode,
            "currency":starter_subscription.currency,
            "active":starter_subscription.active
            }

if __name__ == "__main__":
    dictProduct = StripePayment.MSSSetProduct(strProductName="Starter Subscription",strProductDesc="$12/Month subscription")
    dictPrice = StripePayment.MSSSetProductPrice(strProductID=dictProduct.get("ProductID"), price=1200)
    