import subprocess
import re
import os
import traceback
import datetime  
import ast
import sys
sys.path.append(r".")

from src.utilities.BankStatementUtility import CSplitXMLFile

class CProcessBankStatement:

    @staticmethod
    async def MSGetBankStatementProcessingInfo(strProcessingResult, strXMLOutputDir=""):
        """
        Extracts both file paths and AV summary (XML Status) from the given processing result.
        
        Args:
            strProcessingResult (str): The string containing file paths and report details.
        
        Returns:
            dict: A dictionary containing 'FilePaths' and 'AVSummary'.
        """
        dictProcessingInfo = {
            "FilePaths": [],
            "AVSummary": {},
            "AVBankStatusRes":{}
        }
        
        bIsDuplicateBS = CProcessBankStatement.MSDetectDuplicateLog(input_string= strProcessingResult)
        if not bIsDuplicateBS:
            lsTransactionList = CProcessBankStatement.MSExtractTransactionIDs(strProcessingResult)
            strTallyPredictedXML = CProcessBankStatement.MSExtractXMLFilePath(strProcessingResult)
            # TODO: Please Customer Name Make it Dynamic 
            dictProcessingInfo["FilePaths"] = await CSplitXMLFile.MSplitAndSaveTallyXML(
                            strPredictedXLSXFile=strTallyPredictedXML,
                            strCustomerName="", # NOT USED anywhere
                            exportFolderPath=strXMLOutputDir,
                            rows_per_file=1,# Number of voucher in single XMl file     
                            lsTransactionList = lsTransactionList,
                        )
        else:
            dictProcessingInfo["FilePaths"] = "" # No XML File Would be sent to Customer in  Case of all transaction Duplicate Detected
        
        # Extract AV summary (XML Status)
        dictProcessingInfo["AVSummary"] = CProcessBankStatement.MSGetAVStatus(strProcessingResult)
        dictProcessingInfo['AVBankStatusRes'] = CProcessBankStatement.ExtractBankStatementResponse(strProcessingResult)
        dictProcessingInfo['BankStatement_UID'] = CProcessBankStatement.ExtractBankStatementID(strProcessingResult)
        return dictProcessingInfo
    
    @staticmethod
    def MSGetAVStatus(input_string: str) -> dict:
        """
        Extracts the AV (XML) Status and AccuVelocity summary from the dictionary in the input string.
        """
        av_summary = {}

        try:
            metrics_pattern = re.search(r"Metrics for statementId=\d+:\s+(\{.*\})", input_string, re.DOTALL)
            if metrics_pattern:
                dict_str = metrics_pattern.group(1)
                try:
                    av_summary = eval(dict_str, {"datetime": datetime})
                except Exception as e:
                    print(f"Failed to parse dictionary: {e}")
            else:
                print("No dictionary found in input string.")
        except Exception as e:
            print("Failed to extract AV Status from input string. Error:", e)

        return av_summary
    
    @staticmethod
    def ExtractBankStatementID(input_string: str) -> dict:
        """
        Extracts the statement ID from the input string in the format:
        'Inserted statement '{strFileName}' with ID {intStatementId} into database.'
        Returns a dictionary containing the statement ID.
        """
        iBankStatementId = None

        try:
            # Updated regex to match the pattern and capture intStatementId
            id_pattern = re.search(r"Inserted statement\s*'[^\']*'\s*with ID\s*(\d+)\s*into database", input_string)
            if id_pattern:
                iBankStatementId = int(id_pattern.group(1))
            else:
                print("No statement ID found in input string.")
        except Exception as e:
            print(f"Failed to extract statement ID from input string. Error: {e}")

        return iBankStatementId
    
    @staticmethod
    def ExtractBankStatementResponse(input_string: str) -> dict:
        """
        Extracts the dictionary from a string that includes 'Bank Statement Response: {dictAVRes}'.
        """
        try:
            # Find start of dictionary after the key phrase
            start_index = input_string.find("Bank Statement Response:")
            if start_index == -1:
                print("Bank Statement Response not found.")
                return {}

            # Find the first `{` after the key phrase
            dict_start = input_string.find("{", start_index)
            if dict_start == -1:
                print("Opening brace for dictionary not found.")
                return {}

            # Use bracket counting to extract complete dict
            brace_count = 0
            for i in range(dict_start, len(input_string)):
                if input_string[i] == '{':
                    brace_count += 1
                elif input_string[i] == '}':
                    brace_count -= 1
                    if brace_count == 0:
                        dict_end = i + 1
                        break
            else:
                print("Matching closing brace not found.")
                return {}

            dict_str = input_string[dict_start:dict_end]

            # Safely evaluate the dictionary
            return ast.literal_eval(dict_str)

        except Exception as e:
            print(f"Error while extracting Bank Statement Response: {e}")
            return {}
    
    @staticmethod
    def MSExtractFilePath(input_string: str) -> list:
        """
        Extracts valid .xlsx and .xml file paths from the given string.

        Args:
            input_string (str): The string containing the file paths.

        Returns:
            list: A list of extracted file paths (normalized).
        """
        file_paths = []
        
        # Regex patterns to capture both .xlsx and .xml file paths
        patterns = [
            r"Excel saved successfully:\s+(.*?\.xlsx)",  # For .xlsx files
            r"XML saved successfully:\s+(.*?\.xml)"    # For .xml files
        ]
        
        try:
            for pattern in patterns:
                matches = re.findall(pattern, input_string)
                for match in matches:
                    # Normalize the path and replace double backslashes
                    cleaned_path = os.path.normpath(match.replace('\\\\', '\\'))
                    file_paths.append(cleaned_path)
            
            if not file_paths:
                print("No valid file paths found.")
            
            return file_paths

        except Exception as e:
            print("Failed to extract file paths from output. Error:", e)
            return []
    
    @staticmethod
    def MSDetectDuplicateLog(input_string: str) -> bool:
        """
        Detects if the log indicates all transactions are duplicates based on a specific message.

        Args:
            input_string (str): The log string to analyze.

        Returns:
            bool: True if the log contains the duplicate transactions message, False otherwise.
        """
        # Regex pattern to capture the duplicate transactions message
        pattern = r"Skipping file exports since all are duplicate values"
        
        try:
            # Search for the pattern in the input string
            match = re.search(pattern, input_string)
            
            if match:
                return True
            else:
                print("No duplicate transactions message found.")
                return False

        except Exception as e:
            print("Failed to detect duplicate log message. Error:", e)
            return False

    @staticmethod
    def MSExtractTallyPredictedFilePath(input_string: str) -> list:
        """
        Extracts valid .xlsx file paths containing 'TallyPredicted_' from the given string.

        Args:
            input_string (str): The string containing the file paths.

        Returns:
            list: A list of extracted file paths (normalized) that contain 'TallyPredicted_'.
        """
 
        # Regex pattern to capture .xlsx file paths that contain 'TallyPredicted_'
        pattern = r"Excel saved successfully:\s+(.*?TallyPredicted_.*?\.xlsx)"
        cleaned_path = None
        try:
            matches = re.findall(pattern, input_string)
            for match in matches:
                # Normalize the path and replace double backslashes
                cleaned_path = os.path.normpath(match.replace('\\\\', '\\'))
                return cleaned_path
                # file_paths.append(cleaned_path)

            if not cleaned_path:
                print("No valid file paths found with 'TallyPredicted_'.")
            
            return cleaned_path

        except Exception as e:
            print("Failed to extract file paths from output. Error:", e)
            return cleaned_path

    @staticmethod
    def MSExtractTransactionIDs(input_string: str) -> list:
        """
        Extracts a list of transaction IDs from a string containing a pattern like:
        'List Of Transaction: [1, 2, 34]'

        Args:
            input_string (str): The string to extract transaction IDs from.

        Returns:
            list: A list of integer transaction IDs.
        """
        pattern = r"List Of Transaction:\s*\[([^\]]+)\]"

        try:
            match = re.search(pattern, input_string)
            if not match:
                print("No transaction list found in input.")
                return []

            # Extract and convert IDs to integers
            id_string = match.group(1)
            id_list =  [int(float(id.strip())) for id in id_string.split(',') if id.strip()]
            return id_list

        except Exception as e:
            print(f"Error extracting transaction IDs: {e}")
            return []


    @staticmethod
    def MSExtractXMLFilePath(input_string: str) -> list:
        """
        Extracts valid .xml file paths containing 'TallyXML_' from the given string.

        Args:
            input_string (str): The string containing the file paths.

        Returns:
            list: A list of extracted file paths (normalized) that contain 'TallyXML_'.
        """
 
        # Regex pattern to capture .xml file paths that contain 'TallyXML_'
        pattern = r"XML saved successfully:\s+(.*?\.xml)"    # For .xml files
        cleaned_path = None
        try:
            matches = re.findall(pattern, input_string)
            for match in matches:
                # Normalize the path and replace double backslashes
                cleaned_path = os.path.normpath(match.replace('\\\\', '\\'))
                return cleaned_path
                # file_paths.append(cleaned_path)

            if not cleaned_path:
                print("No valid file paths found with 'TallyXML_'.")
            
            return cleaned_path

        except Exception as e:
            print("Failed to extract file paths from output. Error:", e)
            return cleaned_path
        
    @staticmethod
    async def MSRunCMD(python_path, script_path, company, bank, file_path, working_directory, strXMLOutputDir , debug=False, strClientREQID:str = ""):
        """
        Executes a Python script in a specified virtual environment with a given working directory.

        Args:
            python_path (str): Path to the Python executable in the virtual environment.            script_path (str): Path to the script to be executed.
            company (str): Name of the company.
            bank (str): Name of the bank.
            file_path (str): Path to the input file.
            working_directory (str): Directory to set as the current working directory for the script.
            debug (bool): Whether to enable debug mode.
        """
        # Construct the command
        command = [
            python_path,
            script_path,
            "--company", company,
            "--bank", bank,
            "--file", file_path,
            "--strClientREQID",strClientREQID
        ]
        # command = ['D:\\Customer\\REAL\\AccuVelocity\\prodBankStatementPrediction\\envProdBankStatPred\\Scripts\\python.exe', 'D:\\Customer\\REAL\\AccuVelocity\prodBankStatementPrediction\\src\\main.py', '--company', 'test_gwalia', '--bank', 'BOB', '--file', 'H:\\AI Data\\DailyData\\airen\\2025_03_08\\demo_dup4.xlsx']
        # if debug:
        #     command.append("--debug")

        try:
            # Run the command with the specified working directory
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                cwd=working_directory  # Set the working directory
            )
            print("Command executed successfully!")
            print("Output:")
            print(result.stdout)
            dictResult = await CProcessBankStatement.MSGetBankStatementProcessingInfo(str(result),strXMLOutputDir)
            # lsFilePaths = dictResult["FilePaths"]
            # dictAVSummary = dictResult["AVSummary"]  
            # Fix the path formatting
            # strResult = strFilePath.replace('\\\\', '\\')  # Replace double backslashes with single backslashes

            # Alternative: Using os.path.normpath()
            # strResult = os.path.normpath(strResult)
            return dictResult
        except subprocess.CalledProcessError as e:
            print("Error during command execution:")
            print(e.stderr)

# NOTE: Do NOT USE THIS CGWALIA Class , ITS BACKUP CODE WHICH RUNS PERFECTLY in AV_bVersion1.20 Branch
class CGwalia:
    @staticmethod
    def MSGetBankStatementProcessingInfo(strProcessingResult):
        """
        Extracts both file paths and AV summary (XML Status) from the given processing result.
        
        Args:
            strProcessingResult (str): The string containing file paths and report details.
        
        Returns:
            dict: A dictionary containing 'FilePaths' and 'AVSummary'.
        """
        dictProcessingInfo = {
            "FilePaths": [],
            "AVSummary": {}
        }
        
        # Extract file paths
        dictProcessingInfo["FilePaths"] = CGwalia.MSExtractFilePath(strProcessingResult)
        
        # Extract AV summary (XML Status)
        dictProcessingInfo["AVSummary"] = CGwalia.MSGetAVStatus(strProcessingResult)
        
        return dictProcessingInfo
    
    @staticmethod
    def MSGetAVStatus(input_string: str) -> dict:
        """
        Extracts the AV (XML) Status and AccuVelocity summary from the dictionaries in the input string.
        
        Args:
            input_string (str): The string containing the dictionary data.
        
        Returns:
            dict: A dictionary containing the AccuVelocity summary (including XML Status).
        """
        av_summary = {}

        # Regex pattern to capture dictionary (Metrics for statementId)
        metrics_pattern = r"Metrics for statementId=\d+: (\{.*?\})"

        try:
            # Extract dictionaries (Metrics for statementId)
            dict_matches = re.findall(metrics_pattern, input_string, re.DOTALL)  # re.DOTALL allows matching across newlines
            for dict_str in dict_matches:
                try:
                    # Convert the matched dictionary string to an actual dictionary
                    av_summary = eval(dict_str)  # Convert string representation of dict to actual dict
                    
                except Exception as e:
                    print(f"Failed to parse dictionary: {e}")
            
            if not av_summary:
                print("No valid AccuVelocity summary found.")
            
            return av_summary

        except Exception as e:
            print("Failed to extract AV Status from input string. Error:", e)
            return av_summary
    
    @staticmethod
    def MSExtractFilePath(input_string: str) -> list:
        """
        Extracts valid .xlsx and .xml file paths from the given string.

        Args:
            input_string (str): The string containing the file paths.

        Returns:
            list: A list of extracted file paths (normalized).
        """
        file_paths = []
        
        # Regex patterns to capture both .xlsx and .xml file paths
        patterns = [
            r"Excel saved successfully:\s+(.*?\.xlsx)",  # For .xlsx files
            r"XML saved successfully:\s+(.*?\.xml)"    # For .xml files
        ]
        
        try:
            for pattern in patterns:
                matches = re.findall(pattern, input_string)
                for match in matches:
                    # Normalize the path and replace double backslashes
                    cleaned_path = os.path.normpath(match.replace('\\\\', '\\'))
                    file_paths.append(cleaned_path)
            
            if not file_paths:
                print("No valid file paths found.")
            
            return file_paths

        except Exception as e:
            print("Failed to extract file paths from output. Error:", e)
            return []
    

    @staticmethod
    def MSRunCMD(python_path, script_path, company, bank, file_path, working_directory, debug=False):
        """
        Executes a Python script in a specified virtual environment with a given working directory.

        Args:
            python_path (str): Path to the Python executable in the virtual environment.
            script_path (str): Path to the script to be executed.
            company (str): Name of the company.
            bank (str): Name of the bank.
            file_path (str): Path to the input file.
            working_directory (str): Directory to set as the current working directory for the script.
            debug (bool): Whether to enable debug mode.
        """
        # Construct the command
        command = [
            python_path,
            script_path,
            "--company", company,
            "--bank", bank,
            "--file", file_path
        ]
        # command = ['D:\\Customer\\REAL\\AccuVelocity\\prodBankStatementPrediction\\envProdBankStatPred\\Scripts\\python.exe', 'D:\\Customer\\REAL\\AccuVelocity\prodBankStatementPrediction\\src\\main.py', '--company', 'test_gwalia', '--bank', 'BOB', '--file', 'H:\\AI Data\\DailyData\\airen\\2025_03_08\\demo_dup4.xlsx']
        # if debug:
        #     command.append("--debug")

        try:
            # Run the command with the specified working directory
            result = subprocess.run(
                command,
                capture_output=True,
                text=True,
                check=True,
                cwd=working_directory  # Set the working directory
            )
            print("Command executed successfully!")
            print("Output:")
            print(result.stdout)
            dictResult = CGwalia.MSGetBankStatementProcessingInfo(str(result))
            # lsFilePaths = dictResult["FilePaths"]
            # dictAVSummary = dictResult["AVSummary"]  
            # Fix the path formatting
            # strResult = strFilePath.replace('\\\\', '\\')  # Replace double backslashes with single backslashes

            # Alternative: Using os.path.normpath()
            # strResult = os.path.normpath(strResult)
            return dictResult
        except subprocess.CalledProcessError as e:
            print("Error during command execution:")
            print(e.stderr)



if __name__ == "__main__":
    # Example usage
    python_executable_path = r"C:\Users\<USER>\Desktop\customer\REAL\AccuVelocity\envProdBankStatPred\Scripts\python.exe"  # Replace with your Python path
    script_to_run = r"scripts\app.py"  # Path relative to the working directory
    company_name = "airen_construction"
    bank_name = "boi_1"
    input_file = r"data/airen_construction/boi_1/input_files/AIREN CONSTRUCTION PVT. LTD. 01-04-2024 TO 30-06-2024.xls"
    working_dir = r"C:\Users\<USER>\Desktop\customer\REAL\AccuVelocity\BankStatementSrc"  # Set your required working directory

    # Run the command
    CGwalia.MSRunCMD(
        python_path=python_executable_path,
        script_path=script_to_run,
        company=company_name,
        bank=bank_name,
        file_path=input_file,
        working_directory=working_dir,
        debug=True
    )

