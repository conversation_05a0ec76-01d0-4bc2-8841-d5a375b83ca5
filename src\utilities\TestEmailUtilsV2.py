import sys
sys.path.append(".")

import imaplib
import email
import os
import json
from dotenv import load_dotenv
import asyncio
from datetime import datetime, timedelta
from config.db_config import AsyncSessionLocal
from src.Models.models import EmailProcessingRecord, EmailProcessingStatusEnum, Logs_EmailProcessing
from fastapi import HTTPException
import magic  # Import python-magic
from src.Controllers.GPTResponse_controller import DocumentUploader
import re
from sqlalchemy.future import select

load_dotenv()

class AsyncDocument:
    def __init__(self, filename, content_type, data):
        self.filename = filename
        self.content_type = content_type
        self._data = data

    async def read(self):
        return self._data

    @property
    def size(self):
        return len(self._data)
    
    def get_extension(self):
        return os.path.splitext(self.filename)[1].lower()


class EmailAttachmentDownloader:
    def __init__(self, email_account, metadata, clients, days_limit=7):
        # Email account information
        self.email_account = email_account
        self.provider = metadata['provider']
        self.password = metadata['password']
        
        # IMAP Server setup
        self.imap_server = 'imappro.zoho.in' if self.provider == 'zoho' else 'imap.gmail.com'
        self.imap_port = 993

        self.clients = clients
        self.mail = None

        # Specify the limit of days to go back
        self.days_limit = days_limit

    @staticmethod
    async def MSLog(iUserID, strLogType, strLogMessage):
        """
        Asynchronous logging method to log messages to the database.
        """
        try:
            async with AsyncSessionLocal() as db:
                objLog = Logs_EmailProcessing(
                    UserId=iUserID,
                    LogType=strLogType,
                    LogMessage=strLogMessage
                )

                db.add(objLog)
                await db.commit()
                await db.refresh(objLog)
                return objLog
        
        except Exception as e:
            raise HTTPException(
                status_code=500, detail="There was some error adding the logs for email polling.")

    async def connect_to_mailbox(self):
        """
        Asynchronously connect to the mailbox.
        """
        try:
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.mail.login(self.email_account, self.password)
            self.mail.select('inbox')
            await self.MSLog(None, 'INFO', f"Connected to mailbox for {self.email_account}.")
        except Exception as e:
            await self.MSLog(None, 'ERROR', f"Failed to connect to mailbox: {e}")
            self.mail = None

    async def fetch_relevant_emails(self):
        """
        Fetch recent emails and filter based on senderList and attachment types for each client.
        Only fetch emails within the `days_limit` from today.
        """
        await self.connect_to_mailbox()
        if not self.mail:
            await self.MSLog(None, 'ERROR', "Not connected to mailbox.")
            return []

        try:
            # Calculate the date limit to fetch emails from
            date_limit = (datetime.now() - timedelta(days=self.days_limit)).strftime('%d-%b-%Y')
            # IMAP search query to get all emails from the calculated date limit
            status, messages = self.mail.search(None, f'(SINCE {date_limit})')
            email_ids = messages[0].split()
            relevant_emails = []

            async with AsyncSessionLocal() as db:
                for email_id in email_ids:
                    # Fetch the email's unique identifier (UID)
                    status, data = self.mail.fetch(email_id, '(UID RFC822)')
                    if status != 'OK':
                        await self.MSLog(None, 'ERROR', f"Failed to fetch email ID {email_id.decode()}")
                        continue

                    # Extract the UID correctly
                    uid_line = data[0][0].decode()  # Extract the string from the first part of the tuple
                    match = re.search(r'UID (\d+)', uid_line)
                    uid = match.group(1) if match else None

                    if not uid:
                        await self.MSLog(None, 'ERROR', f"Failed to extract UID from email ID {email_id.decode()}")
                        continue

                    raw_email = data[0][1]
                    msg = email.message_from_bytes(raw_email)
                    sender = msg.get('From')

                    # Check if the email is from a relevant sender for any client
                    for client_name, client_config in self.clients.items():
                        if any(s.lower() in sender.lower() for s in client_config['senderList']):
                            # Check the status of this email in the database
                            result = await db.execute(select(EmailProcessingRecord).where(EmailProcessingRecord.email_id == uid))
                            record = result.scalars().first()

                            if not record or record.status in [
                                EmailProcessingStatusEnum.IN_PROGRESS, 
                                EmailProcessingStatusEnum.NOT_PROCESSED, 
                                EmailProcessingStatusEnum.PARTIALLY_PROCESSED, 
                                EmailProcessingStatusEnum.FAILED
                            ]:
                                relevant_emails.append((email_id, client_config, uid, sender))

            return relevant_emails

        except Exception as e:
            await self.MSLog(None, 'ERROR', f"Error fetching relevant emails: {e}")
            return []

    async def process_email(self, email_id, client_config, uid, sender):
        """
        Asynchronously downloads and uploads the email attachments for a specific client configuration.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Fetch the email
                status, data = self.mail.fetch(email_id, '(RFC822)')
                msg = email.message_from_bytes(data[0][1])

                result = await db.execute(select(EmailProcessingRecord).where(EmailProcessingRecord.email_id == uid))
                record = result.scalars().first()

                # Extract the actual received date from the email headers
                raw_received_date = msg['Date']
                if raw_received_date:
                    received_date = email.utils.parsedate_to_datetime(raw_received_date)
                else:
                    received_date = datetime.now()  # Fallback to current date if not found

                # Create or update processing record
                if not record:
                    record = EmailProcessingRecord(
                        email_id=uid,
                        user_id=client_config['userId'],
                        sender=sender,
                        received_date=received_date,
                        status=EmailProcessingStatusEnum.IN_PROGRESS
                    )
                    db.add(record)
                    await db.commit()

                processed_attachments = record.processed_attachments or []

                mime = magic.Magic(mime=True)

                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()

                        # Check if the attachment has already been processed
                        if filename and filename not in [att['filename'] for att in processed_attachments]:
                            # Validate the attachment type
                            if any(filename.lower().endswith(ext) for ext in client_config['attachmentsTypes']):
                                file_data = part.get_payload(decode=True)
                                if file_data:
                                    try:
                                        # Save the attachment to the client-specific directory
                                        client_data_dir = client_config['dataDirectory']
                                        # Get today's date in the desired format
                                        today = datetime.now().strftime("%Y_%m_%d")

                                        # Create the download directory with today's date
                                        today_download_dir = os.path.join(client_data_dir, today)
                                        
                                        os.makedirs(today_download_dir, exist_ok=True)
                                        
                                        file_path = os.path.join(today_download_dir, filename)

                                        with open(file_path, 'wb') as f:
                                            f.write(file_data)

                                        # **Upload Operation:**
                                        # Use python-magic to detect the actual MIME type
                                        actual_mime_type = mime.from_buffer(file_data)
                                        if actual_mime_type:
                                            content_type = actual_mime_type
                                        else:
                                            content_type = part.get_content_type()

                                        # Create an AsyncDocument instance with accurate MIME type
                                        document = AsyncDocument(
                                            filename=filename,
                                            content_type=content_type,
                                            data=file_data
                                        )

                                        # Instantiate DocumentUploader
                                        objDocUploader = DocumentUploader()

                                        # 1 Upload the document
                                        dictUploadedDocData = await objDocUploader.upload_document(
                                            user_id=client_config['userId'], 
                                            document=document,  # Pass the AsyncDocument instance
                                            strFamilyName=client_config['modelFamilyName'], 
                                            model_name=client_config['modelName'], 
                                            bUsePaidModel=True  # Adjust this as needed
                                        )
                                        
                                        iDocID = dictUploadedDocData.get("DocId", None)

                                        processed_attachments.append({
                                                        "filename": filename,
                                                        "doc_id": iDocID
                                                    })
                                        
                                        # Assign a new list to ensure changes are detected
                                        record.processed_attachments = list(processed_attachments)
                                        db.add(record)  # Explicitly mark the record for updating
                                        await db.commit()

                                        await self.MSLog(client_config['userId'], 'INFO', f"Processed and uploaded attachment: {filename} from {sender}")

                                    except Exception as e:
                                        await self.MSLog(client_config['userId'], 'ERROR', f"Failed to process attachment: {filename}, {e}")
                                        record.status = EmailProcessingStatusEnum.PARTIALLY_PROCESSED
                                        await db.commit()
                                        break

                # **Update logic for marking as processed:**
                # Check if all intended files (based on client attachment types) were successfully processed.
                processed_file_count = len([att for att in processed_attachments if any(att['filename'].lower().endswith(ext) for ext in client_config['attachmentsTypes'])])
                intended_file_count = len([part for part in msg.walk() if part.get_content_disposition() == 'attachment' and any(part.get_filename().lower().endswith(ext) for ext in client_config['attachmentsTypes'])])

                if processed_file_count == intended_file_count:
                    record.status = EmailProcessingStatusEnum.PROCESSED
                else:
                    record.status = EmailProcessingStatusEnum.PARTIALLY_PROCESSED
                await db.commit()

            except Exception as e:
                await self.MSLog(client_config['userId'], 'ERROR', f"Failed to process email: {e}")


    async def run(self):
        """
        Main polling function to continuously check and process unprocessed emails.
        """
        while True:
            relevant_emails = await self.fetch_relevant_emails()
            for email_id, client_config, uid, sender in relevant_emails:
                await self.process_email(email_id, client_config, uid, sender)

            await self.MSLog(None, 'INFO', "Waiting for next polling cycle...")
            # ! Uncomment this code to make sure it sleeps for 1 minute before polling
            # await asyncio.sleep(60)  # Poll every 60 seconds

    def close_connection(self):
        if self.mail:
            self.mail.close()
            self.mail.logout()


async def main():
    # Load Tally User Config
    try:
        with open(r'resource\TallyUserConfigV2.json', 'r') as file:
            tally_config = json.load(file)
    except Exception as e:
        print(f"Failed to load Tally User Config: {e}")
        return

    # Create a downloader for each email account in the config
    tasks = []
    for email_account, config in tally_config.items():
        metadata = config["metadata"]
        clients = config["clients"]
        downloader = EmailAttachmentDownloader(
            email_account=email_account,
            metadata=metadata,
            clients=clients,
            days_limit=1
        )
        tasks.append(downloader.run())

    # Run all downloaders asynchronously
    await asyncio.gather(*tasks)

if __name__ == '__main__':
    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        print("Program interrupted.")
