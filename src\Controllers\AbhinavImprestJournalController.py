import sys
sys.path.append("")
import traceback
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from src.Schemas.TallyJournalVoucherXMLSchema import CTallyJournalVoucherTemplate, TallyJournalVoucherInputSchema, CompanyInfoSchema, LedgerEntrySchema
from src.Schemas.Tally_XML_Schema import InventoryEntrySchema
from src.Controllers.Logs_Controller import CLogController
from src.utilities.helperFunc import CFileHandler
import pandas as pd
import asyncio
from pathlib import Path
from typing import Optional, Dict, List
from src.utilities.helperFunc import CExcelHelper
from src.utilities.helperFunc import CJSONFileReader, CExcelHelper, CDirectoryHelper, CRemarkTranslator
from datetime import datetime, timedelta
import os
from src.Controllers.ImprestJournalDetailsController import CImprestJournalDetails
import pickle
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.Controllers.GoogleDriveController import GoogleDriveService
from src.utilities.PathHandler import dictProjectPaths
from enum import Enum


GRN_FILE_PATH = dictProjectPaths.get("GRN_FILE_PATH",r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/grn_register")
TALLY_MAP_PATH = dictProjectPaths.get("TALLY_MAP_PATH", r"Data/Customer/Abhinav InfraBuild/Mappings/Tally_ERP_MappingsV5.xlsx")
STORAGE_PATH_PRODUCTION = dictProjectPaths.get("STORAGE_PATH_PRODUCTION",r"H:/AI Data/DailyData/AbhinavInfrabuild")
STORAGE_PATH_DEVELOPMENT = dictProjectPaths.get("STORAGE_PATH_DEVELOPMENT",r"H:/AI Data/DailyData/AV Dev/AbhinavInfrabuild")
BUFFER_BEFORE_DATE = 7
BUFFER_AFTER_DATE = 15


class VersionType(str, Enum):
    V1 = "V1"
    V2 = "V2"

class CImprestJournal:

    _mStrLedgerName = ""
    _mDictCompanyData = {
                            "company_name": "",
                            "gst_registration_type": "",
                            "state_name": "",
                            "country_name": "",
                            "gst_in":"",
                        }
    _mstrVoucherTypeName = ""
    _mstrVocuherEntryMode = ""
    _mstrRoundOffLedger = ""
    _mstrDebitLedgerName = "" 
    _mboolTDSApplied = False
    _mstrCategory = None
    _mstrCostCenterLocation = None
    _mIUserId = None
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    _mDfData = None
    _mDfItemMatching = None
    _mDfImprestHolder = None
    _mDfHead = None
    _mDfLatestGRN = None
    _mfTotal = 0
    _mDfErpItemMatch = None
    _mRoundeddate = None
    _msTallyStatus = "Skipped"
    _mStrGRNFileName = ""
    _mlsGrnItemNound = []
    

    @classmethod
    def MCResetAttributes(cls):
        cls._mStrLedgerName = ""
        cls._mDictCompanyData = {
            "company_name": "",
            "gst_registration_type": "",
            "state_name": "",
            "country_name": "",
            "gst_in": "",
        }
        cls._mstrVoucherTypeName = ""
        cls._mstrVocuherEntryMode = ""
        cls._mstrRoundOffLedger = ""
        cls._mstrDebitLedgerName = ""
        cls._mboolTDSApplied = False
        cls._mstrCategory = None
        cls._mstrCostCenterLocation = None
        cls._mIUserId = 4
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""
        cls._mfTotal = 0
        cls._mRoundeddate = None
        cls._msTallyStatus = "Skipped"
        cls._mStrGRNFileName = ""
        cls._mlsGrnItemNound = []

    @classmethod
    async def MCSetAttributes(cls, iUserId: int,boolTDSApplied = False, strCategory = None, strCostCenterLocation = None, GroupedDf:pd.DataFrame = pd.DataFrame()):

        """
            Load and initialize class attributes from a configuration JSON file.

            This method reads a JSON file specified by the path `strConfigFilePath`, extracts various settings,
            and assigns them to class-level attributes. It also logs the process and handles errors related to
            file reading, JSON parsing, and missing keys.

            Parameters:
                strConfigFilePath (str): Path to the configuration JSON file.
                iUserId (int): The user ID for logging and tracking purposes.
                boolTDSApplied (bool, optional): Indicates whether TDS is applied. Defaults to False.
                strCategory (str, optional): Optional category tag to be assigned. Defaults to None.
                strCostCenterLocation (str, optional): Optional cost center location. Defaults to None.
            
            Returns: None
           
        """

        try:
            CImprestJournal.GRNFileVersion = VersionType.V1
            cls.MCResetAttributes()
            CImprestJournal._mIUserId = iUserId
            await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Info","Loading Config file and Initializing attributes")
            # Update the company data dictionary
            cls._mDictCompanyData["company_name"] = "Abhinav Infrabuild Pvt.Ltd.(24-26)"
            cls._mDictCompanyData["gst_registration_type"] = "Regular"
            cls._mDictCompanyData["state_name"] = "Madhya Pradesh"
            cls._mDictCompanyData["country_name"] = "India"
            cls._mDictCompanyData["gst_in"] = "23AAHCA9425D1ZY"
            cls._mfTotal = GroupedDf["Amount Rs."].sum()
            # Set other string attributes
            cls._mstrVoucherTypeName = "AV Journal"
            cls._mstrVocuherEntryMode = "As Voucher"
            cls._mstrRoundOffLedger = ""
            cls._mboolTDSApplied = boolTDSApplied
            cls._mstrCategory = strCategory
            cls._mstrCostCenterLocation = strCostCenterLocation
            cls._mstrCategory = "Primary Cost Category"
        except KeyError as e:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            raise Exception(f"Missing required field in JSON: {str(e)}")
        except Exception as e:
             await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Error",f"{str(traceback.format_exc())}")
             raise

    @staticmethod
    async def MGetNarration(df: pd.DataFrame) -> str:
        """
            Generates a narration string based on unique values in the DataFrame and the current date.

            Parameters
            ----------
            df : pandas.DataFrame
                The DataFrame containing the columns 'Emprest Holder', 'Site Name', and 'JV Sheet no.'.

            Returns
            -------
            str
                A narration string in the format:
                'Being Exp Incurred By <Emprest Holder> For Site <Site Name> JV Sheet No <JV Sheet no.> Entry Date: <current datetime>'.

        """
        try:
            return f"Being Exp Incurred By {df['Emprest Holder'].unique()[0]}, For {'' if pd.isna(df['Site Name'].unique()[0]) else df['Site Name'].unique()[0]} Site, JV Sheet No {df['JV Sheet no.'].unique()[0]}, Entry Date: {datetime.now().strftime('%Y-%m-%d')}."
        except Exception as objException:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", str(traceback.format_exc()))
            raise 

    @staticmethod
    async def MSGenerateCreditLedger(df:pd.DataFrame) -> list:
        """
            Generates credit ledger entries based on the extracted data.

            If TDS is not applicable, a single credit ledger entry is created for the full amount.
            If TDS is applicable, the total is split into a main credit entry and a separate TDS entry.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                list: A list of one or more ledger entry dictionaries.
        """
       
        """
        Generates credit ledger entries.
        If TDS is applicable (via an external function), add a TDS entry.
        """
        try:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Info","Getting Credit Ledger")
            fTotalAmount = 0
            fTotalAmount = CImprestJournal._mfTotal
            cost_centre = [{"name": '' if pd.isna(df['Site Name'].unique()[0]) else df['Site Name'].unique()[0], "amount": fTotalAmount}]
            
            main_credit = {
                "ledger_name": CImprestJournal._mStrLedgerName,  
                "amount": fTotalAmount,
                "is_deemed_positive": False,  # Credit
                "is_party_ledger": True,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": CImprestJournal._mstrCategory,
                "cost_center_allocations": cost_centre,
            }
            return main_credit
        except Exception as objException:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", str(traceback.format_exc()))
            raise
    
    @staticmethod
    async def MGenerateDebitLedger(GroupedDF = None):
        """
            Generates a debit ledger entry based on the extracted data and cost center settings.

            If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """

        try:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Info","Getting Debit Ledger")
            debit_ledger = []
            fTotalAmount = 0
            current_grn_date = None
            max_grn_date = None
            result = None
            transalator = CRemarkTranslator()
            for row_idx, row in GroupedDF.iterrows():
                dictItemDetail = await CImprestJournal.MSGetItemFromGRN(row)
                
                
                # Track maximum GRN Date
                current_grn_date = pd.to_datetime(dictItemDetail["GRN Date"])
                if max_grn_date is None or current_grn_date > max_grn_date:
                    max_grn_date = current_grn_date
                cost_centre = [{"name": row['Site Name'], "amount": -dictItemDetail['Amount']}]
                fTotalAmount = dictItemDetail['Amount']
                result = CImprestJournal._mDfHead.loc[CImprestJournal._mDfHead['ACCOUNT HEAD'].str.lower().str.strip() == row['Account Head'].lower().strip()]
                if result.empty:
                    raise ValueError(f"ValidationError Tally XML: Account Head: '{row['Account Head']}' not Found in the input file's Head Sheet")
                if result.values[0][1].lower().strip() == 'n':
                    ledger = {
                    "ledger_name":row['Account Head'],
                    "amount":-fTotalAmount,
                    "is_deemed_positive": True,  # Debit entry
                    "is_party_ledger": False,
                    "gst_taxability": "Taxable",
                    "gst_type_of_supply": "Services",
                    "cost_center_category": CImprestJournal._mstrCategory,
                    "cost_center_allocations": cost_centre,
                    "str_narration" : await transalator.MTranslateAndRephrase(row['Remarks'])
                }
                else:
                    if result.values[0][2] and pd.notna(result.values[0][2]):
                        dictItemDetail['Item'] = result.values[0][2]
                    inventory = InventoryEntrySchema(
                        stockitemname = dictItemDetail['Item'],
                        rate = str(round(dictItemDetail["Amount"]/dictItemDetail['Stock Qty'])),
                        amount = -dictItemDetail['Amount'],
                        actual_qty = str(dictItemDetail['Stock Qty']) + " "+ dictItemDetail['Unit'],
                        billed_qty = str(dictItemDetail['Stock Qty'])+ " "+ dictItemDetail['Unit'],    
                    )
                    strGRNNo = str(dictItemDetail.get("GRN No",""))
                    ledger = {
                        "ledger_name":row['Account Head'],
                        "amount":-fTotalAmount,
                        "is_deemed_positive": True,  # Debit entry
                        "is_party_ledger": False,
                        "gst_taxability": "Taxable",
                        "gst_type_of_supply": "Services",
                        "cost_center_category": CImprestJournal._mstrCategory,
                        "cost_center_allocations": cost_centre,
                        "str_narration" : await transalator.MTranslateAndRephrase(row['Remarks']),
                        "inventory_allocation" : inventory.dict(),
                        "batch_allocation": {
                        "godownname":"Main Location",
                            "batchname":"Primary Batch",
                            "trackingnumber":strGRNNo,
                            "amount":-fTotalAmount,
                            "actual_qty":str(dictItemDetail['Stock Qty']) + " "+ dictItemDetail['Unit'],
                            "billed_qty":str(dictItemDetail['Stock Qty'])+ " "+ dictItemDetail['Unit']
                        }
                    }
                debit_ledger.append(ledger)
            CImprestJournal._mRoundeddate = CBusinessIntelligence.MSRoundDateToNext5Days(max_grn_date.strftime("%Y%m%d"))
            return debit_ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId,"Error",str(traceback.format_exc()))
            raise objException

    @staticmethod
    async def MSValidteJVSheetNo(data_df: pd.DataFrame) -> None:
        """
        Validate that all unique values in 'JV Sheet no.' column are between 1 and 100.

        Args:
            data_df (pd.DataFrame): DataFrame containing the 'JV Sheet no.' column.

        Raises:
            ValueError: If 'JV Sheet no.' column is missing or values are out of range.
        """
        try:
            strValidationError=""
            if "JV Sheet no." not in data_df.columns:
                raise ValueError("ValidationError Tally XML: Missing required column: JV Sheet no. in input file.")

            unique_jv_sheets = data_df["JV Sheet no."].unique()
            
            if not data_df["JV Sheet no."].dtype in ['int64', 'int32', 'uint64', 'uint32']:
                raise ValueError("Column 'JV Sheet no.' must be of integer type")
            invalid_jv_sheets = [jv for jv in unique_jv_sheets if not 1 <= jv <= 100]
            
            if invalid_jv_sheets:
                strValidationError =  f"ValidationError Tally XML: Invalid values in 'JV Sheet no.' : {', '.join(map(str, invalid_jv_sheets))}. " \
                    "All values must be between 1 and 100."
                raise ValueError(
                    f"ValidationError Tally XML: Invalid values in 'JV Sheet no.' : {', '.join(map(str, invalid_jv_sheets))}. "
                    "All values must be between 1 and 100."
                )
        except Exception as e: 
            err_msg = str(e)
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{err_msg}")
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            strValidationError
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML:The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            CImprestJournal._mStrTracebackLogs = "MSValidteJVSheetNo:\n" + str(traceback.format_exc())
            raise e
            
    @staticmethod
    async def MSGetItemFromGRN(row: pd.Series) -> Dict:
        """
        Retrieve a row from _mDfLatestGRN based on filters and return as a dictionary with updated Amount.

        Args:
            row (pd.Series): A row from the DataFrame containing 'Site Name', 'Date of Exp.', and 'Amount Rs.'.

        Returns:
            Dict: Dictionary containing the matched row or default values with updated 'Amount'.

        Raises:
            ValueError: If required columns are missing in row or _mDfLatestGRN, or if _mDfLatestGRN is None.
        """
        try:
            strValidationError = ""
            # Validate input row columns
            required_row_cols = ["Site Name", "Date of Exp.", "Amount Rs."]
            missing_row_cols = [col for col in required_row_cols if col not in row.index]
            if missing_row_cols:
                strValidationError = f"ValidationError Tally XML: Missing required columns in input file row: {', '.join(missing_row_cols)}"
                raise ValueError(f"ValidationError Tally XML: Missing required columns in input file row: {', '.join(missing_row_cols)}")

            # Validate _mDfLatestGRN
            if CImprestJournal._mDfLatestGRN is None:
                strValidationError = "LatestGRN Not Found or initialized"
                raise ValueError("LatestGRN Not Found or initialized")

            grn_df = CImprestJournal._mDfLatestGRN
            required_grn_cols = ["GRN No", "GRN Date", "Amount", "Item", "Stock Qty", "Unit"]
            missing_grn_cols = [col for col in required_grn_cols if col not in grn_df.columns]
            if missing_grn_cols:
                strValidationError = f"ValidationError Tally XML: Missing required columns in GRN File Name {CImprestJournal._mStrGRNFileName}: {', '.join(missing_grn_cols)}"
                raise ValueError(f"ValidationError Tally XML: Missing required columns in GRN File Name {CImprestJournal._mStrGRNFileName}: {', '.join(missing_grn_cols)}")

            # Convert Date of Exp. to datetime
            try:
                exp_date = pd.to_datetime(row["Date of Exp."])
            except ValueError:
                strValidationError = f"ValidationError Tally XML: Invalid 'Date of Exp.' format: {row['Date of Exp.'].unique()} for JV Sheet No. {row['JV Sheet no.'].unique()}"
                raise ValueError(f"ValidationError Tally XML: Invalid 'Date of Exp.' format: {row['Date of Exp.'].unique()} for JV Sheet No. {row['JV Sheet no.'].unique()}")

            # Define date range: 1 day before and 2 weeks after
            # Updating to 7 days before
            start_date = (exp_date - timedelta(days=BUFFER_BEFORE_DATE)).date()
            end_date = (exp_date + timedelta(days=BUFFER_AFTER_DATE)).date()

            # Filter 1: GRN No containing Site Name
            site_name = str(row["Site Name"])
            grn_filtered = grn_df[grn_df["GRN No"].str.contains(site_name, case=False, na=False)]

            # Convertin GRN Date to Date Time and Filter 2: GRN Date within range
            grn_filtered["GRN Date"] = pd.to_datetime(grn_filtered["GRN Date"]).dt.date; grn_filtered = grn_filtered[(grn_filtered["GRN Date"] >= start_date) & (grn_filtered["GRN Date"] <= end_date)]

            # Filter 3: Exact Amount match
            amount = float(row["Amount Rs."])
            exact_match = grn_filtered[grn_filtered["Amount"] == amount]

            if not exact_match.empty:
                # Return first matching row with updated Amount
                matched_row = exact_match.iloc[0].to_dict()
                matched_index = exact_match.index[0]
                matched_row["Amount"] = min(amount, matched_row["Amount"])
                # Map Item and Unit using _mDfErpItemMatch
                item_name = str(matched_row["Item"])
                item_match = CImprestJournal._mDfErpItemMatch[CImprestJournal._mDfErpItemMatch["ERP Item Name "] == item_name]
                if not item_match.empty:
                    matched_row["Item"] = item_match.iloc[0]["Tally Items"]
                    # Update GRN Date to the maximum of Date of Exp. and GRN Date
                    grn_date = pd.to_datetime(matched_row["GRN Date"])
                    matched_row["GRN Date"] = max(exp_date, grn_date)
                    if not pd.isna(item_match.iloc[0]["Tally Unit"]):
                        matched_row["Unit"] = item_match.iloc[0]["Tally Unit"]
                    grn_df.drop(index=matched_index, inplace=True)
                return matched_row

            # Fallback: Amount within ±2
            amount_range = grn_filtered[
                (grn_filtered["Amount"] >= amount - 2) &
                (grn_filtered["Amount"] <= amount + 2)
            ]

            if not amount_range.empty:
                # Return first matching row with updated Amount
                matched_row = amount_range.iloc[0].to_dict()
                matched_index = amount_range.index[0]
                matched_row["Amount"] = min(amount, matched_row["Amount"])
                # Map Item and Unit using _mDfErpItemMatch
                item_name = str(matched_row["Item"])
                item_match = CImprestJournal._mDfErpItemMatch[CImprestJournal._mDfErpItemMatch["ERP Item Name "] == item_name]
                if not item_match.empty:
                    # Update GRN Date to the maximum of Date of Exp. and GRN Date
                    grn_date = pd.to_datetime(matched_row["GRN Date"])
                    matched_row["GRN Date"] = max(exp_date, grn_date)
                    matched_row["Item"] = item_match.iloc[0]["Tally Items"]
                    if not pd.isna(item_match.iloc[0]["Tally Unit"]):
                        matched_row["Unit"] = item_match.iloc[0]["Tally Unit"]
                    grn_df.drop(index=matched_index, inplace=True)
                return matched_row

            # Default case: Return dictionary with default values
            default_row = {
                "Item": "Misc Expenses",
                "Stock Qty": 1,
                "Unit": "Nos",
                "Amount": amount,
                'GRN Date': row['Date of Exp.']
            }
            CImprestJournal._mlsGrnItemNound.append(f"Date of Exp: {row['Date of Exp.'].strftime('%Y-%m-%d')} Amount: {row['Amount Rs.']}")
            return default_row

        except Exception as e:
            err_msg = str(e)
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            strValidationError
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML:The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{str(traceback.format_exc())}")
            raise e

    @staticmethod
    async def MSValidationForEachRow(df):
        # Validate consistency of 'Site Name'
        try:
            if df["Site Name"].nunique() > 1:
                strValidationError =  f"ValidationError Tally XML: Multiple 'Site Name' values in group 'JV Sheet no.' {df['JV Sheet no.'].unique()[0]}: Found {df['Site Name'].unique()}"
                raise ValueError(
                    f"ValidationError Tally XML: Multiple 'Site Name' values in group 'JV Sheet no.' {df['JV Sheet no.'].unique()[0]}: "
                    f"Found {df['Site Name'].unique()}"
                )

            # Validate consistency of 'Emprest Holder'
            if df["Emprest Holder"].nunique() > 1:
                strValidationError = f"ValidationError Tally XML: Multiple 'Emprest Holder' values in group 'JV Sheet no.' {df['JV Sheet no.'].unique()[0]}: Found {df['Emprest Holder'].unique()}"
                raise ValueError(
                    f"ValidationError Tally XML: Multiple 'Emprest Holder' values in group 'JV Sheet no.' {df['JV Sheet no.'].unique()[0]}: "
                    f"Found {df['Emprest Holder'].unique()}"
                )
        except Exception as e:
            err_msg = str(e)
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{err_msg}")
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            strValidationError
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML:The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            CImprestJournal._mStrTracebackLogs = "MSValidationForEachRow:\n" + str(traceback.format_exc())
            raise e
        
            
    @staticmethod
    async def MSGroupBy(data_df: pd.DataFrame) -> List[pd.DataFrame]:
        """
        Group DataFrame by 'JV Sheet no.' and validate consistency of 'Site Name' and 'Emprest Holder' within each group.

        Args:
            data_df (pd.DataFrame): DataFrame containing 'JV Sheet no.', 'Site Name', and 'Emprest Holder' columns.

        Returns:
            List[pd.DataFrame]: List of DataFrames for each group.

        Raises:
            ValueError: If required columns are missing or values are Multiple within groups.
        """
        try:
            strValidationError=""
            required_cols = ["JV Sheet no.", "Site Name", "Emprest Holder"]
            missing_cols = [col for col in required_cols if col not in data_df.columns]
            if missing_cols:
                strValidationError = f"ValidationError Tally XML: Missing required columns: {', '.join(missing_cols)} for the input file."
                raise ValueError(f"ValidationError Tally XML: Missing required columns: {', '.join(missing_cols)} for the input file.")
            
            # Remove rows where 'JV Sheet no.' is null or empty
            data_df = data_df[~data_df["JV Sheet no."].isnull() & (data_df["JV Sheet no."].astype(str).str.strip() != "")]
            
            # Converting to object data type to desired int
            data_df['JV Sheet no.'] = pd.to_numeric(data_df['JV Sheet no.'], errors='coerce')
            # Remove NaN values
            data_df = data_df.dropna(subset=['JV Sheet no.'])
            # Convert to int
            data_df['JV Sheet no.'] = data_df['JV Sheet no.'].astype('int64')
            
            
            # Group by 'JV Sheet no. and Site Name'
            grouped = data_df.groupby(["JV Sheet no.", "Site Name"])
            group_dfs = []

            for jv_sheet_no, group_df in grouped:
                group_dfs.append(group_df)
            return group_dfs
        except Exception as e:
            err_msg = str(e)
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            strValidationError
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML:The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            CImprestJournal._mStrTracebackLogs = "MSGroupBy:\n" + str(traceback.format_exc())
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{str(traceback.format_exc())}")
            raise e

    @staticmethod
    async def MSCleanTallySXML(xml_str: str) -> str:
    # Parse the XML string
        try:
            root = ET.fromstring(xml_str)

            # Define tags to remove (with or without namespaces)
            tags_to_remove = [
                "GSTOVRDNTAXABILITY",
                "GSTOVRDNTYPEOFSUPPLY"
            ]

            # Remove all matching tags in the tree
            for tag in tags_to_remove:
                for elem in root.findall(f".//{tag}"):
                    parent = root.find(f".//{tag}/..")
                    if parent is not None:
                        parent.remove(elem)
       
            cleaned_xml = ET.tostring(root, encoding='unicode')
            return cleaned_xml
        except Exception as e:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", str(traceback.format_exc()))
            raise
    
    @staticmethod
    async def MSValidateAccountLedger(strCostCentre:str, strEmprestHolder:str, JVSheetNo: int):
        """
        Validate and retrieve the 'ACCOUNT LEDGER' value from the row matching the given Cost Centre and Emprest Holder.

        Args:
            data_df (pd.DataFrame): DataFrame containing 'Site Name', 'Emprest Holder', and 'ACCOUNT LEDGER' columns.
            strCostCentre (str): Cost Centre value to match against 'Site Name' column.
            strEmprestHolder (str): Emprest Holder value to match against 'Emprest Holder' column.

        Returns:
            str: Value of the 'ACCOUNT LEDGER' column from the matched row.

        Raises:
            ValueError: If required columns are missing, no row or multiple rows match, or 'ACCOUNT LEDGER' is None.
        """
        try:
            strValidationError=""
            # Filter rows matching strCostCentre and strEmprestHolder
            matched_rows = CImprestJournal._mDfImprestHolder[
                (CImprestJournal._mDfImprestHolder["COST CENTER"].str.lower().str.strip() == strCostCentre.lower().strip()) &
                (CImprestJournal._mDfImprestHolder["PERSON NAME"].str.lower().str.strip() == strEmprestHolder.lower().strip())
            ]
             # Validate exactly one row matches
            if len(matched_rows) == 0:
                strValidationError = f"No Matching Account Ledger found for Site Name '{strCostCentre}', Emprest Holder '{strEmprestHolder}' and JV Sheet No. {JVSheetNo} in input file's sheet: IMPREST HOLDER."
                raise ValueError(
                    f"ValidationError Tally XML: No Matching Account Ledger found for Site Name '{strCostCentre}' and Emprest Holder '{strEmprestHolder}' and JV Sheet No. {JVSheetNo} in input file's sheet: IMPREST HOLDER."
                )
            if len(matched_rows) > 1:
                strValidationError = f"Multiple Account Ledger for Site Name '{strCostCentre}' and Emprest Holder '{strEmprestHolder}' and JV Sheet No. {JVSheetNo}in input file's sheet: IMPREST HOLDER."
                raise ValueError(
                    f"ValidationError Tally XML: Multiple Account Ledger for Site Name '{strCostCentre}' and Emprest Holder '{strEmprestHolder}' and JV Sheet No. {JVSheetNo} in input file's sheet: IMPREST HOLDER."
                )
            
             # Get the single matched row
            matched_row = matched_rows.iloc[0]

            # Validate 'ACCOUNT LEDGER' is not None
            if pd.isna(matched_row["ACCOUNT LEDGER"]):
                raise ValueError(
                    f"ValidationError Tally XML: No ACCOUNT LEDGER found for Site Name '{strCostCentre}' Emprest Holder '{strEmprestHolder}' and JV Sheet No. {JVSheetNo} in input file's Sheet: IMPREST HOLDER."
                )

            # Return the 'ACCOUNT LEDGER' value
            return str(matched_row["ACCOUNT LEDGER"])
        
        except Exception as e:
            err_msg = str(e)
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            strValidationError
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML:The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            CImprestJournal._mStrTracebackLogs = "MSValidateAccountLedger:\n" + str(traceback.format_exc())
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{str(traceback.format_exc())}")
            raise e
    
    @staticmethod
    async def MSCreateJournalXML(iUserId, df: pd.DataFrame, lsUdfData: None = None):
        try:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Info", "Started Creating XML")
            await CImprestJournal.MCSetAttributes(iUserId = iUserId, GroupedDf = df)
            strCostCentre = '' if pd.isna(df['Site Name'].unique()[0]) else df['Site Name'].unique()[0]
            strEmprestHolder = df["Emprest Holder"].unique()[0]
            iJVSheetNo = df["JV Sheet no."].unique()[0]
            ledger_entries = []
            
            # Getting ledger name
            CImprestJournal._mStrLedgerName = await CImprestJournal.MSValidateAccountLedger(strCostCentre, strEmprestHolder,df["JV Sheet no."].unique()[0])
            
            # Getting Credit and Debit Ledger
            credit_ledger = await CImprestJournal.MSGenerateCreditLedger(df)
            debit_ledger = await CImprestJournal.MGenerateDebitLedger(df)
            
            # Making list items not found in GRN
            if CImprestJournal._mlsGrnItemNound:
                
                CImprestJournal._msStrAccuVelocityComments += f"\nGRN Not Found For {df['Site Name'].unique()[0]} Site:  "
                # Use a for loop to build the formatted strings with numbering
                for i in range(len(CImprestJournal._mlsGrnItemNound)):
                    item = CImprestJournal._mlsGrnItemNound[i]
                    formatted_item = f"{i+1}. {item}"
                    CImprestJournal._msStrAccuVelocityComments += "\n " + formatted_item
            
            for item in debit_ledger:
                ledger_entries.append(LedgerEntrySchema(**item))
            ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            invoice_date = CImprestJournal._mRoundeddate
            
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CImprestJournal._mDictCompanyData.get("company_name", ""),
                    gst_in="23AAHCA9425D1ZY",
                    state_name="Madhya Pradesh",
                    country_name="India",
                ),
                narration=await CImprestJournal.MGetNarration(df),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="AV Journal",
                voucher_date = invoice_date,
                reference=str(iJVSheetNo),
                ledger_entries=ledger_entries,
                udf_data=lsUdfData,
                str_costcentre =  strCostCentre,
                reference_date = invoice_date
            )
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            xml_str = await CImprestJournal.MSCleanTallySXML(xml_str)
            return xml_str
            
        except Exception as e:
            err_msg = str(e)
            CImprestJournal._msTallyStatus = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Skipped"
                        )
            CImprestJournal._mStrTracebackLogs =   (
                            str(traceback.format_exc())        
                        )
            CImprestJournal._msStrAccuVelocityComments =   (
                            err_msg
                            if err_msg.startswith("ValidationError Tally XML:")
                            else "Error – Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
                        )
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{str(traceback.format_exc())}")

            raise e
    
    @staticmethod
    async def MSGetDateRange(df):
        """
        Returns a string representing the range of dates from minimum to maximum 
        in the 'Date of Exp.' column of the provided DataFrame.

        Parameters
        ----------
        df : pandas.DataFrame
            The DataFrame containing a 'Date of Exp.' column with date values.

        Returns
        -------
        str
            A string in the format 'YYYY-MM-DD to YYYY-MM-DD' representing the 
            minimum and maximum dates. Returns an error message if the column 
            is not found or if date processing fails.
            if 'Date of Exp.' not in df.columns:
                return "Column 'Date of Exp.' not found"
        """
        try:
            # Convert to datetime if not already
            dates = pd.to_datetime(df['Date of Exp.'])
            min_date = dates.min().strftime("%d-%m-%Y")
            max_date = dates.max().strftime("%d-%m-%Y")
            return f"{min_date} to {max_date}"
        except Exception as e:
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"{str(e)}")
            raise e
 
    
    @staticmethod
    async def MSCreateXMLS(strExcelPath: str, iUserId: int, bDeveloperMode: bool = False, strClientREQID: str = None, strReqGenereated: str = None, bDownloadERPFile = False):

        try:
            CImprestJournal.GRNFileVersion = VersionType.V1

            xml_str = ""
            CImprestJournal.MCResetAttributes()
            await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Info", "Started Creating XML")
            
            # Validate Excel file path
            excel_path = Path(strExcelPath)
            if not excel_path.exists() or not excel_path.is_file():
                await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"Excel file not found at: {strExcelPath}")
                raise FileNotFoundError(f"Excel file not found at: {strExcelPath}")

            # Read all sheets
            xl = pd.ExcelFile(strExcelPath)
            required_sheets = {"ITEM MATCHING", "IMPREST HOLDER", "DATA", "Head"}
            if not all(sheet in xl.sheet_names for sheet in required_sheets):
                missing_sheets = required_sheets - set(xl.sheet_names)
                await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"ValidationError Tally XML: Missing required sheets in the input file: {', '.join(missing_sheets)}")
                raise ValueError(f"ValidationError Tally XML: Missing required sheets in the input file: {', '.join(missing_sheets)}")

            # Read sheets into separate variables
            CImprestJournal._mDfItemMatching = pd.read_excel(xl, sheet_name="ITEM MATCHING")
            CImprestJournal._mDfImprestHolder = pd.read_excel(xl, sheet_name="IMPREST HOLDER")
            CImprestJournal._mDfData = pd.read_excel(xl, sheet_name="DATA")
            CImprestJournal._mDfHead = pd.read_excel(xl, sheet_name="Head")
            strExcelFilePath = CDirectoryHelper.MSGetLatestFileFromNestedFolder(GRN_FILE_PATH)
            if CImprestJournal.GRNFileVersion == VersionType.V1:  
                CImprestJournal._mDfLatestGRN = CExcelHelper.read_file_temp(strExcelFilePath)
                new_columns = {
                0: "GRN No",
                1: "GRN Date",
                2: "Party Bill No",
                3: "Party Bill Date",
                4: "Veh No",
                5: "Ch No",
                6: "PO No",
                7: "Indent No",
                8: "Party",
                9: "Remarks",
                10: "Item",
                11: "Stock Qty",
                12: "Unit",
                13: "Weight",
                14: "Rate",
                15: "Amount",
                16: "RST NO"
                }
                CImprestJournal._mDfLatestGRN = CImprestJournal._mDfLatestGRN.rename(columns=new_columns)
            else:
                # Method To handle new GRN Format
                CImprestJournal._mDfLatestGRN = CExcelHelper.read_GRN_Updated_Format(strExcelFilePath)
            CImprestJournal._mStrGRNFileName = strExcelFilePath.split('\\')[-1]
            
            # CImprestJournal._mDfErpItemMatch = pd.read_excel(TALLY_MAP_PATH)
            try:
                drive_service = GoogleDriveService()
                CImprestJournal._mDfErpItemMatch = drive_service.handle_file(download=bDownloadERPFile)
            except Exception as e:
                await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"There was a problem in getting Item ERP Matching file")
                raise Exception("There was a problem in getting Item ERP Matching file")

            
            # Define required columns for each sheet
            sheet_columns = {
                "DATA": ["Timestamp", "Email address", "Site Name", "Emprest Holder","Date of Exp.","Account Head","Amount Rs.","Payment Mode","Remarks","JV Sheet no."],
                "IMPREST HOLDER": ["S NO", "PERSON NAME", "COST CENTER", "ACCOUNT LEDGER"],
                "ITEM MATCHING": ["S NO", "Stock Item", "Account Head"],
                "Head": ["ACCOUNT HEAD", "Head with Items", "AGAINST ITEM"]
            }

            # Validate columns for each sheet
            for sheet_name, required_cols in sheet_columns.items():
                df = {
                    "ITEM MATCHING": CImprestJournal._mDfItemMatching,
                    "IMPREST HOLDER": CImprestJournal._mDfImprestHolder,
                    "DATA": CImprestJournal._mDfData,
                    "Head": CImprestJournal._mDfHead
                }[sheet_name]
                
                missing_cols = [col for col in required_cols if col not in df.columns]
                if missing_cols:
                    await CLogController.MSWriteLog(CImprestJournal._mIUserId, "Error", f"ValidationError Tally XML: Missing required columns in input file sheet {sheet_name}: {', '.join(missing_cols)}")
                    raise ValueError(f"ValidationError Tally XML: Missing required columns in input file sheet {sheet_name}: {', '.join(missing_cols)}")
            
            # Validating Column Type in DATA sheet
            
            # Check and convert 'Date of Exp.' column
            if CImprestJournal._mDfData['Date of Exp.'].dtype == 'object':  # Checks if column is string/object
                CImprestJournal._mDfData['Date of Exp.'] = pd.to_datetime(
                    CImprestJournal._mDfData['Date of Exp.'], 
                    format='%d-%m-%Y', 
                    errors='coerce'
                )

            # Check and convert 'Timestamp' column
            if CImprestJournal._mDfData['Timestamp'].dtype == 'object':  # Checks if column is string/object
                CImprestJournal._mDfData['Timestamp'] = pd.to_datetime(
                    CImprestJournal._mDfData['Timestamp'], 
                    errors='coerce'
                )
                        

            # Group and validate consistency of 'Site Name' and 'Emprest Holder'
            grouped_dfs = await CImprestJournal.MSGroupBy(CImprestJournal._mDfData)
            iJID = 0
            iJournalID = 0
            iJournalID = await CImprestJournalDetails.MSGetNextJournalID(CImprestJournal._mIUserId)
            saved_xml_files_path = []
            lsReportDict = [] # To Create CSV Report
            for df in grouped_dfs:
                site_name = ""
                try:
                    # Validate 'JV Sheet no.' column
                    await CImprestJournal.MSValidteJVSheetNo(df)
                    site_name = '' if pd.isna(df['Site Name'].unique()[0]) else df['Site Name'].unique()[0]

                    await CImprestJournal.MSValidationForEachRow(df)
                    bIsDuplicate =await CImprestJournalDetails.MSIsDataFrameInJournal(df,CImprestJournal._mIUserId)
                    if bIsDuplicate:
                        CImprestJournal._msStrAccuVelocityComments = "A duplicate entry was detected in Tally."
                        kwargs = {
                            "ImprestFileID":iJournalID, 
                            "strClientREQID":strClientREQID,
                            "JVSheetNo": df['JV Sheet no.'].unique()[0],
                            "EmprestHolder":df['Emprest Holder'].unique()[0],
                            "EmailAddress":df['Email address'].unique()[0],
                            "SiteName":site_name,
                            "TotalAmount":df['Amount Rs.'].sum(),
                            "AVXMLGeneratedStatus":'Duplicate',
                            "AVComments":CImprestJournal._msStrAccuVelocityComments,
                            "RecievedDate": datetime.now(),
                            "CReqGeneratedTimeAt":strReqGenereated,
                            "TracebackLogs":"-"
                        }
                        lsReportDict.append(kwargs)
                        await CImprestJournalDetails.MSInsertJournalRecord(kwargs, iUserId)
                        continue

                    xml_str = await CImprestJournal.MSCreateJournalXML(iUserId, df)
                    
                    itotalTransaction = df.shape[0]
                    strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="Unknown", no_of_stock_items=itotalTransaction, strVoucherType="JOURNAL_VOUCHER")
                    dateRange = await CImprestJournal.MSGetDateRange(df)
                    try:
                        kwargs = {
                            "ImprestFileID":iJournalID, 
                            "strClientREQID":strClientREQID,
                            "JournalDataFrame": pickle.dumps(df),
                            "JVSheetNo": df['JV Sheet no.'].unique()[0],
                            "EmprestHolder":df['Emprest Holder'].unique()[0],
                            "EmailAddress":df['Email address'].unique()[0],
                            "SiteName":site_name,
                            "TotalAmount":df['Amount Rs.'].sum(),
                            "TotalEntries":itotalTransaction,
                            "AVXMLGeneratedStatus":"Success",
                            "AVComments":CImprestJournal._msStrAccuVelocityComments,
                            "RecievedDate": datetime.now(),
                            "EstAccountantTimeSaved":strTimeSaved,
                            "CReqGeneratedTimeAt":strReqGenereated,
                            "strXMLResponse":xml_str,
                            "DateOfExpensePeriod":dateRange,
                            "TracebackLogs":"-"
                        }
                        result = await CImprestJournalDetails.MSInsertJournalRecord(kwargs, iUserId)
                        iJID = result['JVID']
                        strXMLFileName = f"Imprest_Journal_ClientRequestID_{strClientREQID}_JournalID_{iJournalID}_JID_{iJID}.xml"
                       
                        # Save XML in date-wise folder
                        if bDeveloperMode:
                            strDownloadDirPath = STORAGE_PATH_PRODUCTION
                        else:
                            strDownloadDirPath = STORAGE_PATH_DEVELOPMENT
                        strDownloadDirPath = os.path.join(strDownloadDirPath, datetime.today().strftime("%Y_%m_%d"), "Imprest Journal")
                        os.makedirs(strDownloadDirPath, exist_ok=True)
                        strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                        with open(strTodaysXmlFilePath, 'w', encoding='utf-8') as file:
                            file.write(xml_str)
                        saved_xml_files_path.append(strTodaysXmlFilePath)
                        await CImprestJournalDetails.MSInsertJournalRecord(kwargs, iUserId)
                        lsReportDict.append(kwargs)
                        
                    except Exception as e:
                        await CLogController.MSWriteLog(iUserId, "Error", f"{str(traceback.format_exc())}")
                except ValueError as e:
                    kwargs = {
                            "ImprestFileID":iJournalID, 
                            "strClientREQID":strClientREQID,
                            "JVSheetNo": df['JV Sheet no.'].unique()[0],
                            "EmprestHolder":df['Emprest Holder'].unique()[0],
							"EmailAddress":df['Email address'].unique()[0],
                            "SiteName":site_name,
                            "TotalAmount":df['Amount Rs.'].sum(),
                            "AVXMLGeneratedStatus":CImprestJournal._msTallyStatus,
                            "AVComments":CImprestJournal._msStrAccuVelocityComments,
                            "RecievedDate": datetime.now(),
                            "CReqGeneratedTimeAt":strReqGenereated,
                            "TracebackLogs":CImprestJournal._mStrTracebackLogs
                        }
                    lsReportDict.append(kwargs)
                    await CImprestJournalDetails.MSInsertJournalRecord(kwargs, iUserId)
                    continue
                except Exception as e:
                    kwargs = {
                            "ImprestFileID":iJournalID, 
                            "strClientREQID":strClientREQID,
                            "EmprestHolder":df['Emprest Holder'].unique()[0],
							"EmailAddress":df['Email address'].unique()[0],
                            "SiteName":site_name,
                            "TotalAmount":df['Amount Rs.'].sum(),
                            "AVXMLGeneratedStatus":CImprestJournal._msTallyStatus,
                            "AVComments":CImprestJournal._msStrAccuVelocityComments,
                            "RecievedDate": datetime.now(),
                            "CReqGeneratedTimeAt":strReqGenereated,
                            "TracebackLogs":CImprestJournal._mStrTracebackLogs
                        }
                    lsReportDict.append(kwargs)
                    await CLogController.MSWriteLog(iUserId, "Error", f"{str(traceback.format_exc())}")
                    await CImprestJournalDetails.MSInsertJournalRecord(kwargs, iUserId)
                    continue
            return {
                "lsReportDict":lsReportDict,
                "saved_xml_files_path":saved_xml_files_path,
            }

        
        except Exception as objException:
            await CLogController.MSWriteLog(iUserId, "Error", f"{str(traceback.format_exc())}")
            raise objException

async def main():
    main_excel_path = r"h:\\AI Data\\DailyData\\AbhinavInfrabuild\\2025_07_07\\AI KILP Imprest (2).xlsx"
    
    Result =await CImprestJournal.MSCreateXMLS(strClientREQID="ABC",iUserId=4, strExcelPath = main_excel_path,strReqGenereated=datetime.now())
    print(Result['lsReportDict'])
    filename = "Test.xml"
    try:
        # with open(filename, 'w', encoding='utf-8') as file:
        #     file.write(Result['xmlContent'])
        print("Result : \n", Result)
        return True
    except IOError as e:
        print(f"Error writing to file: {e}")
        return False
    

if __name__ == "__main__":
    asyncio.run(main())
