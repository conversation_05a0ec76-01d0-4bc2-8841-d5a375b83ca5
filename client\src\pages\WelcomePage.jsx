import { useEffect, useState } from 'react';
import logo from '../assets/logoW.svg';
import rocket from '../assets/rocket.svg';
import { GoArrowDown } from "react-icons/go";
import { motion, AnimatePresence } from 'framer-motion';
import toast, { Toaster } from 'react-hot-toast';
import axios from 'axios'
const WelcomePage = () => {
    const [showDetails, setShowDetails] = useState(false);
    const [enteredEmail, setEnteredEmail] = useState(false);
    const toggleDetails = () => {
        setShowDetails(!showDetails);

        // If we are showing details, scroll down to the details view.
        if (!showDetails) {
            const detailsSection = document.getElementById("detailsSection");
            if (detailsSection) {
                detailsSection.scrollIntoView({ behavior: 'smooth' });
            }
        } else {
            // If hiding details, scroll back up to the top of the page.
            window.scrollTo({
                top: 0,
                behavior: 'smooth',
            });
        }
    };


    useEffect(() => {
        const handleScroll = () => {
            // This threshold should be adjusted based on your content and design
            const threshold = window.innerHeight / 2;

            setShowDetails(window.scrollY > threshold);
        };

        // Add scroll event listener
        window.addEventListener('scroll', handleScroll, { passive: true });

        return () => {
            // Remove scroll event listener
            window.removeEventListener('scroll', handleScroll);
        };
    }, [showDetails]);

    const handleEmailClick = async () => {
        const formData = new FormData();
        formData.append("email", enteredEmail);

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upcoming-user-add`, formData);
            // Handle successful response
            // console.log(response.data); // Assuming you want to log the response data

            // Show success toast for 1000 milliseconds
            toast.success("Email added successfully!", { autoClose: 1000 });

            // Wait for 1000 milliseconds before initiating scroll effect
            setTimeout(() => {
                // Scroll to the details section
                const detailsSection = document.getElementById("detailsSection");
                if (detailsSection) {
                    detailsSection.scrollIntoView({ behavior: 'smooth' });
                }
            }, 2000);
        } catch (error) {
            // Handle error
            console.error("Error adding email:", error);
            toast.error("Failed to add email. Please try again later.");
        }
    };

    return (
        <div className="bg-gradient-to-b from-[#003654] to-[#006daa] text-white w-full overflow-y-auto">
            <Toaster position="top-center" />
            <div className="min-h-screen flex flex-col items-center text-center">
                <AnimatePresence>
                    {!showDetails && (
                        <>
                            <motion.div
                                initial={{ opacity: 0, y: -50 }}
                                animate={{ opacity: 1, y: 0 }}
                                exit={{ opacity: 0, y: -50 }}
                                transition={{ duration: 0.5 }}
                                className="pt-32 w-full text-center"
                            >
                                <img src={logo} alt="AccuVelocity Logo" className="mx-auto" />
                                <p className='text-3xl mt-16' style={{fontWeight:400}}>Something great is on the way</p>
                                <div className="text-9xl 
                                mysqlk-36">
                                    COMING SOON
                                </div>
                            </motion.div>
                            {/* Spacer div */}
                            <div className="flex-grow"></div>
                            {/* Navigation and indicator for more content */}
                            <div className='cursor-pointer text-xlo mb-4' onClick={toggleDetails}>
                                Scroll Down
                            </div>
                            <div className='cursor-pointer animate-bounce mb-4' onClick={toggleDetails}>
                                <GoArrowDown className="text-3xl mx-auto" />
                            </div>
                        </>
                    )}
                </AnimatePresence>
            </div>

            <div id="detailsSection" className="min-h-screen flex flex-col items-center text-center py-16">
                <AnimatePresence>
                    {showDetails && (
                        <motion.div
                            initial={{ opacity: 0, scale: 0.95 }}
                            animate={{ opacity: 1, scale: 1 }}
                            exit={{ opacity: 0, scale: 0.95 }}
                            transition={{ duration: 0.5 }}
                            className="flex flex-col items-center justify-center"
                        >
                            <h2 className="text-4xl font-medium mb-4 lgo:mb-8">Ready to take control of your data effortlessly?</h2>
                            <div className="text-center mb-8 w-full lgo:max-w-4xl mx-auto">
                                <p style={{ fontSize: '18px', fontWeight: 300, lineHeight: '30px', letterSpacing: '0.02em', opacity: '90%' }}>
                                    With AccuVelocity, you can easily capture, connect, and harness your data with precision and simplicity. Our cutting-edge Generative AI tool extracts valuable information from unstructured documents and databases, eliminating data barriers. Seamlessly transform unstructured data from multiple sources into actionable insights.
                                </p>
                            </div>
                            <img src={rocket} alt="Rocket Illustration" className="w-2/3 max-w-sm mx-auto my-5 mb-10" />
                            <p className='text-2xlo mb-8'>Be the first to hear when we go live!</p>
                            <div className="w-full max-w-lg px-5">
                                <form className="flex flex-col items-start w-full" onSubmit={handleEmailClick}>
                                    <label htmlFor="email" className="ml-3 mb-2 text-white font-light">Email*</label>
                                    <div className="flex w-full bg-[#ffff] bg-opacity-10 p-2 rounded-xlo">
                                        <input
                                            id="email"
                                            type="email"
                                            required
                                            onChange={(e) => { setEnteredEmail(e.target.value) }}
                                            className="flex-grow p-1 text-white rounded-xlo bg-transparent focus:bg-transparent focus:outline-none placeholder-white bg-opacity-10"
                                        />
                                        <button
                                            type="submit"
                                            className="px-4 py-1 text-base font-light text-white bg-[#031823] border border-[#003654] rounded-xlo"
                                        >
                                            Notify Me
                                        </button>
                                    </div>
                                </form>
                            </div>
                        </motion.div>
                    )}
                </AnimatePresence>
            </div>
        </div>
    );
};

export default WelcomePage;