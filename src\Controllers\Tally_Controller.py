from typing import Any, Dict
import  json
from datetime import datetime
import re
import zipfile
from typing import List, Optional
import traceback
from fastapi import HTT<PERSON>Exception
from enum import Enum
import sys
sys.path.append(".")
from src.Models.models import ModelTable, TallyTemplate, TallyUserConfig, IntegrationConfig, TallyModelConfig, TallyDocRecords, TallyExports, UploadedDoc, TallyStatusEnum, TDLProcessingRecordStatusEnum, VoucherType
from src.Controllers.DocumentData_controller import CDocumentData
from src.Schemas.Tally_Schemas import AddTallyBody
from src.utilities.helperFunc import CAVXMLParser
from sqlalchemy.future import select
from sqlalchemy import func, or_, cast, Date, and_
from datetime import datetime
from config.db_config import AsyncSessionLocal
from src.Controllers.Logs_Controller import CLogController
import requests
from config.constants import Constants
from sqlalchemy.exc import SQLAlchemyError
import re
import aiohttp  # For making asynchronous HTTP requests
from fastapi import  UploadFile
from typing import List
import os
import pytz
import io
from src.Models.models import AVRequestDetail
from src.utilities.PathHandler import dictProjectPaths

class CTallyController:
    
    @staticmethod
    async def MSSaveTallyExport( iUserID, lsObjFiles:List[UploadFile]):
        try:
            async with AsyncSessionLocal() as session:
                await CLogController.MSWriteLog(iUserID, "Info", f"Saving the Exported Tally Data.")

                # Define base directory
                base_directory = dictProjectPaths.get("strTallyController_ExportBaseDir",  r"H:/AI Data/TallyExports")
                
                # Add today's date folder
                today_date = datetime.now().strftime("%d_%m_%Y")
                save_directory = os.path.join(base_directory, today_date)
                zip_directory = os.path.join(save_directory, "zipDir")

                # Ensure directories exist
                os.makedirs(save_directory, exist_ok=True)
                os.makedirs(zip_directory, exist_ok=True)

                saved_files = []
                for  file in lsObjFiles:
                    original_filename = file.filename
                    file_content = await file.read()  # Read the file content

                    # Wrap the file content in a BytesIO object for zipfile operations
                    file_like = io.BytesIO(file_content)

                    # Check if the file is a zip file
                    is_zip = zipfile.is_zipfile(file_like)

                    if is_zip:
                        # Rename and save the zip file
                        timestamp = datetime.now().strftime("%d_%m_%Y_%H_%M_%S")
                        zip_filename = f"{os.path.splitext(original_filename)[0]}_{timestamp}.zip"
                        zip_file_path = os.path.join(zip_directory, zip_filename)

                        with open(zip_file_path, "wb") as f:
                            f.write(file_content)
                        
                        saved_files.append(zip_file_path)
                        await CLogController.MSWriteLog(iUserID, "Info", f"Zip File Saved at location {zip_file_path}.")

                        # Extract the contents of the zip file
                        with zipfile.ZipFile(zip_file_path, "r") as zip_ref:
                            zip_ref.extractall(save_directory)
                            extracted_files = zip_ref.namelist()
                            for extracted_file in extracted_files:
                                strExtractedFilePath = os.path.join(save_directory, extracted_file)
                                await CTallyController.MSStoreXMLAsXLSX(iUserID=iUserID, strPathToXML=strExtractedFilePath)

                                await CLogController.MSWriteLog(iUserID, "Info", f"Extracted: {extracted_file} to {save_directory}")

                    else:
                        # Save non-zip files
                        file_path = os.path.join(save_directory, original_filename)
                        with open(file_path, "wb") as f:
                            f.write(file_content)
                            
                        await CTallyController.MSStoreXMLAsXLSX(iUserID=iUserID, strPathToXML=file_path)
                        saved_files.append(file_path)
                        await CLogController.MSWriteLog(iUserID, "Info", f"File Saved at location {file_path}.")


                # Prepare database entry
                export_entry = TallyExports(
                    user_id=iUserID,
                    files=saved_files,
                    message="-",
                    created_at=datetime.now(),
                    updated_at=datetime.now()
                )

                session.add(export_entry)
                await session.commit()

                return {
                    "message": "Export data saved successfully.",
                    "saved_files": saved_files
                }

        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "error", f"Failed to Save Exported data, Error:{str(e)}.")
            await CLogController.MSWriteLog(iUserID, "debug", f"{traceback.format_exc()}.")
            raise HTTPException(status_code=500, detail=f"An error occurred while saving the export: {str(e)}")


    @staticmethod
    async def MSStoreXMLAsXLSX(iUserID, strPathToXML):
        try:
            # After receiving the xml file convert it to xl file for storing the stock item database
            if iUserID == 2:
                strStockItemDirPath = dictProjectPaths.get("strParagTraders_StockItemDBDir", r"H:/AI Data/17_ParagTraders/StockItemsDB" )
            else:
                strStockItemDirPath = dictProjectPaths.get("strParagTraders_StockItemDevDBDir", r"H:/AI Data/17_ParagTraders/StockItemsDB/Developer")
            os.makedirs(strStockItemDirPath, exist_ok=True)

            # Generate timestamp in the format day_month_year_hour_minute
            timestamp = datetime.now().strftime("%d_%m_%Y_%H_%M")

            # Create the file name
            strStockItemFileName = f"FilteredStockItemExport_{timestamp}.xlsx"

            # Combine directory path and file name
            strPathStockItemXLSX = os.path.join(strStockItemDirPath, strStockItemFileName)
            strStartXMLTag = 'BODY/DATA/COLLECTION'  # Adjust based on your XML structure

            # Define the flat target tags to extract
            lsTargetXMLTags = [
                'STOCKITEM',           # Corresponds to the 'NAME' attribute
                'PARENT',
                'GSTAPPLICABLE',
                'GSTTYPEOFSUPPLY',
                'BASEUNITS',
                'ISBATCHWISEON',
                'ISCOSTTRACKINGON',
                'OPENINGBALANCE',
                'OPENINGVALUE',
                'OPENINGRATE',
                '_CLOSINGBALANCE',
                '_CLOSINGVALUE',
                '_CLOSINGRATE',
                '_INTEGRATEDTAX',
                '_CENTRALTAX',
                '_CESS',
                "_HSNCODE"
            ]
            outputFilePath = CAVXMLParser.MSConvertSpecificXMLTagsToExcel(input_xml_path=strPathToXML, output_excel_path= strPathStockItemXLSX, start_path=strStartXMLTag, target_tags=lsTargetXMLTags, nested_tags=None)
            return outputFilePath
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "error", f"Failed to Save Exported data, Error:{str(e)}.")
            await CLogController.MSWriteLog(iUserID, "debug", f"{traceback.format_exc()}.")
            print(f"Failed to Store XML file as XLSX, Error:{str(e)}.")


    # ! No Logging is there
    @staticmethod
    async def MSInitializeTemplates():
        """
        Purpose : This method reads a JSON file containing template dummy data and inserts it into the tally_template table.

        Inputs  : No inputs are required

        Output  : It returns a success message upon successful insertion or raises an HTTPException.

        Example : await CTallyController.MSInitializeTemplates()
        """
        try:
            print("**** Tally Templates Creation Started ****")
            # Read the JSON file
            with open(Constants.TallyTemplatePath, 'r') as file:
                dictTemplates = json.load(file)

            # Validate the JSON structure
            # if not isinstance(templates, list):
            #     raise HTTPException(status_code=400, detail="Invalid JSON format: Expected a list of templates.")

            async with AsyncSessionLocal() as session:
                for templateName, templateData in dictTemplates.items():
                    # Ensure required fields are present
                    if 'TemplateKey' not in templateData or 'Header' not in templateData or 'Body' not in templateData:
                        raise HTTPException(status_code=400, detail="Invalid JSON format: Missing required fields.")

                    # Check if the template already exists
                    existing_template = await session.execute(select(TallyTemplate).filter(TallyTemplate.TemplateKey == templateData["TemplateKey"]))
                    if existing_template.scalars().first():
                        raise HTTPException(status_code=400, detail=f"Template with name '{templateName}' already exists.")

                    # Insert the new template
                    new_template = TallyTemplate(
                        Name=templateName,
                        TemplateKey=templateData["TemplateKey"],
                        Header=templateData['Header'],
                        Body=templateData['Body']
                    )
                    session.add(new_template)
                
                # Commit the transaction
                await session.commit()
            
            print("**** Tally Templates Creation Completed ****")
            return {"message": "Templates have been successfully initialized."}

        except HTTPException as http_exc:
            # Re-raise HTTPException to be handled by FastAPI
            raise http_exc

        except json.JSONDecodeError:
            raise HTTPException(status_code=400, detail="Invalid JSON file. Please provide a valid JSON file.")

        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="JSON file not found. Please check the file path.")

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred during the template initialization process: {str(e)}")


    @staticmethod
    async def MSGetTallyTemplateOnKey(template_key: str):
        """
        Purpose : This method fetches a single template from the tally_template table using the provided TemplateKey.
                  It also adds the list of required fields from the 'Body' field.

        Inputs  : template_key (str): The key of the template to fetch.

        Output  : It returns the template data as a dictionary if found, or raises an HTTPException if not.

        Example : await CTallyController.get_template_by_key("1")
        """
        try:
            async with AsyncSessionLocal() as session:
                # Query the template based on the provided TemplateKey
                result = await session.execute(select(TallyTemplate).filter(TallyTemplate.TemplateKey == template_key))
                template = result.scalars().first()

                if not template:
                    raise HTTPException(status_code=404, detail=f"Template with key '{template_key}' not found.")

                # Extract required fields from Body
                required_fields = [
                    field_name for field_name, field_data in template.Body.items()
                    if isinstance(field_data, dict) and field_data.get('required') is True
                ]

                # Prepare the template data in dict format
                template_dict = {
                    "Id": template.Id,
                    "Name": template.Name,
                    "TemplateKey": template.TemplateKey,
                    "Header": template.Header,
                    "Body": template.Body,
                    "CreatedDateTime": template.CreatedDateTime,
                    "ModifiedDateTime": template.ModifiedDateTime,
                    "required-fields": required_fields
                }

                return template_dict

        except HTTPException as http_exc:
            # Re-raise HTTPException to be handled by FastAPI
            raise http_exc

        except Exception as e:
            # Catch-all for any other exceptions
            raise HTTPException(status_code=500, detail=f"An error occurred while fetching the template: {str(e)}")
        
    @staticmethod
    async def MSGetAllTemplates(iUserId):
        """
        Purpose : This method fetches all templates from the tally_template table.

        Inputs  : No inputs are required.

        Output  : It returns a list of all templates in the database or raises an HTTPException.

        Example : await CTallyController.MSGetAllTemplates()
        """
        try:
            async with AsyncSessionLocal() as session:
                # Fetch all templates
                result = await session.execute(select(TallyTemplate))
                templates = result.scalars().all()

                # Check if any templates were found
                if not templates:
                    raise HTTPException(status_code=404, detail="No templates found.")

                # Prepare the response
                template_list = []
                for template in templates:
                    template_list.append({
                        "Id": template.Id,
                        "Name": template.Name,
                        "TemplateKey": template.TemplateKey,
                        "Header": template.Header,
                        "Body": template.Body,
                        "CreatedDateTime": template.CreatedDateTime,
                        "ModifiedDateTime": template.ModifiedDateTime,
                    })

                return {"templates": template_list}

        except HTTPException as http_exc:
            # Re-raise HTTPException to be handled by FastAPI
            raise http_exc

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while fetching templates: {str(e)}")

    @staticmethod
    async def MSUpdateTallyDocRecordBaseOnREQID(
        iDocID: int,
        iUserID: int,
        strREQID: str,
        invoice_no: str = None,
        invoice_date: datetime = None,
        tally_api_req: dict = None,
        tally_api_resp: dict = None,
        tally_status: str = None,
        av_tally_xml_status: str = None,
        strAVComments: str = None,
        DocErrorMsg: str = None,
        resp_date_time=None,
        voucher_type: VoucherType = None
    ):
        """
        Input:

            1) iDocID: int
            Document ID to locate the record.

            2) iUserID: int
            ID of the user requesting the update.

            3) strREQID: str
            Unique request ID of the record.

            4) Other kwargs like invoice_no, tally_status, etc. to update values.

        Output:

            dict: Updated record metadata.

        Purpose:

            To update an existing TallyDocRecords entry based on (REQID, iDocID, iUserID).
            If not found, raises 404 error.
        """
        try:
            async with AsyncSessionLocal() as session:
                query = select(TallyDocRecords).filter(
                    TallyDocRecords.DocID == iDocID,
                    TallyDocRecords.Userid == iUserID,
                    TallyDocRecords.REQID == strREQID
                )
                result = await session.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail="Matching record not found for update.")

                if invoice_no is not None:
                    record.InvoiceNo = invoice_no
                if invoice_date is not None:
                    record.InvoiceDate = invoice_date
                if tally_api_req is not None:
                    record.TallyAPIReq = json.dumps(tally_api_req)
                if tally_status is not None:
                    record.TallyStatus = tally_status
                if av_tally_xml_status is not None:
                    record.AVTallyXMLStatus = av_tally_xml_status
                if tally_api_resp is not None:
                    record.TallyAPIResp = json.dumps(tally_api_resp)
                if strAVComments is not None:
                    record.AVComments = strAVComments
                if DocErrorMsg is not None:
                    record.DocErrorMsg = DocErrorMsg
                if resp_date_time is not None:
                    record.RespDateTime = resp_date_time
                if voucher_type is not None:
                    record.VoucherType = voucher_type

                await session.commit()
                return {
                    "Id": record.Id,
                    "DocID": record.DocID,
                    "Userid": record.Userid,
                    "InvoiceNo": record.InvoiceNo,
                    "InvoiceDate": record.InvoiceDate,
                    "TallyStatus": record.TallyStatus,
                    "AVTallyXMLStatus": record.AVTallyXMLStatus,
                    "TallyAPIReq": json.loads(record.TallyAPIReq),
                    "TallyAPIResp": json.loads(record.TallyAPIResp),
                    "ReqDateTime": record.ReqDateTime,
                    "RespDateTime": record.RespDateTime,
                    "DocErrorMsg": record.DocErrorMsg,
                    "REQID": record.REQID,
                    "AVComments": record.AVComments,
                    "VoucherType": record.VoucherType
                }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error updating TallyDocRecord: {str(e)}")


    @staticmethod
    async def MSSetTallyDocRecords(
        doc_id: int = None,
        invoice_no: str = None,
        invoice_date: datetime = None,
        DocErrorMsg: str = None,
        tally_api_req: dict = None,
        tally_status: str = None,
        av_tally_xml_status: str = None,
        tally_api_resp: dict = None,
        resp_date_time=None,
        strAVComments: str = None,
        REQID: str = None,
        user_id: int = None,
        voucher_type: VoucherType = None
    ):
        """
        WARNING: Please verify document belongs to the particular user before performing operation
        Purpose: This method updates a TallyDocRecords entry if it exists or creates a new one if not.
        """
        try:
            async with AsyncSessionLocal() as session:
                record = None

                # Enhanced search logic
                if doc_id is None and invoice_no:
                    query = select(TallyDocRecords).filter(TallyDocRecords.InvoiceNo == invoice_no)

                    if user_id is not None:
                        query = query.filter(TallyDocRecords.Userid == user_id)

                    if voucher_type is not None:
                        query = query.filter(TallyDocRecords.VoucherType == voucher_type)

                    result = await session.execute(query)
                    record = result.scalars().first()

                # If still not found, try with doc_id
                if record is None and doc_id is not None:
                    query = select(TallyDocRecords).filter(TallyDocRecords.DocID == doc_id)
                    result = await session.execute(query)
                    record = result.scalars().first()

                # Get ReqDateTime from uploaded_docs if possible
                uploaded_doc = await session.execute(
                    select(UploadedDoc.UploadedDateTime).where(
                        UploadedDoc.DocId == (doc_id or record.DocID if record else None)
                    )
                )
                uploaded_doc = uploaded_doc.scalar_one_or_none()
                ReqDateTime = uploaded_doc if uploaded_doc else datetime.now()

                if record:
                    # Update existing record
                    if tally_api_req is not None:
                        record.TallyAPIReq = json.dumps(tally_api_req)
                    if invoice_no is not None:
                        record.InvoiceNo = invoice_no
                    if invoice_date is not None:
                        record.InvoiceDate = invoice_date
                    if tally_status is not None:
                        record.TallyStatus = tally_status
                    if av_tally_xml_status is not None:
                        record.AVTallyXMLStatus = av_tally_xml_status
                    if DocErrorMsg is not None:
                        record.DocErrorMsg = DocErrorMsg
                    if tally_api_resp is not None:
                        record.TallyAPIResp = json.dumps(tally_api_resp)
                    if resp_date_time is not None:
                        record.RespDateTime = resp_date_time
                    if REQID is not None:
                        record.REQID = REQID
                    if strAVComments is not None:
                        record.AVComments = strAVComments
                    if user_id is not None:
                        record.Userid = user_id
                    if voucher_type is not None:
                        record.VoucherType = voucher_type
                else:
                    # Create new record
                    record = TallyDocRecords(
                        DocID=doc_id,
                        Userid=user_id,
                        InvoiceNo=invoice_no,
                        InvoiceDate=invoice_date,
                        TallyAPIReq=json.dumps(tally_api_req) if tally_api_req is not None else '{}',
                        TallyStatus=tally_status,
                        AVTallyXMLStatus=av_tally_xml_status,
                        ReqDateTime=ReqDateTime,
                        TallyAPIResp=json.dumps(tally_api_resp) if tally_api_resp is not None else '{}',
                        RespDateTime=resp_date_time,
                        DocErrorMsg=DocErrorMsg,
                        REQID=REQID,
                        AVComments=strAVComments or '',
                        VoucherType=voucher_type
                    )
                    session.add(record)

                await session.commit()
                return {
                    "Id": record.Id,
                    "DocID": record.DocID,
                    "Userid": record.Userid,
                    "InvoiceNo": record.InvoiceNo,
                    "InvoiceDate": record.InvoiceDate,
                    "TallyAPIReq": json.loads(record.TallyAPIReq),
                    "TallyStatus": record.TallyStatus,
                    "AVTallyXMLStatus": record.AVTallyXMLStatus,
                    "ReqDateTime": record.ReqDateTime,
                    "TallyAPIResp": json.loads(record.TallyAPIResp),
                    "RespDateTime": record.RespDateTime,
                    "CreatedDateTime": record.CreatedDateTime,
                    "DocErrorMsg": record.DocErrorMsg,
                    "REQID": record.REQID,
                    "AVComments": record.AVComments,
                    "VoucherType": record.VoucherType,
                }
        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while setting TallyDocRecords: {str(e)}")

    @staticmethod
    async def MSCreateTallyDocRecord(
        iUserId: int,
        iDocID: int,
        strREQID: str,
        invoice_no: str = None,
        invoice_date: datetime = None,
        tally_api_req: dict = None,
        tally_api_resp: dict = None,
        tally_status: str = None,
        av_tally_xml_status: str = None,
        strAVComments: str = None,
        DocErrorMsg: str = None,
        resp_date_time=None,
        voucher_type: VoucherType = None
    ):
        """
        Input:

            1) iUserId: int
            ID of the user creating the record.

            2) iDocID: int
            Document ID this Tally record refers to.

            3) strREQID: str
            Unique request ID linked to the document.

            4) invoice_no, invoice_date, tally_api_req, etc.:
            Optional metadata for the Tally document.

        Output:

            int: Newly created TallyDocRecord ID.

        Purpose:

            To strictly create a new TallyDocRecords entry without updating any existing records.
        """
        try:
            async with AsyncSessionLocal() as session:
                ReqDateTime = datetime.now()

                new_record = TallyDocRecords(
                    DocID=iDocID,
                    Userid=iUserId,
                    InvoiceNo=invoice_no,
                    InvoiceDate=invoice_date,
                    TallyAPIReq=json.dumps(tally_api_req) if tally_api_req else '{}',
                    TallyAPIResp=json.dumps(tally_api_resp) if tally_api_resp else '{}',
                    TallyStatus=tally_status,
                    AVTallyXMLStatus=av_tally_xml_status,
                    ReqDateTime=ReqDateTime,
                    RespDateTime=resp_date_time,
                    AVComments=strAVComments or "",
                    DocErrorMsg=DocErrorMsg,
                    REQID=strREQID,
                    VoucherType=voucher_type
                )

                session.add(new_record)
                await session.commit()
                await session.refresh(new_record)
                return new_record.Id

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Error creating TallyDocRecord: {str(e)}")

    @staticmethod
    async def MSFetchTallyDocRecords(
        doc_id: Optional[int] = None,
        invoice_no: Optional[str] = None,
        userid: Optional[int] = None,
        voucher_type: Optional[VoucherType] = None
    ) -> List[Dict[str, Any]]:
        """
        Purpose: Fetch TallyDocRecords entries using DocID, InvoiceNo, Userid, or VoucherType.

        Inputs:
            - doc_id: int (Optional; The DocID of the record to fetch)
            - invoice_no: str (Optional; The invoice number of the record to fetch)
            - userid: int (Optional; The User ID associated with the record)
            - voucher_type: VoucherType (Optional; The voucher type of the record)

        Output: Returns a list of matching records as dictionaries.

        Example: await CTallyController.MSFetchTallyDocRecords(doc_id=1, userid=100, voucher_type=VoucherType.PV_WITH_INVENTORY)
        """
        try:
            # Validate that at least one parameter is provided
            if not any([doc_id, invoice_no, userid, voucher_type]):
                raise HTTPException(
                    status_code=400,
                    detail="At least one of doc_id, invoice_no, userid, or voucher_type must be provided."
                )

            async with AsyncSessionLocal() as session:
                query = select(TallyDocRecords)

                # Apply filters based on provided parameters
                if doc_id is not None:
                    query = query.filter(TallyDocRecords.DocID == doc_id)
                if invoice_no is not None:
                    query = query.filter(TallyDocRecords.InvoiceNo == invoice_no)
                if userid is not None:
                    query = query.filter(TallyDocRecords.Userid == userid)
                if voucher_type is not None:
                    query = query.filter(TallyDocRecords.VoucherType == voucher_type)

                # Order by CreatedDateTime descending
                query = query.order_by(TallyDocRecords.CreatedDateTime.desc())

                result = await session.execute(query)
                records = result.scalars().all()  # Fetch all matching records

                listResult = []
                for record in records:
                    dictResult = {
                        "Id": record.Id,
                        "DocID": record.DocID,
                        "Userid": record.Userid,
                        "InvoiceNo": record.InvoiceNo,
                        "InvoiceDate": record.InvoiceDate,
                        "TallyAPIReq": json.loads(record.TallyAPIReq) if record.TallyAPIReq else {},
                        "TallyStatus": record.TallyStatus,
                        "AVTallyXMLStatus": record.AVTallyXMLStatus,
                        "ReqDateTime": record.ReqDateTime,
                        "TallyAPIResp": json.loads(record.TallyAPIResp),
                        "RespDateTime": record.RespDateTime,
                        "CreatedDateTime": record.CreatedDateTime,
                        "REQID": record.REQID,
                        "AVComments": record.AVComments,
                        "DocErrorMsg": record.DocErrorMsg,
                        "VoucherType": record.VoucherType.value if record.VoucherType else None
                    }
                    listResult.append(dictResult)

                return listResult

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"An error occurred while fetching TallyDocRecords: {str(e)}"
            )

    @staticmethod
    async def MSFetchTallyDocRecordsByDocIDOrREQID(
        doc_id: int = None, REQID: str = None, invoice_no: str = None
    ):
        """
        Purpose: Fetch TallyDocRecords entries using DocID and InvoiceNo or DocID and REQID.

        Inputs:
            - doc_id: int (Optional; The DocID of the record to fetch)
            - REQID: str (Optional; The REQID of the record to fetch)
            - invoice_no: str (Optional; The invoice number of the record to fetch)

        Output: Returns a list of matching records as dictionaries.

        Example: await CTallyController.MSFetchTallyDocRecordsByDocIDOrREQID(doc_id=1, REQID="REQ123")
        """
        try:
            if not doc_id or (not invoice_no and not REQID):
                raise HTTPException(status_code=400, detail="DocID and either InvoiceNo or REQID must be provided.")

            async with AsyncSessionLocal() as session:
                query = select(TallyDocRecords).filter(TallyDocRecords.DocID == doc_id)

                if invoice_no:
                    query = query.filter(TallyDocRecords.InvoiceNo == invoice_no)
                elif REQID:
                    query = query.filter(TallyDocRecords.REQID == REQID)

                query = query.order_by(TallyDocRecords.CreatedDateTime.desc())  # Order by CreatedDateTime descending

                result = await session.execute(query)
                records = result.scalars().all()  # Fetch all records

                listResult = []
                for record in records:
                    dictResult = {
                        "Id": record.Id,
                        "DocID": record.DocID,
                        "InvoiceNo": record.InvoiceNo,
                        "InvoiceDate": record.InvoiceDate,
                        "TallyAPIReq": json.loads(record.TallyAPIReq),
                        "TallyStatus": record.TallyStatus,
                        "AVTallyXMLStatus": record.AVTallyXMLStatus,
                        "ReqDateTime": record.ReqDateTime,
                        "TallyAPIResp": json.loads(record.TallyAPIResp),
                        "RespDateTime": record.RespDateTime,
                        "CreatedDateTime": record.CreatedDateTime,
                        "DocErrorMsg": record.DocErrorMsg,
                        "AVComments": record.AVComments,
                        "REQID": record.REQID
                    }
                    listResult.append(dictResult)

                return listResult

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while fetching TallyDocRecords: {str(e)}")
    
    @staticmethod
    async def MSGetTallyDocRecords(doc_id: int):
        """
        Purpose: This method retrieves a TallyDocRecords entry based on DocID.

        Inputs:
            - doc_id: int (The DocID of the record to fetch)

        Output: Returns the record as a dictionary if found, or raises an HTTPException if not found.

        Example: await CTallyController.MSGetTallyDocRecords(doc_id=1)
        """
        try:
            async with AsyncSessionLocal() as session:
                # Query the record based on DocID
                result = await session.execute(select(TallyDocRecords).filter(TallyDocRecords.DocID == doc_id))
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail=f"Record with DocID '{doc_id}' not found.")

                # Return the record as a dictionary (deserialize the JSON fields)
                return {
                    "Id": record.Id,
                    "DocID": record.DocID,
                    "TallyAPIReq": json.loads(record.TallyAPIReq),  # Deserialize from JSON
                    "TallyStatus": record.TallyStatus,
                    "ReqDateTime": record.ReqDateTime,
                    "TallyAPIResp": json.loads(record.TallyAPIResp),  # Deserialize from JSON
                    "RespDateTime": record.RespDateTime,
                    "CreatedDateTime": record.CreatedDateTime,
                    "DocErrorMsg":record.DocErrorMsg
                }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while fetching TallyDocRecords: {str(e)}")
        
    @staticmethod
    async def MSManageIntegration(iUserID: int, authKey: str = None, integration: str = "Tally", action: str = "subscribe"):
        """
        Manage integration subscription and unsubscription for a user.

        Inputs:
            - iUserID: int - The ID of the user.
            - authKey: str - The authentication key for the Tally integration (required for subscription).
            - integration: str - The type of integration ("Tally" by default).
            - action: str - The action to perform ("subscribe" or "unsubscribe").

        Output:
            - Returns a success message upon successful operation or raises an HTTPException.

        Example:
            await CTallyController.MSManageIntegration(iUserID=1, authKey="authKey123", action="subscribe")
            await CTallyController.MSManageIntegration(iUserID=1, action="unsubscribe")
        """
        async with AsyncSessionLocal() as db:
            try:
                if integration == "Tally":
                    # Subscription
                    if action == "subscribe":
                        # if not authKey:
                        #     raise HTTPException(status_code=400, detail="AuthKey is required for subscription.")

                        # Create or update the TallyUserConfig for the user
                        tally_config = await db.execute(select(TallyUserConfig).filter(TallyUserConfig.UserID == iUserID))
                        objTallyUserConfig = tally_config.scalars().first()

                        if not objTallyUserConfig:
                            new_tally_user_config = TallyUserConfig(UserID=iUserID, AuthKey=authKey, TallyEnable=True)
                            db.add(new_tally_user_config)
                        else:
                            objTallyUserConfig.AuthKey = authKey
                            objTallyUserConfig.TallyEnable = True

                        # Update the integration config
                        integration_config = await db.execute(select(IntegrationConfig).filter(IntegrationConfig.UserID == iUserID))
                        objIntegrationConfig = integration_config.scalars().first()

                        if not objIntegrationConfig:
                            new_integration_config = IntegrationConfig(UserID=iUserID, Tally=True)
                            db.add(new_integration_config)
                        else:
                            objIntegrationConfig.Tally = True

                        await db.commit()
                        return {"message": "Integration subscription successful."}

                    # Unsubscription
                    elif action == "unsubscribe":
                        # Fetch the integration config
                        integration_config = await db.execute(select(IntegrationConfig).filter(IntegrationConfig.UserID == iUserID))
                        objIntegrationConfig = integration_config.scalars().first()

                        if objIntegrationConfig and objIntegrationConfig.Tally:
                            objIntegrationConfig.Tally = False

                            # Also remove the TallyUserConfig for this user
                            # tally_config = await db.execute(select(TallyUserConfig).filter(TallyUserConfig.UserID == iUserID))
                            # objTallyUserConfig = tally_config.scalars().first()

                            # if objTallyUserConfig:
                            #     await db.delete(objTallyUserConfig)

                            await db.commit()
                            return {"message": "Integration unsubscription successful."}
                        else:
                            raise HTTPException(status_code=404, detail="Tally integration not found for the user.")

                    else:
                        raise HTTPException(status_code=400, detail="Unsupported action type. Use 'subscribe' or 'unsubscribe'.")

                else:
                    raise HTTPException(status_code=400, detail="Unsupported integration type.")

            except Exception as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=f"Error occurred while managing integration: {str(e)}")

    @staticmethod
    async def MSGetTallyUserConfig(iUserID: int):
        """
        Fetches the TallyUserConfig for a given user ID.

        Args:
            iUserID (int): The ID of the user.

        Returns:
            dict: A dictionary containing the TallyUserConfig details.

        Raises:
            HTTPException: If the configuration is not found or an error occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Query the TallyUserConfig based on the UserID
                query = select(TallyUserConfig).filter(TallyUserConfig.UserID == iUserID)
                result = await db.execute(query)
                tally_user_config = result.scalar_one_or_none()

                if not tally_user_config:
                    raise HTTPException(status_code=404, detail="Tally user configuration not found.")

                # Convert the TallyUserConfig model to a dictionary
                config_dict = {
                    "Id": tally_user_config.Id,
                    "UserID": tally_user_config.UserID,
                    "AuthKey": tally_user_config.AuthKey,
                    "TallyEnable": tally_user_config.TallyEnable,
                    "TallyHeaderObj": tally_user_config.TallyHeaderObj,
                    "TallyLedgerConfig": tally_user_config.TallyLedgerConfig,
                    "TallyStockItemConfig": tally_user_config.TallyStockItemConfig,
                    "TotalPagesProcessed": tally_user_config.TotalPagesProcessed,
                    "TotalTimeSavedInMinutes":tally_user_config.TotalTimeSavedInMinutes,
                    "CreatedDateTime": tally_user_config.CreatedDateTime.isoformat() if tally_user_config.CreatedDateTime else None,
                    "ModifiedDateTime": tally_user_config.ModifiedDateTime.isoformat() if tally_user_config.ModifiedDateTime else None
                }

                return config_dict

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"An error occurred while fetching Tally user configuration: {str(e)}")

    @staticmethod
    async def MSGetDetailSummaryDeliveryNote(iUserID: int, file_name: str):
        """
        Fetch summary and details from AVRequestDetail based on user ID and file name.

        Args:
            iUserID (int): The ID of the user.
            file_name (str): The name of the file to filter EstAccountantTimeSaved.

        Returns:
            dict: Summary containing:
                - total_count (int)
                - file_time_saved (float or None)
                - all_time_saved (List[float])
        """
        async with AsyncSessionLocal() as db:
            try:
                # Base query filter
                base_filter = and_(
                    AVRequestDetail.User_UID == iUserID,
                    AVRequestDetail.AVXMLGeneratedStatus.in_(["PartialSuccess", "Success"]),
                    AVRequestDetail.strVoucherType == "DELIVERY_NOTE"
                )

                # 1. Total Count of matching rows
                count_query = select(func.count()).where(base_filter)
                total_count_result = await db.execute(count_query)
                total_count = total_count_result.scalar()

                # 2. EstAccountantTimeSaved for the given file name
                

                # 3. List of EstAccountantTimeSaved for matching rows
                time_saved_query = select(AVRequestDetail.EstAccountantTimeSaved).where(base_filter)
                time_saved_result = await db.execute(time_saved_query)
                all_time_saved = [row[0] for row in time_saved_result.fetchall()]

                return {
                    "total_count": total_count,
                    "all_time_saved": all_time_saved
                }

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"An error occurred while fetching AVRequestDetail summary: {str(e)}")

            
    @staticmethod
    async def MSSetTallyUserConfig(
        iUserID: int,
        auth_key: str = None,
        tally_enable: bool = None,
        TallyHeaderObj: dict = None,
        tally_ledger_config: dict = None,
        tally_stock_item_config: dict = None,
        iTotalPagesProcessed: int = None,
        iTotalTimeSaved: int = None
    ):
        """
        Sets or updates the TallyUserConfig for a given user ID, updating only the provided arguments.

        Args:
            iUserID (int): The ID of the user (required).
            auth_key (str, optional): The authentication key for the Tally configuration.
            tally_enable (bool, optional): Boolean to enable or disable Tally.
            TallyHeaderObj (dict, optional): The JSON configuration for Voucher Type.
            tally_ledger_config (dict, optional): The JSON configuration for Ledger.
            tally_stock_item_config (dict, optional): The JSON configuration for Stock Item.
            iTotalPagesProcessed (int, optional): Total pages processed.
            iTotalTimeSaved (int, optional): Total time saved.

        Returns:
            dict: A success message indicating the configuration has been set or updated.

        Raises:
            HTTPException: If an error occurs during the process.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Query the TallyUserConfig based on the UserID
                query = select(TallyUserConfig).filter(TallyUserConfig.UserID == iUserID)
                result = await db.execute(query)
                tally_user_config = result.scalar_one_or_none()

                if tally_user_config:
                    # Update only the provided fields
                    if auth_key is not None:
                        tally_user_config.AuthKey = auth_key
                    if tally_enable is not None:
                        tally_user_config.TallyEnable = tally_enable
                    if TallyHeaderObj is not None:
                        tally_user_config.TallyHeaderObj = TallyHeaderObj
                    if tally_ledger_config is not None:
                        tally_user_config.TallyLedgerConfig = tally_ledger_config
                    if tally_stock_item_config is not None:
                        tally_user_config.TallyStockItemConfig = tally_stock_item_config
                    if iTotalPagesProcessed is not None:
                        tally_user_config.TotalPagesProcessed = iTotalPagesProcessed
                    if iTotalTimeSaved is not None:
                        tally_user_config.TotalTimeSavedInMinutes = iTotalTimeSaved

                    message = "Tally user configuration updated successfully."
                else:
                    # Create a new TallyUserConfig with only provided fields
                    new_tally_user_config = TallyUserConfig(UserID=iUserID)
                    if auth_key is not None:
                        new_tally_user_config.AuthKey = auth_key
                    if tally_enable is not None:
                        new_tally_user_config.TallyEnable = tally_enable
                    if TallyHeaderObj is not None:
                        new_tally_user_config.TallyHeaderObj = TallyHeaderObj
                    if tally_ledger_config is not None:
                        new_tally_user_config.TallyLedgerConfig = tally_ledger_config
                    if tally_stock_item_config is not None:
                        new_tally_user_config.TallyStockItemConfig = tally_stock_item_config
                    if iTotalPagesProcessed is not None:
                        new_tally_user_config.TotalPagesProcessed = iTotalPagesProcessed
                    if iTotalTimeSaved is not None:
                        new_tally_user_config.TotalTimeSavedInMinutes = iTotalTimeSaved

                    db.add(new_tally_user_config)
                    message = "Tally user configuration created successfully."

                # Commit the transaction to save changes
                await db.commit()

                return {"message": message}

            except Exception as e:
                await db.rollback()  # Rollback transaction on error
                raise HTTPException(
                    status_code=500,
                    detail=f"An error occurred while setting Tally user configuration: {str(e)}"
                )

            
            
    @staticmethod
    async def MSAddTallyModelConfig(
        iUserID: int, 
        templateName: str, 
        companyName: str, 
        modelId: int, 
        mappings: dict, 
        GSTIN: str = None,
        ExtraFields: dict = None,
        tally_enable:bool = False,
        tally_ledger_config:dict = None,
        TallyHeaderObj:dict=None,
        tally_stock_item_config:dict = None
        
    ):
        """
        Adds or updates a Tally model configuration and updates TallyUserConfig if field mappings have a FieldType and FieldValue.

        Args:
            iUserID (int): The ID of the user.
            templateName (str): The name of the Tally template.
            companyName (str): The name of the company.
            modelId (int): The ID of the associated model.
            mappings (dict): The JSON mappings between the template fields and the model fields.
            GSTIN (str): The GST Identification Number for the company (optional).
            ExtraFields (dict): Additional fields (optional).

        Returns:
            dict: A success message.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Fetch the TallyTemplateID based on the template name
                template_record = await db.execute(
                    select(TallyTemplate.Id).filter(TallyTemplate.Name == templateName)
                )
                objTallyTemplate = template_record.scalars().first()

                if not objTallyTemplate:
                    raise HTTPException(status_code=404, detail="Template not found.")

                # Check if a TallyModelConfig already exists for the given modelId
                existing_config = await db.execute(
                    select(TallyModelConfig).filter(TallyModelConfig.ModelId == modelId)
                )
                objModelConfig = existing_config.scalars().first()

                if objModelConfig:
                    # Update the existing configuration
                    objModelConfig.TallyTemplateID = objTallyTemplate
                    objModelConfig.CompanyName = companyName
                    objModelConfig.Mappings = mappings
                    objModelConfig.GSTIN = GSTIN
                    objModelConfig.ExtraFields = ExtraFields
                    message = "Tally model configuration updated successfully."
                else:
                    # Create a new TallyModelConfig
                    new_model_config = TallyModelConfig(
                        TallyTemplateID=objTallyTemplate,
                        CompanyName=companyName,
                        ModelId=modelId,
                        Mappings=mappings,
                        GSTIN=GSTIN,
                        ExtraFields=ExtraFields
                    )
                    db.add(new_model_config)
                    message = "Tally model configuration added successfully."

                # Get existing TallyUserConfig for the user (if any)
                tally_user_config_response = await CTallyController.MSGetTallyUserConfig(iUserID)
                tally_ledger_config = tally_user_config_response['TallyLedgerConfig'] if tally_user_config_response['TallyLedgerConfig'] else {}

                # Update TallyLedgerConfig with the mappings FieldValue if FieldType is present
                for field_key, field_data in mappings.get("Fields", {}).items():
                    field_type = field_data.get("FieldType")
                    field_value = field_data.get("FieldValue")

                    if field_type and field_value:
                        # If FieldType doesn't exist in TallyLedgerConfig, create an empty dict for it
                        if field_type not in tally_ledger_config:
                            tally_ledger_config[field_type] = {}

                        # If FieldValue doesn't exist under FieldType, add it with an empty dict as value
                        if field_value not in tally_ledger_config[field_type]:
                            tally_ledger_config[field_type][field_value] = {}

                # Now set the updated TallyLedgerConfig back to the user
                auth_key = tally_user_config_response['AuthKey'] if tally_user_config_response else "DefaultAuthKey"
                await CTallyController.MSSetTallyUserConfig(iUserID=iUserID, auth_key=auth_key, tally_ledger_config=tally_ledger_config,tally_enable=tally_enable, TallyHeaderObj=TallyHeaderObj,tally_stock_item_config=tally_stock_item_config)

                # Commit the changes
                await db.commit()

                return {"message": message}

            except HTTPException as http_exc:
                raise http_exc

            except Exception as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=f"Error occurred while adding or updating Tally model configuration: {str(e)}")

    @staticmethod
    async def MSGetModelConfig(iUserID: int, iModelID: int) -> Dict[str, Any]:
        """
        Fetches the Tally model configuration associated with a specific user and model.

        Args:
            iUserID (int): The ID of the user.
            iModelID (int): The ID of the model to fetch the configuration for.

        Returns:
            Dict[str, Any]: The Tally model configuration.

        Raises:
            HTTPException: If the configuration is not found or an error occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(TallyModelConfig).join(
                    ModelTable,
                    TallyModelConfig.ModelId == ModelTable.Id
                ).filter(
                    TallyModelConfig.ModelId == iModelID
                )
                result = await db.execute(query)
                model_config = result.scalar_one_or_none()

                if not model_config:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Tally model configuration not found for user {iUserID} and model {iModelID}")
                    raise HTTPException(status_code=404, detail="Tally model configuration not found")

                config_dict = {
                    column.name: getattr(model_config, column.name)
                    for column in model_config.__table__.columns
                    if column.name != 'Id'  # Exclude 'Id' if you don't want to include it
                }

                # Convert datetime objects to strings for JSON serialization
                for key in ['CreatedDateTime', 'ModifiedDateTime']:
                    if key in config_dict and config_dict[key]:
                        config_dict[key] = config_dict[key].isoformat()

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully fetched Tally model configuration for user {iUserID} and model {iModelID}")
                return {"config": config_dict}

            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Database error while fetching Tally model configuration: {str(e)}")
                raise HTTPException(status_code=500, detail="Database error occurred")
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Unexpected error while fetching Tally model configuration: {str(e)}")
                raise HTTPException(status_code=500, detail="An unexpected error occurred")


    @staticmethod
    async def MSParseDate(strDate):
        try:
            # Try parsing with four-digit year first
            return datetime.strptime(strDate, "%m/%d/%Y")
        except Exception as e:
            # If parsing fails, try with two-digit year
            return datetime.strptime(strDate, "%m/%d/%y")
        
    # @staticmethod
    # async def MSFetchMappedDocumentData(iUserID: int, iDocID: int):
    #     """
    #     Fetches the mapped document data based on Tally configuration for a given document ID.

    #     Args:
    #         iUserID (int): The ID of the user.
    #         iDocID (int): The ID of the document.

    #     Returns:
    #         dict: A dictionary containing the mapped document data.

    #     Raises:
    #         HTTPException: If the document is not found or an error occurs.
    #     """
    #     from src.Controllers.GPTResponse_controller import CGPTResponseData

    #     async with AsyncSessionLocal() as db:
    #         try:
    #             dictData = await CGPTResponseData.MSGetDocumentById(iUserID=iUserID, iDocId=iDocID)
    #             dictDocExtractedData = dictData.get("DocExtractedData")
                
                

    #         except HTTPException as http_exc:
    #             raise http_exc
    #         except Exception as e:
    #             await CLogController.MSWriteLog(iUserID, "Error", f"An error occurred while fetching mapped document data for document {iDocID}: {str(e)}")
    #             await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
    #             raise HTTPException(status_code=500, detail="An error occurred while processing your request")
    
    
    @staticmethod
    def preprocess_mapped_data(tally_mappings, dictFields, extra_fields, model_fields):
        """
        Maps and preprocesses the data based on Tally mappings and field categories.

        Args:
            tally_mappings (dict): Mappings from Tally fields to model fields.
            dictFields (dict): Extracted fields from the document.
            extra_fields (dict): Extra fields from Tally configuration.
            model_fields (list): List of field definitions from the model.

        Returns:
            dict: The mapped and preprocessed data.
        """
        field_info = {field['FieldName']: field for field in model_fields}
        mapped_data = {}

        # Regex pattern to identify Debit Ledger N fields (e.g., Debit Ledger 1, Debit Ledger 2, etc.)
        debit_ledger_pattern = re.compile(r'Debit Ledger (\d+)')

        IGST = dictFields.get("IGST Total Amt")
        CGST = dictFields.get("CGST Total Amt")
        SGST = dictFields.get("SGST Total Amt")
        
        # Regular expression to match a number greater than 0
        pattern = r'^[0-9]*\.?[0-9]+$'

        if IGST and IGST is not None and IGST != 0:
            if re.match(pattern, str(IGST)):
                SelectedGST = "outside state"
            else:
                SelectedGST = "state"
        else:
            SelectedGST = "state"
        
        

            
        def process_field(value, model_field):
            """Helper function to process fields based on category and format."""
            if model_field in field_info:
                field_category = field_info[model_field].get('FieldCategory')
                field_format = field_info[model_field].get('FieldFormat')

                if field_category == 'Date' and value:
                    try:
                        # Parse the date using the field format
                        date_obj = datetime.strptime(value, field_format)
                        # Convert to Tally's required format (YYYY-MM-DD)
                        value = date_obj.strftime('%Y-%m-%d')
                    except ValueError:
                        # If parsing fails, leave the value as is
                        pass

                elif field_category == 'Currency' and value:
                    # Remove currency symbols and commas
                    value = re.sub(r'[^\d.]', '', value)

            return value

        # Map fields based on Tally mappings
        for tally_key, mapping_data in tally_mappings.items():
            model_field = mapping_data.get("ModelField")
            field_type = mapping_data.get("FieldType")
            field_value = mapping_data.get("FieldValue")

            # Check if tally_key matches Debit Ledger N pattern
            match = debit_ledger_pattern.match(tally_key)

            if match:
                # Get the number N from the tally_key (e.g., Debit Ledger 1 -> N = 1)
                debit_ledger_number = match.group(1)
                amount_key = f"Debit Ledger {debit_ledger_number} Amount"

                # Process Debit Ledger N and Debit Ledger N Amount if FieldType, FieldValue, and ModelField are present
                if field_type and field_value and model_field:
                    
                    if SelectedGST == "state" and model_field == "IGST Total Amt":
                        continue
                    
                    if SelectedGST == "outside state" and model_field in ["CGST Total Amt", "SGST Total Amt"]:
                        continue
                    # Add Debit Ledger N field with FieldValue from tally_mappings
                    mapped_data[f"Debit Ledger {debit_ledger_number}"] = field_value
                    # Add Debit Ledger N Amount with the value from dictFields using the model field
                    mapped_data[amount_key] = process_field(dictFields.get(model_field, ""), model_field)

            else:
                # Process regular fields
                if model_field:
                    value = process_field(dictFields.get(model_field, ""), model_field)
                elif field_type and field_value:
                    # If user has directly provided the value
                    value = field_value
                else:
                    value = None  # If neither model_field nor user-provided value exist

                # Add the mapped field to the mapped_data
                if value is not None:
                    mapped_data[tally_key] = value

        return mapped_data


    @staticmethod
    async def MSFetchMappedDocumentData(iUserID: int, iDocID: int):
        """
        Fetches the mapped document data based on Tally configuration for a given document ID.

        Args:
            iUserID (int): The ID of the user.
            iDocID (int): The ID of the document.

        Returns:
            dict: A dictionary containing the mapped document data.

        Raises:
            HTTPException: If the document is not found or an error occurs.
        """
        from src.Controllers.GPTResponse_controller import CGPTResponseData
        from src.Controllers.Vendor_Controller import CVendorController

        async with AsyncSessionLocal() as db:
            try:
                # Fetch document data
                dictData = await CGPTResponseData.MSGetDocumentById(iUserID=iUserID, iDocId=iDocID)
                dictModelFields = await CVendorController.GetAllReqInvoiceFields(iUserID=iUserID, iModelId=dictData["UploadedDoc"]["ModelId"])
                dictDocExtractedData = dictData.get("DocExtractedData", {})
                dictFields = {k: v for item in dictDocExtractedData.get("Fields", []) for k, v in item.items()}

                # Fetch Tally model configuration
                modelId = dictData.get("UploadedDoc", {}).get("ModelId")
                if not modelId:
                    raise HTTPException(status_code=404, detail="Model ID not found for the document")

                tally_config = await db.execute(select(TallyModelConfig).filter(TallyModelConfig.ModelId == modelId))
                tally_config = tally_config.scalar_one_or_none()

                if not tally_config:
                    raise HTTPException(status_code=404, detail="Tally configuration not found for this document's model")

                tally_mappings = tally_config.Mappings.get('Fields', {})
                extra_fields = tally_config.ExtraFields

                # Preprocess and map the data
                model_fields = dictModelFields.get("ModelData", {}).get("Fields", [])
                preprocessed_data = CTallyController.preprocess_mapped_data(tally_mappings, dictFields, extra_fields, model_fields)

                result = {
                    "DocID": iDocID,
                    "ModelID": modelId,
                    "ModelName": dictData.get("UploadedDoc", {}).get("ModelName", ""),
                    "TallyTemplateID": tally_config.TallyTemplateID,
                    "CompanyName": tally_config.CompanyName,
                    "GSTIN": tally_config.GSTIN,
                    "MappedData": preprocessed_data
                }

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully fetched and preprocessed mapped document data for document {iDocID}.")
                return result

            except HTTPException as http_exc:
                raise http_exc
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"An error occurred while fetching mapped document data for document {iDocID}: {str(e)}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while processing your request")

    @staticmethod
    async def MSGetTallyTemplateById(iUserID: int, iTemplateId: int):
        """
        Fetches a Tally template based on the given template ID.

        Args:
            iUserID (int): The ID of the user requesting the template.
            iTemplateId (int): The ID of the template to fetch.

        Returns:
            dict: A dictionary containing the Tally template details.

        Raises:
            HTTPException: If the template is not found or an error occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(TallyTemplate).filter(TallyTemplate.Id == iTemplateId)
                result = await db.execute(query)
                template = result.scalar_one_or_none()

                if not template:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Tally template with ID {iTemplateId} not found.")
                    raise HTTPException(status_code=404, detail="Tally template not found")

                template_dict = {
                    "Id": template.Id,
                    "Name": template.Name,
                    "TemplateKey": template.TemplateKey,
                    "CreatedDateTime": template.CreatedDateTime.isoformat() if template.CreatedDateTime else None,
                    "ModifiedDateTime": template.ModifiedDateTime.isoformat() if template.ModifiedDateTime else None,
                    # Add any other fields from your TallyTemplate model that you want to include
                }

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully fetched Tally template with ID {iTemplateId}.")
                return  template_dict

            except HTTPException as http_exc:
                raise http_exc
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"An error occurred while fetching Tally template with ID {iTemplateId}: {str(e)}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while processing your request")


    @staticmethod
    async def MSSendToTally(iUserID: int, docIds: List[int]):
        """
        Fetches mapped data for the given document IDs and sends it to the Tally API.

        Args:
            iUserID (int): The ID of the user.
            docIds (List[int]): List of document IDs to process.

        Returns:
            dict: A dictionary containing the Tally API response and processed document IDs.

        Raises:
            HTTPException: If an error occurs during the process.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Fetch TallyUserConfig for auth key
                tally_user_config = await db.execute(select(TallyUserConfig).filter(TallyUserConfig.UserID == iUserID))
                tally_user_config = tally_user_config.scalar_one_or_none()
                if not tally_user_config:
                    raise HTTPException(status_code=404, detail="Tally user configuration not found")

                tally_body = {"body": []}
                processed_doc_ids = []

                for doc_id in docIds:
                    try:
                        # Fetch mapped data and model config
                        mapped_data = await CTallyController.MSFetchMappedDocumentData(iUserID, doc_id)
                        model_config = await CTallyController.MSGetModelConfig(iUserID, mapped_data["ModelID"])
                        model_config["config"]["CompanyName"] = "Temp"
                        iTemplateKey = model_config["config"]["TallyTemplateID"]
                        dictTemplateData = await CTallyController.MSGetTallyTemplateById(iUserID=iUserID, iTemplateId=iTemplateKey)

                        # Prepare Tally API header
                        tally_api_header = {
                            "X-Auth-Key": tally_user_config.AuthKey,
                            "Template-Key": dictTemplateData["TemplateKey"],
                            "CompanyName": model_config["config"]["CompanyName"],
                            "AddAutoMaster": "0",
                            "Automasterids": "1,2,3",
                            "version": "3",      # ! Need to take from user
                            'Content-Type': 'application/json'
                        }

                        tally_body["body"].append(mapped_data["MappedData"])
                        processed_doc_ids.append(doc_id)

                        # Prepare the complete request data (header + body)
                        complete_request_data = {
                            "header": tally_api_header,
                            "body": [mapped_data["MappedData"]]
                        }
                        
                        # Store request data
                        await CTallyController.MSSetTallyDocRecords(
                            doc_id=doc_id,
                            tally_api_req=complete_request_data,
                            tally_status="Processing",
                            req_date_time=datetime.now()
                        )
                        
                    except HTTPException as e:
                        await CLogController.MSWriteLog(iUserID, "Error", f"Failed to fetch mapped data for document {doc_id}: {str(e)}")
                        # Continue processing other documents

                if not tally_body["body"]:
                    raise HTTPException(status_code=400, detail="No valid documents to process")

                # Send request to Tally API
                tally_api_url = "https://api.excel2tally.in/api/User/PurchaseWithoutInventory"  # Replace with actual Tally API endpoint
                async with aiohttp.ClientSession() as session:
                    async with session.post(tally_api_url, headers=tally_api_header, json=tally_body) as response:
                        if response.status == 200:
                            try:
                                tally_response = await response.json()
                            except aiohttp.ContentTypeError:
                                tally_response = await response.text()
                        else:
                            tally_response = await response.text()

                if response.status == 200:
                    # Update Tally status for processed documents
                    for doc_id in processed_doc_ids:
                        # await CDocumentData.MSUpdateDocumentTallyStatus(iUserID, doc_id, "Success")    #! For updating tally status in uploaded docs table, make sure to update the uploaded doc tables tallystatus enum to our latest tally status enum
                        
                        # Store response data
                        await CTallyController.MSSetTallyDocRecords(
                            doc_id=doc_id,
                            tally_api_resp=tally_response,
                            tally_status="Success",
                            resp_date_time=datetime.now()
                        )
                    
                    await CLogController.MSWriteLog(iUserID, "Info", f"Successfully sent data to Tally for documents: {processed_doc_ids}")
                    return {
                        "message": "Data successfully sent to Tally",
                        "tally_response": tally_response,
                        "processed_documents": processed_doc_ids
                    }
                else:
                    # Store error response
                    for doc_id in processed_doc_ids:
                        await CTallyController.MSSetTallyDocRecords(
                            doc_id=doc_id,
                            tally_api_resp={"error": tally_response},
                            tally_status="Error",
                            resp_date_time=datetime.now()
                        )
                    raise HTTPException(status_code=response.status, detail=f"Tally API returned an error: {tally_response}")

            except HTTPException as http_exc:
                raise http_exc
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"An error occurred while sending data to Tally: {str(e)}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while processing your request")
    
    @staticmethod
    async def MSBIsDuplicateXML(iUserID:int, iDocumentNumber:int = None, iDocID: int = None, objVoucherType:Optional[VoucherType] = None ) -> bool:
        """
            Purpose : With The UserID and DocumentId check the Recorde is duplicate or not
            
            Inputs:
                - iUserID(int) :  UserID of Document
                - iDocumentNumber(int) :  Invoice number of Currant Document

            Output:
                bool : Return True If Record is Duplicate 
        """
        if isinstance(objVoucherType, Enum):
            objVoucherType = objVoucherType.value
        dictMatchedRecords = None
        if iDocumentNumber is None or iDocumentNumber == "":
            dictMatchedRecords = await CTallyController.MSFetchTallyDocRecords(
                userid=iUserID,
                doc_id=iDocID, 
                voucher_type=objVoucherType
            )
        else:
            dictMatchedRecords = await CTallyController.MSFetchTallyDocRecords(
                userid=iUserID,
                invoice_no=iDocumentNumber,
                voucher_type=objVoucherType
            )
        if dictMatchedRecords:
            # Iterate through all matched records and check if any contain 'Success' status
            for record in dictMatchedRecords:
                avtally_xml_status = record.get("AVTallyXMLStatus")

                tally_status = record.get("TallyStatus")

                iDocId = record.get("DocID")
                if (tally_status and tally_status.value == "Success") or ((avtally_xml_status and avtally_xml_status.value == "Success") or (avtally_xml_status and avtally_xml_status.value == "PartialSuccess") ):
                    if not iDocId:
                        return True
                    
                    objDocumentData = await CDocumentData.MSGetDocMetaDataByDocId(userid=iUserID,doc_id=iDocId,strVoucherType = objVoucherType)
                    if objDocumentData.get("UserId") == iUserID:
                        return True
                    
        return False
    
    @staticmethod
    async def MSValidateUserLimit(iUserId, strDate,iCurrReqPage, iPageLimit=50, iReqLimit=100):

        # TODO: For Purchase Order, GRN , BankStatement Limits Defined
        async with AsyncSessionLocal() as db:
            try:
            
                try:
                    date = datetime.strptime(strDate, '%Y-%m-%d').date()
                except ValueError:
                    raise ValueError("Date format should be 'yyyy-mm-dd'")
                
                # Prepare date filters
                start_of_day = datetime.combine(date, datetime.min.time())
                end_of_day = datetime.combine(date, datetime.max.time())

                # Build the query using async syntax
                stmtCheckPageLimit = select(
                    func.coalesce(func.sum(UploadedDoc.PageCount), 0).label('TotalPages')
                ).select_from(UploadedDoc).join(
                    AVRequestDetail, UploadedDoc.DocId == AVRequestDetail.DOC_UID
                ).where(
                    UploadedDoc.UserId == iUserId,
                    UploadedDoc.UploadedDateTime >= start_of_day,
                    UploadedDoc.UploadedDateTime <= end_of_day,
                    or_(
                        AVRequestDetail.AVXMLGeneratedStatus in [TallyStatusEnum.Success, TallyStatusEnum.PartialSuccess, TallyStatusEnum.ValidationError]
                    )
                )

                # Execute the query asynchronously
                resultCheckPageLimit = await db.execute(stmtCheckPageLimit)
                iPageProcess = resultCheckPageLimit.scalar() or 0
                
                            
                # Build the query
                stmtCheckReqCount = select(
                    func.count(AVRequestDetail.ID).label('ProcessCount')
                ).where(
                    AVRequestDetail.User_UID == iUserId,
                    AVRequestDetail.CReqGeneratedTimeAt >= start_of_day,
                    AVRequestDetail.CReqGeneratedTimeAt <= end_of_day
                )

                # Execute the query asynchronously
                resultCheckReqCount = await db.execute(stmtCheckReqCount)
                iReqProcess = resultCheckReqCount.scalar() or 0
                
                if (iPageProcess + iCurrReqPage) < iPageLimit:
                    bPageAllowProcess = True
                else:
                    bPageAllowProcess = False
                    
                if iReqProcess < iReqLimit:
                    bReqAllow = True
                else:
                    bReqAllow = False
                
                return {
                    "iUserId":iUserId,
                    "iPageProcess":iPageProcess,
                    "strDate": strDate,
                    "bPageAllowProcess":bPageAllowProcess,
                    "iReqProcess":iReqProcess,
                    "bReqAllow":bReqAllow, 
                    "iCurrReqPage": iCurrReqPage
                }
            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "ERROR", f"USerID : {iUserId} Error while counting the page and request - limit per user")

# DISCARDED CLASS , TABLE
# class CTDLProcessingController:

#     @staticmethod
#     async def MSAddTDLDocProcessingRecord(
#         user_id: int = None,
#         attachments: list = None,
#         status: str = None,
#         log_message: str = None,
#         retry_count: int = 0,
#         strClientReqID: str = None,
#         ResponseAt: datetime = None
#     ):
#         """
#         Purpose : This method creates or updates a TDL processing record.

#         Inputs  :
#             - user_id: int (The ID of the user processing the record.)
#             - attachments: dict (JSON data of processed attachment names.)
#             - status: str (Processing status.)
#             - log_message: str (Log message for tracking.)
#             - retry_count: int (Retry count, default is 0.)
#             - strClientReqID: str (Client request ID to track requests.)
#             - ResponseAt: datetime (Response time of processing.)

#         Output  : Returns the created/updated record as a dictionary.
#         """
#         try:
#             async with AsyncSessionLocal() as session:
#                 # Create a new record
#                 record = TDLProcessingRecords(
#                     UserId=user_id,
#                     attachments=json.dumps(attachments) if attachments else '[]',
#                     status=status,
#                     LogMessage=log_message,
#                     retry_count=retry_count,
#                     REQID=strClientReqID,
#                     ResponseAt=ResponseAt
#                 )
#                 session.add(record)
#                 await session.commit()
#                 return {
#                     "Id": record.Id,
#                     "UserId": record.UserId,
#                     "attachments": json.loads(record.attachments),
#                     "status": record.status,
#                     "LogMessage": record.LogMessage,
#                     "retry_count": record.retry_count,
#                     "REQID": record.REQID,
#                     "ResponseAt": record.ResponseAt,
#                     "created_at": record.created_at,
#                     "updated_at": record.updated_at
#                 }
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"An error occurred while processing the record: {str(e)}")

#     @staticmethod
#     async def MSUpdateTDLDocProcessingRecord(
#         user_id: int, strClientReqID: str,
#         status: str = None, log_message: str = None,
#         retry_count: int = None, ResponseAt: datetime = None,
#         attachments = None
#     ):
#         """
#         Purpose : This method updates an existing TDL processing record.

#         Inputs  :
#             - user_id: int (User ID of the processing record.)
#             - strClientReqID: str (Client request ID to locate the record.)
#             - status: str (Updated processing status, must match TDLProcessingRecordStatusEnum.)
#             - log_message: str (Updated log message.)
#             - retry_count: int (Updated retry count.)
#             - ResponseAt: datetime (Updated response time.)
#         """
#         try:
#             async with AsyncSessionLocal() as session:
#                 record = await session.execute(
#                     select(TDLProcessingRecords)
#                     .where(TDLProcessingRecords.UserId == user_id)
#                     .where(TDLProcessingRecords.REQID == strClientReqID)
#                 )
#                 record = record.scalar_one_or_none()
#                 if not record:
#                     raise HTTPException(status_code=404, detail="Record not found")
                
#                 if status:
#                     try:
#                         record.status = TDLProcessingRecordStatusEnum[status].value
#                     except KeyError:
#                         raise HTTPException(status_code=400, detail=f"Invalid status value: {status}. Must be one of {[e.name for e in TDLProcessingRecordStatusEnum]}")
                
#                 if log_message:
#                     record.LogMessage = log_message
#                 if retry_count is not None:
#                     record.retry_count = retry_count
#                 if ResponseAt:
#                     record.ResponseAt = ResponseAt
#                 if attachments:
#                     record.attachments = json.dumps(attachments) if isinstance(attachments, (list, dict)) else attachments
#                 record.updated_at = datetime.utcnow()
                
#                 await session.commit()
#                 return {
#                     "Id": record.Id,
#                     "UserId": record.UserId,
#                     "attachments": json.loads(record.attachments) if record.attachments else None,
#                     "status": record.status,
#                     "LogMessage": record.LogMessage,
#                     "retry_count": record.retry_count,
#                     "REQID": record.REQID,
#                     "ResponseAt": record.ResponseAt,
#                     "created_at": record.created_at,
#                     "updated_at": record.updated_at
#                 }
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"An error occurred while updating the record: {str(e)}")

#     @staticmethod
#     async def MSGetTDLDocProcessingRecord(user_id: int, strClientReqID: str):
#         """
#         Purpose : This method retrieves an existing TDL processing record.

#         Inputs  :
#             - user_id: int (User ID of the processing record.)
#             - strClientReqID: str (Client request ID to locate the record.)

#         Returns :
#             - A dictionary containing the record's details if found, otherwise raises an HTTPException.
#         """
#         try:
#             async with AsyncSessionLocal() as session:
#                 record = await session.execute(
#                     select(TDLProcessingRecords)
#                     .where(TDLProcessingRecords.UserId == user_id)
#                     .where(TDLProcessingRecords.REQID == strClientReqID)
#                 )
#                 record = record.scalar_one_or_none()
#                 if not record:
#                     raise HTTPException(status_code=404, detail="Record not found")
#                 return {
#                     "Id": record.Id,
#                     "UserId": record.UserId,
#                     "attachments": json.loads(record.attachments) if record.attachments else None,
#                     "status": record.status,
#                     "LogMessage": record.LogMessage,
#                     "retry_count": record.retry_count,
#                     "REQID": record.REQID,
#                     "ResponseAt": record.ResponseAt,
#                     "created_at": record.created_at,
#                     "updated_at": record.updated_at
#                 }
#         except Exception as e:
#             raise HTTPException(status_code=500, detail=f"An error occurred while retrieving the record: {str(e)}")

async def main():
    lsPONumbers = ['ABLK-1', 'ABLK-15-2', 'ABLK-248', 'ABLK-3', 'ABLK-378-249', 'ABLK-4', 'ABLK-5', 'ABLK-6', 'ABLK-7', 'BPMV-1-1', 'BPMV-10', 'BPMV-186', 'BPMV-375-183', 'BPMV-375-184', 'BPMV-378-185', 'BPMV-4', 'BPMV-5-2', 'BPMV-5-3', 'BPMV-5-5', 'BPMV-6', 'BPMV-7', 'BPMV-8', 'BPMV-9', 'CSLV-1', 'CSLV-10', 'CSLV-11-11', 'CSLV-12', 'CSLV-13', 'CSLV-15-14', 'CSLV-15-15', 'CSLV-16', 'CSLV-17', 'CSLV-18', 'CSLV-19', 'CSLV-2', 'CSLV-20', 'CSLV-21-21', 'CSLV-22-22', 'CSLV-23', 'CSLV-24', 'CSLV-25', 'CSLV-26', 'CSLV-3', 'CSLV-375', 'CSLV-376', 'CSLV-377', 'CSLV-378-378', 'CSLV-379', 'CSLV-4', 'CSLV-5', 'CSLV-7-6', 'CSLV-7-7', 'CSLV-8', 'CSLV-9-9', 'EBRM-1', 'EBRM-11', 'EBRM-12-10', 'EBRM-12-12', 'EBRM-13', 'EBRM-14', 'EBRM-15', 'EBRM-16', 'EBRM-199', 'EBRM-2', 'EBRM-200', 'EBRM-3', 'EBRM-4', 'EBRM-5', 'EBRM-6', 'EBRM-7', 'EBRM-8', 'EBRM-9', 'KILP-1', 'KILP-2', 'M-GENO-1', 'ODLB-1', 'ODLB-2', 'PGTV-1', 'PGTV-11-2', 'PGTV-3', 'PGTV-4', 'SF-GODWON-378-14', 'SF-GODWON-4-1', 'SF-GSMPGC-1', 'SF-GSMPGC-2', 'SF-JCADP-1', 'SF-JCADP-2', 'SF-JCADP-3', 'SF-MILPD-1', 'TFAB-1', 'TFAB-10', 'TFAB-12', 'TFAB-12-4', 'TFAB-18-11', 'TFAB-2', 'TFAB-3', 'TFAB-5', 'TFAB-6', 'TFAB-7', 'TFAB-8', 'TFAB-9', 'WELB-1', 'WELB-11-2', 'WELB-15-3', 'WELB-26-6', 'WELB-4', 'WELB-5']
    lsGrnNumbers = ['CSLV-29', 'EBRM-21', 'CSLV-672', 'EBRM-55', 'BPMV-23', 'KILP-9', 'KILP-8', 'TFAB-37', 'EBRM-54', 'BPMV-5', 'BPMV-41', 'WELB-256', 'EBRM-57', 'BPMV-27', 'BPMV-39', 'ABLK-765', 'CSLV-18', 'GENO-3', 'TFAB-58', 'EBRM-39', 'TFAB-36', 'KILP-7', 'TFAB-103', 'BPMV-2', 'TFAB-89', 'KILP-15', 'TFAB-2', 'KILP-3', 'ABLK-18', 'CSLV-30', 'KILP-2', 'ABLK-8', 'TFAB-88', 'ABLK-10', 'WELB-8', 'KILP-13', 'PGTV-5', 'TFAB-45', 'EBRM-31', 'BPMV-32', 'GENO-4', 'TFAB-41', 'TFAB-1', 'BPMV-28', 'BPMV-4', 'WELB-7', 'TFAB-43', 'BPMV-21', 'TFAB-95', 'ABLK-19', 'TFAB-79', 'CSLV-16', 'SF-GODWON-1', 'GENO-13', 'EBRM-52', 'ODLB-14', 'PGTV-2', 'ABLK-23', 'GENO-583', 'CSLV-23', 'SF-GODWON-2', 'TFAB-100', 'ABLK-13', 'EBRM-29', 'EBRM-11', 'TFAB-56', 'CSLV-24', 'ODLB-6', 'BPMV-26', 'BPMV-15', 'TFAB-16', 'ABLK-3', 'WELB-5', 'TFAB-33', 'TFAB-93', 'PGTV-1', 'KILP-12', 'EBRM-10', 'TFAB-29', 'ABLK-24', 'EBRM-8', 'GENO-21', 'EBRM-20', 'ODLB-623', 'ODLB-8', 'ABLK-764', 'TFAB-23', 'M-GENO-1', 'ABLK-12', 'TFAB-98', 'EBRM-50', 'EBRM-7', 'EBRM-47', 'ODLB-625', 'CSLV-14', 'WELB-25', 'WELB-14', 'ABLK-759', 'TFAB-53', 'ODLB-624', 'KILP-18', 'BPMV-11', 'BPMV-17', 'KILP-11', 'ABLK-16', 'BPMV-18', 'TFAB-51', 'TFAB-97', 'WELB-13', 'BPMV-7', 'WELB-9', 'WELB-22', 'TFAB-80', 'WELB-11', 'TFAB-75', 'TFAB-17', 'ABLK-763', 'TFAB-42', 'BPMV-12', 'EBRM-23', 'KILP-17', 'WELB-259', 'SF-GODWON-13', 'TFAB-76', 'BPMV-24', 'EBRM-19', 'TFAB-67', 'ABLK-25', 'TFAB-85', 'ABLK-14', 'EBRM-38', 'TFAB-96', 'TFAB-10', 'SF-ABLK-3', 'BPMV-1', 'CSLV-10', 'TFAB-27', 'CSLV-9', 'EBRM-16', 'EBRM-28', 'EBRM-622', 'GENO-20', 'BPMV-35', 'EBRM-22', 'PGTV-663', 'GENO-8', 'TFAB-38', 'WELB-2', 'GENO-15', 'ABLK-761', 'CSLV-671', 'ABLK-1', 'CSLV-25', 'EBRM-46', 'CSLV-21', 'EBRM-37', 'WELB-17', 'ABLK-5', 'PGTV-3', 'EBRM-5', 'WELB-27', 'CSLV-6', 'GENO-24', 'TFAB-55', 'TFAB-28', 'PGTV-8', 'CSLV-4', 'EBRM-3', 'WELB-260', 'BPMV-43', 'TFAB-21', 'WELB-20', 'TFAB-60', 'EBRM-33', 'TFAB-3', 'EBRM-624', 'EBRM-24', 'GENO-9', 'TFAB-66', 'KILP-5', 'EBRM-49', 'TFAB-19', 'CSLV-679', 'GENO-7', 'TFAB-52', 'CSLV-12', 'TFAB-5', 'TFAB-77', 'BPMV-31', 'BPMV-25', 'GENO-18', 'TFAB-99', 'TFAB-82', 'BPMV-20', 'ABLK-15', 'TFAB-101', 'BPMV-29', 'TFAB-71', 'BPMV-34', 'EBRM-14', 'EBRM-623', 'EBRM-34', 'ABLK-760', 'TFAB-35', 'TFAB-47', 'TFAB-8', 'ABLK-9', 'ODLB-2', 'BPMV-33', 'GENO-11', 'BPMV-16', 'TFAB-22', 'TFAB-18', 'EBRM-27', 'EBRM-4', 'GENO-6', 'CSLV-680', 'EBRM-56', 'TFAB-62', 'BPMV-8', 'BPMV-19', 'GENO-19', 'TFAB-6', 'ABLK-7', 'CSLV-17', 'BPMV-42', 'ODLB-626', 'TFAB-31', 'CSLV-36', 'TFAB-20', 'CSLV-8', 'EBRM-12', 'EBRM-44', 'EBRM-13', 'ABLK-767', 'EBRM-6', 'WELB-15', 'CSLV-3', 'PGTV-6', 'EBRM-60', 'BPMV-30', 'TFAB-436', 'BPMV-22', 'TFAB-59', 'WELB-23', 'PGTV-9', 'EBRM-48', 'KILP-572', 'CSLV-27', 'EBRM-43', 'KILP-571', 'TFAB-61', 'TFAB-63', 'EBRM-25', 'EBRM-42', 'TFAB-83', 'WELB-12', 'CSLV-15', 'TFAB-102', 'ABLK-762', 'TFAB-87', 'WELB-257', 'BPMV-10', 'TFAB-70', 'TFAB-54', 'TFAB-65', 'TFAB-7', 'TFAB-84', 'CSLV-676', 'EBRM-18', 'ODLB-3', 'EBRM-53', 'WELB-10', 'TFAB-81', 'WELB-21', 'BPMV-3', 'TFAB-24', 'TFAB-12', 'GENO-22', 'BPMV-14', 'EBRM-51', 'ABLK-4', 'TFAB-44', 'GENO-10', 'WELB-3', 'ABLK-2', 'TFAB-49', 'GENO-12', 'TFAB-105', 'M-GENO-2', 'WELB-1', 'EBRM-62', 'TFAB-73', 'EBRM-35', 'TFAB-32', 'TFAB-13', 'TFAB-30', 'GENO-2', 'TFAB-68', 'EBRM-9', 'ODLB-10', 'BPMV-40', 'PGTV-4', 'TFAB-9', 'TFAB-86', 'WELB-16', 'KILP-1', 'TFAB-15', 'Total', 'CSLV-2', 'ODLB-7', 'ABLK-17', 'EBRM-15', 'M-GENO-88', 'TFAB-14', 'TFAB-25', 'KILP-14', 'GENO-584', 'BPMV-36', 'TFAB-90', 'TFAB-46', 'KILP-4', 'TFAB-34', 'CSLV-673', 'CSLV-677', 'CSLV-20', 'CSLV-5', 'CSLV-22', 'TFAB-106', 'ODLB-11', 'WELB-24', 'TFAB-57', 'TFAB-4', 'TFAB-26', 'TFAB-74', 'CSLV-7', 'GENO-16', 'BPMV-38', 'EBRM-36', 'TFAB-94', 'TFAB-107', 'CSLV-19', 'ODLB-5', 'GENO-14', 'CSLV-681', 'KILP-570', 'ABLK-22', 'TFAB-48', 'TFAB-39', 'CSLV-26', 'CSLV-31', 'ODLB-12', 'ZFLB-692', 'TFAB-78', 'ABLK-21', 'TFAB-40', 'KILP-10', 'ODLB-1', 'TFAB-69', 'CSLV-28', 'EBRM-32', 'EBRM-30', 'BPMV-37', 'WELB-6', 'WELB-19', 'WELB-4', 'GENO-5', 'WELB-26', 'TFAB-92', 'EBRM-59', 'PGTV-7', 'EBRM-2', 'BPMV-13', 'ABLK-20', 'TFAB-91', 'CSLV-35', 'TFAB-64', 'ABLK-6', 'BPMV-9', 'EBRM-45', 'GENO-17', 'CSLV-11', 'EBRM-58', 'WELB-258', 'WELB-18', 'ODLB-9', 'GENO-23', 'TFAB-50', 'TFAB-11', 'BPMV-6', 'EBRM-17', 'KILP-6', 'ODLB-13', 'TFAB-104', 'TFAB-72', 'ABLK-766', 'ODLB-4', 'GENO-1', 'EBRM-26', 'CSLV-1', 'KILP-16', 'ABLK-11', 'CSLV-32', 'CSLV-13']

    for poNumber in lsPONumbers:
        poNumber = poNumber.strip()
        await CTallyController.MSSetTallyDocRecords(
            user_id=4,
            doc_id=None,
            invoice_no=poNumber,
            voucher_type=VoucherType.PURCHASE_ORDER,
            av_tally_xml_status="Success"
        )

    for GRNNumber in lsGrnNumbers:
        GRNNumber = GRNNumber.strip()
        await CTallyController.MSSetTallyDocRecords(
            user_id=4,
            doc_id=None,
            invoice_no=GRNNumber,
            voucher_type=VoucherType.RECEIPT_NOTE,
            av_tally_xml_status="Success"
        )
    
    
import asyncio

if __name__ == "__main__":
    
    
    asyncio.run(main())
        
    # asyncio.run(CTallyController.MSStoreXMLAsXLSX(4, r"H:\AI Data\TallyExports\19_02_2025\FilteredStockItemExport_19_02_2025_14_20_21.xml"))