# src/Controllers/CStaticTallyAPI.py

import os
import sys
import re
from src.Controllers.auth_controller import <PERSON><PERSON><PERSON><PERSON>roller
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from src.utilities.DBHelper import (
    CPromptTable,
    CUserTable,
    CExtractedValidationTable,
    CDocumentTable,
    CExtractionTable,
)
from fastapi import HTTPException

class CStaticTallyAPI:
    def __init__(self, user_id, doc_id):
        self.iUserId = user_id
        self.DocId = doc_id
        self.TallyUserConfigObj = None
        self.UploadDocObj = None

    async def initialize(self):
        # Fetch Tally User Configuration
        self.TallyUserConfigObj = await CTallyController.MSGetTallyUserConfig(iUserID=self.iUserId)
        
        # Validate Tally Enablement
        if not self.TallyUserConfigObj.get("TallyEnable"):
            raise HTTPException(
                status_code=400, 
                detail="Tally is not enabled for you. Please contact the developer team."
            )
        
        # Validate Auth Key
        auth_key = self.TallyUserConfigObj.get("AuthKey")
        if not auth_key:
            raise HTTPException(
                status_code=400, 
                detail="Validation failed for your Tally Auth Key."
            )
        
        # Get Upload Document Attributes
        self.UploadDocObj = await CDocumentData.MSGetDocById(
            user_id=self.iUserId, 
            docId=self.DocId,
            isBinaryDataRequired=False
        )
        
        # Validate Document Status
        status = self.UploadDocObj.get("Status", "").lower()
        if status not in ["tobeapproved", "approved"]:
            raise HTTPException(
                status_code=400, 
                detail="Unable to extract document."
            )
        
        # Validate Model Name
        model_name = self.UploadDocObj.get("ModelName", "").lower()
        if model_name not in ["simpolo"]:
            raise HTTPException(
                status_code=400, 
                detail="Unable to find your model name on our server."
            )
