{"0": {"model_name": "Invoice", "family_name": "Demo Finance", "doc_path": "Data/doc_initialize/Demo-Invoice.pdf", "doc_name": "Demo-Invoice.pdf", "doc_page_count": 2, "doc_type": "PDF", "is_scanned": false, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9sqRvb6pzt5MJ8htWMq5fBZ6tu3jN", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 6638, "prompt_tokens": 6164, "completion_tokens": 474}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"VendorName\": [\"meyer\", 1, 266, 73, 581, 154], \"InvoiceNumber\": [\"11428979\", 1, 1568, 300, 1681, 321], \"InvoiceDate\": [\"06/04/2024\", 1, 1537, 333, 1676, 356], \"DueDate\": [\"07/04/2024\", 2, 1454, 526, 1585, 548], \"TotalInvoiceAmountWithTax\": [\"$711.61\", 1, 1428, 1197, 1513, 1217], \"TotalTax\": [\"$29.61\", 1, 1429, 1142, 1502, 1161], \"ShipToOrServiceToLocation\": [\"HEALTHPLUS MEDICAL GROUP, 789 OAK LANE, SUITE 200, METROPOLIS, NY, 54321\", 1, 1306, 456, 1628, 527]}, \"Tables\": {\"LineItemTable\": [{\"SerialNumber\": \"1\", \"Description\": \"THERA-BAND LATEX-FREE PROFESSION\", \"Quantity\": \"1\", \"Rate\": \"$415.3000\", \"Amount\": \"$415.30\"}, {\"SerialNumber\": \"2\", \"Description\": \"THERA-BAND LATEX-FREE PROFESSION\", \"Quantity\": \"1\", \"Rate\": \"$99.3000\", \"Amount\": \"$99.30\"}, {\"SerialNumber\": \"3\", \"Description\": \"BODY SPORT HAND THERAPY PUTTY, G\", \"Quantity\": \"10\", \"Rate\": \"$83.3700\", \"Amount\": \"$83.370\"}, {\"SerialNumber\": \"4\", \"Description\": \"BODY SPORT HAND THERAPY PUTTY, R.\", \"Quantity\": \"10\", \"Rate\": \"$83.3700\", \"Amount\": \"$83.370\"}]}, \"meta_data\": {\"page_width\": 1758.0, \"page_height\": 2275.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_4e2b2da518"}, "doc_verified_data": {"Fields": {"Vendor Name": ["MeyerPT", 2, 93, 181, 132, 192], "Invoice Number": ["11428979", 1, 539, 97, 583, 108], "Invoice Date": ["06/04/2024", 1, 529, 108, 581, 119], "Due Date": ["07/04/2024", 2, 502, 177, 548, 188], "Total Invoice Amount With Tax": ["$711.61", 1, 498, 411, 527, 420], "Total Tax": ["$29.61", 1, 498, 391, 523, 401], "Ship To Or Service To Location": ["HEALTHPLUS MEDICAL GROUP, 789 OAK LANE, SUITE 200, METROPOLIS, NY, 54321", 1, 449, 153, 564, 163]}, "Tables": {"LineItemTable": [{"Serial Number": "1", "Description": "THERA-BAND LATEX-FREE PROFESSION", "Quantity": "1", "Rate": "$415.3000", "Amount": "$415.30"}, {"Serial Number": "2", "Description": "THERA-BAND LATEX-FREE PROFESSION", "Quantity": "1", "Rate": "$99.3000", "Amount": "$99.30"}, {"Serial Number": "3", "Description": "BODY SPORT HAND THERAPY PUTTY, G", "Quantity": "10", "Rate": "$83.3700", "Amount": "$83.370"}, {"Serial Number": "4", "Description": "BODY SPORT HAND THERAPY PUTTY, R", "Quantity": "10", "Rate": "$83.3700", "Amount": "$83.370"}]}, "meta_data": {"page_width": 612.0, "page_height": 792.0}}}, "1": {"model_name": "Account Statement", "family_name": "Demo Finance", "doc_path": "Data/doc_initialize/Demo-AccountSatement.pdf", "doc_name": "Demo-AccountSatement.pdf", "doc_page_count": 2, "doc_type": "PDF", "is_scanned": false, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9mIShPlx17xzHJp6TEIM2f7o2DNxI", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 7806, "prompt_tokens": 5883, "completion_tokens": 1923}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"BankName\": [\"Pacific Valley Bank\", 1, 11, 76, 124, 85], \"BranchName\": [\"P.O. Box 3648 Salinas, CA 93912\", 1, 11, 93, 163, 112], \"AccountHolder\": [\"PINNACLE MEDICAL GROUP INC\", 1, 14, 170, 159, 211], \"AccountNumber\": [\"9645\", 1, 357, 321, 412, 330], \"StatementPeriod\": [\"11/01/2023-11/30/2023\", 1, 317, 48, 536, 62], \"CurrencyUnit\": [\"$USD\", 2, 520, 255, 579, 264], \"OpeningBalance\": [\"$4,969.61\", 1, 298, 412, 338, 421], \"ClosingBalance\": [\"$4,813.79\", 1, 298, 446, 338, 455], \"TotalCredits\": [\"$15,953.33\", 1, 293, 423, 338, 432]}, \"Tables\": {\"Transactions\": [{\"Date\": \"11/01/2023\", \"Description\": \"PNC MERCHANT DEPOSIT ************\", \"Amount\": \"$64.48\"}, {\"Date\": \"11/02/2023\", \"Description\": \"HCCLAIMPMT VPAY E027517091 TRN* 1* ********** **********\", \"Amount\": \"$13.99\"}, {\"Date\": \"11/06/2023\", \"Description\": \"HCCLAIMPMT VPAY E027658173 TRN* 1* ********* **********\", \"Amount\": \"$153.93\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027673380 TRN* 1* ********* **********\", \"Amount\": \"$32.02\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027676273 TRN* 1* ********* **********\", \"Amount\": \"$90.00\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027676273 TRN* 1* 01893469* **********\", \"Amount\": \"$90.00\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027678124 TRN* 1* 01892953* **********\", \"Amount\": \"$90.00\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027678652 TRN* 1* 01894510* **********\", \"Amount\": \"$90.00\"}, {\"Date\": \"11/07/2023\", \"Description\": \"HCCLAIMPMT VPAY E027679547 TRN* 1* 01894835* **********\", \"Amount\": \"$90.00\"}, {\"Date\": \"11/08/2023\", \"Description\": \"HCCLAIMPMT VPAY E027700793 TRN* 1* 139119330* 1362685608\", \"Amount\": \"$180.00\"}, {\"Date\": \"11/08/2023\", \"Description\": \"HCCLAIMPMT VPAY E027711727 TRN* 1* 138041938* 1362685608\", \"Amount\": \"$180.00\"}, {\"Date\": \"11/09/2023\", \"Description\": \"HCCLAIMPMT VPAY E027747760 TRN* 1* 138387751* 1362685608\", \"Amount\": \"$180.00\"}, {\"Date\": \"11/09/2023\", \"Description\": \"HCCLAIMPMT VPAY E027764337 TRN* 1* 139120001* 1362685608\", \"Amount\": \"$270.00\"}, {\"Date\": \"11/10/2023\", \"Description\": \"MERCHANT BNKCD DEPOSIT 359200119881\", \"Amount\": \"$270.00\"}, {\"Date\": \"11/13/2023\", \"Description\": \"MERCHANT BNKCD DEPOSIT 359200119881\", \"Amount\": \"$270.00\"}, {\"Date\": \"11/13/2023\", \"Description\": \"MERCHANT BNKCD DEPOSIT 359200119881\", \"Amount\": \"$632.15\"}, {\"Date\": \"11/13/2023\", \"Description\": \"PNC MERCHANT DEPOSIT ************\", \"Amount\": \"$77.44\"}, {\"Date\": \"11/13/2023\", \"Description\": \"MERCHANT BNKCD DEPOSIT 359200119881\", \"Amount\": \"$294.08\"}, {\"Date\": \"11/02/2023\", \"Description\": \"VPAY F027527770 V75240522 752405220\", \"Amount\": \"$39.50\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359200119881\", \"Amount\": \"$2.59\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD INTERCHNG 359200119881\", \"Amount\": \"$12.85\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359161480884\", \"Amount\": \"$27.95\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359161483888\", \"Amount\": \"$27.95\"}, {\"Date\": \"11/13/2023\", \"Description\": \"PNC MERCHANT DEPOSIT ************\", \"Amount\": \"$77.44\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD FEE 359200119881\", \"Amount\": \"$35.56\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359161481882\", \"Amount\": \"$44.95\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359161482880\", \"Amount\": \"$44.95\"}, {\"Date\": \"11/03/2023\", \"Description\": \"MERCHANT BNKCD DISCOUNT 359161484886\", \"Amount\": \"$44.95\"}, {\"Date\": \"11/06/2023\", \"Description\": \"PNC MERCHANT DISCOUNT ************\", \"Amount\": \"$40.39\"}, {\"Date\": \"11/06/2023\", \"Description\": \"PGANDE WEB ONLINE 69993630101923\", \"Amount\": \"$380.02\"}, {\"Date\": \"11/13/2023\", \"Description\": \"BK OF AMER VI/MC ONLINE PMT CKF242250070POS\", \"Amount\": \"$100.00\"}, {\"Date\": \"11/15/2023\", \"Description\": \"FDMS FDMS PYMT 052-1633099-000\", \"Amount\": \"$28.28\"}, {\"Date\": \"11/15/2023\", \"Description\": \"FDMS FDMS PYMT 052-1633305-000\", \"Amount\": \"$28.28\"}, {\"Date\": \"11/15/2023\", \"Description\": \"FDMS FDMS PYMT 052-1633166-000\", \"Amount\": \"$28.41\"}, {\"Date\": \"11/15/2023\", \"Description\": \"FDMS FDMS PYMT 052-1632693-000\", \"Amount\": \"$28.41\"}, {\"Date\": \"11/15/2023\", \"Description\": \"FDMS FDMS PYMT 052-1633304-000\", \"Amount\": \"$28.41\"}, {\"Date\": \"11/28/2023\", \"Description\": \"PGANDE WEB ONLINE **************\", \"Amount\": \"$57.68\"}, {\"Date\": \"11/28/2023\", \"Description\": \"PINNACLE MEDICAL Trfr 2BofA *********\", \"Amount\": \"$15,000.00\"}, {\"Date\": \"11/30/2023\", \"Description\": \"SERVICE CHARGE\", \"Amount\": \"$108.02\"}]}, \"meta_data\": {\"page_width\": 612.0, \"page_height\": 792.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c4e5b6fa31"}, "doc_verified_data": {"Fields": {"BankName": ["Pacific Valley Bank", 1, 11, 76, 124, 85], "BranchName": ["P.O. Box 3648 Salinas, CA 93912", 1, 11, 93, 163, 112], "AccountHolder": ["PINNACLE MEDICAL GROUP INC", 1, 14, 170, 159, 211], "AccountNumber": ["9645", 1, 357, 321, 412, 330], "StatementPeriod": ["11/01/2023-11/30/2023", 1, 317, 48, 536, 62], "Currency unit": ["$USD", 2, 520, 255, 579, 264], "OpeningBalance": ["$4,969.61", 1, 298, 412, 338, 421], "ClosingBalance": ["$4,813.79", 1, 298, 446, 338, 455], "TotalCredits": ["$15,953.33", 1, 293, 423, 338, 432]}, "Tables": {"Transactions": [{"Date": "11/01/2023", "Description": "PNC MERCHANT DEPOSIT ************", "Amount": "$64.48"}, {"Date": "11/02/2023", "Description": "HCCLAIMPMT VPAY E027517091 TRN* 1* ********** **********", "Amount": "$13.99"}, {"Date": "11/06/2023", "Description": "HCCLAIMPMT VPAY E027658173 TRN* 1* ********* **********", "Amount": "$153.93"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027673380 TRN* 1* ********* **********", "Amount": "$32.02"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027676273 TRN* 1* ********* **********", "Amount": "$90.00"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027676273 TRN* 1* 01893469* **********", "Amount": "$90.00"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027678124 TRN* 1* 01892953* **********", "Amount": "$90.00"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027678652 TRN* 1* 01894510* **********", "Amount": "$90.00"}, {"Date": "11/07/2023", "Description": "HCCLAIMPMT VPAY E027679547 TRN* 1* 01894835* **********", "Amount": "$90.00"}, {"Date": "11/08/2023", "Description": "HCCLAIMPMT VPAY E027700793 TRN* 1* 139119330* 1362685608", "Amount": "$180.00"}, {"Date": "11/08/2023", "Description": "HCCLAIMPMT VPAY E027711727 TRN* 1* 138041938* 1362685608", "Amount": "$180.00"}, {"Date": "11/09/2023", "Description": "HCCLAIMPMT VPAY E027747760 TRN* 1* 138387751* 1362685608", "Amount": "$180.00"}, {"Date": "11/09/2023", "Description": "HCCLAIMPMT VPAY E027764337 TRN* 1* 139120001* 1362685608", "Amount": "$270.00"}, {"Date": "11/10/2023", "Description": "MERCHANT BNKCD DEPOSIT 359200119881", "Amount": "$270.00"}, {"Date": "11/13/2023", "Description": "MERCHANT BNKCD DEPOSIT 359200119881", "Amount": "$270.00"}, {"Date": "11/13/2023", "Description": "MERCHANT BNKCD DEPOSIT 359200119881", "Amount": "$632.15"}, {"Date": "11/13/2023", "Description": "PNC MERCHANT DEPOSIT ************", "Amount": "$77.44"}, {"Date": "11/13/2023", "Description": "MERCHANT BNKCD DEPOSIT 359200119881", "Amount": "$294.08"}, {"Date": "11/02/2023", "Description": "VPAY F027527770 V75240522 752405220", "Amount": "$39.50"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359200119881", "Amount": "$2.59"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD INTERCHNG 359200119881", "Amount": "$12.85"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359161480884", "Amount": "$27.95"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359161483888", "Amount": "$27.95"}, {"Date": "11/13/2023", "Description": "PNC MERCHANT DEPOSIT ************", "Amount": "$77.44"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD FEE 359200119881", "Amount": "$35.56"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359161481882", "Amount": "$44.95"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359161482880", "Amount": "$44.95"}, {"Date": "11/03/2023", "Description": "MERCHANT BNKCD DISCOUNT 359161484886", "Amount": "$44.95"}, {"Date": "11/06/2023", "Description": "PNC MERCHANT DISCOUNT ************", "Amount": "$40.39"}, {"Date": "11/06/2023", "Description": "PGANDE WEB ONLINE 69993630101923", "Amount": "$380.02"}, {"Date": "11/13/2023", "Description": "BK OF AMER VI/MC ONLINE PMT CKF242250070POS", "Amount": "$100.00"}, {"Date": "11/15/2023", "Description": "FDMS FDMS PYMT 052-1633099-000", "Amount": "$28.28"}, {"Date": "11/15/2023", "Description": "FDMS FDMS PYMT 052-1633305-000", "Amount": "$28.28"}, {"Date": "11/15/2023", "Description": "FDMS FDMS PYMT 052-1633166-000", "Amount": "$28.41"}, {"Date": "11/15/2023", "Description": "FDMS FDMS PYMT 052-1632693-000", "Amount": "$28.41"}, {"Date": "11/15/2023", "Description": "FDMS FDMS PYMT 052-1633304-000", "Amount": "$28.41"}, {"Date": "11/28/2023", "Description": "PGANDE WEB ONLINE **************", "Amount": "$57.68"}, {"Date": "11/28/2023", "Description": "PINNACLE MEDICAL Trfr 2BofA *********", "Amount": "$15,000.00"}, {"Date": "11/30/2023", "Description": "SERVICE CHARGE", "Amount": "$108.02"}]}, "meta_data": {"page_width": 612.0, "page_height": 792.0}}}, "2": {"model_name": "Job Description", "family_name": "Demo HR", "doc_path": "Data/doc_initialize/Demo-Job_Description.pdf", "doc_name": "Demo-Job_Description.pdf", "doc_page_count": 2, "doc_type": "PDF", "is_scanned": false, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9mITjEG6ZnAF62ZuFfGYALXlkMGAA", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 2722, "prompt_tokens": 2087, "completion_tokens": 635}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"CompanyName\": [\"Resolution Life Group Services\", 1, 182, 147, 323, 160], \"Location\": [\"Worldwide, preferred time zone UK or Continental Europe\", 1, 182, 127, 440, 141], \"JobTitle\": [\"Data Delivery Manager\", 1, 182, 107, 287, 121], \"RequiredExperience\": [\"Min 10 years work experience in successful delivery of complex data related projects end to end.\", 2, 119, 63, 516, 88], \"Salary\": [\"1,20,000\", 1, 182, 215, 224, 229], \"Description\": [\"The Data Delivery Manager is the key enabler to satisfy the delivery of Data capabilities required for delivery of Transformation for Group. The Data Delivery Manager will be the conduit between the Project Sponsor, Group and business platform stakeholders and external Data delivery partners and is accountable for successfully delivering the Data related parts of a project. The role involves coordination of the day-to-day activities, support on the prioritization of the requirements/backlog as well as support on the strategic planning over the project lifetime. The role plays a pivotal role to the timely implementation and success of any Data related project.\", 1, 83, 370, 556, 483], \"Responsibilities\": [\"Work in close collaboration with project sponsors and stakeholders to ensure appropriate definition of project goals, deliverables, and acceptance criteria. Day-to-day coordination on progress, prioritization and problem solving with the external Data implementation team. Support the process of documenting business requirements/user stories from stakeholders. Support prioritization of the requirements catalogue/user story backlog. Coordinate interactions with the business platforms/entities. Support alignment meetings eg stand-ups, showcases, SteerCo, Working Groups etc. Create detailed project plans and sprint backlogs. Create and deliver project work plans and revise as appropriate to meet changing needs or requirements.\", 1, 101, 551, 524, 688], \"Qualifications\": [\"Qualified in Accountant\", 1, 182, 304, 288, 317], \"LanguageProficiency\": [\"Hindi,English,Marathi\", 1, 182, 274, 280, 288], \"Industry\": [\"Technical\", 1, 182, 245, 231, 259]}, \"Tables\": {\"Skills\": [{\"SkillName\": \"Experience in Agile or DataOps delivery, quality practices, techniques and tools at all layers of data engineering\"}, {\"SkillName\": \"Familiarity with JIRA and other prioritization tools Knowledge and experience with project management methodologies (Agile/Waterfall) to work with intricate, multifaceted projects.\"}, {\"SkillName\": \"Excellent communication and coordination skills\"}, {\"SkillName\": \"Comfortable with changing and flexible requirements from business owner\"}, {\"SkillName\": \"Customer oriented attitude\"}, {\"SkillName\": \"High degree of self-motivation\"}]}, \"meta_data\": {\"page_width\": 612.0, \"page_height\": 792.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_18cc0f1fa0"}, "doc_verified_data": {"Fields": {"CompanyName": ["Resolution Life Group Services", 1, 182, 147, 323, 160], "Location": ["Worldwide, preferred time zone UK or Continental Europe", 1, 182, 127, 440, 141], "JobTitle": ["Data Delivery Manager", 1, 182, 107, 287, 121], "RequiredExperience": ["Min 10 years work experience in successful delivery of complex data related projects end to end.", 2, 119, 63, 516, 88], "Salary": ["1,20,000", 1, 182, 215, 224, 229], "Description": ["The Data Delivery Manager is the key enabler to satisfy the delivery of Data capabilities required for delivery of Transformation for Group. The Data Delivery Manager will be the conduit between the Project Sponsor, Group and business platform stakeholders and external Data delivery partners and is accountable for successfully delivering the Data related parts of a project. The role involves coordination of the day-to-day activities, support on the prioritization of the requirements/backlog as well as support on the strategic planning over the project lifetime. The role plays a pivotal role to the timely implementation and success of any Data related project.", 1, 83, 370, 556, 483], "Responsibilities": ["Work in close collaboration with project sponsors and stakeholders to ensure appropriate definition of project goals, deliverables, and acceptance criteria. Day-to-day coordination on progress, prioritization and problem solving with the external Data implementation team. Support the process of documenting business requirements/user stories from stakeholders. Support prioritization of the requirements catalogue/user story backlog. Coordinate interactions with the business platforms/entities. Support alignment meetings eg stand-ups, showcases, SteerCo, Working Groups etc. Create detailed project plans and sprint backlogs. Create and deliver project work plans and revise as appropriate to meet changing needs or requirements.", 1, 101, 551, 524, 688], "Qualifications": ["Qualified in Accountant", 1, 182, 304, 288, 317], "Language Proficiency": ["Hindi, English, Marathi", 1, 182, 274, 280, 288], "Industry": ["Technical", 1, 182, 245, 231, 259]}, "Tables": {"Skills": [{"Skill Name": "Experience in Agile or DataOps delivery, quality practices, techniques and tools at all layers of data engineering"}, {"Skill Name": "Familiarity with JIRA and other prioritization tools Knowledge and experience with project management methodologies (Agile/Waterfall) to work with intricate, multifaceted projects."}, {"Skill Name": "Excellent communication and coordination skills"}, {"Skill Name": "Comfortable with changing and flexible requirements from business owner"}, {"Skill Name": "Customer oriented attitude"}, {"Skill Name": "High degree of self-motivation"}]}, "meta_data": {"page_width": 612.0, "page_height": 792.0}}}, "3": {"model_name": "Medical Invoice", "family_name": "Demo Medical Insurance", "doc_path": "Data/doc_initialize/Demo-medical_invoice.pdf", "doc_name": "Demo-medical_invoice.pdf", "doc_page_count": 1, "doc_type": "PDF", "is_scanned": true, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9tAIqjAhaoebMDvyt13Uxd7tgLcfW", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 7282, "prompt_tokens": 6628, "completion_tokens": 654}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"PatientCity\": [\"SAN DIEGO\", 1, 197, 750, 316, 768], \"PatientState\": [\"CA\", 1, 585, 739, 617, 753], \"PatientZipCode\": [\"92113\", 1, 193, 799, 306, 818], \"TotalCharge\": [\"334.04\", 1, 1110, 1906, 1184, 1928], \"AmountPaid\": [\"0.00\", 1, 1315, 1902, 1366, 1923], \"ServiceFacilityLocationInformation\": [\"OCCUPATIONAL MEDICAL GROUP, 032 W 8th Street, National City CA 91950-1002\", 1, 609, 1961, 818, 2032], \"BillingProviderInfo&Ph#\": [\"Occupational Medical Group, PO Box 51032, Los Angeles CA 90051-5332, Phone: ************\", 1, 1009, 1933, 1372, 2030], \"InsuredZipCode\": [\"45262\", 1, 1091, 514, 1202, 531]}, \"Tables\": {\"LineItemTable\": [{\"DatesOfServiceFrom\": \"07-03-23\", \"DatesOfServiceTo\": \"07-03-23\", \"Procedures,ServicesOrSupplies\": \"99226\", \"DiagonsisPartner\": \"A.B\", \"Charges\": \"47.89\", \"DaysOrUnits\": \"1.0\", \"RenderingProviderId.#\": \"**********\"}, {\"DatesOfServiceFrom\": \"07-03-23\", \"DatesOfServiceTo\": \"07-03-23\", \"Procedures,ServicesOrSupplies\": \"99080\", \"DiagonsisPartner\": \"A.B\", \"Charges\": \"75.00\", \"DaysOrUnits\": \"1.0\", \"RenderingProviderId.#\": \"**********\"}, {\"DatesOfServiceFrom\": \"07-03-23\", \"DatesOfServiceTo\": \"07-03-23\", \"Procedures,ServicesOrSupplies\": \"97112\", \"DiagonsisPartner\": \"A.B\", \"Charges\": \"56.61\", \"DaysOrUnits\": \"1.0\", \"RenderingProviderId.#\": \"**********\"}, {\"DatesOfServiceFrom\": \"07-03-23\", \"DatesOfServiceTo\": \"07-03-23\", \"Procedures,ServicesOrSupplies\": \"97535\", \"DiagonsisPartner\": \"B\", \"Charges\": \"4.54\", \"DaysOrUnits\": \"1.0\", \"RenderingProviderId.#\": \"**********\"}]}, \"meta_data\": {\"page_width\": 1681.0, \"page_height\": 2379.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c9aa9c0491"}, "doc_verified_data": {"Fields": {"Patient City": ["SAN DIEGO", 1, 77, 598, 228, 617], "Patient State": ["CA", 1, 572, 599, 611, 617], "Patient Zip Code": ["92113-9998", 1, 77, 665, 222, 686], "Total Charge": ["$334.04", 1, 1147, 1987, 1228, 2004], "Amount Paid": ["$0.00", 1, 1375, 1987, 1427, 2005], "Service Facility Location Information": ["032 W 8th Street, National City CA 91950-1002", 1, 502, 2065, 668, 2082], "Billing Provider Info & Ph #": ["Occupational Medical Group, PO Box 51032, Los Angeles CA 90051-5332, (949) 867-4111", 1, 1124, 2041, 1402, 2061], "Insured Zip Code": ["92113-9998", 1, 77, 665, 222, 686]}, "Tables": {"LineItemTable": [{"Dates of Service From": "07-03-2023", "Dates of Service To": "07-03-2023", "Procedures, Services or Supplies": "99226", "Diagnosis Partner": "A, B", "Charges": "$147.89", "Days or Units": "1.0", "Rendering Provider Id.#": "**********"}, {"Dates of Service From": "07-03-2023", "Dates of Service To": "07-03-2023", "Procedures, Services or Supplies": "99080", "Diagnosis Partner": "A, B", "Charges": "$75.00", "Days or Units": "1.0", "Rendering Provider Id.#": "**********"}, {"Dates of Service From": "07-03-2023", "Dates of Service To": "07-03-2023", "Procedures, Services or Supplies": "97112", "Diagnosis Partner": "A, B", "Charges": "$56.61", "Days or Units": "1.0", "Rendering Provider Id.#": "**********"}, {"Dates of Service From": "07-03-2023", "Dates of Service To": "07-03-2023", "Procedures, Services or Supplies": "97535", "Diagnosis Partner": "A, B", "Charges": "$54.54", "Days or Units": "1.0", "Rendering Provider Id.#": "**********"}]}, "meta_data": {"page_width": 1681.0, "page_height": 2379.0}}}, "4": {"model_name": "Medical Record", "family_name": "Demo Medical Insurance", "doc_path": "Data/doc_initialize/Demo-medical_record.pdf", "doc_name": "Demo-medical_record.pdf", "doc_page_count": 1, "doc_type": "PDF", "is_scanned": true, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9tBMaJiP7bIXdofNPymPLFbivEmeE", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 3880, "prompt_tokens": 3491, "completion_tokens": 389}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"PatientCity\": [\"SAN DIEGO\", 1, 574, 683, 736, 702], \"PatientState\": [\"CA\", 1, 880, 693, 963, 709], \"PatientZipCode\": [\"92458\", 1, 1179, 703, 1272, 720], \"Occupation\": [\"MECHANIC X 5 YEARS\", 1, 110, 743, 448, 765], \"DateOfInjury\": [\"1/24/2023\", 1, 110, 707, 348, 728], \"ClaimAdministratorName\": [\"CHNCHE ROOKE\", 1, 116, 825, 353, 843], \"ClaimAdministratorAddress\": [\"PO BOX 1300\",1, 190, 858, 320, 889], \"EmployerName\": [\"AMERIELD SCAFFELD\", 1, 104, 896, 504, 916], \"ClaimsAdministratorCity\": [\"BROOKFIELD\", 1, 390, 868, 500, 889], \"ClaimsAdministratorZipCode\": [\"53008-1300\", 1, 105, 868, 813, 889], \"ClaimsAdministratorState\": [\"WI\", 1, 105, 868, 813, 889], \"InjuryDetails(Diagonsis)\": [\"(1)S61214D; Laceration without foreign body of right ring finger without damage to nail, subsequent encounter (2)S61212D; Laceration without foreign body of right middle finger without damage to nail, subsequent encounter\", 1, 1445, 1057, 1467, 1491]}, \"meta_data\": {\"page_width\": 1681.0, \"page_height\": 2379.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c9aa9c0491"}, "doc_verified_data": {"Fields": {"Patient City": ["SAN DIEGO", 1, 574, 683, 736, 702], "Patient State": ["CA", 1, 880, 693, 963, 709], "Patient Zip Code": ["92458", 1, 1179, 703, 1272, 720], "Occupation": ["MECHANIC X 5 YEARS", 1, 110, 743, 448, 765], "Date of Injury": ["1/24/2023", 1, 110, 707, 348, 728], "Claim Administrator Name": ["CHNCHE ROOKE", 1, 106, 825, 343, 843], "Claim Administrator Address": ["PO BOX 1300", 1, 105, 868, 813, 889], "Employer Name": ["AMERIELD SCAFFELD", 1, 104, 896, 504, 916], "Claims Administrator City": ["BROOKFIELD", 1, 390, 868, 500, 889], "Claims Administrator Zip Code": ["53008-1300", 1, 95, 882, 804, 901], "Claims Administrator State": ["WI", 1, 90, 1430, 1200, 1500], "Injury Details(Diagnosis)": ["1)S61214D; Laceration without foreign body of right ring finger without damage to nail, subsequent encounter\n2)S61212D; Laceration without foreign body of right middle finger without damage to nail, subsequent encounter", 1, 103, 1455, 1066, 1498]}, "Tables": {}, "meta_data": {"page_width": 1681.0, "page_height": 2379.0}}}, "5": {"model_name": "Reconsideration", "family_name": "Demo Medical Insurance", "doc_path": "Data/doc_initialize/Demo-reconsideration.pdf", "doc_name": "Demo-reconsideration.pdf", "doc_page_count": 1, "doc_type": "PDF", "is_scanned": true, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9mIW4q2hfupfjsttyYZQjggi2PoMV", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 3933, "prompt_tokens": 3244, "completion_tokens": 689}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"ReviewDate\": [\"2/15/2021\", 1, 1406, 380, 1506, 396], \"ProviderTaxID\": [\"*********\", 1, 154, 545, 501, 564], \"Jurisdiction\": [\"California\", 1, 197, 585, 473, 603], \"DateOfInjury\": [\"31/9/2020\", 1, 1054, 580, 1342, 606], \"Payee\": [\"SHEILDBA MITTAL MD A PROFRESS DBA WORKWELL MEDICAL GROUP; 1172 S MAIN ST # 380; Salinas, CA 93901\", 1, 270, 699, 869, 725], \"ICDCodes\": [\"S29.012D, S23.3XXD, M62.830\", 1, 1103, 779, 1500, 801], \"RenderingProvider\": [\"846 FREEDOM BLVD; Salinas, CA 95076\", 1, 432, 947, 686, 965], \"EmployerID\": [\"ALWC133076\", 1, 1248, 945, 1412, 963], \"RenderingProviderNPI\": [\"**********\", 1, 480, 1104, 622, 1124], \"TotalBilled\": [\"337.62\", 1, 1124, 2061, 1181, 2076], \"TotalFee\": [\"183.09\", 1, 1234, 2063, 1292, 2075], \"TotalPPO\": [\"20.09\", 1, 1354, 2062, 1403, 2075], \"TotalAllowed\": [\"134.44\", 1, 1466, 2060, 1524, 2075]}, \"Tables\": {\"LineItemTable\": [{\"DateOfService\": \"2/24/2021\", \"Code\": \"99214\", \"Mod\": \"00\", \"Qty\": \"1.00\", \"Billed\": \"196.25\", \"Fee\": \"41.72\", \"PPO\": \"20.09\", \"Allowed\": \"134.44\"}, {\"DateOfService\": \"2/24/2021\", \"Code\": \"99072\", \"Mod\": \"00\", \"Qty\": \"1.00\", \"Billed\": \"125.00\", \"Fee\": \"125.00\", \"PPO\": \"0.00\", \"Allowed\": \"0.00\"}, {\"DateOfService\": \"2/24/2021\", \"Code\": \"WC002\", \"Mod\": \"00\", \"Qty\": \"1.00\", \"Billed\": \"16.37\", \"Fee\": \"16.37\", \"PPO\": \"0.00\", \"Allowed\": \"0.00\"}]}, \"meta_data\": {\"page_width\": 1681.0, \"page_height\": 2379.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c4e5b6fa31"}, "doc_verified_data": {"Fields": {"Review Date": ["2/15/2021", 1, 1406, 380, 1506, 396], "Provider Tax ID": ["*********", 1, 154, 545, 501, 564], "Jurisdiction": ["California", 1, 197, 585, 473, 603], "Date of Injury": ["31/9/2020", 1, 1054, 580, 1342, 606], "Payee": ["SHEILDBA MITTAL MD A PROFRESS DBA WORKWELL MEDICAL GROUP; 1172 S MAIN ST # 380; Salinas, CA 93901", 1, 270, 699, 869, 725], "ICD Codes": ["S29.012D, S23.3XXD, M62.830", 1, 1103, 779, 1500, 801], "Rendering Provider": ["846 FREEDOM BLVD; Salinas, CA 95076", 1, 432, 947, 686, 965], "Employer ID": ["ALWC133076", 1, 1248, 945, 1412, 963], "Rendering Provider NPI": ["**********", 1, 480, 1104, 622, 1124], "Total Billed": ["337.62", 1, 1124, 2061, 1181, 2076], "Total Fee": ["183.09", 1, 1234, 2063, 1292, 2075], "Total PPO": ["20.09", 1, 1354, 2062, 1403, 2075], "Total Allowed": ["134.44", 1, 1466, 2060, 1524, 2075]}, "Tables": {"LineItemTable": [{"Date of Service": "2/24/2021", "Code": "99214", "Mod": "00", "Qty": "1.00", "Billed": "196.25", "Fee": "41.72", "PPO": "20.09", "Allowed": "134.44"}, {"Date of Service": "2/24/2021", "Code": "99072", "Mod": "00", "Qty": "1.00", "Billed": "125.00", "Fee": "125.00", "PPO": "0.00", "Allowed": "0.00"}, {"Date of Service": "2/24/2021", "Code": "WC002", "Mod": "00", "Qty": "1.00", "Billed": "16.37", "Fee": "16.37", "PPO": "0.00", "Allowed": "0.00"}]}, "meta_data": {"page_width": 1681.0, "page_height": 2379.0}}}, "6": {"model_name": "Insurance EOR", "family_name": "Demo Medical Insurance", "doc_path": "Data/doc_initialize/Demo-EOR.pdf", "doc_name": "Demo-EOR.pdf", "doc_page_count": 2, "doc_type": "PDF", "is_scanned": true, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9mIWPoxPXdNul5DOvO2bQlry8ROlI", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 17592, "prompt_tokens": 16229, "completion_tokens": 1363}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"NPINumber\": [\"**********\", 1, 1115, 342, 1248, 361], \"ProviderTaxID\": [\"*********\", 2, 291, 377, 414, 395], \"DateOfInjury\": [\"01/23/2023\", 1, 1110, 408, 1234, 427], \"TotalAllowance\": [\"0.00\", 1, 1437, 1713, 1477, 1729], \"TotalBR/Red\": [\"589.87\", 1, 1004, 1673, 1069, 1689], \"TotalPPO/Red\": [\"0.00\", 1, 1301, 1673, 1341, 1688], \"TotalCharge\": [\"589.87\", 1, 868, 1672, 934, 1689], \"TotalOther/Red\": [\"0.00\", 1, 1437, 1674, 1479, 1688], \"RenderingProviderName\": [\"YANG, RONG\", 1, 54, 1752, 459, 1773], \"InvoiceDate\": [\"03/09/2023\", 1, 1110, 507, 1234, 526]}, \"Tables\": {\"LineItemTable\": [{\"DOS\": \"01/25/23\", \"Code/Proc\": \"99204\", \"Units\": \"1.000\", \"Charge\": \"247.43\", \"BR/Red\": \"247.43\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"72110\", \"Units\": \"1.000\", \"Charge\": \"81.58\", \"BR/Red\": \"81.58\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"73610\", \"Units\": \"1.000\", \"Charge\": \"58.64\", \"BR/Red\": \"58.64\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"73630\", \"Units\": \"1.000\", \"Charge\": \"54.82\", \"BR/Red\": \"54.82\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"73590\", \"Units\": \"1.000\", \"Charge\": \"49.97\", \"BR/Red\": \"49.97\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"A4467\", \"Units\": \"1.000\", \"Charge\": \"39.00\", \"BR/Red\": \"39.00\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"L0625\", \"Units\": \"1.000\", \"Charge\": \"34.53\", \"BR/Red\": \"34.53\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"A9150\", \"Units\": \"1.000\", \"Charge\": \"3.80\", \"BR/Red\": \"3.80\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"A9273\", \"Units\": \"1.000\", \"Charge\": \"1.25\", \"BR/Red\": \"1.25\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"A6449\", \"Units\": \"1.000\", \"Charge\": \"2.79\", \"BR/Red\": \"2.79\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"49483060901\", \"Units\": \"21.000\", \"Charge\": \"8.81\", \"BR/Red\": \"8.81\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}, {\"DOS\": \"01/25/23\", \"Code/Proc\": \"69618001001\", \"Units\": \"50.000\", \"Charge\": \"7.25\", \"BR/Red\": \"7.25\", \"PPO/Red\": \"0.00\", \"Other/Red\": \"0.00\", \"Allowance\": \"0.00\"}]}, \"meta_data\": {\"page_width\": 1715.0, \"page_height\": 2331.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c4e5b6fa31"}, "doc_verified_data": {"Fields": {"NPI Number": ["**********", 1, 1115, 342, 1248, 361], "Provider Tax ID": ["*********", 2, 291, 377, 414, 395], "Date of Injury": ["01/23/2023", 1, 1110, 408, 1234, 427], "Total Allowance": ["0.00", 1, 1437, 1713, 1477, 1729], "Total BR/Red": ["589.87", 1, 1004, 1673, 1069, 1689], "Total PPO/Red": ["0.00", 1, 1301, 1673, 1341, 1688], "Total Charge": ["589.87", 1, 868, 1672, 934, 1689], "Total Other/Red": ["0.00", 1, 1437, 1674, 1479, 1688], "Rendering Provider Name": ["YANG, RONG", 1, 54, 1752, 459, 1773], "Invoice Date": ["03/09/2023", 1, 1110, 507, 1234, 526]}, "Tables": {"LineItemTable": [{"DOS": "01/25/23", "Code/Proc": "99204", "Units": "1.000", "Charge": "247.43", "BR/Red": "247.43", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "72110", "Units": "1.000", "Charge": "81.58", "BR/Red": "81.58", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "73610", "Units": "1.000", "Charge": "58.64", "BR/Red": "58.64", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "73630", "Units": "1.000", "Charge": "54.82", "BR/Red": "54.82", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "73590", "Units": "1.000", "Charge": "49.97", "BR/Red": "49.97", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "A4467", "Units": "1.000", "Charge": "39.00", "BR/Red": "39.00", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "L0625", "Units": "1.000", "Charge": "34.53", "BR/Red": "34.53", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "A9150", "Units": "1.000", "Charge": "3.80", "BR/Red": "3.80", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "A9273", "Units": "1.000", "Charge": "1.25", "BR/Red": "1.25", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "A6449", "Units": "1.000", "Charge": "2.79", "BR/Red": "2.79", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "49483060901", "Units": "21.000", "Charge": "8.81", "BR/Red": "8.81", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}, {"DOS": "01/25/23", "Code/Proc": "69618001001", "Units": "50.000", "Charge": "7.25", "BR/Red": "7.25", "PPO/Red": "0.00", "Other/Red": "0.00", "Allowance": "0.00"}]}, "meta_data": {"page_width": 1715.0, "page_height": 2331.0}}}, "7": {"model_name": "Receipt", "family_name": "Demo Finance", "doc_path": "Data/doc_initialize/Demo-Receipt.jpg", "doc_name": "Demo-Receipt.jpg", "doc_page_count": 0, "doc_type": "JPEG", "is_scanned": true, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9mITBwNyTM181Wb68YHfMAkccC7I7", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 3595, "prompt_tokens": 2291, "completion_tokens": 1304}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"CompanyName\": [\"Walmart\", 1, 81, 53, 162, 71], \"CompanyAddress\": [\"882 S. STATE ROAD 136, GREENWOOD, IN 46143\", 1, 63, 85, 180, 108], \"TypeOfRecipt\": [\"Retail\", 1, 81, 53, 162, 71], \"TransactionDate\": [\"04-27-2019\", 1, 63, 541, 194, 554], \"Time\": [\"12:59:46\", 1, 63, 541, 194, 554], \"ReceiptNumber\": [\"0783 5080 4072 3416 2495 6\", 1, 38, 499, 221, 511], \"PaymentMethod\": [\"Cash\", 1, 112, 462, 233, 477], \"Subtotal\": [\"139.44\", 1, 124, 429, 231, 443], \"Tax\": [\"4.58\", 1, 207, 442, 231, 450], \"Discounts\": [\"0.64\", 1, 40, 388, 177, 401], \"FinalAmount\": [\"144.02\", 1, 143, 452, 232, 463]}, \"Tables\": {\"ItemsPurchased\": [{\"ItemName\": \"TATER TOTS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.96\", \"TotalPrice\": \"2.96\"}, {\"ItemName\": \"HARD/PROV/DC\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.68\", \"TotalPrice\": \"2.68\"}, {\"ItemName\": \"SNACK BARS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"4.98\", \"TotalPrice\": \"4.98\"}, {\"ItemName\": \"HRI CL CHS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"5.88\", \"TotalPrice\": \"5.88\"}, {\"ItemName\": \"HRI CL CHS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"5.88\", \"TotalPrice\": \"5.88\"}, {\"ItemName\": \"HRI 12 U SG\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"5.88\", \"TotalPrice\": \"5.88\"}, {\"ItemName\": \"HRI CL PEP\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"6.88\", \"TotalPrice\": \"6.88\"}, {\"ItemName\": \"EARBUDS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"4.88\", \"TotalPrice\": \"4.88\"}, {\"ItemName\": \"SC BCN CHOOR\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"6.98\", \"TotalPrice\": \"6.98\"}, {\"ItemName\": \"ABF THINBRST\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"9.72\", \"TotalPrice\": \"9.72\"}, {\"ItemName\": \"HARD/PROV/DC\", \"ItemQuantity\": \"2\", \"ItemPrice\": \"2.68\", \"TotalPrice\": \"5.36\"}, {\"ItemName\": \"DV RSE OIL M\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"5.94\", \"TotalPrice\": \"5.94\"}, {\"ItemName\": \"APPLE 3 BAG\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"6.47\", \"TotalPrice\": \"6.47\"}, {\"ItemName\": \"STOK LT SWT\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"4.42\", \"TotalPrice\": \"4.42\"}, {\"ItemName\": \"PEANUT BUTTR\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"6.44\", \"TotalPrice\": \"6.44\"}, {\"ItemName\": \"AVO VERDE\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.98\", \"TotalPrice\": \"2.98\"}, {\"ItemName\": \"ROLLS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"1.28\", \"TotalPrice\": \"1.28\"}, {\"ItemName\": \"BTS DRY BLON\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"6.58\", \"TotalPrice\": \"6.58\"}, {\"ItemName\": \"GALE\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"32.00\", \"TotalPrice\": \"32.00\"}, {\"ItemName\": \"TR HS FRM\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.74\", \"TotalPrice\": \"2.74\"}, {\"ItemName\": \"BAGELS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"4.56\", \"TotalPrice\": \"4.56\"}, {\"ItemName\": \"GV SLIDERS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.98\", \"TotalPrice\": \"2.98\"}, {\"ItemName\": \"ACCESSORY\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"0.97\", \"TotalPrice\": \"0.97\"}, {\"ItemName\": \"CHEEZE IT\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"4.00\", \"TotalPrice\": \"4.00\"}, {\"ItemName\": \"RITZ\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.78\", \"TotalPrice\": \"2.78\"}, {\"ItemName\": \"RUFFLES\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"2.50\", \"TotalPrice\": \"2.50\"}, {\"ItemName\": \"GV HNY GRMS\", \"ItemQuantity\": \"1\", \"ItemPrice\": \"1.28\", \"TotalPrice\": \"1.28\"}]}, \"meta_data\": {\"page_width\": 269.0, \"page_height\": 678.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": 1721297769, "system_fingerprint": "fp_5e997b69d8"}, "doc_verified_data": {"Fields": {"CompanyName": ["Walmart", 1, 81, 53, 162, 71], "CompanyAddress": ["882 S. STATE ROAD 136, <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, IN 46143", 1, 63, 85, 180, 108], "TypeOfReceipt": ["Retail", 1, 81, 53, 162, 71], "TransactionDate": ["04-27-2019", 1, 63, 541, 194, 554], "Time": ["12:59:46", 1, 63, 541, 194, 554], "ReceiptNumber": ["0783 5080 4072 3416 2495 6", 1, 38, 499, 221, 511], "PaymentMethod": ["Cash", 1, 112, 462, 233, 477], "Subtotal": ["139.44", 1, 124, 429, 231, 443], "Tax": ["4.58", 1, 207, 442, 231, 450], "Discounts": ["0.64", 1, 40, 388, 177, 401], "FinalAmount": ["144.02", 1, 143, 452, 232, 463]}, "Tables": {"ItemsPurchased": [{"ItemName": "TATER TOTS", "ItemQuantity": "1", "ItemPrice": "2.96", "TotalPrice": "2.96"}, {"ItemName": "HARD/PROV/DC", "ItemQuantity": "1", "ItemPrice": "2.68", "TotalPrice": "2.68"}, {"ItemName": "SNACK BARS", "ItemQuantity": "1", "ItemPrice": "4.98", "TotalPrice": "4.98"}, {"ItemName": "HRI CL CHS", "ItemQuantity": "1", "ItemPrice": "5.88", "TotalPrice": "5.88"}, {"ItemName": "HRI CL CHS", "ItemQuantity": "1", "ItemPrice": "5.88", "TotalPrice": "5.88"}, {"ItemName": "HRI 12 U SG", "ItemQuantity": "1", "ItemPrice": "5.88", "TotalPrice": "5.88"}, {"ItemName": "HRI CL PEP", "ItemQuantity": "1", "ItemPrice": "6.88", "TotalPrice": "6.88"}, {"ItemName": "EARBUDS", "ItemQuantity": "1", "ItemPrice": "4.88", "TotalPrice": "4.88"}, {"ItemName": "SC BCN CHOOR", "ItemQuantity": "1", "ItemPrice": "6.98", "TotalPrice": "6.98"}, {"ItemName": "ABF THINBRST", "ItemQuantity": "1", "ItemPrice": "9.72", "TotalPrice": "9.72"}, {"ItemName": "HARD/PROV/DC", "ItemQuantity": "2", "ItemPrice": "2.68", "TotalPrice": "5.36"}, {"ItemName": "DV RSE OIL M", "ItemQuantity": "1", "ItemPrice": "5.94", "TotalPrice": "5.94"}, {"ItemName": "APPLE 3 BAG", "ItemQuantity": "1", "ItemPrice": "6.47", "TotalPrice": "6.47"}, {"ItemName": "STOK LT SWT", "ItemQuantity": "1", "ItemPrice": "4.42", "TotalPrice": "4.42"}, {"ItemName": "PEANUT BUTTR", "ItemQuantity": "1", "ItemPrice": "6.44", "TotalPrice": "6.44"}, {"ItemName": "AVO VERDE", "ItemQuantity": "1", "ItemPrice": "2.98", "TotalPrice": "2.98"}, {"ItemName": "ROLLS", "ItemQuantity": "1", "ItemPrice": "1.28", "TotalPrice": "1.28"}, {"ItemName": "BTS DRY BLON", "ItemQuantity": "1", "ItemPrice": "6.58", "TotalPrice": "6.58"}, {"ItemName": "GALE", "ItemQuantity": "1", "ItemPrice": "32.00", "TotalPrice": "32.00"}, {"ItemName": "TR HS FRM", "ItemQuantity": "1", "ItemPrice": "2.74", "TotalPrice": "2.74"}, {"ItemName": "BAGELS", "ItemQuantity": "1", "ItemPrice": "4.56", "TotalPrice": "4.56"}, {"ItemName": "GV SLIDERS", "ItemQuantity": "1", "ItemPrice": "2.98", "TotalPrice": "2.98"}, {"ItemName": "ACCESSORY", "ItemQuantity": "1", "ItemPrice": "0.97", "TotalPrice": "0.97"}, {"ItemName": "CHEEZE IT", "ItemQuantity": "1", "ItemPrice": "4.00", "TotalPrice": "4.00"}, {"ItemName": "RITZ", "ItemQuantity": "1", "ItemPrice": "2.78", "TotalPrice": "2.78"}, {"ItemName": "RUFFLES", "ItemQuantity": "1", "ItemPrice": "2.50", "TotalPrice": "2.50"}, {"ItemName": "GV HNY GRMS", "ItemQuantity": "1", "ItemPrice": "1.28", "TotalPrice": "1.28"}]}, "meta_data": {"page_width": 269.0, "page_height": 678.0}}}, "8": {"model_name": "Resume", "family_name": "Demo HR", "doc_path": "Data/doc_initialize/Demo-Resume.pdf", "doc_name": "Demo-Resume.pdf", "doc_page_count": 2, "doc_type": "PDF", "is_scanned": false, "doc_status": "ToBeApproved", "extract_api_status_code": 200, "comment": null, "is_doc_approved": false, "is_deleted": false, "extract_api_response": {"id": "chatcmpl-9st8mJu9mcKAtg3D1xOrhwpTkJCPv", "model": "gpt-4o-2024-05-13", "usage": {"total_tokens": 6639, "prompt_tokens": 5649, "completion_tokens": 990}, "object": "chat.completion", "choices": [{"index": 0, "message": {"role": "assistant", "content": "{\"Fields\": {\"FullName\": [\"<PERSON>\", 1, 402, 382, 728, 408], \"Gender\": [\"Male\",  1, 440, 490, 485, 520], \"BirthDate\": [\"August 12, 1985\", 1, 520, 490, 640, 520], \"EmailAddress\": [\"<EMAIL>\",  1, 515, 470, 735, 495], \"Address\": [\"5678 Oak Avenue Rivertown, NY 10501\", 1, 740, 470, 1040, 510], \"ContactNumber\": [\"9149476543\", 1, 390, 465, 510, 490], \"LinkedInProfileURL\": [\"https://www.linkedin.com/in/michael t-smith-a719481\", 1, 720, 495, 1150, 535], \"ObjectiveStatement\": [\"Highly motivated Project Manager and Technical Consultant with a passion for empowering non-profit organizations, Seeks to leverage a unique blend of technical expertise (Visual Basic, SQL, etc) and strategic planning to automate workflow and deliver efficient, scalable solutions. Proven ability to build strong client relationships and ensure project alignment with organizational goals.\", 1, 360, 565, 1440, 655], \"Skills\": [\"Visual Basic, SQL, C, C++, Java, Windows NT/2000/XP/Vista, Linux, Unix, SQL Server, Microsoft (MS) Access, Oracle, JCL, SQL, DB2, MS Visio, MS Excel, MS FrontPage, MS Word\", 1, 330, 1070, 1060, 1220], \"Certifications\": [\"Project Management Professional (PMP), CompTIA Project+, Microsoft SQL Server Certification, Python Institute - Python 3 Certification, NTEN Cybersecurity Fundamentals, C++ Institute - C++ Certified Programmer\", 2, 270, 1090, 770, 1340], \"Achievements\": [\"Dean's List (3 semesters), Presidential Scholar (2 semesters), Women in Technology (August 2019 May 2021), Intramural Soccer (Spring 2019)\", 2, 290, 950, 1160, 1020], \"LanguagesKnown\": [\"English, Spanish, French\",2, 270, 1395, 450, 1530], \"TechnologiesUsed\": [\"Visual Basic, SQL, C, C++, Java, Windows NT/2000/XP/Vista, Linux, Unix, SQL Server, Microsoft (MS) Access, Oracle, JCL, SQL, DB2, MS Visio, MS Excel, MS FrontPage, MS Word\", 2, 330, 450, 1320, 550]}, \"Tables\": {\"Education Details\": [{\"Degree\": \"Bachelor of Science in Computer Science\", \"InstitutionName\": \"Southeastern Louisiana University (SLU)\", \"GraduationYear\": \"May 2021\", \"GPA/Marks/%\": \"3.84/4.00\"}], \"Work Experience\": [{\"CompanyName\": \"SLU Computer Science Department\", \"Role\": \"Blog Writer\", \"StartYear\": \"2020\", \"EndYear\": \"Present\", \"Description/Responsibility\": \"Designed a student portal website that allows access to blogs, research articles, and inter-office communication. Wrote and published 5+ blog posts highlighting current industry trends, meeting monthly deadlines. Interviewed faculty, staff, and employers ambitiously to generate content for blog posts and other relevant articles.\"}, {\"CompanyName\": \"Financial Network Services, Pty Ltd.\", \"Role\": \"Unix Administrator Assistant\", \"StartYear\": \"2019\", \"EndYear\": \"2019\", \"Description/Responsibility\": \"Operated Unix Hp/Ux 11i in the transferring of files between development and testing regions. Led team of 4 to configure notebooks and install multilingual software for 6 overseas consultants. Created user accounts and set permissions and passwords with UNIX-SCO.\"}], \"Projects\": [{\"ProjectName\": \"Tangi Humane Society\", \"Description\": \"Served as a project team leader and developed a database to track the adoption of animals proficiently. Used Visual Basic to effectively establish a new user interface that improved user experience significantly. Incorporated Java, HTML, and Flash to create an interactive website\", \"TechnologiesUsed\": \"Visual Basic, Java, HTML, Flash\", \"Role\": \"Project Team Leader\"}]}, \"meta_data\": {\"page_width\": 1681.0, \"page_height\": 2379.0}}", "tool_calls": null, "function_call": null}, "logprobs": null, "finish_reason": "stop"}], "created": **********, "system_fingerprint": "fp_c832e4513b"}, "doc_verified_data": {"Fields": {"Full Name": ["<PERSON>", 1, 36, 42, 220, 67], "Gender": ["Male", 1, 36, 127, 438, 137], "Birth Date": ["08-12-1985", 1, 36, 127, 438, 137], "Email Address": ["<EMAIL>", 1, 36, 108, 382, 119], "Address": ["5678 Oak Avenue, Rivertown, NY 10501", 1, 36, 108, 382, 119], "Contact Number": ["9149476543", 1, 36, 108, 382, 119], "Linked In Profile URL": ["https://www.linkedin.com/in/micha<PERSON>-t-smith-a719481a", 1, 36, 127, 438, 137], "Objective Statement": ["Highly motivated Project Manager and Technical Consultant with a passion for empowering non-profit organizations. Seeks to leverage a unique blend of technical expertise (Visual Basic, SQL, etc.) and strategic planning to automate workflows and deliver efficient, scalable solutions. Proven ability to build strong client relationships and ensure project alignment with organizational goals.", 1, 36, 174, 126, 226], "Skills": ["Project management and consulting experience primarily focused upon nonprofit projects, requiring quick turnarounds and extensive troubleshooting, Proven skills in planning, strategy development, and knowledge of customer and client business processes, Excellent team building and management skills with demonstrated success", 1, 72, 263, 393, 322], "Certifications": ["Project Management Professional (PMP), CompTIA Project+, Microsoft SQL Server Certification, Python Institute - Python 3 Certification, NTEN Cybersecurity Fundamentals, C++ Institute - C++ Certified Programmer", 2, 72, 425, 250, 522], "Achievements": ["Dean’s List (3 semesters); Presidential Scholar (2 semesters), Women in Technology (August 2019 – May 2021); Intramural Soccer (Spring 2019)", 2, 72, 359, 430, 388], "Languages Known": ["English, Spanish, French", 2, 72, 559, 104, 605], "Technologies Used": ["Visual Basic, SQL, C, C++, Java, Windows NT/ 2000/ XP/ Vista, Linux, Unix, SQL Server, Microsoft (MS) Access, Oracle, JCL, DB2, MS Visio, MS Excel, MS FrontPage, MS word, Java, HTML, Flash", 1, 72, 471, 534, 147]}, "Tables": {"Education Details": [{"Degree": "Bachelor of Science in Computer Science", "Institution Name": "Southeastern Louisiana University (SLU)", "Graduation Year": "May 2021", "GPA/Marks/%": "3.84/4.00"}], "Work Experience": [{"Company Name": "SLU Computer Science Department", "Role": "Blog Writer", "Start Year": "2020", "End Year": "Present", "Description / Responsibility": "Designed a student portal website that allows access to blogs, research articles, and inter-office communication, Wrote and published 5+ blog posts highlighting current industry trends, meeting monthly deadlines, Interviewed faculty, staff, and employers ambitiously to generate content for blog posts and other relevant articles"}, {"Company Name": "Financial Network Services, Pty Ltd.", "Role": "Unix Administrator Assistant", "Start Year": "2019", "End Year": "2019", "Description / Responsibility": "Operated Unix Hp/Ux 11i in the transferring of files between development and testing regions, Led team of 4 to configure notebooks and install multilingual software for 6 overseas consultants, Created user accounts and set permissions and passwords with UNIX - SCO"}], "Projects": [{"Project Name": "Tangi Humane Society", "Description": "Served as a project team leader and developed a database to track the adoption of animals proficiently, Used Visual Basic to effectively establish a new user interface that improved user experience significantly, Incorporated Java, HTML, and Flash to create an interactive website", "Technologies Used": "Visual Basic, Java, HTML, Flash", "Role": "Project team leader"}]}, "meta_data": {"page_width": 612.0, "page_height": 792.0}}}}