{"type": "json_schema", "json_schema": {"name": "TOTO", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerCity": {"type": "string", "description": "Name of city from address  of the seller issuing the invoice"}, "SellerState": {"type": "string", "description": "Name of state from address of the seller issuing the invoice"}, "SellerCountry": {"type": "string", "description": "Name of country from address of the seller issuing the invoice"}, "SellerPincode": {"type": "integer", "description": "Pincode of the seller issuing the invoice"}, "SellerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the supplier or seller"}, "SellerPreparationDate": {"type": "integer", "description": "The date(in ddmmyy format) when the seller prepared the invoice"}, "SellerRemovalDate": {"type": "string", "description": "The date (in ddmmyy format) when the goods were removed or dispatched by the seller"}, "SellerSONO": {"type": "integer", "description": "The Sales Order (SO) number provided by the seller"}, "SellerProjectName": {"type": "string", "description": "The name of the project associated with the transaction or sale"}, "SellerIRN": {"type": "string", "description": "The Invoice Reference Number (IRN) generated for the invoice"}, "SellerE-wayBillNO": {"type": "string", "description": "The E-way Bill number used for the transportation of goods"}, "InvoiceNo": {"type": "string", "description": "Invoice number"}, "SAPDocNo": {"type": "string", "description": "SAP document number"}, "DraftInvoice": {"type": "string", "description": "Draft invoice number"}, "InvoiceDate": {"type": "integer", "description": "Date(in ddmmyy format) when the invoice was issued"}, "OrderNo": {"type": "string", "description": "Order number associated with the invoice"}, "DoNo": {"type": "integer", "description": "Delivery Order (DO) number associated with the invoice"}, "TransporterName": {"type": "string", "description": "Name of the transporter"}, "VehicleNo": {"type": "string", "description": "Vehicle number used for transportation"}, "LRNo": {"type": "integer", "description": "Lorry Receipt (LR) number issued by the transporter for tracking the shipment"}, "LRDate": {"type": "integer", "description": "Lorry Receipt (LR) date (in ddmmyy format) issued by the transporter for tracking the shipment"}, "PaymentTerms": {"type": "string", "description": "The agreed-upon terms for payment between the buyer and seller, including due dates and conditions"}, "IncoTerms": {"type": "string", "description": "The international commercial terms (Incoterms) that define the responsibilities of the buyer and seller for the delivery of goods under the contract"}, "SiteAdd": {"type": "string", "description": "The address of the site where the goods or services are to be delivered or performed"}, "BilledToCode": {"type": "integer", "description": "Code of the billing party"}, "BilledToName": {"type": "string", "description": "Name of the billing party"}, "BilledToAddress": {"type": "string", "description": "Address of the billing party"}, "BilledToStateName": {"type": "string", "description": "State name of the billing party"}, "BilledToStateCode": {"type": "integer", "description": "State code of the billing party"}, "BilledToGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number of billimg party"}, "BilledToPlaceOfSupply": {"type": "string", "description": "Location where the supply is made"}, "BilledToDescriptionOfGoods": {"type": "string", "description": "Description of the goods or services being billed to the customer"}, "ShippedToCode": {"type": "integer", "description": "Code of the shipping party"}, "ShipppedToName": {"type": "string", "description": "Name of the shipping party"}, "ShippedToAddress": {"type": "string", "description": "Address of the shipping party"}, "ShippedToStateName": {"type": "string", "description": "State name of the Shipping party"}, "ShippedToGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number of Shipping party"}, "IGSTRate": {"type": "number", "description": "IGST Tax Rate at Source percentage"}, "GrandTotalQTY(PC))": {"type": "number", "description": "The total quantity of items in pieces"}, "GrandTotalAmount(SellingPrice(INR))": {"type": "number", "description": "The total selling price of the items in Indian Rupees (INR) before taxes"}, "GrandTotalFreight(INR)": {"type": "number", "description": "The total freight charges in Indian Rupees (INR)"}, "GrandTotalInsurance(INR)": {"type": "number", "description": "The total insurance charges in Indian Rupees (INR)"}, "GrandTotalTaxableAmount(INR)": {"type": "number", "description": "The total taxable amount in Indian Rupees (INR) before taxes"}, "GrandTotalIGSTAmount(INR)": {"type": "number", "description": "The total Integrated Goods and Services Tax (IGST) amount in Indian Rupees (INR)"}, "GrandTotalAmount(incl.tax)(INR)": {"type": "number", "description": "The total amount including taxes in Indian Rupees (INR)"}, "TCSRate": {"type": "number", "description": "Tax Rate at Source percentage"}, "TCSAmount": {"type": "number", "description": "Tax Amount at Source percentage"}, "TotalValue": {"type": "number", "description": "Total value of the invoice"}, "TotalInvoicevalue(InWords)": {"type": "string", "description": "Total invoice value written in words"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"S.No": {"type": "string", "description": "Serial number of the item in the invoice"}, "ItemCode": {"type": "string", "description": "Code of the product or service"}, "DescriptionofGoods/Services": {"type": "string", "description": "Description of the goods or services"}, "HSN/SAC": {"type": "integer", "description": "HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code)"}, "Qty(PC)": {"type": "integer", "description": "Quantity of the item in pieces"}, "Rate/Price(INR)": {"type": "number", "description": "Rate or price per item"}, "TotalAmount(selling price)(INR)": {"type": "number", "description": "Total selling price of the item"}, "Discount(INR)": {"type": "number", "description": "Discount on the item"}, "Freight(INR)": {"type": "number", "description": "Freight charges for the item"}, "Insurance(INR)": {"type": "number", "description": "Insurance charges for the item"}, "TaxableAmount(INR)": {"type": "number", "description": "Taxable amount for the item"}, "IGSTRate(%)": {"type": "number", "description": "Integrated Goods and Services Tax rate applied to the item"}, "IGSTAmount(INR)": {"type": "number", "description": "IGST amount applied to the item"}, "TotalAmount(incl.tax)(INR)": {"type": "number", "description": "Total amount including tax"}}, "required": ["S.No", "ItemCode", "DescriptionofGoods/Services", "HSN/SAC", "<PERSON><PERSON>(PC)", "Rate/Price(INR)", "TotalAmount(selling price)(INR)", "Discount(INR)", "Freight(INR)", "Insurance(INR)", "TaxableAmount(INR)", "IGSTRate(%)", "IGSTAmount(INR)", "TotalAmount(incl.tax)(INR)"], "additionalProperties": false}, "description": "Details of all the items in the invoice"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerState", "SellerCountry", "SellerPincode", "SellerGSTINNo", "SellerPreparationDate", "SellerRemovalDate", "SellerSONO", "SellerProjectName", "SellerIRN", "SellerE-wayBillNO", "InvoiceNo", "SAPDocNo", "DraftInvoice", "InvoiceDate", "OrderNo", "DoNo", "TransporterName", "VehicleNo", "LRNo", "LRDate", "PaymentTerms", "IncoTerms", "SiteAdd", "BilledToCode", "Billed<PERSON><PERSON><PERSON>ame", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BilledToStateName", "BilledToStateCode", "BilledToGSTIN", "BilledToPlaceOfSupply", "BilledToDescriptionOfGoods", "ShippedToCode", "ShipppedToName", "ShippedToAddress", "ShippedToStateName", "ShippedToGSTIN", "IGSTRate", "GrandTotalQTY(PC))", "GrandTotalAmount(SellingPrice(INR))", "GrandTotalFreight(INR)", "GrandTotalInsurance(INR)", "GrandTotalTaxableAmount(INR)", "GrandTotalIGSTAmount(INR)", "GrandTotalAmount(incl.tax)(INR)", "TCSRate", "TCSAmount", "TotalValue", "TotalInvoicevalue(InWords)", "Table"], "additionalProperties": false}}}