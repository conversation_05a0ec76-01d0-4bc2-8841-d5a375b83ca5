import sys
sys.path.append(".")
from sqlalchemy.orm import selectinload
from sqlalchemy.future import select
from sqlalchemy import select, or_, desc, asc, update
from fastapi import HTTPException
import traceback
from datetime import datetime
import datetime
from datetime import date
from config.db_config import AsyncSessionLocal
from src.Models.models import Plan, StripeUser, UserSubscription
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.user_Logs_Controller import CUserLogController
from config.db_config import AsyncSessionLocal
import os
from os.path import join, dirname
from dotenv import load_dotenv
from src.utilities.email_utils import CEmailer
from dateutil.relativedelta import relativedelta
import pytz
import json


# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")

load_dotenv(dotenv_path)

class CPaymentApp:

    mActiveLiveMode = bool(os.getenv('LIVEMODE') == "true")
    
    @staticmethod
    async def MSSetPlans(stripe_price_id, stripe_product_id, plan_name, plan_price, plan_currency, payment_type, plan_type, pageCount, description=None):
        """
        purpose :- Add a new plan to the database
        Input:- stripe_price_id (str), stripe_product_id (str), plan_name (str), plan_price (float), plan_currency (str), payment_type (str), plan_type (str), pageCount: int, description (str, optional)
        Output:- plan (Plan)
        Example:- await CPaymentApp.MSSetPlans("price_123", "prod_456", "Basic Plan", 9.99, "USD", "recurring", "monthly", 75, "This is a basic plan")
        """
        try:
            async with AsyncSessionLocal() as session:
                new_plan = Plan(
                    stripe_price_id=stripe_price_id, # unique plan id == stripe plan id
                    stripe_product_id=stripe_product_id,
                    plan_name=plan_name,
                    plan_price=plan_price,
                    plan_currency=plan_currency,
                    payment_type=payment_type,
                    plan_type=plan_type,
                    description=description,
                    pageCount =pageCount
                )
                session.add(new_plan)
                await session.commit()
                await session.refresh(new_plan)
                await CLogController.MSWriteLog(None, "Info", f"New plan created: {new_plan.plan_name}")
                return new_plan
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error adding plan: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while adding the plan")

    @staticmethod
    async def MSSetStripeUser(user_id, stripe_customer_id, stripeResponse:dict, bTestMode=False):
        """
        purpose :- Add a new Stripe user to the database
        Input:- user_id (int), stripe_customer_id (str)
        Output:- stripe_user (StripeUser)
        Example:- await CPaymentApp.MSSetStripeUser(1, "cus_12345")
        """
        try:
            async with AsyncSessionLocal() as session:
                # Check if user_id already exists in StripeUser table
                if not bTestMode:
                    result = await session.execute(
                        select(StripeUser).where(StripeUser.user_id == user_id)
                    )
                    existing_user = result.scalars().first()

                    if existing_user:
                        await CLogController.MSWriteLog(user_id, "Info", f"Stripe user already exists: {existing_user.stripe_customer_id}")
                        return existing_user

                # If user_id does not exist, add a new stripe user
                new_stripe_user = StripeUser(
                    user_id=user_id,
                    stripe_customer_id=stripe_customer_id,
                    stripeResponse=json.dumps(stripeResponse)
                )
                session.add(new_stripe_user)
                await session.commit()
                await session.refresh(new_stripe_user)
                await CLogController.MSWriteLog(user_id, "Info", f"New Stripe user created: {new_stripe_user.stripe_customer_id}")
                return new_stripe_user
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error adding stripe user: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while adding the Stripe user")

    @staticmethod
    async def MSSetUserSubscription(stripe_id, user_id, plan_id, subscription_id, plan_start_date, plan_end_date, payment_type, current_status,stripeResponse:dict):
        """
        purpose :- Add a new user subscription to the database
        Input:- stripe_id (int), user_id (int), plan_id (int), subscription_id (str), plan_start_date (datetime), plan_end_date (datetime), payment_type (str), current_status (str)
        Output:- user_subscription (UserSubscription)
        Example:- await CPaymentApp.MSSetUserSubscription(1, 1, 1, "sub_12345", datetime.utcnow(), datetime.utcnow() + timedelta(days=30), "recurring", "active")
        """
        try:
            async with AsyncSessionLocal() as session:
                new_subscription = UserSubscription(
                    stripe_id=stripe_id,
                    user_id=user_id,
                    plan_id=plan_id,
                    subscription_id=subscription_id,
                    plan_start_date=plan_start_date,
                    plan_end_date=plan_end_date,
                    payment_type=payment_type,
                    current_status=current_status,
                    stripeResponse=json.dumps(stripeResponse)
                )
                session.add(new_subscription)
                await session.commit()
                await session.refresh(new_subscription)
                await CLogController.MSWriteLog(user_id, "Info", f"New subscription created for user: {user_id}")
                return new_subscription
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error adding user subscription: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while adding the user subscription")

    @staticmethod
    async def MSGetPlans():
        """
        purpose :- Retrieve all plans from the database
        Input:- None
        Output:- plans (list of Plan)
        Example:- await CPaymentApp.MSGetPlans()
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(Plan))
                plans = result.scalars().all()
                await CLogController.MSWriteLog(None, "Info", "Retrieved all plans")
                return plans
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error retrieving plans: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the plans")
    

    @staticmethod
    async def MSGetPlanOnPriceID(price_id, bRaiseError=False):
        """
        purpose :- Retrieve a plan by its stripe_price_id from the database
        Input:- price_id (str)
        Output:- plan (Plan)
        Example:- await CPaymentApp.MSGetPlanOnPriceID("price_12345")
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(Plan)
                    .filter(
                        Plan.stripe_price_id == price_id,
                        Plan.LIVEMODE == CPaymentApp.mActiveLiveMode
                    )
                    .order_by(Plan.created_at.desc())
                )
                plan = result.scalars().first()
                if not plan:
                    if bRaiseError:
                        raise HTTPException(status_code=404, detail="Plan not found")
                    else:
                        return None
                await CLogController.MSWriteLog(None, "Info", f"Retrieved plan: {plan.plan_name} with price_id: {price_id}")
                return plan
        except HTTPException as http_exc:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise http_exc
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error retrieving plan: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the plan")
    
    @staticmethod
    async def MSGetPlanOnID(planId):
        """
        purpose :- Retrieve a plan by its id from the database
        Input:- planId (str)
        Output:- plan (Plan)
        Example:- await CPaymentApp.MSGetPlanOnPriceID("price_12345")
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(Plan)
                    .filter(
                        Plan.id == planId,
                        Plan.LIVEMODE == CPaymentApp.mActiveLiveMode
                    )
                )
                plan = result.scalars().first()
                if not plan:
                    raise HTTPException(status_code=404, detail="Plan not found")
                await CLogController.MSWriteLog(None, "Info", f"Retrieved plan: {plan.plan_name} with planId: {planId}")
                return plan
        except HTTPException as http_exc:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise http_exc
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error retrieving plan: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the plan")
    
    
    @staticmethod
    async def MSGetStripeUser(user_id, bRaiseError=False):
        """
        purpose :- Retrieve a Stripe user by user_id from the database
        Input:- user_id (int)
        Output:- stripe_user (StripeUser)
        Example:- await CPaymentApp.MSGetStripeUser(1)
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(StripeUser)
                    .filter(
                        StripeUser.user_id == user_id,
                        StripeUser.LIVEMODE == CPaymentApp.mActiveLiveMode
                    )
                    .order_by(StripeUser.created_at.desc())
                )
                stripe_user = result.scalars().first()
                if not stripe_user:
                    if bRaiseError:
                        raise HTTPException(status_code=404, detail="Stripe user not found")
                    else:
                        return None
                await CLogController.MSWriteLog(user_id, "Info", f"Retrieved Stripe user: {stripe_user.stripe_customer_id}")
                return stripe_user
        except HTTPException as http_exc:
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise http_exc
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error retrieving Stripe user: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the Stripe user")

    @staticmethod
    async def MSGetStripeUserByCustomerId(stripe_customer_id, bRaiseError=False):
        """
        Purpose: Retrieve the latest Stripe user by stripe_customer_id from the database
        Input: stripe_customer_id (str)
        Output: stripe_user (StripeUser)
        Example: await CPaymentApp.MSGetStripeUserByCustomerId("cus_123456789")
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(StripeUser)
                    .filter(StripeUser.stripe_customer_id == stripe_customer_id, StripeUser.LIVEMODE == CPaymentApp.mActiveLiveMode)
                    .order_by(StripeUser.created_at.desc())
                )
                stripe_user = result.scalars().first()
                if not stripe_user:
                    if bRaiseError:
                        raise HTTPException(status_code=404, detail="Stripe user not found")
                    else:
                        return None
                await CLogController.MSWriteLog(stripe_user.user_id, "Info", f"Retrieved Stripe user: {stripe_user.stripe_customer_id}")
                return stripe_user
        except HTTPException as http_exc:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise http_exc
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error retrieving Stripe user: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the Stripe user")
        
    @staticmethod
    async def MSGetUserSubscription(user_id, includeAll=False, payment_type="recurring"):
        """
        Purpose: Retrieve all subscriptions for a user by user_id from the database
        Input: user_id (int), includeAll (bool), payment_type (str)
        Output: subscriptions (list of UserSubscription)
        Example: await CPaymentApp.MSGetUserSubscription(1)
        """
        try:
            async with AsyncSessionLocal() as session:
                async with session.begin():
                    # Construct query with filters
                    query = select(UserSubscription).filter(
                        UserSubscription.user_id == user_id,
                        UserSubscription.LIVEMODE == CPaymentApp.mActiveLiveMode,
                        UserSubscription.payment_type == payment_type
                    )
                    if not includeAll:
                        query = query.order_by(UserSubscription.created_at.desc()).limit(1)

                    result = await session.execute(query)
                    subscriptions = result.scalars().all()

                    await CLogController.MSWriteLog(user_id, "Info", f"Retrieved subscriptions for user: {user_id}")
                    if not subscriptions:
                        return None
                    elif subscriptions and isinstance(subscriptions, list) and len(subscriptions) > 0 and not includeAll:
                        return subscriptions[0]
                    else:
                        return subscriptions
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error retrieving subscriptions: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving the subscriptions")

    @staticmethod
    async def cancel_latest_subscription(user_id, include_all=False):
        """
        Updates the `current_status` to False for the latest subscription of a user.

        Args:
            user_id (int): The ID of the user whose latest subscription needs to be cancelled.
            include_all (bool): If True, cancel all subscriptions for the user, otherwise only the latest one.

        Returns:
            bool: True if the update was successful, False otherwise.
        """
        try:
            async with AsyncSessionLocal() as session:
                async with session.begin():
                    # Construct query based on include_all
                    query = select(UserSubscription).filter(UserSubscription.user_id == user_id, UserSubscription.LIVEMODE == CPaymentApp.mActiveLiveMode)
                    if not include_all:
                        query = query.order_by(UserSubscription.created_at.desc()).limit(1)

                    result = await session.execute(query)
                    subscriptions = result.scalars().all()

                    if not subscriptions:
                        await CLogController.MSWriteLog(user_id, "Info", f"No subscriptions found for user: {user_id}")
                        return False

                    for subscription in subscriptions:
                        # Update current_status to False
                        stmt = (
                            update(UserSubscription)
                            .where(UserSubscription.id == subscription.id)
                            .values(current_status=False)
                        )
                        await session.execute(stmt)

                    await session.commit()
                    await CLogController.MSWriteLog(user_id, "Info", f"Cancelled subscription(s) for user: {user_id}")
                    return True

        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Error cancelling subscription(s): {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while cancelling the subscriptions")
        
    @staticmethod
    def convert_unix_to_date(unix_timestamp):
        """
        Convert a Unix timestamp to a date string in SQL datetime format (YYYY-MM-DD HH:MM:SS)
        in the US timezone.
        """
        # Convert to UTC datetime
        utc_date = datetime.datetime.fromtimestamp(unix_timestamp, pytz.utc)
        # Convert to US timezone
        us_date = utc_date.astimezone(pytz.timezone('Asia/Kolkata'))
        # Return in SQL datetime format
        return us_date.strftime('%Y-%m-%d %H:%M:%S')

    @staticmethod
    def calculate_topup_plan_end_date(unix_timestamp):
        """
        Convert a plan start date (Unix timestamp) to a date string in SQL datetime format (YYYY-MM-DD HH:MM:SS)
        in the US timezone, adding two years.
        """
        # Convert to UTC datetime
        plan_start_date_utc = datetime.datetime.fromtimestamp(unix_timestamp, pytz.utc)
        # Calculate end date by adding two years
        end_date_utc = plan_start_date_utc + relativedelta(years=2)
        # Convert to US timezone
        end_date_us = end_date_utc.astimezone(pytz.timezone('Asia/Kolkata'))
        # Return in SQL datetime format
        return end_date_us.strftime('%Y-%m-%d %H:%M:%S')
    
    @staticmethod
    async def MSCreateUserSubscription(stripeEvent, stripeResponse):
        """
        Purpose: Create a user subscription based on the Stripe event data
        Input: stripeEvent (str), stripeResponse (dict)
        Output: dict containing the status of the operation
        Example: await CPaymentApp.MSCreateUserSubscription(event_type, event_data)
        """
        user_id = None
        try:
            from src.Controllers.auth_controller import CAuthController
            # Extract meaningful data
            dictStripInvoiceData = CPaymentApp.MSExtractStripeResponse(stripeEvent, stripeResponse)
            
            stripe_customer_id = dictStripInvoiceData.get("customerID")
            plan_start_date = dictStripInvoiceData.get("periodStart")
            plan_end_date = dictStripInvoiceData.get("periodEnd")
            payment_type = dictStripInvoiceData.get("paymentType")
            subscription_id = dictStripInvoiceData.get("subscriptionID")
            current_status = dictStripInvoiceData.get("CurrentStatus")
            livemode = dictStripInvoiceData.get("livemode")
            quantity = dictStripInvoiceData.get("quantity")
            
            if ((CPaymentApp.mActiveLiveMode == livemode) and current_status):
                dictPlanData = await CPaymentApp.MSGetPlanOnPriceID(dictStripInvoiceData.get("priceID"), bRaiseError=True)
                plan_id = dictPlanData.id
                pageCount = int(quantity) * int(dictPlanData.pageCount)
                planName = dictPlanData.plan_name
                active_plan_type = dictPlanData.plan_type
                choosen_payment_type = dictPlanData.payment_type
                
                userData = await CAuthController.MSGetUserOnEmailId(dictStripInvoiceData.get("emailId"))
                user_id = userData.get("uid")
                
                StripeUserTable = await CPaymentApp.MSSetStripeUser(user_id=user_id, stripe_customer_id=stripe_customer_id, stripeResponse=stripeResponse)
                StripeId = StripeUserTable.id
                
                # Create a transaction for the user's Accuvelocity Premium service purchase
                StripUserSubscriptionTable = await CPaymentApp.MSSetUserSubscription(
                    stripe_id=StripeId, user_id=user_id, plan_id=plan_id,
                    subscription_id=subscription_id, plan_start_date=plan_start_date,
                    plan_end_date=plan_end_date, payment_type=payment_type,
                    current_status=current_status, stripeResponse=stripeResponse
                )
                
                if (choosen_payment_type.lower() == "recurring" and payment_type == "recurring"):
                    # Set subscription service benefits
                    await CAuthController.MSUpdateUserDetails(profilePicture=None,
                        user_id=user_id, current_user_id=user_id, page_limit_left=pageCount, pro_page_limit=pageCount,
                        active_plan_name=planName, is_subscription_active=True, bOverwrite=False, active_plan_type=active_plan_type
                    )
                    await CLogController.MSWriteLog(user_id, "INFO", "Subscription created successfully")
                    emailer = CEmailer(iUserID=user_id)
                    await emailer.send_email(
                        to=[userData.get("email")],
                        template_name='SubscriptionOnboarding',
                        template_data={
                            'customer_name': userData.get("name"),
                            'plan_name': planName,
                            'premium_feature_1':'Custom Model',
                            'advantage_of_premium_feature_1': f"Unlimited custom models to tailor the experience to your specific needs.",
                            'premium_feature_2': "PageLimit",
                            'advantage_of_premium_feature_2': f"Upto {pageCount} Pages, giving you the flexibility to handle extensive content.",
                        }
                    )
                    await CUserLogController.MSWriteLog(user_id, "Info", f"You are now subscribed to the {dictPlanData.plan_name} Premium plan on a {active_plan_type} basis, costing ${float(dictPlanData.plan_price):.2f}. This subscription includes {pageCount} pages. Your subscription starts on {plan_start_date} EDT and will renew on {plan_end_date} EDT. Your unique subscription ID is {subscription_id}.", "Billing")
                else:
                    # Set top up plan page count when plan type is one_time
                    await CAuthController.MSUpdateUserDetails(profilePicture=None,
                        user_id=user_id, current_user_id=user_id, page_limit_left=pageCount, pro_page_limit=pageCount,
                        bOverwrite=False
                    )
                    await CLogController.MSWriteLog(user_id, "INFO", f"User ID {user_id} - Top Up Plan purchased successfully. Pages added: {pageCount}.")
                    Data = await CAuthController.MSGetSingleUser(user_id)
                    emailer = CEmailer(iUserID=user_id)
                    await emailer.send_email(
                        to=[userData.get("email")],
                        template_name='ToUpNotification',
                        template_data={
                            'User_Name': userData.get("name"),
                            'Top_Up_Date': StripUserSubscriptionTable.created_at,
                            'Total_Pages': pageCount,
                            'Current_Balance': Data.get('page_limit_left')
                        }
                    )
                    await CUserLogController.MSWriteLog(user_id, "Info", f"Your top-up plan has been successfully purchased! You are currently on the {dictPlanData.plan_name} Premium plan subscription. You have added {pageCount} pages at ${float(dictPlanData.plan_price):.2f} per page, totaling ${float(dictPlanData.plan_price) * float(pageCount):.2f}. Your purchase date is {plan_start_date} EDT.", "Billing")
                return {"status": True}
            else:
                await CUserLogController.MSWriteLog(user_id, "Info", f"Oops! We couldn't process your purchase of the premium subscription. Please reach out to our support <NAME_EMAIL> for assistance. We’re here to help!", "Billing")
                await CLogController.MSWriteLog(user_id, "warning", f"Live mode mismatch or invalid status: {livemode}, {current_status}")
                return {"status": False}
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"stripeEvent - {stripeEvent} ,\n StripeResponse {stripeResponse} , \n Error creating subscription: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Error", f"Error creating subscription: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="An error occurred while creating the subscription")
    
    @staticmethod
    async def MSCancelSubscription(stripeEvent, stripeResponse):
        """
        Purpose: Cancel a user subscription based on the Stripe event data
        Input: stripeEvent (str), stripeResponse (dict)
        Output: dict containing the status of the operation
        Example: await CPaymentApp.MSCancelSubscription(event_type, event_data)
        """
        user_id = None
        try:
            from src.Controllers.auth_controller import CAuthController
            # Extract meaningful data
            dictStripInvoiceData = CPaymentApp.MSExtractStripeResponse(stripeEvent, stripeResponse)
            stripe_customer_id = dictStripInvoiceData.get("customerId")
            subscriptionId = dictStripInvoiceData.get("subscriptionID")
            livemode = dictStripInvoiceData.get("livemode")
            status = dictStripInvoiceData.get("status")
            
            await CLogController.MSWriteLog(user_id, "INFO", f"Cancellation details: {dictStripInvoiceData.get('cancellation_details')}")
            # need to verfiy status in  cancelled,  
            if CPaymentApp.mActiveLiveMode == livemode:
                stripUserTable = await CPaymentApp.MSGetStripeUserByCustomerId(stripe_customer_id)
                user_id = stripUserTable.user_id
                
                await CLogController.MSWriteLog(user_id, "INFO", f"Subscription Status: {status}")
                
                if status in ["unpaid", "incomplete_expired", "incomplete", "canceled", "past_due"]: #WARN: remove this "active" it not required
                    await CAuthController.MSUpdateUserDetails(profilePicture=None,
                        user_id=user_id, current_user_id=user_id, # page_limit_left=0, pro_page_limit=0,
                        active_plan_name="Free", is_subscription_active=False,bOverwrite=True,
                    )
                    # Current Subscription status -> Disableed
                    await CPaymentApp.cancel_latest_subscription(user_id)
                await CLogController.MSWriteLog(user_id, "INFO", "Subscription canceled successfully")
                return {"status": status}
            else:
                await CLogController.MSWriteLog(user_id, "warning", f"Live mode mismatch: {livemode}")
                return {"status": status}
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"stripeEvent - {stripeEvent} ,\n StripeResponse {stripeResponse} , \n Error - Cancelling User Subscription : {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="An error occurred while canceling the subscription")

    
    @staticmethod
    async def MSSetStripeUserOnEvent(stripeEvent, stripeResponse):
        """
        Purpose: Set Stripe user based on the Stripe event customer.created data
        Input: stripeEvent (str), stripeResponse (dict)
        Output: dict containing the status of the operation
        Example: await CPaymentApp.MSSetStripeUserOnEvent(event_type, event_data)
        """
        user_id = None
        try:
            from src.Controllers.auth_controller import CAuthController
            # Extract meaningful data
            dictStripInvoiceData = CPaymentApp.MSExtractStripeResponse(stripeEvent, stripeResponse)
            stripe_customer_id = dictStripInvoiceData.get("customerId")
            stripObj = dictStripInvoiceData.get("object")
            livemode = dictStripInvoiceData.get("livemode")
            emailId = dictStripInvoiceData.get("emailId")
            
            if CPaymentApp.mActiveLiveMode == livemode:
                userTable = await CAuthController.MSGetUserOnEmailId(EmailID=emailId)
                user_id = userTable.get("uid")
                
                await CPaymentApp.MSSetStripeUser(user_id, stripe_customer_id, stripeResponse=stripeResponse)
                await CLogController.MSWriteLog(user_id, "INFO", "Stripe user set successfully")
                return {"status": True}
            else:
                await CLogController.MSWriteLog(user_id, "warning", f"Live mode mismatch or invalid object: {livemode}, {stripObj}")
                return {"status": False}
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"stripeEvent - {stripeEvent} ,\n StripeResponse {stripeResponse} , \n Error - setting Stripe user: {str(e)}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="An error occurred while setting the Stripe user")
    
    @staticmethod
    def MSExtractStripeResponse(stripeEvent, stripeResponse):
        """
        Purpose: Extract meaningful data from the Stripe event response
        Input: stripeEvent (str), stripeResponse (dict)
        Output: dict containing the extracted data
        Example: extracted_data = CPaymentApp.MSExtractStripeResponse(event_type, event_data)
        """
        extractResponse = {}
        # Strip Customer Create
        if stripeEvent == "customer.created":
            extractResponse["customerId"] = stripeResponse.get("id")
            extractResponse["object"] = stripeResponse.get("object")
            extractResponse["emailId"] = stripeResponse.get("email")
            extractResponse["livemode"] = stripeResponse.get("livemode")
            extractResponse["name"] = stripeResponse.get("name")
        # Cancelled Event
        elif stripeEvent in ["subscription_schedule.canceled","customer.subscription.deleted","customer.subscription.paused","subscription_schedule.aborted"]:
            extractResponse["subscriptionID"] = stripeResponse.get("id")
            extractResponse["customerId"] = stripeResponse.get("customer")
            extractResponse["object"] = stripeResponse.get("object")
            extractResponse["livemode"] = stripeResponse.get("livemode")
            extractResponse["status"] = stripeResponse.get("status")
            extractResponse["cancellation_details"] = stripeResponse.get("cancellation_details",{})
        # Successful Event
        elif stripeEvent == "subscription_schedule.completed":
            first_item = stripeResponse["items"]["data"][0]
            extractResponse["subscriptionID"] = first_item.get("subscription")
            extractResponse["productID"] = first_item["price"].get("product")
            extractResponse["priceID"] = first_item["price"].get("id")
            extractResponse["paymentType"] = first_item["price"].get("type")
            period_end = first_item["current_period_end"]
            period_start = first_item["current_period_start"]
            extractResponse["customerID"] = stripeResponse.get("customer")
            extractResponse["quantity"] = first_item.get("quantity")
            extractResponse["periodStart"] = CPaymentApp.convert_unix_to_date(period_end)
            extractResponse["periodEnd"] = CPaymentApp.convert_unix_to_date(period_start)
            extractResponse["livemode"] = stripeResponse.get("livemode")
            extractResponse["CurrentStatus"] = bool(stripeResponse.get("status") == "active")
        elif stripeEvent == "invoice.payment_succeeded":
            first_item = stripeResponse["lines"]["data"][0]
            extractResponse["subscriptionID"] = first_item.get("subscription")
            extractResponse["productID"] = first_item["price"].get("product")
            extractResponse["priceID"] = first_item["price"].get("id")
            extractResponse["paymentType"] = first_item["price"].get("type")
            period_end = first_item["period"].get("end")
            period_start = first_item["period"].get("start")
            extractResponse["emailId"] = stripeResponse.get("customer_email")
            extractResponse["quantity"] = first_item.get("quantity")
            extractResponse["customerID"] = stripeResponse.get("customer")
            extractResponse["periodStart"] = CPaymentApp.convert_unix_to_date(period_start)
            extractResponse["periodEnd"] =  CPaymentApp.calculate_topup_plan_end_date(period_end) if first_item["price"].get("type") == "one_time" else CPaymentApp.convert_unix_to_date(period_end)
            extractResponse["livemode"] = stripeResponse.get("livemode")
            extractResponse["CurrentStatus"] = bool((stripeResponse.get("status") == "paid" or stripeResponse.get("status") == "succeeded") and stripeResponse.get("paid"))
        
        return extractResponse

if __name__ == "__main__":
    current_period_end=1723203981
    current_period_start= 1720525581
    print(CPaymentApp.convert_unix_to_date(current_period_end)," ", CPaymentApp.convert_unix_to_date(current_period_start))