import { useState, useEffect } from "react";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { IoMdEyeOff, IoMdEye } from "react-icons/io";
import { Link } from 'react-router-dom';

const UpdatePassword = () => {
 const [currentPassword, setCurrentPassword] = useState("");
 const [newPassword, setNewPassword] = useState("");
 const [confirmNewPassword, setConfirmNewPassword] = useState("");
 const [showCurrentPassword, setShowCurrentPassword] = useState(false);
 const [showNewPassword, setShowNewPassword] = useState(false);
 const [showConfirmNewPassword, setShowConfirmNewPassword] = useState(false);

 const [passwordCriteria, setPasswordCriteria] = useState({
    minLength: false,
    oneUppercase: false,
    oneNumber: false,
    oneSymbol: false,
 });

 const [passwordChecks, setPasswordChecks] = useState({
    notSameAsOld: false,
    matchesConfirm: false,
 });

 // New state to track if all conditions are met
 const [allConditionsMet, setAllConditionsMet] = useState(false);

 useEffect(() => {
    const minLength = newPassword.length >= 8;
    const oneUppercase = /[A-Z]/.test(newPassword);
    const oneNumber = /\d/.test(newPassword);
    const oneSymbol = /[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(newPassword);
    const notSameAsOld = currentPassword !== newPassword;
    const matchesConfirm = newPassword === confirmNewPassword  && newPassword && confirmNewPassword;

    setPasswordCriteria({
      minLength,
      oneUppercase,
      oneNumber,
      oneSymbol,
    });

    setPasswordChecks({
      notSameAsOld,
      matchesConfirm,
    });

    // Update allConditionsMet based on the new criteria and checks
    setAllConditionsMet(minLength && oneUppercase && oneNumber && oneSymbol && notSameAsOld && matchesConfirm);
 }, [currentPassword, newPassword, confirmNewPassword]);

 const toggleCurrentPasswordVisibility = () => setShowCurrentPassword(!showCurrentPassword);
 const toggleNewPasswordVisibility = () => setShowNewPassword(!showNewPassword);
 const toggleConfirmNewPasswordVisibility = () => setShowConfirmNewPassword(!showConfirmNewPassword);

 const handleSubmit = async (e) => {
    e.preventDefault();
    try {
      const token = localStorage.getItem('token');
      const response = await axios.post(`${import.meta.env.VITE_SERVER}/update-password`, {
        "CurrentPassword": currentPassword,
        "UpdatedPassword": newPassword
      }, {
        headers: {
            "Authorization": `Bearer ${token}`
        }
      });

      if (response.status === 200) {
        toast.success("Password updated successfully!");
        window.location.href = "/profile";
      }
    } catch (error) {
      if (error.response && error.response.data && error.response.data.detail) {
          toast.error(error.response.data.detail);
      } else {
          toast.error("An unexpected error occurred"); // Fallback message
      }
      console.error("Login failed:", error);
  }
 };

 const criteriaTextColor = (isValid) => (isValid ? 'text-green-500' : 'text-gray-500');
 const renderTick = (isValid) => (isValid ? '✔' : '✘');

  return(
     <div style={{overflow:'hidden'}} className='max-h-[95vh]'>
    <div className="px-6 py-4">
      <h1 className="text-sm font-semibold text-[#707EAE]  hover:underline"><Link to="/profile">Profile</Link> / Update Password</h1>
    </div>
    <div className="min-h-screen flex flex-col justify-center items-center">
      <Toaster position="top-center" />
      <div className="w-full max-w-md">
        <form className="space-y-6" onSubmit={handleSubmit}>
          {/* Current Password */}
          <div className="relative">
            <label htmlFor="currentPassword" className="block text-sm sm:text-base font-medium font-inter text-gray-800 mb-1">
              Current Password<span className="text-red-500">*</span>
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <input
                type={showCurrentPassword ? 'text' : 'password'}
                className="block w-full p-3 border border-r-0 border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={currentPassword}
                onChange={(e) => {
                  const trimmedValue = e.target.value.replace(/\s/g, ''); // Remove spaces from the input
                  setCurrentPassword(trimmedValue);
                }}
                required
                pattern="^\S+$" // Pattern disallowing spaces
                title="Password cannot contain spaces" // Error message for invalid pattern
              />
              {/* Toggle Current Password visibility */}
              <button
                type="button"
                onClick={toggleCurrentPasswordVisibility}
                className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3">
                {showCurrentPassword ? (
                  <IoMdEyeOff className="h-5 w-5 text-gray-600" />
                ) : (
                  <IoMdEye className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>
          </div>

          {/* New Password */}
          <div className="relative">
            <label htmlFor="newPassword" className="block text-sm sm:text-base font-medium font-inter text-gray-800 mb-1">
              New Password<span className="text-red-500">*</span>
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <input
                type={showNewPassword ? 'text' : 'password'}
                className="block w-full p-3 border border-r-0 border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={newPassword}
                onChange={(e) => {
                  const trimmedValue = e.target.value.replace(/\s/g, ''); // Remove spaces from the input
                  setNewPassword(trimmedValue);
                }}
                required
                pattern="^\S+$" // Pattern disallowing spaces
                title="Password cannot contain spaces" // Error message for invalid pattern
              />
              {/* Toggle New Password visibility */}
              <button
                type="button"
                onClick={toggleNewPasswordVisibility}
                className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3">
                {showNewPassword ? (
                  <IoMdEyeOff className="h-5 w-5 text-gray-600" />
                ) : (
                  <IoMdEye className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>
          </div>

          {/* Confirm New Password */}
          <div className="relative">
            <label htmlFor="confirmNewPassword" className="block text-sm sm:text-base font-medium font-inter text-gray-800 mb-1">
              Confirm New Password<span className="text-red-500">*</span>
            </label>
            <div className="mt-1 flex rounded-md shadow-sm">
              <input
                type={showConfirmNewPassword ? 'text' : 'password'}
                className="block w-full p-3 border border-r-0 border-gray-300 rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                value={confirmNewPassword}
                onChange={(e) => {
                  const trimmedValue = e.target.value.replace(/\s/g, ''); // Remove spaces from the input
                  setConfirmNewPassword(trimmedValue);
                }}
                required
                pattern="^\S+$" // Pattern disallowing spaces
                title="Password cannot contain spaces" // Error message for invalid pattern
              />
              {/* Toggle Confirm New Password visibility */}
              <button
                type="button"
                onClick={toggleConfirmNewPasswordVisibility}
                className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3">
                {showConfirmNewPassword ? (
                  <IoMdEyeOff className="h-5 w-5 text-gray-600" />
                ) : (
                  <IoMdEye className="h-5 w-5 text-gray-600" />
                )}
              </button>
            </div>
          </div>

          {/* Password Criteria Checks */}
          <div className="pl-5 mt-2">
            <span className={criteriaTextColor(passwordCriteria.minLength)}>{renderTick(passwordCriteria.minLength)} At least 8 characters</span><br />
            <span className={criteriaTextColor(passwordCriteria.oneUppercase)}>{renderTick(passwordCriteria.oneUppercase)} At least one uppercase letter</span><br />
            <span className={criteriaTextColor(passwordCriteria.oneNumber)}>{renderTick(passwordCriteria.oneNumber)} At least one number</span><br />
            <span className={criteriaTextColor(passwordCriteria.oneSymbol)}>{renderTick(passwordCriteria.oneSymbol)} At least one symbol</span><br />
            <span className={criteriaTextColor(passwordChecks.notSameAsOld)}>{renderTick(passwordChecks.notSameAsOld)} Different from Current Password</span><br />
          </div>

          {/* Submit Button */}
          <div className="flex items-center justify-center">
            <button
              className={`bg-primary hover:bg-[#002744] w-full text-white font-bold py-2 px-4 rounded focus:outline-none focus:shadow-outline ${allConditionsMet ? '' : 'opacity-50 cursor-not-allowed'}`}
              type="submit"
              disabled={!allConditionsMet}
            >
              Update Password
            </button>
          </div>
        </form>
      </div>
    </div>
  </div>
  );
};

export default UpdatePassword;