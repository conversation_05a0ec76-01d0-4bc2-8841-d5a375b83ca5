from pydantic import BaseModel
from typing import Optional, Dict, Any, Union
from src.Models.models import TallyStatusEnum
from enum import Enum
# Id = Column(Integer, primary_key=True)
# TallyTemplateID = Column(Integer, ForeignKey('tally_template.Id', ondelete="CASCADE"), nullable=False)
# UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
# AuthKey = Column(String(255), nullable=False)
# TemplateKey = Column(Enum('1', '2', '3', '4', '5', '7', '8',
#                     '10', '11', '12', '13', '15', '16', '18'), nullable=False)
# CompanyName = Column(String(50), nullable=False)
# AddAutoMaster = Column(Enum('0', '1'), nullable=True)  
# AutoMasterIds = Column(String(20), nullable=True)   # Can  add multiple auto master id
# Version = Column(Enum('1', '3'), nullable=False)

# ✨ KEEP the enum as-is
class ENetworkLocationUpdateMode(str, Enum):
    APPEND  = "append"
    REPLACE = "replace"
    
class VoucherType(str, Enum):
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"

class AddTallyHeader(BaseModel):
    TallyTemplateID: int
    AuthKey: str
    TemplateKey: str
    CompanyName: str
    Version: str
    AddAutoMaster: Optional[str] = None 
    AutoMasterIds: Optional[str] = None 

class AddTallyBody(BaseModel):
    HeaderID:int
    DocID:int 
    BodyData:dict

class UpdateTallyHeader(BaseModel):
    TallyTemplateID: Optional[int] = None
    AuthKey: Optional[str] = None
    TemplateKey: Optional[str] = None
    CompanyName: Optional[str] = None
    Version: Optional[str] = None
    AddAutoMaster: Optional[str] = None
    AutoMasterIds: Optional[str] = None


# Define Pydantic schema to parse the JSON body
class TallyDocRecordUpdate(BaseModel):
    doc_id: int
    tally_api_req: Optional[Dict[str, Any]] = None
    tally_status: Optional[str] = None
    req_date_time: Optional[str] = None  # Assuming it's a string or adjust to correct format
    tally_api_resp: Optional[Dict[str, Any]] = None
    DocErrorMsg : Optional[str] = None
    resp_date_time: Optional[str] = None  # Assuming it's a string or adjust to correct format

# Define Pydantic schema to parse the JSON body
class TallyModelConfig(BaseModel):
    templateName: str
    companyName: str
    TransactionType: str
    modelId: int
    mappings: Dict[str, Any]
    GSTIN: Optional[str] = None


class TallyUserConfig(BaseModel):
    auth_key: str
    tally_enable: bool
    tally_voucher_type_config: Dict[str, Any] 
    tally_ledger_config: Dict[str, Any] 
    tally_stock_item_config: Dict[str, Any] 
    

class TallyModelConfig(BaseModel):
    templateName: str
    companyName: str
    modelId: int
    mappings: Optional[Dict] = None  # Assuming mappings is a dictionary
    GSTIN: Optional[str] = None
    extrafields: Optional[Dict] = None  # Assuming extrafields is a dictionary
    tally_enable: bool = False
    tally_ledger_config:Optional[Dict] = None
    tally_voucher_type_config: Optional[Dict] = None
    tally_stock_item_config: Optional[Dict] = None
    
class TallyStockItemObj(BaseModel):
    ItemDescription: str
    ItemQualityCode:str
    StockItemPrefix:str = "SIMPOLO TILES "
    strPcs: Optional[str] = None
    strDimension : Optional[str] = None

class sanitaryStockItemReqObj(BaseModel):
    ItemDescription: str
    ItemMaterialCode:Optional[str]
    StockItemPrefix:str = "H-" # Hansgrohe 

class chemicalStockItemReqObj(BaseModel):
    ItemDescription: str
    ItemWeight:Optional[int]
    ItemWeightUnit:Optional[str]
    StockItemPrefix:str = "PG-" # Powergrace 

class ParagTradersStockItemCreation(BaseModel):
    simpoloItemCreation:Optional[bool] = False   # Enable to create stock item if not exist in customer stock item
    nexionItemCreation:Optional[bool] = False
    hangroheItemCreation:Optional[bool] = False
    kohlerItemCreation:Optional[bool] = False
    geberitItemCreation:Optional[bool] = False
    aquantItemCreation:Optional[bool] = False # As Per Shubhum, We have turn off this Item Creation as Other Important Details Not Adaptable to filled such as Item Group, Category, custom Fields, etc
    totoItemCreation:Optional[bool] = False
    iconItemCreation:Optional[bool] = False

class GPTResponseResObj(BaseModel):
    GPTResponseDict: Optional[dict] = None
    GPTResponseObjFile: Optional[str] = None
    GPTResponseDictFile: Optional[str] = None

class TallyStockItemColumn(BaseModel):
    # this object is to store gpt api extraction keys name 
    Location: Optional[str] = None
    Name: Optional[str] = None
    BatchName:Optional[str] = None
    Discount:Optional[str] = None
    Qty:Optional[str] = None
    Rate: Optional[str] = None 
    Amount: Optional[str] = None 

class PTAdditionalChargesObj(BaseModel):
    CashDiscountCharge : Optional[str] = None
    InsuranceCharge : Optional[str] = None
    PAndFOtherCharge : Optional[str] = None
    SpecialProductDiscount: Optional[str] = None
    DisplayDiscount: Optional[str] = None
    FrieghtCharges: Optional[Union[str, int, float]] = None  # Allows both str and int
    TCSCharges : Optional[str] = None
    

class TallyRoundOffAmountObj(BaseModel):
    # this object is to calculate round off from gpt api extraction keys name 
    TotalAmount: Optional[str] = None
    ItemAmount: Optional[str] = None
    IGSTRate: Optional[str] = None
    IGSTAmount:Optional[str] = None
    CGSTRate:Optional[str] = None
    CGSTAmount: Optional[str] = None
    SGSTRate: Optional[str] = None
    SGSTAmount:Optional[str] = None
    objAdditionalCharges: Optional[PTAdditionalChargesObj] = None

class TallyAPIHeaderObj(BaseModel):
    AUTHKEY:Optional[str] = None
    TemplateKey: Optional[str] = None
    CompanyName: Optional[str] = None
    AddAutoMaster: Optional[str] = None
    Automasterids: Optional[str] = None
    version:  Optional[str] = None


class TallyLedgerName(BaseModel):
    lsCustomerLedgerDetails :  Optional[list] = None
    strGSTNumber : Optional[str] = None
    strSellerState : Optional[str] = None
    strSellerCity : Optional[str] = None


class TallyPriceListReportResponse(BaseModel):
    # Both Sanitary , Tiles Columns
    strVendorName:Optional[str] = "-"
    strPriceListEffectiveStartDate:Optional[str]="-"
    strInvoiceNo:Optional[str] = "-"
    strInvoiceDate:Optional[str] = "-"
    strItemId:Optional[str] = "-"
    strItemName:Optional[str] = "-"
    strItemQty:Optional[str] = "-"
    strItemAmount:Optional[str] = "-"  # Item Rate * Qty
    strAccuvelocityComments:Optional[str]="-"
    strPriceListAmount:Optional[str] = "-"
    strMatchedItemProperty :Optional[str] = "-"
    # Sanitary Columns
    strItemRate:Optional[str] = "-"      
    # strIsPriceMatchWithPriceList:Optional[str] = "-"
    strIsSanitaryItemRateMatch:Optional[str] = "-"

    # Tiles Columns
    strDiscount:Optional[str] = "-"
    strExFacAmount:Optional[str] = "-" 
    strPCSPerBox:Optional[str] = "-"
    strPriceListRatePerSqFt:Optional[str]="-"
    strPriceListCoverageAreaSqFt:Optional[str]="-"
    strItemRatePerBox:Optional[str] = "-"
    strItemRatePerSqFt:Optional[str] = "-"
    strIsPriceListRatePerBoxMatch:Optional[str] = "-"
    strIsPriceListRatePerSqFtMatch:Optional[str] = "-"
    # strIsMatchCalculatedPriceListAmount:Optional[str]="-"
    
class ICDPegasus(BaseModel):
    GPTExtractedData:Optional[dict] = {}
    dictCommonFields:Optional[dict] = {}
    strJobNumber:Optional[str] = ""
    strContainerNumber: Optional[str] = ""
    strAccountName: Optional[str] = ""
    lsAdditionalInfo: Optional[list] = []

class ClientDNReqObj(BaseModel):
    strQuotUniqueNumber: Optional[str] = ""
    strQuotItemSRNO: Optional[str] = ""
    strTallyClientName: Optional[str] = "AV Select Customer"
    strOrderTerms1 : Optional[str] = ""
    strOrderTerms2 : Optional[str] = ""
    # strIsNewClient: Optional[str] = "No"
    strClientAddress1: Optional[str] = ""
    strClientAddress2: Optional[str] = ""
    strClientAddress3: Optional[str] = ""
    strClientAddress4: Optional[str] = ""
    strClientState: Optional[str] = ""
    strClientCountry: Optional[str] = ""
    strClientPinCode: Optional[str] = ""
    strClientGSTRegistrationType: Optional[str] = "" # Composition | Regular | Unregistered / Consumer
    strClientGSTIN:  Optional[str] = ""
    strCartageAmount: Optional[str] = ""
    strQuotationDocName: Optional[str] = ""


class AVTallyXMLResponse(BaseModel):
    iDocID: int
    strClientRequestID: str
    strAVTallyStatus: Optional[TallyStatusEnum] = "NotProcess"
    strAVComments: Optional[str] = "-"
    strXMLFilePath: Optional[str] = None
    strUniqueDocNo: Optional[str] =None
    strDocDate: Optional[str] = None
    strResponseDate: Optional[str] = None
    strDocErrorMsg: Optional[str] = None
    strTallyPrimeAPIReq : Optional[str] = None # Request Object , TODO: Discard once API Not Use
    strTallyPrimeAPIRes :Optional[str] = None # Response Object
