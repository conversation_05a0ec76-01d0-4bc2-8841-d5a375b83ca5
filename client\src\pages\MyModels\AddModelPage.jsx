import { useEffect, useState, useRef } from "react";
import { useLocation, useNavigate } from "react-router-dom";
import { Link } from "react-router-dom";
import axios from "axios";
import toast, { Toaster } from "react-hot-toast";
import { Formik, Form, Field, FieldArray } from "formik";
import { LuTable } from "react-icons/lu";
import { IoMdAddCircleOutline } from "react-icons/io";
import { BiEditAlt } from "react-icons/bi";
import { BsThreeDotsVertical } from "react-icons/bs";
import categoryData from "../../../../resource/Category_N_Formats.json"; // Path to your JSON file
import { MdDelete } from "react-icons/md";
import { MdOutlineContentCopy } from "react-icons/md";
import { CiCircleInfo } from "react-icons/ci";
import { Tooltip } from "@material-tailwind/react";
import { FaRegSave } from "react-icons/fa";
import { MdOutlineCancel } from "react-icons/md";
import PropTypes from 'prop-types';

const ModelAddPage = ({ isOpen }) => {
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(true);
    const location = useLocation();
    const { state } = location;
    const [vName, setVName] = useState("");
    const [vFamilyName, setVFamilyName] = useState("");
    const [vDesc, setVDesc] = useState("");
    const iModelId = state?.iModelId || "";
    const [data, setData] = useState({ Fields: [], Tables: [] });
    const [isEditingNameNFamily, setIsEditingNameNFamily] = useState(false);

    const [originalValues, setOriginalValues] = useState({
        vFamilyName: vFamilyName,
        vName: vName,
        vDesc: vDesc
    });

    const [dropdownPosition, setDropdownPosition] = useState({ top: 0, left: 0 });
    const [dropdownOpenIndex, setDropdownOpenIndex] = useState(null);
    const [dropdownTimeout, setDropdownTimeout] = useState(null);

    const [clickedRowIndex, setClickedRowIndex] = useState(null);
    const [clickedRowDupIndex, setClickedRowDupIndex] = useState(null);
    const [clickedColumnIndex, setClickedColumnIndex] = useState(null);

    const columnRefs = useRef([]);

    useEffect(() => {
        const handleBeforeUnload = (event) => {
            event.preventDefault();
            event.returnValue = ''; // Display a confirmation dialog
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, []);

    const handleVendorNameChange = (e, setFieldValue) => {
        let input = e.target.value.replace(/[^a-zA-Z\s0-9]/g, ''); // Remove non-alphabetic characters
        let truncatedInput = input.slice(0, 50);
        setFieldValue('vName', truncatedInput);
    };

    const handleVendorFamilyNameChange = (e, setFieldValue) => {
        let input = e.target.value.replace(/[^a-zA-Z\s]/g, '');
        let truncatedInput = input.slice(0, 50);
        setFieldValue('vFamilyName', truncatedInput);
    };

    const handleVendorDescChange = (e, setFieldValue) => {
        if (e.target.value.length <= 200) {
            setFieldValue('vDesc', e.target.value);
        }
    };

    const handleUpdateFamilyName = async (values) => {
        if (
            originalValues.vName.trim() !== values.vName.trim() ||
            originalValues.vFamilyName.trim() !== values.vFamilyName.trim() ||
            originalValues.vDesc.trim() !== values.vDesc.trim()
        ) {
            if (values.vFamilyName.trim() && values.vName.trim() && values.vDesc.trim()) {
                try {
                    const response = await axios.put(
                        `${import.meta.env.VITE_SERVER}/Model/Model?iModelId=${iModelId}&strNewModelName=${values.vName.trim()}&strNewModelFamily=${values.vFamilyName.trim()}&strNewModelDesc=${encodeURIComponent(values.vDesc)}`, {},
                        {
                            headers: { Authorization: `Bearer ${localStorage.getItem("token")}` },
                        }
                    );

                    if (response.status === 200) {
                        setVName(values.vName.trim());
                        setVFamilyName(values.vFamilyName.trim());
                        setVDesc(values.vDesc);

                        // Set the original values after updating the state
                        setOriginalValues({
                            vName: values.vName.trim(),
                            vFamilyName: values.vFamilyName.trim(),
                            vDesc: values.vDesc.trim()
                        });

                        setData((prevData) => ({
                            ...prevData,
                            Fields: values.Fields,
                            Tables: values.Tables,
                        }));
                    } else {
                        toast.error("Failed to update the names, please try again later.");
                    }
                } catch (error) {
                    console.error("Error loading model data:", error);
                    toast.error("Failed to update the names, please try again later.");
                }
            } else {
                toast.error("ModelFamily, ModelName & Model Description can't be empty");
            }
        }
        setIsEditingNameNFamily(false);
    };

    const handleDuplicateTable = (tableIndex, setFieldValue, values) => {

        const tableToDuplicate = values.Tables[tableIndex];
        const newTable = {
            ...tableToDuplicate,
            TableName: `${tableToDuplicate.TableName} Copy`,
            Fields: tableToDuplicate.Fields.map(field => ({
                ...field
            }))
        };
        setFieldValue("Tables", [...values.Tables, newTable]);
    };

    const toggleDropdown = (index, event) => {
        if (dropdownOpenIndex === index) {
            setDropdownOpenIndex(null);
        } else {
            const rect = event.target.getBoundingClientRect();
            setDropdownPosition({ top: rect.top + window.scrollY + rect.height, left: rect.left + window.scrollX });
            setDropdownOpenIndex(index);
        }
    };

    const dropdownRef = useRef(null);

    const handleClickOutside = (event) => {
        if (
            dropdownRef.current &&
            !dropdownRef.current.contains(event.target) &&
            !event.target.closest('.dropdown-toggle-button')
        ) {
            setDropdownOpenIndex(null);
        }
    };

    const handleScroll = () => {
        setDropdownOpenIndex(null);
    };


    useEffect(() => {
        document.addEventListener("mousedown", handleClickOutside);
        document.addEventListener("scroll", handleScroll, true); // Use capture phase

        return () => {
            document.removeEventListener("mousedown", handleClickOutside);
            document.removeEventListener("scroll", handleScroll, true);
        };
    }, []);

    // Function to handle cancellation of editing
    const handleCancel = (setFieldValue) => {
        // Reset values to the original
        setFieldValue('vFamilyName', originalValues.vFamilyName);
        setFieldValue('vName', originalValues.vName);
        setFieldValue('vDesc', originalValues.vDesc);
        setIsEditingNameNFamily(false);
    };


    useEffect(() => {
        fetchData();
    }, []);

    const fetchData = async () => {
        try {
            const response = await axios.get(
                `${import.meta.env.VITE_SERVER}/Model/ModelFields?iModelId=${iModelId}&strModelName=${encodeURIComponent(vName)}`,
                {
                    headers: { Authorization: `Bearer ${localStorage.getItem("token")}` },
                }
            );
            if (Object.keys(response.data.ModelData).length === 0) {
                const Fields = [];
                const Tables = [];
                setData({ Fields, Tables });
            } else {
                const { Fields, Tables } = response.data.ModelData;
                setData({ Fields, Tables });
            }
            if (response?.data?.Model?.ModelName && response?.data?.Model?.FamilyName) {
                setVName(response.data.Model.ModelName);
                setVFamilyName(response.data.Model.FamilyName);
                setVDesc(response.data.Model.Description);

                // Set the original values after updating the state
                setOriginalValues({
                    vName: response.data.Model.ModelName,
                    vFamilyName: response.data.Model.FamilyName,
                    vDesc: response.data.Model.Description
                });
            }

            setIsLoading(false);
        } catch (error) {
            console.error("Error loading model data:", error);
            toast.error("Error loading model data.");
            setIsLoading(false);
        }
    };

    const handleFieldChange = (index, event, setFieldValue) => {
        const { name, value } = event.target;
        setFieldValue(name, value);

        // Check if the category is set to "Currency"
        if (name.includes('FieldCategory')) {
            if (value === 'Currency') {
                const formatFieldName = name.replace('FieldCategory', 'FieldFormat');
                setFieldValue(formatFieldName, '$ USD');
            }
            else if (value === 'Date') {
                const formatFieldName = name.replace('FieldCategory', 'FieldFormat');
                setFieldValue(formatFieldName, 'MM-DD-YYYY');
            }
        }
    };

    // Outside the ModelAddPage component definition
    const handleDeleteTable = (tableIndex, setFieldValue, values) => {
        if (window.confirm("Are you sure you want to delete this table?")) {
            // Update the Tables array in Formik's state by filtering out the table at tableIndex
            setFieldValue(
                "Tables",
                values.Tables.filter((table, index) => index !== tableIndex)
            );

            toast.success("Table deleted successfully!");
        }
    };

    const handleRemoveField = async (
        fieldId,
        tableIndex,
        fieldIndex,
        removeField
    ) => {
        if (window.confirm("Are you sure you want to remove this field?")) {
            try {
                if (fieldId === undefined) {
                    removeField(fieldIndex);
                } else {
                    const response = await axios.delete(
                        `${import.meta.env.VITE_SERVER}/Model/ModelFields?iModelId=${iModelId}&strModelName=${encodeURIComponent(vName)}`,
                        {
                            headers: {
                                Authorization: `Bearer ${localStorage.getItem("token")}`,
                            },
                            data: [fieldId],
                        }
                    );

                    if (response.status === 200) {
                        toast.success("Field deleted successfully!");
                        removeField(fieldIndex);
                    } else {
                        toast.error("Failed to delete field.");
                    }
                }
            } catch (error) {
                toast.error("Error deleting field.");
                console.error("Error deleting field:", error.response || error.message);
            }
        }
    };

    return (
        <>
            <div className="px-6 pt-2">
                <h1 className="text-sm font-semibold text-[#707EAE]">
                    <Link to="/ModelListPage" className="hover:underline">
                        Models / {vName}
                    </Link>
                </h1>
                <h1 className="text-3xl font-semibold text-[#3F3F3F]">{vName}</h1>
            </div>

            <Formik
                initialValues={{ ...data, vName, vFamilyName, vDesc }}
                enableReinitialize
                onSubmit={async (values, { setSubmitting }) => {

                    setSubmitting(true);

                    // Validation flags
                    let isValid = true;

                    // Check for Fields array
                    if (values.Fields.length === 0 && values.Tables.length === 0) {
                        toast.error("Please add some field or table.");
                        isValid = false;
                    }

                    values.Fields.forEach((field) => {
                        if (!field.FieldName || !field.FieldCategory) {
                            toast.error("Field name and category cannot be empty.");
                            isValid = false;
                        }
                        const category = categoryData.categories.find(
                            (cat) => cat.CategoryName === field.FieldCategory
                        );
                        if (category?.Formats.length && !field.FieldFormat) {
                            toast.error("Please select a format for fields requiring one.");
                            isValid = false;
                        }
                    });

                    // Check for Tables array
                    values.Tables.forEach((table) => {
                        if (!table.TableName) {
                            toast.error(
                                `Table name can't be empty.`
                            );
                            isValid = false;
                        }

                        if (table.Fields.length === 0) {
                            toast.error(
                                `Table ${table.TableName} can't be empty.`
                            );
                            isValid = false;
                        }

                        table.Fields.forEach((field) => {
                            if (!field.FieldName || !field.FieldCategory) {
                                toast.error(
                                    `Column name and category cannot be empty in ${table.TableName}.`
                                );
                                isValid = false;
                            }
                            const category = categoryData.categories.find(
                                (cat) => cat.CategoryName === field.FieldCategory
                            );
                            if (category?.Formats.length && !field.FieldFormat) {
                                toast.error(
                                    `Please select a format for Column requiring one in ${table.TableName}.`
                                );
                                isValid = false;
                            }
                        });
                    });

                    if (isEditingNameNFamily) {
                        toast.error(
                            `Please Save the Model Name, Model Family Name and Model Description Before Saving.`
                        );
                        isValid = false;
                    }
                    // If validation fails, do not submit data
                    if (!isValid) {
                        setSubmitting(false);
                        return;
                    }

                    let reqValues = {
                        "Fields": values.Fields,
                        "Tables": values.Tables
                    }
                    // If validation passes, submit data
                    try {
                        const response = await axios.post(
                            `${import.meta.env.VITE_SERVER
                            }/Model/ModelFields?strModelName=${vName}&strFamilyName=${vFamilyName}`,
                            reqValues,
                            {
                                headers: {
                                    "Content-Type": "application/json",
                                    Authorization: `Bearer ${localStorage.getItem("token")}`,
                                },
                            }
                        );
                        if (response.status === 200) {
                            toast.success("Model Data Saved Successfully!");
                            navigate(-1);
                        } else {
                            toast.error("Failed to Save Model Data.");
                        }
                    } catch (error) {
                        console.error(
                            "Error submitting data:",
                            error.response || error.message
                        );
                        toast.error("Failed to Save Model Data.");
                    }
                    setSubmitting(false);
                }}
            >
                {({ values, setFieldValue }) => (
                    <Form onKeyDown={(e) => {
                        if (e.key === 'Enter' && e.target.tagName !== 'TEXTAREA') {
                            e.preventDefault();
                        }
                    }}>
                        <div className="">
                            <div className="flex justify-between items-center pt-3 pb-3 pl-6 pr-6 text-xl font-bold bg-gray-300 ml-7 mr-7 mt-7 rounded-xl shadow-lg text-[#003654] relative">
                                <div className="flex">
                                    <div className="flex font-normal pl-2 pr-3">
                                        {isEditingNameNFamily ? (
                                            <>
                                                <div className="p-3"></div>
                                                <div className="fixed inset-0 flex items-center justify-center bg-black bg-opacity-50 z-40">
                                                    <div className="bg-white rounded-lg shadow-lg p-10 w-full max-w-xl">
                                                        <div className="grid grid-cols-1 gap-4">
                                                            <div className="flex items-center">
                                                                <div className="font-bold mr-2 w-1/3">
                                                                    Model Family:
                                                                </div>
                                                                <Field
                                                                    name="vFamilyName"
                                                                    component="input"
                                                                    defaultValue={vFamilyName}
                                                                    onChange={(e) => handleVendorFamilyNameChange(e, setFieldValue)}
                                                                    maxLength="50"
                                                                    placeholder="Enter Model Family Name"
                                                                    className="w-2/3 text-lg h-8 shadow appearance-none border rounded-lg py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                                />
                                                            </div>
                                                            <div className="flex items-center">
                                                                <div className="font-bold mr-2 w-1/3">
                                                                    Model Name:
                                                                </div>
                                                                <Field
                                                                    name="vName"
                                                                    component="input"
                                                                    defaultValue={vName}
                                                                    onChange={(e) => handleVendorNameChange(e, setFieldValue)}
                                                                    maxLength="50"
                                                                    placeholder="Enter Model Name"
                                                                    className="w-2/3 text-lg h-8 shadow appearance-none border rounded-lg py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                                />
                                                            </div>
                                                            <div className="flex items-center">
                                                                <div className="font-bold mr-2 w-1/3">
                                                                    Model Description:
                                                                </div>
                                                                <textarea
                                                                    name="vDesc"
                                                                    defaultValue={vDesc}
                                                                    onChange={(e) => handleVendorDescChange(e, setFieldValue)}
                                                                    placeholder="Enter Model Description(Character limit Upto 200)"
                                                                    className="w-2/3 text-lg shadow border rounded-lg py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                                    maxLength={200}
                                                                />
                                                            </div>
                                                            <div className="flex justify-end">
                                                                <button
                                                                    type="button"
                                                                    onClick={() => handleUpdateFamilyName(values)}
                                                                    className="bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-full mr-2"
                                                                >
                                                                    Save
                                                                </button>
                                                                <button
                                                                    type="button"
                                                                    onClick={() => handleCancel(setFieldValue)}
                                                                    className="bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-full"
                                                                >
                                                                    Cancel
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </div>
                                            </>
                                        ) : (
                                            <>
                                                <div className="flex mr-2">
                                                    <div className="font-bold mr-2 flex">
                                                        Model Family
                                                        <Tooltip content={
                                                            <div>
                                                                The category that best fits your document(e.g., Demo_Accounting, Demo_HR).<br />
                                                                Organizing your models into families makes it easier to manage and identify them.
                                                            </div>
                                                        }>
                                                            <div>
                                                                <CiCircleInfo className="ml-1 mt-1  text-xl" />
                                                            </div>
                                                        </Tooltip>
                                                    </div>
                                                    <span className={`whitespace-nowrap ${isOpen ? "w-52" : "w-58"} overflow-hidden text-ellipsis`}>
                                                        {originalValues.vFamilyName}
                                                    </span>
                                                </div>
                                                |
                                                <div className="flex ml-2">
                                                    <div className="font-bold mr-2 flex">
                                                        Model Name
                                                        <Tooltip content={
                                                            <div>
                                                                The specific type of document you are uploading (e.g., Invoice, Receipt).<br /> This helps AccuVelocity apply the correct extraction model for accurate data processing.
                                                            </div>
                                                        }>
                                                            <div>
                                                                <CiCircleInfo className="ml-1 mt-1  text-xl" />
                                                            </div>
                                                        </Tooltip>
                                                    </div>
                                                    <span className={`whitespace-nowrap ${isOpen ? "w-40" : "w-58"} overflow-hidden text-ellipsis`}>
                                                        {originalValues.vName}
                                                    </span>
                                                </div>
                                                &nbsp;|
                                                <div className="flex ml-2 truncate">
                                                    <div className="font-bold mr-2 flex">
                                                        Model Description
                                                        <Tooltip content={
                                                            <div className="w-80">
                                                                This section provides detailed information about the model,including its architecture, parameters, performance metrics, and usage guidelines.
                                                            </div>
                                                        }>
                                                            <div>
                                                                <CiCircleInfo className="ml-1 mt-1  text-xl" />
                                                            </div>
                                                        </Tooltip>
                                                    </div>
                                                    <span className={`whitespace-nowrap ${isOpen ? "w-64" : "w-[22rem]"} overflow-hidden text-ellipsis`}>
                                                        {originalValues.vDesc}
                                                    </span>
                                                </div>
                                                <button
                                                    onClick={() => setIsEditingNameNFamily(true)}
                                                    className="text-2xl hover:bg-blue-100 rounded-3xl ml-2"
                                                >
                                                    <BiEditAlt />
                                                </button>
                                            </>
                                        )}
                                    </div>
                                </div>

                                <div className="inline-block text-left absolute top-1/2 transform -translate-y-1/2 right-6">
                                    <div className="">
                                        <button
                                            type="button"
                                            onClick={() => {
                                                if (
                                                    window.confirm(
                                                        "Are you sure you want to discard all unsaved changes?"
                                                    )
                                                )
                                                    navigate(-1);
                                            }}
                                            className="mr-2 text-sm bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-3xl focus:outline-none focus:shadow-outline"
                                        >
                                            <Tooltip content="Click here to cancel and discard any changes made">
                                                <div className="flex">
                                                    <MdOutlineCancel className="text-lg mr-2" />
                                                    Cancel
                                                </div>
                                            </Tooltip>
                                        </button>

                                        <button
                                            type="submit"
                                            className="mr-2 text-sm bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-3xl focus:outline-none focus:shadow-outline"
                                        >
                                            <Tooltip content="Click here to save your changes and update your details.">
                                                <div className="flex">
                                                    <FaRegSave className="text-lg mr-2" />
                                                    Save
                                                </div>
                                            </Tooltip>
                                        </button>
                                    </div>
                                </div>
                            </div>

                            <div
                                className="p-6 bg-[#fff] ml-7 mr-7 mb-4 rounded-xl shadow-lg"
                                style={{
                                    minHeight: "calc(90vh - 150px)",
                                    maxHeight: "calc(90vh - 100px)",
                                    overflow: "auto",
                                }}
                            >
                                <Toaster position="top-center" />
                                <div className="mb-6">
                                    {/* Display the initial fields table */}
                                    <FieldArray name="Fields">
                                        {({ insert, remove, push }) => (
                                            <>
                                                <div className="max-h-[55vh] p-5 border-2 border-gray-300 rounded-3xl  bg-gray-200">
                                                    <div className="pb-3 text-[#003654]">
                                                        <span className="text-2xl font-bold mr-5">
                                                            Fields
                                                        </span>
                                                    </div>

                                                    <div className="overflow-auto max-h-[34vh]">

                                                        <table className="min-w-full leading-normal border-r-38 border-collapse">
                                                            <thead className="sticky top-0 z-10 ">
                                                                <tr className=" bg-[#C0C0C0] text-left text-[#003654]">
                                                                    <th className="text-sm px-5 py-2 border-2 border-gray-300 font-bold">
                                                                        <div className="flex">
                                                                            Field Name{" "}
                                                                            <span className=" text-red-500">*</span>
                                                                            <Tooltip content="Enter the name of the field you wish to extract data from.">
                                                                                <div>
                                                                                    <CiCircleInfo className="ml-2 text-xl" />
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    </th>

                                                                    <th className="text-sm px-5 py-2 border-2 border-gray-300 font-bold">
                                                                        <div className="flex">
                                                                            Category{" "}
                                                                            <span className="text-red-500">*</span>
                                                                            <Tooltip content="Specify the category to organize your data accordingly.">
                                                                                <div>
                                                                                    <CiCircleInfo className="ml-2 text-xl" />
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    </th>
                                                                    <th className="text-sm px-5 py-2 border-2 border-gray-300 font-bold">
                                                                        <div className="flex">
                                                                            Format
                                                                            <Tooltip content="Define the format for the selected category to ensure data consistency.">
                                                                                <div>
                                                                                    <CiCircleInfo className="ml-2 text-xl" />
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    </th>
                                                                    <th className="text-sm  px-5 py-2 border-2 border-gray-300 font-bold">
                                                                        <div className="flex">
                                                                            Prompt Assist
                                                                            <Tooltip content="Guidance or assistance provided to help fetch the field correctly.">
                                                                                <div>
                                                                                    <CiCircleInfo className="ml-2 text-xl" />
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    </th>
                                                                    <th className="text-sm px-5 py-2 border-2 border-gray-300 font-bold">
                                                                        <div className="flex">
                                                                            Notes
                                                                            <Tooltip content="Additional comments or remarks regarding the data or field.">
                                                                                <div>
                                                                                    <CiCircleInfo className="ml-2 text-xl" />
                                                                                </div>
                                                                            </Tooltip>
                                                                        </div>
                                                                    </th>
                                                                    <th className=" text-sm px-5 py-2 border-2 border-gray-300 font-bold"></th>
                                                                </tr>
                                                            </thead>
                                                            <tbody>
                                                                {values?.Fields?.map((field, index) => (
                                                                    <tr
                                                                        key={index}
                                                                        className={`border-2 ${index % 2 === 0
                                                                            ? "bg-white"
                                                                            : "bg-[#F8F9FC]"
                                                                            } `}
                                                                        onClick={() => setClickedRowIndex(index)}
                                                                    >

                                                                        <td className="text-sm text-gray-700 justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedRowDupIndex(index)}>
                                                                            <Field
                                                                                as="textarea"
                                                                                name={`Fields[${index}].FieldName`}
                                                                                className={`h-10 ${index % 2 === 0
                                                                                    ? "bg-white"
                                                                                    : "bg-[#F8F9FC]"
                                                                                    } w-full border-0 px-3 resize-none overflow-auto py-2 align-middle `}
                                                                                value={field.FieldName}
                                                                                placeholder="Field Name"
                                                                            />
                                                                        </td>

                                                                        <td className="text-sm text-gray-700 justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedRowDupIndex(index)}>
                                                                            <Field
                                                                                as="select"
                                                                                name={`Fields[${index}].FieldCategory`}
                                                                                className={`h-10 ${index % 2 === 0
                                                                                    ? "bg-white"
                                                                                    : "bg-[#F8F9FC]"
                                                                                    } w-full border-0 px-3 resize-none overflow-auto`}
                                                                                value={field.FieldCategory}
                                                                                placeholder="Field Category"
                                                                                onChange={(event) => handleFieldChange(index, event, setFieldValue)}
                                                                            >
                                                                                <option disabled value="">
                                                                                    Select Category
                                                                                </option>
                                                                                {categoryData.categories.map(
                                                                                    (category, index) => (
                                                                                        <option
                                                                                            key={index}
                                                                                            value={category.CategoryName}
                                                                                        >
                                                                                            {category.CategoryName}
                                                                                        </option>
                                                                                    )
                                                                                )}
                                                                            </Field>
                                                                        </td>

                                                                        <td className="text-sm text-gray-700    justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedRowDupIndex(index)}>
                                                                            <Field
                                                                                as="select"
                                                                                name={`Fields[${index}].FieldFormat`}
                                                                                className={`h-10 ${index % 2 === 0
                                                                                    ? "bg-white"
                                                                                    : "bg-[#F8F9FC]"
                                                                                    } w-full border-0 px-3 resize-none overflow-auto disabled:text-gray-400`}
                                                                                value={field.FieldFormat}
                                                                                placeholder="Field Format"
                                                                                disabled={
                                                                                    !categoryData.categories.find(
                                                                                        (category) =>
                                                                                            category.CategoryName ===
                                                                                            values.Fields[index].FieldCategory
                                                                                    )?.Formats.length
                                                                                }
                                                                                title={
                                                                                    categoryData.categories.find(
                                                                                        (category) =>
                                                                                            category.CategoryName ===
                                                                                            values.Fields[index].FieldCategory
                                                                                    )?.Formats.length
                                                                                        ? "Please select a format"
                                                                                        : "No format required"
                                                                                }
                                                                            >
                                                                                {categoryData.categories.find(
                                                                                    (category) =>
                                                                                        category.CategoryName ===
                                                                                        values.Fields[index].FieldCategory
                                                                                )?.Formats.length ? (
                                                                                    <option disabled value="">
                                                                                        Select a format
                                                                                    </option>
                                                                                ) : (
                                                                                    <option disabled value="">
                                                                                        No format required
                                                                                    </option>
                                                                                )}

                                                                                {categoryData.categories
                                                                                    .find(
                                                                                        (category) =>
                                                                                            category.CategoryName ===
                                                                                            values.Fields[index].FieldCategory
                                                                                    )
                                                                                    ?.Formats.map((format, index) => (
                                                                                        <option key={index} value={format}>
                                                                                            {format}
                                                                                        </option>
                                                                                    ))}
                                                                            </Field>
                                                                        </td>

                                                                        <td className="text-sm text-gray-700    justify-center items-center whitespace-normal break-words border-2 border-gray-300"
                                                                            onClick={() => setClickedRowDupIndex(index)}
                                                                        >
                                                                            <Field
                                                                                as="textarea"
                                                                                name={`Fields[${index}].FieldDescription`}
                                                                                className={`h-10 ${index % 2 === 0
                                                                                    ? "bg-white"
                                                                                    : "bg-[#F8F9FC]"
                                                                                    } w-full border-0 px-3 resize-none overflow-auto py-2 align-middle`}
                                                                                value={field.FieldDescription}
                                                                                placeholder="Field Description"
                                                                            />
                                                                        </td>

                                                                        <td className="text-sm text-gray-700  justify-center items-center whitespace-normal break-words border-2 border-gray-300"
                                                                            onClick={() => setClickedRowDupIndex(index)}
                                                                        >
                                                                            <Field
                                                                                as="textarea"
                                                                                name={`Fields[${index}].FieldNotes`}
                                                                                className={`h-10 ${index % 2 === 0
                                                                                    ? "bg-white"
                                                                                    : "bg-[#F8F9FC]"
                                                                                    } w-full border-0 px-3 resize-none overflow-auto py-2 align-middle`}
                                                                                value={field.FieldNotes}
                                                                                placeholder="Field Notes"
                                                                            />
                                                                        </td>

                                                                        <td className="max-w-[1em] text-sm relative px-5 py-3 border-2 border-gray-300">
                                                                            <button
                                                                                type="button"
                                                                                onClick={(event) => toggleDropdown(index, event)}
                                                                                className="text-[#003654] dropdown-toggle-button"
                                                                            >
                                                                                <BsThreeDotsVertical />
                                                                            </button>
                                                                        </td>
                                                                    </tr>
                                                                ))}
                                                            </tbody>
                                                        </table>

                                                        <div
                                                            ref={dropdownRef}
                                                            style={{
                                                                position: 'absolute',
                                                                top: dropdownPosition.top,
                                                                left: dropdownPosition.left,
                                                                display: dropdownOpenIndex !== null ? 'block' : 'none',
                                                                zIndex: 10,
                                                            }}
                                                            onMouseEnter={() => clearTimeout(dropdownTimeout)}
                                                            onMouseLeave={() => {
                                                                const timeout = setTimeout(() => {
                                                                    setDropdownOpenIndex(null);
                                                                }, 0);
                                                                setDropdownTimeout(timeout);
                                                            }}
                                                            tabIndex={-1}
                                                        >
                                                            <div className="bg-white border mt-2 rounded shadow-2xl py-2">
                                                                <Tooltip content="Click to duplicate this field.">
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => {
                                                                            if (clickedRowDupIndex !== null) {
                                                                                insert(clickedRowDupIndex, {
                                                                                    FieldName: values.Fields[dropdownOpenIndex].FieldName,
                                                                                    FieldCategory: values.Fields[dropdownOpenIndex].FieldCategory,
                                                                                    FieldFormat: values.Fields[dropdownOpenIndex].FieldFormat,
                                                                                    FieldDescription: values.Fields[dropdownOpenIndex].FieldDescription,
                                                                                    FieldNotes: values.Fields[dropdownOpenIndex].FieldNotes,
                                                                                });
                                                                            } else {
                                                                                push({
                                                                                    FieldName: values.Fields[dropdownOpenIndex].FieldName,
                                                                                    FieldCategory: values.Fields[dropdownOpenIndex].FieldCategory,
                                                                                    FieldFormat: values.Fields[dropdownOpenIndex].FieldFormat,
                                                                                    FieldDescription: values.Fields[dropdownOpenIndex].FieldDescription,
                                                                                    FieldNotes: values.Fields[dropdownOpenIndex].FieldNotes,
                                                                                });
                                                                            }

                                                                            setDropdownOpenIndex(null);
                                                                            setClickedRowDupIndex(null);
                                                                        }}
                                                                        className="rounded-3xl flex px-4 py-2 text-sm text-gray-700 hover:bg-gray-100 w-full text-left"
                                                                    >
                                                                        <MdOutlineContentCopy className="h-5 w-6" />
                                                                        Duplicate
                                                                    </button>
                                                                </Tooltip>

                                                                <Tooltip content="Click to remove this field.">
                                                                    <button
                                                                        type="button"
                                                                        onClick={() => {
                                                                            handleRemoveField(
                                                                                values.Fields[dropdownOpenIndex]._id,
                                                                                -1,
                                                                                dropdownOpenIndex,
                                                                                remove
                                                                            );
                                                                            setDropdownOpenIndex(null);
                                                                        }}
                                                                        className="rounded-3xl flex px-4 py-2 text-sm text-gray-700 hover:bg-red-500 hover:text-white w-full text-left"
                                                                    >
                                                                        <MdDelete className="h-5 w-6" />
                                                                        Remove
                                                                    </button>
                                                                </Tooltip>
                                                            </div>
                                                        </div>

                                                    </div>

                                                    <div className="flex justify-start m-2 mt-8 ">
                                                        <button
                                                            type="button"
                                                            onClick={() => {
                                                                if (clickedRowIndex !== null) {
                                                                    insert(clickedRowIndex, {
                                                                        FieldName: "",
                                                                        FieldCategory: "Numbers & Text",
                                                                        FieldFormat: "",
                                                                        FieldDescription: "",
                                                                        FieldNotes: "",
                                                                    });
                                                                } else {
                                                                    push({
                                                                        FieldName: "",
                                                                        FieldCategory: "Numbers & Text",
                                                                        FieldFormat: "",
                                                                        FieldDescription: "",
                                                                        FieldNotes: "",
                                                                    });
                                                                }
                                                                setClickedRowIndex(null); // Reset the clicked row index
                                                            }}
                                                            className="text-sm bg-[#FFFFFF] hover:bg-[#f3f3f3] text-[#003654] border-2 border-[#003654] font-bold py-2 px-4 mr-2 rounded-3xl focus:outline-none focus:shadow-outline"
                                                        >
                                                            <div className="flex">
                                                                <IoMdAddCircleOutline className="text-xl mr-1 " />{" "}
                                                                <Tooltip content="Click here to add new fields to the form"> Add New Field</Tooltip>
                                                            </div>
                                                        </button>

                                                        <button
                                                            type="button"
                                                            onClick={() =>
                                                                setFieldValue("Tables", [
                                                                    ...values.Tables,
                                                                    {
                                                                        TableName: `New Table ${values.Tables.length
                                                                            ? values.Tables.length + 1
                                                                            : 1
                                                                            }`,
                                                                        Fields: [
                                                                            {
                                                                                FieldName: "",
                                                                                FieldCategory: "Numbers & Text",
                                                                                FieldFormat: "",
                                                                                FieldDescription: "",
                                                                                FieldNotes: "",
                                                                            },
                                                                        ],
                                                                    },
                                                                ])
                                                            }

                                                            className="text-sm bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-3xl focus:outline-none focus:shadow-outline"
                                                        >
                                                            <div className="flex">
                                                                <LuTable className="text-xl mr-2" />
                                                                <Tooltip content="Click here to add new Table to the form"> Add New Table</Tooltip>
                                                            </div>
                                                        </button>
                                                    </div>
                                                </div>
                                            </>
                                        )}
                                    </FieldArray>

                                    {/* Display the tables */}
                                    {values?.Tables?.map((table, tableIndex) => (
                                        <div
                                            key={tableIndex}
                                            className="mt-5 p-8 max-h-[50vh] border-2 border-gray-300 rounded-3xl overflow-hidden bg-gray-200"
                                        >
                                            {table.isEditing ? (
                                                // If in editing mode, show an input field and a save button
                                                <div className="flex mb-3">
                                                    <Field
                                                        name={`Tables[${tableIndex}].TableName`}
                                                        component="input"
                                                        maxLength="100"
                                                        placeholder="Enter Table Name"
                                                        className="max-w-[30em] shadow appearance-none border rounded-lg w-full py-2 px-3 text-gray-700 leading-tight focus:outline-none focus:shadow-outline"
                                                    />
                                                    <button
                                                        type="button"
                                                        onClick={() => {
                                                            if (values.Tables[tableIndex].TableName.trim()) {
                                                                setFieldValue(
                                                                    `Tables[${tableIndex}].isEditing`,
                                                                    false
                                                                );
                                                            }
                                                            else {
                                                                toast.error("Table name can't be empty");
                                                            }
                                                        }}
                                                        className="ml-2 bg-[#003654]  text-white font-bold py-2 px-4 rounded-3xl"
                                                    >
                                                        Save
                                                    </button>
                                                </div>
                                            ) : (
                                                // If not editing, display the table name and an edit button
                                                <>
                                                    <div className="flex pb-3 text-[#003654] items-center justify-between">
                                                        <div className="flex items-center">
                                                            <span className="text-lg font-bold mr-3">
                                                                {table.TableName}
                                                            </span>
                                                            <button
                                                                onClick={() =>
                                                                    setFieldValue(
                                                                        `Tables[${tableIndex}].isEditing`,
                                                                        true
                                                                    )
                                                                }
                                                                className="text-2xl hover:bg-blue-100 rounded-3xl"
                                                            >
                                                                <BiEditAlt />
                                                            </button>
                                                        </div>

                                                        <Tooltip content="Click to duplicate this table with all columns.">
                                                            <div>
                                                                <button
                                                                    type="button"

                                                                    onClick={() => handleDuplicateTable(tableIndex, setFieldValue, values)}

                                                                    className="text-2xl hover:bg-blue-200 rounded-3xl"
                                                                >
                                                                    <MdOutlineContentCopy className="h-6 w-6" />
                                                                </button>
                                                            </div>
                                                        </Tooltip>
                                                    </div>
                                                </>
                                            )}

                                            <FieldArray name={`Tables[${tableIndex}].Fields`}>
                                                {({ insert, remove, push }) => (
                                                    <>

                                                        <div className="max-h-[32vh] overflow-x-auto ">
                                                            <table className=" max-w-full leading-normal ">
                                                                <tbody>
                                                                    {/* Header for Field Name */}
                                                                    <tr
                                                                        className={`text-sm text-left text-[#003654] border-2`}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]">
                                                                            <div className="flex justify-between">
                                                                                <div className="flex justify-start">
                                                                                    Column Name{" "}
                                                                                    <span className="text-red-500">*</span>
                                                                                </div>
                                                                                <div className="flex justify-end">
                                                                                    <Tooltip content="Enter the name of the column you wish to extract data from.">
                                                                                        <div >
                                                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                                                        </div>
                                                                                    </Tooltip>
                                                                                </div>
                                                                            </div>
                                                                        </th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td key={fieldIndex} className="min-w-[25vh] text-sm text-gray-700    justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedColumnIndex(fieldIndex)}
                                                                                ref={el => columnRefs.current[fieldIndex] = el}
                                                                            >
                                                                                <Field
                                                                                    as="textarea"
                                                                                    name={`Tables[${tableIndex}].Fields[${fieldIndex}].FieldName`}
                                                                                    className={`min-h-[1em] ${fieldIndex % 2 === 0
                                                                                        ? "bg-white"
                                                                                        : "bg-[#F8F9FC]"
                                                                                        } w-full border-0 px-3 resize-none overflow-auto h-10 py-2 align-middle`}
                                                                                    value={field.FieldName}
                                                                                    placeholder="Column Name"
                                                                                />
                                                                            </td>
                                                                        ))}
                                                                    </tr>

                                                                    {/* Header for Field Category */}
                                                                    <tr
                                                                        className={` text-sm text-left text-[#003654] border-2`}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]">
                                                                            <div className="flex justify-between">
                                                                                <div className="flex justify-start">
                                                                                    Column Category{" "}
                                                                                    <span className="text-red-500">*</span>
                                                                                </div>
                                                                                <div>
                                                                                    <Tooltip content="Specify the category to organize your data accordingly.">
                                                                                        <div>
                                                                                            <CiCircleInfo className="ml-2 text-xl" />
                                                                                        </div>
                                                                                    </Tooltip>
                                                                                </div>
                                                                            </div>
                                                                        </th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td key={fieldIndex} className="min-w-[25vh] text-sm text-gray-700 justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedColumnIndex(fieldIndex)}
                                                                            >
                                                                                <Field
                                                                                    as="select"
                                                                                    name={`Tables[${tableIndex}].Fields[${fieldIndex}].FieldCategory`}
                                                                                    className={`h-10 ${fieldIndex % 2 === 0
                                                                                        ? "bg-white"
                                                                                        : "bg-[#F8F9FC]"
                                                                                        } w-full border-0 px-3 resize-none overflow-auto`}
                                                                                    value={field.FieldCategory}
                                                                                    placeholder="Field Category"
                                                                                    onChange={(event) => handleFieldChange(fieldIndex, event, setFieldValue)}
                                                                                >
                                                                                    <option disabled value="">
                                                                                        Select Category
                                                                                    </option>
                                                                                    {categoryData.categories.map(
                                                                                        (category, index) => (
                                                                                            <option
                                                                                                key={index}
                                                                                                value={category.CategoryName}
                                                                                            >
                                                                                                {category.CategoryName}
                                                                                            </option>
                                                                                        )
                                                                                    )}
                                                                                </Field>
                                                                            </td>
                                                                        ))}
                                                                    </tr>

                                                                    {/* Other fields like Field Format, Description, and Notes can be similarly transposed */}
                                                                    {/* Header for Field Format */}
                                                                    <tr
                                                                        className={`text-sm text-left text-[#003654] border-2 `}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]">
                                                                            <div className="flex justify-between">
                                                                                Format

                                                                                <Tooltip content="Define the format for the selected category to ensure data consistency.">
                                                                                    <div className="">
                                                                                        <CiCircleInfo className="ml-2 text-xl" />
                                                                                    </div>
                                                                                </Tooltip>
                                                                            </div>
                                                                        </th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td key={fieldIndex} className="min-w-[25vh] text-sm text-gray-700    justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedColumnIndex(fieldIndex)}
                                                                            >
                                                                                <Field
                                                                                    as="select"
                                                                                    name={`Tables[${tableIndex}].Fields[${fieldIndex}].FieldFormat`}
                                                                                    className={` ${fieldIndex % 2 === 0
                                                                                        ? "bg-white"
                                                                                        : "bg-[#F8F9FC]"
                                                                                        } w-full border-0 px-3 h-10 resize-none overflow-auto disabled:text-gray-400`}
                                                                                    value={field.FieldFormat}
                                                                                    placeholder="Column Format"
                                                                                    disabled={
                                                                                        !categoryData.categories.find(
                                                                                            (category) =>
                                                                                                category.CategoryName ===
                                                                                                field.FieldCategory
                                                                                        )?.Formats.length
                                                                                    }
                                                                                    title={
                                                                                        categoryData.categories.find(
                                                                                            (category) =>
                                                                                                category.CategoryName ===
                                                                                                field.FieldCategory
                                                                                        )?.Formats.length
                                                                                            ? "Please select a format"
                                                                                            : "No format required"
                                                                                    }
                                                                                >
                                                                                    {categoryData.categories.find(
                                                                                        (category) =>
                                                                                            category.CategoryName ===
                                                                                            field.FieldCategory
                                                                                    )?.Formats.length ? (
                                                                                        <option disabled value="">
                                                                                            Select a format
                                                                                        </option>
                                                                                    ) : (
                                                                                        <option disabled value="">
                                                                                            No format required
                                                                                        </option>
                                                                                    )}

                                                                                    {categoryData.categories
                                                                                        .find(
                                                                                            (category) =>
                                                                                                category.CategoryName ===
                                                                                                field.FieldCategory
                                                                                        )
                                                                                        ?.Formats.map((format, index) => (
                                                                                            <option
                                                                                                key={index}
                                                                                                value={format}
                                                                                            >
                                                                                                {format}
                                                                                            </option>
                                                                                        ))}
                                                                                </Field>
                                                                            </td>
                                                                        ))}
                                                                    </tr>

                                                                    {/* Header for Field Description */}
                                                                    <tr
                                                                        className={`text-sm text-left text-[#003654] border-2 `}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]">
                                                                            <div className="flex justify-between">
                                                                                Prompt Assist
                                                                                <Tooltip content="Guidance or assistance provided to help extract the Column correctly.">
                                                                                    <div className="justify-end">
                                                                                        <CiCircleInfo className="ml-2 text-xl" />
                                                                                    </div>
                                                                                </Tooltip>
                                                                            </div>
                                                                        </th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td key={fieldIndex} className="min-w-[25vh] text-sm text-gray-700    justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedColumnIndex(fieldIndex)}
                                                                            >
                                                                                <Field
                                                                                    as="textarea"
                                                                                    name={`Tables[${tableIndex}].Fields[${fieldIndex}].FieldDescription`}
                                                                                    className={`min-h-[1em] ${fieldIndex % 2 === 0
                                                                                        ? "bg-white"
                                                                                        : "bg-[#F8F9FC]"
                                                                                        } w-full border-0 px-3 resize-none overflow-auto h-10 py-2 align-middle`}
                                                                                    value={field.FieldDescription}
                                                                                    placeholder="Column Description"
                                                                                />
                                                                            </td>
                                                                        ))}
                                                                    </tr>

                                                                    {/* Header for Field Description */}
                                                                    <tr
                                                                        className={`text-sm text-left text-[#003654] border-2`}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]">
                                                                            <div className="flex justify-between">
                                                                                Notes
                                                                                <Tooltip content="Additional comments or remarks regarding the data or Column.">
                                                                                    <div>
                                                                                        <CiCircleInfo className="ml-2 text-xl" />
                                                                                    </div>
                                                                                </Tooltip>
                                                                            </div>
                                                                        </th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td key={fieldIndex} className="min-w-[25vh] text-sm text-gray-700  justify-center items-center whitespace-normal break-words border-2 border-gray-300" onClick={() => setClickedColumnIndex(fieldIndex)}
                                                                            >
                                                                                <Field
                                                                                    as="textarea"
                                                                                    name={`Tables[${tableIndex}].Fields[${fieldIndex}].FieldNotes`}
                                                                                    className={`min-h-[1em] ${fieldIndex % 2 === 0
                                                                                        ? "bg-white"
                                                                                        : "bg-[#F8F9FC]"
                                                                                        } w-full border-0 px-3 resize-none overflow-auto h-10 py-2 align-middle`}
                                                                                    value={field.FieldNotes}
                                                                                    placeholder="Column Notes"
                                                                                />
                                                                            </td>
                                                                        ))}
                                                                    </tr>

                                                                    {/* Actions */}
                                                                    <tr
                                                                        className={`text-sm text-left text-[#003654] border-2 ${tableIndex % 2 === 0
                                                                            ? "bg-white"
                                                                            : "bg-[#F8F9FC]"
                                                                            }`}
                                                                    >
                                                                        <th className="sticky left-0 z-10 min-w-[20vh] px-5 py-3 border-2 border-gray-300 font-bold bg-[#C0C0C0]"></th>
                                                                        {table.Fields.map((field, fieldIndex) => (
                                                                            <td
                                                                                key={`actions-${fieldIndex}`}
                                                                                className="min-w-[25vh] py-0.5 border-2 border-gray-300"
                                                                            >
                                                                                <Tooltip content="Click to remove this column from the table.">
                                                                                    <button
                                                                                        type="button"
                                                                                        onClick={() =>
                                                                                            handleRemoveField(
                                                                                                field._id,
                                                                                                tableIndex,
                                                                                                fieldIndex,
                                                                                                remove
                                                                                            )
                                                                                        }
                                                                                        className="w-full rounded-3xl  flex justify-center bg-white text-red-500 hover:bg-red-700 hover:text-white font-bold py-2 px-4"
                                                                                    >
                                                                                        <MdDelete className="h-5 w-5" />
                                                                                        Remove
                                                                                    </button>
                                                                                </Tooltip>
                                                                            </td>
                                                                        ))}
                                                                    </tr>
                                                                </tbody>
                                                            </table>
                                                        </div>

                                                        <div className="flex justify-start m-1 mt-5">
                                                            <Tooltip content="Click to add a new column to the table.">
                                                                <button
                                                                    type="button"
                                                                    onClick={() => {
                                                                        if (clickedColumnIndex !== null) {
                                                                            insert(clickedColumnIndex, {
                                                                                FieldName: "",
                                                                                FieldCategory: "Numbers & Text",
                                                                                FieldFormat: "",
                                                                                FieldDescription: "",
                                                                                FieldNotes: "",
                                                                            });
                                                                        } else {
                                                                            push({
                                                                                FieldName: "",
                                                                                FieldCategory: "Numbers & Text",
                                                                                FieldFormat: "",
                                                                                FieldDescription: "",
                                                                                FieldNotes: "",
                                                                            });
                                                                        }
                                                                        setClickedColumnIndex(null); // Reset the clicked column index
                                                                    }}
                                                                    className="text-sm bg-[#FFFFFF] hover:bg-[#f3f3f3] text-[#003654] border-2 border-[#003654] font-bold py-2 px-4 mr-2 rounded-3xl focus:outline-none focus:shadow-outline"
                                                                >
                                                                    <div className="flex">
                                                                        <IoMdAddCircleOutline className="text-xl mr-1" />{" "}
                                                                        Add Column
                                                                    </div>
                                                                </button>
                                                            </Tooltip>
                                                            <Tooltip content="Click to delete this table.">
                                                                <button
                                                                    type="button"
                                                                    onClick={() =>
                                                                        handleDeleteTable(
                                                                            tableIndex,
                                                                            setFieldValue,
                                                                            values
                                                                        )
                                                                    }
                                                                    className="text-sm bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-3xl focus:outline-none focus:shadow-outline"
                                                                >
                                                                    <div className="flex">
                                                                        <MdDelete className="text-xl mr-1" />
                                                                        Delete Table
                                                                    </div>
                                                                </button>
                                                            </Tooltip>
                                                        </div>
                                                    </>
                                                )}
                                            </FieldArray>
                                        </div>
                                    ))}
                                </div>
                            </div>
                        </div>
                    </Form>
                )}
            </Formik>
        </>
    );
};

export default ModelAddPage;
ModelAddPage.propTypes = {
    isOpen: PropTypes.bool.isRequired
};