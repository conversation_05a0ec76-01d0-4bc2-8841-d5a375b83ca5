"""
Generalized Purchase with Inventory Class for handling multiple vendors.
This class provides a unified interface for generating Tally XML for different vendors.
"""
import re
import pandas as pd
from datetime import datetime
from pathlib import Path
import traceback
import os
import json
from fuzzywuzzy import process, fuzz
import asyncio
from decimal import Decimal, ROUND_HALF_UP, ROUND_DOWN
from typing import Dict, List, Any, Optional, Union
import sys
sys.path.append(".")
from src.Schemas.Tally_XML_Schema import (
    BillAllocationSchema,
    CompanyInfoSchema,
    ConsigneeDetailsSchema,
    PartyDetailsSchema,
    InventoryEntrySchema,
    LedgerEntrySchema,
    BatchAllocationSchema,
    AccountingAllocationSchema,
    CategoryAllocationSchema,
    CostCenterAllocationSchema,
    RateDetailSchema,
    TallyPurchaseInventoryVoucherSchema,
    InvoiceDelNotesSchema,
    InvoiceOrderListSchema
)
from src.Controllers.GoogleDriveController import GoogleDriveService
from PWIMatchingOpenAI.matchRunner import C<PERSON>edgerMatcher, run_matcher_subprocess
from PWIMatchingOpenAI import helperFunctionsInAsync
from src.Controllers.Logs_Controller import CLogController
from src.utilities.helperFunc import CJSONFileReader, CExcelHelper, CDirectoryHelper, DateHelper 
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.Controllers.GrnNoPITrackingController import CGrnNoPITracking
from fastapi import HTTPException
from src.utilities.PurchaseOrderUtilities import CPOUtilities
from src.utilities.GRNUtilities import CGRNUtilities
from src.utilities.PathHandler import dictProjectPaths
import decimal
from enum import Enum

class VersionType(str, Enum):
    V1 = "V1"
    V2 = "V2"


class CGeneralizePurchaseWithInventory:
    """
    Generalized class for handling purchase with inventory tasks across different vendors.
    This class provides static methods that can be used for all vendors.
    """

    OPENAIMODEL = "gpt-4.1"
    # PWI V3 Config
    SELLER_MAP_PATH = Path(r"SellerName.json")
    PYTHON_EXECUTABLE_PATH = Path(r"5_Env/Scripts/python.exe")
    WORKING_DIR = Path(r"PWIMatchingOpenAI")
    
    # NOTE Not being used
    _mstrMappingExcelPath = r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Mappings/Tally_ERP_MappingsV9.xlsx"
    _mbUseFuzzyMatching = False     # NOTE: IF TRUE, FINDS THE MATHCING ITEM FROM TALLY DATA
        
    PO_FOLDERPATH = dictProjectPaths.get("PO_FOLDERPATH",r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/po_register")
    GRN_FOLDERPATH = dictProjectPaths.get("GRN_FILE_PATH",r"H:/AI Data/Abhinav Infrabuild PVT. LTD/Downloads/grn_register")


    def __init__(self, iUserID, dictExtractedData = None, iDocId = None, strClientREQID=None, bDebug = False):
        self.iUserID = iUserID
        self.dictExtractedData = dictExtractedData
        self.iDocId = iDocId
        self.strClientREQID = strClientREQID
        self._GRNFileVersion = VersionType.V1
        # Tally Related Client Config
        self._mStrTallyCompanyName = None
        self._mStrClientName = None
        self._mDictVendorConfig = {}
        self._mGSTRegistrationType = "Regular"
        self._mGSTIN = None

        # Class variables to track stock items
        self._mlsTotalStockItems = []       # Tracks all stock items from the invoice
        self._mlsNonExistingStockItems = [] # Tracks non-existing stock items
        self._mStrTracebackLogs = ""
        self._mAVTallyStatus = "Skipped"
        self._mLsAccuVelocityComments = []
        self._miSRNO = 0
        self.bDebug = bDebug
        self.strGRNLatestFile = None
        self.strPOLatestFile = None


    @staticmethod
    def MSCleanXLSFile(strXLSFilePath: str):
        try:
            df_list = pd.read_html(strXLSFilePath)
            if len(df_list) < 2:
                raise ValueError("Expected at least 2 tables in the HTML file; found fewer.")
            df = df_list[1]  # Second table contains the data

            # Check if the first row contains column names
            first_row = df.iloc[0].astype(str).str.strip()
            expected_columns = [
                'GRN No', 'GRN Date', 'Party Bill No', 'Party Bill Date', 'Veh No',
                'Ch No', 'PO No', 'Indent No', 'Party', 'Remarks', 'Item', 'Stock Qty',
                'Unit', 'Weight', 'Rate', 'Amount', 'RST NO.'
            ]
            
            # If the first row looks like column names, use it as headers
            if any(col in first_row.values for col in expected_columns):
                df.columns = first_row
                df = df.drop(0).reset_index(drop=True)
            else:
                # Otherwise, assign expected column names
                if len(df.columns) == len(expected_columns):
                    df.columns = expected_columns
                else:
                    raise ValueError(f"Number of columns ({len(df.columns)}) does not match expected ({len(expected_columns)}).")
           

            return df
        except Exception as e:
            raise Exception(f"Error while reading XLS file: {str(e)}")

    def MSReadGRNFile(self, strLatestGRNFile: str = Path(r"Data/Customer/Abhinav InfraBuild/GRN_Excel.xlsx")):
        """
        Reads an XLS file and verifies specified columns, using pd.read_excel for .xlsx files
        and MSCleanXLSFile for .xls files.
        
        Args:
            strLatestGRNFile (str): Path to the XLS file.
            
        Returns:
            pd.DataFrame: DataFrame containing the XLS file data.
            
        Raises:
            FileNotFoundError: If the XLS file does not exist.
            ValueError: If required columns are missing.
            Exception: For other unexpected errors during file reading.
        """
        try:
            # Get the latest file from the nested folder
            latest_file = CDirectoryHelper.MSGetLatestFileFromNestedFolder(CGeneralizePurchaseWithInventory.GRN_FOLDERPATH) #TODO: Production Change 
            
            # Use the latest file if available, otherwise fall back to the default path
            if latest_file:
                strLatestGRNFile = Path(latest_file)
            else:
                strLatestGRNFile = Path(r"Data/Customer/Abhinav InfraBuild/GRN File/GRN_Excel.xlsx")
            
            self.strGRNLatestFile = strLatestGRNFile

            # Validate file path
            file_path = Path(strLatestGRNFile)
            if not file_path.is_file():
                raise FileNotFoundError(f"File '{strLatestGRNFile}' does not exist or is not a file.")

            # Determine the file extension
            extension = file_path.suffix.lower()
            print(f"Debug: File path: {strLatestGRNFile}, Extension: {extension}")

            # Read the file based on the extension
            if extension == '.xlsx':
                print(f"Debug: Reading .xlsx file using pd.read_excel")
                df = pd.read_excel(strLatestGRNFile)
            elif extension == '.xls':
                print(f"Debug: Reading .xls file using MSCleanXLSFile")
                df = CGeneralizePurchaseWithInventory.MSCleanXLSFile(strLatestGRNFile)
            else:
                raise ValueError(f"Unsupported file extension: {extension}. Expected .xls or .xlsx")

            # Define columns to verify
            columns_to_verify = [
                'GRN No', 'GRN Date', 'Party Bill No', 'Party Bill Date', 'Veh No',
                'Ch No', 'PO No', 'Indent No', 'Party', 'Remarks', 'Item', 'Stock Qty',
                'Unit', 'Weight', 'Rate', 'Amount', 'RST NO.'
            ]
            
            # Check if all required columns are present
            missing_columns = [col for col in columns_to_verify if col not in df.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
            
            print(f"Debug: Successfully loaded DataFrame with columns: {df.columns.tolist()}")
            return df
        
        except FileNotFoundError as e:
            raise FileNotFoundError(f"Error: {str(e)}")
        except ValueError as e:
            raise ValueError(f"Error: {str(e)}")
        except Exception as e:
            raise Exception(f"Unexpected error while reading XLS file: {str(e)}")

    async def MSGenerteNarration(self,strCostCentre="AV Cost Centre"):
        """
            Client: Abhinav Infrabuild
            Constructs a narration string for an invoice using extracted data.

            Parameters:
            - cost_centre (str): The cost centre to be included in the narration.

            Returns:
            - str: A formatted narration string.
        """
        try:
            iInvoiceNumber = self.dictExtractedData.get("InvoiceNo", "")
            invoice_date = self.dictExtractedData.get("InvoiceDate", "")
            date_object = datetime.strptime(invoice_date, "%Y%m%d")
            invoice_date = date_object.strftime("%d.%m.%y")
            item_details = self.lsAllItems

            # Collect item names safely
            item_names = [item.get("Item", "") for item in item_details if item.get("Item")]

            # Check if the number of items is greater than 10
            current_date = datetime.now().strftime("%d.%m.%Y")
            if len(item_details) > 10:
                # Format the narration string
                narration_parts = [
                    f"BILL No. {iInvoiceNumber}",
                    f"DTD {invoice_date}",
                    f"TOWARDS MATERIAL PURCHASE FOR {strCostCentre} SITE , BILL ENTRY DATE: {current_date}"
                ]
            else:
                # Format the narration string
                narration_parts = [
                    f"BILL No. {iInvoiceNumber}",
                    f"DTD {invoice_date}",
                    f"TOWARDS {', '.join(item_names)}. ",
                    f"{strCostCentre} SITE, BILL ENTRY DATE: {current_date}"
                ]
            

            # Join all parts with spaces
            narration = " ".join(narration_parts)
            return narration
        except Exception as objException:
            await CLogController.MSWriteLog(None, "Error", objException)
            self._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())  
            # raise
            self._mLsAccuVelocityComments.append("Please Add Narration Manually.")
        

    @staticmethod
    def MSExtractPOGRNDetailFromGRN(lsMappedGRNPOItems: List[Dict]) -> List[Dict[str, Union[str, None]]]:
        """
        Extracts and formats GRN No, GRN Date, PO No, and PO Date from the 'Items' list in each GRN record.
        Removes duplicates by setting duplicate GRN or PO fields to None.

        - GRN Date is formatted to 'YYYYMMDD'
        - PO Date is parsed from 'DD/Mon/YYYY' and formatted to 'YYYYMMDD'

        Args:
            lsMappedGRNPOItems (List[Dict]): List of dictionaries containing PO and GRN data.

        Returns:
            List[Dict[str, Union[str, None]]]: List of simplified records with formatted fields.
        """
        results = []
        seen_grns = set()
        seen_pos = set()

        try:
            items = lsMappedGRNPOItems
            for item in items:
                # Extract GRN details from item or fallback to lsGRNDetails
                grn_no = item.get('GRNNO') 
                grn_date = item.get('GRNDate')

                if grn_no in seen_grns:
                    grn_no_out = None
                    grn_date_out = None
                else:
                    seen_grns.add(grn_no)
                    grn_no_out = grn_no

                    if isinstance(grn_date, (datetime, pd.Timestamp)):
                        grn_date_out = grn_date.strftime("%Y%m%d")
                    elif isinstance(grn_date, str):
                        # Try to parse grn_date if it's a string
                        try:
                            grn_date_out = datetime.strptime(grn_date, "%d-%b-%Y").strftime("%Y%m%d")  # Adjust format as needed
                        except ValueError:
                            grn_date_out = None
                    else:
                        grn_date_out = None

                # Extract PO details from item or fallback to PODetails
                po_no = item.get('PONO') 
                po_date_raw = item.get('PODate') 

                if po_no in seen_pos:
                    po_no_out = None
                    po_date_out = None
                else:
                    seen_pos.add(po_no)
                    po_no_out = po_no
                    for fmt in ["%d-%b-%Y", "%d/%b/%Y"]:
                        try:
                            po_date_out = datetime.strptime(po_date_raw, fmt).strftime("%Y%m%d")
                        except (ValueError, TypeError):
                            po_date_out = None 
                            continue
                # Things To Do:
                # Step 1: Check Current GRN is in Database If not then Enter it
                # Step 2 : Check For Duplicates
                # Step 3: Raise Error if Duplicate Found
                # Make Sure Code Performs without any error if GRN nout found
                results.append({
                    'GRN No': grn_no_out,
                    'GRN Date': grn_date_out,
                    'PO No.': po_no_out,
                    'PO Date': po_date_out
                })

        except Exception as e:
            raise

        return results


    def MGetTaxAmount(self):
        '''
            Calculate Tax Amount from GRN Data Frame
        '''
        self._mfTaxAmount = 0
        try:
           
            self._mfTaxAmount = sum(item.get("TotalGST", 0) for item in self.lsAllItems)
            return self._mfTaxAmount
        except Exception as e:
            self._mStrTracebackLogs += "MGetTaxAmount:\n" + str(traceback.format_exc())
            raise Exception(f"Error in Getting Tax Amount: {e}")
 
    def MGetTotalAmount(self):
        '''
            Calculate Total Amount from GRN Data frame and TAX
        '''
        self.mfGrandTotalAmount = 0
        try:          
            self.mfGrandTotalAmount = self._mfSubTotalAmount + self._mfTaxAmount + self.mfRoundoff
            return self.mfGrandTotalAmount
        except Exception as e:
            self._mStrTracebackLogs += "MGetTotalAmount:\n" + str(traceback.format_exc())
            raise Exception(f"Error in Getting Total Amount: {e}")  
 
    def MCalculateRoundOff(self):
        '''
        Calculate Round-Off Value based on subtotal and tax.
        Round-off may be negative or positive.
        '''
        self.mfRoundoff = 0
        try:
            decimal.getcontext().rounding = decimal.ROUND_HALF_UP
            
            # Calculate subtotal from the 'Amount' column
            self._mfSubTotalAmount = sum(
                decimal.Decimal(item.get("Amount", 0))
                for item in self.lsAllItems
            )

            # Ensure _mfTaxAmount is Decimal
            self._mfTaxAmount = decimal.Decimal(self._mfTaxAmount)

            # Calculate gross amount
            fGrossAmount = self._mfSubTotalAmount + self._mfTaxAmount

            # Round to nearest whole number
            fRoundedTotal = decimal.Decimal(round(float(fGrossAmount)))

            # Calculate signed round-off value
            self.mfRoundoff = fRoundedTotal - fGrossAmount

            return float(self.mfRoundoff)
        except Exception as e:
            self._mStrTracebackLogs += "MCalculateRoundOff:\n" + str(traceback.format_exc())
            raise Exception(f"Error in Calculating Round-Off: {e}")
 
 
    @staticmethod
    async def MSGetMappedGRNItems(iUserId,  dfGRNDetails, bDownloadERP = False):
        lsGRNProcessingDetails = []
        lsAccuVelocityComments = []
        lsPONoNotFound = []
        try:
            # Ensure dfGRNDetails is a standalone copy, not a view
            dfGRNDetails = dfGRNDetails.copy()

            # strMappingExcelFile = CGeneralizePurchaseWithInventory._mstrMappingExcelPath
            # if not os.path.exists(strMappingExcelFile):
            #     strMappingExcelFile = r"Data\Customer\Abhinav InfraBuild\Mappings\Tally_ERP_MappingsV6.xlsx"
            
            # # START -> Load Item mapping Excel for item name normalization ------------------------------
            # mapping_df, ledger_df = CExcelHelper.read_excel_sheets(strMappingExcelFile, ["ITEM MAPPING", "ledger sheet"])

            # Updated Code For getting Item ERP Matching from google drive
            try:
                drive_service = GoogleDriveService()
                df_mapping = drive_service.handle_file(bisMultipleSheet=True, download = bDownloadERP)
            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "Error", f"There was a problem in getting Item ERP Matching file")
                raise Exception("There was a problem in getting Item ERP Matching file")
               
            sheets = ["ITEM MAPPING", "ledger sheet"]
            mapping_df, ledger_df = df_mapping[sheets[0]],df_mapping[sheets[1]]
            
            mapping_df['ERP Item Name '] = mapping_df['ERP Item Name '].astype(str).str.strip()
            mapping_df['Tally Items'] = mapping_df['Tally Items'].astype(str).str.strip()
            mapping_df['Tally Unit'] = mapping_df['Tally Unit'].astype(str).str.strip()
            mapping_df['ERP Unit'] = mapping_df['ERP Unit'].astype(str).str.strip()
            mapping_df['ERP Item Name_normalized'] = mapping_df['ERP Item Name '].str.lower().str.strip()
            
            # Check for duplicates
            duplicate_rows = mapping_df[mapping_df['ERP Item Name_normalized'].duplicated(keep=False)]
            if not duplicate_rows.empty:
                duplicate_items = duplicate_rows['ERP Item Name '].unique()
                duplicate_list_str = "\n".join(f"- {item}" for item in duplicate_items)
                raise HTTPException(
                    status_code=500,
                    detail=f"ValidationError Tally XML: Duplicate ERP Item Names found in mapping Excel. Please update the file to remove duplicates:\n{duplicate_list_str}"
                )
            
            mapping_dict = mapping_df.set_index('ERP Item Name_normalized')[['Tally Unit', "ERP Unit", 'Tally Items']].to_dict(orient='index')

            # END -> Load Item mapping Excel for item name normalization ------------------------------
            
            
            # START -> Reading PO Details an updating as required -------------------------------------------
            strPOFilePath = CPOUtilities.MSGetLatestPOFilePath() #TODO: Production Change 
            if not os.path.exists(strPOFilePath):
                strPOFilePath = Path(r"Data/Customer/Abhinav InfraBuild/PO File/PO_REGISTER1870.xls")
                # raise HTTPException(status_code=404, detail="PO file not found, please make sure the latest PO file is downloaded.")

            print(f"Found PO File at location '{strPOFilePath}'.")
            dfPurchaseOrders = CExcelHelper.read_file(strPOFilePath)

            # PO Columns to Verify
            po_columns_to_verify = ['PO DATE', 'PO NO.', 'VENDOR', 'ITEM CODE', 'ITEM GROUP', 'ITEM', 'QTY',
            'UNIT', 'WEIGHT', 'WEIGHT UNIT', 'UNIT RATE', 'ITEM AMOUNT',
            'DESC PERC', 'DESC', 'VAT', 'EXCISE AMT', 'NET AMT', 'PO FOR',
            'QUOTATION NO.', 'QUOTATION BY', 'DELIVERY SITE', 'AGAINST C FORM',
            'DELIVERY DAYS', 'PAYMENT DAYS', 'BILLING SITE', 'WAR DESC',
            'OTHER DESC', 'TECH DESC', 'GST CAL', 'PO STATUS']
            
            # Check if all required columns are present
            missing_columns = [col for col in po_columns_to_verify if col not in dfPurchaseOrders.columns]
            if missing_columns:
                raise ValueError(f"Missing required columns: {', '.join(missing_columns)}")
                        
            dfPurchaseOrders["PO NO."] = dfPurchaseOrders["PO NO."].astype(str).str.strip()
            
                # Normalize PO item names and map to Tally names
            dfPurchaseOrders['ITEM_normalized'] = dfPurchaseOrders['ITEM'].str.lower().str.strip()
            dfPurchaseOrders['Tally Item'] = dfPurchaseOrders['ITEM_normalized'].map(
                lambda x: mapping_dict.get(x, {}).get('Tally Items', dfPurchaseOrders['ITEM'].loc[dfPurchaseOrders['ITEM_normalized'] == x].iloc[0])
            )
            dfPurchaseOrders['Tally Unit'] = dfPurchaseOrders['ITEM_normalized'].map(
                lambda x: mapping_dict.get(x, {}).get('Tally Unit', dfPurchaseOrders['UNIT'].loc[dfPurchaseOrders['ITEM_normalized'] == x].iloc[0])
            )
            # Create po_info_dict with aggregated info and all items
            po_info_dict = {}
            for po_no, group in dfPurchaseOrders.groupby("PO NO."):
                po_info_dict[po_no] = {
                    "PO Date": group["PO DATE"].iloc[0],
                    "PO NO.": group["PO NO."].iloc[0],
                    "Items": group.to_dict(orient="records")  # List of dictionaries, each with all item columns
                }
                
            # END -> Reading PO Details an updating as required -------------------------------------------

            # START -> Create GRN Details with PO Items and requried information --------------------------
            # Group by "GRN No" and create grn_list
            grn_groups = dfGRNDetails.groupby("GRN No")
            grn_list = []
            for grn_no, group in grn_groups:
                po_no = str(group["PO No"].iloc[0]).strip()
                po_info = po_info_dict.get(po_no, {"PO Date": "", "CST": '0',"Round Off":'0'})
                po_date_str = po_info["PO Date"]
                
                
                # Fetch PO item details from the PO information (already present in po_info_dict)
                po_items = po_info_dict.get(po_no, {}).get("Items", [])

                grn_data = {
                    "GRN No": grn_no,
                    "GRN Date": group["GRN Date"].iloc[0],
                    "Supplier": group["Party"].iloc[0],
                    "PO No": group["PO No"].iloc[0] if not pd.isna(group["PO No"].iloc[0]) else "",
                    "Remarks": group["Remarks"].iloc[0],
                    "PO Date": po_date_str,
                    "Items": group[["Item", "Stock Qty", "Unit", "Rate", "Amount"]].to_dict("records"),
                    "Party Bill No": group["Party Bill No"].iloc[0],
                    "Challan No":group["Ch No"].iloc[0],
                    "PO_Items": po_items
                }
                grn_list.append(grn_data)
            
            # END -> Create GRN Details with PO Items and requried information --------------------------
            
            
            for i, dictGRNDetails in enumerate(grn_list):
                dictGRNProcessingDetail = {
                    "XMLFilePath": "-",
                    "SR_No.": i+1,
                    "PODetails":{},
                    "GRN No": dictGRNDetails.get("GRN No", "-"),
                    "GRN_Date": dictGRNDetails.get("GRN Date", "-"),
                    "Supplier": dictGRNDetails.get("Supplier", "-"),
                    "Items": dictGRNDetails.get("Items", []),
                    "Total_Items": len(dictGRNDetails.get("Items", [])),
                    "Time_Saved": "0 Seconds",
                    "Time_Statistics": {"hours": 0, "minutes": 0, "seconds": 0},
                    "AV_Status": "Skipped",
                    "AV_Comments": "-"
                }
                # strXmlData = None
                
                # Update supplier name using ledger_dict with normalization
                # supplier = dictGRNDetails.get("Supplier")
                # if supplier is None or (isinstance(supplier, float) and math.isnan(supplier)) or (isinstance(supplier, str) and not supplier.strip()) or (isinstance(supplier, str) and supplier == 'nan'):
                #     supplier = "AIPL Head Office (Godown Main)"
                # else:
                #     supplier = str(supplier).strip()
                
                # supplier_normalized = supplier.lower()
                # if supplier_normalized in ledger_dict:
                #     supplier = ledger_dict[supplier_normalized]
                #     if not pd.isna(supplier) and str(supplier).strip().lower() != 'nan':
                #         supplier = supplier

                
                # dictGRNDetails["Supplier"] = supplier
                # dictGRNProcessingDetail["Supplier"] = supplier
                
                # Update item names and units using mapping_dict
                for item in dictGRNDetails["Items"]:
                    
                    item["GRNNO"] = dictGRNDetails.get('GRN No')
                    item["GRNDate"] = dictGRNDetails.get('GRN Date')
                    
                    item_name = item["Item"]
                    item_name_normalized = str(item_name).lower().strip()
                    value = mapping_dict.get(item_name_normalized, {}).get('Tally Items')
                    isValidTallyItem = (
                        value is not None and
                        not pd.isna(value) and
                        str(value).strip().lower() != 'nan'
                    )
                    if isValidTallyItem:
                        item["ERP_Item"] = item["Item"]
                        item["ERP_Unit"] = item["Unit"]
                        item["ERP_Rate"] = item["Rate"]
                        item["ERP_Amount"] = item["Amount"]
                        
                        item["Item"] = mapping_dict[item_name_normalized]['Tally Items']
                        strTallyUnit = mapping_dict[item_name_normalized]['Tally Unit']
                        strERPUnit = mapping_dict[item_name_normalized]['ERP Unit']
                        
                        if strTallyUnit is not None and not pd.isna(strTallyUnit) and str(strTallyUnit).strip().lower() != 'nan' and strTallyUnit != "":
                            item["Unit"] = strTallyUnit
                        else:
                            item["Unit"] = strERPUnit
                        
                    else:
                        item["ERP_Item"] = item["Item"]
                        item["ERP_Unit"] = item["Unit"]
                        item["ERP_Rate"] = item["Rate"]
                        item["ERP_Amount"] = item["Amount"]

                
                # Match GRN items with PO items and update rates and amounts if matching
                po_no = dictGRNDetails.get("PO No")
                if po_no and po_no in po_info_dict:
                    try:
                        dictPODetails = po_info_dict[po_no]
                        dictGRNProcessingDetail["PODetails"] = dictPODetails
                        po_items = {
                            item["Tally Item"]: item
                            for item in po_info_dict[po_no].get("Items", [])
                            if "Tally Item" in item
                        }

                        for grn_item in dictGRNDetails.get("Items", []):
                            try:
                                tally_item_name = grn_item.get("Item")
                                if not tally_item_name:
                                    dictGRNProcessingDetail["AV_Comments"] += "Missing 'Item' field in GRN item."
                                    continue

                                po_item = po_items.get(tally_item_name)
                                if not po_item:
                                    dictGRNProcessingDetail["AV_Comments"] += f"Item {tally_item_name} not found in PO {po_no}."
                                    continue

                                grn_unit = grn_item.get("Unit")
                                po_unit = po_item.get("Tally Unit")
                                
                                if po_item.get("Tally Unit") == "nan":
                                    po_unit = po_item.get("UNIT") 
                   
                                grn_item["PONO"] = po_no
                                grn_item["PODate"] = dictGRNDetails.get('PO Date')
                                
                                # Convert to string to ensure comparison works
                                if str(grn_unit).strip().lower() == str(po_unit).strip().lower():
                                    try:
                                        print(f"GRN -> {dictGRNDetails.get('GRN No', '-')}  PO NO  {po_no}  -->   BEFORE UPDATES  GRN Item Rate-> {grn_item['Rate']}  GRN Item Quantity-> {float(grn_item.get('Stock Qty', 0))}     GRN ITEM AMOUNT  ->  {grn_item['Amount']} ")

                                        unit_rate = float(po_item.get("UNIT RATE", 0))
                                        stock_qty = float(grn_item.get("Stock Qty", 0))
                                        grn_item["Rate"] = unit_rate
                                        grn_item["Amount"] = unit_rate * stock_qty
                                        
                                        print(F"GRN -> {dictGRNDetails.get('GRN No', '-')}  PO NO  {po_no}  -->  AFTER UPDATES GRN Item Rate-> {grn_item['Rate']}    GRN Item Quantity-> {stock_qty}     GRN ITEM AMOUNT  ->  {grn_item['Amount']} ")
                                    except (ValueError, TypeError) as calc_err:
                                        dictGRNProcessingDetail["AV_Comments"] += (
                                            f"Error calculating amount for item {tally_item_name}: {calc_err}"
                                        )
                                        print(f"Error calculating amount for item {tally_item_name}: {calc_err}")
                                else:
                                    msg = (
                                        f"Unit mismatch for item {tally_item_name} in PO {po_no}: "
                                        f"GRN unit '{grn_unit}', PO unit '{po_unit}'"
                                    )
                                    dictGRNProcessingDetail["AV_Comments"] += f"{msg}"
                                    print(f"Warning: {msg}")

                                # CALCULATE TOTAL GST APPLICABLE FOR CURRENT QUANTITY 
                                try:
                                    vat = float(po_item.get("VAT", 0))
                                    qty = float(po_item.get("QTY", 0))
                                    stock_qty = float(grn_item.get("Stock Qty", 0))

                                    if qty == 0:
                                        raise ValueError("Quantity is zero, cannot divide by zero.")

                                    grn_item["TotalGST"] = (vat / qty) * stock_qty

                                except (ValueError, TypeError, KeyError) as e:
                                    # Handle error (log it, set default, raise custom error, etc.)
                                    grn_item["TotalGST"] = 0
                                    print(f"Error computing TotalGST: {e}")

                            except Exception as item_err:
                                dictGRNProcessingDetail["AV_Comments"] += (
                                    f"Unexpected error processing GRN item '{tally_item_name}': {item_err}"
                                )
                                print(f"Unexpected error processing GRN item '{tally_item_name}': {item_err}")
                    except Exception as outer_err:
                        dictGRNProcessingDetail["AV_Comments"] += f"Error processing PO match for PO {po_no}: {outer_err}"
                        print(f"Error processing PO match for PO {po_no}: {outer_err}")
                else:
                    dictGRNProcessingDetail["AV_Comments"] += f"PO No '{po_no}' not found in PO info."
                    print(f"PO No '{po_no}' not found in PO info.")
                    if po_no not in lsPONoNotFound:
                        lsPONoNotFound.append(po_no)
                        lsAccuVelocityComments.append(f"PO Number '{po_no}' is missing or not found in the latest PO file located at: {strPOFilePath}.")
                    # raise HTTPException(status_code=500, detail=f"ValidationError – Tally XML: PO Number '{po_no}' is missing or not found in the latest PO file located at: {strPOFilePath}.")

                lsGRNProcessingDetails.extend(dictGRNProcessingDetail.get("Items", []))
                # TODO: ADD TAG AV Comments FOR ITEMS NOT FOUND IN PO OR PO NOT FOUND 
                
        except HTTPException as he:
            await CLogController.MSWriteLog(iUserId, "Error", f"Error in matcher subprocess: {str(he)}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Error in matcher subprocess: {str(e)}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="Unable to map GRN items with PO Items.")
            
        return lsGRNProcessingDetails , lsAccuVelocityComments
    
    @staticmethod
    def MSGetGRNDetails(dfGRN, strPINumber, strPIPartyName, strPIDate=None):
        def normalize_party_name(name):
            return re.sub(r'[^a-zA-Z0-9]', '', name).lower().strip()
        
        normalized_pi_party = normalize_party_name(strPIPartyName)

        dfFiltered = dfGRN[
            (dfGRN["Party Bill No"].astype(str).str.strip() == strPINumber.strip()) &
            (
                (dfGRN["Party"].astype(str).apply(lambda x: normalize_party_name(x)) == normalized_pi_party) |
                (dfGRN["Party"].astype(str).apply(lambda x: normalized_pi_party in normalize_party_name(x)))
            )
        ]

        # if strPIDate:
        #     dfFiltered = dfFiltered[dfFiltered["Bill Date"] == strPIDate]

        return dfFiltered
    
    @staticmethod
    def MSGetPODetails(dfPO, strPONumber):
        return dfPO[dfPO["PO No"] == strPONumber]
    

    def MSetUserAttributes(self):
        
        if self.iUserID == 11:
            """Client Prerequisites:

                1. Ledger Configuration – "AV Select Ledger"

                    Group: Sundry Creditors

                    State: Madhya Pradesh

                    Country: India

                2. Stock Item Setup – "AV Select Item"

                    Category: Stock Item Creation

                    Under: Primary

                    Units: Nos

                    Taxability: Taxable

                    GST Rate: 18%

                    Type of Supply: Goods

                3. Voucher Type – "AV PURCHASE"

                    Base Type: Purchase (do not reuse existing Purchase voucher type)

                    Method of Voucher Numbering: Automatic with Manual Override

                    Settings: Allow voucher date editing and narration
            """
            self._mStrTallyCompanyName = "Abhinav Infrabuild Pvt.Ltd.(24-26)"
            self._mStrClientName = "abhinav"
            self._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/Abhinav InfraBuild/TallyLedgerConfig.json"))
            self._mGSTIN = "23AAHCA9425D1ZY"
            self._mGSTRegistrationType = "Regular"
            self._mStateName = "Madhya Pradesh"
            self._mCountryName = "India"
            self._mLsAddress = ["207-208 Industry House,A.B.Road Indore(M.P.)",
                "Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)",
                "E-Mail : <EMAIL>"
            ]
            self._mPinCode = ""
            self._mStrVoucherClassName = "CSLV"
            self._mStrDueDateDays = "45 Days"
            self._mStrPurchaseLedgerName = "Purchase - GST - Contract"
            self._mInvoiceDate = self.dictExtractedData.get("InvoiceDate", "") 
            self._mEffectiveDate = CBusinessIntelligence.MSRoundDateToNext5Days(self.dictExtractedData.get("InvoiceDate", ""))
            self.bIsVoucherDateSameAsInvoiceDate = False
        elif self.iUserID == 2:
            self._mStrTallyCompanyName = "PARAG TRADERS (24-25)"
            self._mStrClientName = "parag"
            self._mGSTIN = "23AAKFV4306N1ZX"
            self._mGSTRegistrationType = "Regular"
            self._mStateName = "Madhya Pradesh"
            self._mCountryName = "India"
            self._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/17_ParagTraders/TallyLedgerConfig.json"))
            self._mLsAddress = []
            self._mPinCode = ""
            self._mStrVoucherClassName = ""
            self._mStrDueDateDays = ""
            self._mStrPurchaseLedgerName = "GST INTERSTATE PURCHASE (18%)"
            self._mInvoiceDate = self.dictExtractedData.get("InvoiceDate", "")
            self._mEffectiveDate = self.dictExtractedData.get("InvoiceDate", "")
            self.bIsVoucherDateSameAsInvoiceDate = True

        elif self.iUserID == 7:
            self._mStrTallyCompanyName = "Gwalia Sweets Pvt Ltd - (24-25)"
            self._mStrClientName = "gwalia"
        else:
            print("User ID not found in configuration : GeneralizePurchaseWithInventory")
        
        return {
            "strTallyCompanyName": self._mStrTallyCompanyName,
            "strClientName": self._mStrClientName,
            "dictTallyLedgerConfig": self._mDictVendorConfig
        }

    def MCreateCompanyInfo(self):
        # Validate required instance attributes
        missing_fields = []
        
        if not getattr(self, "_mStrTallyCompanyName", None):
            missing_fields.append("Tally Company Name (_mStrTallyCompanyName)")
        if not getattr(self, "_mGSTRegistrationType", None):
            missing_fields.append("GST Registration Type (_mGSTRegistrationType)")
        if not getattr(self, "_mGSTIN", None):
            missing_fields.append("GSTIN (_mGSTIN)")
        if not getattr(self, "_mStateName", None):
            missing_fields.append("State Name (_mStateName)")
        if not getattr(self, "_mCountryName", None):
            missing_fields.append("Country Name (_mCountryName)")

        if missing_fields:
            raise ValueError(f"[❌] Missing required company info fields: {', '.join(missing_fields)}")

        # All fields validated — proceed to create schema
        self.company = CompanyInfoSchema(
            company_name=self._mStrTallyCompanyName,
            gst_registration_type=self._mGSTRegistrationType,
            gst_in=self._mGSTIN,
            state_name=self._mStateName,
            country_name=self._mCountryName
        )
        return self.company

    def MCreateConsigneeInfo(self):
        # Validate required instance attributes
        missing_fields = []
        
        if not getattr(self, "_mStrTallyCompanyName", None):
            missing_fields.append("Tally Company Name (_mStrTallyCompanyName)")
        if not getattr(self, "_mGSTIN", None):
            missing_fields.append("GSTIN (_mGSTIN)")
        if not getattr(self, "_mStateName", None):
            missing_fields.append("State Name (_mStateName)")
        if not getattr(self, "_mCountryName", None):
            missing_fields.append("Country Name (_mCountryName)")
        if not getattr(self, "_mLsAddress", None):
            missing_fields.append("Address (_mLsAddress)")

        if missing_fields:
            raise ValueError(f"[❌] Missing required consignee info fields: {', '.join(missing_fields)}")

        # All fields validated — proceed to create schema
        self.consignee = ConsigneeDetailsSchema(
            mailing_name=self._mStrTallyCompanyName,
            address_list=self._mLsAddress,
            gst_in=self._mGSTIN,
            state_name=self._mStateName,
            country_name=self._mCountryName, 
            pin_code=self._mPinCode
        )
        return self.consignee

    # PURCHASE LEDGER
    def MCreatePartyInfo(self, dictVendorConfig: Dict):
        """
        Creates a PartyDetailsSchema instance from the given vendor config.
        """
        required_keys = [
            "party_name", "address_list", "gst_registration_type",
            "gst_in", "state_name", "country_name", "pin_code"
        ]

        missing = [key for key in required_keys if key not in dictVendorConfig]
        if missing:
            raise KeyError(f"[❌] Missing vendor config keys: {', '.join(missing)}")
        
        self.party = PartyDetailsSchema(
            party_name=dictVendorConfig["party_name"],
            address_list=dictVendorConfig["address_list"],
            gst_registration_type=dictVendorConfig["gst_registration_type"],
            gst_in=dictVendorConfig["gst_in"],
            state_name=dictVendorConfig["state_name"],
            country_name=dictVendorConfig["country_name"],
            pin_code=dictVendorConfig["pin_code"]
        )
        return self.party
    
    def MGetVendorConfig(self) -> Dict:
        """
        Retrieves the vendor configuration based on the extracted seller name.
        Validates existence in vendor config and handles normalization.
        """
        self.strVendorName = self.dictExtractedData.get("SellerDetails").get("SellerName", "").strip()

        if not self.strVendorName:
            raise ValueError("[❌] SellerName is missing in extracted data.")

        # Normalize vendor name
        matchKey = CGeneralizePurchaseWithInventory.MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = self._mDictVendorConfig, strInputText = self.strVendorName)



        if matchKey == " ":
            raise ValueError(
                f"[❌] Vendor '{self.strVendorName}' is not supported. Please contact the AccuVelocity team."
            )

        # Return original key's config
        return self._mDictVendorConfig[matchKey]
    
    async def MGetAllStockItemInfo(self, bUseFuzzy=False):
        """
        Get all stock item information for the invoice.

        Returns:
            list: List of InventoryEntrySchema objects
        """
        lsResult = []
        lsInventoryItemDetails = []
        try:
            
            dictGRNDetail = self.lsAllItems
            if dictGRNDetail is None:
                raise HTTPException(status_code=500, detail="Unable to find GRN details. Please ensure the GRN is created before processing this invoice.")
            
            if bUseFuzzy:
                # Create a temporary JSON file with the extracted data
                os.makedirs(Path(r"GitIgnore/GeneralizePWIV3/Temp"), exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_json_path = os.path.join(Path(r"GitIgnore/GeneralizePWIV3/Temp"), f"temp_items_{timestamp}.json")

                # Save the item details to a temporary JSON file
                with open(temp_json_path, "w", encoding="utf-8") as f:
                    json.dump(self.dictExtractedData, f, indent=4)
                
                if self.bDebug:
                    print(f"Saved temporary item details to {temp_json_path}")
                    # Run the matcher subprocess and await its result
                    print("Running matcher subprocess...")

                await CLogController.MSWriteLog(self.iUserID, "Debug", f"Running matcher subprocess with JSON file: {temp_json_path}")
                lsResult = run_matcher_subprocess(
                    working_dir=CGeneralizePurchaseWithInventory.WORKING_DIR,
                    python_env_path=CGeneralizePurchaseWithInventory.PYTHON_EXECUTABLE_PATH,
                    json_path=temp_json_path,
                    company_name=self._mStrClientName,
                    seller_map_path=CGeneralizePurchaseWithInventory.SELLER_MAP_PATH,
                    bGlobalSearch = False # Note: Enable this to search globally in client stock item data
                )
                await CLogController.MSWriteLog(self.iUserID, "Debug", f"Matcher subprocess completed with {len(lsResult) if lsResult else 0} results")
                if self.bDebug:
                    print(f"Matcher subprocess completed with {len(lsResult) if lsResult else 0} results")

                # If no results, return empty list
                if not lsResult:
                    await CLogController.MSWriteLog(self.iUserID, "Debug", "No matching results found")
                    if self.bDebug:
                        print("No matching results found")
                    lsResult = ["AV Select Item"] * len(self.dictExtractedData.get("ItemDetails", []))
                    # return lsInventoryItemDetails
                
                item_details = dictGRNDetail
            
            else:
                item_details = dictGRNDetail
                lsResult = [None] * len(item_details)  # Fill with None if not using fuzzy matching
    

            for index, (dictItemDetail, fuzzyResult) in enumerate(zip(item_details, lsResult)):
                try:
                    self._mlsTotalStockItems.append(dictItemDetail)

                    # Use fuzzy matched name or direct item name
                    strStockItemName = fuzzyResult.get("bestMatch", "") if fuzzyResult else dictItemDetail.get("Item", "")
                    if "None" in strStockItemName or not strStockItemName:
                        strStockItemName = "AV Select Item"

                    strCostCenter = CGRNUtilities.MSGetCostCenter(dictItemDetail)
                    strQuantity = str(dictItemDetail.get("Stock Qty", "0"))
                    strRate = str(dictItemDetail.get("Rate", "0"))
                    iItemAmount = float(dictItemDetail.get("Amount", "0"))

                    po_date = DateHelper.convert_date(str(dictItemDetail.get("PODate"))) if dictItemDetail.get("PODate") else ""
                    # Batch Allocation
                    batch_alloc = BatchAllocationSchema(
                        godownname="Main Location",
                        batchname="Primary Batch",
                        amount=-iItemAmount,
                        actual_qty=strQuantity,
                        billed_qty=strQuantity,
                        order_no=dictItemDetail.get("PONO"),
                        tracking_no=dictItemDetail.get("GRNNO"),
                        orderduedate = po_date
                    )

                    # Accounting Allocation
                    accounting_alloc = AccountingAllocationSchema(
                        ledgername=self._mStrPurchaseLedgerName,
                        amount=-iItemAmount,
                        is_deemed_positive=True,
                        category_allocations=[
                            CategoryAllocationSchema(
                                category="Primary Cost Category",
                                is_deemed_positive=True,
                                cost_center_allocations=[
                                    CostCenterAllocationSchema(name=strCostCenter, amount=-iItemAmount)
                                ]
                            )
                        ]
                    )

                    # GST Rate Details
                    lsRateDetails = [
                        RateDetailSchema(
                            gstrate_duty_head="CGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=9
                        ),
                        RateDetailSchema(
                            gstrate_duty_head="SGST/UTGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=9
                        ),
                        RateDetailSchema(
                            gstrate_duty_head="IGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=18
                        )
                    ]

                    # Final Inventory Entry
                    inv_entry = InventoryEntrySchema(
                        stockitemname=strStockItemName,
                        rate=strRate,
                        amount=-iItemAmount,
                        actual_qty=strQuantity,
                        billed_qty=strQuantity,
                        batch_allocations=[batch_alloc],
                        gstledgersource=self._mStrPurchaseLedgerName,
                        accounting_allocations=[accounting_alloc],
                        gst_ovrd_stored_nature=None,
                        description=strStockItemName,
                        rate_details=lsRateDetails
                    )

                    lsInventoryItemDetails.append(inv_entry)

                except Exception as e:
                    print(f"Error processing stock item {dictItemDetail} : {e}")
                    await CLogController.MSWriteLog(self.iUserID, "Error", f"Error processing stock item {dictItemDetail} : {e}")
                    self._mStrTracebackLogs += f"XML Error: Row Details : {dictItemDetail} \n" + traceback.format_exc()
                    self._miSRNO += 1
                    self._mLsAccuVelocityComments.append(f"{self._miSRNO}. Manually Punch In Item: {dictItemDetail.get('ItemName')}")
                    self._mlsNonExistingStockItems.append(dictItemDetail)

        except Exception as e:
            print("Error in matcher subprocess:", str(e))
            print("Traceback:", traceback.format_exc())
            await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in matcher subprocess: {str(e)}")
            await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            # TODO: 
            lsResult = ["AV Select Item"] * len(self.dictExtractedData.get("ItemDetails", []))
            # return lsInventoryItemDetails


        
        return lsInventoryItemDetails

    async def MGetTallyXML(self, lsUDFData=None, iDocId = None,bDownloadERP = False):
        """
        Generate Tally XML for the given vendor and extracted data.

        Returns:
            dict: Dictionary containing XML content and other information
        
            1. Unknown VendorName = AV Select Ledger
        """
        dictAPIResponse = {}
        bISDeveloperMode = True if self.iUserID is None or self.strClientREQID is None or self.iDocId is None else False
        try:
            # Set default UDF data if not provided
            if lsUDFData is None:
                lsUDFData = [{}]
            
            strNarration = ""
            lsinvoice_del_notes_schema = []
            lsinvoice_order_list_schema = []

            # initialize user attributes
            self.MSetUserAttributes()

            # Create company info
            self.MCreateCompanyInfo()

            # Create party ledger
            dictVendorWisePartyInfo = self.MGetVendorConfig()
            self.MCreatePartyInfo(dictVendorConfig = dictVendorWisePartyInfo)

            # Create consignee details (same as company)
            self.MCreateConsigneeInfo()
            
            # Get GRN Details 
            # dfGRN = self.MSReadGRNFile()
            if self._GRNFileVersion == VersionType.V2: 
                latest_file = CDirectoryHelper.MSGetLatestFileFromNestedFolder(CGeneralizePurchaseWithInventory.GRN_FOLDERPATH) 
                dfGRN = CExcelHelper.read_GRN_Updated_Format(latest_file)
            else:
                dfGRN = self.MSReadGRNFile()
            
            dfMatchedGRNDetails = CGeneralizePurchaseWithInventory.MSGetGRNDetails(dfGRN, strPINumber=self.dictExtractedData.get("InvoiceNo", ""), strPIPartyName=dictVendorWisePartyInfo["party_name"])

            if dfMatchedGRNDetails.empty:
                print("No matching GRN found")
                self._mAVTallyStatus = "ValidationError"
                dictAPIResponse = self.MCreateResponse(XMLData=None, strAVComments=f"Validation Error – Tally XML: Unable to find GRN data for Invoice Number '{self.dictExtractedData.get('InvoiceNo', '')}' associated with Party Name '{dictVendorWisePartyInfo['party_name']}' in the latest GRN file: '{self.strGRNLatestFile}'.")
                if not bISDeveloperMode:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId=self.iUserID,
                        strClientREQID=self.strClientREQID,
                        DocID=self.iDocId,
                        AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                        TracebackLogs=dictAPIResponse["TracebackLogs"],
                        strAccuVelocityComments=dictAPIResponse["AVComments"]
                    )
                return dictAPIResponse
            else:
                # TODO: ------ - - -Uncomment and update below code to only punch invoice details and not grn details as no matching grn is 
                # Get count of unique item names from the matched GRN
                unique_grn_items = dfMatchedGRNDetails["Item"].dropna().unique()
                unique_grn_item_count = len(unique_grn_items)

                # Get count of extracted items from XML/JSON
                extracted_item_count = len(self.dictExtractedData.get("ItemDetails", []))


                if unique_grn_item_count != extracted_item_count:
                    
                    print("Mismatch Found in Len of PI Item and GRN Items")
                    self._mAVTallyStatus = "ValidationError"
                    dictAPIResponse = self.MCreateResponse(XMLData=None, strAVComments=f"Validation Error – Tally XML: Total Items in Purchase Invoice({len(self.dictExtractedData.get('ItemDetails', []))}) is not same as items in GRN({len(dfMatchedGRNDetails)}).")
                    if not bISDeveloperMode:
                        await CAVRequestDetail.MSUpdateRecord(
                            iUserId=self.iUserID,
                            strClientREQID=self.strClientREQID,
                            DocID=self.iDocId,
                            AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                            TracebackLogs=dictAPIResponse["TracebackLogs"],
                            strAccuVelocityComments=dictAPIResponse["AVComments"]
                        )
                    return dictAPIResponse
                
            self.lsAllItems, lsMappedGRNPOAVComments = await CGeneralizePurchaseWithInventory.MSGetMappedGRNItems(iUserId=self.iUserID, dfGRNDetails=dfMatchedGRNDetails,bDownloadERP=bDownloadERP)
            self._mLsAccuVelocityComments.extend(lsMappedGRNPOAVComments)
            dictGRNDetail = None
                       
            # Calculating the Tax Amount
            self.MGetTaxAmount()
            
            # Calculating gross amount and roundoff amount
            self.MCalculateRoundOff()
            
            # Calculating the total amount
            self.MGetTotalAmount()
            
            items = self.lsAllItems if self.lsAllItems else None

            if items and isinstance(items, list) and len(items) > 0:
                strCostCenter = CGRNUtilities.MSGetCostCenter(items[0])
            else:
                strCostCenter = None  # or handle appropriately


            

            # Get inventory items
            lsStockItemInfo = await self.MGetAllStockItemInfo(bUseFuzzy=CGeneralizePurchaseWithInventory._mbUseFuzzyMatching)
            if not lsStockItemInfo:
                self._mAVTallyStatus = "Skipped"
                dictAPIResponse = self.MCreateResponse(XMLData=None, strAVComments="Error – Tally XML: Unable to generate XML for all stock items due to an unknown error. Please enter the items manually.")
                if not bISDeveloperMode:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId=self.iUserID,
                        strClientREQID=self.strClientREQID,
                        DocID=self.iDocId,
                        AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                        TracebackLogs=dictAPIResponse["TracebackLogs"],
                        strAccuVelocityComments=dictAPIResponse["AVComments"]
                    )
                return dictAPIResponse

            # Get total amount
            # fTotalAmount = self.dictExtractedData.get("TotalAmount", 0)

            # Create party bill allocation
            partyBillAllocation = BillAllocationSchema(
                name=self.dictExtractedData.get("InvoiceNo", ""),
                billtype="New Ref",
                amount=self.mfGrandTotalAmount,
                billcreditperiod="45 Days" # Static Value Specified By Abhinav Infra
            )

            # Create party ledger entry
            party_ledger_entry = LedgerEntrySchema(
                ledger_name=self.party.party_name,
                amount=self.mfGrandTotalAmount,
                is_deemed_positive=False,
                is_party_ledger=True,
                bill_allocation=partyBillAllocation
            )

            # Create tax ledger entries
            ledger_entries = [party_ledger_entry]

            # Add CGST entry if available
            if "Taxes" in self.dictExtractedData and "MainTaxes" in self.dictExtractedData["Taxes"] and self.dictExtractedData["Taxes"]["ApplicableGstTaxType"] == "CGST+SGST":
                cgst_entry = LedgerEntrySchema(
                    ledger_name="CGST",
                    amount=-(self._mfTaxAmount/2),
                    is_deemed_positive=True,
                    is_party_ledger=False
                )
                ledger_entries.append(cgst_entry)

                # Add SGST entry if available
                sgst_entry = LedgerEntrySchema(
                    ledger_name="SGST",
                    amount=-(self._mfTaxAmount/2),
                    is_deemed_positive=True,
                    is_party_ledger=False
                )
                ledger_entries.append(sgst_entry)
            elif "MainTaxes" in self.dictExtractedData["Taxes"] and self.dictExtractedData["Taxes"]["ApplicableGstTaxType"] == "IGST":
                igst_entry = LedgerEntrySchema(
                    ledger_name="IGST", # Client Tally Tax Ledger Name
                    amount=-self._mfTaxAmount,
                    is_deemed_positive=True,
                    is_party_ledger=False
                )
                ledger_entries.append(igst_entry)

            
            roundoff_entry = LedgerEntrySchema(
                ledger_name="Round Off",
                amount=-float(self.mfRoundoff),  # Use float for serialization if needed
                is_deemed_positive=True,
                is_party_ledger=False
            )
            ledger_entries.append(roundoff_entry)

            # Generate Narration
            strNarration = await self.MSGenerteNarration(strCostCentre=strCostCenter)

            invoice_no = self.dictExtractedData.get("InvoiceNo", "")
            
            # PO and GRN Invoice Level Reference
            if self.lsAllItems:
                try:
                    # TODO: Please change input parameter to Dhruvin MAPPED GRN Details instead of lsGRNDetails
                    lsDetailsFromGRN = CGeneralizePurchaseWithInventory.MSExtractPOGRNDetailFromGRN(self.lsAllItems)
                    
                    # Create a method to check duplicate and insert data into table
                    grn_numbers = [item.get('GRN No') for item in lsDetailsFromGRN if item.get('GRN No') and item.get('GRN No') != '-']
                    if grn_numbers:
                        bDuplicateGRN = await CGrnNoPITracking.MSCheckDuplicateGrn(grn_numbers, self.iUserID, invoice_no = invoice_no)
                        if not bDuplicateGRN:
                            kwargs = {
                                "Doc_id" : iDocId,
                                "GrnNo" : grn_numbers,
                                "VendorName": self.dictExtractedData['SellerDetails']['SellerName']
                            }
                            await CGrnNoPITracking.MSInsertGrnRecord(kwargs, self.iUserID)
                    for dictGRNDetail in lsDetailsFromGRN:
                        strPODate = dictGRNDetail.get("PO Date", "")
                        strGRNDate = dictGRNDetail.get("GRN Date", "")
                        """
                        strGRNDate look like "20250515" if found greater than this 
                        """
                        strPONumber = dictGRNDetail.get("PO No.", "")
                        strGRNNumber = dictGRNDetail.get("GRN No", "")
                        # Verifying If Empty
                        if strPODate and strPONumber:
                            poInvoiceDetail = InvoiceOrderListSchema(
                                basic_order_date=strPODate,
                                basic_purchase_order_no=strPONumber
                            )
                            lsinvoice_order_list_schema.append(poInvoiceDetail)
                        if strGRNDate and strGRNNumber:
                            
                            grnInvoiceDetail = InvoiceDelNotesSchema(
                                basic_shipping_date=strGRNDate,
                                basic_ship_delivery_note=strGRNNumber
                            )

                            lsinvoice_del_notes_schema.append(grnInvoiceDetail)

                            # Optional: GRN Date found to be greater than invoice date then update invoice date
                            try:
                                grn_date = datetime.strptime(strGRNDate, "%Y%m%d")
                                invoice_date = datetime.strptime(self.dictExtractedData.get("InvoiceDate", ""), "%Y%m%d")
                                
                                if grn_date > invoice_date:
                                    self._mEffectiveDate = CBusinessIntelligence.MSRoundDateToNext5Days(strGRNDate) # Rounding GRN Date +5 , +10 , +15,  +20 , so on
                            except ValueError:
                                print(f"Invalid date format encountered: {strGRNDate} or Invoice Date {self._mInvoiceDate}, Effective Date: {self._mEffectiveDate}")
                except Exception as e:
                    if str(e).startswith("ValidationError"):
                        raise e
                    if self.bDebug:
                        print("Error in extracting PO and GRN details from lsGRN:", str(e))
                    await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in extracting PO and GRN details from GRN: {str(e)}")
                    await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise
            
            if dictVendorWisePartyInfo["party_name"].strip().lower() in ['ultratech cement ltd. (rmc)' , "acc limited", "hella infra market private limited"]:
                invoice_no = self.y.get("InvoiceNo", "")[-4:]
            
            # Create voucher
            voucher = TallyPurchaseInventoryVoucherSchema(
                company_info=self.company,
                voucher_class=self._mStrVoucherClassName, # Client Specific Voucher Class Name
                party_details=self.party,
                consignee_details=self.consignee,
                voucher_number=datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
                invoice_date=self._mInvoiceDate,
                effective_date=self._mEffectiveDate,
                invoice_no=invoice_no,
                voucher_type="AV PURCHASE", #TODO: AV PURCHASE for generalize customer wise
                ledger_entries=ledger_entries,
                inventory_entries=lsStockItemInfo,
                udf_data=lsUDFData,
                strCompGSTIN = self._mGSTIN,
                basicduedateofpymt = self._mStrDueDateDays,
                bIsVoucherDateSameAsInvoiceDate = self.bIsVoucherDateSameAsInvoiceDate,
                narration = strNarration,
                invoice_del_notes_schema = lsinvoice_del_notes_schema,
                invoice_order_list_schema = lsinvoice_order_list_schema,
                cost_center_name=strCostCenter
            )

            # Generate XML content
            strXMLContent = voucher.to_string(pretty=True)

            # Save XML content to GitIgnore directory
            if self.bDebug:
                try:
                    os.makedirs(Path("GitIgnore/GeneralizePWIV3/XML"), exist_ok=True)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    xml_file_path = Path(f"GitIgnore/GeneralizePWIV3/XML/xml_output_{timestamp}.xml")
                    with open(xml_file_path, "w", encoding="utf-8") as xml_file:
                        xml_file.write(strXMLContent)
                    print(f"XML content saved to {xml_file_path}")
                except Exception as e:
                    if self.bDebug:
                        print(f"Error saving XML content: {str(e)}")
                    await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in Saving XML: {str(e)}")
                    await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise

            if len(self._mlsNonExistingStockItems) > 0:
                self._mAVTallyStatus = "PartialSuccess"
                strAVComments = "PartialSuccess - Tally XML: ".join(self._mLsAccuVelocityComments)
                dictAPIResponse = self.MCreateResponse(XMLData=strXMLContent, strAVComments=strAVComments)
            else:
                self._mAVTallyStatus = "Success"
                if self._mLsAccuVelocityComments:
                    strAVComments = "NOTE - Tally XML: " + " | ".join(self._mLsAccuVelocityComments)
                else:
                    strAVComments = "-"
                dictAPIResponse = self.MCreateResponse(XMLData=strXMLContent, strAVComments=strAVComments)
            
        except Exception as e:
            # Check if the exception has a 'detail' attribute and starts with 'ValidationError'
            if hasattr(e, 'detail') and isinstance(e.detail, str) and e.detail.startswith("ValidationError"):
                self._mAVTallyStatus = "ValidationError"
                strAVComments = e.detail
            elif str(e).startswith("ValidationError"):
                self._mAVTallyStatus = "ValidationError"
                strAVComments = str(e)
            else:
                self._mAVTallyStatus = "Skipped"
                strAVComments = "Error Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            # Handle exceptions
            # strErrorMsg = f"Error in MGetTallyXML: {str(e)}\n{traceback.format_exc()}"
            self._mStrTracebackLogs += "MGetTallyXML: " + traceback.format_exc()
            if self.bDebug:
                print("Error Occur while Creating XML File: ",str(traceback.format_exc()))
            
            await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in MGetTallyXML: {str(e)}")
            dictAPIResponse = self.MCreateResponse(strAVComments=strAVComments)
        
        if not bISDeveloperMode:
            if dictAPIResponse["TallyStatus"] in ["Success","PartialSuccess"]:

                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="Unknown", no_of_stock_items=len(self._mlsTotalStockItems) - len(self._mlsNonExistingStockItems), strVoucherType="PV_WITH_INVENTORY")
            else:
                strTimeSaved = "NOT_APPLICABLE"
                
            await CAVRequestDetail.MSUpdateRecord(
                iUserId=self.iUserID,
                strClientREQID=self.strClientREQID,
                DocID=self.iDocId,
                AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                TracebackLogs=self._mStrTracebackLogs,
                strAccuVelocityComments=dictAPIResponse["AVComments"], 
                EstAccountantTimeSaved = strTimeSaved
            )
        return dictAPIResponse

    def MCreateResponse(self, XMLData = None, strAVComments=None):
        
        return {
            "xmlContent": XMLData,
            "allStockItems": self._mlsTotalStockItems,
            "nonExistingStockItems": self._mlsNonExistingStockItems,
            "TallyStatus": self._mAVTallyStatus,
            "AVComments": strAVComments,
            "TracebackLogs": self._mStrTracebackLogs
        }
    
    @staticmethod
    def MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = {}, strInputText = "shyam traders"):
        try:
            # Open the JSON file and load the data
            if strLedgerConfigFile is not None:
                with open(strLedgerConfigFile, "r") as file:
                    dictLedgerConfig = json.load(file)
            
            inp = strInputText.lower().strip()
            
            if not inp:
                return " "
            else:
                # Naive search: Simple substring search
                lsNaiveSearch = []
                for key in dictLedgerConfig.keys():
                    if inp in key.lower():
                        lsNaiveSearch.append(key)
                print("Naive Search Results:", lsNaiveSearch)
            
                # Fuzzy search using fuzzywuzzy with token_sort_ratio for better matching
                lsFuzzySearch = process.extract(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                threshold = 70
                filtered_fuzzy_results = [match for match in lsFuzzySearch if match[1] >= threshold]
                print("Filtered Fuzzy Search Results:", filtered_fuzzy_results)
            
                # Best match using extractOne
                best_match = process.extractOne(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                print("Best Fuzzy Match:", best_match[0])
                return best_match[0]
        except Exception as e:
            return " "

    @staticmethod
    def MSSaveXMLToGitIgnore(dictResponse, strPrefix="xml_output"):
        """
        Save XML content to GitIgnore directory.

        Args:
            dictResponse: Dictionary containing XML content
            strPrefix: Prefix for the filename (default: "xml_output")

        Returns:
            str: Path to the saved file
        """
        try:
            # Create GitIgnore directory if it doesn't exist
            os.makedirs(Path("GitIgnore/Abhinav Infra/XML"), exist_ok=True)

            # Generate a timestamp for the filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save the XML content to a file in the GitIgnore directory
            if dictResponse and dictResponse.get("xmlContent"):
                xml_file_path = Path(f"GitIgnore/Abhinav Infra/XML/{strPrefix}_{timestamp}.xml")
                with open(xml_file_path, "w", encoding="utf-8") as xml_file:
                    xml_file.write(dictResponse["xmlContent"])
                print(f"XML content saved to {xml_file_path}")
                return xml_file_path
            else:
                error_file_path = Path(f"GitIgnore/Abhinav Infra/XML/{strPrefix}_error_{timestamp}.txt")
                with open(error_file_path, "w", encoding="utf-8") as error_file:
                    error_file.write(f"Error: {dictResponse.get('ErrorMsg', 'Unknown error')}")
                print(f"Error occurred. Details saved to {error_file_path}")
                return error_file_path
        except Exception as e:
            print(f"Error saving XML to GitIgnore: {str(e)}")
            return None


if __name__ == "__main__":
    import asyncio
    import json
    import os
    from datetime import datetime

    # Create GitIgnore directory if it doesn't exist
    os.makedirs("GitIgnore\\Abhinav Infra", exist_ok=True)

    # Load the JSON data
    with open(r"H:\AI Data\Abhinav Infrabuild PVT. LTD\DemoInv\1.json") as f:
        dictExtractedData = json.load(f)

    # # Call the method to get the XML content
    # dictResponse = asyncio.run(CGeneralizePurchaseWithInventory.MGetTallyXML(
    #     iUserId=4,
    #     dictExtractedData=dictExtractedData,
    #     strVendorName="ASHKELON ENTERPRISES",
    #     lsUDFData=None,
    #     strClientREQID=None,
    #     iDocId=None,
    #     strCustomerName="Abhinav InfraBuild"
    # ))

    objGeneralizePWIV3 = CGeneralizePurchaseWithInventory(iUserID  = 11, dictExtractedData = dictExtractedData, iDocId = None, strClientREQID = None, bDebug=True)
    dictTallyResponse = asyncio.run( objGeneralizePWIV3.MGetTallyXML(lsUDFData=None, iDocId = 2))
    XMLData = dictTallyResponse.get("xmlContent")
    # print(CGeneralizePurchaseWithInventory.MSReadPOFile())



    # test_data = [
    # {
    #     'GRN No': 'G001',
    #     'GRN_Date': datetime(2024, 5, 10),
    #     'PODetails': {
    #         'PO NO.': 'PO123',
    #         'PO Date': '01/May/2024'
    #     }
    # },
    # {
    #     'GRN No': 'G002',
    #     'GRN_Date': datetime(2024, 5, 11),
    #     'PODetails': {
    #         'PO NO.': 'PO124',
    #         'PO Date': '02/May/2024'
    #     }
    # },
    # {
    #     'GRN No': 'G001',  # Duplicate GRN No
    #     'GRN_Date': datetime(2024, 5, 12),
    #     'PODetails': {
    #         'PO NO.': 'PO125',
    #         'PO Date': '03/May/2024'
    #     }
    # },
    # {
    #     'GRN No': 'G003',
    #     'GRN_Date': datetime(2024, 5, 13),
    #     'PODetails': {
    #         'PO NO.': 'PO123',  # Duplicate PO NO.
    #         'PO Date': '01/May/2024'
    #     }
    # },
    # {
    #     'GRN No': 'G004',
    #     'GRN_Date': datetime(2024, 5, 14),
    #     'PODetails': {
    #         'PO NO.': 'PO126',
    #         'PO Date': '04/May/2024'
    #     }
    # },
    # ]
    # result = CGeneralizePurchaseWithInventory.MSExtractPOGRNDetailFromGRN(test_data)
    # print(result)
    # TESTING
    # dfGRN = CGeneralizePurchaseWithInventory.MSReadGRNFile()
    # dfPO = CGeneralizePurchaseWithInventory.MSReadPOFile()

    # dfMatchedGRNDetails = CGeneralizePurchaseWithInventory.MSGetGRNDetails(dfGRN, strPINumber="ST/24-25/4463", strPIPartyName="SHYAM TRADERS")
    # print(dfMatchedGRNDetails)
    # strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="Abhinav InfraBuild", no_of_stock_items=2, strVoucherType="PV_WITH_INVENTORY")

    # Save the XML content to a file in the GitIgnore directory
    # CGeneralizePurchaseWithInventory.MSSaveXMLToGitIgnore(dictResponse, "test_output")