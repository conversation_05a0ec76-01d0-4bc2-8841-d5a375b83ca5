from typing import Dict, Any
import os
import re
import json
import aiofiles
from fastapi import <PERSON><PERSON>PException
from config.constants import Constants
from config.db_config import AsyncSessionLocal  # Ensure you have an async session maker set up
from src.Models.models import Logs  # Make sure your Logs model is correctly defined
from src.Controllers.Logs_Controller import <PERSON><PERSON>ogController
from src.Controllers.prompt_controller import CPromptController
from pathlib import Path

def read_json_file(file_path):
    """
    Read JSON data from a file.

    Args:
    - file_path (str): Path to the JSON file.

    Returns:
    - dict: Parsed JSON data as a dictionary.
    """
    try:
        with open(file_path, 'r', encoding='utf-8') as file:
            data = json.load(file)
            return data
    except FileNotFoundError:
        print(f"Error: File '{file_path}' not found.")
        return None
    except json.JSONDecodeError as e:
        print(f"Error decoding JSON from '{file_path}': {str(e)}")
        return None
        

def replace_sample_with_zero(data):
    """
    Replaces 'Sample' values in the dictionary with 0.
    
    Args:
        data (dict): Input dictionary containing nested fields and tables with 'Sample' values.
    
    Returns:
        dict: Modified dictionary with 'Sample' replaced by 0.
    """
    if isinstance(data, dict):
        return {key: replace_sample_with_zero(value) for key, value in data.items()}
    elif isinstance(data, list):
        return [replace_sample_with_zero(item) for item in data]
    else:
        return 0 if data == 'Sample' else data


class MakeModelGPTPrompt:
    INIT_MODEL_CONFIG = Path(r"resource/UserIntializeModelConfig.json")
    
    @staticmethod
    def UpperCaseFormatString(field_description,isFieldDescSeparate=False):
        if field_description is None:
            field_description = ""
            
        def capitalizeMatch(match):
            return match.group(0).capitalize()
        # Replace double quotes with single quotes
        field_description = field_description.replace('"', "'")
        # Replace tabs and new lines with spaces
        field_description = re.sub(r'[\t\n]+', ' ', field_description)
        # Capitalize the first letter of each word and join them without spaces
        formatted_string = re.sub(r'\b\w', capitalizeMatch, field_description)
        
        # If not isFieldDescSeparate, remove spaces from the string
        if not isFieldDescSeparate:
            formatted_string = formatted_string.replace(' ', '')
            formatted_string = formatted_string.replace("'", '')
        return formatted_string
    
    @staticmethod
    def isStrEmpty(input_string):
        # This regex pattern matches all whitespaces and newline characters
        cleaned_string = re.sub(r'\s+', '', input_string)
        return cleaned_string
    
    @staticmethod
    async def MSSetModelFieldValue( UserID,selectedCategoryName, current_format, field_description,json_file_path= Constants.strModelCategoryFormatPath):
        try:
            # Check if the file exists
            if not os.path.exists(json_file_path):
                raise FileNotFoundError(f"The file '{json_file_path}' does not exist.")
            data = {}
            with open(json_file_path) as file:
                data = json.load(file)

            for category in data['categories']:
                if category['CategoryName'].lower() == selectedCategoryName.lower():
                    currentCategory = (category['CategoryName'].lower()).strip()
                    if ((isinstance(current_format, str) and current_format.lower() == "as per your document" and currentCategory == "date") or 
                    (currentCategory in  ["only text", "numbers & text", "currency","boolean"])):
                        result_string = "String"
                    elif currentCategory == "only numbers":
                        result_string = "Digit"
                    elif (current_format.lower()).strip() != "as per your document" and currentCategory == "date":
                        # Assuming current_format is given 
                        result_string = ""
                    else:
                        result_string = "String"
                    
                    if isinstance(current_format, str) and current_format.strip() and (current_format.lower() != "as per your document"):
                        if not result_string:
                            # Exclude 'In' Keyword when No Field Type Mention
                            result_string += current_format.strip()
                        else:
                            result_string += f"In{current_format.strip()}"

                    if field_description and isinstance(field_description, str) and MakeModelGPTPrompt.isStrEmpty(field_description):
                        description = field_description[:Constants.MaxModelFieldCharLength]  # WARN limit description to max 50 characters
                        result_string += f"FollowsFormat{field_description}"

                    # Convert first letter of every word to uppercase
                    result_string = re.sub(r"(^|\s)(\w)", lambda m: m.group(1) + m.group(2).upper(), result_string)

                    # Remove single and extra spaces
                    result_string = re.sub(r'\s+', '', result_string.strip())
                    return result_string       
            else:
                raise ValueError("Invalid category name provided.")
        except FileNotFoundError:
            raise HTTPException(status_code=404, detail="File not found.")
        except json.JSONDecodeError:
            raise HTTPException(status_code=500, detail="Error decoding JSON.")
        except Exception as e:
            raise HTTPException(status_code=500, detail=str(e))
    
    @staticmethod
    async def ReadConfigFile(UserID: int, StrPath: str) -> Dict[str, Any]:
        """
        Asynchronously reads and returns the JSON configuration from the specified file path.
        """
        try:
            async with aiofiles.open(StrPath, 'r', encoding='utf-8') as File:
                content = await File.read()
                await CLogController.MSWriteLog(UserID, "Info", f"Successfully read config file from {StrPath}")
                return json.loads(content)
        except FileNotFoundError as e:
            await CLogController.MSWriteLog(UserID, "Error", f"Configuration file at {StrPath} was not found.")
            raise HTTPException(status_code=404, detail=f"The configuration file at {StrPath} was not found.") from e
        except json.JSONDecodeError as e:
            await CLogController.MSWriteLog(UserID, "Error", f"Failed to decode JSON from the configuration file at {StrPath}.")
            raise HTTPException(status_code=400, detail=f"Failed to decode JSON from the modelconfiguration file at {StrPath}.") from e
        except Exception as e:
            await CLogController.MSWriteLog(UserID, "Error", f"An unexpected error occurred while reading the configuration file at {StrPath}: {str(e)}")
            raise HTTPException(status_code=500, detail=f"An unexpected error occurred while reading the configuration file at {StrPath}") from e
    
    @staticmethod
    def TranformToGeminiDict(UserID, dictModelData):
        """
        Transforms database data into a structured dictionary with all values as strings.
        
        Args:
            input_list (List[Dict[str, Any]]): A list of dictionaries representing database data.
        
        Returns:
            Dict[str, Any]: A dictionary with transformed data with all values as strings.
        """
        ResultDict = {}

        # Process Fields
        for key, value in dictModelData.items():
            if key.lower() == "tables":
                if  value:
                    ResultDict["Tables"] = {}
                    for table in value:
                        table_name = table["TableName"]
                        table_fields = table["Fields"]
                        table_field_dict = {}
                        for field in table_fields:
                            FieldNameTransformed = MakeModelGPTPrompt.UpperCaseFormatString(field.get('FieldName', ""))
                            table_field_dict[FieldNameTransformed] = "String"
                        ResultDict["Tables"][table_name] = [table_field_dict]
            else:
                ResultDict.setdefault(str(key), [])
                dictParentFields = {}
                for field in value:
                    FieldNameTransformed = MakeModelGPTPrompt.UpperCaseFormatString(field.get('FieldName', ""))
                    dictParentFields[FieldNameTransformed] = "String"
                ResultDict[str(key)] = dictParentFields

        return ResultDict

    @staticmethod
    def TranformToPreDefinedModelDict(UserID, dictModelData):
        """
        Transforms database data into a structured dictionary with all values as strings.
        
        Args:
            input_list (List[Dict[str, Any]]): A list of dictionaries representing database data.
        
        Returns:
            Dict[str, Any]: A dictionary with transformed data with all values as strings.
        """
        ResultDict = {}
        # Assing Sample Value to Every Extraction Field of Model 
        strStaticFieldValue = "Sample"
        # Process Fields
        for key, value in dictModelData.items():
            if key.lower() == "tables":
                # if value:
                ResultDict["Tables"] = {}
                for table in value:
                    table_name = table["TableName"]
                    table_fields = table["Fields"]
                    table_field_dict = {}
                    for field in table_fields:
                        table_field_dict[field.get('FieldName')] = strStaticFieldValue
                    ResultDict["Tables"][table_name] = [table_field_dict]
            else:
                ResultDict.setdefault(str(key), [])
                lsFields = []
                for field in value:
                    lsFields.append({field.get('FieldName') : strStaticFieldValue})
                ResultDict[str(key)] = lsFields
        ResultDict = CKeyValMapper.MSMapUserGivenFields(dictModelData, ResultDict)
        return ResultDict

    @staticmethod
    def preDefinedHumanApprovalDict(dictModelData):
        """
        Replaces 'Sample' values in the dictionary with 0 for pre-defined human approval dict handling.
        
        Args:
            data (dict): Input dictionary containing nested fields and tables with values to replace.
        
        Returns:
            dict: Modified dictionary with 'Sample' replaced by 0.
        """
        return replace_sample_with_zero(dictModelData)
    
    @staticmethod
    async def TransformDBDataIntoDict(UserID, dictModelData):
        ResultDict = {}
        
        # Process Fields
        for Key in dictModelData.keys():
            if Key.lower() == "tables":
                # Process Tables & respective table fields
                if dictModelData["Tables"]:
                    ResultDict.setdefault(str(Key), {})
                    for table in dictModelData["Tables"]:
                        table_name = table["TableName"]
                        table_fields = table["Fields"]
                        table_field_dict = {}
                        for field in table_fields:
                            FieldNameTransformed = MakeModelGPTPrompt.UpperCaseFormatString(field.get('FieldName', ""))
                            FieldCategory = field.get('FieldCategory', "")
                            FieldFormat = field.get('FieldFormat', "")
                            FieldDescription = field.get('FieldDescription', "")
                            TransformFieldData = await MakeModelGPTPrompt.MSSetModelFieldValue(UserID, selectedCategoryName=FieldCategory, current_format=FieldFormat, field_description=FieldDescription)
                            table_field_dict[FieldNameTransformed] = f"Actual{FieldNameTransformed}In{TransformFieldData}"
                        ResultDict["Tables"][table_name] = [table_field_dict]
            else:
                #  Provide Parent Field Header
                ResultDict.setdefault(str(Key), [])
                for field in dictModelData[Key]:
                    FieldNameTransformed = MakeModelGPTPrompt.UpperCaseFormatString(field.get('FieldName', ""))
                    FieldCategory = field.get('FieldCategory', "")
                    FieldFormat = field.get('FieldFormat', "")
                    FieldDescription = field.get('FieldDescription', "")
                    TransformFieldData = await MakeModelGPTPrompt.MSSetModelFieldValue(UserID, selectedCategoryName=FieldCategory, current_format=FieldFormat, field_description=FieldDescription)
                    
                    ResultDict[str(Key)].append({FieldNameTransformed: f"Actual{FieldNameTransformed}In{TransformFieldData}"})
        
        else:
            return ResultDict

    
    @staticmethod
    async def MakePromptDict(UserID: int, iModelId: int, strModelName: str,strFamilyName:str):
        """
        Asynchronously constructs a prompt dictionary for GPT model execution based on input data and configuration.
        """
        try:
            from src.Controllers.Vendor_Controller import CVendorController
            from src.utilities.DBHelper import CModelTable

                
            GetVendorDetails = await CVendorController.GetAllReqInvoiceFields(iUserID=UserID, iModelId=iModelId)
            
            if 'ModelData' not in GetVendorDetails.keys():
                raise HTTPException(status_code=404, detail=f"No ModelFields found in ModelFields for this userID {UserID}")
            else:
                dictModelData = GetVendorDetails['ModelData']
            
            # Get PromptMessageStr, ResultDict from Fields Meta Data
            preDefinedModelDict = MakeModelGPTPrompt.TranformToPreDefinedModelDict(UserID=UserID, dictModelData=dictModelData) 
            ResultDict = await MakeModelGPTPrompt.TransformDBDataIntoDict(UserID=UserID, dictModelData=dictModelData)
            GeminiResultDict = MakeModelGPTPrompt.TranformToGeminiDict(UserID=UserID, dictModelData=dictModelData)
            await CModelTable.MSUpdateModelPreDefinedColumn(model_id=iModelId , user_id =UserID, updated_dict=preDefinedModelDict)
            await MakeModelGPTPrompt.MakeModelPrompt(UserID=UserID, ModelId=iModelId, ResultDict=ResultDict, strModelName=strModelName,strFamilyName=strFamilyName)
            await MakeModelGPTPrompt.MakeModelPrompt(UserID=UserID, ModelId=iModelId, ResultDict=GeminiResultDict, strModelName=strModelName, strFamilyName=strFamilyName,isGemini=True)
            await CLogController.MSWriteLog(UserID, "Info", "Prompt dictionary created successfully.")
            
        except json.JSONDecodeError:
            # Specific error for JSON issues
            raise HTTPException(status_code=500, detail="Failed to parse configuration file. Please contact the Developer team.")
        except FileNotFoundError:
            # Configuration file not found
            raise HTTPException(status_code=500, detail="Configuration file not found. Please contact the Developer team.")
        except (ValueError, TypeError) as e:
            # For our custom validations and other errors
            raise HTTPException(status_code=500, detail=f"Document is not Processed due to Internal Server Error: {e}. Please contact the Developer team.")
        except Exception as e:
            await CLogController.MSWriteLog(UserID, "Error", f"An error occurred during MakePromptDict execution: {str(e)}")
            # A catch-all for any other unexpected errors
            raise HTTPException(status_code=500, detail="Document is not Processed due to an unexpected error. Please contact the Developer team.")
    
    @staticmethod
    async def MakeModelPrompt(UserID, ModelId, ResultDict, strFamilyName, strModelName, isGemini=False):
        try:
            Step1PromptStr = None
            Step3PromptStr = None
            strAPIModel = 'gemini' if isGemini else 'gpt'
            # ModelFamily_ModelName_GPT/Gemini_General
            strAPIModelSeries = f"{strFamilyName}_{strModelName}_{Constants.GeminiAPIModelName}_{Constants.GeminiAPISuffix}" if isGemini else f"{strFamilyName}_{strModelName}_{Constants.GPTAPIModelName}_{Constants.GPTAPISuffix}"
            ConfigDict = read_json_file(MakeModelGPTPrompt.INIT_MODEL_CONFIG)

            for index, dictModelConfig in ConfigDict.items():
                if isinstance(dictModelConfig.get("model_name"),str):
                    if dictModelConfig.get("model_name").lower() == str(strModelName).lower():
                        Step1PromptStr = ConfigDict[index]['model_prompt'].get(f'{strAPIModel}-prompt-step-1', None)
                        Step3PromptStr = ConfigDict[index]['model_prompt'].get(f'{strAPIModel}-prompt-step-3', None)
                        break
            else:
                if Step1PromptStr is None and Step3PromptStr is None:
                    Step1PromptStr = ConfigDict['0']['model_prompt'].get(f'{strAPIModel}-prompt-step-1', '')
                    Step3PromptStr = ConfigDict['0']['model_prompt'].get(f'{strAPIModel}-prompt-step-3', '')
                    
            FinalPromptStr = f"{Step1PromptStr} {ResultDict}{Step3PromptStr}"
            
            await CPromptController.update_prompt(iUserID=UserID, ModelId=ModelId, prompt_text=FinalPromptStr, ModelSeries=strAPIModelSeries)
        
        except Exception as e:
            await CLogController.MSWriteLog(UserID, "Error", f"An error occurred during MakeModelPrompt execution: {str(e)}")
            raise