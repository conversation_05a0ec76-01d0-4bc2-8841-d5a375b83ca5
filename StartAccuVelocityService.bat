@echo off
:: Set the path to the source directory
set SOURCE_DIR=C:\Users\<USER>\Desktop\Customer\REAL\AccuVelocity

:: Open VS Code at the specified directory
start code "%SOURCE_DIR%"

:: Wait for VS Code to open
timeout /t 5 /nobreak

:: Send commands to the terminal inside VS Code using the `code` CLI command
:: Use `code --wait` to keep the script running until VS Code is closed

:: Open the terminal in VS Code and run the first command
code --new-window "%SOURCE_DIR%" --command "workbench.action.terminal.new"
timeout /t 2
code --new-window "%SOURCE_DIR%" --command "workbench.action.terminal.sendSequence {\"text\":\"uvicorn src.main:app_main --host 0.0.0.0 --port 8024 --workers 4\n\"}"
timeout /t 2

:: Run the second command in the terminal
code --new-window "%SOURCE_DIR%" --command "workbench.action.terminal.sendSequence {\"text\":\"uvicorn src.doc_exec_main:app_gpt --host 0.0.0.0 --port 9001 --workers 4\n\"}"

exit
