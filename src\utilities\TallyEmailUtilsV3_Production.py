import random
import string
import sys
import uuid
import base64
import jwt
from pathlib import Path
sys.path.append(".")

import imaplib
import email
import os
import asyncio
import re
from datetime import datetime
from openpyxl import Workbook
from openpyxl import load_workbook
from config.db_config import AsyncSessionLocal
from sqlalchemy.future import select
from src.Models.models import EmailProcessingRecord, EmailProcessingStatusEnum, Logs_EmailProcessing, TallyBackupRecord, TallyBackupStatusEnum
import json
from dotenv import load_dotenv
import zipfile
import pytz
from src.Controllers.ParagTradersControllers import CHansgrohe_XML, CNexion_XML, CSimpolo_XML, CKohler_XML, CToto_XML, CGeberit_XML, CAquant_XML, CQuotation, CIcon_XML
from src.Controllers.CustomLogger import CLogger
import shutil
import csv

import magic
from src.utilities._TallyEmailProcessor import BaseEmailProcessor
import traceback
from email import header
from sqlalchemy import and_
from email.utils import parsedate_tz, mktime_tz
import httpx
import asyncio
from src.utilities.helperFunc import CEx<PERSON><PERSON>el<PERSON>,APIHelper
from src.utilities.TallyHelper import CDocument
from src.utilities.PriceListAVREPORT import CPriceListREPORT


import TallyEmailSender as TallyReportSender
load_dotenv()

class AsyncDocument:
    def __init__(self, filename, content_type, data):
        self.filename = filename
        self.content_type = content_type
        self._data = data

    async def read(self):
        return self._data

    @property
    def size(self):
        return len(self._data)
    
    def get_extension(self):
        return os.path.splitext(self.filename)[1].lower()


class EmailAttachmentProcessor(BaseEmailProcessor):
    def __init__(self, email_account, provider, password, imap_server, imap_port, clients, days_limit=7):
        super().__init__(email_account, provider, password, imap_server, imap_port)
        self.clients = clients
        self.days_limit = days_limit
        
    @staticmethod
    async def MSGetDailyStatistics(lsDictProcessedDocs, iTimePerPage):
        iTotalTimeSaved = 0
        iTotalPageProcessed = 0
        
        # Calculate TotalTime saved and Total PagesProcesed
        for dictAttachmentInfo in lsDictProcessedDocs:
            if dictAttachmentInfo["Status"] == "Added to tally":
                iTotalPageProcessed += dictAttachmentInfo["PageCount"]
                iTotalTimeSaved += dictAttachmentInfo["PageCount"] * iTimePerPage
        
        return {
            "TotalTimeSaved":iTotalTimeSaved,
            "TotalPageProcessed":iTotalPageProcessed
        }
        
    @staticmethod
    def clean_filename(filename):
        try:
            # Replace line breaks and normalize spaces
            filename = filename.replace('\r', '').replace('\n', ' ').strip()
            # Remove invalid characters for filesystems
            filename = "".join(c for c in filename if c not in r'\/:*?"<>|')
        except Exception as e:
            print(f"Failed to clean the filename, {filename}")
        return filename

    async def initialize_report(self, userid):
        """Initialize the report CSV file with headers if it doesn't exist."""
        if not os.path.isfile(self.report_file):

            await self.log_to_db(None, "INFO", f"Creating new report file: {self.report_file}")
            with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=[
                    'Received Date',
                    'Vendor Name',
                    'Invoice No',
                    'Invoice Date',
                    'Total Amount',
                    'Tally Punch-in Status',
                    'Accuvelocity Comments'
                ])
                writer.writeheader()
        else:
            await self.log_to_db(None, "ERROR", f"Report file already exists: {self.report_file}")


    async def process_filename(self, filename):
        try:
            # Decode filename if encoded
            decoded_filename, encoding = header.decode_header(filename)[0]
            if isinstance(decoded_filename, bytes):
                filename = decoded_filename.decode(encoding or 'utf-8')
            elif isinstance(decoded_filename, str) and decoded_filename.startswith('=?UTF-8?b?'):
                filename = header.decode_header(decoded_filename)[0][0].decode('utf-8')
            filename = EmailAttachmentProcessor.clean_filename(filename)

        except Exception as e:
            # Log the error if decoding fails
            await self.log_to_db(None, 'ERROR', f"Failed to decode the file name, current file name: {filename}")
        return filename


    # Function to check if the file exists in lsAllAttachmentInfo and its status
    @staticmethod
    async def is_filename_processed(filename, processed_attachments):
        for att in processed_attachments:
            if att['FileName'].lower() == filename.lower():  # Case-insensitive match
                return att['Status'] == "Added to tally"  # Returns True if status is "Added To Tally"
        return False  # Filename not found in lsAllAttachmentInfo


    async def process_email(self, email_id, client_config, uid, sender, bRetry):
            """
            Asynchronously processes email attachments by passing them to the ProcessFiles function.
            
            Args:
                email_id (str): The ID of the email to process.
                client_config (dict): Configuration dictionary containing user and client details.
                uid (str): Unique identifier (not used in this version).
                sender (str): Email sender address.
                bRetry (bool): Indicates if this is a retry attempt.
            """
            bTestMode = False  # Default as per original code

            try:
                # Ensure the connection is active before fetching the email
                await self.ensure_connection()

                # Fetch the email
                status, data = self.mail.fetch(email_id, '(RFC822)')
                msg = email.message_from_bytes(data[0][1])
                strVoucherType = msg.get('Subject', '')
                

                # Collect attachments and prepare parameters for ProcessFiles
                documents = []
                lsClientDocMetaData = []
                save_dir = "GitIgnore/EmailProcess"
                os.makedirs(save_dir, exist_ok=True)

                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = await self.process_filename(part.get_filename())
                        # Validate attachment type against allowed extensions
                        if any(filename.lower().endswith(ext) for ext in client_config['attachmentsTypes']):
                            file_data = part.get_payload(decode=True)
                            if file_data:
                                unique_filename = f"{uuid.uuid4()}_{filename}"
                                file_path = os.path.join(save_dir, unique_filename)
                                with open(file_path, 'wb') as f:
                                    f.write(file_data)
                        
                        # Create CDocument object with file path
                                document = CDocument(file_path)
                                documents.append(document)
                                # Prepare metadata dictionary
                                file_type = filename.split('.')[-1]  # Extract extension as type
                                file_metadata={
                                    'filename': filename,
                                    'Type': file_type,
                                    'location': "",
                                    "checksum":""   
                                }
                                lsClientDocMetaData.append(str(file_metadata))
                                
                  
                  
                files = []  
                checksums = []      
                for doc in documents:
                                file_content = doc.read_file()
                                files.append(("documents", (doc.filename, file_content, doc.content_type)))
                                checksums.append(doc.checksum)
                
                data = {
                "checksums": ",".join(checksums)
            }


                        


                
                strCurrentTime = datetime.now().strftime("%H%M%S")
                formatted_customer_name = client_config["customerName"].replace(" ", "").upper() if not bTestMode else "AVDEVELOPER"
                strRandomAlpaNumeric = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
                
                strClientREQID = f"REQ_CS{formatted_customer_name}_TS{strCurrentTime}_UID{strRandomAlpaNumeric}"
                # Prepare parameters for ProcessFiles
                iUserid = client_config['userId']  # Extracted from client_config
                checksums = ""  # Default value as not computed in original code
                # strSerializeUserConfig = ""  # Default as specified
                strSerializeUserConfig = f"""{{"Exe_version": "1.11", "Exe_ReleaseDate": "2025-03-29", "Tdl_version": "8.0", "Tdl_ReleaseDate": "2025-03-29", "worker": 2, "apiEndpoints": ["http://*************:8034/", "http://**************:8034/", "http://*************:8024/", "http://**************:8024/"], "USER_UUID": "", "iPageAllowedPerDay": 51, "iRequestAllowedPerDay": 101, "strClientREQID": "{strClientREQID}", "ClientReqDir": "Requests"}}"""
                
                
                bScanForVirus = False  # Default from ProcessFiles signature
                bIsMultivendorDoc = False  # Default as specified
                strSystemName = client_config['customerName']  # Default from ProcessFiles signature
                

                # Call ProcessFiles with collected parameters
                if documents:  # Only call if there are attachments to process
                    strVoucherTypeobj=strVoucherType
                    
                    strDocProcessingURL=APIHelper.MSDecideServiceURL()
                    
                    # strDocProcessingURL = "http://************:8029/IndianInvTally/process_doc"
                        
                    
                    # JWT Payload
                    payload = {
                        "uid": iUserid,
                        "name": client_config["customerName"],
                        "mac_address": "",
                        "role":"General",
                    }
                    
                    SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')

                    # Create JWT Token
                    strToken = jwt.encode(payload, SECRET_KEY, algorithm="HS256")
                    headers = {
                    "Authorization": f"Bearer {strToken}"
                }
                    
                    with httpx.Client(timeout=600) as client:
                        response = client.post(
                            strDocProcessingURL,
                            headers=headers,
                            params={"bTestMode":bTestMode,"bScanForVirus":False ,"strVoucherType":strVoucherTypeobj,"strSerializeUserConfig": strSerializeUserConfig,"lsClientDocMetaData":lsClientDocMetaData, "bIsMultivendorDoc":bIsMultivendorDoc,"strSystemName":strSystemName,"bEmail":True},
                            files=files,
                            data=data
                        )

                        if response.status_code != 200:
                            error_message = f"Failed to process documents for email {email_id}. Status code: {response.status_code}, Response: {response.text}"
                            await self.log_to_db(client_config['userId'], 'ERROR', error_message)
                        else:
                            await self.log_to_db(client_config['userId'], 'INFO', f"Successfully processed documents for email {email_id}.")
                        
                        await self.log_to_db(client_config['userId'], 'INFO', f"Processed email {email_id} attachments via ProcessFiles.")
                else:
                    await self.log_to_db(client_config['userId'], 'INFO', f"No valid attachments found in email {email_id}.")

            except Exception as e:
                await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to process email {email_id}: {str(e)}")









    async def update_report(self, entry):
        """
        Update the report CSV with a new entry.
        If an entry for the same filename or invoice number exists, update only the provided fields
        while keeping the existing data for other fields intact.
        """
        # Read existing entries
        entries = []
        bEntryAlreadyExist = False
        if os.path.isfile(self.report_file):
            with open(self.report_file, mode='r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # Check if the current row matches the entry by 'Invoice No' or 'File Name'
                    if (row['File Name'] == entry.get('File Name')):
                        if (row['Tally Punch-in Status'] != 'Success'):
                            # Update only provided fields and retain others
                            for key, value in entry.items():
                                if value is not None and value != "-":
                                    row[key] = value
                        bEntryAlreadyExist = True
                    entries.append(row)

        # If no matching entry was found, append the new entry
        if not bEntryAlreadyExist:
            entries.append(entry)

        # Write back all entries
        with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
            
            fieldnames = [
                'File Name',
                'Received Date',
                'Vendor Name',
                'Invoice No',
                'Invoice Date',
                'Total Amount',
                'Total Pages',
                'Estimated Time Saved',
                'Tally Punch-in Status',
                'Pricelist Verified',
                'Accuvelocity Comments'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(entries)

        await self.log_to_db(None, 'INFO', f"Report updated for file: {entry.get('Invoice No') or entry.get('File Name')}")


    async def fetch_and_process_emails(self):
        while True:
            try:
                email_ids = await self.fetch_emails(self.days_limit)
                relevant_emails = []

                async with AsyncSessionLocal() as db:
                    # Ensure the connection is active before fetching the email
                    await self.ensure_connection()
                        
                    for email_id in email_ids:
                        # Fetch the email's unique identifier (UID)
                        status, data = self.mail.fetch(email_id, '(UID RFC822)')
                        if status != 'OK':
                            await self.log_to_db(None, 'ERROR', f"Failed to fetch email ID {email_id.decode()}")
                            continue

                        # Extract the UID correctly
                        uid_line = data[0][0].decode()
                        match = re.search(r'UID (\d+)', uid_line)
                        uid = match.group(1) if match else None

                        if not uid:
                            await self.log_to_db(None, 'ERROR', f"Failed to extract UID from email ID {email_id.decode()}")
                            continue

                        # Extract sender information
                        raw_email = data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        sender = msg.get('From')

                        # Check if the email is from a relevant sender for any client
                        for client_name, client_config in self.clients.items():
                            if any(s.lower() in sender.lower() for s in client_config['senderList']):
                                # Check the status of this email in the database
                                result = await db.execute(
                                    select(EmailProcessingRecord).where(
                                        and_(
                                            EmailProcessingRecord.email_id == email_id,
                                            EmailProcessingRecord.sender == sender
                                        )
                                    )
                                )
                                record = result.scalars().first()

                                if not record or (record.status == EmailProcessingStatusEnum.FAILED_TO_PROCESS and record.retry_count <= client_config["MaxRetryCount"]):
                                    bRetry=False  # To specify weather current iteration is an retry or not
                                    if record:
                                        bRetry = True
                                    relevant_emails.append((email_id, client_config, uid, sender, bRetry))

                # Process relevant emails
                for email_id, client_config, uid, sender, bRetry in relevant_emails:
                    await self.process_email(email_id, client_config, uid, sender, bRetry)
                
                # lsAttachmentsEmailProcessing = [self.process_email(email_id=email_id, client_config=client_config, uid=uid, sender=sender) for email_id, client_config, uid, sender in relevant_emails]
                
                # asyncio.gather(*lsAttachmentsEmailProcessing, return_exceptions=True)

            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to Etch and process email: {e}")   
            await asyncio.sleep(5)     # Added 10 Minutes of sleep between polling new emails with pdf attachments

    @staticmethod
    async def create_pricelist_report(iUserId, dictExtractedData, strVendorName, strReportFilePath):
        lsRows = []
        if strVendorName.lower() == "hansgrohe":
            lsRows = await CHansgrohe_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)

        elif strVendorName.lower() == "geberit":
            lsRows = await CGeberit_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "nexion":
            lsRows = await CNexion_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "simpolo":
            lsRows = await CSimpolo_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData, strversion="v3")
            
        elif strVendorName.lower() == "toto":
            lsRows = await CToto_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
            
        elif strVendorName.lower() == "kohler":
            lsRows = await CKohler_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        elif strVendorName.lower() == "aquant":
            lsRows = await CAquant_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)    
        else:
            raise ValueError("No Pricelist found for the given vendor.")
        
        # Call Function to write the csv report 
        if lsRows:
            await CPriceListREPORT.update_pricing_list_report(lsReportRows=lsRows, report_pricing_file=strReportFilePath)
            

    @staticmethod
    async def get_formatted_time(iMinutes):
        
        strFormattedString = ""
        
        try:
            # Convert time to hours and minutes format if needed
            if iMinutes >= 60:
                hours = int(iMinutes // 60)
                minutes = int(iMinutes % 60)
                strFormattedString = f"{hours} Hours {minutes} Minutes"
            else:
                strFormattedString = f"{iMinutes} Minutes"
        except Exception as e:
            pass
        
        return strFormattedString


async def runAttachmentsPolling():
    # Load configuration
    try:
        strConfigurationPath = Path(r"resource/TallyUserConfigV3_Production.json")

        with open(fr"{strConfigurationPath}", 'r') as file:
            tally_config = json.load(file)
    except Exception as e:
        print(f"Failed to load Tally User Config: {e}")
        return

    print(f"******** Running Tally In Production Mode, Using Configuration File: {strConfigurationPath} ********")
    
    # Create a downloader for each email account
    tasks = []
    for email_account, config in tally_config.items():
        processor = EmailAttachmentProcessor(
            email_account=email_account,
            provider=config['metadata']['provider'],
            password=config['metadata']['password'],
            imap_server= os.getenv('ZOHO_IMAP_SERVER') if config['metadata']['provider'] == 'zoho' else os.getenv('GMAIL_IMAP_SERVER'),
            imap_port=int(os.getenv('IMAP_PORT')),
            clients=config['clients'],
            days_limit=1
        )
        
        tasks.append(processor.fetch_and_process_emails())

    await asyncio.gather(*tasks, return_exceptions=True)


class BackupEmailProcessor(BaseEmailProcessor):
    def __init__(self, email_account, provider, password, imap_server, imap_port, backup_clients, days_limit=7):
        super().__init__(email_account, provider, password, imap_server, imap_port)
        self.backup_clients = backup_clients
        self.days_limit = days_limit

    @staticmethod
    def extract_zip_attachment(zip_file_path, extract_to=None):
        """
        Extracts the contents of a zip file to a specified directory.
        """
        try:
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                if extract_to is None:
                    extract_to = os.path.dirname(zip_file_path)
                zip_ref.extractall(extract_to)
            print(f"Extraction complete. Files extracted to {extract_to}")
            return True
        except Exception as e:
            print(f"An error occurred while extracting the zip file: {e}")
            return False
    
    @staticmethod
    async def MSGetTextFileContent(file_path):
        """
        Reads the contents of a text file and returns it as a string.

        Parameters:
        file_path (str): The path to the text file.

        Returns:
        str: The content of the text file.
        """
        try:
            with open(file_path, 'r', encoding='utf-16') as file:
                content = file.read()
            return content
        except Exception as e:
            print(f"An error occurred while reading the file: {e}")
            return None


    @staticmethod
    async def MSExtractBackupStatus(log_content):
        """
        Extracts the backup status from the log content and checks if the backup was successful.

        Parameters:
        log_content (str): The log content as a string.

        Returns:
        dict: A dictionary containing the backup success status and a message.
        """
        # Find all backup completion lines
        status_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}) \*\* Backup done for the task.*?Errors: (\d+).*?Processed files: (\d+).*?Backed up files: (\d+)', re.MULTILINE)
        matches = status_pattern.findall(log_content)
        
        if matches:
            # Get the last match
            last_match = matches[-1]
            timestamp, errors, processed_files, backed_up_files = last_match

            errors = int(errors)
            processed_files = int(processed_files)
            backed_up_files = int(backed_up_files)

            # Check backup success criteria
            if errors == 0 and processed_files > 0 and backed_up_files > 0:
                return {
                    'status': True,
                    'message': f"Backup was successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
            else:
                return {
                    'status': False,
                    'message': f"Backup was not successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
        else:
            return {
                'status': False,
                'message': "No backup status found in the log."
            }

    async def process_email(self, email_id, backup_client_config, uid, sender):
        """
        Process backup-related email to extract and store backup status.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Ensure the connection is active before fetching the email
                await self.ensure_connection()

                # Fetch the email
                status, data = self.mail.fetch(email_id, '(RFC822)')
                msg = email.message_from_bytes(data[0][1])

                try:
                    # Extract the 'Date' header from the email to get the actual received date
                    email_received_date = msg.get('Date')

                    # If the 'Date' header is available, parse it, else fall back to 'Received' or the current time
                    if email_received_date:
                        # Parse the 'Date' header to get the received date in the correct timezone
                        parsed_date = parsedate_tz(email_received_date)
                        if parsed_date:
                            email_received_date = datetime.fromtimestamp(mktime_tz(parsed_date))
                        else:
                            raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                    else:
                        # If 'Date' header is missing, fall back to 'Received' header (usually present)
                        received_headers = msg.get_all('Received', [])
                        if received_headers:
                            # Take the first Received header and parse the date
                            first_received = received_headers[0]
                            parsed_date = parsedate_tz(first_received)
                            if parsed_date:
                                email_received_date = datetime.fromtimestamp(mktime_tz(parsed_date))
                            else:
                                raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                        else:
                            raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                except Exception as e:
                    raise ValueError(f"Failed to parse received date of email, Error: {e}")

                # Extract attachment zip file
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()

                        # Proceed only if filename ends with .zip (assuming backups are zipped)
                        if filename and filename.lower().endswith('.zip'):
                            file_data = part.get_payload(decode=True)
                            if file_data:
                                try:
                                    # Create the download directory using email_received_date
                                    email_date_dir = email_received_date.strftime("%Y_%m_%d")  # Adjust format as needed
                                    client_data_dir = backup_client_config['dataDirectory']

                                    backup_download_dir = os.path.join(client_data_dir, email_date_dir, "Tally Backup")
                                    os.makedirs(backup_download_dir, exist_ok=True)
                                                                        
                                    # Save zip attachment temporarily
                                    temp_zip_path = os.path.join(backup_download_dir, filename)
                                    with open(temp_zip_path, 'wb') as f:
                                        f.write(file_data)

                                    # Extract zip
                                    self.extract_zip_attachment(temp_zip_path)

                                    # Format email_received_date to match log file naming convention
                                    log_date_str = email_received_date.strftime("%Y-%m-%d")  # Adjust format if logs use different conventions
                                    # Process extracted files to find logs matching email_received_date
                                    for root, dirs, files in os.walk(backup_download_dir):
                                        for file in files:
                                            if log_date_str in file:  # Match log file to email's received date
                                                log_file_path = os.path.join(root, file)
                                                try:
                                                    log_file_content = await BackupEmailProcessor.MSGetTextFileContent(log_file_path)
                                                    dict_backup_info = await BackupEmailProcessor.MSExtractBackupStatus(log_file_content)

                                                    # Determine backup status
                                                    status_enum = TallyBackupStatusEnum.SUCCESS if dict_backup_info['status'] else TallyBackupStatusEnum.FAILED

                                                    # Save record to TallyBackupRecord model
                                                    log_record = TallyBackupRecord(
                                                        email_id=uid,
                                                        user_id=backup_client_config['userId'],
                                                        sender=sender,
                                                        received_date=email_received_date,
                                                        logFileContent=json.dumps(log_file_content),
                                                        status=status_enum,
                                                        logMessage=json.dumps(dict_backup_info['logLine'])
                                                    )

                                                    db.add(log_record)
                                                    await db.commit()
                                                    await self.log_to_db(None, 'INFO', f"Backup status processed for {log_date_str}")
                                                except Exception as e:
                                                    await self.log_to_db(None, 'ERROR', f"Failed to process log file {file}: {e}")

                                except Exception as e:
                                    await self.log_to_db(None, 'ERROR', f"Failed to process backup email {filename}: {e}")
                await self.log_to_db(None, 'INFO', f"Processed backup email from {sender}")
            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to process backup email: {e}")

    async def fetch_and_process_emails(self):
        while True:
            try:
                email_ids = await self.fetch_emails(self.days_limit)
                relevant_emails = []

                async with AsyncSessionLocal() as db:
                    # Ensure the connection is active before fetching the email
                    await self.ensure_connection()
                        
                    for email_id in email_ids:
                        # Fetch the email's unique identifier (UID)
                        status, data = self.mail.fetch(email_id, '(UID RFC822)')
                        if status != 'OK':
                            await self.log_to_db(None, 'ERROR', f"Failed to fetch email ID {email_id.decode()}")
                            continue

                        # Extract the UID correctly
                        uid_line = data[0][0].decode()
                        match = re.search(r'UID (\d+)', uid_line)
                        uid = match.group(1) if match else None

                        if not uid:
                            await self.log_to_db(None, 'ERROR', f"Failed to extract UID from email ID {email_id.decode()}")
                            continue

                        # Check if this email UID has already been processed
                        existing_record = await db.execute(
                            select(TallyBackupRecord).where(TallyBackupRecord.email_id == uid)
                        )
                        record = existing_record.scalars().first()

                        if record:
                            continue

                        # Extract sender information and subject
                        raw_email = data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        sender = msg.get('From')
                        subject = msg.get('Subject', '')

                        # Extract device name from subject
                        device_name_match = re.search(r"Cobian Backup 11 \((.*?)\)", subject)
                        if not device_name_match:
                            continue

                        extracted_device_name = device_name_match.group(1)

                        # Check if the email is from a relevant backup sender and has matching device name
                        for client_name, client_config in self.backup_clients.items():
                            if any(s.lower() in sender.lower() for s in client_config['senderList']) and \
                                    client_config.get('backupDeviceName', '').lower() == extracted_device_name.lower():
                                relevant_emails.append((email_id, client_config, uid, sender))

                # Process relevant emails
                for email_id, client_config, uid, sender in relevant_emails:
                    await self.process_email(email_id, client_config, uid, sender)

                # lsBackupEmailProcessingTask = [self.process_email(email_id=email_id, backup_client_config=client_config, uid=uid, sender=sender) for email_id, client_config, uid, sender in relevant_emails]
                # asyncio.gather(*lsBackupEmailProcessingTask, return_exceptions=True)
            
            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to fetch and process backup emails: {e}")
            
            await asyncio.sleep(5)     # Added 10 Minutes of sleep between polling Backup status emails

async def runBackupPolling():
    # Load configuration
    try:
        strConfigurationPath = Path(r"resource/TallyUserConfigV3_Production.json")

        with open(fr"{strConfigurationPath}", 'r') as file:
            tally_config = json.load(file)
    except Exception as e:
        print(f"Failed to load Tally User Config: {e}")
        return

    print(f"******** Running Tally In Production Mode, Using Configuration File: {strConfigurationPath} ********")
    
    # Create a downloader for each email account
    tasks = []
    for email_account, config in tally_config.items():
        backup_processor = BackupEmailProcessor(
            email_account=email_account,
            provider=config['metadata']['provider'],
            password=config['metadata']['password'],
            imap_server=os.getenv('ZOHO_IMAP_SERVER') if config['metadata']['provider'] == 'zoho' else os.getenv('GMAIL_IMAP_SERVER'),
            imap_port=int(os.getenv('IMAP_PORT')),
            backup_clients=config['clients'],
            days_limit=1
        )
        
        tasks.append(backup_processor.fetch_and_process_emails())

    await asyncio.gather(*tasks, return_exceptions=True)



async def run():
    """
    Runs Email Attachments polling and Backup Status polling at the same time
    """
    try:
        await asyncio.gather(
            runBackupPolling(),        # Poll for backup emails
            runAttachmentsPolling(),  # Poll for emails with attachments
            return_exceptions=True
        )
    except KeyboardInterrupt:
        print("Program interrupted.")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == '__main__':
    CLogger.MCSetupLogging(strLogsDirPath="Logs")
    # pass
    
    # **** 1 For Polling Emails with attachments and to upload them to db *****

    # try:
    #     asyncio.run(runAttachmentsPolling())     # For polling email and downloading and uploading attachments to db
    # except KeyboardInterrupt:
    #     print("Program interrupted.")

    # ****                                                                *****


    # **** 2 For Polling for Backup Emails                                *****
    # try:
    #     asyncio.run(runBackupPolling())     # For polling email and downloading and uploading attachments to db
    # except KeyboardInterrupt:
    #     print("Program interrupted.")

    # ****                                                                *****


    # **** 3 For Running Both Attachments Polling and Backup Status Polling*****
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        print("Program interrupted.")
    except Exception as e:
        print(f"An error occurred: {e}")
    
    # ****                                                                *****

