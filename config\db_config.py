import urllib.parse
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.ext.declarative import declarative_base
from sqlalchemy.orm import sessionmaker
import os
from os.path import join, dirname

from dotenv import load_dotenv
# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")

load_dotenv(dotenv_path)

# Initialization
# Retrieve the password as a string and encode it properly
password = os.getenv('DATABASE_PASSWORD_DEV')
if password is not None:
    password_encoded = urllib.parse.quote_plus(password)
else:
    # Handle the case where the environment variable is not defined
    exit()
# Use create_async_engine for asynchronous operations
engine = create_async_engine(
    f"mysql+aiomysql://{os.getenv('DATABASE_USER')}:{password_encoded}@{os.getenv('DATABASE_URL_DEV')}:{os.getenv('DATABASE_PORT')}/{os.getenv('DATABASE_NAME')}",
    echo=False,  # You can set echo to False in production
    pool_size=25,  # Adjust pool size as per your requirement
    pool_timeout=400,  # Adjust timeout as per your requirement
    pool_recycle=3600,  # Optional: Recycle connections after a certain time
    pool_pre_ping=True,  # Optional: Enable connection health checks
    max_overflow=10,  # Maximum number of connections in excess of pool_siz
    
)

# AsyncSession configuration
AsyncSessionLocal = sessionmaker(
    engine, expire_on_commit=False, class_=AsyncSession
)

Base = declarative_base()

# Dependency to get the async session
async def get_db():
    async with AsyncSessionLocal() as session:
        yield session

if __name__ == "__main__":
    # Asynchronously create all tables. You might need to adjust this if you manage migrations differently
    import asyncio

    async def async_main():
        async with engine.begin() as conn:
            await conn.run_sync(Base.metadata.create_all)

    asyncio.run(async_main())
