import json
import traceback

from fastapi import <PERSON><PERSON>P<PERSON>x<PERSON>
from config.constants import Constants
from src.Controllers.Logs_Controller import <PERSON><PERSON>ogController
from src.Controllers.MultipleVendorRecordHelper import CMultipleVendorRecordHelper
from src.GPTAPI import C<PERSON><PERSON><PERSON>R<PERSON>ponse
from src.utilities import AWS_Async
from src.utilities.helperFunc import CJSONFileReader


class CSplitAlgo:
    @staticmethod
    async def SplitAlgoGetResponse(aws_file_path, iUserId, strReqDocHashed, strClientREQID, bDeveloperMode, strCustomerName):
        """
            Input:

                1) aws_file_path: str
                The S3 or AWS-hosted path to the document to process.

                2) iUserId: int
                The ID of the user requesting the split algorithm response.

                3) strReqDocHashed: str
                A hash of the request document, used for caching or idempotency.

                4) strClientREQID: str
                A unique identifier for the client’s request, used for tracing and logging.

                5) bDeveloperMode: bool
                If True, enables verbose logging and may bypass certain production checks.

                6) strCustomerName: str
                The name of the customer for whom the vendor responses are being gathered.

            Output:

                dict:
                    A dictionary mapping each vendor identifier to its processed GPT response, e.g.:
                    {
                        "VendorA": {"status": "success", "response": {...}},
                        "VendorB": {"status": "error",   "message": "..."},
                        ...
                    }

            Purpose:

                To orchestrate the end-to-end split-algorithm workflow by:
                - Retrieving the document from the specified AWS location.
                - Computing or validating the request hash.
                - Invoking the vendor-specific GPT split algorithm to generate responses.
                - Aggregating and formatting each vendor’s GPT response.
                - Returning a consolidated dictionary for downstream processing.
        """
        dictMultipleVendorGPTResponse = None
        objSplitAlgoDBRecord = None
        dictAWSCallResult = None
        
        # Load config and prompt data (this block is now centralized)
        config_data = CSplitAlgo.MSGetSplitAlgoConfig(bDeveloperMode=bDeveloperMode, strCustomerName=strCustomerName)
        
        # Check for an existing Split Algo Record
        existing_record = await CMultipleVendorRecordHelper.get_existing_record(user_id=iUserId, hash_code=strReqDocHashed)

        # If record exists, check if AWS and GPT responses are valid
        if existing_record is not None:
            aws_ok = (existing_record.ObjAWSResponse is not None and existing_record.AWSStatusCode == 200)
            gpt_ok = (existing_record.ObjGPTResponse is not None and existing_record.GPTStatusCode == 200)

            if aws_ok and gpt_ok:
                dictMultipleVendorGPTResponse = existing_record.dictGPTResponse
                await CMultipleVendorRecordHelper.update_multiple_vendor_record(
                    mvr_id=existing_record.MVR_ID, CREQ_ID=strClientREQID, LogMessage="Refetched Split Algo Record Successfully")
            
            elif aws_ok and not gpt_ok:
                dictAWSCallResult = existing_record.ObjAWSResponse
                m_strUserContentStructured = dictAWSCallResult.get("strAWSUserContent")
                objResponse = await CSplitAlgo.MSCallAPI(config_data["strSystemPrompt"], m_strUserContentStructured, config_data)
                
                # Update record after successful GPT API call
                await CMultipleVendorRecordHelper.update_multiple_vendor_record(
                    mvr_id=existing_record.MVR_ID,
                    CREQ_ID=strClientREQID,
                    GPTUserContent=m_strUserContentStructured,
                    ObjGPTResponse=objResponse,
                    dictGPTResponse=json.loads(objResponse["choices"][0]["message"]["content"]),
                    LogMessage="Successfully Completed",
                    GPTStatusCode=200
                )
                await CLogController.MSWriteLog(
                    iUserId, "DEBUG",
                    f"Multi Vendor Split Algo: dictAWSCallResult = {dictAWSCallResult}\n"
                    f"SystemPrompt = {config_data['strSystemPrompt']}\n"
                    f"StructuredUserContent = {m_strUserContentStructured}\n"
                    f"Model = {config_data['strModel']}\n"
                    f"Seed = {config_data['intSeed']}\n"
                    f"ResponseFormat = {config_data['dictResponseFormat']}\n"
                    f"Reasoning Effort = {config_data['reasoning_effort']}\n"
                    f"Max Tokens = {config_data['max_completion_tokens']} , bIsReasoningModel = True"
                )
                await CLogController.MSWriteLog(iUserId, "INFO", f"Multi Vendor Split Algo: {objResponse}")
                dictMultipleVendorGPTResponse = json.loads(objResponse["choices"][0]["message"]["content"])
            
            else:
                # If no valid record, process a new extraction
                try:
                    dictAWSCallResult = await AWS_Async.extractByAwsTextract(aws_file_path)
                    objSplitAlgoDBRecord = await CMultipleVendorRecordHelper.create_multiple_vendor_record(
                        user_id=iUserId,
                        creq_id=strClientREQID,
                        hash_code=strReqDocHashed,
                        obj_aws_response=dictAWSCallResult.get("objAWSResponse"),
                        gpt_user_content=dictAWSCallResult.get("strAWSUserContent"),
                        obj_gpt_response=None,
                        dict_gpt_response=None,
                        log_message="AWS Completed",
                        aws_status_code=200,
                        gpt_status_code=404
                    )
                    m_strUserContentStructured = dictAWSCallResult.get("strAWSUserContent")
                    objResponse = await CSplitAlgo.MSCallAPI(config_data["strSystemPrompt"], m_strUserContentStructured, config_data)
                    
                    # Update record with GPT response
                    await CMultipleVendorRecordHelper.update_multiple_vendor_record(
                        mvr_id=objSplitAlgoDBRecord.MVR_ID,
                        CREQ_ID=strClientREQID,
                        GPTUserContent=m_strUserContentStructured,
                        ObjGPTResponse=objResponse,
                        dictGPTResponse=json.loads(objResponse["choices"][0]["message"]["content"]),
                        LogMessage="Successfully Completed",
                        GPTStatusCode=200
                    )
                    await CLogController.MSWriteLog(
                        iUserId, "DEBUG",
                        f"Multi Vendor Split Algo: dictAWSCallResult = {dictAWSCallResult}\n"
                        f"SystemPrompt = {config_data['strSystemPrompt']}\n"
                        f"StructuredUserContent = {m_strUserContentStructured}\n"
                        f"Model = {config_data['strModel']}\n"
                        f"Seed = {config_data['intSeed']}\n"
                        f"ResponseFormat = {config_data['dictResponseFormat']}\n"
                        f"Reasoning Effort = {config_data['reasoning_effort']}\n"
                        f"Max Tokens = {config_data['max_completion_tokens']} , bIsReasoningModel = True"
                    )
                    await CLogController.MSWriteLog(iUserId, "INFO", f"Multi Vendor Split Algo: {objResponse}")
                except Exception as e:
                    await CLogController.MSWriteLog(iUserId, "ERROR", f"Multi Vendor Split Algo: {traceback.format_exc()}")
                    raise HTTPException(status_code=505, detail="Unable to Process Document, Please Proceed it Manually.")
                
                dictMultipleVendorGPTResponse = json.loads(objResponse["choices"][0]["message"]["content"])
        
        else:
            # If no existing record found, process a new extraction
            try:
                dictAWSCallResult = await AWS_Async.extractByAwsTextract(aws_file_path)
                objSplitAlgoDBRecord = await CMultipleVendorRecordHelper.create_multiple_vendor_record(
                    user_id=iUserId,
                    creq_id=strClientREQID,
                    hash_code=strReqDocHashed,
                    obj_aws_response=dictAWSCallResult.get("objAWSResponse"),
                    gpt_user_content=dictAWSCallResult.get("strAWSUserContent"),
                    obj_gpt_response=None,
                    dict_gpt_response=None,
                    log_message="AWS Completed",
                    aws_status_code=200,
                    gpt_status_code=404
                )
                m_strUserContentStructured = dictAWSCallResult.get("strAWSUserContent")
                objResponse = await CSplitAlgo.MSCallAPI(config_data["strSystemPrompt"], m_strUserContentStructured, config_data)
                
                # Update record with GPT response
                await CMultipleVendorRecordHelper.update_multiple_vendor_record(
                    mvr_id=objSplitAlgoDBRecord.MVR_ID,
                    CREQ_ID=strClientREQID,
                    GPTUserContent=m_strUserContentStructured,
                    ObjGPTResponse=objResponse,
                    dictGPTResponse=json.loads(objResponse["choices"][0]["message"]["content"]),
                    LogMessage="Successfully Completed",
                    GPTStatusCode=200
                )
                await CLogController.MSWriteLog(
                    iUserId, "DEBUG",
                    f"Multi Vendor Split Algo: dictAWSCallResult = {dictAWSCallResult}\n"
                    f"SystemPrompt = {config_data['strSystemPrompt']}\n"
                    f"StructuredUserContent = {m_strUserContentStructured}\n"
                    f"Model = {config_data['strModel']}\n"
                    f"Seed = {config_data['intSeed']}\n"
                    f"ResponseFormat = {config_data['dictResponseFormat']}\n"
                    f"Reasoning Effort = {config_data['reasoning_effort']}\n"
                    f"Max Tokens = {config_data['max_completion_tokens']} , bIsReasoningModel = True"
                )
                await CLogController.MSWriteLog(iUserId, "INFO", f"Multi Vendor Split Algo: {objResponse}")
            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "ERROR", f"Multi Vendor Split Algo: {traceback.format_exc()}")
                raise HTTPException(status_code=505, detail="Unable to Process Document, Please Proceed it Manually.")
            
            dictMultipleVendorGPTResponse = json.loads(objResponse["choices"][0]["message"]["content"])
        
        return dictMultipleVendorGPTResponse

    @staticmethod
    def MSGetSplitAlgoConfig(strCustomerName, bDeveloperMode = False ):  
        """
        Input:

            1) strCustomerName: str
               The name of the customer for whom the split-algorithm configuration is requested.

            2) bDeveloperMode: bool, optional (default False)
               If True, loads developer-specific configuration overrides.

        Output:

            dict:
                A dictionary containing key parameters for the split algorithm:
                - "strModel": str,              # Model name to use
                - "intSeed": int,               # Seed for deterministic outputs
                - "reasoning_effort": str,      # Level of reasoning detail
                - "max_completion_tokens": int, # Maximum tokens for generation
                - "dictResponseFormat": dict,   # Schema for response formatting
                - "strSystemPrompt": str        # System prompt template

        Purpose:

            To read and assemble the split-algorithm configuration by:
            - Loading vendor/customer-specific settings from a configuration source.
            - Applying development-mode overrides if `bDeveloperMode` is True.
            - Returning a standardized config dictionary for use in GPT calls.

        """
        config_path = Constants.GPTConfigPath
        dict_config = CJSONFileReader.read_json_file(config_path)
        m_vendor_config = dict_config.get("Multiplevendorconfig", {})
        
        # Read additional settings (response format, system prompt)
        response_format_path = m_vendor_config.get("ResponseFormat_path", "")
        systemprompt_file_path = m_vendor_config.get("Systemprompt_file_path", "")
        
        response_format = CJSONFileReader.read_json_file(response_format_path)
        if bDeveloperMode:
            m_strResponseFormat = response_format["AVDEVELOPER"]
        else:
            m_strResponseFormat = response_format[strCustomerName]

        with open(systemprompt_file_path, 'r') as f:
            m_strSystemPrompt = f.read()

        return {
            "strModel": m_vendor_config.get("strModel", ""),
            "intSeed": m_vendor_config.get("intSeed", 0),
            "reasoning_effort": m_vendor_config.get("reasoning_effort", ""),
            "max_completion_tokens": m_vendor_config.get("max_completion_tokens", 0),
            "dictResponseFormat": m_strResponseFormat,
            "strSystemPrompt": m_strSystemPrompt
        }

    @staticmethod
    async def MSCallAPI( system_prompt, user_content, config):
        """
            Input:

                1) system_prompt: str
                The system-level instruction or context to prime the ChatGPT model.

                2) user_content: str
                The user’s input content or query to be sent to the model.

                3) config: dict
                A configuration dictionary containing:
                - "strModel": str,            # The model name to use (e.g., "gpt-4").
                - "intSeed": int,             # A seed for deterministic outputs.
                - "dictResponseFormat": dict, # Schema describing the desired response format.
                - "reasoning_effort": str,    # Level of reasoning detail (e.g., "high", "medium").
                - "max_completion_tokens": int # Maximum tokens to generate in the response.

            Output:

                dict:
                    The parsed response from the ChatGPT API, structured according to `dictResponseFormat`.

            Purpose:

                To abstract and standardize calls to the ChatGPT API by:
                - Accepting system and user prompts along with runtime configuration.
                - Delegating the call to `CGPTAPIResponse.MSCallGPTAPI` with the correct parameters.
                - Ensuring the response is returned in a consistent, schema-validated dictionary.
        """
        return await CGPTAPIResponse.MSCallGPTAPI(
            strSystemContent=system_prompt,
            strUserContent=user_content,
            strModel=config["strModel"],
            intSeed=config["intSeed"],
            dictResponseFormat=config["dictResponseFormat"],
            reasoning_effort=config["reasoning_effort"],
            max_completion_tokens=config["max_completion_tokens"],
            bIsReasoningModel=True
        )

