{"2": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "bankcustomerName": "Parag Traders", "customerName": "Parag Traders", "dataDirectory": "H:\\AI Data\\DailyData\\ParagTraders", "userId": 2, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "company_name": "ParagTraders", "bank_name": "boi_1", "bVerifyBackup": false, "bUseGeneralizeGPTSchema": true, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "305A3A4B0C6A", "TallyUserName": "KANUNGO", "UserName": "<PERSON><PERSON><PERSON>"}, {"UUID": "704D7B70FB04", "TallyUserName": "KANUNGO", "UserName": "<PERSON><PERSON>"}, {"UUID": "PLEASE  GET", "TallyUserName": "KANUNGO", "UserName": "RAHUL"}], "ExtractionAPIVendorWiseConfig": {"Hansgrohe": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 163284}, "Simpolo": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Kohler": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "8": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "AV Dev", "dataDirectory": "H:\\AI Data\\DailyData\\DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bUseGeneralizeGPTSchema": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": true, "UUID_DETAILS": [{"UUID": "08BFB81AE174", "TallyUserName": "HENIL", "UserName": "Developer"}], "company_name": "airen_construction", "bank_name": "boi_1", "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "5": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf", "xlsx"], "customerName": "ICD", "dataDirectory": "H:\\AI Data\\DailyData\\ICD", "userId": 5, "backupDeviceName": "INDIA", "company_name": "ICD", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "ExtractionAPIVendorWiseConfig": {"Pegasus": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "container corporation of india ltd.": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "7": {"senderList": [], "attachmentsTypes": ["pdf"], "bankcustomerName": "Gwalia", "customerName": "Gwalia", "dataDirectory": "H:\\AI Data\\DailyData\\Gwalia", "userId": 7, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bUseGeneralizeGPTSchema": false, "company_name": "gwalia", "bank_name": "BOB", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "9": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "Prem Textiles", "dataDirectory": "H:\\AI Data\\DailyData\\DeveloperTeam", "bUseGeneralizeGPTSchema": false, "userId": 9, "backupDeviceName": "INDIA", "company_name": "Prem Textiles", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "10": {"senderList": [], "attachmentsTypes": ["xls", "pdf", "xlsx"], "dataDirectory": "H:\\AI Data\\DailyData\\VedanshSchool", "userId": 10, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bankcustomerName": "NO NEED", "customerName": "Vedansh School", "company_name": "test_vis", "bank_name": "NO NEED", "bank_details": {"account_details": {"**************": "HDFC_2732", "**************": "HDFC_3648"}}, "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": true, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "BatchWiseProcessConfig": {"pages_per_batch": 27, "max_batch_connection": 5, "max_worker_per_client": 6, "_comment_": "NOTE: Worker Change as per System Configuration : Doc Extration Service, THis Batch wise page would be use only when runtime decision exception occur"}, "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "4": {"senderList": [], "attachmentsTypes": ["pdf", "xlsx"], "dataDirectory": "H:\\AI Data\\DailyData\\DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": [], "lsEmailCC": ["<EMAIL>"], "bank_details": {"account_details": {"**************": "HDFC_2732", "**************": "HDFC_3648"}}, "bUseGeneralizeGPTSchema": false, "bankcustomerName": "ICICI", "customerName": "<PERSON><PERSON><PERSON><PERSON>frabuild", "company_name": "test_abhinavInfra", "bank_name": "ICICI", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bSmartVendorDetectAlgo": false, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "ExtractionAPIVendorWiseConfig": {"Hansgrohe": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 163284}, "Simpolo": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Pegasus": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "container corporation of india ltd.": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Kohler": {"strExtractAPIName": "GrokReasoning", "bIsGrokAPI": true, "bIsReasoningModel": true, "reasoning_effort": "low", "max_tokens": 27000}, "Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "11": {"senderList": [], "attachmentsTypes": ["xls", "pdf", "xlsx"], "dataDirectory": "H:\\AI Data\\DailyData\\AbhinavInfrabuild", "userId": 11, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bankcustomerName": "ICICI", "customerName": "<PERSON><PERSON><PERSON><PERSON>frabuild", "company_name": "test_abhinavInfra", "bank_name": "ICICI", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bDownloadERPMappingFile": false, "bUseGeneralizeGPTSchema": true, "bSmartVendorDetectAlgo": false, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}, "12": {"senderList": [], "attachmentsTypes": ["pdf"], "bankcustomerName": "", "customerName": "Dhanuka Traders", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/DhanukaTraders", "userId": 12, "backupDeviceName": "", "lsEmailReceivers": [], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": true, "bSmartVendorDetectAlgo": true, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "company_details": {"company_tally_name": "Dhanuka Traders - (from 1-Apr-22) - (from 1-Apr-23) - (from 1-Apr-24)", "company_address": ["57, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>"], "company_gstin": "23ABPPD6898K1Z0", "company_state": "Madhya Pradesh", "gst_registration_type": "Unregistered/Consumer", "cmp_gst_registration_type": "Regular"}, "ExtractionAPIVendorWiseConfig": {"Unknown": {"strExtractAPIName": "GPT4o", "bIsGrokAPI": false, "bIsReasoningModel": false, "max_tokens": 16384}}}}