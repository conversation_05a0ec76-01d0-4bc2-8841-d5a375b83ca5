import { useState, useEffect  } from "react";
import axios from 'axios'
import toast, { Toaster } from 'react-hot-toast';
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import CreatableSelect from 'react-select/creatable';
import { Tooltip } from "@material-tailwind/react";

export default function DuplicateVendorsModal({ isOpen, onClose, modelFamilyList, selectedModelID, selectedModel, selectedModelFamily}) {
    
    const [vendorName, setVendorName] = useState('');
    const [vendorFamilyName, setVendorFamilyName] = useState('');
    const [vendorDesc, setVendorDesc] = useState('');
    const [vendorFamilyOptions, setVendorFamilyOptions] = useState([]);
    const [inputValue, setInputValue] = useState('');
    const [showOptions, setShowOptions] = useState(false);

    const navigate = useNavigate();

    const filteredOptions = vendorFamilyOptions.filter(option => 
        option.label.toLowerCase().includes(inputValue.toLowerCase())
    );
    const existingOption = vendorFamilyOptions.find(option => 
        option.label.toLowerCase() === inputValue.toLowerCase()
    );

    useEffect(() => {
        // fetchVendorFamilies();
        const families = modelFamilyList.map(family => ({ value: family, label: family }));
        setVendorFamilyOptions(families);
        // setVendorFamilyOptions([{value:"families1", label:"families1"}, {value:"families2", label:"families2"}, {value:"families3", label:"families3"}, {value:"families4", label:"families4"}]);
    }, [modelFamilyList]);

    const handleOnClose = () => {
        setInputValue('');
        setVendorName('');
        setVendorFamilyName('');

        onClose();
    };

    const handleVendorNameChange = (e) => {
        // Only allow alphabetic characters and spaces
        let input = e.target.value.replace(/[^a-zA-Z\s0-9]/g, ''); // Remove non-alphabetic characters
        // Limit input to a maximum of 50 characters
        let truncatedInput = input.slice(0, 50);
        // Convert to Title Case (Upper Camel Case)
        // let titleCasedInput = truncatedInput.replace(/\b\w/g, (char) => char.toUpperCase());

        // titleCasedInput = titleCasedInput.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());

        setVendorName(truncatedInput);
    };

    const handleVendorFamilyNameChange = (e) => {
        let input = e.target.value.replace(/[^a-zA-Z\s]/g, '');
        let truncatedInput = input.slice(0, 50);
        // let titleCasedInput = truncatedInput.replace(/\b\w/g, (char) => char.toUpperCase());

        // titleCasedInput = titleCasedInput.replace(/\w\S*/g, (txt) => txt.charAt(0).toUpperCase() + txt.substr(1).toLowerCase());
        setVendorFamilyName(truncatedInput);
        setInputValue(truncatedInput);
        setShowOptions(true);
    };

    const handleOptionClick = (option) => {
        
        setVendorFamilyName(option.label);
        setInputValue(option.label);
        setShowOptions(false);
    };

    const handleSubmit = async () => {

        let updatedVendorFamilyName = vendorFamilyName;

        const forbiddenModels = ["demo finance", "demo hr", "demo medical insurance"];

        if (forbiddenModels.includes(updatedVendorFamilyName?.trim().toLowerCase())) {
            // Display a toast message indicating that deletion is not allowed
            toast.error(`The Model Family '${updatedVendorFamilyName?.trim()}' is reserved for built-in demo models. Please choose a different name.`);
            return;
        }

        if (!updatedVendorFamilyName?.trim()) {
            updatedVendorFamilyName = "Custom";
            setVendorFamilyName(updatedVendorFamilyName);
        }

        // Check if the vendorName is empty or equals "Model"
        if (!vendorName.trim() || vendorName.trim().toLowerCase() === "model") {
            toast.error("Model Name cannot be Empty or the same as 'Model'");
            return; // Exit the function early if validation fails
        }

        const apiUrl = `${import.meta.env.VITE_SERVER}/Model/Duplicate?iModelId=${selectedModelID}&strNewModelName=${encodeURIComponent(vendorName.trim())}&strFamilyName=${updatedVendorFamilyName.trim()}&strModelDesc=${encodeURIComponent(vendorDesc.trim())}`;

        try {
            const response = await axios.post(apiUrl, null, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
            
            if (response.status === 200) {
                
                toast.success("Model Successfully Duplicated.");
                navigate('/AddModelPage', { state: { iModelId: response.data.ModelDetails.Model.Id, vName: vendorName.trim(), vFamilyName:updatedVendorFamilyName.trim() } })
                
            }
            else if(response.status === 204)
            {
                toast.error(`The selected model for duplication is currently empty. Please add some fields to the model before proceeding.`);
            }
            else{
                // Handle non-200 status codes
                toast.error(`Failed to Duplicate Model: ${response.data.message}`);

                setTimeout(() => {
                    handleOnClose(); // Close modal after successful submission
                    window.location.href = "/ModelListPage"; // Redirect after showing the toast
                }, 3000); // Adjust the delay as needed to allow the toast to be visible
            }
            // onClose(); // Close modal after successful submission
            // window.location.href = "/ModelListPage"
        } catch (error) {
            if(error?.response?.status === 409)
            {
                toast.error(error?.response?.data?.detail)
            }
            else if(error?.response?.status === 204)
                {
                    toast.error(error?.response?.data?.detail)
                }
            else
            {
                toast.error("Failed to duplicate Model, Please try again later.")
            }
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center bg-black bg-opacity-60 backdrop-blur-xs transition-opacity">
            <Toaster position="top-center"></Toaster>
            <div className="relative mx-auto w-full max-w-md rounded-xl bg-[#ffff] p-6 shadow-md">
                <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between">
                    <h4 className="text-2xl font-semibold text-gray-900">
                        Duplicate Model 
                        <Tooltip content={`Model-${selectedModel}  Family-${selectedModelFamily}`}>
                            <div>
                                <h4 className="text-lg font-semibold text-gray-900" style={{ overflow: 'hidden', textOverflow: 'ellipsis', whiteSpace: 'nowrap', maxWidth: '320px' }}>
                                    {selectedModel}-{selectedModelFamily}
                                </h4>                            
                            </div>
                        </Tooltip>
                    </h4>
                                                {/* <h4 className="text-2xl font-semibold text-gray-900">Duplicate Model</h4> */}

                        <button
                            onClick={() => handleOnClose()}
                            className="rounded-xl bg-[#003654] hover:bg-[#002744] text-white py-1.5 px-4 transition-color font-bold focus:outline-none focus:ring focus:ring-blue-500"
                        >
                            Close
                        </button>
                    </div>
                    <div className="relative w-full">
                        <span
                            className="w-full text-lg rounded-md border border-gray-300 px-3 py-2 text-[#003654] focus:border-blue-500 focus:outline-none"
                            > 
                            Model Family
                        </span>
                        
                        <div className=" mb-10">
                            <input
                                type="text"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                placeholder="Model Family Name-Default(Custom)"
                                value={inputValue}
                                onChange={handleVendorFamilyNameChange}
                                onFocus={() => setShowOptions(true)}
                                onBlur={() => setTimeout(() => setShowOptions(false), 200)}
                            />
                            {showOptions && (
                                <div className="absolute z-10 w-full bg-white border border-gray-300 mt-1 rounded-md shadow-lg max-h-40 overflow-y-auto">
                                    {filteredOptions.map(option => (
                                        <div
                                            key={option.value}
                                            className="px-3 py-2 cursor-pointer hover:bg-gray-200"
                                            onClick={() => handleOptionClick(option)}
                                        >
                                            {option.label}
                                        </div>
                                    ))}
                                    {!existingOption && inputValue && (
                                        <div
                                            className="px-3 py-2 cursor-pointer hover:bg-gray-200"
                                            onClick={() => handleOptionClick({ value: inputValue, label: inputValue })}
                                        >
                                            Add "{inputValue}"
                                        </div>
                                    )}
                                </div>
                            )}
                        </div>
                        <div className="mb-10">
                            <span
                                className="w-full text-lg rounded-md border border-gray-300 px-3 py-2 text-[#003654] focus:border-blue-500 focus:outline-none"
                                > 
                                Model Name   <span className="text-red-500">*</span>
                            </span>
                            <input
                                type="text"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                placeholder="Model Name"
                                value={vendorName}
                                onChange={handleVendorNameChange}
                                maxLength={50} // Enforce a maximum input size of 50 characters
                            />
                        </div>

                        <div>
                            <span
                                className="w-full text-lg rounded-md border border-gray-300 px-3 py-2 text-[#003654] focus:border-blue-500 focus:outline-none"
                                > 
                                Model Description
                            </span>
                            <input
                                type="text"
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                placeholder="Model Description"
                                value={vendorDesc}
                                onChange={(event) => setVendorDesc(event.target.value)} // Capture input value
                                onKeyDown={(event) => {
                                    if (event.key === 'Enter') {
                                        handleSubmit();
                                    }
                                }}
                                maxLength={100} // Enforce a maximum input size of 100 characters
                            />
                        </div>
                    </div>
                    <div className="flex justify-end mt-4">
                        <button
                            onClick={handleSubmit}
                            className={`rounded-xl bg-[#003654] hover:bg-[#002744] font-bold text-white py-2 px-3 w-full transition-color focus:outline-none focus:ring focus:ring-blue-500 `}
                        
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

DuplicateVendorsModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
};