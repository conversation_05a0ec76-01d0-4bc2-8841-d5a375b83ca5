# Loading the .env file
from copy import deepcopy
import sys
import os

sys.path.append(r".")
from src.Controllers.user_Logs_Controller import C<PERSON>serLogController
from src.Controllers.AVRequestDetailController import CAVRequestDetail

from io import BytesIO
import math
from sqlalchemy.exc import SQLAlchemyError
from typing import Optional
from datetime import datetime
from sqlalchemy import select, or_, desc, asc, update
import asyncio
from sqlalchemy.future import select
from sqlalchemy import func
from fastapi.responses import StreamingResponse
from fastapi import HTTPException, Path, Body
# Import your SQLAlchemy UploadedDoc ModelTable
from src.Models.models import UploadedDoc, StatusEnum, ModelTable, DocExtractedData, Prompt,User, TallyDocRecords
from src.Schemas.Doc_Schema import GetAllDocFilterQuery, AdditionalDocDetails
from config.constants import Constants
from sqlalchemy.future import select
from config.db_config import AsyncSessionLocal
from src.Controllers.Logs_Controller import CLog<PERSON>ontroller
from src.utilities.S3BucketHelper import CAWSS3Storage
from src.utilities.DBHelper import CUserTable
from src.utilities.helperFunc import GetFiletypeFromFileName, ReadExcelToDict
import traceback
import base64
import pytz
from src.utilities.date_helper import CDateHelper
from src.utilities.helperFunc import  Hashing, DateHelper, CStringFormat
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence

STATUS_MAPPING = {
    "approved": "Approved",
    "error": "Error",
    "notprocess": "Not Processed",
    "onhold": "On Hold",
    "tobeapproved": "To Be Approved",
    "processing": "Processing"
}

class CDocumentData:

    @staticmethod
    async def MSIsDuplicateFile(strCheckSum: str, iUserID: int, strVoucherType=None) -> bool:
        """
            Check if the uploaded file is duplicated or not
        """
        async with AsyncSessionLocal() as db:
            try:
                # Step 1: Query the UploadedDoc table to check for matching checksum and user ID
                result = await db.execute(
                    select(UploadedDoc)
                    .filter(
                        UploadedDoc.CheckSum == strCheckSum,  # Ensure exact match
                        UploadedDoc.CheckSum != "",  # Ensure it's not an empty string
                        UploadedDoc.UserId == iUserID  # Ensure correct user filtering
                    )
                )
                uploaded_docs = result.scalars().all()

                if not uploaded_docs:
                    return {
                        "bIsDuplicate": False,
                        "MatchedRow": {}
                    }
                else:
                    # If a matching uploaded document is found
                    for uploaded_doc in uploaded_docs:
                        # uploaded_doc is a tuple, so the first element is the UploadedDoc instance
                        uploaded_doc = uploaded_doc  # Unpack to get the UploadedDoc object
                        # Fetch the associated TallyDocRecords using the DocId
                        # tally_result = await db.execute(
                        #     select(TallyDocRecords)
                        #     .filter(TallyDocRecords.DocID == uploaded_doc.DocId)  # Use DocId to fetch the matching TallyDocRecords
                        # )
                        # tally_record = tally_result.scalar_one_or_none()

                        dictDocData = await CDocumentData.MSGetDocById(user_id=iUserID, docId=uploaded_doc.DocId,strVoucherType = strVoucherType)
                        strModelName = dictDocData.get("ModelName")
                        # If TallyDocRecord is found, process the data
                        # if tally_record:
                        msg = f"A duplicate document was detected | Msg: {uploaded_doc.DocErrorMsg if uploaded_doc.DocErrorMsg not in [None, ''] else 'N/A'}"
                        # else:
                            # msg = f"A duplicate document was detected | Msg: {uploaded_doc.DocErrorMsg if uploaded_doc.DocErrorMsg not in [None, ''] else 'N/A'}"
                        
                        matched_row = {
                            "APIStatusCode": 200,
                            "detail": "Existing row found",
                            "DocId": uploaded_doc.DocId,
                            "UserId": uploaded_doc.UserId,
                            "ModelId": uploaded_doc.ModelId,
                            "ModelName": strModelName,
                            "DocName": uploaded_doc.DocName,
                            "PageCount": uploaded_doc.PageCount,
                            "file_type": uploaded_doc.file_type,
                            "is_scanned_document": uploaded_doc.is_scanned_document,
                            "UsedPaidModel": uploaded_doc.UsedPaidModel,
                            "DocDebugMsg": uploaded_doc.DocDebugMsg,
                            "DocErrorMsg": msg,
                            "DocExtractionAPIStatusCode": uploaded_doc.DocExtractionAPIStatusCode,
                            "UploadedDateTime": uploaded_doc.UploadedDateTime,
                            "ModifiedDateTime": uploaded_doc.ModifiedDateTime,
                            "Status": uploaded_doc.Status,
                            "TallyStatus": uploaded_doc.TallyStatus,
                            "DocS3ObjectKey": uploaded_doc.DocS3ObjectKey,
                            "Comment": uploaded_doc.Comment,
                            "CommentByUserId": uploaded_doc.CommentByUserId,
                            "ApprovedByUserId": uploaded_doc.ApprovedByUserId,
                            "DocumentRawTxt": uploaded_doc.DocumentRawTxt,
                            "DocumentRawTxtObject": uploaded_doc.DocumentRawTxtObject,
                            "DocumentRawTxtExtractBy": uploaded_doc.DocumentRawTxtExtractBy,
                            "isDeleted": uploaded_doc.isDeleted,
                            "GPTResponseForModel": uploaded_doc.GPTResponseForModel,
                            "AdditionalDocDetails": uploaded_doc.AdditionalDocDetails,
                            "CheckSum": uploaded_doc.CheckSum,
                            "DocRetryCount": uploaded_doc.DocRetryCount if uploaded_doc.DocRetryCount else 0,
                            "DocRetryDetails": uploaded_doc.DocRetryDetails
                        }

                        return {
                            "bIsDuplicate": True,
                            "MatchedRow": matched_row
                        }

                    # If no TallyDocRecords found
                    return {
                        "bIsDuplicate": False,
                        "MatchedRow": {}
                    }
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", "An HTTP error occurred.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {traceback.format_exc()}")
                raise e
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", "An unexpected error occurred.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="An unexpected error occurred.")
            
            
            
    @staticmethod
    async def upload_pdf_to_db(user_id: int, strFamilyName:str, strModelName: str, DocS3ObjectKey: str, bUsePaidModel:bool, DocDebugMsg: str = "", DocErrorMsg: str = "", Comment:str="", file_data: bytes = None, file_name: str = "", file_type: str = "", is_scanned_doc: int = 0, iPageCount:int = 0, strDocStatus : str = "NotProcess",DocExtractionAPIStatusCode:int =404, dictGPTResponseForModelExtraction={}, objAdditionalDocDetails = None,DocumentRawTxt=None, DocumentRawTxtObject=None, DocumentRawTxtExtractBy=None, BReUseDocData=False, strClientREQID = None, strVoucherType = None, SkipDuplicateCheck=True):
        """
        Uploads a PDF document to the database.
        """
        iModelID = None
        objModelData = None
        strModelFamily = "Custom"
        start_extract_time = datetime.now()
        # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
        print(f"Start upload_pdf_to_db Document Process (DocID: {file_name}) - {start_extract_time}")
        async with AsyncSessionLocal() as db:
            try:
                # strModelName - Unknown
                if strModelName == "null":
                    strModelName = "Unknown"

                dfICDPegasusAdditionalInfo = None
                if not file_data or not file_name or not file_type:
                    await CLogController.MSWriteLog(user_id, "Error", "Invalid File Type, Please Provide a PDF, Word, or Text File.")
                    raise HTTPException(
                        status_code=400, detail="Invalid File Type, Please Provide a PDF, Word, or Text File.")

                strCheckSum = Hashing.calculate_checksum(file_data)
                if not SkipDuplicateCheck:
                    dictResult = await CDocumentData.MSIsDuplicateFile(strCheckSum,user_id)
                    #check for uploaded file is duplicated or not
                    if dictResult['bIsDuplicate'] and BReUseDocData == False:
                        dictDuplicateFound = dictResult['MatchedRow']
                        dictDuplicateFound["APIStatusCode"] = 409 # conflict raise
                        # dictDuplicateFound["DocErrorMsg"] = "A duplicate entry was detected."
                        return dictDuplicateFound
                        # raise HTTPException(status_code=409, detail="Duplicate Record Found")
                    
                    if dictResult['bIsDuplicate'] and BReUseDocData == True:
                        # Check additional Mandatory Document given or not
                        dfICDPegasusAdditionalInfo = None
                        if objAdditionalDocDetails is not None: #and dictResult['MatchedRow']['AdditionalDocDetails'] is None:
                            # NOTE: All to store additional information for this list of vendors
                            dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
                            # Flatten the list of vendors from all customers into a single list
                            lsVendorNames = [vendor for vendors in dictAvailableCompany["VendorName"] for vendor in vendors]
                            if dictResult['MatchedRow']["ModelName"].lower() in lsVendorNames:
                                # Process the Excel file and convert it to a dictionary
                                dfICDPegasusAdditionalInfo = ReadExcelToDict(objAdditionalDocDetails)

                                # Update the AdditionalDocDetails column in the UploadedDoc table
                                await CDocumentData.MSUpdateUploadedDocRecord(
                                    iUserId=user_id,
                                    iDocId=dictResult['MatchedRow']["DocId"],
                                    AdditionalDocDetails=dfICDPegasusAdditionalInfo
                                )

                                # Update the local copy of the record to reflect the changes
                                dictResult['MatchedRow']['AdditionalDocDetails'] = dfICDPegasusAdditionalInfo

                                await CLogController.MSWriteLog(user_id, "Info", f"Updated AdditionalDocDetails for document ID: {dictResult['MatchedRow']['DocId']}")
                        
                        dictResult['MatchedRow']["filename"] = file_name
                        if dictResult['MatchedRow']["DocRetryDetails"] is not None:
                            lsPrevious = dictResult['MatchedRow'].get("DocRetryDetails").get("ClientREQID", [])
                            lsPrevious.append(strClientREQID)
                            dictDocRetryDetails = {"ClientREQID": lsPrevious}
                        else:
                            dictDocRetryDetails = {"ClientREQID": [strClientREQID]}

                        iDocRetryCount = dictResult['MatchedRow'].get("DocRetryCount", 1) + 1

                        await CDocumentData.MSUpdateUploadedDocRecord(
                            iUserId=user_id,
                            iDocId=dictResult['MatchedRow']["DocId"],
                            DocRetryCount=iDocRetryCount,
                            DocRetryDetails=dictDocRetryDetails
                        )
                        # AVRecordDetail Table : DOC_UID, IsRecordUIDPresent Update
                        await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = strClientREQID, ReqDocHashCode= strCheckSum, DOC_UID=dictResult['MatchedRow']["DocId"], IsRecordUIDPresent=True)
                        return dictResult['MatchedRow']
                
                await CLogController.MSWriteLog(user_id, "Info", f"Uploading a {file_type} File To Database Started.")

                # Fetch ModelTable ID, NOTE: GENERALIZE_PWI_V3
                bUserGeneralizeGPTEnable = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = user_id, strVoucherType = strVoucherType)
                if not bUserGeneralizeGPTEnable:
                    lsObjFoundVendors = await db.execute(select(ModelTable).filter(ModelTable.Name == strModelName, ModelTable.UserID == user_id,ModelTable.FamilyName==strFamilyName))
                    objModelData = lsObjFoundVendors.scalars().first()
                    if not objModelData:
                        await CLogController.MSWriteLog(user_id, "Error", f"ModelTable Named {strModelName} not found.")
                        raise HTTPException(
                            status_code=404, detail="We couldn't find the selected model.")
                    iModelID = objModelData.Id
                    strModelFamily = objModelData.FamilyName
                    strModelName = objModelData.Name

                # NOTE: All to store additional information for this list of vendors
                dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
                # Flatten the list of vendors from all customers into a single list
                lsVendorNames = [vendor for vendors in dictAvailableCompany["VendorName"] for vendor in vendors]
                if objAdditionalDocDetails is not None and strModelName.lower() in lsVendorNames:
                    dfICDPegasusAdditionalInfo = ReadExcelToDict(objAdditionalDocDetails)
                
                new_file = UploadedDoc(
                    UserId=user_id,
                    ModelId=iModelID,
                    DocName=file_name,
                    file_type=file_type,
                    PageCount=iPageCount,
                    is_scanned_document=is_scanned_doc,
                    DocS3ObjectKey= DocS3ObjectKey,
                    Comment = Comment,
                    CommentByUserId = user_id,
                    DocExtractionAPIStatusCode=DocExtractionAPIStatusCode,
                    DocDebugMsg=DocDebugMsg,
                    DocErrorMsg=DocErrorMsg,
                    Status=strDocStatus,
                    TallyStatus='NotProcess',
                    UsedPaidModel=bUsePaidModel,
                    GPTResponseForModel=dictGPTResponseForModelExtraction,
                    AdditionalDocDetails = dfICDPegasusAdditionalInfo,
                    DocumentRawTxt = DocumentRawTxt,
                    DocumentRawTxtObject = DocumentRawTxtObject,
                    DocumentRawTxtExtractBy = DocumentRawTxtExtractBy,
                    CheckSum = strCheckSum,
                    DocVendorName = strModelName,
                    DocRetryCount = 1 ,
                    DocRetryDetails = {"ClientREQID" : [strClientREQID]}
                )

                db.add(new_file)
                await db.commit()
                await db.refresh(new_file)

                await CLogController.MSWriteLog(user_id, "Info", f"Successfully Added {file_name} File To Database.")
                await CUserLogController.MSWriteLog(user_id, "Info", f"Document uploaded sucessfully({file_name}) |  Document Model : {strModelName}.", "MyDocuments")
                # AVRecordDetail Table : DOC_UID, IsRecordUIDPresent Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=user_id, strClientREQID = strClientREQID, ReqDocHashCode= strCheckSum, DOC_UID=new_file.DocId, IsRecordUIDPresent=True)
                end_extract_time =  datetime.now()
                extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
                print(f"End upload_pdf_to_db Document Process (DocName: {file_name}) -  Time taken: {extract_time_taken:.2f} seconds.")
                return {
                    "APIStatusCode": 200,
                    "detail": "File uploaded successfully",
                    "filename": file_name,
                    "file_type": file_type,
                    "DocId": new_file.DocId,
                    "PageCount":new_file.PageCount,
                    "is_scanned_document": is_scanned_doc,
                    "DocS3ObjectKey": new_file.DocS3ObjectKey,
                    "DocDebugMsg": DocDebugMsg,
                    "Comment": Comment,
                    "DocErrorMsg": DocErrorMsg,
                    "UserId": user_id,
                    "ModelId": new_file.ModelId,
                    "ModelName": strModelName,
                    "ModelFamilyName": strModelFamily,
                    "Status": new_file.Status,
                    "ApprovedByUserId": new_file.ApprovedByUserId,
                    "DocExtractionAPIStatusCode":new_file.DocExtractionAPIStatusCode,
                    "CommentByUserId": new_file.CommentByUserId,
                    "AdditionalDocDetails":new_file.AdditionalDocDetails,
                    "DocumentRawTxtExtractBy":new_file.DocumentRawTxtExtractBy,
                    "DocumentRawTxtObject":new_file.DocumentRawTxtObject,
                    "DocumentRawTxt":new_file.DocumentRawTxt
                }
            except HTTPException as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to Add PDF To Database.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to Add PDF To Database.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail=f"Something went wrong while saving your document. Please try again shortly.")
            except Exception as e:
                await db.rollback()

                await CLogController.MSWriteLog(user_id, "Error", f"Failed to Upload {file_name if file_name else '[File Name Not Fetched]'} File To Database.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Failed to Upload {file_name if file_name else '[File Name Not Fetched]'} File To Database, Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="File upload failed. Please check your file and try again.")

    
    @staticmethod
    async def MSUpdateUploadedDocRecord(iUserId: int, iDocId: int, **kwargs):
        """
        Input:

            1) iUserId: int
            ID of the user initiating the update. Used for logging purposes.

            2) iDocId: int
            The primary key (DocId) of the UploadedDoc record to update.

            3) kwargs: dict
            Dictionary of field-value pairs to update.

        Output:

            dict: Contains success status and updated DocId.

        Purpose:

            To update specified columns of a record in the UploadedDoc table using DocId.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(UploadedDoc).filter(UploadedDoc.DocId == iDocId)
                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail=f"UploadedDoc record not found for DocId {iDocId}")

                for key, value in kwargs.items():
                    if hasattr(record, key):
                        setattr(record, key, value)

                await db.commit()
                return {"success": True, "DocId": iDocId, "message": "UploadedDoc record updated successfully"}

            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserId, "Error", f"SQL error while updating UploadedDoc: {str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", traceback.format_exc())
                raise HTTPException(status_code=500, detail="Database error occurred")

            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserId, "Error", f"Unexpected error: {str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", traceback.format_exc())
                raise HTTPException(status_code=500, detail="Unhandled exception occurred")

    @staticmethod
    async def MSUpdateDocFieldsFromGPTResponse(dictGPTAPIResopnse: dict, iUserId: int, iDocId: int, ModelNameAlias:str = "-"):
        """
        Extract invoice details and update the uploaded document record.
        """
        try:
            dictDocData = dictGPTAPIResopnse
            # NOTE: GeneralizePWIV3
            bIsGeneralizePWIV3 = True if iUserId in [11,10] else False
            
            # Extract Invoice Number
            strInvoiceNo = dictDocData.get("InvoiceNo",
                            dictDocData.get("DocumentNumber",
                            dictDocData.get("DocRefNo",
                            dictDocData.get("InvoiceNumber",
                            dictDocData.get("BillToInvoiceNo",
                            dictDocData.get("InvoiceNo.",
                            dictDocData.get("CreditNoteNo/InvoiceNo")))))))
            if isinstance(strInvoiceNo, int):
                strInvoiceNo = str(strInvoiceNo)
            strInvoiceNo = strInvoiceNo.strip() if strInvoiceNo and strInvoiceNo.strip() else "-"
            # Extract and parse Invoice Date
            strInvoceDate = dictDocData.get("InvoiceDate",
                            dictDocData.get("InvoicetoDate",
                            dictDocData.get("BillToDateOfIssue",
                            dictDocData.get("CreditDate/InvoiceDate"))))
            
            try:
                inputDate = strInvoceDate
                strInvoceDate = await DateHelper.MSUserReadableDate(inputDate)

                if strInvoceDate.lower() in "Invalid date format".lower():
                    strInvoceDate = await DateHelper.MSConvertIntToDateFromYYYYMMDD(inputDate)                # Convert MM.DD.YYYY → DD/MM/YYYY
                month, day, year = strInvoceDate.split(".")
                strInvoceDate = f"{day}/{month}/{year}"
            except Exception:
                strInvoceDate = "-"
                pass

            # Extract and format Total Amount
            strTotalAmount = dictDocData.get("GrandTotal",
                            dictDocData.get("TotalAmount",
                            dictDocData.get("TotalValue",
                            dictDocData.get("TotalAmountINR",
                            dictDocData.get("TotalInvoiceValue",
                            dictDocData.get("NetAmount",
                            dictDocData.get("Total",
                            dictDocData.get("TotalChargeable",
                            dictDocData.get("TotalInvoiceAmount",
                            dictDocData.get("TotalChargeable",
                            dictDocData.get("TotalInvoiceAmount(RoundOff)",
                            dictDocData.get("TotalInvoiceValue(InFigure)",
                            dictDocData.get("FinalSummary", {}).get("TaxableValue",
                            dictDocData.get("FinalSummary", {}).get("FinalTotalAmountINR"))))))))))))))

            # Format Total Amount to user-readable string
            strTotalAmount = CStringFormat.MSFormatINRAmount(strTotalAmount)
            
            from src.Controllers.DocumentData_controller import CDocumentData
            # Update document record
            if bIsGeneralizePWIV3:
                await CDocumentData.MSUpdateUploadedDocRecord(
                    iUserId=iUserId,
                    iDocId=iDocId,
                    DocUniqueNo=strInvoiceNo,
                    DocDate=strInvoceDate,
                    DocVendorName = dictDocData.get("SellerDetails").get("SellerName","Unknown"),
                    DocTotalAmount=strTotalAmount,
                    ModelNameAlias=ModelNameAlias
                )
                await CLogController.MSWriteLog(iUserId, "Info", f"AV REPORT COLUMNS Update: DocUniqueNo= {strInvoiceNo}, DocDate: {strInvoceDate}, DocTotalAmount: {strTotalAmount} , iDocId: {iDocId}, DocVendorName: {dictDocData.get('SellerDetails').get('SellerName','Unknown')}")
            else:
                await CDocumentData.MSUpdateUploadedDocRecord(
                    iUserId=iUserId,
                    iDocId=iDocId,
                    DocUniqueNo=strInvoiceNo,
                    DocDate=strInvoceDate,
                    DocTotalAmount=strTotalAmount,
                    ModelNameAlias=ModelNameAlias
                )
                await CLogController.MSWriteLog(iUserId, "Info", f"AV REPORT COLUMNS Update: DocUniqueNo= {strInvoiceNo}, DocDate: {strInvoceDate}, DocTotalAmount: {strTotalAmount} , iDocId: {iDocId}")
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Error Occured While Updating AV REPORT COLUMNS {str(traceback.format_exc())}")
            print(f"[Error] Failed to update document fields: {e}")
              
    @staticmethod
    async def MSDeleteDocuments(user_id: int, document_ids: list):
        """
        Marks the specified documents as deleted by setting isDeleted to True.
        """
        async with AsyncSessionLocal() as db:
            try:

                await CLogController.MSWriteLog(user_id, "Info", f"Marking documents as deleted: {document_ids}")

                # Update the isDeleted column for the specified document IDs
                stmt = update(UploadedDoc).where(
                    UploadedDoc.DocId.in_(document_ids),
                    UploadedDoc.UserId == user_id
                ).values(isDeleted=True)

                await db.execute(stmt)
                await db.commit()

                await CLogController.MSWriteLog(user_id, "Info", f"Successfully marked documents as deleted: {document_ids}")
                
                result = await db.execute(select(UploadedDoc.DocName).where(UploadedDoc.DocId.in_(document_ids)))
                doc_names = result.scalars().all()
                
                # Log each document name
                for doc_name in doc_names:
                    await CUserLogController.MSWriteLog(user_id, "Info", f"The document '{doc_name}' was deleted successfully.", r"MyDocuments")
                return {
                    "APIStatusCode": 200,
                    "detail": "Documents marked as deleted successfully",
                    "document_ids": document_ids
                }
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to mark documents as deleted.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail=f"Failed to delete documents. Please try again later.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to mark documents as deleted.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="Failed to delete documents. Please try again later.")
                


    @staticmethod
    async def MSUpdateModel(user_id: int, docId: int, modelId: int, modelFamily: str = Constants.DefaultFamilyName):
        """
        Updates the vendor ID for a document.
        """
        try:
            await CLogController.MSWriteLog(user_id, "Info", f"Updating Vendor ID for DocId : {docId}.")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == docId, UploadedDoc.UserId == user_id))
                document = result.scalars().first()

                if document is None:
                    await CLogController.MSWriteLog(user_id, "Error", f"Document with DocId : {docId} not found.")
                    raise HTTPException(status_code=404, detail="The document you are looking for does not exist.")

                # For logging
                resultOldModelName = await db.execute(select(ModelTable.Name).where(ModelTable.Id == document.ModelId))
                strOldModelName = deepcopy(resultOldModelName.scalar())
                resultOldModelFamilyName = await db.execute(select(ModelTable.FamilyName).where(ModelTable.Id == document.ModelId))
                strOldModelFamilyName = deepcopy(resultOldModelFamilyName.scalar())

                # Update the vendor ID
                document.ModelId = modelId
                document.FamilyName = modelFamily

                # Commit the changes
                await db.commit()
                await db.refresh(document)

                await CLogController.MSWriteLog(user_id, "Info", f"Vendor ID updated successfully for DocId : {docId}.")

                # For logging
                resultNewModelName = await db.execute(select(ModelTable.Name).where(ModelTable.Id == modelId))
                strNewModelName = resultNewModelName.scalar()
                await CUserLogController.MSWriteLog(user_id, "Info", f"Document model category / model updated from '{strOldModelFamilyName}' & '{strOldModelName}' to '{document.FamilyName}' & '{strNewModelName}' For Document: '{document.DocName}'", "Upload")

                return {
                    "status": "success",
                    "message": "Vendor ID updated successfully",
                    "DocId": document.DocId,
                    "UserId": document.UserId,
                    "ModelId": document.ModelId,
                    "FamilyName" : document.FamilyName,
                    "DocName": document.DocName,
                    "UpdatedVendorId": modelId
                }

        except HTTPException as e:
            await db.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to update Vendor ID for DocId : {docId}.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await db.rollback()
            await CLogController.MSWriteLog(user_id, "Error", f"An error occurred while updating Vendor ID for DocId : {docId}.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="We ran into a problem while processing your request.")
        
        
    @staticmethod
    async def MSGetDocById(user_id: int, docId: int, isBinaryDataRequired=False, isUserMetaData = False, strVoucherType = None):
        try:
            UserData = {}
            model = None
            strModelName = "Unknown"
            strModelFamilyName = "Custom"
            strModelDescription = None
            
            await CLogController.MSWriteLog(user_id, "Info", f"Fetching of Document Started, DocId : {docId}.")

            async with AsyncSessionLocal() as db:
                # NOTE: GENERALIZE_PWI_V3
                bUserGeneralizeGPTEnable = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = user_id, strVoucherType = strVoucherType)
                if bUserGeneralizeGPTEnable:
                    result = await db.execute(
                        select(UploadedDoc).filter(UploadedDoc.DocId == docId)
                    )
                    
                    document = result.scalars().first()
                    if not document:
                        raise HTTPException(status_code=404, detail="The document you are looking for does not exist.")
                    strModelName = getattr(document, 'DocVendorName', "Unknown")
                    if strModelName is None:
                        strModelName = "Unknown"
                else:
                    # Query to get the document along with the model details
                    results = await db.execute(
                        select(UploadedDoc, ModelTable)
                        .join(ModelTable, UploadedDoc.ModelId == ModelTable.Id)
                        .filter(UploadedDoc.DocId == docId)
                    )
                    document, model = results.first()
                    strModelName = model.Name
                    strModelFamilyName = model.FamilyName
                    strModelDescription = model.Description

                if document is None:
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                uploaded_edt = CDateHelper.extract_edt_components(document.UploadedDateTime)
                modified_edt = CDateHelper.extract_edt_components(document.ModifiedDateTime)
                await CLogController.MSWriteLog(user_id, "Info", f"Document Fetched Successfully.")

                if isUserMetaData:
                    UserData = await CUserTable.MSGetUser(user_id = user_id)
                if isBinaryDataRequired:
                    if UserData.get("integration_config", {}).get("isTallyConfigured", False):
                    # Encoding DocBinaryData to Base64 for JSON serialization
                        S3Response = CAWSS3Storage.MSGetS3Object(bucket_name = os.getenv("s3_bucket_name_for_tally_docs"), object_name=document.DocS3ObjectKey)
                    else:
                        S3Response = CAWSS3Storage.MSGetS3Object(object_name=document.DocS3ObjectKey)
                        
                    retrieveS3Object, retriveS3ObjectMetaData = S3Response.get("data"), S3Response.get("metadata")
                    doc_binary_data_encoded = base64.b64encode(retrieveS3Object).decode(
                        'utf-8') if retrieveS3Object else None
                    return {
                        "DocId": document.DocId,
                        "UserId": document.UserId,
                        "DocName": document.DocName,
                        "PageCount": document.PageCount,
                        "is_scanned_document": document.is_scanned_document,
                        "DocBinaryData": doc_binary_data_encoded,
                        "file_type": document.file_type,
                        "ModelId": document.ModelId,
                        "ModelName": strModelName,
                        "ModelFamilyName": strModelFamilyName,
                        "ModelDescription": strModelDescription,
                        "DocDebugMsg": document.DocDebugMsg,
                        "DocS3ObjectKey": document.DocS3ObjectKey,
                        "Comment": document.Comment,
                        "DocErrorMsg": document.DocErrorMsg,
                        "DocExtractionAPIStatusCode": document.DocExtractionAPIStatusCode,
                        "UploadedDateTime": uploaded_edt,
                        "ModifiedDateTime": modified_edt,
                        "Status": document.Status if document.Status else None,
                        "TallyStatus": document.TallyStatus if document.TallyStatus else None,
                        "ApprovedByUserId": document.ApprovedByUserId,
                        "CommentByUserId": document.CommentByUserId,
                        "UserData":UserData,
                        "IsPaidModel": document.UsedPaidModel,  # Check if ModelSeries contains 'chatgpt'
                        "AdditionalDocDetails":document.AdditionalDocDetails,
                        "DocumentRawTxt":document.DocumentRawTxt, # Customize Raw Text to Serve as Input for the OpenAI Model Response
                        "DocumentRawTxtExtractBy":document.DocumentRawTxtExtractBy, # Raw Txt Extract Service Name such as AWS, Google Doc AI
                        "DocumentRawTxtObject":document.DocumentRawTxtObject,  # AWS Object, Other Raw Txt Extract Service
                        "CheckSum":document.CheckSum
                    }
                else:
                    return {
                        "DocId": document.DocId,
                        "UserId": document.UserId,
                        "DocName": document.DocName,                       
                        "PageCount": document.PageCount,
                        "is_scanned_document": document.is_scanned_document,
                        "file_type": document.file_type,
                        "ModelId": document.ModelId,
                        "ModelName": strModelName,
                        "ModelFamilyName": strModelFamilyName,
                        "DocDebugMsg": document.DocDebugMsg,
                        "DocS3ObjectKey": document.DocS3ObjectKey,
                        "Comment": document.Comment,
                        "DocErrorMsg": document.DocErrorMsg,
                        "DocExtractionAPIStatusCode": document.DocExtractionAPIStatusCode,
                        "UploadedDateTime": uploaded_edt,
                        "ModifiedDateTime": modified_edt,
                        "Status": document.Status if document.Status else None,
                        "TallyStatus": document.TallyStatus if document.TallyStatus else None,
                        "ApprovedByUserId": document.ApprovedByUserId,
                        "CommentByUserId": document.CommentByUserId,
                        "UserData":UserData,
                        "IsPaidModel": document.UsedPaidModel,  # Check if ModelSeries contains 'chatgpt'
                        "AdditionalDocDetails":document.AdditionalDocDetails,
                        "DocumentRawTxt":document.DocumentRawTxt, # Customize Raw Text to Serve as Input for the OpenAI Model Response
                        "DocumentRawTxtExtractBy":document.DocumentRawTxtExtractBy, # Raw Txt Extract Service Name such as AWS, Google Doc AI
                        "DocumentRawTxtObject":document.DocumentRawTxtObject,  # AWS Object, Other Raw Txt Extract Service
                        "CheckSum":document.CheckSum
                    }
        except HTTPException as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to Retrieve Document for given DocID {docId}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", f"Failed to Retrieve Document for given DocID {docId}")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Sorry, we couldn't retrieve the document. Please try again.")


    @staticmethod
    async def MSDownloadPdf(iUserID: int, iDocId: int = Path(..., description="The ID of the document to download")):
        """
        Purpose : Download a PDF document by its ID from the database.

        Inputs  :   (1) iDocId   :   The ID of the document to download.

        Output  : A StreamingResponse containing the PDF data if found, otherwise raises an HTTPException.

        Example : await CDocumentData.MSDownloadPdf(iDocId=123)
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Trying to Download PDF With Document having DocID {iDocId}")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                objDocument = result.scalars().first()

                if not objDocument:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Document having DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                if not objDocument.DocS3ObjectKey:
                    await CLogController.MSWriteLog(iUserID, "Error", f"No PDF Document having DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="No PDF data found for the requested document.")

                S3Response = CAWSS3Storage.MSGetS3Object(object_name= objDocument.DocS3ObjectKey)
                retrieveS3Object, retriveS3ObjectMetaData = S3Response.get("data"), S3Response.get("metadata")
                FileBytesIo = BytesIO(retrieveS3Object)
                strFilename = objDocument.DocName.replace(" ", "_")

                strFileType = GetFiletypeFromFileName(objDocument.DocName)
                strMediaType = [key for key, value in Constants.allowed_content_types.items(
                ) if value == strFileType.upper()]

                response = StreamingResponse(
                    FileBytesIo, media_type=strMediaType[0])
                response.headers["Content-Disposition"] = f"attachment; filename=\"{strFilename}\""

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Downloaded PDF With Document having DocID {iDocId}.")

                return response
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Downloaded PDF With Document having DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Downloaded PDF With Document having DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="An error occurred during PDF download")

    @staticmethod
    def apply_filters(query, filterQuery, hasAdminRights:bool):
        # apply user wise filter in case of Admin Rights
        if filterQuery.iUserId and hasAdminRights and filterQuery.iUserId is not None and filterQuery.iUserId != 'null':
            query = query.filter(UploadedDoc.UserId == int(filterQuery.iUserId))
        
        if filterQuery.strDocStatus:
            query = query.filter(UploadedDoc.Status == filterQuery.strDocStatus)

        # Apply date range filtering
        if filterQuery.strStartdate:
            start_date_obj = datetime.strptime(filterQuery.strStartdate, '%Y-%m-%d')
            start_date_obj = start_date_obj.replace(hour=0, minute=0, second=0, microsecond=0)  # start of the day
            if filterQuery.strEnddate:
                end_date_obj = datetime.strptime(filterQuery.strEnddate, '%Y-%m-%d')
                end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59, microsecond=999999)  # end of the day
            else:
                end_date_obj = datetime.now()
                end_date_obj = end_date_obj.replace(hour=23, minute=59, second=59, microsecond=999999)  # end of the current day

            query = query.filter(UploadedDoc.UploadedDateTime.between(start_date_obj, end_date_obj))

        # Apply search filtering if a search term is provided
        if filterQuery.strSearchInTable:
            search = f"%{filterQuery.strSearchInTable}%"
            modified_search = f"%{filterQuery.strSearchInTable.replace(' ', '')}%"
            query = query.filter(
                or_(
                    UploadedDoc.DocName.like(search),
                    ModelTable.Name.like(search),
                    UploadedDoc.Status.like(search),
                    UploadedDoc.DocName.like(modified_search),
                    ModelTable.Name.like(modified_search),
                    UploadedDoc.Status.like(modified_search)
                )
            )

        return query

    @staticmethod
    def apply_ordering(query, filterQuery, hasAdminRights:bool):
        # Dynamic ordering based on provided filters
        # Determine which order to apply based on a hierarchy of importance
        if hasattr(filterQuery, 'bUserIdAsc') and filterQuery.bUserIdAsc is not None and hasAdminRights:
            # Apply ordering based on file name and then return immediately
            if filterQuery.bUserIdAsc:
                query = query.order_by(asc(UploadedDoc.UserId))
            else:
                query = query.order_by(desc(UploadedDoc.UserId))
            return query  # Stop further ordering to ensure only this condition is applied
        
        if hasattr(filterQuery, 'bFileNameAsc') and filterQuery.bFileNameAsc is not None:
            # Apply ordering based on file name and then return immediately
            if filterQuery.bFileNameAsc:
                query = query.order_by(asc(UploadedDoc.DocName))
            else:
                query = query.order_by(desc(UploadedDoc.DocName))
            return query  # Stop further ordering to ensure only this condition is applied

        if hasattr(filterQuery, 'bUploadDateAsc') and filterQuery.bUploadDateAsc is not None:
            # Apply ordering based on upload date and then return immediately
            if filterQuery.bUploadDateAsc:
                query = query.order_by(asc(UploadedDoc.UploadedDateTime))
            else:
                query = query.order_by(desc(UploadedDoc.UploadedDateTime))
            return query  # Stop further ordering to ensure only this condition is applied
        
        if hasattr(filterQuery, 'bStatusAsc') and filterQuery.bStatusAsc is not None:
            # Apply ordering based on status and then return immediately
            if filterQuery.bStatusAsc:
                query = query.order_by(asc(UploadedDoc.Status))
            else:
                query = query.order_by(desc(UploadedDoc.Status))
            return query  # Stop further ordering to ensure only this condition is applied

        if hasattr(filterQuery, 'bDateAsc') and filterQuery.bDateAsc is not None:
            # Apply ordering based on modification date and then return immediately
            if filterQuery.bDateAsc:
                query = query.order_by(asc(UploadedDoc.ModifiedDateTime))
            else:
                query = query.order_by(desc(UploadedDoc.ModifiedDateTime))
            return query  # Stop further ordering to ensure only this condition is applied
        
        if hasattr(filterQuery, 'bDocTypeAsc') and filterQuery.bDocTypeAsc is not None:
            # Apply ordering based on modification date and then return immediately
            if filterQuery.bDocTypeAsc:
                query = query.order_by(asc(ModelTable.Name))
            else:
                query = query.order_by(desc(ModelTable.Name))
            return query  # Stop further ordering to ensure only this condition is applied

        return query  # Return the query as is if no conditions are met

    @staticmethod
    def format_response(hasAdminRights, documents, total_docs_count, page, per_page,dictUserData):
        # Formats the response to include pagination and document data
        # Calculate total pages needed
        total_pages = math.ceil(total_docs_count / per_page) if per_page else 1
        
        return {
            "Users": dictUserData if (hasAdminRights and dictUserData and dictUserData is not None)  else None,
            "pagination": {
                "total_documents": total_docs_count,
                "total_pages": total_pages,
                "current_page": page,
                "per_page": per_page,
            },
            "documents": [
                {
                    "DocId": doc[0].DocId,
                    "UserId": doc[0].UserId,
                    "DocName": doc[0].DocName,
                    "is_scanned_document": doc[0].is_scanned_document,
                    "Comment":doc[0].Comment,
                    "file_type": doc[0].file_type,
                    "ModelId": doc[0].ModelId,
                    "ModelName": doc[1],  # Access ModelTable.Name
                    "ModelFamilyName": doc[2],  #Access ModelTable.FamilyName
                    "ModelDescription": doc[3],  #Access ModelTable.FamilyName
                    "DocDebugMsg": doc[0].DocDebugMsg,
                    "DocErrorMsg": doc[0].DocErrorMsg,
                    "DocS3ObjectKey":doc[0].DocS3ObjectKey,
                    "DocExtractionAPIStatusCode": doc[0].DocExtractionAPIStatusCode,
                    "UploadedDateTime": CDateHelper.extract_edt_components(doc[0].UploadedDateTime),
                    "ModifiedDateTime": CDateHelper.extract_edt_components(doc[0].ModifiedDateTime),
                    "Status": doc[0].Status,
                    "TallyStatus": doc[0].TallyStatus,
                    "ApprovedByUserId":doc[0].ApprovedByUserId,
                    "CommentByUserId":doc[0].CommentByUserId,
                    "IsPaidModel": doc[0].UsedPaidModel,  # Check if ModelSeries contains 'chatgpt'
                }
                for doc in documents
            ],
        }

    @staticmethod
    async def MSGetAllDocument( user_id: int , hasAdminRights:bool , page: int = 1, per_page: Optional[int] = None, filterQuery: GetAllDocFilterQuery = None):
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(user_id, "Info", "Fetching All Documents.")
                query = select(UploadedDoc, ModelTable.Name, ModelTable.FamilyName, ModelTable.Description).join(ModelTable, UploadedDoc.ModelId == ModelTable.Id)
                query = query.filter(or_(UploadedDoc.isDeleted == 0, UploadedDoc.isDeleted.is_(None)))
                if user_id and not hasAdminRights:
                    query = query.filter(UploadedDoc.UserId == user_id)
                if filterQuery:
                    query = CDocumentData.apply_filters(query, filterQuery, hasAdminRights)
                    query = CDocumentData.apply_ordering(query, filterQuery, hasAdminRights)
                if per_page is not None:
                    total_docs_count = await db.scalar(select(func.count()).select_from(query.subquery()))
                    query = query.offset((page - 1) * per_page).limit(per_page)
                else:
                    total_docs_count = await db.scalar(select(func.count()).select_from(query))
                documents = await db.execute(query)
                documents = documents.all()
                await CLogController.MSWriteLog(user_id, "Info", f"len Doc: {len(documents)}, page: {page}, total pages {math.ceil(total_docs_count // per_page) if per_page else 1}")
                dictUserData = {}
                if hasAdminRights:
                    dictUserData = await CUserTable.MSGetAllUsersWithoutFilters(iUserID= int(user_id))
                return CDocumentData.format_response(hasAdminRights, documents, total_docs_count, page, per_page,dictUserData)
            except Exception as e:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to Fetch All Documents.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail=f"An error occurred while retrieving documents.")


    @staticmethod
    async def delete_document(
        iUserID: int, docId: int = Path(..., description="The ID of the document to delete")
    ):
        try:       # pending
            await CLogController.MSWriteLog(iUserID, "Info", f"deletion of document with DocID {docId} Started.")
            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == docId))
                document = result.scalars().first()
                if not document:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Failed to delete document with DocID {docId}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are trying to Delete does not exist.")

                await db.delete(document)
                await db.commit()

                return {"detail": "Document deleted successfully"}
        except HTTPException as e:
            raise e
        except Exception as e:
            await db.rollback()

            await CLogController.MSWriteLog(iUserID, "Info", f"Failed to delete document with DocID {docId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="An error occurred while deleting the document")

    @staticmethod
    async def MSUpdateDoc(iUserID, bUsedPaidModel:bool, iDocId: int = Path(..., description="The ID of the document to update")):
        """
        Purpose : Update the status of a document in the database.

        Inputs  :   (1) iDocId     : The ID of the document to update.
                    (2) bUsedPaidModel : Weather used paid model for extraction or not.

        Output  : Used to update weather paid model user for extraction or free model is used

        Example : await CDocumentData.MSUpdateDoc(iUserID = 1, iDocId=123, bUsedPaidModel=True)
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Extraction model used for document with DocID {iDocId}started.")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                objDocument = result.scalars().first()
                if not objDocument:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Document with DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                objDocument.UsedPaidModel = bUsedPaidModel
                
                try:
                    await db.commit()
                except SQLAlchemyError as e:

                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Extraction model used for Document with DocID {iDocId}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=500, detail="Failed to update document status.")

                return {"detail": "Document data updated successfully"}

        except HTTPException as e:
            raise e
        except Exception as e:

            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Extraction model used for Document with DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Oops! Something went wrong. Please try again.")


    @staticmethod
    async def MSUpdateDocumentStatus(iUserID, iDocId: int = Path(..., description="The ID of the document to update"),
                                        strDocErrorMsg=None, strDocDebugMsg=None, DocExtractionAPIStatusCode=None,
                                        eNewStatus: StatusEnum = Body(..., description="The new status to set for the document")):
        """
        Purpose : Update the status of a document in the database.

        Inputs  :   (1) iDocId     : The ID of the document to update.
                    (2) eNewStatus : The new status to set for the document.

        Output  : A confirmation message of the status update.

        Example : await CDocumentData.MSUpdateDocumentStatus(iDocId=123, eNewStatus=StatusEnum.Processed)
        """
        try:

            await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Status of document with DocID {iDocId} to {eNewStatus} started.")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                objDocument = result.scalars().first()
                if not objDocument:

                    await CLogController.MSWriteLog(iUserID, "Error", f"Document with DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                objDocument.Status = eNewStatus
                if eNewStatus is not None:
                    if isinstance(eNewStatus, StatusEnum):
                        status_str = eNewStatus.value.lower()
                    elif isinstance(eNewStatus, str):
                        status_str = eNewStatus.lower()
                    else:
                        raise ValueError("eNewStatus must be a string or StatusEnum")
                    
                    if status_str == "approved":
                        objDocument.ApprovedByUserId = iUserID
                        
                if strDocErrorMsg is not None:
                    objDocument.DocErrorMsg = strDocErrorMsg
                if strDocDebugMsg is not None:
                    objDocument.DocDebugMsg = strDocDebugMsg
                if DocExtractionAPIStatusCode is not None:
                    objDocument.DocExtractionAPIStatusCode = DocExtractionAPIStatusCode
                    
                try:
                    await db.commit()
                except SQLAlchemyError as e:

                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Updated Status of Document with DocID {iDocId} to {eNewStatus}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=500, detail="Failed to update document status.")

                resultDocumentName = await db.execute(select(UploadedDoc.DocName).filter(UploadedDoc.DocId == iDocId))
                strDocumentName = resultDocumentName.scalar()
                if status_str == 'approved' or status_str == 'tobeapproved' or status_str == 'error': 
                    await CUserLogController.MSWriteLog(iUserID, "Info", f"Status of document '{strDocumentName}' changed to '{STATUS_MAPPING.get(status_str)}'", "MyDocuments")
                return {"detail": "Document status updated successfully", "Status": status_str}

        except HTTPException as e:
            raise e
        except Exception as e:

            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Updated Status of Document with DocID {iDocId} to {eNewStatus}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Oops! Something went wrong. Please try again.")

    @staticmethod
    async def MSSetDocumentComment(iUserID: int, iDocId: int, strDocComment: str, hasAdminRights: bool):
        """
        Purpose: Set a comment for a document in the database.

        Inputs:
        - iUserID (int): The ID of the user who is setting the comment.
        - iDocId (int): The ID of the document.
        - strDocComment (str): The comment to set for the document.
        - hasAdminRights (bool): A flag indicating whether the user has admin rights.

        Output:
        - Returns a JSON response containing a message indicating that the comment was set successfully.
        The response includes the comment, the user who set the comment, and the approval status.

        Example:
        await CDocumentData.MSSetDocumentComment(iUserID=123, iDocId=456, strDocComment="This is a comment.", hasAdminRights=True)
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Setting comment for document with DocID {iDocId}.")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                objDocument = result.scalars().first()
                if not objDocument:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Document with DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(status_code=404, detail="We couldn't find the document you're trying to update. Please try again later.")
                try:
                    objDocument.Comment = strDocComment
                    objDocument.CommentByUserId = iUserID
                    # Set the ModifiedDateTime to the current time in EDT
                    objDocument.ModifiedDateTime = datetime.now(pytz.timezone('America/New_York'))
                    await db.commit()
                    
                except SQLAlchemyError as e:
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to set comment for document with DocID {iDocId}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(status_code=500, detail="We're unable to update your comment on the document right now. Please try again later.")

                # For logging
                resultDocumentName = await db.execute(select(UploadedDoc.DocName).filter(UploadedDoc.DocId == iDocId))
                strDocumentName = resultDocumentName.scalar()
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Comment added in document '{strDocumentName}', Comment : {strDocComment}", "MyDocuments")
                return {"detail": "Document comment set successfully", "comment": strDocComment, "ApprovedBy":"AccuVelocity" if hasAdminRights else "Self"}
            
        except HTTPException as e:
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to set comment for document with DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(status_code=500, detail="We encountered a problem saving your comment on the document. Please try again later.")
    
    @staticmethod
    async def MSResetDocumentData(iUserID, iDocId: int, strDocErrorMsg: str = "", strDocDebugMsg: str = "", DocExtractionAPIStatusCode: int = 0, IsDocApprovedStatus: int = 0, ApprovedByUserId: int = None):
        """
        Purpose: Resets the data of a document in the database.

        Parameters:
        iUserID (int): The ID of the user who is resetting the document.
        iDocId (int): The ID of the document to reset.
        strDocErrorMsg (str): The error message associated with the document.
        strDocDebugMsg (str): The debug message associated with the document.
        DocExtractionAPIStatusCode (int): The extraction API status code associated with the document.

        Raises:
        HTTPException: If the document could not be found or if there was an error updating the document.

        Returns:
        A JSON response containing a message indicating that the document was reset.
        """
        try:
            # Log the process initiation
            await CLogController.MSWriteLog(iUserID, "Info", f"Resetting document with DocID {iDocId}")
            async with AsyncSessionLocal() as db:
                # Execute the query to find the document based on the DocId
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                document = result.scalars().first()

                # If no document is found, log the error and raise HTTPException
                if not document:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Document with DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                # Reset the document data
                document.DocErrorMsg = strDocErrorMsg
                document.DocDebugMsg = strDocDebugMsg
                document.DocExtractionAPIStatusCode = DocExtractionAPIStatusCode
                document.Status = "Processing"
                document.ApprovedByUserId = ApprovedByUserId
                # ! Rewrite the Logic for Resetting Approved Fields
                # resultExtractedData = await db.execute(select(DocExtractedData)
                #                                         .filter(DocExtractedData.DocId == iDocId)
                #                                         .order_by(desc(DocExtractedData.ModifiedDateTime))
                #                                         .limit(1))
                
                # objExtractedDoc = resultExtractedData.scalars().first()
                
                # if objExtractedDoc:
                #     objExtractedDoc.ApprovedFields = ""
                # Commit the changes to the database
                try:
                    await db.commit()
                except SQLAlchemyError as e:
                    # Rollback the changes and log the error
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to reset document with DocID {iDocId}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=500, detail="We couldn't reset your document data.")

                # Return a confirmation message
                return {"detail": f"Document with DocID {iDocId} data reset successfully"}
        except HTTPException as e:
            # Log unexpected errors and re-raise as HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to reset document with DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            # Log unexpected errors and re-raise as HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to reset document with DocID {iDocId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="We encountered an unexpected error while resetting your document data. Please try again later.")

    @staticmethod
    async def MSGetDocMetaDataByDocId(userid: int, doc_id: int = Path(..., description="The ID of the document to retrieve metadata for"), strVoucherType = None):
        """
        Retrieves metadata for a specific document by its ID.

        Inputs:
        - userid: for logging
        - doc_id: The ID of the document for which metadata is requested.

        Outputs:
        - JSON response containing the document's ID, debug message, error message, and extraction API status code.

        Example:
        GET /documents/123/metadata
        """
        strModelName = None
        strModelFamily = None
        async with AsyncSessionLocal() as db:
            try:
                # Log the process initiation
                await CLogController.MSWriteLog(userid, "Info", f"Retrieving metadata for document with DocID {doc_id}")

                # Execute the query to find the document based on the DocId
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == doc_id))
                document = result.scalars().first()

                # If no document is found, log the error and raise HTTPException
                if not document:
                    await CLogController.MSWriteLog(userid, "Error", f"Document with DocID {doc_id} not found.")
                    await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                # GENERALIZE_PWI_V3 Execute the query to find the document based on the DocId
                bUserGeneralizeGPTEnable = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = userid,strVoucherType = strVoucherType)
                if not bUserGeneralizeGPTEnable: 
                    modelResult = await db.execute(select(ModelTable).filter(ModelTable.Id == document.ModelId))
                    model = modelResult.scalars().first()

                    # If no document is found, log the error and raise HTTPException
                    if not model:
                        await CLogController.MSWriteLog(userid, "Error", f"Model with ModelId {document.ModelId} not found.")
                        await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                        raise HTTPException(
                            status_code=404, detail="The model you selected does not exist.")
                    strModelName = model.Name
                    strModelFamily = model.FamilyName
                    
                # Return the document metadata
                return {
                    "UserId":document.UserId,
                    "DocPages":document.PageCount,
                    "document_id": document.DocId,
                    "Status":  document.Status.value if document.Status else None,
                    "error_msg": document.DocErrorMsg,
                    "doc_extraction_api_status_code": document.DocExtractionAPIStatusCode,
                    "model_id": document.ModelId,
                    "model_name": strModelName if strModelName is not None else document.DocVendorName, # Use DocVendorName Column of Uploaded_docs table
                    "model_family": strModelFamily if strModelFamily is not None else "Custom",
                    "IsPaidModel" : document.UsedPaidModel
                }
            except HTTPException as e:
                raise e
            except Exception as e:
                # Log unexpected errors and re-raise as HTTPException
                await CLogController.MSWriteLog(userid, "Error", f"Failed to retrieve metadata for document with DocID {doc_id}.")
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                    status_code=500, detail="An error occurred while retrieving document data. Please try again later.")

    @staticmethod
    async def MSUpdateDocumentTallyStatus(iUserID, iDocId: int = Path(..., description="The ID of the document to update"),
                                          eNewStatus: StatusEnum = Body(..., description="The new status to set for the document")):
        """
        Purpose : Update the status of a document in the database.

        Inputs  :   (1) iUserID    : The ID of the user who is updating the document.
                    (2) iDocId     : The ID of the document to update.
                    (3) eNewStatus : The new status to set for the document.

        Output  : A confirmation message of the status update.

        Example : await CDocumentData.MSUpdateDocumentTallyStatus(iUserID = 1, iDocId=123, eNewStatus=StatusEnum.Processed)
        """
        try:

            await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Tally Status of document with DocID {iDocId} to {eNewStatus} started.")

            async with AsyncSessionLocal() as db:
                result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == iDocId))
                objDocument = result.scalars().first()
                if not objDocument:

                    await CLogController.MSWriteLog(iUserID, "Error", f"Document with DocID {iDocId} not found.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=404, detail="The document you are looking for does not exist.")

                objDocument.TallyStatus = eNewStatus

                try:
                    await db.commit()
                except SQLAlchemyError as e:

                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Tally Status of Document with DocID {iDocId} to {eNewStatus}.")
                    await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=500, detail="An Error Occurred While Updating the Document's Tally Status.")

                return {"detail": "Document Tally status updated successfully", "new_status": eNewStatus}
        except HTTPException as e:
            raise e

        except Exception as e:

            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Tally Status of Document with DocID {iDocId} to {eNewStatus}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Oops! There was a problem processing your request.")
        
    @staticmethod
    async def filter_attrs(doc, excluded_fields=None, additional_excluded_fields=None):
        """
        Filter out internal SQLAlchemy attributes and other specified fields from model instances.
        """
        if excluded_fields is None:
            excluded_fields = {'_sa_instance_state', 'UserId', 'DocId', 'ModelId'}
        else:
            excluded_fields.update({'_sa_instance_state', 'UserId', 'DocId', 'ModelId'})

        if additional_excluded_fields:
            excluded_fields.update(additional_excluded_fields)

        return {key: value for key, value in doc.__dict__.items() if key not in excluded_fields}


    @staticmethod
    async def MSCopyDocumentToUser(source_user_id: int, target_user_id: int):
        """
        Copy documents from a source user to a target user, ensuring that all dependencies like models
        and prompts are correctly handled.
        """
        try:
            async with AsyncSessionLocal() as db:

                # Asynchronously validate user permissions and document existence
                source_user = await db.execute(select(User).filter_by(uid=source_user_id))
                source_user = source_user.scalars().first()

                target_user = await db.execute(select(User).filter_by(uid=target_user_id))
                target_user = target_user.scalars().first()

                if not (source_user and target_user):
                    return "Validation failed: One or both users not found."
                
                # Fetch all documents associated with the source user
                documents = await db.execute(select(UploadedDoc).filter_by(UserId=source_user_id))
                documents = documents.scalars().all()

                if not documents:
                    return "No documents to copy."
                
                # Retrieve all models for the target user for quick lookup, using Name and FamilyName as the key
                target_models = await db.execute(select(ModelTable).filter_by(UserID=target_user_id))
                target_models = {(model.Name, model.FamilyName): model for model in target_models.scalars().all()}

                # Fetch all prompts available to the target user
                target_prompts = await db.execute(select(Prompt).filter_by(UserID=target_user_id))
                target_prompt_series = {prompt.ModelSeries for prompt in target_prompts.scalars().all()}

                for document in documents:
                    source_model = await db.execute(select(ModelTable).filter_by(Id=document.ModelId))
                    source_model = source_model.scalars().first()

                    if not source_model:
                        continue

                    target_model = target_models.get((source_model.Name, source_model.FamilyName))
                    if not target_model:
                        continue

                    # Filter attributes and explicitly set UserId, DocId to None, and ModelId from target_model
                    filtered_doc_attrs = await CDocumentData.filter_attrs(document)
                    new_document = UploadedDoc(**filtered_doc_attrs, UserId=target_user_id, DocId=None, ModelId=target_model.Id)
                    db.add(new_document)
                    await db.commit()
                    await db.refresh(new_document)



                    extracted_data = await db.execute(select(DocExtractedData).filter_by(DocId=document.DocId))
                    extracted_data = extracted_data.scalars().all()

                    for data in extracted_data:
                        source_prompt = await db.execute(select(Prompt).filter_by(Id=data.DocExtractionPromptID))
                        source_prompt = source_prompt.scalars().first()

                        if not source_prompt or source_prompt.ModelSeries not in target_prompt_series:
                            continue  # Skip this data if the required prompt is not available to the target user

                        filtered_data_attrs = await CDocumentData.filter_attrs(data, additional_excluded_fields={'Id'})
                        new_data = DocExtractedData(**filtered_data_attrs, Id=None, DocId=new_document.DocId)
                        db.add(new_data)

                    await db.commit()

                return "All documents copied successfully."
        
        except HTTPException as e:
            raise e

        except Exception as e:

            # await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Tally Status of Document with DocID {iDocId} to {eNewStatus}.")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Oops! There was a problem processing your request.")

@staticmethod
async def CopyDocToUserAccounts(srcUserId:int, targetUserIds: list):
    for target_user_id in targetUserIds:   
        result = await CDocumentData.MSCopyDocumentToUser(srcUserId, target_user_id)
        print(result)
@staticmethod
async def CheckDuplicateRecord():
    
    Path = r"Z:\AI Data\DailyData\ParagTraders\2025_02_06\Sales_AN-13489.pdf"
    print("in CheckDuplicateRecord")
    file_name = os.path.basename(Path)
    file_type = 'application/pdf'
    file_data = None
    # Read PDF file in binary mode
    with open(Path, 'rb') as file:
        file_data = file.read()
    
    await CDocumentData.upload_pdf_to_db(
                        user_id=4,
                        strModelName="Aquant",
                        strFamilyName="Custom",
                        DocS3ObjectKey = "S3ObjName",
                        Comment = "Comment",
                        file_data=file_data,
                        file_name=file_name,
                        file_type=file_type,
                        is_scanned_doc=False,
                        iPageCount=2,
                        bUsePaidModel=False
                        
                )
# Run the main function
if __name__ == "__main__":
    # asyncio.run(CopyDocToUserAccounts(2, [4]))
    # asyncio.run(CheckDuplicateRecord())
    dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
    # Flatten the list of vendors from all customers into a single list
    lsVendorNames = [vendor for vendors in dictAvailableCompany["VendorName"] for vendor in vendors]
    print(lsVendorNames)
    # print(ReadExcelToDict(r'H:\\AI Data\\DailyData\\DeveloperTeam\\2025_02_18\\av_pegasus_additionaldetails.xlsx'))