from fastapi import FastAP<PERSON>, HTTPException, status, Request
from jose import JWTError, jwt
import os
from src.Schemas.auth_models import CheckoutSessionRequest
import base64

app = FastAPI()

# Your secret key and algorithm might differ
SECRET_KEY = os.getenv('JWT_SECRET')
ALGORITHM = "HS256"

async def get_user_token(request: Request):
    authorization: str = request.headers.get("Authorization")
    token_prefix, token = authorization.split()
    return token

async def get_current_user(request: Request):
    authorization: str = request.headers.get("Authorization")
    token_prefix, token = authorization.split()
    if token_prefix.lower() != "bearer":
        raise HTTPException(
            status_code=status.HTTP_401_UNAUTHORIZED,
            detail="Invalid authentication credentials",
            headers={"WWW-Authenticate": "Bearer"},
        )

    try:

        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        strRoleName = payload.get("role")

        if not (strRoleName == "SuperAdmin" or strRoleName == "Admin" or strRoleName == "Verifier"):
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Only Admin can access this resource",
            )
    except JWTError:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail="Could not validate credentials",
        )


def get_current_user_id(request: Request):
    authorization: str = request.headers.get("Authorization")
    if authorization is not None:
        token_prefix, token = authorization.split()
        if token_prefix.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        try:
            if ("indianinvtally/process_doc" in str(request.url).lower()) or ("tally/export" in str(request.url).lower()) or  ("tally/client_import_tally_response" in str(request.url).lower()) or  ("indianinvtally/client_request_delivered_at" in str(request.url).lower() or ("/indianinvtally/day_book_update") in str(request.url).lower()):
                SECRET_KEY = base64.urlsafe_b64encode(b'ea7634b4c4b4c23a42218d4b0c814869')
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                iUserID = payload.get("uid")
            else:
                SECRET_KEY = os.getenv('JWT_SECRET')
                payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
                iUserID = payload.get("id")
            if iUserID:
                return iUserID

            else:
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials",
                    headers={"WWW-Authenticate": "Bearer"},
                )

        except JWTError:
            raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )
    else:
        print("Token received:", authorization.split())
        raise HTTPException(
                status_code=status.HTTP_403_FORBIDDEN,
                detail="Could not validate credentials",
            )


async def admin_required(request: Request):
    return await get_current_user(request)


async def user_required(request: Request):
    """
        Purpose : Used As verification that it's a valid user
    """
    return get_current_user_id(request)

async def PlanRequest(request: Request):
    """
        Purpose: Used As verification that it's a valid user
    """
    userId = get_current_user_id(request)
    request_body = await request.json()
    price_id = request_body.get("price_id")
    mode = request_body.get("mode", None)
    quantity = request_body.get("quantity", None)
    if not price_id:
        raise HTTPException(status_code=400, detail="price_id is required")
    if mode is not None and quantity is not None:
        response = CheckoutSessionRequest(userId=userId,price_id=request_body.get("price_id"), mode=mode, quantity=quantity)
    elif mode is not None and quantity is None:
        response = CheckoutSessionRequest(userId=userId,price_id=request_body.get("price_id"), mode=mode)
    elif mode is None and quantity is not None:
        response = CheckoutSessionRequest(userId=userId,price_id=request_body.get("price_id"), quantity=quantity)
    else:
        response = CheckoutSessionRequest(userId=userId,price_id=request_body.get("price_id"))
    return response

def UserAndRoleCheck(request: Request):
    try:
        # Extract and validate the authorization header
        authorization: str = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header is missing",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token_prefix, token = authorization.split()
        if token_prefix.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract role and docId from request
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        user_id = payload.get("id")
        role = payload.get("role")
        
        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role ID is required"
            )
            
        # Prepare role data
        role_data = {}
        role_data["role_type"] = str(role)
        # Allow to read any document if role is not general
        role_data["hasAdminRights"] = True if role.lower() in ["admin","super admin","verifier"] else False

        return {"UserId":user_id,"RoleData":role_data}
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token data"
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal Server Error")