const Button = {
  // The styles all button have in common
  baseStyle: {
    color: "#fff",
    fontFamily: "Inter, 'sans-serif'",
    fontWeight: 400,
    borderRadius: "4px",
    _focus: { border: "none", outline: "none" },
  },

  sizes: {
    sm: { px: "12px", py: "8px", fontSize: "14px", lineHeight: "17px" },
    md: { px: "20px", py: "8px", fontSize: "14px", lineHeight: "16px" },
    lg: { px: "24px", py: "8px", fontSize: "14px", lineHeight: "16px" },
  },

  variants: {
    primary: {
      bgColor: "primary",
    },
    link: {
      color: "#000000",
      textDecorationLine: "none",
    },
  },

  // default values for `size` and `variant`
  defaultProps: {
    size: "md",
    variant: "primary",
  },
};

export default Button;
