import zipfile
import os
import re

class CTallyBackupHelper:

    @staticmethod
    def MSExtractZIP(zip_file_path, extract_to=None):
        """
        Extracts the contents of a zip file to a specified directory.

        Parameters:
        zip_file_path (str): The path to the zip file to be extracted.
        extract_to (str): The path where the files should be extracted. If None, extracts to the current directory.

        Returns:
        bool: True if extraction is successful, otherwise False.
        """
        try:
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                if extract_to is None:
                    extract_to = os.path.dirname(zip_file_path)
                zip_ref.extractall(extract_to)
            print(f"Extraction complete. Files extracted to {extract_to}")
            return True
        except Exception as e:
            print(f"An error occurred while extracting the zip file: {e}")
            return False


    @staticmethod
    def MSGetTextFileContent(file_path):
        """
        Reads the contents of a text file and returns it as a string.

        Parameters:
        file_path (str): The path to the text file.

        Returns:
        str: The content of the text file.
        """
        try:
            with open(file_path, 'r', encoding='utf-16') as file:
                content = file.read()
            return content
        except Exception as e:
            print(f"An error occurred while reading the file: {e}")
            return None


    @staticmethod
    def MSExtractBackupStatus(log_content):
        """
        Extracts the backup status from the log content and checks if the backup was successful.

        Parameters:
        log_content (str): The log content as a string.

        Returns:
        dict: A dictionary containing the backup success status and a message.
        """
        # Find all backup completion lines
        status_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}) \*\* Backup done for the task.*?Errors: (\d+).*?Processed files: (\d+).*?Backed up files: (\d+)', re.MULTILINE)
        matches = status_pattern.findall(log_content)
        
        if matches:
            # Get the last match
            last_match = matches[-1]
            timestamp, errors, processed_files, backed_up_files = last_match

            errors = int(errors)
            processed_files = int(processed_files)
            backed_up_files = int(backed_up_files)

            # Check backup success criteria
            if errors == 0 and processed_files > 0 and backed_up_files > 0:
                return {
                    'status': True,
                    'message': f"Backup was successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
            else:
                return {
                    'status': False,
                    'message': f"Backup was not successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
        else:
            return {
                'status': False,
                'message': "No backup status found in the log."
            }


if __name__  == "__main__":
    
    strFileContent = CTallyBackupHelper.MSGetTextFileContent(r"H:\log 2024-10-23.txt")
    print(CTallyBackupHelper.MSExtractBackupStatus(strFileContent))
