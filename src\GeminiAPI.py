from fastapi import <PERSON>TTPException
import asyncio
from os.path import join, dirname
import traceback
from dotenv import load_dotenv
import google.generativeai as genai
import os
import time
from src.utilities.PromptBuilder import MakeModelGPTPrompt
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.utilities.helperFunc import CExtractionHelper
from pathlib import Path
import json 
import re
# Load environment variables from the .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

# Configure the Google Generative AI with an API key from the environment
genai.configure(api_key=os.getenv("GEMINIAPI1"))

class CallGeminiAPI:
    """
    A class to interact with the Google Generative AI Gemini API to generate content based on input text.
    
    Attributes:
        strGeminiStableModel (str): The stable model version of the Gemini API.
        bDebug (bool): Flag to enable debug mode, which prints additional outputs for debugging.
    """
    strGeminiStableModel = "gemini-1.0-pro-001"

    def __init__(self, document_id, UserData: dict, docExtractedTxt: str, iDocPageCount: int, strGeminiConfigPath=Path(r"resource/GeminiConfig.json"), bDebug=False):
        self.UserData = UserData
        self.docExtractedTxt = docExtractedTxt
        self.iDocPageCount = iDocPageCount
        self.strGeminiConfigPath = strGeminiConfigPath
        self.bDebug = bDebug
        self.DocValidationStatus = True
        self.document_id = document_id
        # Pre-initialize error handling attributes
        self.strDocErrorMsg, self.strDocDebugMsg, self.DocErrorCode = "", "", None

    async def initialize(self):
        try:
            self.user_id = self.UserData.get('uid', None)
            if self.user_id is None:
                self.strDocErrorMsg = "Error User ID Not found"
                self.DocErrorCode = 404
                self.strDocDebugMsg = "Error User ID Not found"
                raise HTTPException(status_code=404, detail=self.strDocErrorMsg)

            if (self.UserData["free_page_limit_usage"] + self.iDocPageCount) > self.UserData["total_allowed_free_page_limit"]:
                self.strDocErrorMsg = "Your Lite Document processing limit exceeded, Please buy our plans to continue the extraction."
                self.DocErrorCode = 429
                self.strDocDebugMsg = "iDocPageCount exceeds UserData['free_page_limit_usage']"
                await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
                raise HTTPException(status_code=self.DocErrorCode, detail=self.strDocErrorMsg)
            
            self.GeminiConfigData = await MakeModelGPTPrompt.ReadConfigFile(UserID=self.user_id, StrPath=self.strGeminiConfigPath)
            await self.log("Info", "Preprocess Validation Started")

            CallGeminiAPI.strGeminiStableModel = self.GeminiConfigData.get("gemini-model", None)
            if CallGeminiAPI.strGeminiStableModel is None:
                self.strDocErrorMsg = "Service Unavailable: Unable to process this Document"
                self.DocErrorCode = 500
                self.strDocDebugMsg = "strGeminiStableModel not found in GeminiConfig"
                await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
                raise HTTPException(status_code=500, detail=self.strDocErrorMsg)

            # Calculate document extraction token count
            DocumentExtractionToken = CallGeminiAPI.getGeminiInputTokenCount(self.docExtractedTxt)
            await self.log("Info", f"Gemini Validation Completed with token count: {DocumentExtractionToken}")

        except HTTPException as e:
            self.strDocErrorMsg = "It seems there's an issue with validating your document."
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await self.log("Error", self.strDocErrorMsg)
            await self.log("Debug", self.strDocDebugMsg)
            raise e
        except Exception as e:
            self.strDocErrorMsg = "It seems there's an issue with validating your document."
            self.DocErrorCode = 500
            self.strDocDebugMsg = f"Traceback: {str(traceback.format_exc())}"
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= self.DocErrorCode)
            await self.log("Error", self.strDocErrorMsg)
            await self.log("Debug", self.strDocDebugMsg)
            raise HTTPException(status_code=500, detail=self.strDocErrorMsg)
    
    async def log(self, level: str, message: str):
        """
        Helper method to log messages with the specified severity level.

        Args:
            level (str): The severity level of the log ('Info', 'Error', 'Debug').
            message (str): The message to log.
        """
        await CLogController.MSWriteLog(self.user_id, level, message)
        
    @staticmethod
    def getGeminiInputTokenCount(input_text):
        """
        Counts the number of tokens in the input text based on the Gemini model's tokenizer.
        
        Args:
            input_text (str): The text for which to count tokens.
        
        Returns:
            int: The number of tokens in the input text.
        """
        model = genai.GenerativeModel(CallGeminiAPI.strGeminiStableModel)
        iPromptToken = model.count_tokens(str(input_text))
        return iPromptToken
    
    async def get_gemini_response(self, input_text, max_retries=3, max_output_tokens=2048, temperature=0.4, top_p=0.8, top_k=20):
        """
        Generates content based on the input text using the Gemini model.
        
        Args:
            input_text (str): The text input for content generation.
            max_retries (int): The maximum number of retry attempts in case of failures.
            max_output_tokens (int): The maximum number of output tokens to generate.
            temperature (float): The randomness of the response (lower is more deterministic).
            top_p (float): The nucleus sampling parameter focusing on top probable tokens.
            top_k (int): The number of top tokens considered at each step.
        
        Returns:
            tuple: A tuple containing the generated content, debug message, and user-friendly error message.
                   If an error occurs, the generated content will be None.
        """
        model = genai.GenerativeModel(CallGeminiAPI.strGeminiStableModel)
        attempt = 0
        self.strDocErrorMsg = ""
        self.strDocDebugMsg = ""
        while attempt < max_retries:
            try:
                response = model.generate_content(
                    input_text,
                    generation_config=genai.types.GenerationConfig(
                        max_output_tokens=max_output_tokens,
                        temperature=temperature,  # Lower for more predictable output
                        top_p=top_p,              # Focus on the most likely tokens
                        top_k=top_k               # Narrow down the choices to top 20
                    )
                )
                
                if not response or response is None or not response.text:
                    await self.log("Debug", "We couldn't process your document this time.Please try again later.")
                    raise HTTPException(status_code=500, detail=f"We couldn't process your document this time.Please try again later. {response}")
                
                try:
                    await self.log("Debug", f"Gemini Response - {response}")
                    json_objects = CallGeminiAPI.extract_json(response.text)
                    if not isinstance(json_objects, dict):
                        await self.log("Debug", f"Expected a dictionary, but got a non-dict object : {response.text}")
                        raise HTTPException(status_code=500,detail="We couldn't process your document this time.Please try again later.")
                    else:
                        for item,value in json_objects.items():
                            # Update Fields Key Dict Data value
                            if isinstance(value, dict) and str(item).lower() != 'tables':
                                updatedItem  = CExtractionHelper.MSConvertDictToListOfDict(value)
                                json_objects[item] = updatedItem
                        else:
                            return {"json_objects":json_objects, "response":response}
                except Exception as e:
                    await self.log("Debug", f"Dictionary conversion error. Traceback: {traceback.print_exc()}, response.text : {response.text}")
                    raise HTTPException(status_code=e.status_code, detail= "We couldn't process your document this time.Please try again later.")
            except HTTPException as http_exc:
                self.strDocDebugMsg = f"Attempt {attempt + 1} failed: {str(e)}"
                self.strDocErrorMsg ="We couldn't process your document this time.Please try again later."
                await self.log("Debug", str(self.strDocDebugMsg) + str(self.strDocErrorMsg))
                
                if self.bDebug:
                    print(f"Attempt {attempt+1} failed: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                attempt += 1
            except Exception as e:
                self.strDocDebugMsg = f"Attempt {attempt + 1} failed: {str(e)}"
                self.strDocErrorMsg ="We couldn't process your document this time.Please try again later."
                await self.log("Debug", str(self.strDocDebugMsg) + str(self.strDocErrorMsg))
                
                if self.bDebug:
                    print(f"Attempt {attempt+1} failed: {e}")
                await asyncio.sleep(2 ** attempt)  # Exponential backoff
                attempt += 1
        await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= 500)
        raise HTTPException(
                status_code=500, detail="We couldn't process your document this time.Please try again later.")
    @staticmethod
    def extract_json(text_response):
        try:
            cleaned_text = text_response.replace("\n", " ")
            return CallGeminiAPI.extract_dict_from_curly_braces(cleaned_text)
        except Exception as e:
            raise ValueError("Unable to decode any JSON objects from the response.")

    @staticmethod
    def extract_dict_from_curly_braces(input_string):
        # Step 1: Remove non-breaking spaces and fix double quotes
        input_string = re.sub(r'[\u00A0\u2007\u202F]', ' ', input_string)  # Replace all known non-breaking spaces with regular spaces
        input_string = re.sub(r'\\n', ' ', input_string)  # Replace newline escape characters with a space
        input_string = re.sub(r'\\t', ' ', input_string)  # Replace tab escape characters with a space
        input_string = re.sub(r'\\x[a-fA-F0-9]{2}', '', input_string)  # Remove hexadecimal escape sequences

        # Step 2: Normalize structure
        input_string = input_string.strip()  # Remove leading and trailing spaces
        input_string = re.sub(r'^```JSON\n|\n```\s*$', '', input_string)  # Remove code block delimiters
        input_string = input_string.replace('"', '')  # Removing double quotes to prevent error in dictionary conversion
        input_string = input_string.replace("'", '"')  # Ensure consistent use of double quotes
        input_string = re.sub(r'\s*:\s*', ':', input_string)  # Ensure consistent spacing around ':'
        input_string = re.sub(r',\s*', ',', input_string)  # Ensure consistent spacing after commas
        input_string = input_string.replace("None", '"Not Found"')  # Ensure consistent JSON null representation
        input_string = input_string.replace("null", '"Not Found"')  # Normalize null representation
        input_string = re.sub(r'\\\\|//', '', input_string)  # Remove occurrences of double backslashes or double forward slashes
        # Step 3: Convert to JSON
        try:
            json_obj = json.loads(input_string)  # Load as JSON object
            return json_obj
        except json.JSONDecodeError as e:
            raise e
        
    @staticmethod
    def extend_search(text, span):
        # Extend the search to try to capture nested structures
        start, end = span
        nest_count = 0
        for i in range(start, len(text)):
            if text[i] == '{':
                nest_count += 1
            elif text[i] == '}':
                nest_count -= 1
                if nest_count == 0:
                    return text[start:i+1]
        return text[start:end]
    
    async def run(self, objPromptData):
        try:
            strPrompt = objPromptData.get("Prompt",None)

            if strPrompt is None:
                await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg="Unable to process this Document, Please try again later.",strDocDebugMsg="Prompt Not Found in Prompt Object",DocExtractionAPIStatusCode=404)
                raise HTTPException(status_code=404,detail="Prompt Not Found in PromptData")
            
            await self.log("Info", f"Prompt Used: {strPrompt}  \n  Extracted Text: {self.docExtractedTxt}")
            strGeminiInputPrompt = strPrompt + str(self.docExtractedTxt) 
            objResponse = await self.get_gemini_response(str(strGeminiInputPrompt))
            await self.log("Debug", f"Prompt Used: {strPrompt}  \n  GeminiAPI Response : {str(objResponse.get('response'))}")
            
            return {"response":{"response":str(objResponse.get("response"))}, "json_objects":objResponse.get("json_objects")}
        except HTTPException as http_exc:
            raise HTTPException(
                status_code=http_exc.status_code, detail=http_exc.detail)
        except Exception as e:
            await CDocumentData.MSUpdateDocumentStatus(iUserID=self.user_id, iDocId=self.document_id, eNewStatus="Error",strDocErrorMsg=self.strDocErrorMsg,strDocDebugMsg=self.strDocDebugMsg, DocExtractionAPIStatusCode= 500)
            raise HTTPException(
                status_code=500, detail="We couldn't process your document this time.Please try again later.")