{"type": "json_schema", "json_schema": {"name": "General_<PERSON><PERSON><PERSON>", "strict": true, "schema": {"type": "object", "properties": {"InvoiceNo": {"type": "string", "description": "The unique identifier assigned to the invoice by the seller or service provider. This is typically a numerical or alphanumeric code."}, "InvoiceDate": {"type": "string", "description": "The date when the invoice was generated and issued by the seller, in the format 'YYYYMMDD'"}, "InvoiceTime": {"type": "string", "description": "The exact time when the invoice was generated, in the format 'HHMMSS'"}, "Class": {"type": "string", "description": "The class of the student is studying."}, "OnlineRegNo": {"type": "integer", "description": "The Online Registration Number mentioned on the receipt."}, "ModeOfTransaction": {"type": "string", "description": "Mode of Transaction through which trasnsaction took place."}, "SellerDetails": {"type": "object", "description": "The Details of Seller / Vendor / Service provider / Bill from", "properties": {"SellerName": {"type": "string", "description": "The full name of the seller or service provider"}, "SellerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the seller or service provider, if listed on the invoice"}, "SellerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the seller, if mentioned"}, "SellerEntityBasedOnPanOrGst": {"type": "string", "description": "Classification of Entities Based on PAN or GST Number based on fourth character", "enum": ["Association of Persons (AOP)", "Body of Individuals (BOI)", "Company", "Limited Liability Partnership (LLP)", "Firm (Partnership)", "Government", "Hindu Undivided Family (HUF)", "Artificial Juridical Person", "Local Authority", "Individual", "Trust"]}, "SellerContactNumber": {"type": "string", "description": "The contact phone number of the seller, if available."}, "SellerEmail": {"type": "string", "description": "The email address of the seller, if provided."}, "SellerAddress": {"type": "string", "description": "The full physical address of the seller, if mentioned."}, "SellerState": {"type": "string", "description": "State of seller", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "additionalProperties": false, "required": ["<PERSON><PERSON><PERSON><PERSON>", "SellerGST", "SellerPAN", "SellerEntityBasedOnPanOrGst", "SellerContactNumber", "Seller<PERSON>mail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerState"]}, "BuyersDetails": {"type": "object", "description": "The Details of Buyer / Purchaser / Service receiver / Bill to", "properties": {"BuyerName": {"type": "string", "description": "The full legal name of the buyer or service recipient as stated on the invoice."}, "BuyerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the buyer, if mentioned."}, "BuyerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the buyer, if mentioned"}, "BuyerEntityBasedOnPanOrGst": {"type": "string", "description": "Classification of Entities Based on PAN or GST Number based on fourth character", "enum": ["Association of Persons (AOP)", "Body of Individuals (BOI)", "Company", "Limited Liability Partnership (LLP)", "Firm (Partnership)", "Government", "Hindu Undivided Family (HUF)", "Artificial Juridical Person", "Local Authority", "Individual", "Trust"]}, "BuyerContactNumber": {"type": "string", "description": "The contact phone number of the buyer, if mentioned."}, "BuyerEmail": {"type": "string", "description": "The email address of the buyer listed on the invoice, if mentioned"}, "BuyerAddress": {"type": "string", "description": "The full physical address of the buyer, if mentioned"}, "BuyerState": {"type": "string", "description": "State of buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "additionalProperties": false, "required": ["BuyerName", "BuyerGST", "BuyerPAN", "BuyerEntityBasedOnPanOrGst", "BuyerContactNumber", "BuyerEmail", "BuyerAddress", "BuyerState"]}, "SubTotal": {"type": "number", "description": "The subtotal of the invoice before any taxes, discounts, or additional charges, if specified. If not mentioned, this should be set to '0'"}, "Discounts": {"type": "array", "description": "All types of discounts applied on subtotal amount, not on seperate items. Reply with applicable discount only.", "items": {"type": "object", "properties": {"DiscountName": {"type": "string", "description": "Name of discount for selected DiscountRate and DiscountAmount", "enum": ["Trade Discount", "Cash Discount", "Quantity Discount", "Seasonal Discount", "Promotional Discount", "<PERSON>yal<PERSON> Discount", "Early/Advance Payment Discount", "Special Product Discount", "Display Discount", "Employee Discount", "Festival Discount", "Digital Payment Discount", "Bundle/Combo Discount", "Clearance Discount", "Other Discount"]}, "DiscountRate": {"type": "number", "description": "Discount rate percentage, if provided"}, "DiscountAmount": {"type": "number", "description": "Total discount (without minus sign) amount for selected discount name and percentage"}}, "required": ["DiscountName", "DiscountRate", "DiscountAmount"], "additionalProperties": false}}, "Charges": {"type": "array", "description": "Any additional charges or fees or costs applied on subtotal amount. Reply with applicable charges only.", "items": {"type": "object", "properties": {"ChargeName": {"type": "string", "description": "Name of charge for selected ChargeRate and ChargeAmount", "enum": ["Service Charge", "Handling Charge", "Packaging Charge", "Freight/Transportation Charge", "Delivery/Shipping Charge", "Insurance Fee", "Installation Charge", "Min Charges", "Maintenance Charge", "Labor/Installation Charge", "Convenience Charge", "Digital Payment Surcharge", "Bank Charges", "GST (Goods and Services Tax)", "Cess/Surcharge", "Regulatory/Statutory Charges", "Demurrage Charges", "Courier Charges", "Gas Charge", "VAT", "Maintance Charge", "Excess Gas Charge", "TCS", "Late Payment Charge", "Voluntary Contribution", "Other Charges"]}, "ChargeRate": {"type": "number", "description": "Discount rate percentage, if provided"}, "ChargeAmount": {"type": "number", "description": "Total discount (without minus sign) amount for selected ChargeName and/or ChargeRate"}}, "required": ["ChargeName", "ChargeRate", "ChargeAmount"], "additionalProperties": false}}, "Taxes": {"type": "object", "description": "Must include either CGST+SGST or IGST in output.", "properties": {"ApplicableGstTaxType": {"type": "string", "description": "Fetch buyer state. then fetch seller state. then if these both are same then 'CGST & SGST' applies otherwise 'IGST'. Strickly follow this.", "enum": ["CGST+SGST", "IGST"]}, "ApplicableGstTaxTypeReason": {"type": "string", "description": "Reason for selecting ApplicableGstTaxType"}, "MainTaxes": {"anyOf": [{"type": "array", "description": "The CGST/SGST rate and amount,  if supply of goods or service is within state, i.e. intrastate transactions, CGST/SGST applies. If transaction is intrastate but wrongly IGST is mentioned in input(User prompt) then convert to CGST+SGST. Also include exempted TaxableAmount values (With TaxRate '0' and TaxAmount '0'), if there are any, by cross checking Amount-1 with Amount-2 mismatch. Where Amount-1 is SubTotal - Discounts + Charges and AMount-2 is Total CGST+SGST Taxable amount", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate. If 'TaxRate' and 'TaxAmount' is zero then consider it under 'Exempted'", "enum": ["CGST", "SGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "CGST/SGST rate percentage", "enum": [0.0, 0.125, 1.5, 2.5, 3.0, 6.0, 9.0, 14.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount occurs for selected TaxName and TaxRate, Generally called as Taxable amount"}, "TaxAmount": {"type": "number", "description": "Total CGST/SGST amount for selected TaxName and TaxRate"}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}, {"type": "object", "description": "The IGST rate and amount,  if supply of goods or service is out of state, i.e. interstate transactions or If goods or services are supplied to an SEZ unit, IGST applies. If transaction is interstate but wrongly CGST+SGST is mentioned in input(User prompt) then convert to IGST", "properties": {"IGST": {"type": "array", "description": "Give only if transaction is in different state", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate. If 'TaxRate' and 'TaxAmount' is zero then consider it under 'Exempted'", "enum": ["IGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "IGST rate percentage", "enum": [0.0, 0.25, 3.0, 5.0, 12.0, 18.0, 28.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount occurs for selected TaxName and TaxRate, Generally called as Taxable amount"}, "TaxAmount": {"type": "number", "description": "Total IGST amount for selected TaxName and TaxRate"}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}}, "additionalProperties": false, "required": ["IGST"]}]}, "OtherTaxes": {"type": "object", "description": "Taxes other than CGST, SGST, IGST", "properties": {"TaxName": {"type": "string", "description": "Name of tax"}, "TaxRate": {"type": "number", "description": "IGST rate percentage"}, "Hsn/SacCode": {"type": "number", "description": "HSN (Harmonized System of Nomenclature) / SAC (Service Accounting Code)"}, "TaxAmount": {"type": "number", "description": "Amount"}}, "required": ["TaxName", "TaxRate", "Hsn/SacCode", "TaxAmount"], "additionalProperties": false}}, "additionalProperties": false, "required": ["ApplicableGstTaxType", "ApplicableGstTaxTypeReason", "MainTaxes", "OtherTaxes"]}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the invoice total. If the adjustment is negative, a negative value should be provided. Extremely important to note that minus sign can be after RoundingOff amount like '0.3-', in this condition consider it as '-0.3'"}, "TotalAmount": {"type": "number", "description": "The total amount payable as per the invoice, including Discounts, Charges, Taxes and RoundingOff adjustments."}, "Products/GoodsNameList": {"type": "array", "description": "A list of Name of Goods mentioned in invoice.", "items": {"type": "object", "properties": {"Product/GoodsName": {"type": "string", "description": "The name of the Product/Goods."}}, "required": ["Product/GoodsName"], "additionalProperties": false}}}, "required": ["InvoiceNo", "InvoiceDate", "InvoiceTime", "OnlineRegNo", "ModeOfTransaction", "Class", "SellerDetails", "BuyersDetails", "SubTotal", "Discounts", "Charges", "Taxes", "RoundingOff", "TotalAmount", "Products/GoodsNameList"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING. ST<PERSON><PERSON><PERSON><PERSON>Y CROSS CHECK ALL PROVIDED VALUES THAT FOLLOWS THIS FORMULA : SubTotal - Discounts + Charges + Taxes (CGST/SGST/IGST/Other) + RoundingOff = TotalAmount"}}}