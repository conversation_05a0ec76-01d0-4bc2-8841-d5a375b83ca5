{"TrialOnboarding": {"Email_Subject": "Welcome to your AccuVelocity free trial, let's get started!", "Email_Body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Welcome to Your AccuVelocity Free Trial</title>\n<style>\nbody {\nfont-family: Arial, sans-serif;\nbackground-color: white;\ncolor: #003654;\nmargin: 0;\npadding: 0;\n}\n.container {\nwidth: 100%;\nmax-width: 1000px;\nmargin: 0 auto;\npadding: 20px;\nbox-sizing: border-box;\n}\n.header {\ndisplay: flex;\nalign-items: center;\npadding-bottom: 20px;\nborder-bottom: 2px solid #003654;\nbackground: #003654;\npadding-top: 20px;\n}\n.header img {\nheight: 40px;\nmargin-right: 20px;\n}\n.content {\npadding: 20px 0;\ncolor: #003654;\n}\n.footer {\npadding-top: 20px;\nborder-top: 2px solid #003654;\ntext-align: center;\n}\n.footer p {\nfont-size: 14px;\ncolor: #003654;\n}\n.contact-info {\ncolor: #003654;\n}\n@media (max-width: 768px) {\n.container {\npadding: 10px;\n}\n.header img {\nheight: 30px;\n}\n}\n</style>\n</head>\n<body>\n<div class=\"container\">\n<div class=\"header\" style=\"background:#003654; padding-top:20px; text-align:center;\">\n<img src=\"cid:logo_image\" alt=\"AccuVelocity Logo\" style=\"display:block; margin:0 auto;\">\n</div>\n<div class=\"content\">\n<h1>Welcome to Your AccuVelocity Free Trial, Let's Get Started, {{ customer_name }}!</h1>\n<p>Hello {{ customer_name }},</p>\n<div style=\"color: black;\">\n<p>Thank you for signing up for our AccuVelocity free trial! We're excited to have you on board and can't wait for you to experience everything AccuVelocity has to offer.</p>\n<!-- <p>To help you get started, here are some resources to make the most of your trial:</p>\n<ul>\n<li><a href=\"{{ getting_started_guide }}\" class=\"contact-info\">Getting Started Guide</a>: This guide will walk you through the setup process and basic features.</li>\n<li><a href=\"{{ tutorial_video }}\" class=\"contact-info\">Tutorial Video</a>: This video will show you how to use AccuVelocity effectively.</li>\n<li><a href=\"{{ faq }}\" class=\"contact-info\">FAQ</a>: Find answers to common questions about AccuVelocity.</li>\n</ul> -->\n<p>Your free trial will last for {{ trial_duration }}, and you can access all premium features during this time. If you have any questions or need assistance, reach out to our support team at <a href=\"mailto:<EMAIL>\" class=\"contact-info\"><EMAIL></a> or +1 (*************.</p>\n<p>Visit our website to learn more: <a href=\"https://www.accuvelocity.com\" class=\"contact-info\">www.accuvelocity.com</a></p>\n</div>\n<p>Best regards,</p>\n<p>AccuVelocity Team</p>\n</div>\n<div class=\"footer\">\n<p>&copy; 2024 AccuVelocity. All rights reserved.</p>\n</div>\n</div>\n</body>\n</html>"}, "SubscriptionOnboarding": {"Email_Subject": "Welcome to AccuVelocity Premium, let's continue your success!", "Email_Body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Welcome to AccuVelocity {{ plan_name }}</title>\n<style>\nbody {\nfont-family: Arial, sans-serif;\nbackground-color: white;\ncolor: #003654;\nmargin: 0;\npadding: 0;\n}\n.container {\nwidth: 100%;\nmax-width: 1000px;\nmargin: 0 auto;\npadding: 20px;\nbox-sizing: border-box;\n}\n.header {\ndisplay: flex;\nalign-items: center;\npadding-bottom: 20px;\nborder-bottom: 2px solid #003654;\nbackground: #003654;\npadding-top: 20px;\n}\n.header img {\nheight: 40px;\nmargin-right: 20px;\n}\n.content {\npadding: 20px 0;\ncolor: #003654;\n}\n.footer {\npadding-top: 20px;\nborder-top: 2px solid #003654;\ntext-align: center;\n}\n.footer p {\nfont-size: 14px;\ncolor: #003654;\n}\n.contact-info {\ncolor: #003654;\n}\n@media (max-width: 768px) {\n.container {\npadding: 10px;\n}\n.header img {\nheight: 30px;\n}\n}\n</style>\n</head>\n<body>\n<div class=\"container\">\n<div class=\"header\" style=\"background:#003654; padding-top:20px; text-align:center;\">\n<img src=\"cid:logo_image\" alt=\"AccuVelocity Logo\" style=\"display:block; margin:0 auto;\">\n</div>\n<div class=\"content\">\n<h1>Welcome to AccuVelocity Premium, Let's Continue Your Success, {{ customer_name }}!</h1>\n<p>Hello {{ customer_name }},</p>\n<div style=\"color: black;\">\n<p>Congratulations on upgrading to our AccuVelocity Premium Plan! We're delighted to have you on board and are committed to helping you achieve even greater success with our advanced features and dedicated support.</p>\n<p>As a Premium member, you can now enjoy:</p>\n<ul>\n<li><b>{{ premium_feature_1 }}</b>: Benefit from {{ advantage_of_premium_feature_1 }}.</li>\n<li><b>{{ premium_feature_2 }}</b>: Enjoy {{ advantage_of_premium_feature_2 }}.</li>\n</ul>\n<p>If you have any questions or need assistance, our support team is always here to help at <a href=\"mailto:<EMAIL>\" class=\"contact-info\"><EMAIL></a> or +1 (*************.</p>\n<p>Visit our website to explore more: <a href=\"https://www.accuvelocity.com\" class=\"contact-info\">www.accuvelocity.com</a></p>\n<p>We're excited to continue supporting your journey with AccuVelocity and look forward to your continued success.</p>\n</div>\n<p>Best regards,</p>\n<p>AccuVelocity Team</p>\n</div>\n<div class=\"footer\">\n<p>&copy; 2024 AccuVelocity. All rights reserved.</p>\n</div>\n</div>\n</body>\n</html>"}, "NewContactUsNotification": {"Email_Subject": "New Contact Us Request Received!", "Email_Body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>New Contact Us Request</title>\n<style>\nbody {\nfont-family: Arial, sans-serif;\nbackground-color: white;\ncolor: #003654;\nmargin: 0;\npadding: 0;\n}\n.container {\nwidth: 100%;\nmax-width: 1000px;\nmargin: 0 auto;\npadding: 20px;\nbox-sizing: border-box;\n}\n.header {\ndisplay: flex;\nalign-items: center;\npadding-bottom: 20px;\nborder-bottom: 2px solid #003654;\nbackground: #003654;\npadding-top: 20px;\n}\n.header img {\nheight: 40px;\nmargin-right: 20px;\n}\n.content {\npadding: 20px 0;\ncolor: #003654;\n}\n.footer {\npadding-top: 20px;\nborder-top: 2px solid #003654;\ntext-align: center;\n}\n.footer p {\nfont-size: 14px;\ncolor: #003654;\n}\n.contact-info {\ncolor: #003654;\n}\n@media (max-width: 768px) {\n.container {\npadding: 10px;\n}\n.header img {\nheight: 30px;\n}\n}\n</style>\n</head>\n<body>\n<div class=\"container\">\n<div class=\"header\" style=\"background:#003654; padding-top:20px; text-align:center;\">\n<img src=\"cid:logo_image\" alt=\"AccuVelocity Logo\" style=\"display:block; margin:0 auto;\">\n</div>\n<div class=\"content\">\n<h1>New Contact Us Request from {{ customer_name }}</h1>\n<p>Hello Team,</p>\n<div style=\"color: black;\">\n<p>We have received a new contact request. Below are the details:</p>\n<ul>\n<li><b>Query Category:</b> {{ query_category }}</li>\n<li><b>Customer Name:</b> {{ customer_name }}</li>\n<li><b>Email:</b> <a href=\"mailto:{{ email }}\" class=\"contact-info\">{{ email }}</a></li>\n<li><b>Phone Number:</b> {{ phone_number }}</li>\n<li><b>Country:</b> {{ country }}</li>\n<li><b>Designation:</b> {{ designation }}</li>\n<li><b>Company Name:</b> {{ company_name }}</li>\n<li><b>Message:</b> {{ message }}</li>\n<li><b>Status:</b> {{ status }}</li>\n</ul>\n<p>Please follow up with the customer as soon as possible to address their query.</p>\n</div>\n<p>Best regards,</p>\n<p>AccuVelocity Team</p>\n</div>\n<div class=\"footer\">\n<p>&copy; 2024 AccuVelocity. All rights reserved.</p>\n</div>\n</div>\n</body>\n</html>"}, "BugReportNotification": {"Email_Subject": "Your Support Ticket Has Been Received", "Email_Body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Support Ticket Confirmation</title>\n<style>\nbody {\nfont-family: Arial, sans-serif;\nbackground-color: #f4f4f4;\nmargin: 0;\npadding: 0;\n}\n.email-container {\nwidth: 100%;\nmax-width: 600px;\nmargin: 0 auto;\nbackground-color: #ffffff;\npadding: 20px;\nborder: 1px solid #dddddd;\nborder-radius: 5px;\n}\n.email-header, .email-footer {\ntext-align: center;\npadding: 10px 0;\nbackground-color: #0073e6;\ncolor: #ffffff;\n}\n.email-header img {\nmax-width: 200px;\nmargin: 0 auto;\ndisplay: block;\n}\n.email-body {\nmargin: 20px 0;\n}\n.email-body p {\nline-height: 1.6;\n}\n.ticket-details {\nbackground-color: #f9f9f9;\npadding: 10px;\nborder-radius: 5px;\n}\n.ticket-details p {\nmargin: 0;\n}\n.email-footer p {\nmargin: 0;\nfont-size: 0.9em;\n}\n.support-link {\ncolor: #0073e6;\ntext-decoration: none;\n}\n</style>\n</head>\n<body>\n<div class=\"email-container\">\n<div class=\"email-header\">\n<img src=\"cid:logo_image\" alt=\"AccuVelocity Logo\">\n</div>\n<div class=\"email-body\">\n<p>Dear {{ Customer_Name }},</p>\n<p>Thank you for contacting AccuVelocity Support. This email confirms that we have received your support request, and a ticket has been created for you. Our team is dedicated to promptly resolving your issue.</p>\n<div class=\"ticket-details\">\n<p><strong>Ticket ID:</strong> {{ Ticket_ID }}</p>\n<p><strong>Issue Reported:</strong> {{ Description }}</p>\n<p><strong>Date Submitted:</strong> {{ Date_Submitted }}</p>\n</div>\n<p>Our support team is currently reviewing your ticket. You can expect a response within {{ Response_Time_Frame }}. If we require any additional information, we will contact you via this email.</p>\n<h3>Next Steps:</h3>\n<ol>\n<li><strong>Review Your Issue:</strong> Our support team will review the details you have provided.</li>\n<li><strong>Initial Response:</strong> We will reach out to you with an initial response and any follow-up questions if needed.</li>\n<li><strong>Resolution:</strong> Our team will work on resolving the issue and keep you updated on the progress.</li>\n</ol>\n<p><strong>Need Immediate Assistance?</strong></p>\n<p>If you have any urgent questions or require immediate assistance, please feel free to contact our support team directly at {{ SupportPhoneNumber }}.</p>\n<p>Thank you for your patience and understanding. We are committed to providing you with the best possible service and will do our utmost to resolve your issue promptly.</p>\n</div>\n<div class=\"email-footer\">\n<p>Best regards,</p>\n<p>AccuVelocity Support Team</p>\n</div>\n</div>\n</body>\n</html>"}, "ToUpNotification": {"Email_Subject": " Pages Added Successfully: AccuVelocity Pages Top-Up Confirmation", "Email_Body": "<!DOCTYPE html>\n<html lang=\"en\">\n<head>\n<meta charset=\"UTF-8\">\n<meta name=\"viewport\" content=\"width=device-width, initial-scale=1.0\">\n<title>Pages Added Successfully</title>\n<style>\nbody {\n    font-family: Arial, sans-serif;\n    background-color: #f4f4f4;\n    margin: 0;\n    padding: 0;\n}\n.email-container {\n    width: 100%;\n    max-width: 600px;\n    margin: 0 auto;\n    background-color: #ffffff;\n    padding: 20px;\n    border: 1px solid #dddddd;\n    border-radius: 5px;\n}\n.email-header, .email-footer {\n    text-align: center;\n    padding: 10px 0;\n    background-color: #0073e6;\n    color: #ffffff;\n}\n.email-header img {\n    max-width: 200px;\n    margin: 0 auto;\n    display: block;\n}\n.email-body {\n    margin: 20px 0;\n}\n.email-body p {\n    line-height: 1.6;\n}\n.details {\n    background-color: #f9f9f9;\n    padding: 10px;\n    border-radius: 5px;\n}\n.details p {\n    margin: 0;\n}\n.email-footer p {\n    margin: 0;\n    font-size: 0.9em;\n}\n.support-link {\n    color: #0073e6;\n    text-decoration: none;\n}\n</style>\n</head>\n<body>\n<div class=\"email-container\">\n<div class=\"email-header\">\n<img src=\"cid:logo_image\" alt=\"AccuVelocity Logo\">\n</div>\n<div class=\"email-body\">\n<p>Dear {{ User_Name }},</p>\n<p>We hope this message finds you well.</p>\n<p>We are pleased to inform you that your AccuVelocity account has successfully been topped up with additional add-on pages.</p>\n<div class=\"details\">\n<p><strong>Top-Up Date:</strong> {{ Top_Up_Date }}</p>\n<p><strong>Total Pages Added:</strong> {{ Total_Pages }}</p>\n<p><strong>Current Balance:</strong> {{ Current_Balance }}</p>\n</div>\n<p>These additional pages are now available and can be used immediately. If you have any questions or need further assistance, our support team is here to help. You can reach us at <a href=\"mailto:<EMAIL>\" class=\"support-link\"><EMAIL></a> or call us on (*************.</p>\n<p>Thank you for choosing AccuVelocity. We appreciate your continued trust in our services.</p>\n</div>\n<div class=\"email-footer\">\n<p>Best regards,</p>\n<p>AccuVelocity Team</p>\n</div>\n</div>\n</body>\n</html>"}}