// Components/DocumentComment.jsx
import { useState ,useEffect } from 'react';
import axios from 'axios';
import toast from 'react-hot-toast';

const DocumentComponent = ({ docId, docComment, onClose }) => {
    const [comment, setComment] = useState(docComment); // Initialize state with docComment
   

     // Fetch comment when the component mounts
     useEffect(() => {
        const fetchComment = async () => {
            try {
                const response = await axios.get(`${import.meta.env.VITE_SERVER}/doc/?DocId=${docId}`, {
                    headers: {
                        "Authorization": `Bearer ${localStorage.getItem('token')}`
                    }
                });
                if (response.data) {
                    setComment(response.data.Comment); // Assuming 'comment' is the key where the fetched comment is stored
                }
            } catch (error) {
                console.error("Oops! We couldn't load the comment right now. Please try again later.");
            }
        };

        fetchComment();
    }, [docId]); // Only re-run the effect if docId changes

    const handleCommentChange = (event) => {
        if (event.target.value.length <= 2000) {
            setComment(event.target.value);
        }
    };

    const handleSubmitComment = async () => {
        try {
             // Check if the comment is only spaces or empty
            if (comment.trim() === "") {
                toast.error("Oops! It looks like your comment is empty. Please type something to comment.");
                return;
            }

            const encodedComment = encodeURIComponent(comment);
            await axios.put(`${import.meta.env.VITE_SERVER}/doc/comment/?DocId=${docId}&strDocComment=${encodedComment}`, {
            }, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
            onClose(); // Close the modal after submitting
        } catch (error) {
            console.error("Sorry! We couldn't save your comment. Please check your connection and try again.");
        }
    };

    return (
        <div className="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center bg-black bg-opacity-60 backdrop-blur-xs transition-opacity">
            <div className="relative mx-auto w-full max-w-md rounded-xl bg-white p-6 shadow-md">
                <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between">
                        <h4 className="font-semibold text-gray-900" style={{ fontSize: '20px' }}>Add Document Comment</h4>
                        <button
                            onClick={onClose}
                            className="rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 transition-color focus:outline-none focus:ring focus:ring-blue-500"
                        >
                            Close
                        </button>
                    </div>
                    <div className="relative w-full">
                        <textarea
                            className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                            style={{ height: '125px', resize: 'none', overflow: 'auto' }} // Fixed height and no resize
                            placeholder="Please enter your comment here (up to 1000 characters allowed)."
                            value={comment}
                            onChange={handleCommentChange}
                            maxLength={1000}
                        />
                    </div>
                    <div className="flex justify-end mt-4">
                        <button
                            onClick={handleSubmitComment}
                            className="rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 w-full transition-color focus:outline-none focus:ring focus:ring-blue-500"
                        >
                            Submit
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
    
};

export default DocumentComponent;
