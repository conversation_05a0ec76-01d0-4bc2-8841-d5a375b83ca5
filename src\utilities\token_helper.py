# Importing libraries
from jose import <PERSON><PERSON><PERSON><PERSON><PERSON>, jwt
from typing import Op<PERSON>
from datetime import datetime, timedelta
from fastapi import Depends,HTTPException
from fastapi.security import OA<PERSON>2<PERSON><PERSON><PERSON><PERSON>earer
import os

# JWT Configuration

"""Please generate a new JWT_SECRET `using openssl rand -hex 32` command and add it in the .env file"""

# Initializing the Hashing alogorith 
JWT_SECRET = os.getenv("JWT_SECRET")
ALGORITHM = "HS256"
ACCESS_TOKEN_EXPIRE_MINUTES = 30

#Initialize with the login api url
oauth2_scheme = OAuth2PasswordBearer(tokenUrl="/login")



class TokenHelper:
    # Creates an access token
    @staticmethod
    def create_access_token(data: dict, expires_delta: Optional[timedelta] = None):
        to_encode = data
        
        if expires_delta:
            expire = datetime.utcnow() + expires_delta
        else:
            expire = datetime.utcnow() + timedelta(days=30)
            
        to_encode.update({"exp": expire})
        encoded_jwt = jwt.encode(to_encode, JWT_SECRET, algorithm=ALGORITHM)
        return encoded_jwt

    # Verifies if an access token has expired or not
    @staticmethod
    def verify_token(token: str):
        try:
            payload = jwt.decode(token, JWT_SECRET, algorithms=[ALGORITHM])
            
            email: str = payload.get("email")
            iUserID: str = payload.get("uid")
            if email is None:
                raise HTTPException(status_code=401, message="Credential validation error.")
            
            # Check if token has expired
            expiration_time = datetime.fromtimestamp(payload.get("exp"))

            is_expired = datetime.utcnow() > expiration_time
            return email,iUserID, is_expired
        
        except JWTError as e:
            return None, False
        
    # Verifies current logged in user
    def get_current_user(token: str = Depends(oauth2_scheme)):
        return TokenHelper.verify_token(token)
    
if __name__ == "__main__":
    objTokenHelper = TokenHelper()
    strToken = objTokenHelper.create_access_token(data={"email":"<EMAIL>"})
    email, expired = objTokenHelper.verify_token(strToken)
    

