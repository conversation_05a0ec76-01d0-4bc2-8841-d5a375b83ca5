from fastapi import APIRouter, status, HTTPException, Depends, Body
from typing import List
from src.Schemas.Api_Usage_Schema import UserAPIUsageSchema, UserAPITokenUsage, UserAPIRequestUsage
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from sqlalchemy.ext.asyncio import AsyncSession
from src.middleware.checkAuth import user_required


# Replace 'yourapp' with the actual name of your application package
UserAPIUsageRouter = APIRouter(tags=['UserAPIUsage'],  prefix="/api")

# insert_user_api_usage, reset_api_requested_for_user,reset_api_requested_for_all_users, update_user_api_usage, get_single_user_api_usage, @router.post("/user/api-usage/insert", status_code=status.HTTP_201_CREATED, response_model=UserAPIUsageSchema)
@UserAPIUsageRouter.post("/usage", status_code=status.HTTP_201_CREATED, response_model=UserAPIUsageSchema)
async def insert_api_usage(data: UserAPIUsageSchema = Body(...), iUserID:int=Depends(user_required)):
    try:
        # iUserID, iUserApiData: UserAPIUsageSchema
        return await CUserAPIUsageData.MSInsertUserApiUsage(iUserID=iUserID,iUserApiData= data)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Reset API Requested for a Single User
@UserAPIUsageRouter.patch("/usage/reset", status_code=status.HTTP_204_NO_CONTENT)
async def reset_api_requested_single(user_id: int = Depends(user_required)):
    try:
        await CUserAPIUsageData.MSResetApiRequestedForUser(iUserId=user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Reset API Requested for All Users
@UserAPIUsageRouter.patch("/usage/reset-all", status_code=status.HTTP_204_NO_CONTENT)
async def reset_api_requested_all(iUserID:int=Depends(user_required)):
    try:
        await CUserAPIUsageData.MSResetApiRequestedForAllUser(iUserId=iUserID)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Update User API Usage
@UserAPIUsageRouter.put("/usage/update-api-request", status_code=status.HTTP_200_OK, response_model=UserAPIRequestUsage)
async def updateAPIRequest(api_request: int,user_id: int=Depends(user_required) ):
    try:
        return await CUserAPIUsageData.MSUpdateUserApiRequest(iUserId= user_id,iAdditionalRequests=api_request)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@UserAPIUsageRouter.put("/usage/update-api-token", status_code=status.HTTP_200_OK, response_model=UserAPITokenUsage)
async def updateAPITokenUsage(tokens: int ,user_id: int=Depends(user_required)):
    try:
        #iUserId: int, iAdditionalTokens: int
        return await CUserAPIUsageData.MSUpdateUserApiTokenUsage( iUserId=user_id, iAdditionalTokens=tokens )
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    
# Get Single User API Usage
@UserAPIUsageRouter.get("/usage", status_code=status.HTTP_200_OK)
async def get_single_api_usage(user_id:int=Depends(user_required) ):
    try:
        return await CUserAPIUsageData.get_single_user_api_usage( user_id=user_id)
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Get All User API Usage
@UserAPIUsageRouter.get("/usage/all", response_model=List[UserAPIUsageSchema])
async def get_all_api_usage(iUserID:int=Depends(user_required) ):
    try:
        return await CUserAPIUsageData.MSGetAllUserApiUsage(iUserID=iUserID)
    except Exception as e:
        
        raise HTTPException(status_code=500, detail=str(e))