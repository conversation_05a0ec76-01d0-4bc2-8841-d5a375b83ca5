{"type": "json_schema", "json_schema": {"name": "17_<PERSON><PERSON>", "strict": true, "schema": {"type": "object", "properties": {"NameofCompany": {"type": "string", "description": "The name of the company or seller to whom the invoice is addressed."}, "SellerGSTIN": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN) of the seller."}, "SellerPANNo": {"type": "string", "description": "The Permanent Account Number (PAN) of the seller."}, "SellerAddress": {"type": "string", "description": "The complete address of the seller to whom the invoice is addressed."}, "SellerCity": {"type": "string", "description": "The city where the seller is located."}, "SellerState": {"type": "string", "description": "The state in which the seller is located."}, "SellerCountry": {"type": "string", "description": "The country where the seller is based."}, "SellerPinCode": {"type": "integer", "description": "The postal code for the seller's location."}, "SellerCIN": {"type": "string", "description": "The Corporate Identity Number (CIN) of the seller."}, "SellerSEZStatus": {"type": "string", "description": "The status indicating whether the seller is located in a Special Economic Zone (SEZ)."}, "SellerIRNNo": {"type": "string", "description": "The Invoice Reference Number (IRN) of the seller."}, "SellerCycleType": {"type": "string", "description": "The cycle type of the seller (e.g., quarterly, yearly)."}, "InvoiceNumber": {"type": "string", "description": "The unique identifier for the invoice."}, "InvoiceDate": {"type": "string", "description": "The date when the invoice was issued, represented as a string in 'ddmmyy' format.Ex 07/02/2025 then output will be 070225"}, "BOENo": {"type": "integer", "description": "The Bill of Entry (BOE) number for the transaction."}, "BLNo": {"type": "string", "description": "The Bill of Lading (BL) number for the transaction."}, "SupplyTypeCode": {"type": "string", "description": "The code indicating the type of supply."}, "DocumentTypeCode": {"type": "string", "description": "The code representing the document type (e.g., invoice, receipt)."}, "ReverseCharge": {"type": "string", "description": "Indicates if reverse charge applies on the transaction."}, "InvoiceCurrencyCode": {"type": "string", "description": "The currency in which the invoice is issued."}, "Version": {"type": "number", "description": "The version number of the invoice schema."}, "CustomerTradeName": {"type": "string", "description": "The trade name of the customer."}, "CustomerRefCode": {"type": "string", "description": "A reference code for the customer."}, "CustomerLegalName": {"type": "string", "description": "The legal name of the customer."}, "CustomerAddress": {"type": "string", "description": "The address of the customer."}, "CustomerCity": {"type": "string", "description": "The city where the customer is located."}, "CustomerDistrict": {"type": "string", "description": "The district where the customer is located."}, "CustomerState": {"type": "string", "description": "The state where the customer is located."}, "CustomerCountry": {"type": "string", "description": "The country where the customer is located."}, "CustomerPinCode": {"type": "integer", "description": "The postal code of the customer's location."}, "CustomerSEZStatus": {"type": "string", "description": "Indicates whether the customer is located in a Special Economic Zone (SEZ)."}, "CustomerGSTIN": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN) of the customer."}, "CustomerGSTStateCode": {"type": "string", "description": "The GST state code of the customer."}, "CustomerContactPerson": {"type": "string", "description": "The name of the contact person for the customer."}, "CustomerNote": {"type": "string", "description": "Any additional note or instruction from the customer."}, "FinanceLedCode": {"type": "string", "description": "The financial ledger code for the transaction."}, "VCN": {"type": "string", "description": "The Vessel Code Number (VCN) for the transaction."}, "VesselName": {"type": "string", "description": "The name of the vessel involved in the transaction."}, "IGMITEMNo": {"type": "string", "description": "The item number from the Import General Manifest (IGM)."}, "CHAName": {"type": "string", "description": "The name of the Customs House Agent (CHA) involved."}, "LineName": {"type": "string", "description": "The name of the line or shipping company."}, "ImporterName": {"type": "string", "description": "The name of the importer."}, "ImporterAddress": {"type": "string", "description": "The address of the importer."}, "PlaceofSupply": {"type": "string", "description": "The place where the goods or services are supplied."}, "TaxScheme": {"type": "string", "description": "The tax scheme applied to the invoice."}, "PaymentTerm": {"type": "string", "description": "The payment terms for the invoice (e.g., net 30 days)."}, "TotalTaxableAmount": {"type": "number", "description": "Total taxable amount for the Eacg Service"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SRNO": {"type": "integer", "description": "Serial number of the item."}, "SANCODE": {"type": "integer", "description": "Service or product code."}, "SERVICEDESC": {"type": "string", "description": "Description of the service or product."}, "BILLQTY": {"type": "integer", "description": "Quantity of the billed item."}, "BILLRATE": {"type": "integer", "description": "Rate per unit of the item."}, "TAXABLELEAMOUNT": {"type": "integer", "description": "Taxable amount for the item."}, "DISCOUNT": {"type": "integer", "description": "Discount applied to the item."}, "Rate(CGST%)": {"type": "integer", "description": "CGST rate for the item."}, "Amount(CGST)": {"type": "integer", "description": "CGST amount for the item."}, "Rate(SGST%)": {"type": "integer", "description": "SGST rate for the item."}, "Amount(SGST)": {"type": "integer", "description": "SGST amount for the item."}, "Rate(IGST%)": {"type": "integer", "description": "IGST rate for the item."}, "Amount(IGST)": {"type": "integer", "description": "IGST amount for the item."}, "BILLAMOUNT": {"type": "integer", "description": "Total bill amount for the item."}}, "required": ["SRNO", "SANCODE", "SERVICEDESC", "BILLQTY", "BILLRATE", "TAXABLELEAMOUNT", "DISCOUNT", "Rate(CGST%)", "Amount(CGST)", "Rate(SGST%)", "Amount(SGST)", "Rate(IGST%)", "Amount(IGST)", "BILLAMOUNT"], "additionalProperties": false}, "description": "Details of all the items listed in the invoice."}, "TotalTaxValue(InFigure)": {"type": "number", "description": "The total tax value (in figure) for the invoice."}, "TotalInvoiceValue(InFigure)": {"type": "number", "description": "The total invoice value (in figure) after taxes and discounts."}, "InvoiceValue(InWords)": {"type": "string", "description": "The total invoice value written in words."}, "AmountofTaxSubjecttoReverseCharge": {"type": "number", "description": "The amount of tax subject to reverse charge mechanism."}, "LsContainerNumber/Size": {"type": "array", "description": "Contains container numbers and sizes in the format 'cont_no/size'. This list should contain unique values.", "example_values": ["FFAU3647502/40", "KKFU8071020/40"], "items": {"type": "string", "description": "Container number and size combined as 'cont_no/size'. Example: 'FFAU3647502/40'."}}}, "required": ["NameofCompany", "SellerGSTIN", "SellerPANNo", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerState", "SellerCountry", "SellerPinCode", "SellerCIN", "SellerSEZStatus", "SellerIRNNo", "SellerCycleType", "InvoiceNumber", "InvoiceDate", "BOENo", "BLNo", "SupplyTypeCode", "DocumentTypeCode", "Reverse<PERSON><PERSON>ge", "InvoiceCurrencyCode", "Version", "CustomerTradeName", "CustomerRefCode", "CustomerLegalName", "Customer<PERSON><PERSON><PERSON>", "CustomerCity", "CustomerDistrict", "CustomerState", "CustomerCountry", "CustomerPinCode", "CustomerSEZStatus", "CustomerGSTIN", "CustomerGSTStateCode", "CustomerContact<PERSON>erson", "CustomerNote", "FinanceLedCode", "VCN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "IGMITEMNo", "CHAName", "LineName", "ImporterName", "ImporterAddress", "PlaceofSupply", "TaxScheme", "PaymentTerm", "TotalTaxableAmount", "Table", "TotalTaxValue(InFigure)", "TotalInvoiceValue(InFigure)", "InvoiceValue(InWords)", "AmountofTaxSubjecttoReverseCharge", "LsContainerNumber/Size"], "additionalProperties": false}}}