
import pandas as pd
from typing import <PERSON><PERSON>, Dict
import numpy as np
import os
import sys
import xml.etree.ElementTree as ET
from decimal import Decimal, ROUND_HALF_UP, ROUND_DOWN, ROUND_CEILING
import time
import copy
from src.utilities.helperFunc import TransactionUpdater
from src.Controllers.CustomLogger import CLogger
import traceback

class CSplitXMLFile:
 
    # TODO: Please Update Bank Name as per Customer Later on
    
    strBankName = "BOB" # Get from Mitul
    strCompanyName = "" # Get from Mitul
    strTallyBankName = "BOB -  **************"
    

    def _MGetTransactionDetails( row: pd.Series) -> Tuple[float, str, str, str]:
            """
            Purpose : 
                Dynamically figure out:
                - transaction_amount
                - transaction_type  (Payment or Receipt)
                - first_ledger_side (Dr or Cr)
                - second_ledger_side (Cr or Dr)

            Inputs  :
                row : A single transaction row from your DataFrame

            Output  :
                (transaction_amount, transaction_type, first_ledger_side, second_ledger_side)
            """
            colCrDr       = "crOrDr"
            colWithdrawal = "WITHDRAWAL(DR)"
            colDeposit    = "DEPOSIT(CR)"
            colAmount     = "AMOUNT(INR)"

            # Defaults
            transaction_amount = 0.0
            transaction_type   = "Receipt"  # default
            first_ledger_side  = "Cr"
            second_ledger_side = "Dr"

            try:
                # If there's a Cr/Dr column
                if colCrDr in row and isinstance(row[colCrDr], str):
                    if row[colCrDr].upper() == "DR":
                        try:
                            transaction_amount = float(row.get(colAmount, "0").replace(",", ""))
                        except:
                            transaction_amount = float(row.get(colAmount, 0) or 0)
                        transaction_type   = "Payment"
                        first_ledger_side  = "Dr"
                        second_ledger_side = "Cr"
                    else:
                        try:
                            transaction_amount = float(row.get(colAmount, "0").replace(",", ""))
                        except:
                            transaction_amount = float(row.get(colAmount, 0) or 0)
                        transaction_type   = "Receipt"
                        first_ledger_side  = "Cr"
                        second_ledger_side = "Dr"

                # Otherwise, if columns for withdrawal vs deposit
                elif (colWithdrawal in row) or (colDeposit in row):
                    withdrawal_val = row.get(colWithdrawal, np.nan)
                    deposit_val    = row.get(colDeposit, np.nan)

                    if pd.notna(withdrawal_val):
                        try:
                            transaction_amount = float(str(withdrawal_val).replace(",", ""))
                        except:
                            transaction_amount = float(withdrawal_val or 0)
                        transaction_type   = "Payment"
                        first_ledger_side  = "Dr"
                        second_ledger_side = "Cr"
                    else:
                        try:
                            transaction_amount = float(str(deposit_val).replace(",", ""))
                        except:
                            transaction_amount = float(deposit_val or 0)
                        transaction_type   = "Receipt"
                        first_ledger_side  = "Cr"
                        second_ledger_side = "Dr"
                else:
                    # Fallback if only a single 'Amount(INR)' col with no Cr/Dr
                    # Keep defaults: transaction_type=Receipt, first=Cr, second=Dr
                    try:
                        transaction_amount = float(row.get(colAmount, "0").replace(",", ""))
                    except:
                        transaction_amount = float(row.get(colAmount, 0) or 0)
                
                if CSplitXMLFile.strBankName == "BOB"or CSplitXMLFile.strBankName == "HDFC_2732" or CSplitXMLFile.strBankName == "HDFC_3648":
                    if transaction_type == "Payment":
                        transaction_type = "Bank Payment"
                    if transaction_type == "Receipt":
                        transaction_type = "Bank Receipt"
                        
                strLedgerPred = row.get("Tally Ledger Predicted (ML)", "Suspense")
                if strLedgerPred == "S New Cash" or strLedgerPred == "HDFC BANK J NEW 3852" or strLedgerPred == "HDFC Currant A/c 2732" or strLedgerPred == "HDFC SB A/c 9199" or strLedgerPred == "HDFC CURRENT (JUNIOR) A/C -7547" or strLedgerPred == "HDFC BANK S NEW 3648" or strLedgerPred == "Kotak Mahindra Bank-0879 S Fee" or strLedgerPred == "HDFC BANK J 2672" or strLedgerPred == "HDFC BANK S 8305" or strLedgerPred == "Kotak Mahindra Bank-0893 J Fee" or strLedgerPred == "Kotak Mahindra Bank-1271 S Payment" or strLedgerPred == "Kotak Mahindra Bank-2827 CC":
                    transaction_type = "Contra"

            except Exception as e:
                print(e)

            return (transaction_amount, transaction_type, first_ledger_side, second_ledger_side)


    def MCreateTallyXML( df: pd.DataFrame, strCustomerName: str, bDebug: bool = False) -> str:
        """
        Purpose : Convert the DataFrame to Tally-compatible XML format using 
                the same dynamic approach for Payment/Receipt logic, 
                plus the SGST+CGST special case.

        Inputs  :
            df            : DataFrame with final ledger predictions
            strCompanyName: The Tally company name to embed in XML

        Output  :
            Path to the generated XML file (e.g. "transactions.xml")
        """
        try:
            envelope = ET.Element("ENVELOPE")
            header = ET.SubElement(envelope, "HEADER")
            ET.SubElement(header, "VERSION").text = "5"
            ET.SubElement(header, "TALLYREQUEST").text = "Import"
            ET.SubElement(header, "TYPE").text = "Data"
            ET.SubElement(header, "ID").text = "Vouchers"

            body = ET.SubElement(envelope, "BODY")
            desc = ET.SubElement(body, "DESC")
            static_vars = ET.SubElement(desc, "STATICVARIABLES")
            ET.SubElement(static_vars, "SVCURRENTCOMPANY").text = strCustomerName

            data = ET.SubElement(body, "DATA")
            tally_msg = ET.SubElement(data, "TALLYMESSAGE")

            intVoucherNo = 1
            for idx, row in df.iterrows():
                # gather dynamic details
                (amount, transaction_type, first_side, second_side) = CSplitXMLFile._MGetTransactionDetails(row)
                strLedgerPred = row.get("Tally Ledger Predicted (ML)", "Suspense")
                voucher_type_name = "Contra" if strLedgerPred.lower() == "cash" else transaction_type

                # create <VOUCHER>
                voucher = ET.SubElement(tally_msg, "VOUCHER")

                # date strDate
                dt_obj = pd.to_datetime(
                    #row.get("Txn Date") or row.get("Transaction Date"), 
                    row.get("TRAN DATE"),
                    dayfirst=True, 
                    errors='coerce'
                )
                dt_str = dt_obj.strftime("%Y%m%d") if not pd.isna(dt_obj) else ""

                # basic voucher fields
                ET.SubElement(voucher, "DATE").text = dt_str
                #ET.SubElement(voucher, "NARRATION").text = row.get("Description") or row.get("Transaction Remarks", "")
                ET.SubElement(voucher, "NARRATION").text = row.get("NARRATION", "")
                ET.SubElement(voucher, "VOUCHERTYPENAME").text = voucher_type_name
                ET.SubElement(voucher, "NUMBERINGSTYLE").text = "Automatic"
                ET.SubElement(voucher, "PERSISTEDVIEW").text = "Accounting Voucher View"

                if strLedgerPred.startswith("(as per details)") and strLedgerPred.endswith("Card Machine Exp."):
                    
                    # Extract the card name (value after "(as per details)" and before "|")
                    card_name = strLedgerPred.split("(as per details)")[1].split("|")[0].strip()

                    # Convert to Decimal for precision and rounding
                    amount = Decimal(amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                    machine_exp_amount = (amount * Decimal("0.0082607")).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                    
                    # Ensure total credit matches total debit exactly
                    card_amount = (amount - machine_exp_amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

                    # Compute total debits and credits
                    total_debit = (machine_exp_amount + amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                    total_credit = card_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

                    # If there's a mismatch, adjust card_amount to fix it
                    diff = (total_debit - total_credit).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                    if diff != Decimal("0.00"):
                        card_amount += diff  # Adjust to fix mismatch
                        
                    if card_amount % 1 != 0:
                        # Round card_amount up to the next whole number
                        card_amount_rounded_up = card_amount.to_integral_value(rounding=ROUND_CEILING)
                        
                        # How much we are adding by rounding up
                        extra = (card_amount_rounded_up - card_amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                        
                        # Update card_amount to the rounded-up value
                        card_amount = card_amount_rounded_up
                        
                        # Subtract the extra amount from machine_exp_amount to keep totals balanced
                        machine_exp_amount -= extra
                        # Re-quantize to 2 decimals to avoid leftover fractions
                        machine_exp_amount = machine_exp_amount.quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)
                        
                    machine_exp_amount = (card_amount - amount).quantize(Decimal("0.01"), rounding=ROUND_HALF_UP)

                    # Ledger entry for the Card Name
                    ledger_card = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_card, "LEDGERNAME").text = card_name
                    ET.SubElement(ledger_card, "ISDEEMEDPOSITIVE").text = "No"  # Credit
                    ET.SubElement(ledger_card, "AMOUNT").text = str(card_amount)

                    # Ledger entry for Card Machine Exp.
                    ledger_exp = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_exp, "LEDGERNAME").text = "Card Machine Exp."
                    ET.SubElement(ledger_exp, "ISDEEMEDPOSITIVE").text = "Yes"  # Debit
                    ET.SubElement(ledger_exp, "AMOUNT").text = str(-machine_exp_amount)

                    # Bank ledger entry
                    ledger_bank = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_bank, "LEDGERNAME").text = CSplitXMLFile.strTallyBankName
                    ET.SubElement(ledger_bank, "ISDEEMEDPOSITIVE").text = "Yes"  # Debit
                    ET.SubElement(ledger_bank, "AMOUNT").text = str(-amount)

                elif strLedgerPred.startswith("(as per details)") and strLedgerPred.endswith("+ 4.72 Bank Charges"):
                    
                    amount_adjusted = round(float(amount) - 4.72, 2)

                    ledger_sgst = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_sgst, "LEDGERNAME").text = "Adani Total Gas Ltd"
                    ET.SubElement(ledger_sgst, "ISDEEMEDPOSITIVE").text = "Yes"  # Dr is typically negative
                    ET.SubElement(ledger_sgst, "AMOUNT").text = str(-abs(float(amount - 4.72)))

                    ledger_cgst = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_cgst, "LEDGERNAME").text = "Bank Charges"
                    ET.SubElement(ledger_cgst, "ISDEEMEDPOSITIVE").text = "Yes" # Dr is typically negative
                    ET.SubElement(ledger_cgst, "AMOUNT").text = str(-4.72)

                    ledger_bank = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_bank, "LEDGERNAME").text = CSplitXMLFile.strTallyBankName
                    ET.SubElement(ledger_bank, "ISDEEMEDPOSITIVE").text = "No"  # Cr typically positive
                    ET.SubElement(ledger_bank, "AMOUNT").text = str(abs(amount))
                
                else:
                    # line1 (predicted ledger)
                    ledger_entry_1 = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_entry_1, "LEDGERNAME").text = strLedgerPred

                    isDeemedPos_1 = "Yes" if first_side == "Dr" else "No"
                    signed_amount_1 = -amount if first_side == "Dr" else amount
                    ET.SubElement(ledger_entry_1, "ISDEEMEDPOSITIVE").text = isDeemedPos_1
                    ET.SubElement(ledger_entry_1, "AMOUNT").text = str(signed_amount_1)

                    # line2 (bank ledger)
                    ledger_entry_2 = ET.SubElement(voucher, "LEDGERENTRIES.LIST")
                    ET.SubElement(ledger_entry_2, "LEDGERNAME").text = CSplitXMLFile.strTallyBankName

                    isDeemedPos_2 = "Yes" if second_side == "Dr" else "No"
                    signed_amount_2 = -amount if second_side == "Dr" else amount
                    ET.SubElement(ledger_entry_2, "ISDEEMEDPOSITIVE").text = isDeemedPos_2
                    ET.SubElement(ledger_entry_2, "AMOUNT").text = str(signed_amount_2)

                intVoucherNo += 1

            # finalize
            # xml_file_name = "transactions.xml"
            # ET.ElementTree(envelope).write(xml_file_name, encoding="utf-8", xml_declaration=True)
            return envelope

        except Exception as e:
            print(e)
                
    def MSaveToXML(xmlData: ET.Element, 
                    exportPathXml: str,
                    intIsCleaned: int = 0):
        """
        Saves XML data to a file.

        Inputs:
            xmlData: XML ElementTree object
            exportPathXml: Path where XML file should be saved
        """
        absoluteExportPathXML = os.path.abspath(exportPathXml)
        tree = ET.ElementTree(xmlData)
        tree.write(absoluteExportPathXML, encoding="utf-8", xml_declaration=True)


    async def MSplitAndSaveTallyXML(strPredictedXLSXFile: str,
                            strCustomerName: str,
                            exportFolderPath: str,
                            rows_per_file: int = 5,
                            lsTransactionList: list = []):
        """
        Splits the DataFrame into chunks and generates XML files for each chunk.

        Inputs:
            df              : DataFrame with predicted ledger rows
            strCustomerName : Company name to be used inside XML
            exportFolderPath: Folder path where XML files will be saved
            rows_per_file   : Number of transactions per XML file
        """
        # try:
        #     lsXMLFiles = []
        #     df = pd.read_excel(strPredictedXLSXFile)
        #     # Make sure the export folder exists
        #     os.makedirs(exportFolderPath, exist_ok=True)

        #     total_rows = len(df)
        #     num_files = (total_rows + rows_per_file - 1) // rows_per_file  # Ceiling division

        #     for i in range(num_files):
        #         try:
        #             start_idx = i * rows_per_file
        #             end_idx = min(start_idx + rows_per_file, total_rows)
        #             df_chunk = df.iloc[start_idx:end_idx]

        #             # Create XML envelope for the chunk
        #             envelope = CSplitXMLFile.MCreateTallyXML(df=df_chunk, strCustomerName=strCustomerName)

        #             # Define the filename
        #             file_name = f"transactions_part_{i+1}.xml"
        #             export_path = os.path.join(exportFolderPath, file_name)

        #             # Save the XML file
        #             CSplitXMLFile.MSaveToXML(xmlData=envelope, exportPathXml=export_path)
        #             print(f"Saved: {export_path}")
        #             lsXMLFiles.append(export_path)
        #         except Exception as e:
        #             print(f"Error Occur while processing this index xml {i}, Error is {str(e)}")
        #     return lsXMLFiles
        # except Exception as e:
        #     print(f"Error Occur in Splitting XML Files - {str(e)}")
        #     raise e
        
        try:
            # Create output directory if it doesn't exist
            CLogger.MCSetupLogging()
            os.makedirs(exportFolderPath, exist_ok=True)
            filePrefix =  os.path.splitext(os.path.basename(os.path.normpath(strPredictedXLSXFile)))[0]
            # Read and parse the XML file
            tree = ET.parse(strPredictedXLSXFile)
            root = tree.getroot()
            iStatementId = filePrefix.split('_')[-1]
            # Extract header and desc elements to preserve in each file
            header = root.find('HEADER')
            desc = root.find('BODY/DESC')

            # Find all voucher elements within TALLYMESSAGE
            tallymessage = root.find('BODY/DATA/TALLYMESSAGE')
            vouchers = tallymessage.findall('VOUCHER')
            n = rows_per_file # TODO Change the name of variable at both place

            # Ensure n is positive; if not, default to 1
            if n < 1:
                n = 1

            # Initialize list to store file paths
            file_paths = []

            # Process vouchers in groups of n
            for j, i in enumerate(range(0, len(vouchers), n), 0):
                try:
                    # Get the current group of vouchers (up to n vouchers)
                    group = vouchers[i:i + n]

                    # Create a new ENVELOPE element
                    new_envelope = ET.Element('ENVELOPE')

                    # Copy HEADER using deepcopy
                    new_header = copy.deepcopy(header)
                    new_envelope.append(new_header)

                    # Create BODY
                    new_body = ET.SubElement(new_envelope, 'BODY')

                    # Copy DESC using deepcopy
                    new_desc = copy.deepcopy(desc)
                    new_body.append(new_desc)

                    # Create DATA
                    new_data = ET.SubElement(new_body, 'DATA')

                    # Create TALLYMESSAGE
                    new_tallymessage = ET.SubElement(new_data, 'TALLYMESSAGE')

                    # Copy all vouchers in the current group using deepcopy
                    for voucher in group:
                        voucher_copy = copy.deepcopy(voucher)
                        new_tallymessage.append(voucher_copy)

                    # Convert to string with XML declaration
                    xml_str = ET.tostring(new_envelope, encoding='utf-8', method='xml')
                    xml_str = b'<?xml version="1.0" encoding="utf-8"?>\n' + xml_str
                
                    # Define the output file path
                    output_path = os.path.join(exportFolderPath, f'{filePrefix}_{lsTransactionList[j]}.xml')

                    # Write to the file
                    with open(output_path, 'wb') as f:
                        f.write(xml_str)

                    objUpdate = await TransactionUpdater.MSUpdateTransactionXMLResponse(intTxnId = lsTransactionList[j], intStatementId = iStatementId,strXMLResponse = xml_str)
                    
                    # Add the file path to the list
                    file_paths.append(output_path)
                except Exception as e:
                    CLogger.MCWriteLog("ERROR", f"Error Occur while processing this index xml {i}, Error is {str(e)}")
                # Create Method To Update Transactions

            # Print confirmation message
            print(f"XML files have been created successfully in {exportFolderPath} with up to {n} vouchers each.")

            CLogger.MCWriteLog("INFO", f"XML files have been created successfully in {exportFolderPath} with up to {n} vouchers each, file_paths {file_paths}.")
            # Return the list of file paths
            return file_paths
        except Exception as objException:
            CLogger.MCWriteLog("ERROR", f"Error Occur in Splitting XML Files - {str(traceback.format_exc())}")
            print(f"Error{objException}")
            raise objException

if __name__ == "__main__":
    CSplitXMLFile.MSplitAndSaveTallyXML(
        strPredictedXLSXFile=r"\\192.168.1.15\user_data\MITUL\Downloads\TallyXML_GwaliaTest (1).xml",
        strCustomerName="Gwalia Sweets Pvt Ltd - (24-25)",
        exportFolderPath=r"\\192.168.1.15\user_data\MITUL\Documents\Nisarg Vyas\playground\Test Folder XMl\Gwalia",
        rows_per_file=5 # Change The name to the number of voucher in a single xml
    )
    pass