from config.db_config import AsyncSessionLocal
from src.Models.models import GRNNoPITracking
from src.Controllers.Logs_Controller import CLogController
from sqlalchemy.exc import SQLAlchemyError
import traceback
from datetime import datetime
from sqlalchemy import select

class CGrnNoPITracking:
    """
    Purpose:
        This class handles database operations for the `grn_records` table.
        It provides functionality to create and update records in the `grn_records` table.
    """
    @staticmethod
    async def MSInsertGrnRecord(kwargs: dict, userId: int):
        """
        Adds or updates a GRNNoPITracking record in the database.

        Args:
            kwargs (dict): Dictionary containing column-value pairs (GrnNo, Doc_id, VendorName, TallyImportedStatus).
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing APIStatusCode, detail, and the ID of the created/updated record.

        Raises:
            Exception: If a database error or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Extract required fields from kwargs
                GrnNo = kwargs.get('GrnNo')
                Doc_id = kwargs.get('Doc_id')
                VendorName = kwargs.get('VendorName')
                TallyImportedStatus = kwargs.get('TallyImportedStatus')

                # Validate required fields
                if GrnNo is None:
                    raise ValueError("GrnNo is required")
                if Doc_id is None:
                    raise ValueError("Doc_id is required")
                if VendorName is None:
                    raise ValueError("VendorName is required")
                if TallyImportedStatus is None:
                    TallyImportedStatus = "999"

                
                # Insert new record
                new_record = GRNNoPITracking(
                    GrnNo=GrnNo,
                    Doc_id=Doc_id,
                    VendorName=VendorName,
                    TallyImportedStatus=TallyImportedStatus
                )
                db.add(new_record)
                await db.commit()
                await db.refresh(new_record)

                await CLogController.MSWriteLog(userId, "Info", f"GRNNoPITracking record with ID {new_record.id} created successfully.")
                return {
                    "APIStatusCode": 200,
                    "detail": "Record created successfully",
                    "id": new_record.id
                }

            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Failed to save/update GRNNoPITracking record to DB")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")

    @staticmethod
    async def MSUpdateTallyStatus(Doc_id: int, TallyImportedStatus: str, userId: int):
        """
        Updates the TallyImportedStatus field of a GRNNoPITracking record based on Doc_id.

        Args:
            Doc_id (int): The document ID to identify the record.
            TallyImportedStatus (str): The new status to set (must be a valid TallyStatus value).
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing APIStatusCode, detail, and the ID of the updated record.

        Raises:
            Exception: If a database error, unhandled exception, or invalid status occurs.
        """
        async with AsyncSessionLocal() as db:
            try:

                # Fetch record by Doc_id
                query = select(GRNNoPITracking).where(GRNNoPITracking.Doc_id == Doc_id)
                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    await CLogController.MSWriteLog(userId, "Info", f"No GRNNoPITracking record found for Doc_id {Doc_id}.")
                    return {
                        "APIStatusCode": 404,
                        "detail": "Record not found",
                        "id": None
                    }

                # Update TallyImportedStatus
                record.TallyImportedStatus = TallyImportedStatus
                record.updated_at = datetime.now()
                await db.commit()
                await db.refresh(record)

                await CLogController.MSWriteLog(userId, "Info", f"GRNNoPITracking record with ID {record.id} updated TallyImportedStatus to {TallyImportedStatus}.")
                return {
                    "APIStatusCode": 200,
                    "detail": "TallyImportedStatus updated successfully",
                    "id": record.id
                }

            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Failed to update TallyImportedStatus for GRNNoPITracking record")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")
    
    @staticmethod
    async def MSCheckDuplicateGrn(grn_list: list, userId: int, invoice_no: int):
        """
        Checks if any GRN in the provided list already exists in the database, considering only records
        with TallyImportedStatus of 'Success' or 'PartialSuccess'.

        Args:
            grn_list (list): List of GRN numbers to check for duplicates.
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            None: If no duplicates are found.

        Raises:
            ValueError: If a duplicate GRN is found, with details of Doc_id, GrnNo, and SellerName.
            Exception: If a database error or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Validate input
                if not grn_list or not isinstance(grn_list, list):
                    raise ValueError("grn_list must be a non-empty list")

                # Fetch records where TallyImportedStatus is '200'
                query = select(GRNNoPITracking).where(
                    GRNNoPITracking.TallyImportedStatus == '200'
                )
                result = await db.execute(query)
                records = result.scalars().all()

                # Flatten all grn_no lists into a single list
                all_grns = []
                record_details = {}  # Map GRN to record details for error reporting
                for record in records:
                    grn_no_list = record.GrnNo  # This is a JSON list (e.g., ["GRN001", "GRN002"])
                    if isinstance(grn_no_list, list):
                        for grn in grn_no_list:
                            all_grns.append(grn)
                            record_details[grn] = {
                                "Doc_id": record.Doc_id,
                                "GrnNo": grn_no_list,
                                "SellerName": record.VendorName
                            }

                # Check for duplicates
                for grn in grn_list:
                    if grn in all_grns:
                        details = record_details[grn]
                        error_msg = (f"ValidationError: "
                            f"Duplicate GRN found: {grn}. "
                            f"Details - Invoice No: {invoice_no}, "
                            f"GrnNo: {details['GrnNo']}, "
                            f"SellerName: {details['SellerName']}"
                        )
                        await CLogController.MSWriteLog(userId, "Error", error_msg)
                        raise ValueError(error_msg)

                await CLogController.MSWriteLog(userId, "Info", "No duplicate GRNs found.")
                return False

            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(userId, "Error", f"Failed to check for duplicate GRNs in GRNNoPITracking")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                if str(e).startswith('ValidationError'):
                    raise ValueError(error_msg)
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")