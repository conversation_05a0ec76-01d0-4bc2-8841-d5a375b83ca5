import asyncio
import logging
import os
from datetime import datetime
from dotenv import load_dotenv
from openai import OpenAI
import sys
sys.path.append(".")
from src.Controllers.GPTBatchAPIController import CGPTBatchAPIDB
from src.Controllers.BatchAPIController import CBatchAPIController
from src.Schemas.schemas import GPTBatchAPIStatusEnum
# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)
API_KEY = os.getenv('OPENAI_API_KEY')
client = OpenAI()

class BatchAPIRetrieve:
    def __init__(self, poll_interval: int = 60):
        """
        Initialize the BatchAPIRetrieve class.

        Args:
            poll_interval (int): Interval in seconds to poll for new batch jobs.
        """
        self.poll_interval = poll_interval
        self.logger = self.setup_logger()

    def setup_logger(self):
        """
        Set up the logger for the class.

        Returns:
            logging.Logger: Configured logger instance.
        """
        # Define log directory and file
        log_dir = r'Logs//BatchAPI_logs'
        os.makedirs(log_dir, exist_ok=True)
        today_date_str = datetime.now().strftime('%Y-%m-%d')
        log_file = os.path.join(log_dir, f"BatchAPIRetrieve_{today_date_str}.log")

        # Create logger
        logger = logging.getLogger("BatchAPIRetrieve")
        logger.setLevel(logging.DEBUG)  # Capture all log levels

        # File handler to write logs to a file
        fh = logging.FileHandler(log_file, encoding='utf-8')
        fh.setLevel(logging.DEBUG)

        # Console handler to print logs to the console
        ch = logging.StreamHandler()
        ch.setLevel(logging.INFO)

        # Define formatter and add to both handlers
        formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
        fh.setFormatter(formatter)
        ch.setFormatter(formatter)

        # Add handlers to the logger
        if not logger.handlers:
            logger.addHandler(fh)
            logger.addHandler(ch)

        return logger

    async def start_polling(self):
        """
        Start polling at regular intervals to check for batch jobs.
        """
        while True:
            try:
                # self.logger.info("Polling for batch jobs with 'in_progress' status...")
                # Call the method to get the list of batch jobs with 'in_progress' status
                batch_jobs = await CGPTBatchAPIDB.getBatchRecordsWithStatusFilter(status_list=["in_progress"])

                if not batch_jobs:
                    self.logger.info("No batch job tasks found with 'in_progress' status.")
                else:
                    await self.process_batch_jobs(batch_jobs)

            except Exception as e:
                self.logger.error(f"Error occurred during polling: {e}")

            # Wait for the next polling interval
            await asyncio.sleep(self.poll_interval)

    async def process_batch_jobs(self, batch_jobs: list):
        """
        Process the list of batch jobs.

        Args:
            batch_jobs (list): List of batch jobs to process.
        """
        for singlebatchdetails in batch_jobs:
            batch_job_id = None
            try:
                # Extract required information
                userid = singlebatchdetails.get("userid")
                batch_job_record_id = singlebatchdetails.get("id")
                batch_job_id = singlebatchdetails.get("Batch_Object").get("id")
                base_directory = "Data\\BatchAPI_TasksJsonlFile"
                bProcessTally = True
                filename_format = "%Y%m%d%H%M%S.jsonl"

                self.logger.info(f"Processing batch job ID: {batch_job_id}")

                # Fetch batch job object to check the status and request counts
                batch_job = client.batches.retrieve(batch_job_id)
                
                # Check if all requests have failed
                if (batch_job.request_counts.failed == batch_job.request_counts.total):
                    self.logger.error(f"All requests failed for batch job ID: {batch_job_id}. Marking status as 'error'.")
                    await CGPTBatchAPIDB.MSUpdateBatchRecord(
                        record_id=batch_job_record_id, 
                        update_fields={"Status": GPTBatchAPIStatusEnum.error}
                    )
                    continue  # Skip further processing for this batch job

                # Check if the status is completed before retrieving result file ID
                if batch_job.status != "completed":
                    self.logger.info(f"Skipping batch job ID: {batch_job_id}, current status: {batch_job.status}.")
                    continue  # Skip further processing for non-completed jobs
                
                if batch_job.status in ["failed", "error", "expired", "cancelled"]:
                    self.logger.error(f"All batch task requests failed for batch job ID: {batch_job_id} as batch status found to be {batch_job.status}. Marking status as 'error'.")
                    await CGPTBatchAPIDB.MSUpdateBatchRecord(
                        record_id=batch_job_record_id, 
                        update_fields={"Status": GPTBatchAPIStatusEnum.error}
                    )
                    continue  # Skip further processing for this batch job
                
                # Retrieve the result file ID
                result_file_id = batch_job.output_file_id
                if not result_file_id:
                    if batch_job.error_file_id:
                        result_file_id = batch_job.error_file_id
                        self.logger.error(f"Error file found for batch job ID: {batch_job_id}. Marking status as 'error'.")
                        # Update batch job status to error
                        await CGPTBatchAPIDB.MSUpdateBatchRecord(
                            record_id=batch_job_record_id, 
                            update_fields={"Status": GPTBatchAPIStatusEnum.error}
                        )
                    else:
                        self.logger.warning(f"No result file or error file found for batch job ID: {batch_job_id}.")
                        result_file_id = None

                # Log about the result file ID retrieval
                if result_file_id:
                    self.logger.info(f"Retrieving result file with ID: {result_file_id}")
                    # Call the static method to retrieve and store batch results
                    results = await CBatchAPIController.retrieve_and_store_batch_results(
                        userid=userid,
                        batch_job_record_id=batch_job_record_id,
                        result_file_id=result_file_id,
                        base_directory=base_directory,
                        bProcessTally=bProcessTally,
                        filename_format=filename_format
                    )

                    # Log batch ID after successfully processing it
                    self.logger.info(f"Successfully processed batch job ID: {batch_job_id}")
                else:
                    self.logger.warning(f"Skipped processing for batch job ID: {batch_job_id} due to missing result file ID.")
            
            except Exception as e:
                self.logger.error(f"Failed to process batch job ID: {batch_job_id}. Error: {e}")

# Example usage
if __name__ == "__main__":
    # Start the polling process
    batch_api_retrieve = BatchAPIRetrieve(poll_interval=60)
    asyncio.run(batch_api_retrieve.start_polling())
