### Step-by-Step Guide to Set Up Nginx for AccuVelocity

**First: Open Terminal(at any location)**

1. **Set Permissions for Your Project Directory**
   Ensure the paths reflect your setup (e.g., `/home/<USER>/path_to_your_project`).

   ```sh
    sudo chgrp -R www-data /home/<USER>/path_to_your_project/client/dist
    sudo find /home/<USER>/path_to_your_project/client/dist -type d -exec chmod 750 {} \;
    sudo find /home/<USER>/path_to_your_project/client/dist -type f -exec chmod 640 {} \;
    sudo chmod +x /home/<USER>/home/<USER>/path_to_your_project /home/<USER>/path_to_your_project/client /home/<USER>/path_to_your_project/client/dist
   ```

2. **Configure Nginx for Your Application**
   Edit the Nginx configuration file for your application domain.

   ```sh
   sudo nano /etc/nginx/sites-available/app.accuvelocity.com
   ```

   Add the following configuration:

   ```nginx
   server {
       listen 80;
       server_name app.accuvelocity.com;

       root /home/<USER>/path_to_your_project/client/dist;
       index index.html;

       location / {
           try_files $uri $uri/ /index.html;
       }

       # Handle Gzip Compression
       gzip on;
       gzip_vary on;
       gzip_proxied any;
       gzip_comp_level 6;
       gzip_types text/plain text/css application/json application/javascript text/xml application/xml application/xml+rss text/javascript;

       # Security Headers
       add_header X-Frame-Options "SAMEORIGIN" always;
       add_header X-Content-Type-Options "nosniff" always;
       add_header X-XSS-Protection "1; mode=block" always;
       add_header Referrer-Policy "no-referrer-when-downgrade" always;
       add_header Content-Security-Policy "default-src 'self' http: https: data: blob: 'unsafe-inline'" always;
       add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

       # Disable logging for favicon
       location = /favicon.ico {
           log_not_found off;
           access_log off;
       }

       # Disable logging for robots.txt
       location = /robots.txt {
           log_not_found off;
           access_log off;
       }
   }
   ```

   -> Save the changes with ctrl + s
   -> Now Exit the gnu editor by ctrl + x


3. **Enable the Site and Test the Configuration**
   Create a symbolic link to enable the site and test the Nginx configuration.

   ```sh
   sudo ln -s /etc/nginx/sites-available/app.accuvelocity.com /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

4. **Set Up SSL with Certbot**
   Finally, obtain and install an SSL certificate with Certbot.

   ```sh
   sudo certbot --nginx -d app.accuvelocity.com
   ```

5. **if you get this kind of message that means everything has worked successfully**
   
   ![alt text](image.png)

## Troubleshooting Certbot Nginx Configuration Error

If you encounter the following error message:

```Hint: The Certificate Authority failed to verify the temporary nginx configuration changes made by Certbot. Ensure the listed domains point to this nginx server and that it is accessible from the internet.```


Then ensure to change your public IP pinpoint by following the steps below:

### Update DNS Records

1. **Log in to Your Domain Registrar:**
   - Go to your domain registrar's website (e.g., GoDaddy, Namecheap, Google Domains, etc.) and log in to your account.

2. **Find DNS Management:**
   - Navigate to the DNS management section. It might be labeled as "DNS Settings," "Manage DNS," "DNS Management," or similar.

3. **Update the `A` Record:**
   - Find the `A` record that is currently pointing `www.yourdomain.com` to the old AWS IP address (`ex **************`).
   - Edit this `A` record and update the IP address to your new IP address (`ex **************`).
   - Save the changes.




By following these steps, your Nginx server should be properly configured to serve your AccuVelocity application securely and efficiently.