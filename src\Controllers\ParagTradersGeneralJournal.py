import sys
sys.path.append("")
import traceback
import json
import xml.etree.ElementTree as ET
from datetime import datetime
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, LedgerEntrySchema
from src.Schemas.TallyJournalVoucherXMLSchema import CTallyJournalVoucherTemplate, TallyJournalVoucherInputSchema, CompanyInfoSchema, LedgerEntrySchema
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Logs_Controller import CLogController
import math
import uuid
import os
import asyncio




class CParagJournal:

    _mStrLedgerName = ""
    _mDictCompanyData = {
                            "company_name": "",
                            "gst_registration_type": "",
                            "state_name": "",
                            "country_name": "",
                            "gst_in":"",
                        }
    _mstrVoucherTypeName = ""
    _mstrVocuherEntryMode = ""
    _mstrRoundOffLedger = ""
    _mstrDebitLedgerName = "" 
    _mboolTDSApplied = False
    _mstrCategory = None
    _mstrCostCenterLocation = None
    _mIUserId = 4
    _msStrAccuVelocityComments = ""
    _mStrTracebackLogs = ""

    @classmethod
    def MCResetAttributes(cls):
        cls._mStrLedgerName = ""
        cls._mDictCompanyData = {
            "company_name": "",
            "gst_registration_type": "",
            "state_name": "",
            "country_name": "",
            "gst_in": "",
        }
        cls._mstrVoucherTypeName = ""
        cls._mstrVocuherEntryMode = ""
        cls._mstrRoundOffLedger = ""
        cls._mstrDebitLedgerName = ""
        cls._mboolTDSApplied = False
        cls._mstrCategory = None
        cls._mstrCostCenterLocation = None
        cls._mIUserId = 4
        cls._msStrAccuVelocityComments = ""
        cls._mStrTracebackLogs = ""

    @classmethod
    async def MCSetAttributes(cls, strConfigFilePath: str, iUserId: int,boolTDSApplied = False, strCategory = None, strCostCenterLocation = None):

        """
            Load and initialize class attributes from a configuration JSON file.

            This method reads a JSON file specified by the path `strConfigFilePath`, extracts various settings,
            and assigns them to class-level attributes. It also logs the process and handles errors related to
            file reading, JSON parsing, and missing keys.

            Parameters:
                strConfigFilePath (str): Path to the configuration JSON file.
                iUserId (int): The user ID for logging and tracking purposes.
                boolTDSApplied (bool, optional): Indicates whether TDS is applied. Defaults to False.
                strCategory (str, optional): Optional category tag to be assigned. Defaults to None.
                strCostCenterLocation (str, optional): Optional cost center location. Defaults to None.
            
            Returns: None
           
        """

        try:
            cls.MCResetAttributes()
            CParagJournal._mIUserId = iUserId
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Info","Loading Config file and Initializing attributes")
            # Open and load the JSON file
            with open(strConfigFilePath, 'r') as file:
                data = json.load(file)
            
            # Set the ledger name from DebitLedger.PurchaseLedgerName
            cls._mStrLedgerName = data["VendorName"]

            # NOTE: Debit Ledger is entered manually as we just required Ledger Name

            cls._mstrDebitLedgerName = data["DebitLedger"]["PurchaseLedgerName"]
            # Extract CompanyData for easier access
            company_data = data["CompanyData"]
            # Update the company data dictionary
            cls._mDictCompanyData["company_name"] = company_data["ComapanyName"]
            cls._mDictCompanyData["gst_registration_type"] = company_data["GstRegistrationType"]
            cls._mDictCompanyData["state_name"] = company_data["StateName"]
            cls._mDictCompanyData["country_name"] = company_data["CountryName"]
            cls._mDictCompanyData["gst_in"] = company_data["GstIn"]
            
            # Set other string attributes
            cls._mstrVoucherTypeName = data["VoucherTypeName"]
            cls._mstrVocuherEntryMode = data["VCHEntryMode"]
            cls._mstrRoundOffLedger = data["RoundOffLedger"]
            cls._mboolTDSApplied = boolTDSApplied
            cls._mstrCategory = strCategory
            cls._mstrCostCenterLocation = strCostCenterLocation
        except FileNotFoundError:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Error",f"The file '{strConfigFilePath}' was not found.")
            raise Exception(f"The file '{strConfigFilePath}' was not found.")
        except json.JSONDecodeError as e:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Error",f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
            raise Exception(f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
        except KeyError as e:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            raise Exception(f"Missing required field in JSON: {str(e)}")


    @staticmethod
    async def MGetNarration(invoice_no: str, todays_date: str) -> str:
        """
            Generate a formatted narration string using the invoice number and date.

            Extracts the numeric invoice portion from a formatted invoice string (e.g., "TW-1140/2024-25")
            and combines it with the provided date to produce a standardized narration for record keeping.

            Args:
                invoice_no (str): Invoice number string (e.g., "TW-1140/2024-25").
                todays_date (str): Date string in 'YYYYMMDD' format (e.g., "20250404").

            Returns:
                str: Narration string in the format:
        """
        try:
            return 'Data Entered By Accuvelocity'
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            raise 


    @staticmethod
    async def MSSelectPartyLedgerName():
       
        """
             Asynchronously retrieves the party ledger name for the current vendor.

            This method fetches the debit ledger name (usually set during configuration) from the class-level attribute
            and returns it. It also logs the retrieval action for auditing or debugging purposes.

            Returns:
                str: The debit ledger name associated with the current vendor.
        """
       
        try:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Info","Getting Debit Ledger")
            strPartyName = CParagJournal._mstrDebitLedgerName #Changes Per Vendor
            return strPartyName
        except Exception as objException:
           await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
           raise
    
    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData: dict) -> list:
        """
            Generates credit ledger entries based on the extracted data.

            If TDS is not applicable, a single credit ledger entry is created for the full amount.
            If TDS is applicable, the total is split into a main credit entry and a separate TDS entry.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                list: A list of one or more ledger entry dictionaries.
        """
       
        """
        Generates credit ledger entries.
        If TDS is applicable (via an external function), add a TDS entry.
        """
        try:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Info","Getting Credit Ledger")
            if not CParagJournal._mboolTDSApplied:
                credit_ledgers = []
                fTotalAmount = 0
                fTotalAmount = round(dictExtractedData.get("TotalAmount", 0) + dictExtractedData.get("UsbDrive", 0))
                main_credit = {
                    "ledger_name": CParagJournal._mStrLedgerName,  # Fixed for Adani Hazira
                    "amount": fTotalAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }
                credit_ledgers.append(main_credit)
                return main_credit
            else:
                credit_ledgers = []
                fTotalAmount = 0
                fTotalAmount = round(dictExtractedData.get("TotalAmount", 0) + dictExtractedData.get("UsbDrive", 0))
                fTDSAmount = math.ceil(fTotalAmount * 0.01)
                fAmount = fTotalAmount - fTDSAmount

                main_credit = {
                    "ledger_name": CParagJournal._mStrLedgerName,  # Fixed for Adani Hazira
                    "amount": fAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }

                tds_entry = {
                    "ledger_name": "Tds on Contractors",  # Fixed for Adani Hazira
                    "amount": fTDSAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }

                return [tds_entry, main_credit]
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            raise
    
    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates a debit ledger entry based on the extracted data and cost center settings.

            If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """

        try:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Info","Getting Debit Ledger")
            debit_ledger = []
            fTotalAmount = 0
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0) + dictExtractedData.get("UsbDrive", 0))
            if CParagJournal._mstrCostCenterLocation == None and CParagJournal._mstrCategory == None:
                ledger = {
                # "ledger_name": "J School Function Expenses",
                "ledger_name": await CParagJournal.MSSelectPartyLedgerName(),
                "amount": -fTotalAmount,
                "is_deemed_positive": True,
                "is_party_ledger": False,
            }
                debit_ledger.append(ledger)
                
            else:
                cost_center_allocations = []
                cost_center_allocations.append({"name": CParagJournal._mstrCostCenterLocation, "amount": -fTotalAmount})
                ledger = {
                "ledger_name":await CParagJournal.MSSelectPartyLedgerName(),
                "amount":-fTotalAmount,
                "is_deemed_positive": True,  # Debit entry
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": CParagJournal._mstrCategory,
                "cost_center_allocations": cost_center_allocations,
            }
            return ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId,"Error",objException)
            raise objException

    @staticmethod
    async def MSCleanTallySXML(xml_str: str) -> str:
    # Parse the XML string
        try:
            root = ET.fromstring(xml_str)

            # Define tags to remove (with or without namespaces)
            tags_to_remove = [
                "GSTOVRDNTAXABILITY",
                "GSTOVRDNTYPEOFSUPPLY"
            ]

            # Remove all matching tags in the tree
            for tag in tags_to_remove:
                for elem in root.findall(f".//{tag}"):
                    parent = root.find(f".//{tag}/..")
                    if parent is not None:
                        parent.remove(elem)
       
            cleaned_xml = ET.tostring(root, encoding='unicode')
            return cleaned_xml
        except Exception as e:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", e)
            raise
    
    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):

        try:
            dictXMLResponse = {}
            CParagJournal.MCResetAttributes()
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Info", "Started Creating XML")
            await CParagJournal.MCSetAttributes(strConfigPath, iUserId)

            ledger_entries = []
            credit_ledger = await CParagJournal.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CParagJournal.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            ledger_entries.append(LedgerEntrySchema(**debit_ledger))
            
            if CParagJournal._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            

            invoice_date = dictExtractedData.get("InvoiceDate") if dictExtractedData.get("InvoiceDate") else datetime.now().strftime("%Y%m%d")
            invoice_no = dictExtractedData.get("InvoiceNo", "") or str(uuid.uuid4().int)[:4]

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CParagJournal._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CParagJournal.MGetNarration(str(invoice_no), invoice_date),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="AV Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CParagJournal._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CParagJournal._msTallyStatus
            dictXMLResponse["AVComments"] = CParagJournal._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CParagJournal._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            CParagJournal._msTallyStatus = "Skipped"
            CParagJournal._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CParagJournal._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CParagJournal._msTallyStatus
            dictXMLResponse["AVComments"] = CParagJournal._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CParagJournal._mStrTracebackLogs
            return dictXMLResponse


class CAmritTripeMakers(CParagJournal):
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""


    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CAmritTripeMakers.reset()
            dictXMLResponse =await CParagJournal.MSCreateXML(strConfigPath, iUserId, dictExtractedData, lsUdfData)
            return dictXMLResponse
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            CAmritTripeMakers._msTallyStatus = "Skipped"
            CAmritTripeMakers._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CAmritTripeMakers._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CAmritTripeMakers._msTallyStatus
            dictXMLResponse["AVComments"] = CAmritTripeMakers._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CAmritTripeMakers._mStrTracebackLogs
            return dictXMLResponse

class CSupremeAutoCentre(CParagJournal):
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""


    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CSupremeAutoCentre.reset()
            dictXMLResponse =await CParagJournal.MSCreateXML(strConfigPath, iUserId, dictExtractedData, lsUdfData)
            return dictXMLResponse
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            CSupremeAutoCentre._msTallyStatus = "Skipped"
            CSupremeAutoCentre._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CSupremeAutoCentre._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CSupremeAutoCentre._msTallyStatus
            dictXMLResponse["AVComments"] = CSupremeAutoCentre._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CSupremeAutoCentre._mStrTracebackLogs
            return dictXMLResponse

class CKamalKishore(CParagJournal):
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""


    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CKamalKishore.reset()
            dictXMLResponse =await CParagJournal.MSCreateXML(strConfigPath, iUserId, dictExtractedData, lsUdfData)
            return dictXMLResponse
        except Exception as objException:
            await CLogController.MSWriteLog(CParagJournal._mIUserId, "Error", objException)
            CKamalKishore._msTallyStatus = "Skipped"
            CKamalKishore._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CKamalKishore._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CKamalKishore._msTallyStatus
            dictXMLResponse["AVComments"] = CKamalKishore._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CKamalKishore._mStrTracebackLogs
            return dictXMLResponse


async def main():
    api_response_dir=r"D:\Nisarg\IndianInvoice\data\apiResponses\Supreme Auto Centre"
    output_dir=r"GitIgnore\\Supreme Auto Centre"
    strConfigFilePath = r"Data\Customer\17_ParagTraders\33_Supreme Auto Centre\Supreme Auto Centre_Config.json"
    i = 0
    for filename in os.listdir(api_response_dir):
        try:
            if filename.endswith("_gptResponse.json"):
                #
                json_file_path = os.path.join(api_response_dir, filename)
                
            
                with open(json_file_path, "r") as file:
                    api_response = json.load(file) 
            
                content = api_response['choices'][0]['message']['content']
                content = json.loads(content)
                # CParagJournal.MCSetAttributes(strConfigFilePath, 4)
                XmlOutput = await CSupremeAutoCentre.MSCreateXML(strConfigFilePath, 4, content)
                
            
                invoice_no = content.get("InvoiceNo", i)
                invoice_no = invoice_no.replace("/", "_")
                i += 1
                os.makedirs(output_dir, exist_ok=True)
                
                xml_file_name = f"Invoiceno_{i}_xmlfile.xml"
                xml_file_path = os.path.join(output_dir, xml_file_name)
                
        
                with open(xml_file_path, "w") as xml_file:
                    xml_file.write(XmlOutput.get("xmlContent"))
        except Exception as e:
            print("Error Occur - ", traceback.format_exc())        

if __name__ == "__main__":
    asyncio.run(main())