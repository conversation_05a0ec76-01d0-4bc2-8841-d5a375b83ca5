import sys
sys.path.append(".")
import asyncio
from config.constants import Constants
import os
from sqlalchemy.future import select
from sqlalchemy.sql import and_
from fastapi import HTTPException
from datetime import datetime
import pytz
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func
from fastapi_mail import FastMail, MessageSchema

# Ensure you have your database session and models imported
from config.db_config import AsyncSessionLocal
from src.Models.models import UploadedDoc, StatusEnum, User, Logs
from src.utilities.email_utils import CResetPassword

class StatisticsController:

    @staticmethod
    async def MSGetDailyStatistics(date_str: str = None):
        """
        Retrieves daily statistics including the total number of users created today and their regions,
        total Lite pages extracted today, total Pro pages extracted today, and logins by country.
        """
        try:
            async with AsyncSessionLocal() as db:
                # Parse the date string if provided
                if date_str:
                    target_date = datetime.strptime(date_str, "%d-%m-%Y").date()
                else:
                    target_date = datetime.now(pytz.timezone('Asia/Kolkata')).date()

                date_start = datetime.combine(target_date, datetime.min.time())
                date_end = datetime.combine(target_date, datetime.max.time())

                # Query to count users created today
                total_users_today_query = select(func.count(User.uid)).where(
                    func.date(User.created_at) == target_date
                )
                total_users_today = await db.scalar(total_users_today_query)

                # Query to get regions of users created today
                regions_today_query = select(User.Country, func.count(User.uid)).where(
                    func.date(User.created_at) == target_date
                ).group_by(User.Country)
                regions_today_result = await db.execute(regions_today_query)
                regions_today = regions_today_result.all()

                # Query for Pro pages
                pro_pages_query = select(func.sum(UploadedDoc.PageCount)).where(
                    and_(
                        UploadedDoc.ModifiedDateTime >= date_start,
                        UploadedDoc.ModifiedDateTime <= date_end,
                        UploadedDoc.Status != StatusEnum.NotProcess,
                        UploadedDoc.Status != StatusEnum.Error,
                        UploadedDoc.Status != StatusEnum.Processing,
                        UploadedDoc.UsedPaidModel == True
                    )
                )
                pro_pages_result = await db.scalar(pro_pages_query)
                pro_pages_count = pro_pages_result if pro_pages_result else 0

                # Query for Lite pages
                lite_pages_query = select(func.sum(UploadedDoc.PageCount)).where(
                    and_(
                        UploadedDoc.ModifiedDateTime >= date_start,
                        UploadedDoc.ModifiedDateTime <= date_end,
                        UploadedDoc.Status != StatusEnum.NotProcess,
                        UploadedDoc.Status != StatusEnum.Error,
                        UploadedDoc.Status != StatusEnum.Processing,
                        UploadedDoc.UsedPaidModel == False
                    )
                )
                lite_pages_result = await db.scalar(lite_pages_query)
                lite_pages_count = lite_pages_result if lite_pages_result else 0

                # Query to count logins by country
                login_count_query = (
                    select(User.Country, func.count(Logs.Id))
                    .join(User, Logs.UserID == User.uid)
                    .where(
                        and_(
                            Logs.LogMessage == 'Successfully Logged in.',
                            Logs.CreatedDateTime >= date_start,
                            Logs.CreatedDateTime <= date_end
                        )
                    )
                    .group_by(User.Country)
                )
                login_counts_by_country_result = await db.execute(login_count_query)
                login_counts_by_country = login_counts_by_country_result.all()
                
                # Query to count total logins
                total_logins_query = (
                    select(func.count(Logs.Id))
                    .where(
                        and_(
                            Logs.LogMessage == 'Successfully Logged in.',
                            Logs.CreatedDateTime >= date_start,
                            Logs.CreatedDateTime <= date_end
                        )
                    )
                )
                total_logins_result = await db.scalar(total_logins_query)
                total_logins_count = total_logins_result if total_logins_result else 0

                # Compile all statistics
                daily_statistics = {
                    "date": target_date,
                    "totalUsersCreated": total_users_today,
                    "totalRegionsToday": [{"region": region, "count": count} for region, count in regions_today],
                    "totalProPagesExtracted": pro_pages_count,
                    "totalLitePagesExtracted": lite_pages_count,
                    "totalLoginsByCountry": [{"country": country, "count": count} for country, count in login_counts_by_country],
                    "totalLogins": total_logins_count
                }
                print(daily_statistics)
                return daily_statistics
        except SQLAlchemyError as e:
            raise HTTPException(
                status_code=500, detail="A database error prevented retrieving daily statistics."
            )
        except Exception as e:
            import traceback
            print(traceback.print_exc())
            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request."
            )

    @staticmethod
    async def MSSendDailyStatistics():
        try:
            dictDailyStatistics = await StatisticsController.MSGetDailyStatistics()
            strEmailBody = CResetPassword.strDailyUsageStatistics.format(dailyUsers=dictDailyStatistics.get("totalLogins"), 
                                                                            CompanyName = os.getenv('MAIL_FROM_NAME'),
                                                                            newUsers=dictDailyStatistics.get("totalUsersCreated"),
                                                                            litePagesProcessed = dictDailyStatistics.get("totalLitePagesExtracted"),
                                                                            proPagesProcessed = dictDailyStatistics.get("totalProPagesExtracted")
                                                                            )

            objMessage = MessageSchema(
                subject="Accuvelocity Daily Statistics",
                recipients=Constants.lsDailyStatisticsList,
                body=strEmailBody,
                subtype="html"
            )

            fm = FastMail(CResetPassword.mail_conf)
            await fm.send_message(message=objMessage)
            
        except Exception as e:
            import traceback
            print(traceback.print_exc())
            print("Failed to send daily statistics to developers.")
            
# Entry point of the script
if __name__ == "__main__":
    asyncio.run(StatisticsController.MSSendDailyStatistics())
