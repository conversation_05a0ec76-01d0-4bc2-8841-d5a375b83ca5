import axios from 'axios';
import { toast } from 'react-hot-toast';

const fetchData = async (userId) => {
    try {
        const response = await axios.get(`${import.meta.env.VITE_SERVER}/users/${userId}`, {
            headers: {
                "Authorization": `Bearer ${localStorage.getItem('token')}`,
            },
        });
        return response.data;
    } catch (error) {
        if (error.response && error.response.data && error.response.data.detail) {
            toast.error(error.response.data.detail);
        } else {
            toast.error("An unexpected error occurred");
        }
        console.error('Error fetching user data:', error);
        throw error;
    }
};

export default fetchData;
