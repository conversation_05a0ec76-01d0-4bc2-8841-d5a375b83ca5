import sys
import os
import json
from datetime import datetime
from PyQt5.QtWidgets import (
    QApplication, QMainWindow, QWidget, QVBoxLayout, QHBoxLayout, QTableWidget,
    QTableWidgetItem, QPushButton, QComboBox, QLineEdit, QDateEdit, QLabel,
    QMessageBox, QFileDialog, QStatusBar, QCheckBox
)
from PyQt5.QtCore import Qt, QDate
import requests
from pathlib import Path

class VoucherType:
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"

VOUCHER_TYPES = [
    "",  # Empty option for no filter
    VoucherType.PV_WITH_INVENTORY,
    VoucherType.PV_WITHOUT_INVENTORY,
    VoucherType.DELIVERY_NOTE,
    VoucherType.JOURNAL_VOUCHER,
    VoucherType.BANK_STATEMENT,
    VoucherType.RECEIPT_NOTE,
    VoucherType.PURCHASE_ORDER
]

class AVXMLGeneratedStatus:
    Success = "Success"
    Skipped = "Skipped"
    PartialSuccess = "PartialSuccess"
    Duplicate = "Duplicate"
    NOT_APPLICABLE = "NOT_APPLICABLE"
    ValidationError = "ValidationError"

STATUS_TYPES = [
    "",  # Empty option for no filter
    AVXMLGeneratedStatus.Success,
    AVXMLGeneratedStatus.Skipped,
    AVXMLGeneratedStatus.PartialSuccess,
    AVXMLGeneratedStatus.Duplicate,
    AVXMLGeneratedStatus.NOT_APPLICABLE,
    AVXMLGeneratedStatus.ValidationError
]

class TallyXMLImportStatus:
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

TALLY_STATUS_TYPES = [
    "",  # Empty option for no filter
    TallyXMLImportStatus.SUCCESS,
    TallyXMLImportStatus.FAILED
]

PAGE_SIZES = [10, 20, 50, 100]

class DocumentHistoryApp(QMainWindow):
    def __init__(self):
        super().__init__()
        self.setWindowTitle("Document History")
        self.setGeometry(100, 100, 1200, 600)
        self.user_id = 4  # Hardcoded for testing; replace with authentication
        self.api_base_url = "http://localhost:8000"
        self.current_page = 1
        self.page_size = 20
        self.total_pages = 1
        self.init_ui()

    def init_ui(self):
        central_widget = QWidget()
        self.setCentralWidget(central_widget)
        main_layout = QVBoxLayout(central_widget)

        # Filter Section
        filter_layout = QHBoxLayout()
        self.start_date = QDateEdit()
        self.start_date.setCalendarPopup(True)
        self.start_date.setDate(QDate.currentDate().addDays(-30))
        filter_layout.addWidget(QLabel("Start Date:"))
        filter_layout.addWidget(self.start_date)

        self.end_date = QDateEdit()
        self.end_date.setCalendarPopup(True)
        self.end_date.setDate(QDate.currentDate())
        filter_layout.addWidget(QLabel("End Date:"))
        filter_layout.addWidget(self.end_date)

        self.voucher_type = QComboBox()
        self.voucher_type.addItems(VOUCHER_TYPES)
        filter_layout.addWidget(QLabel("Voucher Type:"))
        filter_layout.addWidget(self.voucher_type)

        self.status = QComboBox()
        self.status.addItems(STATUS_TYPES)
        filter_layout.addWidget(QLabel("XML Status:"))
        filter_layout.addWidget(self.status)

        self.tally_status = QComboBox()
        self.tally_status.addItems(TALLY_STATUS_TYPES)
        filter_layout.addWidget(QLabel("Tally Status:"))
        filter_layout.addWidget(self.tally_status)

        self.document_name = QLineEdit()
        self.document_name.setPlaceholderText("Enter document name...")
        filter_layout.addWidget(QLabel("Document Name:"))
        filter_layout.addWidget(self.document_name)

        apply_filter_btn = QPushButton("Apply Filters")
        apply_filter_btn.clicked.connect(self.load_data)
        filter_layout.addWidget(apply_filter_btn)

        main_layout.addLayout(filter_layout)

        # Page Size Selection
        page_size_layout = QHBoxLayout()
        self.page_size_combo = QComboBox()
        self.page_size_combo.addItems([str(size) for size in PAGE_SIZES])
        self.page_size_combo.setCurrentText(str(self.page_size))
        self.page_size_combo.currentTextChanged.connect(self.change_page_size)
        page_size_layout.addWidget(QLabel("Records per Page:"))
        page_size_layout.addWidget(self.page_size_combo)
        page_size_layout.addStretch()
        main_layout.addLayout(page_size_layout)

        # Table
        self.table = QTableWidget()
        self.table.setColumnCount(11)
        self.table.setHorizontalHeaderLabels([
            "Select", "ID", "Voucher Type", "XML Status", "Tally Status",
            "Document Name", "Document Type", "Generated At",
            "Customer Name", "User Name", "Has XML"
        ])
        self.table.setEditTriggers(QTableWidget.NoEditTriggers)
        main_layout.addWidget(self.table)

        # Pagination
        pagination_layout = QHBoxLayout()
        self.prev_btn = QPushButton("Previous")
        self.prev_btn.clicked.connect(self.prev_page)
        pagination_layout.addWidget(self.prev_btn)

        self.page_input = QLineEdit("1")
        self.page_input.setFixedWidth(50)
        self.page_input.returnPressed.connect(self.goto_page)
        pagination_layout.addWidget(QLabel("Page:"))
        pagination_layout.addWidget(self.page_input)

        self.page_label = QLabel("of 1")
        pagination_layout.addWidget(self.page_label)

        self.next_btn = QPushButton("Next")
        self.next_btn.clicked.connect(self.next_page)
        pagination_layout.addWidget(self.next_btn)

        main_layout.addLayout(pagination_layout)

        # Download Buttons
        download_layout = QHBoxLayout()
        self.download_selected_btn = QPushButton("Download Selected XMLs")
        self.download_selected_btn.clicked.connect(self.download_selected)
        download_layout.addWidget(self.download_selected_btn)

        self.download_single_btn = QPushButton("Download Single XML")
        self.download_single_btn.clicked.connect(self.download_single)
        download_layout.addWidget(self.download_single_btn)

        main_layout.addLayout(download_layout)

        # Status Bar
        self.status_bar = QStatusBar()
        self.setStatusBar(self.status_bar)
        self.status_bar.showMessage("Ready")

        # Load initial data
        self.load_data()

    def load_data(self):
        try:
            filters = {}
            if self.start_date.date().toPyDate() != QDate.currentDate().addDays(-30).toPyDate():
                filters["start_date"] = self.start_date.date().toString("yyyy-MM-dd")
            if self.end_date.date().toPyDate() != QDate.currentDate().toPyDate():
                filters["end_date"] = self.end_date.date().toString("yyyy-MM-dd")
            if self.voucher_type.currentText():
                filters["voucher_type"] = self.voucher_type.currentText()
            if self.status.currentText():
                filters["status"] = self.status.currentText()
            if self.tally_status.currentText():
                filters["tally_status"] = self.tally_status.currentText()
            if self.document_name.text():
                filters["document_name"] = self.document_name.text()

            response = requests.post(
                f"{self.api_base_url}/api/documents/history",
                params={"user_id": self.user_id, "page": self.current_page, "page_size": self.page_size},
                json=filters
            )
            if response.status_code == 403:
                QMessageBox.critical(self, "Error", "This feature is not available for your account.")
                self.status_bar.showMessage("Access denied")
                return
            if response.status_code == 500:
                QMessageBox.critical(self, "Error", "Internal server error. Please check logs.")
                self.status_bar.showMessage("Server error")
                return
            response.raise_for_status()
            data = response.json()

            self.total_pages = data["total_pages"]
            self.page_label.setText(f"of {self.total_pages}")
            self.page_input.setText(str(self.current_page))
            self.prev_btn.setEnabled(self.current_page > 1)
            self.next_btn.setEnabled(self.current_page < self.total_pages)

            self.table.setRowCount(len(data["documents"]))
            for row, doc in enumerate(data["documents"]):
                # Checkbox column
                checkbox = QCheckBox()
                self.table.setCellWidget(row, 0, checkbox)

                self.table.setItem(row, 1, QTableWidgetItem(str(doc["ID"])))
                self.table.setItem(row, 2, QTableWidgetItem(doc["strVoucherType"]))
                self.table.setItem(row, 3, QTableWidgetItem(doc["AVDocumentPtocessedStatus"]))
                self.table.setItem(row, 4, QTableWidgetItem(doc["TallyXMLImportStatus"]))
                self.table.setItem(row, 5, QTableWidgetItem(doc["ReqDocName"]))
                self.table.setItem(row, 6, QTableWidgetItem(doc["ReqDocType"]))
                self.table.setItem(row, 7, QTableWidgetItem(doc["CReqGeneratedTimeAt"]))
                self.table.setItem(row, 8, QTableWidgetItem(doc["strCustomerName"]))
                self.table.setItem(row, 9, QTableWidgetItem(doc["UserName"]))
                self.table.setItem(row, 10, QTableWidgetItem("Yes" if doc["has_xml"] else "No"))

            self.table.resizeColumnsToContents()
            self.status_bar.showMessage(f"Loaded {len(data['documents'])} records")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "Error", f"Failed to load data: {str(e)}")
            self.status_bar.showMessage("Error loading data")

    def change_page_size(self, size: str):
        self.page_size = int(size)
        self.current_page = 1
        self.load_data()

    def prev_page(self):
        if self.current_page > 1:
            self.current_page -= 1
            self.load_data()

    def next_page(self):
        if self.current_page < self.total_pages:
            self.current_page += 1
            self.load_data()

    def goto_page(self):
        try:
            page = int(self.page_input.text())
            if 1 <= page <= self.total_pages:
                self.current_page = page
                self.load_data()
            else:
                QMessageBox.warning(self, "Invalid Page", f"Page must be between 1 and {self.total_pages}")
                self.page_input.setText(str(self.current_page))
        except ValueError:
            QMessageBox.warning(self, "Invalid Page", "Please enter a valid number")
            self.page_input.setText(str(self.current_page))

    def download_selected(self):
        request_ids = []
        for row in range(self.table.rowCount()):
            checkbox = self.table.cellWidget(row, 0)
            if checkbox and checkbox.isChecked():
                request_ids.append(int(self.table.item(row, 1).text()))

        if not request_ids:
            QMessageBox.warning(self, "No Selection", "Please select at least one document")
            return

        try:
            desktop = Path.home() / "Desktop"
            save_path, _ = QFileDialog.getSaveFileName(self, "Save ZIP File", str(desktop / f"documents_{self.user_id}.zip"), "ZIP Files (*.zip)")
            if not save_path:
                return

            response = requests.post(
                f"{self.api_base_url}/api/documents/download",
                params={"user_id": self.user_id},
                json=request_ids
            )
            if response.status_code == 403:
                QMessageBox.critical(self, "Error", "This feature is not available for your account.")
                self.status_bar.showMessage("Access denied")
                return
            if response.status_code == 404:
                QMessageBox.warning(self, "No XMLs", "No valid XMLs found for selected documents")
                self.status_bar.showMessage("No XMLs found")
                return
            if response.status_code == 500:
                QMessageBox.critical(self, "Error", "Internal server error. Please check logs.")
                self.status_bar.showMessage("Server error")
                return
            response.raise_for_status()

            with open(save_path, "wb") as f:
                f.write(response.content)
            QMessageBox.information(self, "Success", f"ZIP file saved to {save_path}")
            self.status_bar.showMessage("Download completed")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "Error", f"Failed to download ZIP: {str(e)}")
            self.status_bar.showMessage("Download failed")

    def download_single(self):
        selected_rows = [row for row in range(self.table.rowCount()) if self.table.cellWidget(row, 0).isChecked()]
        if len(selected_rows) != 1:
            QMessageBox.warning(self, "Invalid Selection", "Please select exactly one document")
            return

        row = selected_rows[0]
        doc_id = self.table.item(row, 1).text()
        has_xml = self.table.item(row, 10).text() == "Yes"
        if not has_xml:
            QMessageBox.warning(self, "No XML", "Selected document has no XML content")
            return

        try:
            desktop = Path.home() / "Desktop"
            save_path, _ = QFileDialog.getSaveFileName(self, "Save XML File", str(desktop / f"AVRequestDetail_{doc_id}.xml"), "XML Files (*.xml)")
            if not save_path:
                return

            response = requests.get(
                f"{self.api_base_url}/api/documents/download/single",
                params={"user_id": self.user_id, "doc_type": "Request", "doc_id": doc_id}
            )
            if response.status_code == 403:
                QMessageBox.critical(self, "Error", "This feature is not available for your account.")
                self.status_bar.showMessage("Access denied")
                return
            if response.status_code == 404:
                QMessageBox.warning(self, "No XML", "XML not found for this document")
                self.status_bar.showMessage("XML not found")
                return
            if response.status_code == 500:
                QMessageBox.critical(self, "Error", "Internal server error. Please check logs.")
                self.status_bar.showMessage("Server error")
                return
            response.raise_for_status()

            with open(save_path, "wb") as f:
                f.write(response.content)
            QMessageBox.information(self, "Success", f"XML file saved to {save_path}")
            self.status_bar.showMessage("Download completed")
        except requests.exceptions.RequestException as e:
            QMessageBox.critical(self, "Error", f"Failed to download XML: {str(e)}")
            self.status_bar.showMessage("Download failed")

if __name__ == "__main__":
    app = QApplication(sys.argv)
    window = DocumentHistoryApp()
    window.show()
    sys.exit(app.exec_())