import { useState, useEffect } from 'react';
import { useNavigate } from 'react-router-dom';
import axios from 'axios';
import { jwtDecode } from "jwt-decode";
import toast, { Toaster } from 'react-hot-toast';
import ProfileBanner from '../assets/ProfileBanner.svg'
import { MdLockOpen } from "react-icons/md"; // Assuming you have these icons
import { FaEdit, FaSave, FaTimes } from "react-icons/fa";
import countriesData from '../assets/countries.json';
import profilePic from '../assets/defaultProfilePicture.jpg'
import PhoneInput from 'react-phone-number-input'
import { library } from '@fortawesome/fontawesome-svg-core';
import { faShareAlt } from '@fortawesome/free-solid-svg-icons';
import { Tooltip } from '@material-tailwind/react';
import { BiMoneyWithdraw } from "react-icons/bi";
import { MdOutlineRequestPage } from "react-icons/md";
import { useRecoilState } from 'recoil';
import { isActivePaidPlanAtom, ActivePlanNameAtom, ActivePlanTypeAtom } from '../context/TrailUsageUserData'
import { CiCircleInfo } from "react-icons/ci";
import { useUserNameSetter } from "../context/userData";
import { BsThreeDots } from "react-icons/bs";
// Add the icon to the library
library.add(faShareAlt);
import * as Yup from 'yup';
import fetchData from '../utils/fetchData';

export default function Profile() {
    const [userData, setUserData] = useState(null);
    const [name, setName] = useState('');
    const [PhoneNo, setPhoneNo] = useState(null);
    const [Email, setEmail] = useState('');
    const [Country, setCountry] = useState('');
    const [editMode, setEditMode] = useState(false);
    const [promoCodeHistory, setPromoCodeHistory] = useState([]);
    const [promoCode, setPromoCode] = useState('');
    const [Organization, setOrganization] = useState('');
    const [selectedFile, setSelectedFile] = useState(null);
    const [totalModelsCreated] = useState(2);
    const [countries, setCountries] = useState(countriesData.countries); // Load country data into state
    const [profilePicture, setProfilePicture] = useState(profilePic);
    const [currentPlan, setCurrentPlan] = useState("Free");
    const navigate = useNavigate();
    const [activePlanName, setActivePlanName] = useRecoilState(ActivePlanNameAtom);
    const [IsActivePaidPlan, setActivePaidPlan] = useRecoilState(isActivePaidPlanAtom);
    const [isPromoCodesExpanded, setIsPromoCodesExpanded] = useState(false);

    const [ActivePlanType, setActivePlanType] = useRecoilState(ActivePlanTypeAtom);
    const [currentToastId, setCurrentToastId] = useState(null);
    const [currentMBToastId, setCurrentMBToastId] = useState(null);
    const setUserName = useUserNameSetter()
    const handleProfilePictureChange = (event) => {
        const file = event.target.files[0];
        if (file) {
            // Create a URL for the selected file
            const newProfilePictureUrl = URL.createObjectURL(file);
            // Update the profile picture state
            setProfilePicture(newProfilePictureUrl);
            setSelectedFile(event.target.files[0])
        }
    };
    const validationSchema = Yup.object().shape({
        phoneNumber: Yup.string()
            .nullable() // Allow null or undefined values
            .matches(
                /^(\+\d{1,3}\s?)?1?\-?\.?\s?\(?\d{3}\)?[\s.-]?\d{3}[\s.-]?\d{4}$/,
                'Phone number is not valid'
            ), // Check if the format is valid if provided
    });
    const cleanPhoneNumber = (phoneNumber) => {
        // Remove all characters except digits and the plus symbol
        if (phoneNumber && phoneNumber != undefined && phoneNumber != null) {
            return phoneNumber.replace(/[^+\d]/g, '');
        }
        return undefined
    };

    function formatDate(date) {
        const months = [
            'January', 'February', 'March', 'April', 'May', 'June',
            'July', 'August', 'September', 'October', 'November', 'December'
        ];
        const day = date.getDate();
        const monthIndex = date.getMonth();
        const year = date.getFullYear();
        return `${day} ${months[monthIndex]}, ${year}`;
    }

    const decodedToken = jwtDecode(localStorage.getItem('token'));
    const userId = decodedToken.id;
    useEffect(() => {
        fetchUserData();
    }, [userId]);

    const toggleExpand = () => {
        setIsPromoCodesExpanded(!isPromoCodesExpanded);
    };

    const formatTo12Hour = (isoString) => {
        const date = new Date(isoString);

        const options = {
            year: 'numeric',
            month: '2-digit',
            day: '2-digit',
            hour: '2-digit',
            minute: '2-digit',
            hour12: true,
        };

        return date.toLocaleString('en-US', options);
    };

    const fetchUserData = async () => {
        try {
            const user =  await fetchData(userId)
            const plan_active_status = user.plan_active_status;
            const active_plan = user.active_plan;
            setActivePlanName(active_plan)
            setActivePaidPlan(plan_active_status)
            setActivePlanType(user.active_plan_type)
            setCurrentPlan(active_plan)
            setUserData(user);
            setName(user.name)
            setEmail(user.email)
            const userName = user.name;
            setUserName(userName);
            setPromoCodeHistory(user.promoCodeHistory);
            setPromoCode(user.promoCodeHistory ? user.promoCodeHistory[0]?.promo_code_name : '');
            setPhoneNo(user.phoneNumber && user.phoneNumber !== "undefined" ? user.phoneNumber : '');
            setCountry(user.Country && user.Country.trim() !== "" && user.Country !== "null" ? user.Country : "Select a country");
            setProfilePicture(user.profilePicture ? `data:image/jpeg;base64,${user.profilePicture}` : profilePic);

        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error fetching user data:', error);
        }
    };

    const updateUser = async (e) => {
        e.preventDefault();
        try {
            let cleanedPhoneNo = cleanPhoneNumber(PhoneNo);
            if (cleanedPhoneNo === "No Number Added") {
                cleanedPhoneNo = '';
                setPhoneNo('')
            }
            // Validate the phone number to ensure it's not empty once added
            await validationSchema.validate({
                phoneNumber: cleanedPhoneNo ? cleanedPhoneNo : null,
            });
            // Ensure the name is not empty
            if (!name.trim()) {
                throw new Error('Name cannot be empty');
            }
        } catch (error) {
            toast.error(error.message); // Display user-friendly error message
            return; // Stop further processing if validation fails
        }

        try {
            const formData = new FormData()
            formData.append("email", Email)
            formData.append("name", name)
            if (promoCode != undefined && promoCode != null && promoCode != "" && promoCode != promoCodeHistory[0]?.promo_code_name) {
                formData.append("promoCodeStr", promoCode)
            }
            if (Country && Country !== "" && Country !== "Select a country") {
                formData.append("Country", Country)
            }
            // Only append phone number if it's not null or empty
            let cleanedPhoneNo = cleanPhoneNumber(PhoneNo);
            formData.append('phoneNumber', cleanedPhoneNo);


            if (selectedFile) {
                formData.append("profilePicture", selectedFile);
            }
            // Send the PUT request
            await axios.put(
                `${import.meta.env.VITE_SERVER}/update/user/${userId}`,
                formData,
                {
                    headers: {
                        Authorization: `Bearer ${localStorage.getItem('token')}`,
                    },
                }
            );
            // console.log('User profile updated successfully!');
            toast.success('User profile updated successfully!');
            setTimeout(() => {
                // navigate("/")
                window.location.href = '/profile';
            }, 1000)
        } catch (error) {
            if (error.message === 'Phone number cannot be empty once added') {
                toast.error(error.message); // Specific error handling for phone number
            } else if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error("Failed to update user details :", error);
        }
    };

    const handleCancel = () => {
        setEditMode(false);
        fetchUserData();   // Added This so no reload of page happens but still data gets refreshed
    };

    const profilePictureStyles = {
        position: 'absolute',
        top: '50%', // Centers the top edge of the picture in the middle of the banner
        left: '50%', // Centers the left edge of the picture in the middle of the banner
        transform: 'translate(-50%, -50%)', // Adjusts the positioning to center the picture itself
        width: '100px',
        height: '100px',
        borderRadius: '50%',
        border: '5px solid white', // Increased border width for better visibility
        backgroundColor: '#fff', // Ensures that the border area is solid white
        padding: '2px', // Adds space between the image and the border
        boxSizing: 'content-box' // Ensures padding does not affect the overall dimensions
    };

    // Add custom styles for both cards to ensure they have the same height
    const cardStyles = {
        display: 'flex',
        flexDirection: 'column',
        justifyContent: 'space-between' // This ensures that footer of the card is pushed to the bottom
        , // Set the height of the card to be 100% of its parent

    };

    if (!userData) {
        return <div className="flex justify-center items-center h-screen">Loading profile...</div>;
    }

    const handleNameChange = (input) => {
        // Remove non-alphabetic characters, except for spaces
        input = input.replace(/[^a-zA-Z\s]/g, '');

        // Limit input to a maximum of 50 characters
        const truncatedInput = input.slice(0, 50);

        // Convert to Title Case (Upper Camel Case)
        const titleCasedInput = truncatedInput.replace(/\b\w/g, (char) => char.toUpperCase());

        // Set the processed name
        setName(titleCasedInput);
    };

    const handleUpdatePassword = () => {
        navigate('/UpdateUserPassword');
    };
    const handlePromoCodeInputChange = (e) => {
        setPromoCode(e.target.value);
    };
    const ManageBillingPage = async () => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/manage-billing-portal`, {
                headers: {
                    Authorization: `Bearer ${localStorage.getItem('token')}`
                }
            });
            if (response.data) {
                console.log('Created billing portal for customer:', response.data);
                window.location.href = response.data.url;
            }
        } catch (error) {
            // Display the toast message
            if (currentMBToastId !== null) {
                toast.dismiss(currentMBToastId);
            }
            if (error.response && error.response.status === 404) {
                console.error("Stripe user not found. Please check your details and try again.", error);
                setCurrentMBToastId(toast.error(
                    <div>
                        It seems you don&apos;t have an AccuVelocity premium subscription yet. Please click{' '}
                        <a href="/upgradeplan" style={{ color: '#003654', textDecoration: 'underline', fontWeight: 500 }}>
                            <strong>Upgrade</strong>
                        </a>{' '}
                        to get started with a premium subscription.
                    </div>,
                    {
                        type: 'error',
                        icon: '⚠️',
                        duration: 6000
                    }
                ))
                // Handle specific UI updates or redirect as needed for 404 errors
            } else {
                setCurrentMBToastId(toast.error(
                    <div>
                        It seems you don&apos;t have an AccuVelocity premium subscription yet. Please click{' '}
                        <a href="/upgradeplan" style={{ color: '#003654', textDecoration: 'underline', fontWeight: 500 }}>
                            <strong>Upgrade</strong>
                        </a>{' '}
                        to get started with a premium subscription.
                    </div>,
                    {
                        type: 'error',
                        icon: '⚠️',
                        duration: 6000
                    }
                ))
            }
        }
    };

    const handleTopUpPlans = () => {
        if (activePlanName == "Free") { // Assuming isPremiumUser is a flag that indicates if the user is a premium subscriber
            // Display the toast message
            if (currentToastId !== null) {
                toast.dismiss(currentToastId);
            }
            setCurrentToastId(toast.error(
                <div>
                    Top up plan is only available to our premium subscribed users. Please click
                    <a href="/upgradeplan" style={{ color: '#003654', textDecoration: 'underline', marginLeft: '5px' }}>
                        <strong>Upgrade</strong>
                    </a> {' '}
                    to subscribe.
                </div>,
                {
                    type: 'error',
                    icon: '⚠️',
                    duration: 6000
                }
            ))
        } else {
            navigate("/AddOnPages")
        }

    }

    return (
        <>
            <div className="flex flex-col bg-[#f4f7fe]">
                <div className="px-6 py-2 pt-4 bg-[#f4f7fe] ">
                    <h1 className="text-3xl xl:text-2xl font-bold text-[#3F3F3F]">Profile</h1>
                </div>
                <Toaster position="top-center" />
                <div className="w-full m-0 p-0" style={{ overflow: 'hidden' }}>
                    <div className="flex items-stretch space-x-4 p-3 smo:p-4 xlo:p-6">
                        <div style={cardStyles} className="bg-[#ffff] rounded-2xl mb-4 p-3 smo:p-4 xlo:p-6 xlo:w-[552px] flex-none">
                            <div className="relative">
                                <img src={userData.profileBanner || ProfileBanner} alt="Profile Banner" className="rounded-2xl w-full h-[131px] object-cover" />
                                {editMode ? (
                                    <div style={profilePictureStyles} className="flex flex-col items-center justify-center">
                                        <img
                                            id="profile-picture-preview"
                                            src={profilePicture}
                                            alt="Profile"
                                            style={profilePictureStyles}
                                        />
                                        <label
                                            htmlFor="profile-upload"
                                            className="flex flex-col items-center justify-center absolute font-bold cursor-pointer"
                                            style={{
                                                bottom: '50%',
                                                right: '50%',
                                                transform: 'translate(50%, 50%)',
                                                color: '#003654',
                                                padding: '20px',
                                                backgroundColor: 'rgba(255, 255, 255, 0.5)',
                                                boxShadow: '0 4px 8px rgba(0, 0, 0, 0.1)',
                                                borderRadius: '50px'
                                            }}
                                        >
                                            Update Picture
                                            <input
                                                id="profile-upload"
                                                type="file"
                                                onChange={handleProfilePictureChange}
                                                className="hidden"
                                            />
                                        </label>
                                    </div>
                                ) : (
                                    <div style={profilePictureStyles}>
                                        <img
                                            id="profile-picture"
                                            src={profilePicture}
                                            alt="Profile"
                                            style={profilePictureStyles}
                                        />
                                    </div>
                                )}
                                <div className="text-center mt-3" style={{ marginTop: '3rem' }}> {/* Adjusted margin */}
                                    <Tooltip content={userData.name}>
                                        <div className="font-bold text-2xl xl:text-lg text-gray-800 tracking-wide leading-normal overflow-hidden">{userData.name}
                                        </div>
                                    </Tooltip>
                                </div>
                            </div>
                            <div className="p-3"> {/* Reduced padding */}
                                <div className="flex justify-between items-center p-4">
                                    <div>
                                        <div className="text-gray-500 text-center">Current Plan</div>
                                        <div
                                            className={`font-bold text-xl xl:text-[1rem] text-gray-800 tracking-wide leading-normal text-center ${(activePlanName !== 'Free') ? 'underline cursor-pointer' : ''}`}
                                            onClick={(activePlanName !== 'Free') ? ManageBillingPage : null}
                                        >
                                            {(activePlanName !== 'Free') ? (
                                                <Tooltip content="Please click to open manage billing portal. You will be redirected to our payment partner - Stripe">
                                                    <div style={{ display: 'flex' }}>
                                                        <span>{currentPlan}</span>
                                                        <CiCircleInfo className="ml-2 text-2xl" />
                                                    </div>
                                                </Tooltip>
                                            ) :
                                                <>{currentPlan}</>
                                            }
                                        </div>
                                    </div>
                                    <div>
                                        <div className="text-gray-500 text-center">Member Since</div>
                                        <div className="font-bold text-xl xl:text-[1rem] text-gray-800 tracking-wide leading-normal">
                                            {userData.created_at ? formatDate(new Date(userData.created_at)) : '17 April, 2024'}
                                        </div>
                                    </div>
                                </div>

                            </div>
                        </div>
                        {/* Contact Details Card */}
                        <div className="mb-4 p-3 bg-[#ffff] rounded-2xl smo:p-4 xlo:p-6 flex-1">
                            <div className="flex justify-between items-center p-3">
                                <div className="text-xl xl:text-[1rem] font-bold mb-4">
                                    Contact Details
                                </div>
                                <div className="flex justify-end space-x-4 mb-4">

                                    {editMode ? (
                                        <div className="flex space-x-4">
                                            <button
                                                onClick={updateUser}
                                                className="text-sm xl:text-xs bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                            >
                                                <FaSave />
                                                <span className="ml-2">Save</span>
                                            </button>
                                            <button
                                                onClick={handleCancel}
                                                className="text-sm xl:text-xs bg-gray-500 hover:bg-gray-600 text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                            >
                                                <FaTimes />
                                                <span className="ml-2">Cancel</span>
                                            </button>
                                        </div>
                                    ) : (
                                        <>
                                            <Tooltip content="Upgrade to access this plan. Available only for subscribed users.">
                                                <button
                                                    onClick={handleTopUpPlans}
                                                    className="text-sm xl:text-xs bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                                >
                                                    <MdOutlineRequestPage className="h-5 w-5" />
                                                    <span className="ml-2">Top Up</span>
                                                </button></Tooltip>
                                            <Tooltip content="Click here to manage your billing information, view invoices, and update payment methods.">
                                                <button
                                                    onClick={() => navigate("/UpgradePlan", { state: { showMessage: true } })}
                                                    className="text-sm xl:text-xs bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                                >
                                                    <BiMoneyWithdraw className="h-5 w-5" />
                                                    <span className="ml-2">Manage Billing</span>
                                                </button></Tooltip>
                                            <Tooltip content="Click here to update your password for better security">
                                                <button
                                                    onClick={handleUpdatePassword}
                                                    className="text-sm xl:text-xs bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                                >
                                                    <MdLockOpen className="h-5 w-5" />
                                                    <span className="ml-2">Update Password</span>
                                                </button></Tooltip>
                                            <Tooltip content="Click to edit your profile">
                                                <button
                                                    onClick={() => setEditMode(true)}
                                                    className="text-sm xl:text-xs bg-[#003654] hover:bg-[#002744] text-white font-bold py-2 px-4 rounded-2xl inline-flex items-center"
                                                >
                                                    <FaEdit />
                                                    <span className="ml-2">Edit Profile</span>
                                                </button></Tooltip>
                                        </>
                                    )}
                                </div>
                            </div>
                            <div className={`grid gap-6 mt-3 max-h-[15vw] ${window.innerWidth >= 1200 && window.innerWidth < 1600 ? 'overflow-y-auto xl:grid-cols-2' : ' grid-cols-3'}`}>
                                <div className={`bg-[#ffff] rounded-2xl p-4 break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`} style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}>
                                    {editMode ? (
                                        <>
                                            <div className='font-bold xl:text-sm'>Name</div>
                                            <input
                                                type="text"
                                                value={name}
                                                onChange={(e) => {
                                                    try {
                                                        handleNameChange(e.target.value);
                                                    } catch (error) {
                                                        toast.error(error.message); // Display user-friendly error message
                                                    }
                                                }
                                                }
                                                className="w-full p-2 xl:text-sm rounded mt-1 bg-gray-100 focus:outline-none focus:ring-1 focus:ring-[#3182ce]"
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <div className='font-bold'>Name</div>
                                            <div>{name}</div>
                                        </>
                                    )}
                                </div>
                                <div className={`bg-[#ffff] rounded-2xl p-4 break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`} style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}>
                                    {editMode ? (
                                        <>
                                            <div className='font-bold xl:text-sm'>Email</div>
                                            <input
                                                type="text"
                                                value={Email}
                                                onChange={(e) => setEmail(e.target.value)}
                                                className="input xl:text-sm w-full p-2 rounded mt-1 bg-gray-100"
                                                disabled
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <div className='font-bold xl:text-sm'>Email</div>
                                            <div className='xl:text-sm'>{Email}</div>
                                        </>
                                    )}
                                </div>
                                <div className={`bg-[#ffff] rounded-2xl p-4 break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`} style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}>
                                    {editMode ? (
                                        <>
                                            <div className='font-bold xl:text-sm'>Phone Number</div>
                                            <div className='bg-gray-100'>
                                                <PhoneInput
                                                    international
                                                    defaultCountry="US"
                                                    placeholder="Phone number (optional)"
                                                    value={PhoneNo}
                                                    onChange={setPhoneNo}
                                                    className='input w-full rounded mt-1 bg-gray-100'

                                                />
                                            </div>
                                        </>
                                    ) : (
                                        <>
                                            <div className='font-bold xl:text-sm'>Phone Number</div>
                                            <div className='xl:text-sm'>{PhoneNo ? PhoneNo : "No Number Added"}</div>
                                        </>
                                    )}
                                </div>
                                <div className={`bg-[#ffff] rounded-2xl p-4 break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`} style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}>
                                    {editMode ? (
                                        <>
                                            <div className='font-bold xl:text-sm'>Country</div>
                                            <select
                                                value={Country}
                                                onChange={(e) => setCountry(e.target.value)}
                                                className="input xl:text-sm w-full p-2 rounded mt-1 bg-gray-100 focus:outline-none focus:ring-1 focus:ring-[#3182ce]"
                                            >
                                                {Country === 'Select a country' && (
                                                    <option value="">
                                                        Select a country
                                                    </option>
                                                )}
                                                {countries.map((country, index) => (
                                                    <option key={index} value={country.value}>
                                                        {country.text}
                                                    </option>
                                                ))}
                                            </select>
                                        </>
                                    ) : (
                                        <>
                                            <div className='font-bold'>Country</div>
                                            <div>{Country}</div>
                                        </>
                                    )}
                                </div>
                                <div className={`bg-[#ffff] rounded-2xl p-4 xl:text-sm break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`} style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}>
                                    {editMode ? (
                                        <>
                                            <div className='font-bold'>Organization</div>
                                            <input
                                                type="text"
                                                value={userData.organization_name}
                                                onChange={(e) => setOrganization(e.target.value)}
                                                className="input xl:text-sm w-full p-2 rounded mt-1 bg-gray-100"
                                                disabled
                                            />
                                        </>
                                    ) : (
                                        <>
                                            <div className='font-bold xl:text-sm'>Organization</div>
                                            <div className='xl:text-sm'>{userData.organization_name}</div>
                                        </>
                                    )}
                                </div>
                                <div
                                    className={`bg-[#ffff] xl:text-sm rounded-2xl p-4 break-words ${window.innerWidth >= 1280 && window.innerWidth < 1600 ? 'm-3' : ''}`}
                                    style={{ boxShadow: '0px 18px 40px 0px rgba(112, 144, 176, 0.12)' }}
                                >
                                    <div className='flex items-center'>
                                        <div className='font-bold xl:text-sm'>Promo Code</div>
                                    </div>
                                    {editMode ? (
                                        <input
                                            type="text"
                                            value={promoCode}
                                            onChange={handlePromoCodeInputChange}
                                            className="input xl:text-sm w-full p-2 rounded mt-1 bg-gray-100 focus:outline-none focus:ring-1 focus:ring-[#3182ce]"
                                            disabled={!!userData.promoCodeName}
                                        />
                                    ) : (
                                        <div className='xl:text-sm'>{promoCodeHistory ? promoCodeHistory[0]?.promo_code_name : 'No Promo Code'}</div>
                                    )}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div className="ml-4 flex flex-wrap justify-start items-start overflow-auto" style={{ width: '100%' }}>
                        {[
                            { title: 'Total Models Created', value: userData.total_models_created || totalModelsCreated, Unit: "Models", tooltipMessage: "The total number of models created so far" },
                            { title: 'Total Pages Processed', value: userData.total_processed_pages, Unit: "Pages", tooltipMessage: "The total number of pages processed" },
                            { title: 'Total Documents Processed', value: userData.total_processed_status, Unit: "Documents", tooltipMessage: "The total number of documents processed" },
                            { title: 'Pro Page Limit Used', value: `${userData.total_allowed_page_limit - userData.page_limit_left}/${userData.total_allowed_page_limit}`, Unit: "Pages", tooltipMessage: "The usage of pro page limit" },
                            { title: 'Lite Page Limit Used', value: `${userData.free_page_limit_usage}/${userData.total_allowed_free_page_limit}`, Unit: "Pages", tooltipMessage: "The usage of Lite page limit" },
                            { title: 'Total Time Saved', value: Math.floor(((userData.total_processed_status * 5) / 60) * 100) / 100, Unit: 'Hours', tooltipMessage: "The total time saved by processing documents" }
                        ].map((stat, index) => (
                            <div key={index} className="bg-[#ffff] rounded-3xl pt-9 pb-9 m-2 w-[250px] xl:w-[158px] mdo:w-1/10 text-center flex flex-col items-center justify-center">
                                <Tooltip content={stat.tooltipMessage}>
                                    <div className="text-center mb-1 text-base 2xl:text-lg xl:text-[0.72rem]">
                                        {stat.title}
                                    </div>
                                </Tooltip>
                                <Tooltip content={`The value is ${stat.value}`}>
                                    <div className="text-[#003654] font-bold 2xl:text-4xl xl:text-3xl p-2">
                                        {stat.value}
                                    </div>
                                </Tooltip>
                                <Tooltip content={`Measured in ${stat.Unit.toLowerCase()}`}>
                                    <div className="text-base 2xl:text-xl font-bold text-center mt-2 p-2">
                                        {stat.Unit}
                                    </div>
                                </Tooltip>
                            </div>
                        ))}
                    </div>

                    {/* Promo Code History Section */}
                    <div className="mt-10 m-6 p-4 bg-white rounded-3xl w-[30vw]">
                        <div className="flex justify-between items-center">
                            <h2 className="text-xl font-bold m-2">Promo Code History</h2>
                            <div>
                                <BsThreeDots className='text-4xl hover: cursor-pointer' onClick={toggleExpand} />
                            </div>
                        </div>
                        <div className="overflow-auto" style={{ maxHeight: '200px' }}>
                            <table className="w-full text-center border-collapse overflow-auto">
                                <thead className='sticky top-0 bg-white'>
                                    <tr>
                                        <th className="border-b-2 p-2">Index</th>
                                        <th className="border-b-2 p-2">Promo Code</th>
                                        <th className="border-b-2 p-2">Pages Added</th>
                                        <th className="border-b-2 p-2">Applied Date</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    {promoCodeHistory.length === 0 ? (
                                        <tr>
                                            <td colSpan="4" className="p-2 text-center">No promo codes applied yet.</td>
                                        </tr>
                                    ) : (
                                        promoCodeHistory.slice(0, isPromoCodesExpanded ? promoCodeHistory.length : 2).map((promo, index) => (
                                            <tr key={index}>
                                                <td className="border-b p-2">{index + 1}</td>
                                                <td className="border-b p-2">{promo.promo_code_name}</td>
                                                <td className="border-b p-2">{promo.value} Pages</td>
                                                <td className="border-b p-2">{formatTo12Hour(promo.used_at)} EDT</td>
                                            </tr>
                                        ))
                                    )}
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </>
    );
}