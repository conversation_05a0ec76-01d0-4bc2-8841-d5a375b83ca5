[program:myfastapiapp]
directory=/home/<USER>/Desktop/AccuVelocity/5_Env
command=/home/<USER>/Desktop/AccuVelocity/5_Env/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 6
autostart=true
autorestart=true
stderr_logfile=/var/log/myfastapiapp/myfastapiapp.err.log
stdout_logfile=/var/log/myfastapiapp/myfastapiapp.out.log

   ```s

4. Create log files for Supervisor:
   ```bash
   sudo mkdir /var/log/myfastapiapp
   sudo touch /var/log/myfastapiapp/myfastapiapp.err.log
   sudo touch /var/log/myfastapiapp/myfastapiapp.out.log


# Delete the configuration file from sites-available
sudo rm /etc/nginx/sites-available/backend.accuvelocity.in

# Remove the symlink from sites-enabled
sudo rm /etc/nginx/sites-enabled/backend.accuvelocity.in


sudo nano /etc/nginx/sites-available/backend.accuvelocity.com
    ```

    Paste the following server block configuration:
    ```nginx
    server {
        listen 80;
        server_name backend.accuvelocity.com;

        location / {
            proxy_pass http://localhost:8000;
            proxy_set_header Host $host;
            proxy_set_header X-Real-IP $remote_addr;
            proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
            proxy_set_header X-Forwarded-Proto $scheme;
        }
    }


sudo ln -s /etc/nginx/sites-available/backend.accuvelocity.com /etc/nginx/sites-enabled/
sudo nginx -t
sudo systemctl reload nginx

sudo certbot --nginx -d backend.accuvelocity.com