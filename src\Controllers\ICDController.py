import json
import asyncio
import os
import sys
import math
import uuid
sys.path.append(".")
import re
import requests
import uuid
import traceback
from datetime import datetime, timedelta
from collections import defaultdict
import pytz
from fastapi import HTTPException
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData

from src.Schemas.Tally_Schemas import TallyStockItemObj, TallyStockItemColumn, TallyRoundOffAmountObj, PTAdditionalChargesObj, sanitaryStockItemReqObj, GPTResponseResObj, ParagTradersStockItemCreation, TallyAPIHeaderObj, TallyLedgerName, chemicalStockItemReqObj, TallyPriceListReportResponse, ICDPegasus
from src.utilities.DBHelper import CExtractionTable
from src.utilities.helperFunc import CJSONFileReader, CommonHelper, ReadExcelToDict, CFile<PERSON><PERSON><PERSON>, Date<PERSON>elper
from src.Controllers.Logs_Controller import CLogController
from src.Schemas.TallyJournalVoucherXMLSchema import CTallyJournalVoucherTemplate, TallyJournalVoucherInputSchema, CompanyInfoSchema, LedgerEntrySchema
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode 
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from config.db_config import AsyncSessionLocal
from src.Models.models import  TallyBackupRecord
from sqlalchemy import Date
from sqlalchemy.future import select
import pandas as pd
import re
import xml.etree.ElementTree as ET
from fastapi import HTTPException
from decimal import Decimal, ROUND_DOWN, ROUND_UP
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from typing import Any
from src.utilities.PathHandler import dictProjectPaths

class CICDController:

    # For Parag Traders - Purchase with Inventory Template Used
    _mMaxAllowedRoundOff = 1.0
    _mbSimpoloStockItemCreation = False  # Enable to create stock item if not exist in customer stock item
    _mbNexionStockItemCreation = False
    _mbHansgroheStockItemCreation = False
    _mbKohlerStockItemCreation = False
    _mbGeberitStockItemCreation = False
    _mbTotoStockItemCreation = False
    _mbAquantStockItemCreation = False
    _mbIconStockItemCreation = False
    # Tax Journal
    _mTallyPrimeAPITemplateURL = "https://api.excel2tally.in/api/User/JournalTemplate"
    _mDeveloperTallyCompanyName = "FAIRDEAL INTERNATIONAL - 2024-25"
    _mDeveloperTallyAUTHKEY = "live_e4fa57395a08401faa1b27b8d538d21e"
    # live_0a44dcc353cb4a63a0b067274b6cdb5f  Found in harshil tally  5.1 for ICD
    _mStrTallyReqObjFile = dictProjectPaths.get("strICD_TallyReqObjectFilePath", r"H:/AI Data/18_FairdealInternational/TallyRequestsObject")

    def __init__(self, user_id, doc_id):
        self.iUserId = user_id
        self.DocId = doc_id
        self.TallyUserConfigObj = None
        self.UploadDocObj = None
        self.ExtractedDocObj = None
        self.__mTallyHeaderObj = {} 
        self.tally_body_obj = []
        self.tally_NotExistStockItems = {}
        self.dictProcessData = {}
        self.AdditionalExcelTemplate = None
        self.tally_AccuvelocityComments = "-"
        self.tally_Tally_status = "Skipped"
        
    

    
    @staticmethod
    async def MSConvertIntToDateFromYYYYMMDD(invoice_date: int) -> str:
        """
        Convert an integer representing a date in various formats to a standard YYYYMMDD format.

        Supported formats:
        - DDMMYYYY
        - DDMMYY
        - DMMYY
        - DMMYYYY

        Note:
            Month: One or two digits
            Day: Two digits
            Year: Two or four digits
        """
        try:
            # Convert the integer to a string for easier manipulation
            invoice_str = str(invoice_date)
            length = len(invoice_str)

            day = month = year = None  # Initialize variables

            if length == 6:
                # Format: DDMMYY (e.g., 080524 -> 08.05.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = '20' + invoice_str[4:]
            elif length == 8:
                # Format: DDMMYYYY (e.g., 08102024 -> 08.10.2024)
                day = invoice_str[:2]
                month = invoice_str[2:4]
                year = invoice_str[4:]
            elif length == 5:
                # Format: DMMYY (e.g., 80524 -> 08.05.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = '20' + invoice_str[3:]
            elif length == 7:
                # Format: DMMYYYY (e.g., 8102024 -> 08.10.2024)
                day = invoice_str[0]
                month = invoice_str[1:3]
                year = invoice_str[3:]
            else:
                await CLogController.MSWriteLog(None, "Error", f"Invalid date format received: {invoice_date}")
                return "Invalid date format"

            # Convert extracted strings to integers for validation
            day_int = int(day)
            month_int = int(month)
            year_int = int(year)

            # Validate extracted date components
            datetime(year_int, month_int, day_int)  # Raises ValueError if the date is invalid

            # Construct the final date string based on the day of the month
            if day_int <= 12:
                converted_date = f"{year_int}{month_int:02}{day_int:02}"  # YYYYMMDD
            else:
                converted_date = f"{year_int}{month_int:02}{day_int:02}"  # YYYYMMDD
            await CLogController.MSWriteLog(
                None, "Info", f"Converted integer date {invoice_date} to string date {converted_date}."
            )
            return converted_date
        except ValueError as ve:
            await CLogController.MSWriteLog(None, "Error", f"ValueError in MSConvertIntToDate: {ve}")
            return "Invalid date format"
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Exception in MSConvertIntToDate: {str(e)}")
            return "Invalid date format"
        
    @staticmethod
    def MSValidateTaxRates(igst_rate, cgst_rate, sgst_rate):
        """
        Validates IGST, CGST, and SGST rates based on specific rules:
        - If IGST is 0.0, validate CGST and SGST rates.
        - If CGST and SGST are 0.0, validate IGST rate only.

        Parameters:
        - igst_rate (float or int): Integrated GST rate
        - cgst_rate (float or int): Central GST rate
        - sgst_rate (float or int): State GST rate

        Returns:
        - None

        Raises:
        - ValueError: If any of the rates are invalid
        """
        valid_igst_rates = {12.0, 18.0, 5.0}
        valid_cgst_sgst_rates = {2.5, 6.0, 9.0, 14.0}  # CGST and SGST share the same valid rates

        try:
            igst_rate = float(igst_rate)
            cgst_rate = float(cgst_rate)
            sgst_rate = float(sgst_rate)
        except ValueError:
            raise ValueError(f"Invalid tax rate values: IGST: '{igst_rate}', CGST: '{cgst_rate}', SGST: '{sgst_rate}'. Please provide numeric values.")

        # Case 1: If IGST is 0.0, validate CGST and SGST
        if igst_rate == 0.0:
            if cgst_rate not in valid_cgst_sgst_rates or sgst_rate not in valid_cgst_sgst_rates:
                error_message = (f"Taxable Ledger details with Rate CGST: '{cgst_rate:.1f}', "
                                f"SGST: '{sgst_rate:.1f}' were not found. Please proceed manually.")
                raise ValueError(error_message)
        
        # Case 2: If CGST and SGST are 0.0, validate IGST
        elif cgst_rate == 0.0 and sgst_rate == 0.0:
            if igst_rate not in valid_igst_rates:
                error_message = (f"Taxable Ledger details with Rate IGST: '{igst_rate:.1f}' were not found. "
                                "Please proceed manually.")
                raise ValueError(error_message)

        else:
            # Case where IGST, CGST, and SGST are valid and should be checked
            if igst_rate not in valid_igst_rates or cgst_rate not in valid_cgst_sgst_rates or sgst_rate not in valid_cgst_sgst_rates:
                error_message = (f"Taxable Ledger details with Rate IGST: '{igst_rate:.1f}', CGST: '{cgst_rate:.1f}', "
                                f"SGST: '{sgst_rate:.1f}' were not found. Please proceed manually.")
                raise ValueError(error_message)
        return True
    
    @staticmethod
    async def MGenerateTallyXML(iUserId, iDocId, dictExtractedData, bRaiseError=False, strClientREQID= None):
        
        dictResponse = {
            "AVComments":"",
            "TallyStatus":"",
            "XMLFilePath":""
        }
        iSupplierInvoiceNumber = ""
        strModelName = ""
        try:
            await CLogController.MSWriteLog(iUserId, "Info", "Starting XML Generation for ICD.")
            
            iSupplierInvoiceNumber = dictExtractedData.get("InvoiceNumber", 
                                    dictExtractedData.get("InvoiceNo", 
                                    dictExtractedData.get("InvoiceNo.", 
                                    dictExtractedData.get("CreditNoteNo/InvoiceNo"))))

            # Get Upload Document Attributes
            objUploadedDocs = await CDocumentData.MSGetDocById(
                                                                user_id=iUserId, 
                                                                docId=iDocId,
                                                                isBinaryDataRequired=False
                                                            )
            strModelName = objUploadedDocs.get("ModelName", "")
            
            await CLogController.MSWriteLog(iUserId, "Info", f"Fetched document details for DocId: {iDocId} and Invoice Number: {iSupplierInvoiceNumber}.")
            bIsDuplicate = None
            if iSupplierInvoiceNumber is None or not iSupplierInvoiceNumber:
                # We Go Base on Doc ID if Invoice Number is Empty or None
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocID=iDocId)
            else:
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocumentNumber=iSupplierInvoiceNumber)

            if bIsDuplicate:
                dictResponse["TallyStatus"] = "Duplicate"
                dictResponse["AVComments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                dictResponse["TallyAPIResp"] = {"status": f"Failed to Create the Tally xml : Tally XML: Duplicate Entry Found in AccuVelocity."}
                dictResponse["DocErrorMsg"] = "AccuVelocity Duplicate Validation Entry Found."
                await CLogController.MSWriteLog(iUserId, "Info", f"Duplicate entry found for invoice numbered '{iSupplierInvoiceNumber}'.")

                # AVRecordDetail -- AVXMLGeneratedStatus, TracebackLogs, strAccuVelocityComments Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE', AVXMLGeneratedStatus=dictResponse["TallyStatus"],TracebackLogs= "WARNING - Duplicate Entry Detected in our AccuVelocity Software", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments=dictResponse["AVComments"])

                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    strREQID = strClientREQID,
                    iDocID=iDocId,
                    iUserID=iUserId,
                    invoice_no=iSupplierInvoiceNumber,
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"] 
                )
                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")

            lsAdditionalInfo = objUploadedDocs.get("AdditionalDocDetails")
            try:
                if strModelName.lower() == "pegasus":
                    binXmlData = await CPegasus_XML.MSCreateXML(dictExtractedData=dictExtractedData, 
                                                            iUserId=iUserId, 
                                                            AdditionalExcelTemplate=lsAdditionalInfo, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "hind terminals":
                    binXmlData = await CHindTerminals_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "adani hazira":
                    binXmlData = await CAdaniHazira_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "container corporation of india ltd.":
                    #TODO: remove this adani class in concor
                    binXmlData = await CConcor_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "goodrich maritime private limited":
                    #TODO: remove this adani class in concor
                    binXmlData = await CGoodRich_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "kotak global logistics pvt ltd":
                    #TODO: remove this adani class in concor
                    binXmlData = await CKotak_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                elif strModelName.lower() == "msc agency":
                    #TODO: remove this adani class in concor
                    binXmlData = await CMSC_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                
                elif strModelName.lower() == "united liner shipping services llp":
                    #TODO: remove this adani class in concor
                    binXmlData = await CUnited_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)
                
                elif strModelName.lower() == "anl singapore pte. ltd. c/o ccai" or strModelName.lower() == "anl singapore pte. ltd" or  strModelName.lower() == "anl singapore pte. ltd.":
                    #TODO: remove this adani class in concor
                    binXmlData = await CANL_XML.MSCreateXML(dictExtractedData=dictExtractedData,  
                                                            AdditionalExcelTemplate=lsAdditionalInfo, iUserId=iUserId, strClientREQID=strClientREQID, iDocID=iDocId)

                else:
                    raise ValueError("ICD Customer - Vendor Not Identify")
                
            except ValueError as ve:
                error_message = str(ve)
    
                # Check if the error message contains "Mismatch detected"
                if "Mismatch detected" in error_message:
                    # Take the specific action for the mismatch message
                    dictResponse["TallyStatus"] = "Skipped"
                    dictResponse["AVComments"] = error_message
                    await CLogController.MSWriteLog(iUserId, "Error", f"Mismatch detected in invoice numbered '{iSupplierInvoiceNumber}', Error:{error_message}.")
                else:
                    # Default action for other ValueErrors
                    dictResponse["TallyStatus"] = "Skipped"
                    dictResponse["AVComments"] =  "Unable to process the invoice, Reason could be Accuracy | Scanned Doc | Table Dense Data. Please proceed manually."
                    await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                    await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise ve
            
            try:
                # Base directory path
                strDownloadDirPath = CPegasus_XML._mStrPegasusDocStoragePath
                today_date = datetime.today().strftime('%Y_%m_%d')
                
                # Create the full directory path with the date-wise folder
                strDownloadDirPath = os.path.join(strDownloadDirPath, today_date)

                # Ensure the directory exists
                os.makedirs(strDownloadDirPath, exist_ok=True)

                strUploadedDocumentName = os.path.splitext(objUploadedDocs.get('DocName', ''))[0]
                strXMLFileName = f"{strClientREQID}_DID{iDocId}_DName{strUploadedDocumentName}.xml"
                strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                bIsFileWritten = CFileHandler.MSWriteFile(strFilePath=strTodaysXmlFilePath, 
                                        fileContent=binXmlData, 
                                        strWriteMode="wb", 
                                        strEncoding=None) 
                if bIsFileWritten:
                    dictResponse["XMLFilePath"] = strTodaysXmlFilePath
                    dictResponse["TallyStatus"] = "Success"
                    dictResponse["AVComments"] = "-"
                    # AVRecordDetail Update  
                    await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                        iUserId=iUserId,
                        dictNewData = {"XMLFilePath": [strTodaysXmlFilePath]},
                        eMode = ENetworkLocationUpdateMode.APPEND,
                        strClientREQID=strClientREQID,
                        docId=iDocId)
                    # Update the status of document processing
                    current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
                    await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                        iDocID = iDocId,
                        iUserID= iUserId,
                        strREQID = strClientREQID,
                        invoice_no=iSupplierInvoiceNumber,
                        av_tally_xml_status="Success",
                        tally_api_resp={"status": f"Successfully created the tally xml at location: {strTodaysXmlFilePath}."},
                        resp_date_time=current_datetime,
                        strAVComments="-"
                    )

                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully stored xml file at location '{strTodaysXmlFilePath}'.")

            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                
                # Update the status of document processing
                current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=iSupplierInvoiceNumber,
                    av_tally_xml_status="Skipped",
                    tally_api_resp={"status": f"Failed to Create the Tally xml."},
                    DocErrorMsg={str(traceback.format_exc())},
                    resp_date_time=current_datetime, 
                    strAVComments="The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                )
                
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

        except HTTPException as he:
            dictResponse["TallyStatus"] = "Skipped"
            dictResponse["AVComments"] = str(he)
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Skipped",
                tally_api_resp={"status": f"Failed to Create the Tally xml {he}."},
                DocErrorMsg={str(traceback.format_exc())},
                resp_date_time=current_datetime,
                strAVComments="The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            )
            
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{he}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        
        except ValueError as ve:
            dictResponse["TallyStatus"] = "Skipped"
            dictResponse["AVComments"] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."  if dictResponse["AVComments"] == "" else dictResponse["AVComments"]
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Skipped",
                tally_api_resp={"status": f"Failed to Create the Tally xml."},
                DocErrorMsg={str(traceback.format_exc())},
                resp_date_time=current_datetime,
                strAVComments="The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            )
            raise ve
        
        except Exception as e:
            dictResponse["TallyStatus"] = "Skipped"
            dictResponse["AVComments"] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."  if dictResponse["AVComments"] == "" else dictResponse["AVComments"]
            
            
            # Update the status of document processing
            current_datetime = datetime.now(pytz.timezone('Asia/Kolkata'))
            await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                iDocID = iDocId,
                iUserID= iUserId,
                strREQID = strClientREQID,
                invoice_no=iSupplierInvoiceNumber,
                av_tally_xml_status="Skipped",
                tally_api_resp={"status": f"Failed to Create the Tally xml."},
                DocErrorMsg={str(traceback.format_exc())},
                resp_date_time=current_datetime, 
                strAVComments="The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            )
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            print(f"An unexpected error occurred: {e}")

            if bRaiseError:
                raise e
            
        return dictResponse


class CPegasus_XML:
    # GPT Keys 
    strTableKey = "Table"
    strInvoiceDateKey = "InvoiceDate"   # converted to MM.DD.YYYY format from DD.MM.YYYY int format
    strSupplierInvoiceNo = "InvoiceNumber"

    strGrandTotalAmtKey = "NetAmount"
    # WARN: Please make this finacial Year as per Document
    strFinancialYear = "2025-2026"
    strAdvPaymentDiscountAmtKey = ""
    # Taxable Amount Keys
    strIGSTAmtKey = "IGSTAmount"      # total Taxable amount key
    strCGSTAmtKey = "CGSTAmount"      # total Taxable amount key
    strSGSTAmtKey = "SGSTAmount"      # total Taxable amount key
    strLineItemAmtKey = "Amount"   # without taxable amount key
    strRoundOffAmtKey = "RoundOff"  

    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _mStrPegasusDocStoragePath = dictProjectPaths.get("strPegasis_DocStoragePath", r"H:/AI Data/DailyData/ICD")


    @staticmethod
    def MSCalculateTDSFixedAmount(item_total, fixedRate=2):
        tds_amount = round(item_total * 0.02, fixedRate)  # Assuming 2% TDS
        fractional_part = tds_amount - int(tds_amount)
        if fractional_part >= 0.5:
            tds_amount = int(tds_amount) + 1
        else:
            tds_amount = int(tds_amount)
        return tds_amount
    
    @staticmethod
    def MSFormatTaxRate(rate):
        rate = float(rate)
        return int(rate) if rate.is_integer() else rate
    
    @staticmethod
    def MSFindInvoiceAdditionalDetails(dictExcelData, invoice_number):
        matching_rows = [row for row in dictExcelData if row['Invoice No'] == invoice_number]
        
        if not matching_rows:
            raise ValueError(f"ValidationError - Tally XML: Mandatory details for invoice '{invoice_number}' are missing in the av_pegasus_additionaldetails.xlsx file. Please ensure all required details are provided before proceeding.")

        elif not matching_rows[0].get("A/C Name"):
            raise ValueError(f"ValidationError - Tally XML: A/C Name is missing in the av_pegasus_additionaldetails.xlsx file for invoice number '{invoice_number}'. Please ensure all required details are provided before proceeding.")

        elif not matching_rows[0].get("Job Number"):
            raise ValueError(f"ValidationError - Tally XML: Job Number is missing in the av_pegasus_additionaldetails.xlsx file containing for invoice number '{invoice_number}'. Please ensure all required details are provided before proceeding.")

        return matching_rows

    
    
    @staticmethod
    async def MSCreateXML(dictExtractedData, iUserId, AdditionalExcelTemplate, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry based on the extracted and additional data.
        This version uses the CTallyJournalVoucherTemplate to generate the XML.
        
        Args:
            dictExtractedData (dict): Dictionary with parsed invoice data.
            iUserId (int): User ID.
            AdditionalExcelTemplate (str or list): Either path to an Excel file or a list 
                that already contains the required additional data.
        
        Returns:
            bytes: The generated XML as bytes.
        """
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False 
        strTimeSaved = "NOT_APPLICABLE"
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Invoice & Additional Details ------------------
            InvoiceNumber = dictExtractedData.get(CPegasus_XML.strSupplierInvoiceNo)
            try:
                lsMatchedInfo = CPegasus_XML.MSFindInvoiceAdditionalDetails(dictAdditionalTemplate, InvoiceNumber)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward
            
            dictAdditionalData = lsMatchedInfo[0]
            # (Other additional details such as Job Number, Container No, A/C Name are available as needed)
            # ----------------------- Get Expense Ledger & Cost Centers -----------------------
            dictExpenseLedger = await CPegasus_XML.MGetExpenseLedger(
                dictExtractedData=dictExtractedData,
                dictCurrentAdditionalInfo=dictAdditionalData
            )
            lsCostCenters = dictExpenseLedger.get("Cost Center", [])

            # ----------------------- Get Duties & Taxes, Round-Off, & Credit Ledgers -----------
            try:
                lsDutiesTaxesLedger = CPegasus_XML.MSFindDutiesLedger(dictExtractedData=dictExtractedData)
            except ValueError as ve:
                err_msg = str(ve)
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            try:
                dictRoundOffLedger = CPegasus_XML.MSGetRoundOff(dictExtractedData=dictExtractedData)
            except ValueError as ve:
                err_msg = str(ve)
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        )
                    )
                return None # propagate the same ValueError upward

            lsCreditedLedger = CPegasus_XML.MSGenerateCreditLedger(dictExtractedData, lsCostCenters)

            # ----------------------- Build Ledger Entries for the Voucher --------------------
            ledger_entries = []

            # Expense Ledger (Debit)
            expense_amount = -dictExpenseLedger.get("Amount", 0.0)
            cost_center_category = CICDController_XML._mStrFinancialYear
            # Build cost center allocations if cost centers exist
            if lsCostCenters:
                base_amount = round(expense_amount / len(lsCostCenters), 2)
                total_distributed = base_amount * len(lsCostCenters)
                rounding_diff = round(expense_amount - total_distributed, 2)
                cost_center_allocations = []
                for idx, cc in enumerate(lsCostCenters):
                    assigned_amount = base_amount
                    if idx == len(lsCostCenters) - 1:
                        assigned_amount += rounding_diff
                    cost_center_allocations.append({"name": cc, "amount": assigned_amount})
            else:
                cost_center_allocations = None

            expense_entry = {
                "ledger_name": dictExpenseLedger.get("Ledger Name", ""),
                "amount": expense_amount,
                "is_deemed_positive": True,  # Debit entry
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": cost_center_category,
                "cost_center_allocations": cost_center_allocations,
            }
            ledger_entries.append(LedgerEntrySchema(**expense_entry))

            # Duties & Taxes Ledger Entries (Debit)
            for tax in lsDutiesTaxesLedger:
                tax_amount = -tax.get("Amount", 0.0)
                tax_entry = {
                    "ledger_name": tax.get("Ledger Name", ""),
                    "amount": tax_amount,
                    "is_deemed_positive": True,
                    "is_party_ledger": False,
                }
                ledger_entries.append(LedgerEntrySchema(**tax_entry))

            # Round-Off Ledger Entry
            round_off_entry = {
                "ledger_name": dictRoundOffLedger.get("Ledger Name", ""),
                "amount": dictRoundOffLedger.get("Amount", 0.0),
                "is_deemed_positive": True if dictRoundOffLedger.get("Debit / Credit") == "Dr" else False,
                "is_party_ledger": False,
            }
            ledger_entries.append(LedgerEntrySchema(**round_off_entry))

            # Credit Ledger Entries (Credit)
            for credit in lsCreditedLedger:
                credit_entry = {
                    "ledger_name": credit.get("Ledger Name", ""),
                    "amount": credit.get("Amount", 0.0),
                    "is_deemed_positive": False,  # Credit entry
                    "is_party_ledger": True,
                }
                # Add Bill Allocation for specific ledgers (if applicable)
                if "PEGASUS INLAND" in credit.get("Ledger Name", "").upper():
                    credit_entry["bill_allocation"] = {
                        "name": credit.get("Bill Ref No", ""),
                        "billtype": "New Ref",
                        "amount": credit.get("Amount", 0.0),
                    }
                ledger_entries.append(LedgerEntrySchema(**credit_entry))

            # ----------------------- Prepare Common Voucher Details --------------------------
            # Convert the invoice date and get today’s date.
            invoice_number = str(dictExtractedData.get("InvoiceNumber", ""))
            iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")

            # ----------------------- Build the Journal Voucher Input Schema ------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CPegasus_XML._mStrCompanyName
                ),
                voucher_date=todays_date,
                narration=dictExpenseLedger.get("Narration", ""),
                reference=f"{invoice_number} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CPegasus_XML._mStrVoucherType,
                numbering_style=CPegasus_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )

            # ----------------------- Generate XML via the Journal Voucher Template -----------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="Pegasus", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward
        
    
    
    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData, lsJobNumbers):
        """
        Generates ledger entries based on the GPTExtractedData from the objICDPegasus.

        Args:
            objICDPegasus (ICDPegasus): The object containing extracted GPT data.

        Returns:
            list: A list containing two dictionaries for the ledger entries.
        """
        # Extract data from objICDPegasus
        data = dictExtractedData
        # Calculate TDS Amount (2% of ItemTotal)
        tds_amount = CPegasus_XML.MSCalculateTDSFixedAmount(data["ItemTotal"])


        # Determine the format of strJobNumber based on lsJobNumbers
        if len(lsJobNumbers) > 1:
            # Extract the numeric part of the job numbers
            numeric_parts = [
                                int(parts[-1]) for job in lsJobNumbers if (parts := job.split('-')) and parts[-1].isdigit()
                            ]

            # Check if the range is continuous
            if max(numeric_parts) - min(numeric_parts) == len(numeric_parts) - 1 and sorted(numeric_parts) == numeric_parts:
                # Continuous range
                strJobNumber = f"{lsJobNumbers[0]} TO {lsJobNumbers[-1]}"
            else:
                # Non-continuous or unsorted, join with commas
                strJobNumber = ", ".join(lsJobNumbers)
        else:
            # Single job number
            strJobNumber = lsJobNumbers[0]
        
        # Prepare the first dictionary for the seller ledger
        ledger_entry_1 = {
            "Ledger Name": "PEGASUS INLAND CONTAINER DEPOT PVT. LTD.",
            "Amount": round(data["NetAmount"] - tds_amount,2), # Grand Total - TDS Amount
            "Debit / Credit": "Cr",
            "Bill Ref No": f"{data['InvoiceNumber']}/{strJobNumber}"
        }

        # Prepare the second dictionary for TDS
        ledger_entry_2 = {
            "Ledger Name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
            "Amount": round(tds_amount, 2),
            "Debit / Credit": "Cr"
        }

        # Return the list of ledger entries
        return [ledger_entry_1, ledger_entry_2]
    
    @staticmethod
    def MSGetRoundOff(dictExtractedData):
        """
        Calculates and validates the round-off amount based on the given data.

        Args:
            data (dict): The input data containing amounts and details.

        Returns:
            float: The calculated round-off amount.

        Raises:
            ValueError: If the calculated or provided round-off value is out of range.
        """
        # Extract data from objICDPegasus
        data = dictExtractedData
        # Extract relevant amounts
        cgst_amount = data.get("CGSTAmount", 0)
        sgst_amount = data.get("SGSTAmount", 0)
        item_total = data.get("ItemTotal", 0)
        net_amount = data.get("NetAmount", 0)
        provided_round_off = data.get("RoundOff", 0)

        # Calculate the sum of CGST, SGST, and ItemTotal
        calculated_total = cgst_amount + sgst_amount + item_total

        # Calculate the round-off amount
        round_off = round(net_amount - calculated_total, 2)

        # Validate the calculated round-off amount
        if abs(round_off) <= 1:
            # Add "ROUND OFF" ledger entry
            return {
                "Ledger Name": "ROUND OFF",
                "Debit / Credit":  "Dr" if round_off >= 0 else "Cr",
                "Amount": -(round_off) if round_off >= 0 else abs(round_off)
            }

        # If calculated round-off is invalid, check the provided round-off
        if abs(provided_round_off) <= 1:
            # Add "ROUND OFF" ledger entry using provided round-off
            return {
                "Ledger Name": "ROUND OFF",
                "Debit / Credit": "Dr", # NOTE: Round Off Value Extracted is Without Sign So its Unable to Decide 'Cr' or 'Dr'  
                "Amount": -(provided_round_off)
            }

        # If neither is valid, raise an error
        raise ValueError("ValidationError - Tally XML: Both calculated and provided round-off amounts are out of range.")


    @staticmethod
    async def MGetExpenseLedger(dictExtractedData, dictCurrentAdditionalInfo):
        """
        Creates an expense ledger dictionary by calculating the sum of amounts
        in the given data and merging it with the provided common fields.

        Args:
            data (dict): Input data containing a 'Table' key with a list of items.
            common_fields (dict): A dictionary containing common fields to be added.

        Returns:
            dict: A dictionary representing the expense ledger.
        """
        # Extract the 'Table' from the input data
        table = dictExtractedData.get("Table", [])
        
        # Calculate the sum of the 'Amount' field in the table
        total_amount = sum(item.get("Amount", 0) for item in table)
        
        # Create the expense ledger dictionary
        lsJobNumbers, strNarration = await CPegasus_XML.MSGenerateNarration(dictExtractedData=dictExtractedData, dictCurrentAdditionalInfo=dictCurrentAdditionalInfo)
                
        return {
                "Amount": total_amount,  # Add the calculated total amount
                "Ledger Name": "ICD Expense",  # Add the ledger name
                "Debit / Credit": "Dr",  # Specify Debit / Credit
                "Cost Center": lsJobNumbers,
                "Narration": strNarration
            }
    
    @staticmethod
    async def MSGenerateNarration(dictExtractedData, dictCurrentAdditionalInfo):
        """
        Generates a narration string based on the given input data.

        Args:
            data (dict): The input data containing invoice and job details.

        Returns:
            str: The formatted narration string.
        """
        
        strJobNumber = ""
        strACName = dictCurrentAdditionalInfo.get("A/C Name", None)
        
        # Use lists to collect job numbers and container numbers
        job_numbers = [job.strip() for job in dictCurrentAdditionalInfo.get("Job Number", "").split(",") if job.strip()] # Resolve , issue 
        # Join the lists with commas to form the final strings
        strJobNumber = ", ".join(job_numbers)
        
        # Extract data from objICDPegasus
        data = dictExtractedData
        # Extract necessary fields from data
        invoice_number = data.get("InvoiceNumber", "N/A")
        invoice_date = data.get("InvoiceDate", "N/A")  # Original invoice date in YYMMDD format
        invoice_date = await CICDController.MSConvertIntToDateFromYYYYMMDD(invoice_date)
        strInovoiceDate = datetime.strptime(str(invoice_date), "%Y%m%d").strftime("%d.%m.%Y")

        # Use Container Number from Additional Info, else if not provided then use from extracted data
        container_no = ""
        strContainerNumberfromAdditional = dictCurrentAdditionalInfo.get("Container No (Please specify if not provided)", None)
        # NOTE: Container comma separated in case of multiple
        if strContainerNumberfromAdditional:
            container_no = strContainerNumberfromAdditional
            
        if not container_no:
            container_no = data.get("ContainerNo", "N/A")
        
        container_size = data.get("ContainerSize", "N/A")
        item_total = data.get("ItemTotal", 0.0)
        # Normal Rounding in TDS@2% Amount
        tds_amount = CPegasus_XML.MSCalculateTDSFixedAmount(item_total)

        # Construct the narration string
        narration = (
            f"BEING INVOICE NO. {invoice_number} DATE {strInovoiceDate} RCVD "
            f"AGST JOB NO. {strJobNumber} A/C {strACName} "
            f"AGST CONT. NO. {container_no}/{container_size} ( TDS DED. ON RS. {item_total:,.2f}"
            f"@2%= {tds_amount} ) PICD"
        )
        return job_numbers, narration
    
    
    @staticmethod
    def MSFindDutiesLedger(dictExtractedData):
        """
        Calculates ledger details for CGST and SGST based on the GST rate column
        and the respective tax amounts.

        Args:
            data (dict): Input data containing invoice details and tax-related information.

        Returns:
            list: A list of dictionaries containing ledger names and their respective amounts.

        Raises:
            ValueError: If IGST is found, indicating no IGST ledger is configured.
        """
        data = dictExtractedData
        # Check if IGSTAmount > 0 and raise an error
        if data.get("IGSTAmount", 0) > 0:
            raise ValueError("ValidationError - Tally XML: No IGST Ledger (duties and taxes) found in your Tally.")

        # Extract CGST and SGST amounts to ensure state-wise tax
        cgst_amount = data.get("CGSTAmount", 0)
        sgst_amount = data.get("SGSTAmount", 0)

        if cgst_amount <= 0 or sgst_amount <= 0:
            raise ValueError("ValidationError - Tally XML: CGST or SGST amounts are not valid for state-wise tax calculation.")

        # Prepare cumulative GST rate-wise amounts
        rate_amount_map = {}
        for item in data.get("Table", []):
            gst_rate = item.get("GSTRate", 0)
            taxable_amount = item.get("Amount", 0) 
            
            # Divide GST rate by 2 for state-wise taxes (CGST and SGST)
            half_rate = gst_rate / 2
            
            # Accumulate amounts for CGST and SGST
            if half_rate not in rate_amount_map:
                rate_amount_map[half_rate] = 0
            rate_amount_map[half_rate] += taxable_amount * (half_rate / 100)

        # Prepare the result list
        result = []
        for rate, amount in rate_amount_map.items():
            if int(rate) not in [6,9]:
                raise ValueError(f"ValidationError - Tally XML: Taxable Ledger details with Rate '{int(rate)}' were not found. Please proceed manually.")
            result.append({
                "Ledger Name": f"ITC CGST @ {int(rate)}%",
                "Debit / Credit":"Dr",
                "Amount": round(amount, 2)
            })
            result.append({
                "Ledger Name": f"ITC SGST @ {int(rate)}%",
                "Debit / Credit":"Dr",
                "Amount": round(amount, 2)
            })

        return result
    
    @staticmethod
    def MSConvertFinancialYear(financial_year):
        start_year, end_year_suffix = financial_year.split('-')
        end_year = start_year[:2] + end_year_suffix
        return f"{start_year}-{end_year}"



class CICDController_XML:
    
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _mStrFinancialYear = "2025-2026"
    
    
    @staticmethod
    def MSCalculateTDSFixedAmount(strPanNo, item_total, precisionDigits=2):
        dictTDSDetails = {
                "TDSRate": 0,
                "TDSAmount":0
            }
        if strPanNo and len(strPanNo) > 4:
            fourth_char = strPanNo[3].upper()  # Convert to uppercase for consistency
            if fourth_char in {'C', 'F'}:
                tds_rate = 2
            else:
                tds_rate = 1
        else:
            tds_rate = 2  # Default to 1% if PAN number is invalid
        
        dictTDSDetails["TDSRate"] = tds_rate
        
        # Calculate TDS amount
        tds_amount = round(item_total * (tds_rate / 100), precisionDigits)
        fractional_part = tds_amount - int(tds_amount)
        
        # Round to nearest integer based on fractional part
        if fractional_part >= 0.5:
            tds_amount = int(tds_amount) + 1
        else:
            tds_amount = int(tds_amount)
        
        dictTDSDetails["TDSAmount"] = tds_amount
        
        return dictTDSDetails

    @staticmethod
    def MSGetDebitLedgerName(dictExtractedData, keys_to_check, name):
        """
        Determines the ledger name based on the presence of a given name in specified keys.
        
        :param dictExtractedData: Dictionary containing extracted data.
        :param keys_to_check: List of keys to check for the name.
        :param name: String that should be present in the key's value.
        :return: Corresponding ledger name.
        """
        name = name.strip().upper()
        for key in keys_to_check:
            value = dictExtractedData.get(key, "").strip().upper()
            if name in value:
                CConcor_XML._msIsReimbursment = False
                return "ICD EXPENSE"
        CConcor_XML._msIsReimbursment = True
        return "REIMBURSEMENT EXPENSES -ICD"
    
    @staticmethod
    async def MSValidateAdditionalExcelTemplate(                # noqa: N802
        *,
        iUserId: int,
        strClientREQID: str,
        iDocID: int,
        AdditionalExcelTemplate: Any,
    ):
        """
        • Ensures the Additional Excel Template is present and of the right type.  
        • Converts it to dict-like form (`ReadExcelToDict`) if needed.  
        • On any validation failure, immediately logs the problem in
          AVRequestDetail and returns **None** so the caller can short-circuit.

        Returns
        -------
        dict | list | None
            Parsed template (dict or list) on success, otherwise None.
        """

        MISSING_MSG = (
            "An av_pegasus_additionaldetails.xlsx file is missing. "
            "Please attach it and try again."
        )
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False 
        try:
            # ------------------ 1) ABSENCE  ---------------------------
            if not AdditionalExcelTemplate:
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "ValidationError",
                        TracebackLogs           = MISSING_MSG,
                        strAccuVelocityComments = MISSING_MSG,
                    )
                return None

            # ------------------ 2) STRING PATH  -----------------------
            if isinstance(AdditionalExcelTemplate, str):
                return ReadExcelToDict(AdditionalExcelTemplate)

            # ------------------ 3) PRE-PARSED LIST / DICT -------------
            if isinstance(AdditionalExcelTemplate, (list, dict)):
                return AdditionalExcelTemplate

            # ------------------ 4) WRONG TYPE  ------------------------
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "ValidationError",
                    TracebackLogs           = MISSING_MSG,
                    strAccuVelocityComments = MISSING_MSG,
                )
            return None

        except Exception as exc:
            # Unexpected error while reading / parsing template
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = f"Error - {exc}",
                    strAccuVelocityComments = (
                        "Error - Tally XML: Unable to read the av_pegasus_additionaldetails.xlsx file. "
                        "The document could not be processed. Please record it manually in Tally."
                    ),
                )
            # Re-raise so the caller can decide what to do next
            raise
    
    @staticmethod
    def MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number: str, lsRequiredCols=[]):
        """
        Purpose: Returns additional invoice details from the provided template, ensuring required fields contain valid data.

        Inputs:
        - dictAdditionalTemplate: The template containing additional details.
        - invoice_number: The invoice number to match.
        - lsRequiredCols: List of required columns to validate.

        Output: A dictionary containing the additional details.

        Example: CICDController.MSFindAdditionalDetails(dictAdditionalTemplate, "INV123", ["Invoice No", "Job Number"])
        """
        for record in dictAdditionalTemplate:
            if record.get("Invoice No", "").strip() == invoice_number.strip():
                if not all(record.get(field, "").strip() for field in lsRequiredCols):
                    raise ValueError(f"ValidationError - Tally XML: The av_pegasus_additionaldetails.xlsx file is missing required columns {lsRequiredCols} for invoice '{invoice_number}.' Please add these columns and try again.")
                
                def parse_comma_separated(value: str):
                    parsed = [v.strip() for v in value.split(",") if v.strip()]
                    return parsed if parsed else []
                
                return {
                    "A/C Name": record.get("A/C Name", "").strip(),
                    "Invoice No": record.get("Invoice No", "").strip(),
                    "Job Number": parse_comma_separated(record.get("Job Number", "")),
                    "Container No": parse_comma_separated(record.get("Container No (Please specify if not provided)", "") or "")
                }
        raise ValueError(f"ValidationError - Tally XML: Mandatory details for invoice {invoice_number} not found in av_pegasus_additionaldetails.xlsx file. Please add it in Excel and update Tally if scanned document accuracy is an issue.")
    
class CHindTerminals_XML:

    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"
    _mPartyLedgerName = "HIND TERMINALS PVT. LTD."

    @staticmethod
    def MSFindDutiesLedger_Hind(dictExtractedData: dict) -> list:
        """
        Processes the ItemTable1 data and returns a list of tax ledger dictionaries.
        For each tax record (IGST, CGST, SGST), we create corresponding ledger entries with negative amounts.
        """
        tax_ledgers = []
        item_table = dictExtractedData.get("ItemTable1", [])
        
        for record in item_table:
            classification = record.get("Classification Of Service", "").upper()
            amount = record.get("Amount(Inr)", 0)
            
            if "IGST" in classification:
                try:
                    match = re.search(r'(\d+(\.\d+)?)%', classification)
                    rate = match.group(1) if match else None
                except Exception:
                    rate = ""
                if rate:
                    tax_ledgers.append({"ledger_name": f"ITC IGST @ {rate}%", "amount": -float(amount)})
            
            elif "CGST" in classification:
                try:
                    match = re.search(r'(\d+(\.\d+)?)%', classification)
                    rate = match.group(1) if match else None
                except Exception:
                    rate = ""
                if rate:
                    tax_ledgers.append({"ledger_name": f"ITC CGST @ {rate}%", "amount": -float(amount)})
            
            elif "SGST" in classification or "UTGST" in classification:
                try:
                    match = re.search(r'(\d+(\.\d+)?)%', classification)
                    rate = match.group(1) if match else None
                except Exception:
                    rate = ""
                if rate:
                    tax_ledgers.append({"ledger_name": f"ITC SGST @ {rate}%", "amount": -float(amount)})
        
        return tax_ledgers

    @staticmethod
    def MSGetRoundOff_Hind(dictExtractedData: dict, expense_amount: float, tax_ledgers: list) -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        In Pegasus, round-off is added as a separate ledger entry.
        """
        credit_amount = float(dictExtractedData.get("TotalInvoiceAmount(RoundOff)", 0))
        # Sum of expense (absolute value) and tax amounts (absolute, since they are negative)
        total_debit = abs(expense_amount) + sum(abs(item["Amount"]) for item in tax_ledgers)
        round_off = round(credit_amount - total_debit, 2)
        # Determine Debit/Credit flag based on sign
        flag = "Dr" if round_off >= 0 else "Cr"
        return {
            "Ledger Name": "ROUND OFF",
            "Debit / Credit": flag,
            "Amount": round_off
        }

    @staticmethod
    def MSGenerateCreditLedger_Hind(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries. The primary credit entry is taken from BillingPartyName.
        If a bill reference exists in the additional data, it is added as a bill allocation.
        If TDS is applicable (TDSAmount > 0), a separate TDS entry is generated.
        """
        credit_ledgers = []
        
        fTotalAmountWOTax = float(dictExtractedData.get("Amount(Before Tax)", 0))    # Total Amount without taxes 
        fTotalAmountWithTax = float(dictExtractedData.get("TotalInvoiceAmount(RoundOff)", 0)) 
        # If TDS is applicable, add a separate credit entry.
        dictTDSInfo =  CICDController_XML.MSCalculateTDSFixedAmount(dictExtractedData.get("Table"), fTotalAmountWOTax)   # TDS on 
        tds_amt = dictTDSInfo.get("TDSAmount")
        
        fCreditAmount = fTotalAmountWithTax-tds_amt
        main_credit = {
            "ledger_name": CHindTerminals_XML._mPartyLedgerName,
            "amount": fCreditAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNo"),
                "billtype": "New Ref",
                "amount": fCreditAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True,
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData, dictAdditionalInfo):
        invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
        
        iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
        
        # Get Job Numbers
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        
        # Get Account Name, prefer additional info over extracted data
        strAccountName = dictAdditionalInfo.get("A/C Name") or dictExtractedData.get("Consignee", "N/A")
        
        # Get Container Numbers, prefer additional info over extracted data
        lsContainerNumbers = dictAdditionalInfo.get("Container No") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
        
        # Ensure lists are converted to comma-separated strings
        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)
        
        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)
        
        strBillNumber = dictExtractedData.get("BL No / BE No", "N/A")
        credit_amount = float(dictExtractedData.get("Amount(Before Tax)", 0))
        
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(dictExtractedData.get("Table"), credit_amount)      # Todo: Verify How is it working
        
        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATE {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {strAccountName}. "
            f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBillNumber} "
            f"( TDS DED. ON RS. {credit_amount}@{dictTDSInfo.get('TDSRate')}% = {dictTDSInfo.get('TDSAmount')} ) "
            f"HIND TERMINALS PVT LTD"
        )
        
        return strNarration
        
    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, strClientREQID=None, iDocID=None, iUserId=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Hind Terminals.
        The generated XML will include the complete structure (expense ledger with cost centers,
        tax ledger entries, round-off, and credit ledger entries with bill allocations and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
           
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------
            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward
            # For example, additional details might include Container Number, Job Number, Bill Ref No, Cost Centers, Financial Year, etc.
            lsJobNumbers= dictAdditionalData.get("Job Number", "N/A")

            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CHindTerminals_XML.MSGetNarration(dictExtractedData=dictExtractedData, dictAdditionalInfo=dictAdditionalData)

            # ----------------------- Ledger Selection & Expense Ledger Entry -----------------
            # Select ledger name based on BillingPartyName:
            selected_ledger = CICDController_XML.MSGetDebitLedgerName(dictExtractedData=dictExtractedData, keys_to_check=["BillingPartyName"], name="FAIRDEAL")       

            expense_amt = -float(dictExtractedData.get("Amount(Before Tax)", 0.0))
            expense_entry = {
                "ledger_name": selected_ledger,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }
        
            # Add cost center allocation if cost centers exist
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers):
                    alloc_amt = base_amt
                    if idx == len(lsJobNumbers) - 1:
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CHindTerminals_XML.MSFindDutiesLedger_Hind(dictExtractedData)

            credit_amt = float(dictExtractedData.get("TotalInvoiceAmount(RoundOff)", 0))
            tax_total = sum(abs(item["amount"]) for item in tax_ledgers)
            round_off_value = round(credit_amt - (abs(expense_amt) + tax_total), 2)
            roundoff_entry = {
                "ledger_name": "ROUND OFF",
                "amount": abs(round_off_value) if round_off_value < 0 else -round_off_value,
                "is_deemed_positive": True ,
                "is_party_ledger": False
            }

            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CHindTerminals_XML.MSGenerateCreditLedger_Hind(dictExtractedData, dictAdditionalData)

            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            # Expense entry (with cost center allocations)
            ledger_entries.append(LedgerEntrySchema(**expense_entry))
            # Tax ledger entries
            for tax in tax_ledgers:
                # For tax entries, mark as debit
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            
            # Round-off entry
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            # Credit ledger entries (can be more than one)
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))

            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CHindTerminals_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerStateName", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{invoice_number} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="hind terminals", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward


class CAdaniHazira_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "ADANI HAZIRA PORT LIMITED"  # This will be used as the credit ledger

    
    @staticmethod
    def MSFindDutiesLedger_Adani(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("Table", [])

        for record in table:
            # Process IGST if applicable
            igst_rate = record.get("Rate(IGST%)", 0)
            if igst_rate and float(igst_rate) > 0:
                amount_igst = float(record.get("Amount(IGST)", 0))
                ledger_name = f"ITC IGST @ {igst_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

            # Process CGST if applicable
            cgst_rate = record.get("Rate(CGST%)", 0)
            if cgst_rate and float(cgst_rate) > 0:
                amount_cgst = float(record.get("Amount(CGST)", 0))
                ledger_name = f"ITC CGST @ {cgst_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst

            # Process SGST if applicable
            sgst_rate = record.get("Rate(SGST%)", 0)
            if sgst_rate and float(sgst_rate) > 0:
                amount_sgst = float(record.get("Amount(SGST)", 0))
                ledger_name = f"ITC SGST @ {sgst_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": amount} for name, amount in tax_ledgers_dict.items()]
        
        return tax_ledgers


    @staticmethod
    def MSGetRoundOff_Adani(dictExtractedData: dict, expense_amount: float, tax_ledgers: list) -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """
        # For Adani Hazira, assume credit amount is TotalInvoiceValue(InFigure)
        credit_amount = float(dictExtractedData.get("TotalTaxableAmount", 0))
        total_debit = abs(expense_amount) + sum(abs(item["amount"]) for item in tax_ledgers)
        round_off = round(credit_amount - total_debit, 2)
        flag = "Dr" if round_off >= 0 else "Cr"
        return {
            "ledger_name": "ROUND OFF",
            "amount": round_off,
            "is_deemed_positive": True if round_off >= 0 else False,
            "is_party_ledger": False
        }


    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        
        credit_ledgers = []
        
        fTotalAmountWOTax = float(dictExtractedData.get("TotalTaxableAmount", 0))
        fTotalTax = float(dictExtractedData.get("TotalTaxValue(InFigure)", 0))
        fTotalAmount = fTotalAmountWOTax + fTotalTax
        
        # (Assuming you have a function similar to MSCalculateTDSFixedAmount)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("SellerPANNo"), 
                                                                    fTotalAmountWOTax)
        
        tds_amt = dictTDSInfo.get("TDSAmount", 0)
        fTotalCreditLedgerAmount =  float(fTotalAmount - tds_amt)
        main_credit = {
            "ledger_name": CAdaniHazira_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNumber", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        # If TDS is applicable, add a separate entry.
        
        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        invoice_number = dictExtractedData.get("InvoiceNumber", "").strip()
        iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
        
        # Retrieve Job Numbers and Container Numbers from additional data
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
        
        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)
        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)
        
        strBLNo = dictExtractedData.get("BLNo", "N/A")
        
        # Optionally, include customer details
        customer = dictAdditionalInfo.get("A/C Name") or dictExtractedData.get("ImporterName", "N/A")
        fTotalAmountWOTaxes = float(dictExtractedData.get("TotalTaxableAmount", 0))
        # If TDS is applicable, retrieve TDS info (optional)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("SellerPANNo"), 
                                                                    fTotalAmountWOTaxes)        # TODO: Verify working as expected
        
        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
            f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
            f"( TDS DED. ON RS. {fTotalAmountWOTaxes} "
            f"@{dictTDSInfo.get('TDSRate','')}% = {dictTDSInfo.get('TDSAmount','')} ) "
            f"ADANI HAZIRA PORT LIMITED"
        )
        return strNarration

    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------
            invoice_number = dictExtractedData.get("InvoiceNumber", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # For example, additional data might include Job Number and Container No
            lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            
            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CAdaniHazira_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Ledger Selection & Expense Ledger Entry -----------------
            # For expense ledger, if CustomerLegalName equals "FAIRDEAL INTERNATIONAL" then use "ICD EXPENSE",
            # else "Reimburshment" (as in Hind Terminals)
            selected_expense_ledger = CICDController_XML.MSGetDebitLedgerName(dictExtractedData=dictExtractedData, keys_to_check=["CustomerLegalName"], name="FAIRDEAL")
            
            # For Adani Hazira, expense amount is computed as:
            # Expense = TotalInvoiceValue(InFigure) - TotalTaxValue(InFigure)
            expense_amt = - float(dictExtractedData.get("TotalTaxableAmount", 0))
            expense_entry = {
                "ledger_name": selected_expense_ledger,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }
            
            # Optionally add cost center allocation if additional data provides job numbers
            if lsJobNumbers:
                # Here we assume the additional data’s Job Number list acts as cost centers
                # For simplicity, we use them directly.
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # You may adjust this
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CAdaniHazira_XML.MSFindDutiesLedger_Adani(dictExtractedData)
            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            fTotalAmountWOTax = float(dictExtractedData.get("TotalTaxableAmount", 0))
            fTotalTax = float(dictExtractedData.get("TotalTaxValue(InFigure)", 0))
            fTotalAmount = fTotalAmountWOTax + fTotalTax
            round_off_value = round(fTotalAmount - (abs(fTotalAmountWOTax) + fTotalTax), 2)
            roundoff_entry = {
                "ledger_name": "ROUND OFF",
                "amount": round_off_value,
                "is_deemed_positive": True if round_off_value >= 0 else False,
                "is_party_ledger": False
            }
            
            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CAdaniHazira_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            ledger_entries.append(LedgerEntrySchema(**expense_entry))
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CAdaniHazira_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('InvoiceNumber','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="adani hazira", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward



class CGoodRich_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "GOODRICH MARITIME PVT. LTD."  # This will be used as the credit ledger

    # Vendor Specific Ledger Amounts, Need to calculate Round off value base on this formula : Sum of Credits Ledger i.e. (GOODRICH MARITIME PVT. LTD. + TDS ON CONTRACT PAYABLE (FOR COMPANY)) --- subtract by debit group (Ocean Freight Expenses + SHIPPING EXPENSES + THC Expenses+ BL Expenses + ITC CGST @ 2.5% + ITC SGST @ 2.5% + ITC CGST @ 9.0% + ITC SGST @ 9.0% ) , NOTE: Ignore Taxable Ledger name we are going to sum of taxable amounts it is irespective of taxable rates

    _msiOceanFreightExpense = 0
    _msiShippingExpenses = 0
    _msiTHCExpenses = 0
    _msiBLExpenses = 0
    _msiTaxableAmount = 0
    _msiRoundOffAmount = 0
    _msiGoodRichAmount = 0
    _msiTDSAmount = 0
    
    @staticmethod
    def MSFindDutiesLedger_GoodRich(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("ItemTable1", [])

        for record in table:
        
            # Process CGST if applicable
            cgst_rate = record.get("RATE", 0)
            if cgst_rate and float(cgst_rate) > 0:
                amount_cgst = float(record.get("CGST", 0))
                # Remove trailing .0 for integer values
                ledger_name = f"ITC CGST @ {float(cgst_rate):g}%"  # Use 'g' formatting to avoid trailing zeros
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst
                
            # Process SGST if applicable
            sgst_rate = record.get("RATE", 0)
            if sgst_rate and float(sgst_rate) > 0:
                amount_sgst = float(record.get("SGST", 0))
                # Remove trailing .0 for integer values
                ledger_name = f"ITC SGST @ {float(sgst_rate):g}%"  # Use 'g' formatting to avoid trailing zeros
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": amount} for name, amount in tax_ledgers_dict.items()]
        for taxDetail in tax_ledgers:
            CGoodRich_XML._msiTaxableAmount += abs(taxDetail["amount"])  # add every tax rates amount
        return tax_ledgers



    @staticmethod
    def generate_expense_entries(item_table, expenses_dict, lsJobNumbers=None):
            # Create a dictionary to store summed amounts by ledger name
        ledger_expenses = {}

        for item in item_table:
            description = item.get("Description", "").lower().strip()  # Normalize description
            taxable_amount = item.get("TaxableAmount", 0.0)

            # Raising Execption if amount in bill is negative TODO: temporary solution please discard after proper legitimate solution
            if taxable_amount < 0:
                raise HTTPException("Invalid Entry: The amount entered in the bill cannot be negative. Please verify the transaction details to ensure accuracy.")

            # Iterate through keys in expenses_dict and check if the description matches any key
            ledger_name = None
            for key in expenses_dict.keys():
                if key.lower().strip() in description:  # Check if the normalized description is part of the normalized key
                    ledger_name = expenses_dict[key]
                    break  # Stop after finding the first match

            if not ledger_name:
                ledger_name = "SHIPPING EXPENSES"  # Default to "SHIPPING EXPENSES" if no match is found SHIPPING EXPENSES

            # Now process the ledger name and taxable amount as before..
            if ledger_name:
                # If a match is found, sum the taxable amounts for that ledger name
                if ledger_name not in ledger_expenses:
                    ledger_expenses[ledger_name] = 0
                ledger_expenses[ledger_name] += taxable_amount

        # Now create the expense entry for each ledger group
        expense_entries = []
        for ledger_name, total_amount in ledger_expenses.items():
            expense_amt = -total_amount  # Assuming you want to apply negative sign to the expense
            if ledger_name == "Ocean Freight Expenses":
                CGoodRich_XML._msiOceanFreightExpense = total_amount
            elif ledger_name == "SHIPPING EXPENSES":
                CGoodRich_XML._msiShippingExpenses = total_amount
            elif ledger_name == "THC Expenses":
                CGoodRich_XML._msiTHCExpenses = total_amount
            elif ledger_name == "BL Expenses":
                CGoodRich_XML._msiBLExpenses = total_amount
            
            expense_entry = {
                "ledger_name": ledger_name,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }

            # Optionally add cost center allocation if job numbers are provided
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # Adjust as needed
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            expense_entries.append(expense_entry)

        return expense_entries
    


    @staticmethod
    def MSGetRoundOff_GoodRich() -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """
        # FORMULA : Sum of Credit Group - Sum of Debit Group
        round_off = round((CGoodRich_XML._msiGoodRichAmount + CGoodRich_XML._msiTDSAmount) - (CGoodRich_XML._msiOceanFreightExpense + CGoodRich_XML._msiShippingExpenses + CGoodRich_XML._msiTHCExpenses + CGoodRich_XML._msiBLExpenses + CGoodRich_XML._msiTaxableAmount),2)
        

        # Convert to Decimal for precision control
        iDecimalRoundOff = Decimal(round_off).quantize(Decimal('0.01'), rounding=ROUND_DOWN) 
        # Convert back to float
        iPreciseRoundOFF = float(iDecimalRoundOff) 
        # NOTE: is_deemed_positive = "Dr" if    round_off >= 0 else "Cr"
        CGoodRich_XML._msiRoundOffAmount = iPreciseRoundOFF
        return {
            "ledger_name": "ROUND OFF",
            "amount": -iPreciseRoundOFF  if iPreciseRoundOFF >= 0 else iPreciseRoundOFF ,
            "is_deemed_positive": True if iPreciseRoundOFF >= 0 else False,
            "is_party_ledger": False
        }


    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        FinalSummary=dictExtractedData.get("FinalSummary",{})
        credit_ledgers = []
        fTotalAmountWOTax = float(FinalSummary.get("TaxableAmount", 0))
        
        fTotalAmountWOTax = float(FinalSummary.get("TaxableAmount", 0))
        fTotalTax = float(float(FinalSummary.get("SGST", 0))+float(FinalSummary.get("CGST", 0)))
        fTotalAmount = fTotalAmountWOTax + fTotalTax
        
        # (Assuming you have a function similar to MSCalculateTDSFixedAmount)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("PANNo"), 
                                                                    fTotalAmountWOTax)
        
        tds_amt = dictTDSInfo.get("TDSAmount", 0)
        # Compute total credit ledger amount
        fTotalCreditLedgerAmount = fTotalAmount - tds_amt  
        # Ceil to the next integer
        fTotalCreditLedgerAmount = math.ceil(fTotalCreditLedgerAmount)  
        # Ensure 2 decimal places using Decimal
        fTotalCreditLedgerAmount = float(Decimal(fTotalCreditLedgerAmount).quantize(Decimal('0.01'), rounding=ROUND_UP))  
        

        tds_amt=round(tds_amt)
        tds_amt=float(f"{tds_amt:.2f}")
        
        
        
        CGoodRich_XML._msiGoodRichAmount = fTotalCreditLedgerAmount

        main_credit = {
            "ledger_name": CGoodRich_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNo.", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        # If TDS is applicable, add a separate entry.
        CGoodRich_XML._msiTDSAmount = tds_amt

        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        invoice_number = dictExtractedData.get("InvoiceNo.", "").strip()
        iInvDate = dictExtractedData.get("InvoiceDate", "")
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d.%m.%y")
        
        # Retrieve Job Numbers and Container Numbers from additional data
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("ContainerNo's", "N/A")
        
        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)
        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)
        
        strBLNo = dictExtractedData.get("B/LNo", "N/A")
        
        # Optionally, include customer details
        customer = dictAdditionalInfo.get("A/C Name") or dictExtractedData.get("Shipper", "N/A")  # Shiped By Whom Name Mention it can be in Invoice or given in Excel Additional File
        
        # Item Total Amount with Tax
        fTotalAmountWOTaxes =  dictExtractedData["FinalSummary"]["FinalTotalAmountINR"] #  + dictExtractedData["FinalSummary"]["FinalTotalAmountINR"] # float(dictExtractedData.get("TotalTaxableAmount", 0))
        # If TDS is applicable, retrieve TDS info (optional)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    strPanNo=dictExtractedData.get("PANNo"), # NOTE: Please Change Pan Number Key As Per API Response 
                                                                    item_total=fTotalAmountWOTaxes)        # TODO: Verify working as expected
        
        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
            f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
            f"( TDS DED. ON RS. {CGoodRich_XML._msiGoodRichAmount } "
            f"@{dictTDSInfo.get('TDSRate','')}% = {CGoodRich_XML._msiTDSAmount} ) "
            f"GOODRICH MARITIME PVT. LTD."
        )
        return strNarration

    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False 
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
        
            # ----------------------- Extract Additional Details -----------------------------
            invoice_number = dictExtractedData.get("InvoiceNo.", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: Unable to process your document. Please enter it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            

            expenses_dict = {
                "OCEAN": "Ocean Freight Expenses",
                "THC": "THC Expenses",
                "SEAL CHARGES": "SHIPPING EXPENSES",
                "MUC CHARGES": "SHIPPING EXPENSES",
                "SEAWAY BILL CHARGES": "BL Expenses",
                "BL": "BL Expenses"
            }
            
            item_table =dictExtractedData.get("ItemTable1",[{}])
            # lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            lsJobNumbers =  dictAdditionalData.get("Job Number", "N/A")
            
            expense_entry = CGoodRich_XML.generate_expense_entries(item_table, expenses_dict, lsJobNumbers)
            
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CGoodRich_XML.MSFindDutiesLedger_GoodRich(dictExtractedData)
            
            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CGoodRich_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            strNarration = await CGoodRich_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            roundoff_entry = CGoodRich_XML.MSGetRoundOff_GoodRich()
            if CGoodRich_XML._msiRoundOffAmount > 2 or CGoodRich_XML._msiRoundOffAmount < -2:
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "Skipped",
                        TracebackLogs           = "err_msg",
                        strAccuVelocityComments = "Error - Tally XML: The extracted document shows unequal Credit and Debit amounts. This mismatch likely stems from poor document quality during extraction."
                    )
                return None  # propagate the same ValueError upward
            
            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            for expense_entry in expense_entry:
                ledger_entries.append(LedgerEntrySchema(**expense_entry))
            
            
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CGoodRich_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('InvoiceNo.','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="goodrich maritime private limited", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward

class CMSC_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "MSC MEDITERRANEAN SHIPPING COMPANY S.A."  # This will be used as the credit ledger
    
    _msiOceanFreightExpense = 0
    _msiShippingExpenses = 0
    _msiTHCExpenses = 0
    _msiBLExpenses = 0
    _msiTaxableAmount = 0
    _msiRoundOffAmount = 0
    _msiMSCAmount = 0
    _msiTDSAmount = 0
    
    @staticmethod
    def MSFindDutiesLedger_MSC(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("ItemTable1", [])

        for record in table:
            def format_rate(rate):
                rate = float(rate)
                return int(rate) if rate.is_integer() else rate

            # Process IGST if applicable
            igst_rate = record.get("IGSTRate", 0)
            if igst_rate and float(igst_rate) > 0:
                amount_igst = float(record.get("IGSTAmount", 0))
                formatted_rate = format_rate(igst_rate)
                ledger_name = f"ITC IGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

            # Process CGST if applicable
            cgst_rate = record.get("CGSTRate", 0)
            if cgst_rate and float(cgst_rate) > 0:
                amount_cgst = float(record.get("CGSTAmount", 0))
                formatted_rate = format_rate(cgst_rate)
                ledger_name = f"ITC CGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst  

            # Process SGST if applicable
            sgst_rate = record.get("SGSTRate", 0)
            if sgst_rate and float(sgst_rate) > 0:
                amount_sgst = float(record.get("SGSTAmount", 0))
                formatted_rate = format_rate(sgst_rate)
                ledger_name = f"ITC SGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": amount} for name, amount in tax_ledgers_dict.items()]
        for taxDetail in tax_ledgers:
            CMSC_XML._msiTaxableAmount += abs(taxDetail["amount"]) # add every tax rates amount
        return tax_ledgers

    @classmethod
    def reset_expenses(cls):
        cls._msiOceanFreightExpense = 0
        cls._msiShippingExpenses = 0
        cls._msiTHCExpenses = 0
        cls._msiBLExpenses = 0
        cls._msiTaxableAmount = 0
        cls._msiRoundOffAmount = 0
        cls._msiMSCAmount = 0
        cls._msiTDSAmount = 0

    @staticmethod
    def generate_expense_entries(item_table, expenses_dict, lsJobNumbers=None):
        # Create a dictionary to store summed amounts by ledger name
        ledger_expenses = {}

        for item in item_table:
            description = item.get("Description", "").lower().strip()  # Normalize description
            taxable_amount = item.get("TaxableAmount", 0.0)

            # For Cases where the amount can't be found from Key Taxable amount
            if not taxable_amount:
                taxable_amount = item.get("INR", 0.0)


            # Iterate through keys in expenses_dict and check if the description matches any key
            ledger_name = None
            for key in expenses_dict.keys():
                if key.lower().strip() in description:  # Check if the normalized description is part of the normalized key
                    ledger_name = expenses_dict[key]
                    break  # Stop after finding the first match

            if not ledger_name:
                ledger_name = "SHIPPING EXPENSES"  # Default to "Shipping Expenses" if no match is found

            # Now process the ledger name and taxable amount as before..
            
            if ledger_name:
                # If a match is found, sum the taxable amounts for that ledger name
                if ledger_name not in ledger_expenses:
                    ledger_expenses[ledger_name] = 0
                ledger_expenses[ledger_name] += taxable_amount

        # Now create the expense entry for each ledger group
        expense_entries = []
        for ledger_name, total_amount in ledger_expenses.items():
            expense_amt = -total_amount  # Assuming you want to apply negative sign to the expense
            if ledger_name == "Ocean Freight Expenses":
                CMSC_XML._msiOceanFreightExpense = total_amount
            elif ledger_name == "SHIPPING EXPENSES":
                CMSC_XML._msiShippingExpenses = total_amount
            elif ledger_name == "THC Expenses":
                CMSC_XML._msiTHCExpenses = total_amount
            elif ledger_name == "BL Expenses":
                CMSC_XML._msiBLExpenses = total_amount
        # for ledger_name, total_amount in ledger_expenses.items():
        #     expense_amt = -total_amount  # Assuming you want to apply negative sign to the expense

            expense_entry = {
                "ledger_name": ledger_name,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }

            # Optionally add cost center allocation if job numbers are provided
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # Adjust as needed
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            expense_entries.append(expense_entry)

        return expense_entries




    @staticmethod
    def MSGetRoundOff_MSC() -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """
        round_off = round((CMSC_XML._msiMSCAmount) - (CMSC_XML._msiOceanFreightExpense + CMSC_XML._msiShippingExpenses + CMSC_XML._msiTHCExpenses + CMSC_XML._msiBLExpenses + CMSC_XML._msiTaxableAmount),2)
        

        # Convert to Decimal for precision control
        iDecimalRoundOff = Decimal(round_off).quantize(Decimal('0.01'), rounding=ROUND_DOWN) 
        # Convert back to float
        iPreciseRoundOFF = float(iDecimalRoundOff) 
        # NOTE: is_deemed_positive = "Dr" if    round_off >= 0 else "Cr"
        CMSC_XML._msiRoundOffAmount = iPreciseRoundOFF
        return {
            "ledger_name": "ROUND OFF",
            "amount": -iPreciseRoundOFF  if iPreciseRoundOFF >= 0 else iPreciseRoundOFF ,
            "is_deemed_positive": True if iPreciseRoundOFF >= 0 else False,
            "is_party_ledger": False
        }




    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        FinalSummary=dictExtractedData.get("FinalSummary",{}) # dictExtractedData.get("FinalSummary",{}).get("TaxableValue")
        credit_ledgers = []
        fTotalAmount = FinalSummary.get("FinalTotalAmountINR", 0) # fTotalAmountWOTax + fTotalTax
        
        CMSC_XML._msiMSCAmount =  fTotalAmount
        main_credit = {
            "ledger_name": CMSC_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": CMSC_XML._msiMSCAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNo", "").strip(),
                "billtype": "New Ref",
                "amount": CMSC_XML._msiMSCAmount
            }
        }
        credit_ledgers.append(main_credit)
    
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
        iInvDate = dictExtractedData.get("InvoiceDate", "")
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d.%m.%y")
        
        # Retrieve Job Numbers and Container Numbers from additional data
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
        
        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)
        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)
        
        strBLNo = dictExtractedData.get("B/LNo", "N/A")
        
        # Optionally, include customer details
        customer = dictAdditionalInfo.get("A/C Name") # or dictExtractedData.get("ImporterName", "N/A")
        # fTotalAmountWOTaxes = float(dictExtractedData.get("TotalTaxableAmount", 0))
        # If TDS is applicable, retrieve TDS info (optional)
        # dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
        #                                                             dictExtractedData.get("PAN"), 
        #                                                             fTotalAmountWOTaxes)        # TODO: Verify working as expected
        
        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
            f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
            # f"( TDS DED. ON RS. {fTotalAmountWOTaxes} "
            # f"@{dictTDSInfo.get('TDSRate','')}% = {dictTDSInfo.get('TDSAmount','')} ) "
            # f"ADANI HAZIRA PORT LIMITED"
        )
        return strNarration

    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------
            CMSC_XML.reset_expenses()

            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: Unable to process your document. Please enter it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CMSC_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
        
            expenses_dict = {
                "SEAFREIGHT": "Ocean Freight Expenses",
                "CARRIER SECURITY FEE":"Ocean Freight Expenses",   # AS PER DISCUSSION WITH AASHISH ON 03-04-2025 AT 1:40 PM
                "EMISSIONS TRADING": "Ocean Freight Expenses",
                "FUEL SURCHARGE": "Ocean Freight Expenses",
                "FUEL EU SURCHARGE": "Ocean Freight Expenses",
                "SEAWAY AMENDMENT FEE":"BL Expenses",
                "TERMINAL HANDLING": "THC Expenses",
                "EQUIPMENT SURCHARGE":"THC Expenses",
                "EQUIPMENT SURCHARGE-INTERMODAL": "THC Expenses",
                "EXPORT CONTAINER FACILITATION AND ADMIN": "BL Expenses",
                "EXPORT CONTAINER FACILITATION": "SHIPPING EXPENSES",
                "CARGO DATA DECLARATION": "SHIPPING EXPENSES",
                "SEAL": "SHIPPING EXPENSES",
                "PRECARRIAGE": "SHIPPING EXPENSES"
            }

            
            item_table =dictExtractedData.get("ItemTable1",[{}])
            # lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            lsJobNumbers =  dictAdditionalData.get("Job Number", "N/A")
            
            expense_entry = CMSC_XML.generate_expense_entries(item_table, expenses_dict, lsJobNumbers)
            
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CMSC_XML.MSFindDutiesLedger_MSC(dictExtractedData) 

            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CMSC_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            roundoff_entry = CMSC_XML.MSGetRoundOff_MSC()
            if CMSC_XML._msiRoundOffAmount > 2 or CMSC_XML._msiRoundOffAmount < -2:
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "Skipped",
                        TracebackLogs           = "err_msg",
                        strAccuVelocityComments = "Error - Tally XML: The extracted document shows unequal Credit and Debit amounts. This mismatch likely stems from poor document quality during extraction."
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            for expense_entry in expense_entry:
                ledger_entries.append(LedgerEntrySchema(**expense_entry))
            
            
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CMSC_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('InvoiceNo','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="msc agency", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)

            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward

class CKotak_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "KOTAK GLOBAL LOGISTICS PVT LTD"  # This will be used as the credit ledger

    _msiOceanFreightExpense = 0
    _msiShippingExpenses = 0
    _msiTHCExpenses = 0
    _msiBLExpenses = 0
    _msiTaxableAmount = 0
    _msiRoundOffAmount = 0
    _msiKotakAmount = 0
    _msiTDSAmount = 0    
    
    
    
    @staticmethod
    def MSFindDutiesLedger_Kotak(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        FinalSummary=dictExtractedData.get("FinalSummary",{})
        
        igst_rate = FinalSummary.get("IGSTRate", 0)
        
        if igst_rate and float(igst_rate) > 0:
            amount_igst = float(FinalSummary.get("IGSTAmount", 0))
            formatted_rate = CPegasus_XML.MSFormatTaxRate(igst_rate)
            ledger_name = f"ITC IGST @ {formatted_rate}%"
            tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst


        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": amount} for name, amount in tax_ledgers_dict.items()]
        for taxDetail in tax_ledgers:
            CKotak_XML._msiTaxableAmount += abs(taxDetail["amount"]) # add every tax rates amount
        return tax_ledgers
        # return tax_ledgers

    @staticmethod
    def generate_expense_entries(item_table, expenses_dict, lsJobNumbers=None):
            # Create a dictionary to store summed amounts by ledger name
        ledger_expenses = {}

        for item in item_table:
            description = item.get("DescriptionOfServices", "").lower().strip()  # Normalize description
            taxable_amount = item.get("Amount", 0.0)

            # Iterate through keys in expenses_dict and check if the description matches any key
            ledger_name = None
            for key in expenses_dict.keys():
                if key.lower().strip() in description.lower():  # Check if the normalized description is part of the normalized key
                    ledger_name = expenses_dict[key]
                    break  # Stop after finding the first match

            if not ledger_name:
                ledger_name = "SHIPPING EXPENSES"  # Default to "Shipping Expenses" if no match is found SHIPPING EXPENSES

            # Now process the ledger name and taxable amount as before..
            if ledger_name:
                # If a match is found, sum the taxable amounts for that ledger name
                if ledger_name not in ledger_expenses:
                    ledger_expenses[ledger_name] = 0
                ledger_expenses[ledger_name] += taxable_amount

        # Now create the expense entry for each ledger group
        expense_entries = []
        for ledger_name, total_amount in ledger_expenses.items():
            expense_amt = -total_amount  # Assuming you want to apply negative sign to the expense
            if ledger_name == "Ocean Freight Expenses":
                CKotak_XML._msiOceanFreightExpense = total_amount
            elif ledger_name == "SHIPPING EXPENSES":
                CKotak_XML._msiShippingExpenses = total_amount
            elif ledger_name == "THC Expenses":
                CKotak_XML._msiTHCExpenses = total_amount
            elif ledger_name == "BL Expenses":
                CKotak_XML._msiBLExpenses = total_amount

            expense_entry = {
                "ledger_name": ledger_name,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }

            # Optionally add cost center allocation if job numbers are provided
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # Adjust as needed
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            expense_entries.append(expense_entry)

        return expense_entries
    


    @staticmethod
    def MSGetRoundOff_kotak() -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """

        round_off = ((CKotak_XML._msiKotakAmount+ CKotak_XML._msiTDSAmount) - (CKotak_XML._msiOceanFreightExpense + CKotak_XML._msiShippingExpenses + CKotak_XML._msiTHCExpenses + CKotak_XML._msiBLExpenses + CKotak_XML._msiTaxableAmount))
        

        # Convert to Decimal for precision control
        iDecimalRoundOff = Decimal(round_off).quantize(Decimal('0.01'), rounding=ROUND_UP) 
        # Convert back to float
        iPreciseRoundOFF = float(iDecimalRoundOff) 
        # NOTE: is_deemed_positive = "Dr" if    round_off >= 0 else "Cr"
        CKotak_XML._msiRoundOffAmount = iPreciseRoundOFF
        return {
            "ledger_name": "ROUND OFF",
            "amount": -iPreciseRoundOFF  if iPreciseRoundOFF >= 0 else iPreciseRoundOFF ,
            "is_deemed_positive": True if iPreciseRoundOFF >= 0 else False,
            "is_party_ledger": False
        }



    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        FinalSummary=dictExtractedData.get("FinalSummary",{})
        credit_ledgers = []
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        fTotalTax = float(FinalSummary.get("FinalTotalTaxAmount", 0))
        fTotalAmount = fTotalAmountWOTax + fTotalTax
        
        # (Assuming you have a function similar to MSCalculateTDSFixedAmount)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("CompanysPAN"), 
                                                                    fTotalAmountWOTax)
        
        tds_amt = dictTDSInfo.get("TDSAmount", 0)
        fTotalCreditLedgerAmount = fTotalAmount - tds_amt  
        
        # Ceil to the next integer
        fTotalCreditLedgerAmount = round(fTotalCreditLedgerAmount)  
        # Ensure 2 decimal places using Decimal
        fTotalCreditLedgerAmount = float(Decimal(fTotalCreditLedgerAmount).quantize(Decimal('0.01'), rounding=ROUND_UP))  

        tds_amt=round(tds_amt)
        tds_amt=float(f"{tds_amt:.2f}")
        CKotak_XML._msiTDSAmount=tds_amt


        
        
        
        # fTotalCreditLedgerAmount=float(f"{fTotalCreditLedgerAmount:.2f}")
        
        # tds_amt = round(tds_amt)
        # tds_amt=float(f"{tds_amt:.2f}")
        CKotak_XML._msiKotakAmount = fTotalCreditLedgerAmount
        
        
        main_credit = {
            "ledger_name": CKotak_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("CreditNoteNo/InvoiceNo", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        # If TDS is applicable, add a separate entry.
        
        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        invoice_number = dictExtractedData.get("CreditNoteNo/InvoiceNo", "").strip()
        iInvDate = dictExtractedData.get("CreditDate/InvoiceDate", "")
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d.%m.%y")
        
        # Retrieve Job Numbers and Container Numbers from additional data
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
        
        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)
        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)
        
        strBLNo = dictExtractedData.get("BLNos", "N/A")
        
        # Optionally, include customer details
        customer = dictAdditionalInfo.get("A/C Name") or dictExtractedData.get("ImporterName", "N/A")
        fTotalAmountWOTaxes = float(dictExtractedData.get("FinalSummary").get("TaxableValue", 0)) 
        # If TDS is applicable, retrieve TDS info (optional)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("CompanysPAN"), 
                                                                    fTotalAmountWOTaxes)        # TODO: Verify working as expected
        
        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
            f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
            f"( TDS DED. ON RS. {fTotalAmountWOTaxes} "
            f"@{dictTDSInfo.get('TDSRate','')}% = {dictTDSInfo.get('TDSAmount','')} ) "
            f"KOTAK GLOBAL LOGISTICS PVT LTD"
        )
        return strNarration

    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------
            invoice_number = dictExtractedData.get("CreditNoteNo/InvoiceNo", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # For example, additional data might include Job Number and Container No
        
            
            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = dictExtractedData.get("CreditDate/InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            dictAdditionalData.get("")
            strNarration = await CKotak_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
            expenses_dict = {
                "Steamer Freight": "Ocean Freight Expenses",
                "THC": "THC Expenses",
                "Documentation": "BL Expenses",
                "Seaway BL": "BL Expenses",
                
                "Seal": "SHIPPING EXPENSES",
                "Facility": "SHIPPING EXPENSES",
                "Rail Recovery": "SHIPPING EXPENSES",
                "INDIA LEVY OF CESS": "SHIPPING EXPENSES"
            }
            
            item_table =dictExtractedData.get("ItemTable",[{}])
            # lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            lsJobNumbers =  dictAdditionalData.get("Job Number", "N/A")
            
            expense_entry = CKotak_XML.generate_expense_entries(item_table, expenses_dict, lsJobNumbers)
            
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CKotak_XML.MSFindDutiesLedger_Kotak(dictExtractedData)
            
            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CKotak_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            roundoff_entry = CKotak_XML.MSGetRoundOff_kotak()
            if CKotak_XML._msiRoundOffAmount > 2 or CKotak_XML._msiRoundOffAmount < -2:
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "Skipped",
                        TracebackLogs           = f"Round OFF Amount Calculation Error: CKotak_XML._msiRoundOffAmount : {CKotak_XML._msiRoundOffAmount}, dictExtractedData: {dictExtractedData}, AdditionalExcelTemplate: {AdditionalExcelTemplate}, iUserId: {iUserId}, strClientREQID: {strClientREQID}, iDocID: {iDocID}",
                        strAccuVelocityComments = "Error - Tally XML: The extracted document shows unequal Credit and Debit amounts. This mismatch likely stems from poor document quality during extraction.",
                        EstAccountantTimeSaved  = strTimeSaved
                    )
                return None  # propagate the same ValueError upward
            
            # ----------------------- Assemble All Ledger Entries ----------------------------

            
            
            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            for expense_entry in expense_entry:
                ledger_entries.append(LedgerEntrySchema(**expense_entry))
            
            
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CKotak_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGST/UIN", "").strip(),
                    state_name=dictExtractedData.get("SellerStateName", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('CreditNoteNo/InvoiceNo','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="kotak global logistics pvt ltd", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally. Unknown Error in Tally XML Creation"
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward

class CUnited_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "UNITED LINER SHIPPING SERVICES LLP"  # This will be used as the credit ledger

    _msiOceanFreightExpense = 0
    _msiShippingExpenses = 0
    _msiTHCExpenses = 0
    _msiBLExpenses = 0
    _msiTaxableAmount = 0
    _msiRoundOffAmount = 0
    _msUnitedAmount = 0
    _msiTDSAmount = 0    
    
    @classmethod
    def reset(cls):
        cls._msiOceanFreightExpense = 0
        cls._msiShippingExpenses = 0
        cls._msiTHCExpenses = 0
        cls._msiBLExpenses = 0
        cls._msiTaxableAmount = 0
        cls._msiRoundOffAmount = 0
        cls._msUnitedAmount = 0
        cls._msiTDSAmount = 0
        
    @staticmethod
    def MSFindDutiesLedger_United(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("ItemTable1", [])

        for record in table:
            def format_rate(rate):
                rate = float(rate)
                return int(rate) if rate.is_integer() else rate

            # Process IGST if applicable
            igst_rate = record.get("IGSTRate", 0)
            if igst_rate and float(igst_rate) > 0:
                amount_igst = round(float(record.get("IGSTAmount", 0)),2)
                formatted_rate = format_rate(igst_rate)
                ledger_name = f"ITC IGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

            # Process CGST if applicable
            cgst_rate = record.get("CGSTRate", 0)
            if cgst_rate and float(cgst_rate) > 0:
                amount_cgst = round(float(record.get("CGSTAmount", 0)),2)
                formatted_rate = format_rate(cgst_rate)
                ledger_name = f"ITC CGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst  

            # Process SGST if applicable
            sgst_rate = record.get("SGSTRate", 0)
            if sgst_rate and float(sgst_rate) > 0:
                amount_sgst = round(float(record.get("SGSTAmount", 0)),2)
                formatted_rate = format_rate(sgst_rate)
                ledger_name = f"ITC SGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": round(amount,2)} for name, amount in tax_ledgers_dict.items()]
        for taxDetail in tax_ledgers:
            CUnited_XML._msiTaxableAmount += abs(taxDetail["amount"]) # add every tax rates amount
        return tax_ledgers

    @staticmethod
    def generate_expense_entries(item_table, expenses_dict, lsJobNumbers=None):
            # Create a dictionary to store summed amounts by ledger name
        ledger_expenses = {}

        for item in item_table:
            description = item.get("Description", "").lower().strip()  # Normalize description
            taxable_amount = item.get("TaxableAmount", 0.0)
            
            # cleaned_description = re.sub(r'[^a-z0-9 ]+', '', description.lower())

            # Iterate through keys in expenses_dict and check if the description matches any key
            ledger_name = None
            for key in expenses_dict.keys():
                if key.lower().strip() in description:  # Check if the normalized description is part of the normalized key
                    ledger_name = expenses_dict[key]
                    break  # Stop after finding the first match

            if not ledger_name:
                ledger_name = "SHIPPING EXPENSES"  # Default to "Shipping Expenses" if no match is found SHIPPING EXPENSES

            # Now process the ledger name and taxable amount as before..
            if ledger_name:
                # If a match is found, sum the taxable amounts for that ledger name
                if ledger_name not in ledger_expenses:
                    ledger_expenses[ledger_name] = 0
                ledger_expenses[ledger_name] += taxable_amount

        # Now create the expense entry for each ledger group
        expense_entries = []
        for ledger_name, total_amount in ledger_expenses.items():
            expense_amt = -total_amount  # Assuming you want to apply negative sign to the expense
            if ledger_name == "Ocean Freight Expenses":
                CUnited_XML._msiOceanFreightExpense = total_amount
            elif ledger_name == "SHIPPING EXPENSES":
                CUnited_XML._msiShippingExpenses = total_amount
            elif ledger_name == "THC Expenses":
                CUnited_XML._msiTHCExpenses = total_amount
            elif ledger_name == "BL Expenses":
                CUnited_XML._msiBLExpenses = total_amount

            expense_entry = {
                "ledger_name": ledger_name,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }

            # Optionally add cost center allocation if job numbers are provided
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # Adjust as needed
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            expense_entries.append(expense_entry)

        return expense_entries
    


    @staticmethod
    def MSGetRoundOff_United() -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """

        round_off = ((CUnited_XML._msUnitedAmount+ CUnited_XML._msiTDSAmount) - (CUnited_XML._msiOceanFreightExpense + CUnited_XML._msiShippingExpenses + CUnited_XML._msiTHCExpenses + CUnited_XML._msiBLExpenses + CUnited_XML._msiTaxableAmount))
        round_off = round(round_off,2)

        # # Convert to Decimal for precision control
        # iDecimalRoundOff = Decimal(round_off).quantize(Decimal('0.01'), rounding=ROUND_UP) 
        # # Convert back to float
        # iPreciseRoundOFF = float(iDecimalRoundOff) 
        
        iPreciseRoundOFF = round_off
        # NOTE: is_deemed_positive = "Dr" if    round_off >= 0 else "Cr"
        CUnited_XML._msiRoundOffAmount = iPreciseRoundOFF
        return {
            "ledger_name": "ROUND OFF",
            "amount": -iPreciseRoundOFF  if iPreciseRoundOFF >= 0 else abs(iPreciseRoundOFF) ,
            "is_deemed_positive": True if iPreciseRoundOFF >= 0 else False,
            "is_party_ledger": False
        }



    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        FinalSummary=dictExtractedData.get("FinalSummary",{})
        credit_ledgers = []
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        fTotalAmount = float(FinalSummary.get("FinalTotalAmountINR", 0))

        
        # (Assuming you have a function similar to MSCalculateTDSFixedAmount)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("PAN"), 
                                                                    fTotalAmountWOTax)
        
        tds_amt = dictTDSInfo.get("TDSAmount", 0)
        fTotalCreditLedgerAmount = fTotalAmount - tds_amt  
        
        # Ceil to the next integer
        fTotalCreditLedgerAmount = round(fTotalCreditLedgerAmount,2) 
         

        tds_amt=round(tds_amt)
        tds_amt=float(f"{tds_amt:.2f}")
        CUnited_XML._msiTDSAmount=tds_amt
        fTotalCreditLedgerAmount = round(fTotalCreditLedgerAmount)
        CUnited_XML._msUnitedAmount = fTotalCreditLedgerAmount
        
        
        main_credit = {
            "ledger_name": CUnited_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNo", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        # If TDS is applicable, add a separate entry.
        
        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        try:
            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d.%m.%y")
            
            FinalSummary=dictExtractedData.get("FinalSummary",{})
            
            # Retrieve Job Numbers and Container Numbers from additional data
            lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
            lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
            
            if isinstance(lsJobNumbers, list):
                lsJobNumbers = ", ".join(lsJobNumbers)
            if isinstance(lsContainerNumbers, list):
                lsContainerNumbers = ", ".join(lsContainerNumbers)
            
            strBLNo = dictExtractedData.get("B/LNo", "N/A")
            
            # Optionally, include customer details
            customer = dictAdditionalInfo.get("A/C Name") # or dictExtractedData.get("ImporterName", "N/A")

            fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
            # If TDS is applicable, retrieve TDS info (optional)
            dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                        dictExtractedData.get("PAN"), 
                                                                        fTotalAmountWOTax)        # TODO: Verify working as expected
            
            strNarration = (
                f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
                f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
                f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
                f"( TDS DED. ON RS. {fTotalAmountWOTax} "
                f"@{dictTDSInfo.get('TDSRate','')}% = {dictTDSInfo.get('TDSAmount','')} ) "
                f"{CUnited_XML._msPartyLedger}"
            )
            return strNarration
        except Exception as e:
            return ""
    
    
    
    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------
            CUnited_XML.reset()

            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: Unable to process your document. Please enter it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CUnited_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
        
            expenses_dict = {
                "OCEAN FREIGHT - FWD": "Ocean Freight Expenses",
                "OCEAN FREIGHT FWD": "Ocean Freight Expenses",
                
                "THC - FWD": "THC Expenses",
                "THC FWD": "THC Expenses",
                "DOC SURRENDER FEE - FWD": "BL Expenses",
                "Seaway BL": "BL Expenses",
                "Bl fees Fwd" : "BL Expenses",
                "BI fees Fwd" : "BL Expenses",
                "ADVANCE MANIFEST SUBMISSION - FWD": "SHIPPING EXPENSES",
                "ENTRY SUMMARY DECLARATION SUR FWD":"SHIPPING EXPENSES",
                "AMENDMENT CHARGES - FWD":"SHIPPING EXPENSES",
                "OTHER SUCHARGE - FWD":"SHIPPING EXPENSES",
                "Facility": "SHIPPING EXPENSES",
                "Rail Recovery": "SHIPPING EXPENSES",
                "DOC SURRENDER FEE -FWD" : "BL Expenses",
                "INDIA LEVY OF CESS": "SHIPPING EXPENSES"
            }

            
            item_table =dictExtractedData.get("ItemTable1",[{}])
            
            lsJobNumbers =  dictAdditionalData.get("Job Number", "N/A")
            
            expense_entry = CUnited_XML.generate_expense_entries(item_table, expenses_dict, lsJobNumbers)
            
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CUnited_XML.MSFindDutiesLedger_United(dictExtractedData) 

            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CUnited_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            roundoff_entry = CUnited_XML.MSGetRoundOff_United()
            if CUnited_XML._msiRoundOffAmount > 2 or CUnited_XML._msiRoundOffAmount < -2:
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "Skipped",
                        TracebackLogs           = "err_msg",
                        strAccuVelocityComments = "Error - Tally XML: The extracted document shows unequal Credit and Debit amounts. This mismatch likely stems from poor document quality during extraction."
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            for expense_entry in expense_entry:
                ledger_entries.append(LedgerEntrySchema(**expense_entry))
            
            
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CUnited_XML._mStrCompanyName,
                    gst_in="23AADFU8796Q1Z5",
                    state_name="Madhya Pradesh",
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('InvoiceNo','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="united liner shipping services llp", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)

            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward

class CANL_XML:
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = "ANL SINGAPORE PTE. LTD. C/O CCAI"  # This will be used as the credit ledger

    _msiOceanFreightExpense = 0
    _msiShippingExpenses = 0
    _msiTHCExpenses = 0
    _msiBLExpenses = 0
    _msiTaxableAmount = 0
    _msiRoundOffAmount = 0
    _msUnitedAmount = 0
    _msiTDSAmount = 0    
    _mfFrieghtDiscount = 0
    
    @classmethod
    def reset(cls): 
        cls._msiOceanFreightExpense = 0
        cls._msiShippingExpenses = 0
        cls._msiTHCExpenses = 0
        cls._msiBLExpenses = 0
        cls._msiTaxableAmount = 0
        cls._msiRoundOffAmount = 0
        cls._msUnitedAmount = 0
        cls._msiTDSAmount = 0
        cls._mfFrieghtDiscount = 0
    
    @staticmethod
    def MSFindDutiesLedger_United(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("ItemTable1", [])

        for record in table:
            def format_rate(rate):
                rate = float(rate)
                return int(rate) if rate.is_integer() else rate

            # Process IGST if applicable
            igst_rate = record.get("IGSTRate", 0)
            if igst_rate and float(igst_rate) > 0:
                amount_igst = round(float(record.get("IGSTAmount", 0)),2)
                formatted_rate = format_rate(igst_rate)
                ledger_name = f"ITC IGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

            # Process CGST if applicable
            cgst_rate = record.get("CGSTRate", 0)
            if cgst_rate and float(cgst_rate) > 0:
                amount_cgst = round(float(record.get("CGSTAmount", 0)),2)
                formatted_rate = format_rate(cgst_rate)
                ledger_name = f"ITC CGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst  

            # Process SGST if applicable
            sgst_rate = record.get("SGSTRate", 0)
            if sgst_rate and float(sgst_rate) > 0:
                amount_sgst = round(float(record.get("SGSTAmount", 0)),2)
                formatted_rate = format_rate(sgst_rate)
                ledger_name = f"ITC SGST @ {formatted_rate}%"
                tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": round(amount,2)} for name, amount in tax_ledgers_dict.items()]
        for taxDetail in tax_ledgers:
            CANL_XML._msiTaxableAmount += abs(taxDetail["amount"]) # add every tax rates amount
        return tax_ledgers

    @staticmethod
    def generate_expense_entries(item_table, expenses_dict, lsJobNumbers=None):
            # Create a dictionary to store summed amounts by ledger name
        ledger_expenses = {}

        for item in item_table:
            description = item.get("Description", "").lower().strip()  # Normalize description
            taxable_amount = round(item.get("TaxableAmount", 0.0),2)

            # Iterate through keys in expenses_dict and check if the description matches any key
            ledger_name = None
            for key in expenses_dict.keys():
                if key.lower().strip() in description.lower():  # Check if the normalized description is part of the normalized key
                    ledger_name = expenses_dict[key]
                    break  # Stop after finding the first match

            if not ledger_name:
                ledger_name = "SHIPPING EXPENSES"  # Default to "Shipping Expenses" if no match is found SHIPPING EXPENSES

            # Now process the ledger name and taxable amount as before..
            if ledger_name:
                # If a match is found, sum the taxable amounts for that ledger name
                if ledger_name not in ledger_expenses:
                    ledger_expenses[ledger_name] = 0
                ledger_expenses[ledger_name] += taxable_amount

        # Now create the expense entry for each ledger group
        expense_entries = []
        for ledger_name, total_amount in ledger_expenses.items():
            expense_amt = -round(total_amount,2)  # Assuming you want to apply negative sign to the expense
            if ledger_name == "Ocean Freight Expenses":
                CANL_XML._msiOceanFreightExpense = total_amount
            elif ledger_name == "SHIPPING EXPENSES":
                CANL_XML._msiShippingExpenses = total_amount
            elif ledger_name == "THC Expenses":
                CANL_XML._msiTHCExpenses = total_amount
            elif ledger_name == "BL Expenses":
                CANL_XML._msiBLExpenses = total_amount

            expense_entry = {
                "ledger_name": ledger_name,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }

            # Optionally add cost center allocation if job numbers are provided
            if lsJobNumbers:
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # Adjust as needed
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs

            expense_entries.append(expense_entry)
        
        return expense_entries

    @staticmethod
    def MSGetRoundOff_ANL() -> dict:
        """
        Calculates the round-off value so that total debits equal the credit.
        """

        round_off = ((CANL_XML._msUnitedAmount+ CANL_XML._mfFrieghtDiscount) - (CANL_XML._msiOceanFreightExpense + CANL_XML._msiShippingExpenses + CANL_XML._msiTHCExpenses + CANL_XML._msiBLExpenses + CANL_XML._msiTaxableAmount+ CANL_XML._msiTDSAmount))
        round_off = round(round_off,2)

        # # Convert to Decimal for precision control
        # iDecimalRoundOff = Decimal(round_off).quantize(Decimal('0.01'), rounding=ROUND_UP) 
        # # Convert back to float
        # iPreciseRoundOFF = float(iDecimalRoundOff) 
        
        iPreciseRoundOFF = round_off
        # NOTE: is_deemed_positive = "Dr" if    round_off >= 0 else "Cr"
        CANL_XML._msiRoundOffAmount = iPreciseRoundOFF
        return {
            "ledger_name": "ROUND OFF",
            "amount": -iPreciseRoundOFF  if iPreciseRoundOFF >= 0 else abs(iPreciseRoundOFF) ,
            "is_deemed_positive": True if iPreciseRoundOFF >= 0 else False,
            "is_party_ledger": False
        }


    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        FinalSummary=dictExtractedData.get("FinalSummary",{})
        credit_ledgers = []
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        
        fTotalAmountWOTax = float(FinalSummary.get("TaxableValue", 0))
        fTotalAmount = float(FinalSummary.get("FinalTotalAmountINR", 0))

    
        
        # fTotalCreditLedgerAmount=float(f"{fTotalCreditLedgerAmount:.2f}")
        
        # tds_amt = round(tds_amt)
        # tds_amt=float(f"{tds_amt:.2f}")
        fTotalCreditLedgerAmount = round(fTotalAmount)
        CANL_XML._msUnitedAmount = fTotalCreditLedgerAmount
        
        
        main_credit = {
            "ledger_name": CANL_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": dictExtractedData.get("InvoiceNo", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        
        spoton_voucher_discount =  abs(dictExtractedData.get("SpotOnVoucher", 0))
        if spoton_voucher_discount != 0:
            spot_on_ledger = {
            "ledger_name": "Ocean Freight Expenses",  
            "amount": spoton_voucher_discount,
            "is_deemed_positive": False, 
            "is_party_ledger": True,
            }
            CANL_XML._mfFrieghtDiscount = spoton_voucher_discount
            credit_ledgers.append(spot_on_ledger)
            
        credit_ledgers.append(main_credit)
        
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        try:
            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d.%m.%y")
            
            # Retrieve Job Numbers and Container Numbers from additional data
            lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
            lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A") or dictExtractedData.get("LsContainerNumber/Size", "N/A")
            
            if isinstance(lsJobNumbers, list):
                lsJobNumbers = ", ".join(lsJobNumbers)
            if isinstance(lsContainerNumbers, list):
                lsContainerNumbers = ", ".join(lsContainerNumbers)
            
            strBLNo = dictExtractedData.get("B/LNo", "N/A")
            
            # Optionally, include customer details
            customer = dictAdditionalInfo.get("A/C Name") # or dictExtractedData.get("ImporterName", "N/A")
            fTotalAmountWOTaxes = float(dictExtractedData.get("TotalTaxableAmount", 0))
            # If TDS is applicable, retrieve TDS info (optional)
            dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                        dictExtractedData.get("PAN"), 
                                                                        fTotalAmountWOTaxes)        # TODO: Verify working as expected
            
            strNarration = (
                f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
                f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
                f"AGST CONT. NO. {lsContainerNumbers} VIDE BL NO. {strBLNo} "
            )
            return strNarration
        except Exception as e:
            return ""
    
    
    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        CANL_XML.reset()
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # ----------------------- Extract Additional Details -----------------------------

            invoice_number = dictExtractedData.get("InvoiceNo", "").strip()
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CICDController_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: Unable to process your document. Please enter it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = dictExtractedData.get("InvoiceDate", "")
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CANL_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
        
            expenses_dict = {
                "OCEAN FREIGHT - FWD": "Ocean Freight Expenses",
                "Express release svc, charges for" : "BL Expenses",
                "Express release service charges": "BL Expenses",
                "OCEAN FREIGHT FWD": "Ocean Freight Expenses",
                "BASIC FREIGHT": "Ocean Freight Expenses",
                "DOC SURRENDER FEE -FWD" : "BL Expenses",
		        "Bunker surcharge Nos": "Ocean Freight Expenses",
                "Bunker Surcharge" : "Ocean Freight Expenses",
                "Terminal handling charge origin": "THC Expenses",
                "Express release svc, charges for": "BL Expenses",
                "Peak Season Adjustment Factor":"Ocean Freight Expenses",
                "Other Service Charges MH IGST @ 18% EXPRESS REL C": "BL Expenses",
                "THC - FWD": "THC Expenses",
                "THC FWD": "THC Expenses",
                "Terminal handl ch origin": "THC Expenses",
                "Terminal handle ch origin": "THC Expenses",
                "Export Documentation Fee": "BL Expenses",
                "Seaway BL": "BL Expenses",
                "Bl fees Fwd" : "BL Expenses",
                "BI fees Fwd" : "BL Expenses",
                "ADVANCE MANIFEST SUBMISSION - FWD": "SHIPPING EXPENSES",
                "ENTRY SUMMARY DECLARATION SUR FWD":"SHIPPING EXPENSES",
                "AMENDMENT CHARGES - FWD":"SHIPPING EXPENSES",
                "OTHER SUCHARGE - FWD":"SHIPPING EXPENSES",
                "Facility": "SHIPPING EXPENSES",
                "Rail Recovery": "SHIPPING EXPENSES",
                "INDIA LEVY OF CESS": "SHIPPING EXPENSES"
            }

            
            item_table =dictExtractedData.get("ItemTable1",[{}])
            # lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            lsJobNumbers =  dictAdditionalData.get("Job Number", "N/A")
            
            expense_entry = CANL_XML.generate_expense_entries(item_table, expenses_dict, lsJobNumbers)
            
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            tax_ledgers = CANL_XML.MSFindDutiesLedger_United(dictExtractedData) 

            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CANL_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            # ---------------------------TDS Amount--------------------------------------
            tds_amount = dictExtractedData.get("TDS 194R", 0)
            if tds_amount != 0:
                tds_entry = {
                "ledger_name": "TDS RECEIVABLE (194R)",
                "amount": -tds_amount,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
                }
                expense_entry.append(tds_entry)
                CANL_XML._msiTDSAmount = tds_amount

            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            roundoff_entry = CANL_XML.MSGetRoundOff_ANL()
            if CANL_XML._msiRoundOffAmount > 2 or CANL_XML._msiRoundOffAmount < -2:
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = "Skipped",
                        TracebackLogs           = "err_msg",
                        strAccuVelocityComments = "Error - Tally XML: The extracted document shows unequal Credit and Debit amounts. This mismatch likely stems from poor document quality during extraction."
                    )
                return None  # propagate the same ValueError upward

            # ----------------------- Assemble All Ledger Entries ----------------------------
            ledger_entries = []
            for expense_entry in expense_entry:
                ledger_entries.append(LedgerEntrySchema(**expense_entry))
            
            
            for tax in tax_ledgers:
                tax["is_deemed_positive"] = True
                tax["is_party_ledger"] = False
                ledger_entries.append(LedgerEntrySchema(**tax))
            ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CANL_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{dictExtractedData.get('InvoiceNo','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="anl singapore pte. ltd. c/o ccai", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)

            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward


class CConcor_XML:
    """
    6. Party Ledger Name Decide Base On Seller ICD Location - i.e. TIHI, PITHAMPUR, MANDIDEEP(PDA)
    1. REIMBURSEMENT Ledger Select When PartyName not found FairlDeal In case of REIMBURSEMENT Dont Apply Taxable Ledger
    4. Title Tax Invoice, IRN 64 Digit Present Check, Performa Invoice Don't Punch into Tally   ===== HOLD
    5. ITC RCM suffix Taxable Ledger Applied When Reverse Charges Applied , ITC CGST RCM @ 2.5%, ITC SGST RCM @ 2.5%  in debit, CGST @ 2.5% (RCM) & SGST @ 2.5% (RCM) in credit - COMPLETED
    7. Manual Need to Select Agst Ref in Container Ledger CONTAINER CORPORATION OF INDIA LTD., TIHI = Asgt Ref - Date - Amount
    """
    _mStrCompanyName = "FAIRDEAL INTERNATIONAL"  # This will be used as the company info AND credit ledger
    _mStrVoucherType = "AV Tax Journal"
    _mStrVoucherNumberingStyle = "Automatic (Manual Override)"
    _msPartyLedger = None  # This will be used as the credit ledger
    _msIsReimbursment = False # True when selected_expense_ledger value contains "REIMBURSEMENT EXPENSES -ICD"
    _msIsReverseChargesApplied = False # True when AmountOfTaxSubjectToReverseCharge > 0.0
    _msInvoiceNumber = None

    @staticmethod
    def MSSelectTaxLedgerPrefix(dictExtractedData: dict):
        iReverseChargesAmount = dictExtractedData.get("AmountOfTaxSubjectToReverseCharge")
        if iReverseChargesAmount > 0.0:
            CConcor_XML._msIsReverseChargesApplied = True
        else:
            CConcor_XML._msIsReverseChargesApplied = False
        return CConcor_XML._msIsReverseChargesApplied

    @staticmethod
    def MSSelectPartyLedgerName(strSellerICD = "MMLP - TIHI"):
        if "TIHI".lower() in strSellerICD.lower():
            CConcor_XML._msPartyLedger = "CONTAINER CORPORATION OF INDIA LTD., TIHI"  
        elif "PITHAMPUR".lower() in strSellerICD.lower():
            CConcor_XML._msPartyLedger = "CONTAINER CORPORATION OF INDIA LTD., PITHAMPUR"  
        elif "MANDIDEEP".lower() in strSellerICD.lower():
            CConcor_XML._msPartyLedger = "CONTAINER CORPORATION OF INDIA LTD, MANDIDEEP(PDA)"  
        else:
            CConcor_XML._msPartyLedger = "CONTAINER CORPORATION OF INDIA LTD"  
        
    @staticmethod
    def MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number: str, lsRequiredCols=[]):
        """
        Returns additional invoice details from the provided template, ensuring required fields contain valid data.
        Parses comma-separated values into lists.
        If an exact match is not found, searches for a match based on the last five digits of the invoice number.
        """
        def parse_comma_separated(value: str):
            return [v.strip() for v in value.split(",") if v.strip()]

        # First, try to find an exact match
        for record in dictAdditionalTemplate:
            if record.get("Invoice No", "").strip() == invoice_number.strip():
                if not all(record.get(field, "").strip() for field in lsRequiredCols):
                    raise ValueError(f"ValidationError - Tally XML: The av_pegasus_additionaldetails.xlsx file is missing required columns {lsRequiredCols} for invoice '{invoice_number}.' Please add these columns and try again.")
                
                return {
                    "A/C Name": record.get("A/C Name", "").strip(),
                    "Invoice No": record.get("Invoice No", "").strip(),
                    "Job Number": parse_comma_separated(record.get("Job Number", "")),
                    "Container No": parse_comma_separated(record.get("Container No (Please specify if not provided)", ""))
                }
        
        # If exact match is not found, try searching by last five digits
        invoice_suffix = invoice_number.strip()[-5:]
        for record in dictAdditionalTemplate:
            if record.get("Invoice No", "").strip().endswith(invoice_suffix):
                return {
                    "A/C Name": record.get("A/C Name", "").strip(),
                    "Invoice No": record.get("Invoice No", "").strip(),
                    "Job Number": parse_comma_separated(record.get("Job Number", "")),
                    "Container No": parse_comma_separated(record.get("Container No (Please specify if not provided)", ""))
                }
        
        raise ValueError(f"ValidationError - Tally XML: Mandatory details for invoice {invoice_number} not found in av_pegasus_additionaldetails.xlsx file. Please add it in Excel and update Tally if scanned document accuracy is an issue.")
    
    
    @staticmethod
    def MSFindDutiesLedger_Concor(dictExtractedData: dict) -> list:
        """
        Processes the Table data and returns a list of tax ledger dictionaries.
        If a ledger already exists, sums the amounts instead of creating duplicates.
        """
        tax_ledgers_dict = {}
        table = dictExtractedData.get("ItemTable1", [])
        for record in table:
            # Process IGST if applicable
            if CConcor_XML._msIsReverseChargesApplied:
                # Reverse Charges we add Taxable Ledger in RCM prefix in debit and then add same amount in Credit  
                igst_rate = CPegasus_XML.MSFormatTaxRate(record.get("IGSTRate", 0)) 
                cgst_rate = CPegasus_XML.MSFormatTaxRate(record.get("CGSTRate", 0))  
                sgst_rate = CPegasus_XML.MSFormatTaxRate(record.get("SGSTRate", 0))  

                CICDController.MSValidateTaxRates(igst_rate=igst_rate, cgst_rate=cgst_rate, sgst_rate=sgst_rate) 
                               
                if igst_rate and float(igst_rate) > 0:
                    amount_igst = float(record.get("IGSTAmount", 0))
                    ledger_name = f"ITC IGST RCM @ {igst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

                    ledger_name2 = f"IGST @ {igst_rate}% (RCM)"
                    tax_ledgers_dict[ledger_name2] = tax_ledgers_dict.get(ledger_name2, 0) - amount_igst
                # Process CGST if applicable
                if cgst_rate and float(cgst_rate) > 0:
                    amount_cgst = float(record.get("CGSTAmount", 0))
                    ledger_name = f"ITC CGST RCM @ {cgst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst
                    ledger_name2 = f"CGST @ {cgst_rate}% (RCM)"
                    tax_ledgers_dict[ledger_name2] = tax_ledgers_dict.get(ledger_name2, 0) - amount_cgst

                # Process SGST if applicable
                if sgst_rate and float(sgst_rate) > 0:
                    amount_sgst = float(record.get("SGSTAmount", 0))
                    ledger_name = f"ITC SGST RCM @ {sgst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst
                    ledger_name2 = f"SGST @ {sgst_rate}% (RCM)"
                    tax_ledgers_dict[ledger_name2] = tax_ledgers_dict.get(ledger_name2, 0) - amount_sgst
            else:
                # Reverse Charges we add Taxable Ledger in RCM prefix in debit and then add same amount in Credit  
                igst_rate = CPegasus_XML.MSFormatTaxRate(record.get("IGSTRate", 0)) 
                cgst_rate = CPegasus_XML.MSFormatTaxRate(record.get("CGSTRate", 0))  
                sgst_rate = CPegasus_XML.MSFormatTaxRate(record.get("SGSTRate", 0))  
                if igst_rate and float(igst_rate) > 0:
                    amount_igst = float(record.get("IGSTAmount", 0))
                    ledger_name = f"ITC IGST @ {igst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_igst

                # Process CGST if applicable
                if cgst_rate and float(cgst_rate) > 0:
                    amount_cgst = float(record.get("CGSTAmount", 0))
                    ledger_name = f"ITC CGST @ {cgst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_cgst

                # Process SGST if applicable
                if sgst_rate and float(sgst_rate) > 0:
                    amount_sgst = float(record.get("SGSTAmount", 0))
                    ledger_name = f"ITC SGST @ {sgst_rate}%"
                    tax_ledgers_dict[ledger_name] = tax_ledgers_dict.get(ledger_name, 0) - amount_sgst

        # Convert the dictionary back to a list of dictionaries
        tax_ledgers = [{"ledger_name": name, "amount": amount} for name, amount in tax_ledgers_dict.items()]
        
        for tax in tax_ledgers:
            ledger_name = tax.get("ledger_name", "")
            
            if "(RCM)".lower() in ledger_name.lower():
                tax["amount"] = abs(tax["amount"])
                tax["is_deemed_positive"] = False  # credit
                tax["is_party_ledger"] = False
            else:
                tax["is_deemed_positive"] = True   # Debit
                tax["is_party_ledger"] = True

        return tax_ledgers


    @staticmethod
    def MSGenerateChargesLedger(dictExtractedData, lsJobNumbers = None):
        try:
            charges_ledger = []
            freight_ledger = {}
            iFreightChares = 0
            # Code to handle Freight charges or other charges
            expense_dict = {
                "FREIGHT CHARGES" : "Long Lead Trp. by Rail"
            }
            expense_keys = [key.lower().strip() for key in expense_dict.keys()]
            # Currently handling only for freight charges if in future other expenses come just add logic here
            for item in dictExtractedData['ItemTable1']:
                activity_desc = item["ActivityDescOfService"].lower().strip()
                if activity_desc in expense_keys:
                    iFreightChares = item["Amount"]
                    original_key = next(k for k in expense_dict.keys() if k.lower().strip() == activity_desc)
                    freight_ledger = {
                        "ledger_name": expense_dict[original_key],
                        "amount": -iFreightChares,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "gst_taxability": "Taxable",
                        "gst_type_of_supply": "Services"
                    }
                     
            
                    # Add cost center allocation if cost centers exist
                    if lsJobNumbers:
                        freight_ledger["cost_center_category"] = CICDController_XML._mStrFinancialYear
                        base_amt = round(iFreightChares / len(lsJobNumbers), 2)
                        total_distributed = base_amt * len(lsJobNumbers)
                        rounding_diff = round(iFreightChares - total_distributed, 2)
                        cost_allocs = []
                        for idx, center in enumerate(lsJobNumbers):
                            alloc_amt = base_amt
                            if idx == len(lsJobNumbers) - 1:
                                alloc_amt += rounding_diff
                            cost_allocs.append({"name": center, "amount": -alloc_amt})
                        freight_ledger["cost_center_allocations"] = cost_allocs

                    charges_ledger.append(freight_ledger)
                    
                    
            return charges_ledger
        except Exception as e:
            raise Exception(f"Error in Generating Debit Ledger. {e}")

    @staticmethod
    def MSGenerateCreditLedger(dictExtractedData: dict, additional_data: dict) -> list:
        """
        Generates credit ledger entries.
        For Adani Hazira, the main credit ledger is fixed as "ADANI HAZIRA PORT LIMITED".
        If TDS is applicable (via an external function), add a TDS entry.
        """
        
        credit_ledgers = []
        fTotalAmountWOTax = 0
        fTotalAmount = 0
        if CConcor_XML._msIsReverseChargesApplied and (not CConcor_XML._msIsReimbursment): # As per AAKASH , No need to Include Reverse Chargs in Reimbursment
            # NOTE: No Taxable amount need to count in case of reimburs
            fTotalAmount = float(dictExtractedData.get("TotalInvoiceValueFigure", 0))
            fTotalAmountWOTax = fTotalAmount
        elif CConcor_XML._msIsReimbursment and CConcor_XML._msIsReverseChargesApplied:
            fTotalAmountWOTax = float(dictExtractedData.get("TotalAmount", 0))
            fTotalAmount = fTotalAmountWOTax
        else:
            fTotalAmountWOTax = float(dictExtractedData.get("TotalAbatedValue", 0))
            fTotalTax = float(dictExtractedData.get("TotalIGSTAmount", 0) + dictExtractedData.get("TotalCGSTAmount", 0) + dictExtractedData.get("TotalSGSTAmount", 0)) 
            fTotalAmount = fTotalAmountWOTax + fTotalTax
        
        # (Assuming you have a function similar to MSCalculateTDSFixedAmount)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("SellerPANNo"), 
                                                                    fTotalAmountWOTax)
        
        tds_amt = dictTDSInfo.get("TDSAmount", 0)
        fTotalCreditLedgerAmount =  float(fTotalAmount - tds_amt)
        main_credit = {
            "ledger_name": CConcor_XML._msPartyLedger,  # Fixed for Adani Hazira
            "amount": fTotalCreditLedgerAmount,
            "is_deemed_positive": False,  # Credit
            "is_party_ledger": True,
            "bill_allocation": {
                "name": CConcor_XML._msInvoiceNumber if CConcor_XML._msInvoiceNumber is not None else dictExtractedData.get("InvoiceNo", "").strip(),
                "billtype": "New Ref",
                "amount": fTotalCreditLedgerAmount
            }
        }
        credit_ledgers.append(main_credit)
        
        # If TDS is applicable, add a separate entry.
        
        if tds_amt > 0:
            tds_entry = {
                "ledger_name": "TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                "amount": tds_amt,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            credit_ledgers.append(tds_entry)
        return credit_ledgers

    @staticmethod
    async def MSGetNarration(dictExtractedData: dict, dictAdditionalInfo: dict):
        """
        Generates a narration string for Adani Hazira based on extracted and additional data.
        """
        invoice_number = CConcor_XML._msInvoiceNumber if CConcor_XML._msInvoiceNumber is not None else dictExtractedData.get("InvoiceNo", "").strip()
        iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
        strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
        
        # Retrieve Job Numbers and Container Numbers from additional data
        lsJobNumbers = dictAdditionalInfo.get("Job Number", "N/A")
        lsContainerNumbers = dictAdditionalInfo.get("Container No", "N/A")

        # Check if lsContainerNumbers is "N/A" or an empty list and fetch from dictExtractedData
        if not lsContainerNumbers or lsContainerNumbers == "N/A":
            lsContainerNumbers = dictExtractedData.get("LsContainerNumber/Size", "N/A")

        if isinstance(lsJobNumbers, list):
            lsJobNumbers = ", ".join(lsJobNumbers)

        if isinstance(lsContainerNumbers, list):
            lsContainerNumbers = ", ".join(lsContainerNumbers)

        
        # strBLNo = dictExtractedData.get("BLNo", "N/A")
        
        # Optionally, include customer details
        customer = dictAdditionalInfo.get("A/C Name") or dictExtractedData.get("ImporterName", "N/A")
        fTotalAmountWOTaxes = float(dictExtractedData.get("TotalAmount", 0))
        # If TDS is applicable, retrieve TDS info (optional)
        dictTDSInfo = CICDController_XML.MSCalculateTDSFixedAmount(
                                                                    dictExtractedData.get("SellerPANNo"), 
                                                                    fTotalAmountWOTaxes)        # TODO: Verify working as expected
        if CConcor_XML._msIsReimbursment:
            strRCMMsg = ""
        else:
            strRCMMsg = f"( RCM BOOKED ON RS. {fTotalAmountWOTaxes:.2f} @18% = {fTotalAmountWOTaxes * 0.18:.2f} )." if CConcor_XML._msIsReverseChargesApplied else ""  # NOTE: As per Aakash No Need to Add Reverse Charges Msg in Narration when Reimbursment Case

        strNarration = (
            f"BEING INVOICE NO {invoice_number} DATED {strInvoiceDate}. "
            f"RCVD AGST JOB NO {lsJobNumbers} A/C {customer}. "
            f"AGST CONT. NO. {lsContainerNumbers} "
            f"( TDS DED. ON RS. {fTotalAmountWOTaxes} "
            f"@{dictTDSInfo.get('TDSRate','')}% = {dictTDSInfo.get('TDSAmount','')} ) "
            f"CCIL "
            f"{strRCMMsg}"
        )
        return strNarration

    @staticmethod
    async def MSCreateXML(dictExtractedData: dict, AdditionalExcelTemplate, iUserId=None, strClientREQID=None, iDocID=None):
        """
        Creates an XML for a Tally Journal Voucher entry for Adani Hazira.
        The generated XML includes expense ledger (with cost center allocations if provided),
        tax ledger entries (from the Table), round-off, and credit ledger entries (with bill allocation
        and TDS if applicable).
        
        Args:
            dictExtractedData (dict): Extracted invoice data.
            AdditionalExcelTemplate (str or list): Additional invoice details.
            
        Returns:
            bytes: The final XML as bytes.
        """
        strTimeSaved = "NOT_APPLICABLE"
        bIsDeveloperRun = True if (iUserId is None or strClientREQID is None or iDocID is None) else False
        try:
            # initialize
            tax_ledgers = []
            CConcor_XML.MSSelectTaxLedgerPrefix(dictExtractedData)
            # ----------------------- Validate & Load Additional Data -----------------------
            dictAdditionalTemplate = await CICDController_XML.MSValidateAdditionalExcelTemplate(
                iUserId=iUserId,
                strClientREQID=strClientREQID,
                iDocID=iDocID,
                AdditionalExcelTemplate=AdditionalExcelTemplate
            )
            if dictAdditionalTemplate is None:
                return None # Return Silently
            
            # Party Ledger Name Decide Base on SellerICD Location i.e. TIHI, PITHAMPUR, MANDIDEEP(PDA)
            CConcor_XML.MSSelectPartyLedgerName(dictExtractedData.get("SellerICD", ""))
            # ----------------------- Extract Additional Details -----------------------------
            invoice_number = dictExtractedData.get("InvoiceNo", "").strip().replace("O", "0") # NOTE; Digital Document AWS Mistake It assumes O when 0 occur in Invoice Number
            CConcor_XML._msInvoiceNumber = invoice_number
            
            lsRequiredAdditionalInfo = ["Invoice No", "Job Number"]
            try:
                dictAdditionalData = CConcor_XML.MSFindAdditionalDetails(dictAdditionalTemplate, invoice_number, lsRequiredCols=lsRequiredAdditionalInfo)
            except ValueError as ve:
                err_msg = str(ve)
                # AVRecordDetail AVXMLGeneratedStatus, strAccuVelocityComments, TracebackLogs Update
                if not bIsDeveloperRun:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId                 = iUserId,
                        strClientREQID          = strClientREQID,
                        DocID                   = iDocID,
                        AVXMLGeneratedStatus    = (
                            "ValidationError"
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Skipped"
                        ),
                        TracebackLogs           = err_msg,
                        strAccuVelocityComments = (
                            err_msg
                            if err_msg.startswith("ValidationError - Tally XML:")
                            else "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                        ),
                    )
                return None  # propagate the same ValueError upward
            # For example, additional data might include Job Number and Container No
            lsJobNumbers = dictAdditionalData.get("Job Number", "N/A")
            
            # ----------------------- Convert Dates & Build Basic Strings --------------------
            iInvDate = await CICDController.MSConvertIntToDateFromYYYYMMDD(dictExtractedData.get("InvoiceDate", ""))
            strInvoiceDate = datetime.strptime(str(iInvDate), "%Y%m%d").strftime("%d-%b-%y")
            todays_date = datetime.today().strftime("%Y%m%d")
            
            strNarration = await CConcor_XML.MSGetNarration(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Ledger Selection & Expense Ledger Entry -----------------
            # For expense ledger, if RegisterPartyName equals "FAIRDEAL INTERNATIONAL" then use "ICD EXPENSE",
            # else "Reimburshment" (as in Hind Terminals)
            selected_expense_ledger = CICDController_XML.MSGetDebitLedgerName(dictExtractedData=dictExtractedData, keys_to_check=["RegisterPartyName"], name="FAIRDEAL")
            
            # For Adani Hazira, expense amount is computed as:
            # Expense = TotalInvoiceValue(InFigure) - TotalTaxValue(InFigure)
            
            # Initializing variable that stores all ledger entries
            ledger_entries = []
            
            # Code to handle Frieght Charges Ledger
            charges_ledger = []
            charges_ledger = CConcor_XML.MSGenerateChargesLedger(dictExtractedData, lsJobNumbers)
            fCharges = 0
            
            if charges_ledger:
                for item in charges_ledger:
                    fCharges += item["amount"]
                    ledger_entries.append(LedgerEntrySchema(**item))
            
            expense_amt = 0.0
                  
            if dictExtractedData['RegisterPartyName'].strip().lower() == "fairdeal international":
                expense_amt = - float(dictExtractedData.get("TotalAmount", 0) - abs(fCharges))
            else:    
                expense_amt = - float(dictExtractedData.get("TotalInvoiceValueFigure", 0) - abs(fCharges))
            expense_entry = {
                "ledger_name": selected_expense_ledger,
                "amount": expense_amt,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services"
            }
            
            # Optionally add cost center allocation if additional data provides job numbers
            if lsJobNumbers:
                # Here we assume the additional data’s Job Number list acts as cost centers
                # For simplicity, we use them directly.
                expense_entry["cost_center_category"] = CICDController_XML._mStrFinancialYear  # You may adjust this
                base_amt = round(expense_amt / len(lsJobNumbers), 2)
                total_distributed = base_amt * len(lsJobNumbers)
                rounding_diff = round(expense_amt - total_distributed, 2)
                cost_allocs = []
                for idx, center in enumerate(lsJobNumbers if isinstance(lsJobNumbers, list) else [lsJobNumbers]):
                    alloc_amt = base_amt
                    if idx == (len(lsJobNumbers) - 1 if isinstance(lsJobNumbers, list) else 0):
                        alloc_amt += rounding_diff
                    cost_allocs.append({"name": center, "amount": alloc_amt})
                expense_entry["cost_center_allocations"] = cost_allocs
            
            # ----------------------- Tax Ledger Entries (Duties & Taxes) ---------------------
            if not CConcor_XML._msIsReimbursment:
                tax_ledgers = CConcor_XML.MSFindDutiesLedger_Concor(dictExtractedData)
            
            # ----------------------- Round-Off Ledger Entry ---------------------------------
            # fTotalAmountWOTax = float(dictExtractedData.get("TotalAmount", 0))
            # fTotalTax = float(dictExtractedData.get("TotalInvoiceValueFigure", 0))
            # fTotalAmount = fTotalAmountWOTax + fTotalTax
            # round_off_value = round(fTotalAmount - (abs(fTotalAmountWOTax) + fTotalTax), 2)
            # roundoff_entry = {
            #     "ledger_name": "ROUND OFF",
            #     "amount": round_off_value,
            #     "is_deemed_positive": True if round_off_value >= 0 else False,
            #     "is_party_ledger": False
            # }
            
            # ----------------------- Credit Ledger Entries ----------------------------------
            credit_ledgers = CConcor_XML.MSGenerateCreditLedger(dictExtractedData, dictAdditionalData)
            
            # ----------------------- Assemble All Ledger Entries ----------------------------
            
            ledger_entries.append(LedgerEntrySchema(**expense_entry))
            for tax in tax_ledgers:
                ledger_entries.append(LedgerEntrySchema(**tax))
            # ledger_entries.append(LedgerEntrySchema(**roundoff_entry))
            for cred in credit_ledgers:
                ledger_entries.append(LedgerEntrySchema(**cred))
            
            # ----------------------- Build the Voucher Input Schema -------------------------
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CAdaniHazira_XML._mStrCompanyName,
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=todays_date,
                narration=strNarration,
                reference=f"{CConcor_XML._msInvoiceNumber if CConcor_XML._msInvoiceNumber is not None else dictExtractedData.get('InvoiceNo','').strip()} dt. {strInvoiceDate}",
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=CICDController_XML._mStrVoucherType,
                numbering_style=CICDController_XML._mStrVoucherNumberingStyle,
                effective_date=todays_date,
                ledger_entries=ledger_entries,
            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            if not bIsDeveloperRun:
                # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="container corporation of india ltd.", no_of_stock_items=0)
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocID, AVXMLGeneratedStatus="Success",TracebackLogs=f"",strAccuVelocityComments=f"-", EstAccountantTimeSaved = strTimeSaved)
            return xml_str.encode("utf-8")
        except Exception as e:
            err_msg = str(traceback.format_exc())
            if not bIsDeveloperRun:
                await CAVRequestDetail.MSUpdateRecord(
                    iUserId                 = iUserId,
                    strClientREQID          = strClientREQID,
                    DocID                   = iDocID,
                    AVXMLGeneratedStatus    = "Skipped",
                    TracebackLogs           = err_msg,
                    strAccuVelocityComments = (
                        "Error - Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    ),
                    EstAccountantTimeSaved  = strTimeSaved
                )
            raise  # propagate the same ValueError upward

async def main():
    # Set up your parameters. Replace these with actual values as needed.
    iUserId = 5  # Example user ID
    iDocId = 3323  # Example document ID
    dictExtractedData = {
    "UserName": "APARNA PRAFULL TILWANKAR",
    "InvoiceNo": "IMP0CGPTX0009276",
    "SellerICD": "MMLP TIHI",
    "SellerIRN": "b370f66ccb2fc8919a74a03f1f060c3c9a7a70a6b8124195c28c49cecfcad489",
    "UserName1": "PRAVEEN MAURYA",
    "meta_data": {
        "page_width": "",
        "page_height": ""
    },
    "ItemTable1": [
        {
            "SrNo1": 1,
            "Amount": 500,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 45.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 45.0,
            "AbatedValue": 500,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "DATA CHARGES",
            "AccountingCodeOfService": 998599
        },
        {
            "SrNo1": 2,
            "Amount": 500,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 45.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 45.0,
            "AbatedValue": 500,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "INFRASTRUCTURE & DEVELOPMENT",
            "AccountingCodeOfService": 998599
        },
        {
            "SrNo1": 3,
            "Amount": 1000,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 90.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 90.0,
            "AbatedValue": 1000,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "EQUIPMENT IMBALANCE CHARGE",
            "AccountingCodeOfService": 999799
        },
        {
            "SrNo1": 4,
            "Amount": 1000,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 90.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 90.0,
            "AbatedValue": 1000,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "TERMINAL INFRA CHARGE",
            "AccountingCodeOfService": 996711
        },
        {
            "SrNo1": 5,
            "Amount": 30000,
            "DisRate": 0.0,
            "CGSTRate": 6.0,
            "IGSTRate": 0.0,
            "SGSTRate": 6.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 1800.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 1800.0,
            "AbatedValue": 30000,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "FREIGHT CHARGES",
            "AccountingCodeOfService": 996512
        },
        {
            "SrNo1": 6,
            "Amount": 4150,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 373.5,
            "IGSTAmount": 0.0,
            "SGSTAmount": 373.5,
            "AbatedValue": 4150,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "HANDLING CHARGES",
            "AccountingCodeOfService": 996719
        },
        {
            "SrNo1": 7,
            "Amount": 100,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 9.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 9.0,
            "AbatedValue": 100,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "WEIGHMENT CHARGES",
            "AccountingCodeOfService": 996711
        },
        {
            "SrNo1": 8,
            "Amount": 500,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 45.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 45.0,
            "AbatedValue": 500,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "DOCUMENTATION & SURVEYOR CHARGES",
            "AccountingCodeOfService": 999799
        },
        {
            "SrNo1": 9,
            "Amount": 1600,
            "DisRate": 0.0,
            "CGSTRate": 9.0,
            "IGSTRate": 0.0,
            "SGSTRate": 9.0,
            "Abatement": 0,
            "WvrAmount": 0.0,
            "CGSTAmount": 144.0,
            "IGSTAmount": 0.0,
            "SGSTAmount": 144.0,
            "AbatedValue": 1600,
            "ContainerNumber": "TCLU7307450",
            "PlaceOfSupplyState": 0,
            "ActivityDescOfService": "PVT TRANSPORTATION",
            "AccountingCodeOfService": 996749
        }
    ],
    "SellerName": "Container Corporation of India Ltd.",
    "Designation": "",
    "InvoiceDate": 250625,
    "SellerGSTIN": "23**********2ZU",
    "SellerPANNo": "**********",
    "TotalAmount": 39350.0,
    "TotalDisRate": 0.0,
    "RegisterState": "MADHYA PRADESH",
    "SellerAddress": "MULTIMODAL LOGISTICS PARK NH-3 NEAR RAU,MHOW MADHYA PRADESH- (MP)",
    "TotalAbatement": 0.0,
    "TotalWvrAmount": 0.0,
    "TotalCGSTAmount": 2641.5,
    "TotalIGSTAmount": 0.0,
    "TotalSGSTAmount": 2641.5,
    "UnregisterState": "",
    "TotalAbatedValue": 39350.0,
    "RegisterPartyAddr": "702, APOLLO PREMIER, VIJAY NAGAR SQUARE",
    "RegisterPartyName": "FAIRDEAL INTERNATIONAL",
    "RegisterStateCode": "MP",
    "RegisterPartyGSTIN": "23AAAFF6157B1ZZ",
    "UnregisterPartyAddr": "",
    "UnregisterPartyName": "",
    "UnregisterStateCode": "",
    "RegisterCustomerCode": "MPWRO0CGPTC00804",
    "LsContainerNumber/Size": [
        "TCLU7307450/20"
    ],
    "PlaceOfSupplyStateCode": 23,
    "PlaceOfSupplyStateName": "MADHYA PRADESH",
    "RegisterServiceSegment": "X",
    "TotalInvoiceValueWords": "FORTY-FOUR THOUSAND SIX HUNDRED THIRTY-THREE",
    "TotalInvoiceValueFigure": 44633.0,
    "TotalUpfrontFreightDiscount": 0.0,
    "UnregisterAddressOfDelivery": "",
    "AmountOfTaxSubjectToReverseCharge": 0.0
}
    
    
    dictResponse = await CConcor_XML.MSCreateXML(
        iUserId = iUserId,
        iDocID = iDocId,
        dictExtractedData = dictExtractedData,
        AdditionalExcelTemplate = r"H:\AI Data\DailyData\ICD\2025_06_26\av_pegasus_additionaldetails.xlsx"
    )
    
    CFileHandler.MSWriteFile(strFilePath="temp.xml", 
                                        fileContent=dictResponse, 
                                        strWriteMode="wb", 
                                        strEncoding=None) 
    
    # Print or process the response
    print(dictResponse)

# async def main():
#     input_folder = r"H:\AI Data\18_FairdealInternational\11_UNITED\GPT Extracted Data"  # Set your input folder path here
#     output_folder = r"GitIgnore\United"  # Set your output folder path here
#     os.makedirs(output_folder, exist_ok=True)
    
#     iUserId = 5  # Example user ID
#     iDocId = 3323  # Example document ID
#     AdditionalExcelTemplate = r"GitIgnore\\av_pegasus_additionaldetails.xlsx"

#     for filename in os.listdir(input_folder):
#         if filename.lower().endswith('.json'):
#             file_path = os.path.join(input_folder, filename)
#             with open(file_path, 'r', encoding='utf-8') as f:
#                 dictExtractedData = json.load(f)
#             try:
#                 dictResponse = await CUnited_XML.MSCreateXML(
#                     iUserId=iUserId,
#                     iDocID=iDocId,
#                     AdditionalExcelTemplate=AdditionalExcelTemplate,
#                     dictExtractedData=dictExtractedData,
#                 )
#                 # Save XML output
#                 output_filename = os.path.splitext(filename)[0] + '.xml'
#                 output_path = os.path.join(output_folder, output_filename)
#                 with open(output_path, 'wb') as out_f:
#                     out_f.write(dictResponse)
#                 print(f"Processed {filename} -> {output_filename}")
#             except Exception as e:
#                 print(f"Error processing {filename}: {e}")



if __name__ == "__main__":
    asyncio.run(main())