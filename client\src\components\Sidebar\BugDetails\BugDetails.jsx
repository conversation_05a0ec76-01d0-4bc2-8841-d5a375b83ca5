// BugDetailsModal.jsx
import { useEffect, useRef, useState } from 'react';
import { IoClose } from 'react-icons/io5';
import PropTypes from 'prop-types';
import { FiUpload } from 'react-icons/fi';
import { Chip } from "@material-tailwind/react";
import axios from 'axios';
import toast, { Toaster } from 'react-hot-toast';
import Flatpickr from 'react-flatpickr';
import 'flatpickr/dist/flatpickr.css';
import { formatInTimeZone } from 'date-fns-tz';
import { MdCalendarToday } from "react-icons/md";
import checkIcon from '../../../assets/SVGs/BugDetails/CeckIcon.svg'

const BugDetailsModal = ({ isOpen, onClose, Version }) => {
    const [dateTime, setDateTime] = useState('');
    const [browser, setBrowser] = useState('');
    const [os, setOS] = useState('');
    const [appVersion, setAppVersion] = useState("");
    const [description, setDescription] = useState('');
    const [isSubmitted, setIsSubmitted] = useState(false);
    const [isLoading, setIsLoading] = useState(false);
    const [files, setFiles] = useState([]);
    const [reset, setReset] = useState(false);
    const fileUploadRef = useRef(null);
    const flatpickrRef = useRef(null);

    const handleFileUpload = (event) => {
        const newFiles = Array.from(event.target.files);

        // Filter out files that exceed the 50MB size limit
        const validFiles = newFiles.filter(file => file.size <= 50 * 1024 * 1024);

        // Access the current files state safely
        setFiles(prevFiles => {
            const allFiles = [...prevFiles, ...validFiles];

            // Check if the total number of files exceeds 5
            if (allFiles.length > 5) {
                alert('Maximum 5 files are allowed.');
                return prevFiles; // Return the previous state without adding new files
            }

            return allFiles;
        });
    };

    // Function to remove a file from the upload list
    const removeFile = (fileName) => {
        setFiles(prevFiles => prevFiles.filter(file => file.name !== fileName));
    };

    const handleIconClick = () => {
        if (flatpickrRef.current) {
            flatpickrRef.current.flatpickr.open();
        }
    };

    const handleDescriptionChange = (e) => {
        const text = e.target.value;
        const words = text.split(/\s+/).filter(word => word.length > 0);

        if (words.length <= 5000) {
            setDescription(text);
        }
    };

    useEffect(() => {
        // Set default date-time to current date-time in EDT
        const now = new Date();
        const edtTimezone = 'America/New_York';

        // Format date and time for Flatpickr in the correct format
        const formattedDateTime = formatInTimeZone(now, edtTimezone, "yyyy-MM-dd'T'HH:mm");
        setDateTime(formattedDateTime);

        // Browser and OS detection
        const userAgent = navigator.userAgent;
        let browserName = '';
        let result = bowser.getParser(userAgent);
        if (result && result.parsedResult.browser.name) {
            browserName = result.parsedResult.browser.name;
        }
        setBrowser(browserName);

        let osName = 'Unknown OS';
        if (userAgent.includes("Win")) osName = "Windows";
        else if (userAgent.includes("Mac")) osName = "MacOS";
        else if (userAgent.includes("Linux")) osName = "Linux";
        else if (userAgent.includes("X11")) osName = "UNIX";
        setOS(osName);
        setAppVersion(Version);
        setDescription("");
    }, [reset, onClose]);

    const handleSubmit = async () => {

        // Check if required fields are not empty
        if (!description) {
            toast.error("Please provide a description of the issue.");
            return;
        }

        // Optionally, you can check for other fields, even though they have default values.
        if (!dateTime || !browser || !os || !appVersion) {
            toast.error("Please fill in all fields before submitting the form.");
            return;
        }

        const formData = new FormData();

        // Format dateTime to ensure it's in the correct format
        const formattedDateTime = new Date(dateTime).toISOString().slice(0, 16);

        // Append form data
        formData.append('BugTime', formattedDateTime);
        formData.append('Browser', browser);
        formData.append('OperatingSystem', os);
        formData.append('AppVersion', appVersion);
        formData.append('Description', description);

        // Append files
        files.forEach((file) => {
            formData.append(`documents`, file);
        });
        setIsLoading(true);
        try {
            // Send the request to the server
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/bug-report`, formData, {
                headers: {
                    'Content-Type': 'multipart/form-data',
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
                        
            // Remove Files after successful report
            setFiles([]);
            // Set the state to submitted
            setIsSubmitted(true);
        } catch (error) {
            toast.error(error.response?.data?.detail || "There is some issue raising the bug, Please try again later.");
            console.error('Error submitting bug report:', error);
        }
        setIsLoading(false);
    };

    if (!isOpen) return null;

    return (
        <>
            <Toaster position="top-center" />
            <div className="fixed inset-0 z-50 flex items-center justify-center bg-black bg-opacity-50 p-4 sm:p-6 md:p-8 lg:p-10 xl:p-12 2xl:p-16">
                <>
                    {isSubmitted ? (
                        <div className="bg-white p-8 w-full max-w-[40rem] mx-auto relative rounded-[15.39px] sm:max-w-sm md:max-w-md lg:max-w-lg xl:max-w-xl 2xl:max-w-2xl">
                            <div
                                className="absolute top-4 right-4 cursor-pointer"
                                onClick={() => {
                                    onClose();
                                    setIsSubmitted(false);
                                    setReset((prev) => !prev);
                                }}
                            >
                                <IoClose className="text-[#003654]" size={30} />
                            </div>
                            <div className="flex flex-col items-center text-center">
                                <div className="mb-5"><img src={checkIcon} /></div>
                                <h2 className="text-2xl font-bold mb-4 text-[#003654]">Thank you for reporting the bug!</h2>
                                <p
                                    className="text-gray-700 mb-7"
                                    style={{
                                        fontWeight: 400,
                                        fontSize: "15px",
                                        lineHeight: "22px",
                                        maxWidth: "100%",
                                    }}
                                >
                                    Your bug report has been successfully submitted. Our team will review the details and get back to you shortly.
                                </p>
                                <div className="flex flex-col items-center w-full">
                                    <button
                                        type="button"
                                        className="bg-[#003654] w-full text-white uppercase px-6 py-3 mb-4 rounded-[8px] hover:bg-[#002744] focus:outline-none focus:ring-[#003654]"
                                        onClick={() => {
                                            onClose();
                                            setIsSubmitted(false);
                                            setReset((prev) => !prev);
                                        }}
                                        style={{
                                            fontWeight: 400,
                                            fontSize: "14px",
                                            lineHeight: "22.4px",
                                        }}
                                    >
                                        Close this window
                                    </button>
                                    <button
                                        type="button"
                                        className="text-[#003654] w-full uppercase rounded-[8px] px-6 py-2 focus:outline-none hover:bg-[#0027446e] hover:text-white"
                                        onClick={() => {
                                            setIsSubmitted(false);
                                            setReset((prev) => !prev);
                                        }}
                                        style={{
                                            fontWeight: 600,
                                            fontSize: "16px",
                                            lineHeight: "23.4px",
                                        }}
                                    >
                                        Report another bug
                                    </button>
                                </div>
                            </div>
                        </div>
                    ) : (
                        <div className="bg-white p-8 w-full max-w-[50rem] mx-auto overflow-y-auto relative rounded-[15.39px] sm:max-w-sm md:max-w-md md:h-[90vh] lg:max-w-lg lg:h-[90vh] xl:max-w-xl xl:h-[90vh] 2xl:max-w-2xl">
                            <div
                                className="absolute top-4 right-4 cursor-pointer"
                                onClick={() => {
                                    onClose();
                                    setFiles([]);
                                }}
                            >
                                <IoClose className="text-[#003654]" size={30} />
                            </div>
                            <h2
                                className="text-2xl font-bold mb-4 text-center text-[#003654]"
                                style={{
                                    fontWeight: 800,
                                    fontSize: "33px",
                                    lineHeight: "36px",
                                }}
                            >
                                Bug Details!
                            </h2>
                            <p
                                className="text-center mb-6 text-[#575F6E]"
                                style={{
                                    fontWeight: 300,
                                    fontSize: "15px",
                                    lineHeight: "16px",
                                }}
                            >
                                Provide a detailed description of the issue you encountered.
                            </p>
                            <form>
                                <div className="mb-4">
                                    <label className="block text-[#242426] font-semibold mb-2">
                                        Date and Time<span className="text-red-500">*</span>
                                    </label>
                                    <div className='flex'>
                                        <Flatpickr
                                            disabled={isLoading}
                                            ref={flatpickrRef}
                                            data-enable-time
                                            value={dateTime}
                                            onChange={(selectedDates) => {
                                                setDateTime(selectedDates[0]);
                                            }}
                                            options={{
                                                enableTime: true,
                                                dateFormat: 'Y-m-d H:i',
                                            }}
                                            className="w-full border-b italic border-gray-300 p-2 focus:outline-none focus:ring-[#003654] cursor-pointer"
                                        />
                                        <MdCalendarToday className='absolute cursor-pointer end-7 mt-3 mr-4' onClick={handleIconClick} />
                                    </div>
                                </div>
                                <div className="mb-4">
                                    <label className="block text-[#242426] font-semibold mb-2">Browser<span className="text-red-500">*</span></label>
                                    <input
                                        disabled={isLoading} // Disable when loading
                                        type="text"
                                        placeholder="Select the browser and its version you were using when the issue happened"
                                        className="w-full italic border-b border-gray-300 p-2 focus:outline-none focus:ring-[#003654]"
                                        value={browser}
                                        onChange={(e) => setBrowser(e.target.value)}
                                    />
                                </div>
                                <div className="mb-4">
                                    <label className="block text-[#242426] font-semibold mb-2">Operating System<span className="text-red-500">*</span></label>
                                    <input
                                        disabled={isLoading} // Disable when loading
                                        type="text"
                                        placeholder="Choose the operating system you were using when the bug occurred."
                                        className="w-full italic border-b border-gray-300 p-2 focus:outline-none focus:ring-[#003654]"
                                        value={os}
                                        onChange={(e) => setOS(e.target.value)}
                                    />
                                </div>
                                <div className="mb-4">
                                    <label className="block text-[#242426] font-semibold mb-2">App Version</label>
                                    <input
                                        type="text"
                                        disabled
                                        placeholder="Specify the version of AccuVelocity you are currently using"
                                        className="w-full italic border-b border-gray-300 p-2 focus:outline-none focus:ring-[#003654]"
                                        value={appVersion}
                                        onChange={(e) => setAppVersion(e.target.value)}
                                    />
                                </div>
                                <div className="mb-4">
                                    <label className="block text-[#242426] font-semibold mb-2">Description of the Issue<span className="text-red-500">*</span></label>
                                    <textarea
                                        disabled={isLoading} // Disable when loading
                                        placeholder="Describe the issue in detail. What went wrong?"
                                        className="w-full border-b italic resize-none border-gray-300 p-2 focus:outline-none focus:ring-[#003654]"
                                        value={description}
                                        onChange={handleDescriptionChange}
                                    ></textarea>
                                </div>
                                <div className="mb-6 relative">
                                    <p
                                        className="text-[#8E8E8E] my-3.5"
                                        style={{
                                            fontWeight: 400,
                                            fontSize: "13px",
                                            lineHeight: "20.8px",
                                            maxWidth: "100%",
                                        }}
                                    >
                                        Attach Screenshots(Upto 5 Only). File size of your screenshots should not exceed 50MB.
                                    </p>
                                    <input
                                        disabled={isLoading} // Disable when loading
                                        ref={fileUploadRef}
                                        type="file"
                                        multiple
                                        onChange={handleFileUpload}
                                        className="absolute cursor-pointer w-full opacity-0 z-10"
                                        accept=".png,.jpg,.jpeg,.gif,.mp4,.bmp,.tiff,.tif,.heic,.heif,.webp,.txt,.csv,.doc,.docx,.xls,.xlsx,.json,.mov"
                                    />
                                    <div
                                        className="border-dashed border-2 p-4 cursor-pointer flex flex-col items-center justify-center h-[5rem] border-[#003654]"
                                        onClick={() => fileUploadRef.current.click()}
                                    >
                                        <div className="flex text-[#003654]">
                                            <FiUpload className="mt-1 mr-2" />
                                            Upload Screenshots / File
                                        </div>
                                    </div>
                                    {files.length > 0 && (
                                        <div className="mt-4 flex overflow-x-auto space-x-2">
                                            {files.map((file, index) => (
                                                <div key={index} className="flex items-center text-sm bg-gray-100 rounded-lg p-2">
                                                    <Chip color="green" value={file.name} className="truncate max-w-xs" />
                                                    <button
                                                        type="button"
                                                        className={`text-[#003654] bg-transparent border-none cursor-pointer ml-2 ${isLoading ? 'disabled:cursor-not-allowed' : ''}`}
                                                        onClick={(e) => {
                                                            e.stopPropagation();
                                                            removeFile(file.name);
                                                        }}
                                                        disabled={isLoading} // Disable when loading
                                                    >
                                                        <IoClose className="text-[#003654]" size={16} />

                                                    </button>
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>

                                <div className="flex justify-center">
                                    <button
                                        type="button"
                                        style={{
                                            fontWeight: 400,
                                            fontSize: "14px",
                                            lineHeight: "22.4px",
                                            alignItems: "center",
                                        }}
                                        className="bg-[#003654] hover:bg-[#002744] w-full text-white uppercase px-6 py-2 rounded-[8px] focus:outline-none focus:ring-[#003654]"
                                        onClick={() => {
                                            handleSubmit();
                                        }}
                                    >
                                        {isLoading ? (
                                            <div className="flex items-center justify-center">
                                                <svg
                                                    className="animate-spin h-5 w-5 mr-3 text-white"
                                                    xmlns="http://www.w3.org/2000/svg"
                                                    fill="none"
                                                    viewBox="0 0 24 24"
                                                >
                                                    <circle
                                                        className="opacity-25"
                                                        cx="12"
                                                        cy="12"
                                                        r="10"
                                                        stroke="currentColor"
                                                        strokeWidth="4"
                                                    ></circle>
                                                    <path
                                                        className="opacity-75"
                                                        fill="currentColor"
                                                        d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4z"
                                                    ></path>
                                                </svg>
                                                Please wait...
                                            </div>
                                        ) : (
                                            "Submit"
                                        )}
                                    </button>
                                </div>
                            </form>
                        </div>
                    )}
                </>
            </div>
        </>
    );
};

BugDetailsModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    Version: PropTypes.string.isRequired,
};

export default BugDetailsModal;
