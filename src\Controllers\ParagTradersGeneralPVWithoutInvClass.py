import sys
sys.path.append(".")
from src.Controllers.Logs_Controller import CLogController
import json
import re
import math
from datetime import datetime
import traceback
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, LedgerEntrySchema, TallyPurchaseVoucherSchema, PartyDetailsSchema, ConsigneeDetailsSchema
import os
import asyncio
from collections import defaultdict
import calendar
import uuid
import xml.etree.ElementTree as ET

class ParagTradersGeneralPVWithoutInvClass:

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    _mStrLedgerName = ""
    _mDictPartyData = {
        "party_name": "",
        "address_list": "",
        "gst_registration_type": "",
        "gst_in": "",
        "state_name": "",
        "country_name": "",
        "pin_code": ""
    }
    _mDictCosigneeData = {
                        "address_list": ["(A UNIT OF VHD DISTRIBUTORS LLP)", "12/4,RACE COURSE ROAD,INDORE","PH:0731-2535659/2535660","TIN-23579075743"],
                        "gst_in": "23AAKFV4306N1ZX",
                        "mailing_name": "PARAG TRADERS (GST)",
                        "state_name": "Madhya Pradesh",
                        "pin_code": "",
                        "email_id":"<EMAIL>",
                        "country_name": "India"
    }

    _mDictCompanyData = {
                        "company_name": "PARAG TRADERS (24-25)",
                        "address_list": ["(A UNIT OF VHD DISTRIBUTORS LLP)", "12/4,RACE COURSE ROAD,INDORE","PH:0731-2535659/2535660","TIN-23579075743"],
                        "gst_registration_type": "Regular",
                        "gst_in": "23AAKFV4306N1ZX",
                        "state_name": "Madhya Pradesh",
                        "country_name": "India",
                        "email_id":"<EMAIL>"
    }
    _mstrVoucherTypeName = ""
    _mstrVocuherEntryMode = ""
    _mstrRoundOffLedger = ""
    _mstrDebitLedgerName = None 
    _mstrCostCenterLocation = ""
    _mIUserId = None
    _mDebitData = {}
    _mRoundOffLedger = ""

    @classmethod
    def MCResetAttributes(cls):
        cls._mStrLedgerName = ""
        cls._mDictCompanyData = {
            "company_name": "",
            "gst_registration_type": "",
            "state_name": "",
            "country_name": "",
            "gst_in": "",
        }
        cls._mDictCosigneeData = {}
        cls._mDictPartyData={}
        cls._mstrVoucherTypeName = ""
        cls._mstrVocuherEntryMode = ""
        cls._mstrRoundOffLedger = ""
        cls._mstrDebitLedgerName = None
        cls._mstrCostCenterLocation = ""
        cls._mIUserId = None
        cls._mDebitData = {}
        cls._mRoundOffLedger = ""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @classmethod
    async def MCSetAttributes(cls, strConfigFilePath: str, strDebitLedgerPath: str,iUserId: int, strCostCenterLocation = None):

        """
            Load and initialize class attributes from a configuration JSON file.

            This method reads a JSON file specified by the path `strConfigFilePath`, extracts various settings,
            and assigns them to class-level attributes. It also logs the process and handles errors related to
            file reading, JSON parsing, and missing keys.

            Parameters:
                strConfigFilePath (str): Path to the configuration JSON file.
                iUserId (int): The user ID for logging and tracking purposes.
                boolTDSApplied (bool, optional): Indicates whether TDS is applied. Defaults to False.
                strCategory (str, optional): Optional category tag to be assigned. Defaults to None.
                strCostCenterLocation (str, optional): Optional cost center location. Defaults to None.
            
            Returns: None
           
        """

        try:
            cls.MCResetAttributes()
            ParagTradersGeneralPVWithoutInvClass._mIUserId = iUserId
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Info","Loading Config file and Initializing attributes")
            # Open and load the JSON file
            with open(strConfigFilePath, 'r') as file:
                data = json.load(file)

            #open and load Debit JSON file
            with open(strDebitLedgerPath, 'r') as file:
                debit_data = json.load(file)
            
            cls._mRoundOffLedger = data.get("RoundOffLedger", {}).get("LedgerName")
            cls._mDebitData = debit_data
            # cls._mDictCompanyData = data
            cls._mbIsIGSTApplied = data.get("bIGSTApplied", False)
            # Set the ledger name from DebitLedger.PurchaseLedgerName
            cls._mStrLedgerName = data["VendorName"]
            # cls._mstrDebitLedgerName = data["DebitLedger"]["PurchaseLedgerName"]
            # Extract CompanyData for easier access
            company_data = data["CompanyData"]
            # Update the company data dictionary
            cls._mDictCompanyData["company_name"] = company_data["ComapanyName"]
            cls._mDictCompanyData["gst_registration_type"] = company_data["GstRegistrationType"]
            cls._mDictCompanyData["state_name"] = company_data["StateName"]
            cls._mDictCompanyData["country_name"] = company_data["CountryName"]
            cls._mDictCompanyData["gst_in"] = company_data["GstIn"]
            
            # Update the consignee data dictionary
            consignee_details = data['ConsigneeDetails']
            cls._mDictCosigneeData['address_list'] = consignee_details["Address"]
            cls._mDictCosigneeData["gst_in"] = consignee_details["GSTIN"]
            cls._mDictCosigneeData["mailing_name"] = consignee_details["MailingName"]
            cls._mDictCosigneeData["state_name"] = consignee_details["State"]
            cls._mDictCosigneeData["country_name"] = consignee_details["Country"]
            cls._mDictCosigneeData["pin_code"] = consignee_details["PinCode"]

            # Update the party data dictionary
            party_details = data['VendorDetails']
            cls._mDictPartyData['party_name'] = data["VendorName"]
            cls._mDictPartyData["address_list"] = party_details["Address"]
            cls._mDictPartyData["gst_registration_type"] = party_details["GstRegistrationType"]
            cls._mDictPartyData["gst_in"] = party_details["GSTIN"]
            cls._mDictPartyData["state_name"] = party_details["State"]
            cls._mDictPartyData["country_name"] = party_details["Country"]
            cls._mDictPartyData["pin_code"] = party_details["PinCode"]
            
            if not data['bisGSTApplied']:
                cls._mstrDebitLedgerName = debit_data["MainDebitLedgerInfo"][0]["Details"]["TallyLedgerName"]

            
            # Set other string attributes
            cls._mstrVoucherTypeName = data["VoucherTypeName"]
            cls._mstrVocuherEntryMode = data["VCHEntryMode"]
            cls._mstrRoundOffLedger = data["RoundOffLedger"]
            cls._mstrCostCenterLocation =  data.get("CostCenterName") or ""
        except FileNotFoundError:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Error",f"The file '{strConfigFilePath}' was not found.")
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"The file '{strConfigFilePath}' was not found.")
        except json.JSONDecodeError as e:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Error",f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
        except KeyError as e:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"Missing required field in JSON: {str(e)}")


    
    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData):
        """
        Returns the ledger entry for the vendor Zeel Pest Solution Llp.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        dictCreditLedgerInfo = {
            "ledger_name": ParagTradersGeneralPVWithoutInvClass._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Credit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            # lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise

        return dictCreditLedgerInfo


    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates a debit ledger entry based on the extracted data and cost center settings.

            If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """

        try:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Info","Getting Debit Ledger")
            debit_ledger = []
            if ParagTradersGeneralPVWithoutInvClass._mstrDebitLedgerName:
                entry = {
                    "ledger_name": ParagTradersGeneralPVWithoutInvClass._mstrDebitLedgerName,
                    "amount": -dictExtractedData.get("TotalAmount",0),
                    "is_deemed_positive": True,
                    "is_party_ledger": False,
                }
                debit_ledger.append(entry)
            else:
                # Check if bIGSTApplied flag is present and True
                if ParagTradersGeneralPVWithoutInvClass._mbIsIGSTApplied:
                    
                    # Add main ledger
                    main_ledger = {
                            "ledger_name": ParagTradersGeneralPVWithoutInvClass._mDebitData.get("MainDebitLedgerInfo", [])[0].get('Details',"").get('TallyLedgerName'),
                            "amount": -dictExtractedData.get('SubTotal',0),
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                        }
                    debit_ledger.append(main_ledger)
                    
                    # Add IGST ledger entry
                    igst_amount = dictExtractedData.get("Taxes", {}).get("MainTaxes", [{}]).get('IGST')[0]['TaxAmount']
                    igst_ledger = {
                        "ledger_name": ParagTradersGeneralPVWithoutInvClass._mDebitData.get("MainDebitLedgerInfo", [])[0].get('Details',"").get("TaxDetails","")[0].get('TaxLedgerName',""),
                        "amount": -round(igst_amount, 2),
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                    }
                    debit_ledger.append(igst_ledger)
                    
                else:
                    # Original else block
                    taxes = dictExtractedData.get("Taxes", {}).get("MainTaxes", [])
                    ledger_infos = ParagTradersGeneralPVWithoutInvClass._mDebitData.get("MainDebitLedgerInfo", [])

                    # Step 2: Group tax amounts by tax rate
                    tax_data_by_rate = defaultdict(lambda: {"CGST": 0, "SGST": 0, "TaxableAmount": 0})
                    total_cgst = 0
                    total_sgst = 0
                    cgst_ledger_name = None
                    sgst_ledger_name = None

                    for tax in taxes:
                        rate = tax.get("TaxRate", 0)
                        tax_name = tax.get("TaxName", "")
                        tax_data_by_rate[rate]["TaxableAmount"] += tax.get("TaxableAmount", 0)
                        tax_data_by_rate[rate][tax.get("TaxName", "")] += tax.get("TaxAmount", 0)

                        # Also accumulate totals for combined CGST/SGST entries
                        if tax_name == "CGST":
                            total_cgst += tax.get("TaxAmount", 0)
                        elif tax_name == "SGST":
                            total_sgst += tax.get("TaxAmount", 0)

                    # Step 3: Match ledger info and create ledger entries
                    for rate, tax_data in tax_data_by_rate.items():
                        matched_ledger = next(
                            (
                                info for info in ledger_infos
                                if all(
                                    any(
                                        detail.get("TaxName") == tax_type and detail.get("TaxRate") == rate
                                        for detail in info.get("Details", {}).get("TaxDetails", [])
                                    )
                                    for tax_type in ["CGST", "SGST"]
                                )
                            ),
                            None
                        )

                        if not matched_ledger:
                            continue  # Skip if no matching ledger found

                        # Main ledger for taxable amount
                        total_charge_amount = sum(item.get("ChargeAmount", 0) for item in dictExtractedData.get("Charges", []))

                        fTotalAmount = dictExtractedData.get("SubTotal",0) + total_charge_amount
                        main_ledger = {
                            "ledger_name": matched_ledger["Details"]["TallyLedgerName"],
                            "amount": -fTotalAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                        }
                        debit_ledger.append(main_ledger)

                        tax_details = matched_ledger["Details"].get("TaxDetails", [])
                        # CGST Ledger Name Based On Rate
                        cgst_ledger_name = next(
                            (t.get("TaxLedgerName") for t in tax_details if t.get("TaxName") == "CGST" and t.get("TaxRate") == rate),
                            "CGST Input A/C"
                        )

                        # CGST ledger
                        if tax_data["CGST"]:
                            cgst_ledger = {
                                "ledger_name": cgst_ledger_name,
                                "amount": -round(total_cgst, 2),
                                "is_deemed_positive": True,
                                "is_party_ledger": False,
                            }
                            debit_ledger.append(cgst_ledger)
                        
                        # SGST Ledger Name Based On Rate
                        sgst_ledger_name = next(
                            (t.get("TaxLedgerName") for t in tax_details if t.get("TaxName") == "SGST" and t.get("TaxRate") == rate),
                            "SGST Input A/C"
                        )
                        # SGST ledger
                        if tax_data["SGST"]:
                            sgst_ledger = {
                                "ledger_name": sgst_ledger_name,
                                "amount": -round(total_sgst, 2),
                                "is_deemed_positive": True,
                                "is_party_ledger": False,
                            }
                            debit_ledger.append(sgst_ledger)   

                # Adding RoundOff Ledger If it does exist
                fRoundOff = dictExtractedData.get("RoundingOff", 0)
                if fRoundOff != 0:
                    if fRoundOff < 0:
                        fRoundOff = abs(fRoundOff)
                    else:
                        fRoundOff = -fRoundOff
                    roundoff_ledger = {
                        "ledger_name": ParagTradersGeneralPVWithoutInvClass._mRoundOffLedger,
                        "amount": fRoundOff,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                    }
                    debit_ledger.append(roundoff_ledger)

            return debit_ledger       
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId,"Error",objException)
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise objException

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None, strNarration = None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            ParagTradersGeneralPVWithoutInvClass.MCResetAttributes()
            await ParagTradersGeneralPVWithoutInvClass.MCSetAttributes(strConfigPath, strDebitLedgerPath, iUserId)
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await ParagTradersGeneralPVWithoutInvClass.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await ParagTradersGeneralPVWithoutInvClass.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            
            ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            for objItem in debit_ledger:
                ledger_entries.append(LedgerEntrySchema(**objItem))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")
            invoice_no = invoice_no if invoice_no else dictExtractedData.get("Bilty/LRNO", "") or str(int(uuid.uuid4().int % 10000)).zfill(4)
            objCompanyInfo = CompanyInfoSchema(**ParagTradersGeneralPVWithoutInvClass._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**ParagTradersGeneralPVWithoutInvClass._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**ParagTradersGeneralPVWithoutInvClass._mDictCosigneeData)

            if strNarration == None:
                strNarration = await ParagTradersGeneralPVWithoutInvClass.MSGetNarration(invoice_date, dictExtractedData)
            

            voucher_input = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                invoice_date=invoice_date,
                narration=strNarration,
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=f'AV {ParagTradersGeneralPVWithoutInvClass._mstrVoucherTypeName}',
                cost_center_name = ParagTradersGeneralPVWithoutInvClass._mstrCostCenterLocation,
                invoice_no=invoice_no,
                udf_data = lsUdfData,
                ledger_entries=ledger_entries,
                voucher_entry_mode = ParagTradersGeneralPVWithoutInvClass._mstrVocuherEntryMode
            )

            # ----------------------- Generate XML via the Template --------------------------
            xml_str = voucher_input.to_string(pretty=True)
            xml_str = await ParagTradersGeneralPVWithoutInvClass.MSCleanTallySXML(xml_str)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse
    
    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO {ParagTradersGeneralPVWithoutInvClass._mStrLedgerName} FOR INV NO. {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}.'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            raise
    
    @staticmethod
    async def MSCleanTallySXML(xml_str: str) -> str:
        # Parse the XML string
        try:
            root = ET.fromstring(xml_str)

            # Modify DIFFACTUALQTY tag value to No
            for elem in root.findall(".//DIFFACTUALQTY"):
                elem.text = "No"
            
            # Modify ISCOSTCENTRE tag value to No
            for elem in root.findall(".//ISCOSTCENTRE"):
                elem.text = "No"
            
            # Remove the first occurrence of VATEXPAMOUNT tag
            vat_exp_amount = root.find(".//VATEXPAMOUNT")
            if vat_exp_amount is not None:
                parent = root.find(".//VATEXPAMOUNT/..")
                if parent is not None:
                    parent.remove(vat_exp_amount)

            cleaned_xml = ET.tostring(root, encoding='unicode')
            return cleaned_xml
        except Exception as e:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._miUserId, "Error", e)
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "MSCleanTallySXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
            raise
class CDMultiMediaGallery(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO CD MULTI MEDIA GALLERY FOR {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}. FOR AMC M/O,{prev_month_formatted}'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            CDMultiMediaGallery._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            try:
                strNarration = await CSainathEngineeringServices.MSGetNarration(dictExtractedData["InvoiceDate"], dictExtractedData)
            except Exception as e:
                dictXMLResponse["AVComments"] = "Kindly enter the narration manually in the voucher"
                strNarration = ""

            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData, strNarration)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CSainathEngineeringServices(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO SAINATH ENGINEERING SERVICES FOR INV NO. {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}. FOR 100 KVA DG M/O,{prev_month_formatted}'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            CSainathEngineeringServices._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            try:
                strNarration = await CSainathEngineeringServices.MSGetNarration(dictExtractedData["InvoiceDate"], dictExtractedData)
            except Exception as e:
                dictXMLResponse["AVComments"] = "Kindly enter the narration manually in the voucher"
                strNarration = ""
            
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData, strNarration)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CMohitEnterprises(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO MOHIT ENTERPRISES FOR INV NO. {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}.'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            CSainathEngineeringServices._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            try:
                strNarration = await CMohitEnterprises.MSGetNarration(dictExtractedData["InvoiceDate"], dictExtractedData)
            except Exception as e:
                dictXMLResponse["AVComments"] = "Kindly enter the narration manually in the voucher"
                strNarration = ""
            
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData, strNarration)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CMangalHardwarePaints(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO MANGAL HARDWARE AND PAINTS FOR INV NO. {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}.'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            CSainathEngineeringServices._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            try:
                strNarration = await CMangalHardwarePaints.MSGetNarration(dictExtractedData["InvoiceDate"], dictExtractedData)
            except Exception as e:
                dictXMLResponse["AVComments"] = "Kindly enter the narration manually in the voucher"
                strNarration = ""
            
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData, strNarration)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CGulabHardwareStores(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO GULAB HARDWARE STORES FOR INV NO. {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}.'
        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            CSainathEngineeringServices._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            try:
                strNarration = await CGulabHardwareStores.MSGetNarration(dictExtractedData["InvoiceDate"], dictExtractedData)
            except Exception as e:
                dictXMLResponse["AVComments"] = "Kindly enter the narration manually in the voucher"
                strNarration = ""
            
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData, strNarration)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CNovelExicconPvtLtd(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:          
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CShreehantinathTraders(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:          
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CShreeRadhaKrishaEnterprises(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:          
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CShriRamEnterprise(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:          
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse

class CJRTechoChem(ParagTradersGeneralPVWithoutInvClass):

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:          
            dictResponse = await ParagTradersGeneralPVWithoutInvClass.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(ParagTradersGeneralPVWithoutInvClass._mIUserId, "Error", objException)
            ParagTradersGeneralPVWithoutInvClass._msTallyStatus = "Skipped"
            ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = ParagTradersGeneralPVWithoutInvClass._msTallyStatus
            dictXMLResponse["AVComments"] = ParagTradersGeneralPVWithoutInvClass._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = ParagTradersGeneralPVWithoutInvClass._mStrTracebackLogs
            return dictXMLResponse



# NOTE Eedge Case class. Reason treats Charges Differently than other class
class CShriMahakalRoadLines:

    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    _mStrLedgerName = ""
    _mDictPartyData = {
        "party_name": "",
        "address_list": "",
        "gst_registration_type": "",
        "gst_in": "",
        "state_name": "",
        "country_name": "",
        "pin_code": ""
    }
    _mDictCosigneeData = {
                        "address_list": ["(A UNIT OF VHD DISTRIBUTORS LLP)", "12/4,RACE COURSE ROAD,INDORE","PH:0731-2535659/2535660","TIN-23579075743"],
                        "gst_in": "23AAKFV4306N1ZX",
                        "mailing_name": "PARAG TRADERS (GST)",
                        "state_name": "Madhya Pradesh",
                        "pin_code": "",
                        "email_id":"<EMAIL>",
                        "country_name": "India"
    }

    _mDictCompanyData = {
                        "company_name": "PARAG TRADERS (24-25)",
                        "address_list": ["(A UNIT OF VHD DISTRIBUTORS LLP)", "12/4,RACE COURSE ROAD,INDORE","PH:0731-2535659/2535660","TIN-23579075743"],
                        "gst_registration_type": "Regular",
                        "gst_in": "23AAKFV4306N1ZX",
                        "state_name": "Madhya Pradesh",
                        "country_name": "India",
                        "email_id":"<EMAIL>"
    }
    _mstrVoucherTypeName = ""
    _mstrVocuherEntryMode = ""
    _mstrRoundOffLedger = ""
    _mstrDebitLedgerName = "" 
    _mstrCostCenterLocation = ""
    _mIUserId = None
    _mDebitData = {}
    _mRoundOffLedger = ""

    @classmethod
    def MCResetAttributes(cls):
        cls._mStrLedgerName = ""
        cls._mDictCompanyData = {
            "company_name": "",
            "gst_registration_type": "",
            "state_name": "",
            "country_name": "",
            "gst_in": "",
        }
        cls._mDictCosigneeData = {}
        cls._mDictPartyData={}
        cls._mstrVoucherTypeName = ""
        cls._mstrVocuherEntryMode = ""
        cls._mstrRoundOffLedger = ""
        cls._mstrDebitLedgerName = ""
        cls._mstrCostCenterLocation = ""
        cls._mIUserId = None
        cls._mDebitData = {}
        cls._mRoundOffLedger = ""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @classmethod
    async def MCSetAttributes(cls, strConfigFilePath: str, strDebitLedgerPath: str,iUserId: int, strCostCenterLocation = None):

        """
            Load and initialize class attributes from a configuration JSON file.

            This method reads a JSON file specified by the path `strConfigFilePath`, extracts various settings,
            and assigns them to class-level attributes. It also logs the process and handles errors related to
            file reading, JSON parsing, and missing keys.

            Parameters:
                strConfigFilePath (str): Path to the configuration JSON file.
                iUserId (int): The user ID for logging and tracking purposes.
                boolTDSApplied (bool, optional): Indicates whether TDS is applied. Defaults to False.
                strCategory (str, optional): Optional category tag to be assigned. Defaults to None.
                strCostCenterLocation (str, optional): Optional cost center location. Defaults to None.
            
            Returns: None
           
        """

        try:
            cls.MCResetAttributes()
            CShriMahakalRoadLines._mIUserId = iUserId
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Info","Loading Config file and Initializing attributes")
            # Open and load the JSON file
            with open(strConfigFilePath, 'r') as file:
                data = json.load(file)

            #open and load Debit JSON file
            with open(strDebitLedgerPath, 'r') as file:
                debit_data = json.load(file)
            
            cls._mRoundOffLedger = data.get("RoundOffLedger", {}).get("LedgerName")
            cls._mDebitData = debit_data
            # Set the ledger name from DebitLedger.PurchaseLedgerName
            cls._mStrLedgerName = data["VendorName"]
            cls._mstrDebitLedgerName = debit_data["MainDebitLedgerInfo"][0]["Details"]["TallyLedgerName"]
            # Extract CompanyData for easier access
            company_data = data["CompanyData"]
            # Update the company data dictionary
            cls._mDictCompanyData["company_name"] = company_data["ComapanyName"]
            cls._mDictCompanyData["gst_registration_type"] = company_data["GstRegistrationType"]
            cls._mDictCompanyData["state_name"] = company_data["StateName"]
            cls._mDictCompanyData["country_name"] = company_data["CountryName"]
            cls._mDictCompanyData["gst_in"] = company_data["GstIn"]
            
            # Update the consignee data dictionary
            consignee_details = data['ConsigneeDetails']
            cls._mDictCosigneeData['address_list'] = consignee_details["Address"]
            cls._mDictCosigneeData["gst_in"] = consignee_details["GSTIN"]
            cls._mDictCosigneeData["mailing_name"] = consignee_details["MailingName"]
            cls._mDictCosigneeData["state_name"] = consignee_details["State"]
            cls._mDictCosigneeData["country_name"] = consignee_details["Country"]
            cls._mDictCosigneeData["pin_code"] = consignee_details["PinCode"]

            # Update the party data dictionary
            party_details = data['VendorDetails']
            cls._mDictPartyData['party_name'] = data["VendorName"]
            cls._mDictPartyData["address_list"] = party_details["Address"]
            cls._mDictPartyData["gst_registration_type"] = party_details["GstRegistrationType"]
            cls._mDictPartyData["gst_in"] = party_details["GSTIN"]
            cls._mDictPartyData["state_name"] = party_details["State"]
            cls._mDictPartyData["country_name"] = party_details["Country"]
            cls._mDictPartyData["pin_code"] = party_details["PinCode"]

            
            # Set other string attributes
            cls._mstrVoucherTypeName = data["VoucherTypeName"]
            cls._mstrVocuherEntryMode = data["VCHEntryMode"]
            cls._mstrRoundOffLedger = data["RoundOffLedger"]
            cls._mstrCostCenterLocation =  data.get("CostCenterName") or ""
        except FileNotFoundError:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Error",f"The file '{strConfigFilePath}' was not found.")
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"The file '{strConfigFilePath}' was not found.")
        except json.JSONDecodeError as e:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Error",f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
        except KeyError as e:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise Exception(f"Missing required field in JSON: {str(e)}")    
        except Exception as e:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise e

    
    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData):
        """
        Returns the ledger entry for the vendor Zeel Pest Solution Llp.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        dictCreditLedgerInfo = {
            "ledger_name": CShriMahakalRoadLines._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Credit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            # lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise

        return dictCreditLedgerInfo


    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates a debit ledger entry based on the extracted data and cost center settings.

            If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """

        try:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Info","Getting Debit Ledger")
            debit_ledger = []
            entry = {
                    "ledger_name": CShriMahakalRoadLines._mstrDebitLedgerName,
                    "amount": -dictExtractedData.get("TotalAmount",0),
                    "is_deemed_positive": True,
                    "is_party_ledger": False,
                }
            debit_ledger.append(entry)
            return debit_ledger       
        except Exception as objException:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId,"Error",objException)
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMCSetAttributes:\n" + str(traceback.format_exc())
            raise objException

    @staticmethod
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None, strNarration = None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CShriMahakalRoadLines.MCResetAttributes()
            await CShriMahakalRoadLines.MCSetAttributes(strConfigPath, strDebitLedgerPath, iUserId)
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CShriMahakalRoadLines.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CShriMahakalRoadLines.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            
            ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            for objItem in debit_ledger:
                ledger_entries.append(LedgerEntrySchema(**objItem))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")
            invoice_no = invoice_no if invoice_no else dictExtractedData.get("Bilty/LRNO", "") or str(int(uuid.uuid4().int % 10000)).zfill(4)
            objCompanyInfo = CompanyInfoSchema(**CShriMahakalRoadLines._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShriMahakalRoadLines._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CShriMahakalRoadLines._mDictCosigneeData)

            if strNarration == None:
                strNarration = await CShriMahakalRoadLines.MSGetNarration(invoice_date, dictExtractedData)
            

            voucher_input = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                invoice_date=invoice_date,
                narration=strNarration,
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type=f'AV {CShriMahakalRoadLines._mstrVoucherTypeName}',
                cost_center_name = CShriMahakalRoadLines._mstrCostCenterLocation,
                invoice_no=invoice_no,
                udf_data = lsUdfData,
                ledger_entries=ledger_entries,
                voucher_entry_mode = CShriMahakalRoadLines._mstrVocuherEntryMode
            )

            # ----------------------- Generate XML via the Template --------------------------
            xml_str = voucher_input.to_string(pretty=True)
            xml_str = await ParagTradersGeneralPVWithoutInvClass.MSCleanTallySXML(xml_str)
            CShriMahakalRoadLines._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CShriMahakalRoadLines._msTallyStatus
            dictXMLResponse["AVComments"] = CShriMahakalRoadLines._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CShriMahakalRoadLines._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId, "Error", objException)
            CShriMahakalRoadLines._msTallyStatus = "Skipped"
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CShriMahakalRoadLines._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CShriMahakalRoadLines._msTallyStatus
            dictXMLResponse["AVComments"] = CShriMahakalRoadLines._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CShriMahakalRoadLines._mStrTracebackLogs
            return dictXMLResponse
    
    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            # Parse invoice_date to datetime
            invoice_dt = datetime.strptime(invoice_date, "%Y%m%d")

            # Calculate previous month
            if invoice_dt.month == 1:
                prev_month = 12
            else:
                prev_month = invoice_dt.month - 1
                

            prev_month_str = calendar.month_abbr[prev_month].upper()
            prev_month_formatted = f"{prev_month_str}.{str(invoice_dt.year)[-2:]}"
            return f'PAID TO {CShriMahakalRoadLines._mStrLedgerName} FOR {dictExtractedData.get("InvoiceNo", "")} DT {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d.%m.%Y")}.'
        except Exception as objException:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId, "Error", objException)
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

class CMaaNaramadaLogistics(CShriMahakalRoadLines):
    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    
    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""
    
    async def MSCreateXML(strConfigPath: str, strDebitLedgerPath : str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
           
            
            dictResponse = await CShriMahakalRoadLines.MSCreateXML(strConfigPath, strDebitLedgerPath, iUserId, dictExtractedData, lsUdfData)
            dictXMLResponse["xmlContent"] = dictResponse['xmlContent']
            dictXMLResponse["TallyStatus"] = dictResponse["TallyStatus"]
            dictXMLResponse["AVComments"] = dictXMLResponse["AVComments"] if (dictResponse["TallyStatus"] == "Success" and dictXMLResponse["AVComments"] is not None) else dictResponse["AVComments"]
            dictXMLResponse["strTracebackLogs"] =  dictResponse["strTracebackLogs"]
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CShriMahakalRoadLines._mIUserId, "Error", objException)
            CShriMahakalRoadLines._msTallyStatus = "Skipped"
            CShriMahakalRoadLines._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CShriMahakalRoadLines._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CShriMahakalRoadLines._msTallyStatus
            dictXMLResponse["AVComments"] = CShriMahakalRoadLines._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CShriMahakalRoadLines._mStrTracebackLogs
            return dictXMLResponse



async def main():
    api_response_dir=r"D:\Nisarg\IndianInvoice\data\apiResponses\Shri Mahakal RoadLines"
    output_dir=r"GitIgnore\Shri Mahakal RoadLines"
    strConfigFilePath = r"Data\Customer\17_ParagTraders\30_MANGAL HARDWARE & PAINTS\MANGAL HARDWARE & PAINTS_Config.json"
    strDebitLedger = r"Data\Customer\17_ParagTraders\30_MANGAL HARDWARE & PAINTS\MANGAL HARDWARE & PAINTSDebit_MapedDebitConfig.json"
    i = 0 
    # for filename in os.listdir(api_response_dir):
    try:
        # if filename.endswith("_gptResponse.json"):
        #     #
        #     json_file_path = os.path.join(api_response_dir, filename)
            
        #     os.makedirs(output_dir, exist_ok=True)

        #     with open(json_file_path, "r") as file:
        #         api_response = json.load(file) 
        
       
        content = {
    "Taxes": {
        "MainTaxes": [
            {
                "TaxName": "CGST",
                "TaxRate": 9,
                "TaxAmount": 384.1,
                "TaxableAmount": 4267.8
            },
            {
                "TaxName": "SGST",
                "TaxRate": 9,
                "TaxAmount": 384.1,
                "TaxableAmount": 4267.8
            }
        ],
        "OtherTaxes": {
            "TaxName": "",
            "TaxRate": 0,
            "TaxAmount": 0
        },
        "ApplicableGstTaxType": "CGST+SGST",
        "ApplicableGstTaxTypeReason": "Buyer and Seller states are same (Madhya Pradesh)"
    },
    "Charges": [],
    "SubTotal": 4267.8,
    "UsbDrive": 0,
    "ChallanNo": "",
    "Discounts": [],
    "InvoiceNo": "B2B/25-26/584",
    "meta_data": {
        "page_width": "",
        "page_height": ""
    },
    "Bilty/LRNO": "",
    "InvoiceDate": "20250516",
    "InvoiceTime": "",
    "ItemDetails": [
        {
            "Rate": 813.56,
            "Size": "",
            "Unit": "No",
            "Brand": "",
            "Grade": "",
            "Taxes": {
                "MainTaxes": [
                    {
                        "TaxName": "CGST",
                        "TaxRate": 9,
                        "TaxAmount": 384.1,
                        "TaxableAmount": 4267.8
                    },
                    {
                        "TaxName": "SGST",
                        "TaxRate": 9,
                        "TaxAmount": 384.1,
                        "TaxableAmount": 4267.8
                    }
                ],
                "OtherTaxes": {
                    "TaxName": "",
                    "TaxRate": 0,
                    "TaxAmount": 0
                },
                "ApplicableGstTaxType": "CGST+SGST"
            },
            "HSNCode": "",
            "Category": "",
            "ItemCode": "",
            "ItemName": "Esdee Syncoat ETCH PRI GREEN 1 LY +H 10ltr Total",
            "Material": "",
            "Quantity": 5.0,
            "ArticleNo": "",
            "NetAmount": 5036,
            "DesignName": "",
            "DiscountRate": 0,
            "OtherColumns": [],
            "FreightAmount": 200,
            "DiscountAmount": 0,
            "InsuranceAmount": 0,
            "PackingAndForwardingAmount": 0,
            "AmountBeforeTaxesAndDiscounts": 4067.8
        }
    ],
    "RoundingOff": 0,
    "TotalAmount": 5036,
    "DeliveryNote": "",
    "BuyersDetails": {
        "BuyerGST": "23AAKFV4306N1ZX",
        "BuyerPAN": "",
        "BuyerName": "Parag Traders",
        "BuyerEmail": "",
        "BuyerState": "Madhya Pradesh",
        "BuyerAddress": "12/4 Racecourse Road, Indore-452003",
        "BuyerContactNumber": "",
        "BuyerEntityBasedOnPanOrGst": "Firm (Partnership)"
    },
    "SellerDetails": {
        "SellerGST": "23**********1Z6",
        "SellerPAN": "**********",
        "SellerName": "Mangal Hardware and Paints",
        "SellerEmail": "<EMAIL>",
        "SellerState": "Madhya Pradesh",
        "SellerAddress": "7, Jagjeevan Ram Nagar, Near Patnipura Square",
        "SellerContactNumber": "",
        "SellerEntityBasedOnPanOrGst": "Individual"
    }
}
       
        # CGeneralClass.MCSetAttributes(strConfigFilePath, 4)
        XmlOutput = await CMangalHardwarePaints.MSCreateXML(strConfigFilePath,strDebitLedger, 4, content)
        
    
        invoice_no = content.get("InvoiceNo", i)
        invoice_no = invoice_no.replace("/", "_")
        
        i += 1
        xml_file_name = f"Invoiceno_{i}_xmlfile.xml"
        xml_file_path = os.path.join(output_dir, xml_file_name)
        

        with open(xml_file_path, "w") as xml_file:
            xml_file.write(XmlOutput.get("xmlContent"))
    except Exception as e:
        print("Error Occur - ", traceback.format_exc())        

if __name__ == "__main__":
    asyncio.run(main())