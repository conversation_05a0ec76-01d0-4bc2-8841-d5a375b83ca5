import { useState, useEffect } from "react";
import axios from 'axios';
import toast, { Toaster } from 'react-hot-toast';
import PropTypes from 'prop-types';
import countryCodes from '../assets/phoneCountries.json'
import loadingAnimation from '../assets/loading.svg'; // Adjust the import path as necessary


export default function AddUserModal({ isOpen, onClose, roles }) {
    const [loading, setLoading] = useState(false);
    const [userData, setUserData] = useState({
        email: '',
        password: '',
        name: '',
        roleName: 'General',
        phoneNumber: '',
        Country: '',
        bUsePaidOCR: false,
        bUsePaidDocExtractor: false,
        promoCodeStr: ''
    });
    const [selectedCountry, setSelectedCountry] = useState(countryCodes[0]);

    useEffect(() => {
        if (isOpen) {
            handleCountrySelect(countryCodes[0].dial_code); // Reset the country selection as well
        } else {
            // Reset the form state when modal is closed
            setUserData({
                email: '',
                password: '',
                name: '',
                roleName: 'General',
                phoneNumber: '',
                Country: '',
                bUsePaidOCR: false,
                bUsePaidDocExtractor: false,
                promoCodeStr: ''
            });
        }
    }, [isOpen]); // Depend on isOpen to trigger this effect

    useEffect(() => {
        if (countryCodes.length > 0) {
            handleCountrySelect(selectedCountry.dial_code)
        }
    }, [countryCodes]);

    const handleClose = () => {
        // Reset userData state to initial empty values
        setUserData({
            email: '',
            password: '',
            name: '',
            roleName: '',
            phoneNumber: '',
            Country: '',
            bUsePaidOCR: false,
            bUsePaidDocExtractor: false,
            promoCodeStr: ''
        });

        onClose(); // Close modal
    };

    const handleCountrySelect = (dialCode) => {
        // Find the country object using the dial code
        const country = countryCodes.find(country => country.dial_code === dialCode);
        setUserData({ ...userData, Country: country.name }); // Update UserData with selected country's name
        setSelectedCountry(country);
        // console.log(userData);
    };

    const handleInputChange = (e) => {
        const { name, value, type, checked } = e.target;
        const newValue = type === 'checkbox' ? checked : value;
        setUserData(prev => ({ ...prev, [name]: newValue }));
    };

    const initializeUserData = async (userId) => {
        try {
            const formData = new FormData();
            formData.append("iUserID", userId);

            const response = await axios.post(`${import.meta.env.VITE_SERVER}/initialize`, formData);
            if (response.status === 200) {
                toast.success("User initialized successfully!");
            } else {
                toast.error("Failed to initialize user.");
            }
        } catch (error) {
            console.error('User Initialization Failed, Please try again later');
            toast.error("User Initialization Failed, Please try again later");
        }
    };

    const handleSubmit = async () => {
        if (!userData.email || !userData.name) {
            toast.error("Please fill in all fields.");
            return;
        }

        const formData = new FormData();
        formData.append("email", userData.email);
        formData.append("name", userData.name);
        formData.append("password", userData.password);
        formData.append("roleName", userData.roleName);
        formData.append("Country", userData.Country);
        formData.append("phoneNumber", userData.phoneNumber);
        formData.append("bUsePaidOCR", userData.bUsePaidOCR);
        formData.append("bUsePaidDocExtractor", userData.bUsePaidDocExtractor);
        formData.append("promoCodeStr", userData.promoCodeStr);

        const apiUrl = `${import.meta.env.VITE_SERVER}/Registration`;
        try {
            setLoading(true);
            let curToken = localStorage.getItem('token');
            let curCreationTime = localStorage.getItem('created')

            const response = await axios.post(apiUrl, formData, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                }
            });

            if (response.status === 200) {
                localStorage.setItem("token", curToken);
                localStorage.setItem("created", curCreationTime);
                const userId = response.data.uid;
                console.log("response.data.uid", userId);

                // Call initializeUserData after successful registration
                await initializeUserData(userId);

                toast.success("User added successfully!");
            } else {
                toast.error("Failed to add user.");
            }
            onClose(); // Close modal after successful submission

            setUserData({ // Reset userData state to initial empty values
                email: '',
                password: '',
                name: '',
                roleName: '',
                phoneNumber: '',
                Country: '',
                bUsePaidOCR: false,
                bUsePaidDocExtractor: false,
                promoCodeStr: ''
            });
        } catch (error) {
            toast.error(error.response.data.detail);
            console.error('Error submitting user:', error.message);
        }
        finally {
            setLoading(false); // Reset loading state
        }
    };

    if (!isOpen) return null;

    return (
        <div className="fixed inset-0 z-50 flex h-screen w-screen items-center justify-center bg-black bg-opacity-60 backdrop-blur-xs transition-opacity">
            <Toaster position="top-center"></Toaster>
            <div className="relative mx-auto w-full max-w-md rounded-xl bg-[#ffff] p-6 shadow-md">
                <div className="flex flex-col gap-4">
                    <div className="flex items-center justify-between">
                        <h4 className="text-2xl font-semibold text-gray-900">Add User</h4>
                        <button
                            onClick={handleClose}
                            className="rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 transition-color focus:outline-none focus:ring focus:ring-blue-500"
                        >
                            Close
                        </button>
                    </div>
                    <label htmlFor="email" className="block text-xm font-medium text-gray-700">Email <span className="text-red-500">*</span></label>
                    <input
                        type="email"
                        name="email"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        placeholder="Email"
                        value={userData.email}
                        onChange={handleInputChange}
                    />
                    <label htmlFor="email" className="block text-xm font-medium text-gray-700">Password <span className="text-red-500">*</span></label>
                    <input
                        type="password"
                        name="password"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        placeholder="Password"
                        value={userData.password}
                        onChange={handleInputChange}
                    />
                    <label htmlFor="name" className="block text-xm font-medium text-gray-700">Name <span className="text-red-500">*</span></label>
                    <input
                        type="text"
                        name="name"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        placeholder="Name"
                        value={userData.name}
                        onChange={handleInputChange}
                    />
                    <label htmlFor="roleName" className="block text-xm font-medium text-gray-700">Role Name <span className="text-red-500">*</span></label>
                    <select
                        name="roleName"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        value={userData.roleName}
                        onChange={handleInputChange}
                    >
                        {/* <option value="General">General</option> */}
                        {roles.map((role, index) => (
                            // role.RoleName !== "General" ? (
                            <option key={index} value={role.RoleName}>{role.RoleName}</option>
                            // ) : null

                        ))}
                    </select>

                    <div>
                        <label htmlFor="phoneNumber" className="block text-xm font-medium text-gray-700">
                            Phone Number
                        </label>
                        <div className="flex items-center">
                            <select
                                value={selectedCountry.dial_code} // Use dial_code for value, which is a string
                                onChange={(e) => handleCountrySelect(e.target.value)}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                style={{ width: '20%' }}
                            >
                                {countryCodes.map((country) => (
                                    <option key={country.name} value={country.dial_code}>
                                        {`${country.dial_code} ${country.name}`}
                                    </option>
                                ))}
                            </select>
                            <input
                                name="phoneNumber"
                                type="tel"
                                value={userData.phoneNumber}
                                onChange={handleInputChange}
                                className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                                placeholder="Phone number"
                                style={{ width: '80%' }}
                            />
                        </div>
                    </div>

                    <label htmlFor="bUsePaidOCR" className="block text-xm font-medium text-gray-700">Use Paid OCR</label>
                    <input
                        type="checkbox"
                        name="bUsePaidOCR"
                        className="form-checkbox h-5 w-5 text-blue-600"
                        checked={userData.bUsePaidOCR}
                        onChange={handleInputChange}
                    />

                    <label htmlFor="bUsePaidDocExtractor" className="block text-xm font-medium text-gray-700">Use Paid Doc Extractor</label>
                    <input
                        type="checkbox"
                        name="bUsePaidDocExtractor"
                        className="form-checkbox h-5 w-5 text-blue-600"
                        checked={userData.bUsePaidDocExtractor}
                        onChange={handleInputChange}
                    />

                    <label htmlFor="promoCodeStr" className="block text-xm font-medium text-gray-700">Promo Code</label>
                    <input
                        type="text"
                        name="promoCodeStr"
                        className="w-full rounded-md border border-gray-300 px-3 py-2 text-gray-700 focus:border-blue-500 focus:outline-none"
                        placeholder="Promo Code"
                        value={userData.promoCodeStr}
                        onChange={handleInputChange}
                    />


                    <div className="flex justify-end mt-4">
                        <button
                            onClick={handleSubmit}
                            className={`rounded-lg bg-[#003654] hover:bg-[#002744] text-white py-2 px-4 w-full transition-color focus:outline-none focus:ring focus:ring-blue-500 ${!(userData.email && userData.name && userData.password) || loading ? 'opacity-50 cursor-not-allowed' : ''}`}
                            disabled={!(userData.email && userData.name && userData.password) || loading}
                        >
                            {loading ? (
                                <img src={loadingAnimation} alt="Loading" className="w-6 h-6 mx-auto" />
                            ) : (
                                "Submit"
                            )}
                        </button>
                    </div>
                </div>
            </div>
        </div>
    );
}

AddUserModal.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    roles: PropTypes.array.isRequired,
};
