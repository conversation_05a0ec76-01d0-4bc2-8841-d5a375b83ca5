import pandas as pd
import sys
sys.path.append("")
from fastapi import HTTPException
from src.utilities.helperFunc import CJSONFileReader
from config.constants import Constants
import calendar 
from datetime import datetime
import json
from pathlib import Path

class CBusinessIntelligence:
    
    @staticmethod
    def MSBIsGeneralizeVCHType(iUserId = None, strVoucherType = None, strConfigFile = Path(r"resource/TDLUserConfig.json")):
        # NOTE: GeneralizePWIV3
        with open(strConfigFile) as f:
            data = json.load(f)

            dictUserConfig = data[str(iUserId)]
            
            bIsGeneralizePWIV3 = dictUserConfig["bUseGeneralizeGPTSchema"]
            # PARAG Trader - Generalize Enable for PV_WITHOUT_INVENTORY, JOURNAL_VOUCHER
            if iUserId in [2]:
                if strVoucherType in ["JOURNAL_VOUCHER", "PV_WITHOUT_INVENTORY"]:
                    return bIsGeneralizePWIV3
                else:
                    # PV_WITH_INVENTORY
                    return False
            elif iUserId in [5]: # ICD Client , Vedansh Fee Receipt == True
                if strVoucherType in ["RECEIPT_NOTE"]:
                    return bIsGeneralizePWIV3
                else:
                    return False
            else:
                return bIsGeneralizePWIV3
    
        return False
    
    @staticmethod
    def MSRoundDateToNext5Days(invoice_date: str) -> str:
        """
        Rounds a date string in YYYYMMDD format to the next date that's a multiple of 5 days.
        
        Args:
            invoice_date (str): Date string in YYYYMMDD format (e.g., "20250322")
            
        Returns:
            str: Rounded date string in YYYYMMDD format (e.g., "20250325")
        """
        try:
            # Parse the input date
            dt_obj = datetime.strptime(invoice_date, "%Y%m%d")
            
            # Get the day of the month
            day = dt_obj.day
            
            # Calculate the next multiple of 5
            if day % 5 == 0:
                # If already a multiple of 5, keep the same date
                next_day = day
            else:
                # Round up to the next multiple of 5
                next_day = day + (5 - (day % 5))
            
            # Check if the next_day is valid for this month
            month_days = calendar.monthrange(dt_obj.year, dt_obj.month)[1]
            if next_day > month_days:
                # If exceeding month end, use the last day of the month
                next_day = month_days
            
            # Create the new date
            new_date = dt_obj.replace(day=next_day)
            
            # Return in YYYYMMDD format
            return new_date.strftime("%Y%m%d")
        except Exception as e:
            # Return original date if any error occurs
            return invoice_date
        
    @staticmethod
    def MSCalcEstimatedTime(vendor_name: str, no_of_stock_items: int = 0, strVoucherType: str = "PV_WITH_INVENTORY"):
        """
        Purpose : This method calculates the estimated processing time for a vendor or voucher type based on predefined time values.

        Inputs  :
            - vendor_name: str (The name of the vendor whose processing time needs to be estimated.)
            - no_of_stock_items: int (The number of stock items to be processed, default is 0.)
            - strVoucherType: str (The type of voucher to be used if vendor is unknown.)

        Output  : Returns the estimated time in a user-friendly format (e.g., "~ 3 min 30 sec").
        """
        try:
            # Define the relative path to the Excel file
            file_path = Path(r"resource/VendorWiseCustomerDetails.xlsx")

            # Read the Excel file
            df = pd.read_excel(file_path, dtype=str)

            # Normalize columns for case-insensitive comparison
            df["VendorName"] = df["VendorName"].str.strip().str.lower()
            df["VoucherType"] = df["VoucherType"].str.strip().str.upper()

            # Normalize input parameters
            vendor_name = vendor_name.strip().lower()

            # Filter the DataFrame for the matching VendorName
            row = df[df["VendorName"].str.lower() == vendor_name]

            if row.empty:
                if strVoucherType == "PV_WITH_INVENTORY":
                    # Calculate total estimated time
                    total_time_sec = 150 + (no_of_stock_items * 20) # per item 20 sec to enter in tally

                    # Convert seconds to a user-readable format
                    minutes, seconds = divmod(total_time_sec, 60)
                    readable_time = f"{minutes} min {seconds} sec" if seconds else f"{minutes} min"

                    return f"~ {readable_time}"
                elif strVoucherType == "PV_WITHOUT_INVENTORY":
                    # Calculate total estimated time
                    total_time_sec = 150

                    # Convert seconds to a user-readable format
                    minutes, seconds = divmod(total_time_sec, 60)
                    readable_time = f"{minutes} min {seconds} sec" if seconds else f"{minutes} min"

                    return f"~ {readable_time}"
                elif strVoucherType == "JOURNAL_VOUCHER":
                    # Only For Abhinav Imprest Journal
                    total_time_sec = no_of_stock_items * 45
                     # Convert seconds to a user-readable format
                    minutes, seconds = divmod(total_time_sec, 60)
                    readable_time = f"{minutes} min {seconds} sec" if seconds else f"{minutes} min"

                    return f"~ {readable_time}"
                else:
                    print("No matching entry found in the Excel file.")
                    return "~ 3 min 30 sec"

            # Extract values
            default_time_sec = int(row["DefaultTimeSec"].values[0])
            per_item_time_sec = int(row["PerItemTimeTakenSec"].values[0])

            # Calculate total estimated time
            total_time_sec = default_time_sec + (no_of_stock_items * per_item_time_sec)

            # Convert seconds to a user-readable format
            minutes, seconds = divmod(total_time_sec, 60)
            readable_time = f"{minutes} min {seconds} sec" if seconds else f"{minutes} min"

            return f"~ {readable_time}"

        except Exception as e:
            print("Error - ", e)
            return "~ 3 min 30 sec"

    def MSGetUUIDDetails(configData: dict, strClientUUID: str, bIsDeveloper: bool):
        """
        Purpose: This function checks if a given Client UUID is present in the UUID_DETAILS list.
        
        Inputs:
            - configData: dict (Configuration data containing UUID_DETAILS)
            - strClientUUID: str (The UUID to search for)
            - bIsDeveloper: bool (Flag to return developer details if True)
        
        Output:
            - Returns the matching dictionary from UUID_DETAILS if found.
            - Returns a static developer dictionary if bIsDeveloper is True.
            - Returns False if the UUID is not found.
        
        Example:
            result = MSGetUUIDDetails(configData, "305A3A4B0C6A", False)
            print(result)  # Output: {"UUID": "305A3A4B0C6A", "TallyUserName": "KANUNGO", "UserName":"Shubhum"}
        """

        # If developer mode is enabled, return the static developer dictionary
        if bIsDeveloper:
            return {"TallyUserName": "Developer", "UserName": "Developer Team", "UUID": "Any Developer System"}

        # Search for the given UUID in the list
        for uuid_entry in configData.get("UUID_DETAILS", []):
            if uuid_entry.get("UUID") == strClientUUID:
                return uuid_entry  # Return the found dictionary

        return False  # Return False if UUID is not found

    @staticmethod
    def MSValidateMacAddress(iUserID: int, strClientUUID: str, bIsDeveloper: bool):
        """
        Purpose: Validates the MAC address (UUID) for a given user by checking it against the stored configuration.
        
        Inputs:
            - iUserID: int (The user ID whose system needs validation.)
            - strClientUUID: str (The UUID of the user's system.)
            - bIsDeveloper: bool (Flag to determine if the user is a developer.)
        
        Output:
            - If UUID is valid, returns the corresponding dictionary from configData.
            - If UUID is invalid, raises an HTTP 404 exception with a detailed error message.
        
        Example:
            try:
                user_info = CBusinessIntelligence.MSValidateMacAddress(2, "305A3A4B0C6A", False)
                print(user_info)  
            except HTTPException as e:
                print(e.detail)
        """
        try:
            # Load config data from the HJSON file
            srtTallUserConfig = Constants.srtTallUserConfig
            config_data = CJSONFileReader.read_json_file(srtTallUserConfig)

            # Validate the UUID
            dictClientUUIDDetails = CBusinessIntelligence.MSGetUUIDDetails(
                config_data.get(str(iUserID), {}), strClientUUID, bIsDeveloper
            )

            if not dictClientUUIDDetails:
                raise HTTPException(
                    status_code=404,
                    detail="\nAccuVelocity AI Software Unable to Validate User System. \n"
                           "Please contact support at +91 98989 42935, <EMAIL>"
                )

            return dictClientUUIDDetails

        except Exception as e:
            raise HTTPException(
                status_code=500,
                detail=f"An unexpected error occurred: {str(e)}"
            )

    @staticmethod
    def MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails):
        # Load the Excel file (update with actual file path if different)
        df = pd.read_excel(file_path, dtype=str)

        # Convert CustomerName to uppercase
        df["CustomerName"] = df["CustomerName"].str.upper().str.strip()

        # Convert VendorName to lowercase and strip whitespaces
        df["VendorName"] = df["VendorName"].str.lower().str.strip()

        # Group by 'CustomerName' and agg regate 'VendorName' as a list
        grouped_data = df.groupby("CustomerName")["VendorName"].apply(list).reset_index()

        # Add "AVDev" as a new row containing all VendorNames
        all_vendors = df["VendorName"].tolist()
        av_dev_entry = pd.DataFrame([{"CustomerName": "AV DEV", "VendorName": all_vendors}])

        # Append the new row to the grouped DataFrame
        return pd.concat([grouped_data, av_dev_entry], ignore_index=True)

    @staticmethod
    def MSCheckVendorPresence(vendor_name, df_grouped):
        """
        Creates a dictionary that holds True if the given vendor_name exists in the customer's vendor list, otherwise False.
        
        Inputs:
            - vendor_name (str): The vendor name to check.
        
        Output:
            - dictDeveloperDecideVendor (dict): A dictionary with customer names as keys and boolean values.
        """
        try:
            # Convert vendor names in dataframe to lowercase for case-insensitive comparison
            df_grouped["VendorName"] = df_grouped["VendorName"].apply(lambda x: [v.lower() for v in x])

            # Create dictionary to check vendor presence
            dictDeveloperDecideVendor = {
                row["CustomerName"]: vendor_name.lower() in row["VendorName"] for _, row in df_grouped.iterrows()
            }
            return dictDeveloperDecideVendor
        except Exception as e:
            print(f"Error in Checking Presence of Vendor - {str(e)}")
            return {}
    
    @staticmethod
    def MSGetPanEntityType(pan_number: str) -> str:
        if len(pan_number) != 10:
            return "Invalid PAN Number"
        
        fourth_char = pan_number[3].upper()
        
        if fourth_char == 'C':
            return "Company"
        elif fourth_char == 'F':
            return "Firm"
        else:
            return "Other"
# Example usage
# estimated_time = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="shri ram enterprise", no_of_stock_items=23)
# print(estimated_time)  # Output: "~ 4 min 10 sec" (for example)
# try:
#     user_info = CBusinessIntelligence.MSValidateMacAddress(2, "305A3A4B0C6A", False)
#     print(user_info)  
# except HTTPException as e:
#     print(e.detail)

# print(CBusinessIntelligence.MSGetPanEntityType("**********"))
dictAvailableCompany = CBusinessIntelligence.MSGetVendorDetailsCustomerWise(file_path = Constants.strVendorWiseCustomerDetails)
print(dictAvailableCompany)