
import sys
sys.path.append(r".")

import os
import json
import asyncio
from src.utilities.SimilaritySearchHelper import CSimilaritySearchHelper

async def process_json_files(directory, processor):
    """
    Iterates through the given directory, reads all JSON files, extracts 'DescriptionOfGoods'
    from the 'Table' key, and calls the processor function with the extracted data.

    Args:
        directory (str): The path to the directory containing JSON files.
        processor (object): An object with a method `get_latest_mapped_ledgers` to process the data.
    """
    stocks = []

    for filename in os.listdir(directory):
        if filename.endswith('.json'):
            filepath = os.path.join(directory, filename)

            try:
                with open(filepath, 'r') as file:
                    data = json.load(file)
                    if 'Table' in data and isinstance(data['Table'], list):
                        for row in data['Table']:
                            if isinstance(row, dict) and 'DescriptionOfGoods' in row:
                                stocks.append(row['DescriptionOfGoods'])

            except (json.JSONDecodeError, FileNotFoundError, KeyError) as e:
                print(f"Error processing file {filename}: {e}")

    if stocks:
        # Call the processor function with the extracted stocks
        mapped_ledgers = await processor.get_latest_mapped_ledgers(stocks)
        print(f"Matching Ledgers Found : {mapped_ledgers}")
        return mapped_ledgers
    else:
        print("No stocks found.")
        return None



# Example directory path and processor usage
if __name__ == "__main__":
    directory_path = r"H:\DEVELOPER_PUBLIC\Developers\Dhruvin\IndianInvoiceProcessing\data\trueData\15_AQUANT"
    
    input_ledger_file = r"D:\Customer\Real\Velocity - GitHub\AccuVelocity\Data\Customer\17_ParagTraders\15_Aquant\AQUANT Stock Item - Copy.xlsx"
    description_column = "StockItemName"
    file_suffix = "WithEmbeddings_aquant"

    processor = CSimilaritySearchHelper(input_ledger_file, description_column, file_suffix)

    asyncio.run(process_json_files(directory_path, processor))
