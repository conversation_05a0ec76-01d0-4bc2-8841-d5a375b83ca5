import { Routes, Route, Navigate } from 'react-router-dom';
import { useEffect, useState } from 'react';
import "@fontsource/poppins"; // Defaults to weight 400
import "@fontsource/poppins/700.css"; // Specify weight
import "@fontsource/poppins/500-italic.css"; // Specify weight and style
import { useRecoilValue, useRecoilState } from "recoil";
import { ChakraProvider } from "@chakra-ui/react";
import routes from "./routes";
import userDataState from './context/userData';
import { Sidebar, MiniSidebar } from './components/Sidebar/index';
import { RxHamburgerMenu } from "react-icons/rx";
import { Alert } from './components/App';
import customTheme from "./theme";
import { setCookie, getCookie } from './utils/cookieUtils';
import { isActivePaidPlanAtom } from './context/TrailUsageUserData';
import { MaintenancePage } from './pages/Common';
import { ResetPassword } from './pages/Authentication';

function App() {
  const [isSidebarOpenSmall, setSidebarOpenSmall] = useState(false);
  const [isSidebarOpenLarge, setSidebarOpenLarge] = useState(true);
  const [screenWidth, setScreenWidth] = useState(window.innerWidth);
  const userData = useRecoilValue(userDataState);
  const [showAlert, setShowAlert] = useState(true);
  const [IsActivePaidPlan, setActivePaidPlan] = useRecoilState(isActivePaidPlanAtom);

  useEffect(() => {
    if (IsActivePaidPlan === "true" || IsActivePaidPlan === true) {
      setShowAlert(false);
    }
  }, [IsActivePaidPlan]);

  useEffect(() => {
    const alertVisibility = localStorage.getItem('showAlert');
    setShowAlert(alertVisibility !== 'false'); // Only show if not explicitly hidden

    const handleResize = () => {
      setScreenWidth(window.innerWidth);
    };
    window.addEventListener('resize', handleResize);


    // Set default sidebar state to open
    let smallSidebarDefaultState = true;
    let largeSidebarDefaultState = true;

    // Retrieve sidebar state from cookies if present
    const smallSidebarState = getCookie('isSidebarOpenSmall');
    const largeSidebarState = getCookie('isSidebarOpenLarge');

    // Update only if cookie is present and explicitly set to 'false'
    if (smallSidebarState === 'false') {
      smallSidebarDefaultState = false;
    }
    if (largeSidebarState === 'false') {
      largeSidebarDefaultState = false;
    }

    setSidebarOpenSmall(smallSidebarDefaultState);
    setSidebarOpenLarge(largeSidebarDefaultState);

    return () => {
      window.removeEventListener('resize', handleResize);
    };
  }, []);

  const handleDismissAlert = () => {
    setShowAlert(false);
    localStorage.setItem('showAlert', 'false'); // Persist state across sessions
  }

  const toggleSidebarSmall = () => {
    const newState = !isSidebarOpenSmall;
    setSidebarOpenSmall(newState);
    setCookie('isSidebarOpenSmall', newState, 1);
  };

  const toggleSidebarLarge = () => {
    const newState = !isSidebarOpenLarge;
    setSidebarOpenLarge(newState);
    setCookie('isSidebarOpenLarge', newState, 1);
  };

  if (import.meta.env.VITE_IS_UNDER_MAINTENANCE) {
    return <MaintenancePage />;
  }

  return (
    <div>
      {userData.isLoggedIn ? (
        <>
          {location.pathname.startsWith('/recovery-password/') ? (
            <Routes>
              <Route path="/recovery-password/:token" element={<ResetPassword />} />
            </Routes>
          ) : (
            <>
              {showAlert && <Alert onDismiss={handleDismissAlert} />}
              {screenWidth <= 768 ? (
                <div>
                  <button onClick={toggleSidebarSmall} className="text-2xl p-2">
                    <RxHamburgerMenu />
                  </button>
                  {isSidebarOpenSmall && <MiniSidebar isOpen={isSidebarOpenSmall} toggleSidebar={toggleSidebarSmall} />}
                </div>
              ) : (
                <Sidebar isOpen={isSidebarOpenLarge} toggleSidebar={toggleSidebarLarge} showAlert={showAlert} />
              )}
              <div style={{ backgroundColor: "#f4f7fe", height: "100vh" }} className={`transition-padding duration-300 ease-in-out ${screenWidth <= 768 ? (isSidebarOpenSmall && 'pl-0') : (isSidebarOpenLarge ? '2xl:pl-64 xl:pl-[12rem]' : 'pl-16')}`}>
                <Routes>
                  {routes(showAlert, isSidebarOpenLarge).map((route, index) => (
                    <Route key={index} {...route} />
                  ))}
                </Routes>
              </div>
            </>
          )}
        </>
      ) : (
        <>
          {userData.isOrgDetailsNotGiven && <Navigate to="/OrganizationDetails" replace />}
          <ChakraProvider
            theme={customTheme}
            toastOptions={{
              defaultOptions: {
                position: "top",
                containerStyle: {
                  width: "full",
                  px: "2",
                  maxWidth: "600px",
                },
              },
            }}
          >
            <Routes>
              {routes(showAlert, isSidebarOpenLarge).map((route, index) => (
                <Route key={index} {...route} />
              ))}
            </Routes>
          </ChakraProvider>
        </>
      )}
    </div>
  );
}

export default App;
