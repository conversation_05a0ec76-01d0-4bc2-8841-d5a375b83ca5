import asyncio
import aioboto3
import aiofiles
import json
import sys
from botocore.exceptions import ClientError
import uuid
import csv 
import os  
from openai import OpenAI


strSystemPromptStructured_1 = '''You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: 

Step 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. 

Step 2: Give output in the given structure:

Step 3: Analyze UserContent completely that is given in the following JSON structure 
{
    "Text" : {
        [ActualPageNumber]: [('[ActualText]', [PageNumber], [x1], [y1], [x2], [y2]),...],...
    }
    
Tables
Heading1, Heading2, ... HeadingN
Cell1, Cell2,..., CellN

Here x1, y1, x2, y2 represents standard bounding box coordinates of actual text. 

Step 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. 

Step 5: Find relevant information from the invoice and fill out the required output JSON file. For tables, consider "Tables" key-value. If something is not found in the invoice then keep the respective value of the output as ''. 

Step 6: Check again for missing any line item in LineItemTable and strictly include all line items from all pages in the output JSON. DO NOT ASSUME ANYTHING.

'''

response_format_1 = {
    "type": "json_schema",
    "json_schema": {
        "name": "simpolo",
        "strict": True,
        "schema": {
            "type": "object",
            "properties": {
                "SellerName": {
                    "type": "string",
                    "description": "Name of the seller to whom the invoice is addressed"
                },
                "SellerAddress": {
                    "type": "string",
                    "description": "Address of the seller to whom the invoice is addressed"
                },
                "SellerCIN": {
                    "type": "string",
                    "description": "Corporate Identification Number (CIN) of the seller providing the goods or services"
                },
                "SellerGSTIN": {
                    "type": "string",
                    "description": "Goods and Services Tax Identification Number (GSTIN) of the supplier or seller"
                },
                "SellerPANNo": {
                    "type": "string",
                    "description": "Permanent Account Number (PAN) of the seller"
                },
                 "SellerTel": {
                    "type": "integer",
                    "description": "Telephone Number of the seller providing the goods or services"
                },
                "SellerTollFree": {
                    "type": "integer",
                    "description": "Toll Free Number of the seller company"
                },
                "SellerWeb": {
                    "type": "string",
                    "description": "Website of the issuing company"
                },
                "SellerEmailId": {
                    "type": "string",
                    "description": "Email ID of the issuing company"
                },
                "SellerIRN": {
                    "type": "string",
                    "description": "Invoice Reference Number (IRN) of the issuing company"
                },
                "Category": {
                    "type": "string",
                    "description": "The category or classification of the invoice, such as product type, service type, or other relevant classifications"
                },
                "Invoice No": {
                    "type": "integer",
                    "description": "Unique identification number assigned to the invoice"
                },
                "Invoice Date/Time": {
                    "type": "string",
                    "description": "The date when the invoice issued in dd/mm/yy format"
                },
                "BuyerName": {
                    "type": "string",
                    "description": "Name of the buyer"
                },
                "BuyerAddress": {
                    "type": "string",
                    "description": "Address of the buyer"
                },
                "BuyerState": {
                    "type": "string",
                    "description": "State Name of the buyer"
                },
                "BuyerStateCode": {
                    "type": "integer",
                    "description": "State code of the buyer"
                },
                "BuyerGSTINNo": {
                    "type": "string",
                    "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"
                },
                "BuyerPANNo": {
                    "type": "string",
                    "description": "Permanent Account Number (PAN) of the buyer"
                },
                "Place of Supply": {
                    "type": "string",
                    "description": "State or location where the supply of goods or services is made"
                },
                "TransporterName": {
                    "type": "string",
                    "description": "Name of the transporter"
                },
                "VehicleNo": {
                    "type": "string",
                    "description": "The vehicle registration number used for transporting goods"
                },
                "L.R. No": {
                    "type": "string",
                    "description": "LR (Lorry Receipt) number issued by the transport company for the shipment"
                },
                "Name of Goods": {
                    "type": "string",
                    "description": "Name of the goods"
                },
                "PO Ref. No": {
                    "type": "string",
                    "description": "Purchase Order Reference Number used for transport"
                },
                "PO Date": {
                    "type": "string",
                    "description": "Purchase Order Date (dd/mm/yy) when the purchase order was issued."
                },
                "Total Qty(Box)": {
                    "type": "integer",
                    "description": "Total number of boxes included in the invoice"
                },
                "Total Qty(Sq. Mt)": {
                    "type": "number",
                    "description": "Total quantity in square meters included in the invoice"
                },
                "Total Amount": {
                    "type": "number",
                    "description": "Total Amount of the invoice before taxes and additional charges"
                },
                "Sub Total": {
                    "type": "number",
                    "description": "The subtotal amount of the invoice before taxes and additional charges"
                },
                "IGSTrateIn%": {
                    "type": "number",
                    "description": "IGST rate in percentage",
                    "enum": [
                        0, 0.25, 3, 5, 12, 18, 28
                    ]
                },
                "IGSTamount": {
                    "type": "number",
                    "description": "IGST amount",
                },
                "Rounding Off": {
                    "type": "number",
                    "description": "The rounding adjustment applied to the total amount of the invoice"
                },
                "Grand Total": {
                    "type": "number",
                    "description": "The final total amount payable on the invoice, including all taxes and rounding adjustments"
                },
                "GrandTotal in Words": {
                    "type": "string",
                    "description": "The final total amount payable on the invoice, written in words, including all taxes and rounding adjustments"
                },
                "Outstanding Amount Inclusive Of Bill": {
                    "type": "number",
                    "description": "The total outstanding amount in the account, including the current bill, subject to realization of payment"
                },
                "Current Year Business With Simpolo": {
                    "type": "number",
                    "description":  "The total business volume or value conducted with Simpolo  (Excluding Insu., Freight, GST and this Invoice Value) in the current financial year"
                },
                "Overdue On Assigned Credit Days": {
                    "type": "number",
                    "description": "The number of overdue days beyond the assigned credit period, based on the company policy"
                },
                "Sales Target For Growth Booster Scheme": {
                    "type": "number",
                    "description": "The sales target set for the Growth Booster Scheme, typically measured in units or revenue"
                },
                "Overdue On Assigned Credit Days": {
                    "type": "number",
                    "description": "The number of overdue days beyond the assigned credit period, based on the company policy"
                },
                "Pending Target For Growth Booster Scheme": {
                    "type": "number",
                    "description": "The remaining sales target to be achieved for the Growth Booster Scheme"
                },
                "BankName": {
                    "type": "string",
                    "description": "The name of the bank involved in the transaction"
                },
                "BankA/C No": {
                    "type": "integer",
                    "description": "The bank account number involved in the transaction"
                },
                "BankIFSC Code": {
                    "type": "string",
                    "description": "The IFSC code of the bank branch used for the transaction"
                },
                "BankBranch": {
                    "type": "string",
                    "description": "The name of the bank branch involved in the transaction"
                },
                "Amount of Tax subject to reverse charge": {
                    "type": "string",
                    "description": "The total amount of tax that is subject to reverse charge"
                },
                "ItemTable": {
                    "type": "array",
                    "items": {
                        "type": "object",
                        "properties": {
                             "Sr No": {
                                "type": "integer",
                                "description": "The serial number of the item in the invoice"
                             },
                            "Description/TradeMark": {
                                "type": "string",
                                "description": "Description and The trade mark or brand associated with the product or service"
                            },
                            "HSNCode/Grade": {
                                "type": "string",
                                "description": "six digits Code used to identify the service or product and The grade or quality level of the product"
                            },
                            "Batch/Shade": {
                                "type": "string",
                                "description": "The batch number or shade for the product, indicating production details"
                            },
                            "Qty(Box)": {
                                "type": "integer",
                                "description": "The quantity of the product measured in boxes"
                            },
                            "Qty(Sq Mt)": {
                                "type": "number",
                                "description": "The quantity of the product measured in square meters"
                            },
                            "Rate/Box": {
                                "type": "number",
                                "description": "The rate per box for the product"
                            },
                            "BasicRate/Sq Ft": {
                                "type": "number",
                                "description": "The basic rate per square foot for the product"
                            },
                            "Trade Disc. Unit": {
                                "type": "number",
                                "description": "The trade discount applied to the product"
                            },
                            "Project/Spel Disc.": {
                                "type": "number",
                                "description":  "The discount offered for specific projects or special circumstances"
                            },
                            "Scheme Disc.": {
                                "type": "number",
                                "description": "The discount offered as part of a scheme or promotion"
                            },
                             "Progressive Disc.": {
                                "type": "number",
                                "description": "The progressive discount applied to the product"
                            },
                            "Loyaly Disc.": {
                                "type": "number",
                                "description": "The loyalty discount applied based on customer loyalty programs"
                            },
                            "Gr.Boost. Disc.": {
                                "type": "number",
                                "description": "The discount offered as part of the Growth Booster scheme"
                            },
                             "Cash Disc.": {
                                "type": "number",
                                "description": "The cash discount provided for prompt payment"
                            },
                             "NetRate/SqFt": {
                                "type": "number",
                                "description": "The net rate per square foot for the product after discounts"
                            },
                             "Amount": {
                                "type": "number",
                                "description":  "The total amount for the item, after applying discounts and taxes"
                            }
                        },
                        "required": ["Sr No","Description/TradeMark","HSNCode/Grade","Batch/Shade","Qty(Box)","Qty(Sq Mt)","Rate/Box","BasicRate/Sq Ft","Trade Disc. Unit","Project/Spel Disc.",
                                     "Scheme Disc.","Progressive Disc.","Loyaly Disc.","Gr.Boost. Disc.","Cash Disc.","NetRate/SqFt","Amount"],
                        "additionalProperties": False
                    },
                    "description": "Include all rows related to the services or products invoiced"
                },
                
            },
            "required": [
                "SellerName","SellerAddress","SellerCIN","SellerGSTIN","SellerPANNo","SellerTel","SellerTollFree","SellerWeb","SellerEmailId","SellerIRN","Category",
                "Invoice No","Invoice Date/Time","BuyerName","BuyerAddress","BuyerState","BuyerStateCode","BuyerGSTINNo","BuyerPANNo","Place of Supply","TransporterName",
                "VehicleNo","L.R. No","Name of Goods","PO Ref. No","PO Date","Total Qty(Box)","Total Qty(Sq. Mt)","Total Amount","Sub Total","IGSTrateIn%","Rounding Off","Grand Total",
                "GrandTotal in Words","Outstanding Amount Inclusive Of Bill","Current Year Business With Simpolo","Overdue On Assigned Credit Days","Sales Target For Growth Booster Scheme","Pending Target For Growth Booster Scheme","BankName","BankA/C No","BankIFSC Code","BankBranch","Amount of Tax subject to reverse charge", "IGSTamount", "ItemTable"
                ],
            "additionalProperties": False
        }
    }
}


async def upload_file_to_s3(s3_client, file_path, bucket_name, object_name=None):
    """
    Uploads a file to an S3 bucket.

    :param s3_client: aioboto3 S3 client.
    :param file_path: Path to the local file.
    :param bucket_name: S3 bucket name.
    :param object_name: S3 object name. If not specified, file_path is used.
    :return: S3 object key.
    """
    if object_name is None:
        object_name = os.path.basename(file_path)
    
    try:
        async with aiofiles.open(file_path, 'rb') as f:
            data = await f.read()
        await s3_client.put_object(Bucket=bucket_name, Key=object_name, Body=data)
        print(f"Uploaded {file_path} to s3://{bucket_name}/{object_name}")
        return object_name
    except ClientError as e:
        print(f"Failed to upload file to S3: {e}")
        sys.exit(1)
    except Exception as e:
        print(f"Unexpected error uploading file to S3: {e}")
        sys.exit(1)


async def start_document_analysis(textract_client, bucket_name, object_name, job_tag="job", notification_channel=None):
    """
    Starts asynchronous document analysis with Textract.

    :param textract_client: aioboto3 Textract client.
    :param bucket_name: S3 bucket name where the document is stored.
    :param object_name: S3 object name of the document.
    :param job_tag: Identifier for the job.
    :param notification_channel: (Optional) SNS topic ARN for notifications.
    :return: Job ID
    """
    try:
        params = {
            'DocumentLocation': {
                'S3Object': {
                    'Bucket': bucket_name,
                    'Name': object_name
                }
            },
            'FeatureTypes': ['TABLES'],
            'JobTag': job_tag
        }
        if notification_channel:
            params['NotificationChannel'] = notification_channel

        response = await textract_client.start_document_analysis(**params)
        job_id = response['JobId']
        print(f"Started document analysis job with Job ID: {job_id}")
        return job_id
    except ClientError as e:
        print(f"Error starting document analysis: {e}")
        sys.exit(1)


async def get_document_analysis(textract_client, job_id):
    """
    Retrieves the results of an asynchronous document analysis job.

    :param textract_client: aioboto3 Textract client.
    :param job_id: ID of the Textract job.
    :return: List of blocks from Textract response.
    """
    try:
        while True:
            response = await textract_client.get_document_analysis(JobId=job_id)
            status = response['JobStatus']
            print(f"Job status: {status}")

            if status in ['SUCCEEDED', 'FAILED']:
                if status == 'SUCCEEDED':
                    print("Document analysis succeeded.")
                    blocks = response.get('Blocks', [])
                    # Handle pagination if NextToken is present
                    next_token = response.get('NextToken', None)
                    while next_token:
                        print("Retrieving next page of results...")
                        response = await textract_client.get_document_analysis(JobId=job_id, NextToken=next_token)
                        blocks.extend(response.get('Blocks', []))
                        next_token = response.get('NextToken', None)
                    return blocks
                else:
                    print("Document analysis failed.")
                    sys.exit(1)
            await asyncio.sleep(5)  # Wait before polling again
    except ClientError as e:
        print(f"Error getting document analysis: {e}")
        sys.exit(1)


def extract_tables_from_blocks(blocks):
    """
    Extracts tables from Textract response blocks.

    :param blocks: List of blocks from Textract response.
    :return: List of tables, each table is a list of rows, each row is a list of cell texts.
    """
    tables = []
    table_blocks = [block for block in blocks if block['BlockType'] == 'TABLE']

    for table in table_blocks:
        cell_blocks = []

        # Extract CELL blocks related to the current table
        relationships = table.get('Relationships', [])
        for rel in relationships:
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    cell = next((b for b in blocks if b['Id'] == child_id and b['BlockType'] == 'CELL'), None)
                    if cell:
                        cell_blocks.append(cell)

        if not cell_blocks:
            continue

        # Sort cells by row and column
        sorted_cells = sorted(cell_blocks, key=lambda x: (x.get('RowIndex', 0), x.get('ColumnIndex', 0)))

        # Determine the number of rows and columns
        max_row = max(cell.get('RowIndex', 0) for cell in sorted_cells)
        max_col = max(cell.get('ColumnIndex', 0) for cell in sorted_cells)

        # Initialize table structure
        table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

        for cell in sorted_cells:
            row = cell.get('RowIndex', 0) - 1  # Zero-based index
            col = cell.get('ColumnIndex', 0) - 1  # Zero-based index
            text = get_text_for_block(blocks, cell)
            if 0 <= row < max_row and 0 <= col < max_col:
                table_data[row][col] = text
            else:
                logging.warning(f"Cell position out of bounds. Row: {row+1}, Column: {col+1}")

        tables.append(table_data)

    return tables


def get_text_for_block(blocks, cell_block):
    """
    Retrieves the text associated with a cell block.

    :param blocks: List of all blocks.
    :param cell_block: The cell block for which text is to be retrieved.
    :return: Concatenated text within the cell.
    """
    text = []
    relationships = cell_block.get('Relationships', [])
    for rel in relationships:
        if rel['Type'] == 'CHILD':
            for child_id in rel['Ids']:
                child = next((b for b in blocks if b['Id'] == child_id), None)
                if child:
                    if child['BlockType'] == 'WORD':
                        text.append(child.get('Text', ''))
                    elif child['BlockType'] == 'SELECTION_ELEMENT' and child.get('SelectionStatus') == 'SELECTED':
                        text.append("X")
    return ' '.join(text)


def get_text_without_tables(blocks):
    """
    Extracts text from Textract response blocks, excluding tables.

    :param blocks: List of blocks from Textract response.
    :return: Dictionary containing extracted text information.
    """
    # Create a mapping from block Id to block for quick lookup
    id_to_block = {block['Id']: block for block in blocks}

    # Collect all WORD Ids that are part of cells
    word_ids_in_cells = set()
    for block in blocks:
        if block['BlockType'] == 'CELL':
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        word_ids_in_cells.update(relationship['Ids'])

    # Map LINE block Ids to their respective page numbers
    line_id_to_page = {}
    page_number = 1  # Assuming page numbers start from 1
    for block in blocks:
        if block['BlockType'] == 'PAGE':
            page_id = block['Id']
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        for child_id in relationship['Ids']:
                            child_block = id_to_block.get(child_id)
                            if child_block and child_block['BlockType'] == 'LINE':
                                line_id_to_page[child_id] = page_number
            page_number += 1  # Increment page number for the next PAGE block

    # Collect LINE blocks that are not part of any table (CELL)
    non_table_lines = []
    for block in blocks:
        if block['BlockType'] == 'LINE':
            is_in_table = False
            if 'Relationships' in block:
                for relationship in block['Relationships']:
                    if relationship['Type'] == 'CHILD':
                        # Check if any of the LINE's child WORD Ids are in word_ids_in_cells
                        if any(child_id in word_ids_in_cells for child_id in relationship['Ids']):
                            is_in_table = True
                            break
            if not is_in_table:
                non_table_lines.append(block)

    # Prepare the output in the desired format
    output = {}
    for line in non_table_lines:
        text = line['Text']
        bbox = line['Geometry']['BoundingBox']
        x1 = round(bbox['Left'], 5)
        y1 = round(bbox['Top'], 5)
        x2 = round(x1 + bbox['Width'], 5)
        y2 = round(y1 + bbox['Height'], 5)
        line_id = line['Id']
        page_num = line_id_to_page.get(line_id, 1)  # Default to page 1 if not found

        if page_num not in output:
            output[page_num] = []
        output[page_num].append([
            text,
            page_num,
            x1,
            y1,
            x2,
            y2
        ])

    return output

async def process_with_openai(combined_txt_filename, formatted_json_filename):
    """
    Processes the combined text file with OpenAI's API and saves the formatted data as JSON.

    :param combined_txt_filename: Path to the combined text file.
    :param config_json_path: Path to the configuration JSON file containing prompts and response formats.
    :param formatted_json_filename: Path to save the formatted JSON data.
    :return: Parsed JSON response from OpenAI.
    """
    # Read combined text file
    try:
        async with aiofiles.open(combined_txt_filename, 'r', encoding='utf-8') as f:
            strUserContentStructured_1 = await f.read()
        print(f"Combined text file '{combined_txt_filename}' read successfully.")
    except Exception as e:
        print(f"Error reading combined text file: {e}")
        sys.exit(1)

    # Define a synchronous function to call OpenAI
    def call_openai():
        try:
            client = OpenAI()

            completion = client.beta.chat.completions.parse(
                                                            model="gpt-4o-2024-08-06",
                                                            messages=[
                                                                {"role": "system", "content": strSystemPromptStructured_1},
                                                                {"role": "user", "content": str(strUserContentStructured_1)}
                                                            ],
                                                            response_format=response_format_1,
                                                            max_tokens=16384,
                                                            seed=33,
                                                            temperature=0
                                                        )

            return completion
        except Exception as e:
            print(f"Error calling OpenAI API: {e}")
            sys.exit(1)

    # Call OpenAI API in executor to avoid blocking
    loop = asyncio.get_running_loop()
    completion = await loop.run_in_executor(None, call_openai)

    # Parse the response
    try:
        response_content = completion.choices[0].message.content
        parsed_response = json.loads(response_content)
        print("OpenAI API response parsed successfully.")
    except Exception as e:
        print(f"Error parsing OpenAI response: {e}")
        sys.exit(1)

    # Save the parsed response to JSON file
    try:
        async with aiofiles.open(formatted_json_filename, 'w', encoding='utf-8') as f:
            await f.write(json.dumps(parsed_response, ensure_ascii=False, indent=4))
        print(f"Formatted data saved to '{formatted_json_filename}'.")
    except Exception as e:
        print(f"Error writing formatted JSON file: {e}")
        sys.exit(1)

    return parsed_response


async def process_document(local_pdf_path, bucket_name, object_name=None, job_tag="ExtractTablesJob", notification_channel=None, base_filename=None):
    """
    Main processing function to handle the document analysis.

    :param local_pdf_path: Path to the local PDF file.
    :param bucket_name: S3 bucket name.
    :param object_name: S3 object name. If not specified, a unique name will be generated.
    :param job_tag: Identifier for the job.
    :param notification_channel: (Optional) SNS topic ARN for notifications.
    :param base_filename: Base name for output files.
    """
    # Initialize aioboto3 Session
    session = aioboto3.Session()

    async with session.client('s3') as s3_client, session.client('textract') as textract_client:
        # Step 1: Upload PDF to S3
        if not object_name:
            object_name = f"textract-input/{uuid.uuid4()}-{os.path.basename(local_pdf_path)}"
        s3_key = await upload_file_to_s3(s3_client, local_pdf_path, bucket_name, object_name)

        # Step 2: Start Document Analysis with Textract
        print("Starting document analysis with Textract...")
        job_id = await start_document_analysis(textract_client, bucket_name, s3_key, job_tag, notification_channel)

        # Step 3: Get Document Analysis Results
        print("Polling for document analysis results...")
        blocks = await get_document_analysis(textract_client, job_id)

        # Step 4: Extract Tables
        print("Extracting tables from results...")
        tables = extract_tables_from_blocks(blocks)
        print(f"Found {len(tables)} table(s).")

        # Step 5: Extract Text Without Tables
        print("Extracting text without tables from results...")
        text_content = get_text_without_tables(blocks)
        text_json_filename = f"{base_filename}_Text.json"
        try:
            async with aiofiles.open(text_json_filename, 'w', encoding='utf-8') as f:
                await f.write(json.dumps(text_content, ensure_ascii=False, indent=4))
            print(f"Extracted text saved to {text_json_filename}.")
        except Exception as e:
            print(f"Error writing to JSON file: {e}")
            sys.exit(1)

        # Step 6: Save Tables to CSV
        print("Saving extracted tables to CSV format...")
        table_csv_filenames = []
        try:
            for idx, table in enumerate(tables, start=1):
                csv_filename = f"{base_filename}_Table{idx}.csv"
                with open(csv_filename, 'w', newline='', encoding='utf-8') as csvfile:
                    writer = csv.writer(csvfile)
                    writer.writerows(table)
                print(f"Extracted table {idx} saved to {csv_filename}.")
                table_csv_filenames.append(csv_filename)
        except Exception as e:
            print(f"Error writing to CSV file: {e}")
            sys.exit(1)

        # Step 7: Create Combined Text File
        print("Creating combined text file...")
        combined_txt_filename = f"{base_filename}_Combined.txt"
        try:
            with open(combined_txt_filename, 'w', encoding='utf-8') as combined_file:
                # Write Text section
                combined_file.write("Text\n")
                combined_file.write(json.dumps(text_content, ensure_ascii=False, indent=4))
                combined_file.write("\n\n")

                # Write Tables
                for idx, csv_filename in enumerate(table_csv_filenames, start=1):
                    combined_file.write(f"Table-{idx}\n")
                    with open(csv_filename, 'r', encoding='utf-8') as csvfile:
                        table_data = csvfile.read()
                    combined_file.write(table_data)
                    combined_file.write("\n\n")
            print(f"Combined text file created: {combined_txt_filename}.")
        except Exception as e:
            print(f"Error writing to combined text file: {e}")
            sys.exit(1)

        # Step 8: Process Extracted Data with OpenAI
        print("Processing extracted data with OpenAI...")
        formatted_json_filename = f"{base_filename}_FormattedData.json"
        formatted_data = await process_with_openai(combined_txt_filename, formatted_json_filename)


        print("Process completed successfully.")
        return formatted_data

async def main():
    # Configuration
    local_pdf_path = r'DhruvinTest/Simpolo/2431005551.PDF'  # Replace with your PDF path
    bucket_name = 'testingparagtraders'  # Replace with your S3 bucket name
    object_name = None  # Optionally specify the S3 object name, else a unique name is generated
    job_tag = "ExtractTablesJob"
    notification_channel = None  # Replace with your SNS topic ARN if needed

    # Derive base filename from the input PDF file name
    base_filename = os.path.splitext(os.path.basename(local_pdf_path))[0]

    dictExtractedData = await process_document(local_pdf_path, bucket_name, object_name, job_tag, notification_channel, base_filename)
    

if __name__ == "__main__":
    asyncio.run(main())
