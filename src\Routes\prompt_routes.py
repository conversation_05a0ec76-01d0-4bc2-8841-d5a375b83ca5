from fastapi import <PERSON><PERSON>outer, Request, Depends, Form, HTTPException, Query
from fastapi.security import OAuth<PERSON><PERSON><PERSON><PERSON><PERSON>earer
from src.middleware.checkAuth import  user_required
from src.Controllers.prompt_controller import <PERSON>rompt<PERSON>ontroller

# Define the token URL if you have one; it's not necessary for just token extraction
oauth2_scheme = OAuth2PasswordBearer(tokenUrl='token')
prompt_router = APIRouter(tags=['Prompt'], prefix="/api/prompt")


@prompt_router.get("/get_prompt/{ModelId}")
async def get_prompt(ModelSeries:str,  ModelId: int, iUserID: int = Depends(user_required)):
    """
    Retrieves the prompt for a given ModelId.
    - **ModelId**: integer representing the unique ID of the ModelTable
    - **iUserID**: integer representing the authenticated user's ID
    """
    try:
        return await CPromptController.get_prompt_by_model_series(iUserID=iUserID, ModelId=ModelId, ModelSeries=ModelSeries)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@prompt_router.get("/get_all_prompts/{ModelId}")
async def get_all_prompts( ModelId: int, iUserID: int = Depends(user_required)):
    """
    Retrieves the all prompts for a given ModelId.
    - **ModelId**: integer representing the unique ID of the ModelTable
    - **iUserID**: integer representing the authenticated user's ID
    """
    try:
        return await CPromptController.get_all_prompts_by_model(iUserID=iUserID, ModelId=ModelId)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))
    

@prompt_router.get("/get_prompt_by_id/{promptId}")
async def get_prompt_by_id(promptId: int, iUserID: int = Depends(user_required)):
    """
    Retrieves the prompt details for a given prompt ID.
    - **promptId**: integer representing the unique ID of the Prompt
    - **iUserID**: integer representing the authenticated user's ID
    """
    try:
        return await CPromptController.MSGetPromptByID(iUserID=iUserID, promptId=promptId)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))