import traceback
from fastapi import HTTP<PERSON>x<PERSON>
from config.constants import Constants
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func
from config.db_config import AsyncSessionLocal
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.Models.models import PromoCode, PromoCodeUsage
from datetime import datetime, timedelta

class CReferralCode:
    
    @staticmethod
    async def MSUsePromoCode(userid:int, strPromocode: str):
        """
            Used for using the promocode, Called when user uses a promocode

            Args:
                userid (int): _description_
                iPromocodeID (int): _description_
        """
        from src.Controllers.auth_controller import CAuthController
        async with AsyncSessionLocal() as session:
            try:
                await CLogController.MSWriteLog(userid, "Info", f"Updating promocode usage with promocode {strPromocode}")
                
                dictPromoDetails = await CReferralCode.MSGetReferralCodeByName(promo_name = strPromocode)
                
                if not dictPromoDetails:
                    await CLogController.MSWriteLog(userid,"Error", f"Promo code '{strPromocode}' is expired, Please try using different promocode.")
                    raise HTTPException(status_code=404, detail= f"Promo code '{strPromocode}' is expired, Please try using different promocode.")
                
                # Check if the promo code usage count exceeds the maximum allowed usage count
                usage_count = await session.execute(select(func.count(PromoCodeUsage.promocode_usage_id)).filter(PromoCodeUsage.promo_code_id == dictPromoDetails.get("PromoCodeId")))
                usage_count = usage_count.scalar()

                if usage_count >= dictPromoDetails.get("MaxPromptCodeUseCount"):
                    await CLogController.MSWriteLog(userid,"Error", f"Promo code '{strPromocode}' usage limit of '{dictPromoDetails.get('MaxPromptCodeUseCount')}' is reached.")
                    raise HTTPException(status_code=400, detail="Promo code usage limit exceeded")

                # Log the promo code usage
                usage = PromoCodeUsage(user_id=userid, promo_code_id=dictPromoDetails.get("PromoCodeId"))
                session.add(usage)

                # Commit the changes
                await session.commit()

                if dictPromoDetails.get("PromoCodeType") == Constants.lsPromoCodeType[0]:
                    await CAuthController.MSUpdateUserAPIUsage(user_id=userid, pro_page_limit=int(dictPromoDetails.get("Value")), page_limit_left=int(dictPromoDetails.get("Value")), bOverwrite=False)
                    
                await CLogController.MSWriteLog(userid, "Info", f"Promo code '{strPromocode}' applied successfully")
                return {"msg": "Promo code used successfully"}

            except HTTPException as http_exc:
                await session.rollback()
                await CLogController.MSWriteLog(userid,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise http_exc

            except Exception as e:
                await session.rollback()
                await CLogController.MSWriteLog(userid,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Unable to process your request right now, please try again later.")

    @staticmethod
    async def MSGetUserPromoCodeUsage(userid: int):
        """
        Fetch all promo code usage based on user ID, sorted in descending order of usage time.

        Args:
            userid (int): The ID of the user

        Returns:
            dict: A dictionary containing the promo code usage details
        """
        promo_code_usage_list = []
        async with AsyncSessionLocal() as session:
            try:
                # Fetch promo code usage records for the user, sorted by usage time in descending order
                result = await session.execute(
                    select(PromoCodeUsage, PromoCode)
                    .join(PromoCode, PromoCodeUsage.promo_code_id == PromoCode.PromoCodeId)
                    .filter(PromoCodeUsage.user_id == userid)
                    .order_by(PromoCodeUsage.used_at.desc())
                )
                
                promo_code_usages = result.fetchall()
                
                if not promo_code_usages:
                    return promo_code_usage_list
                
                for usage, promo_code in promo_code_usages:
                    promo_code_usage_list.append({
                        "promo_code_id": usage.promo_code_id,
                        "promo_code_name": promo_code.PromoCodeName,
                        "used_at": usage.used_at,
                        "promo_code_type": promo_code.PromoCodeType,
                        "action": promo_code.Action,
                        "value": promo_code.Value
                    })

                return promo_code_usage_list

            except Exception as e:
                await session.rollback()
                await CLogController.MSWriteLog(userid, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Unable to fetch promo code usage right now, please try again later.")

    @staticmethod
    async def MSSetReferralCode(userid:int, promo_name: str, promo_code_type: str, action: str, value: str, expire_time :[None, datetime]= None) -> bool:
        """
        Insert a new promo code into the table.

        Args:
            promo_name (str): The name of the promo code.
            created_time (datetime): The created time of the promo code.
            promo_code_type (str): The type of the promo code.
            action (str): The action associated with the promo code.
            value (str): The value associated with the promo code.

        Returns:
            bool: True if the promo code is successfully inserted, False otherwise.
        """
        # Check if promo name exceeds maximum length
        if len(promo_name) > 20:
            await CLogController.MSWriteLog(userid,"Error", f"Promo name exceeds maximum length of 20 characters, For:{promo_name}.")
            raise HTTPException(status_code=400,detail="Promo name exceeds maximum length of 20 characters.")

        # Calculate default expiration time (30 days ahead from current time)
        if expire_time is None:
            expire_time = datetime.now() + timedelta(days=30)

        async with AsyncSessionLocal() as session:
            try:
                # check prompt exist
                isExists = await CReferralCode.MSIsValidReferralCode(promo_name)
                if isExists.get("status"):
                    await CLogController.MSWriteLog(userid,"Error", f"PromoCode already Exists: {promo_name}.")
                    raise HTTPException(status_code=200, detail="PromoCode already Exists")
                # Create a new PromoCode instance
                new_promo_code = PromoCode(
                    PromoCodeName=promo_name,
                    ExpireTime=expire_time,
                    PromoCodeType=promo_code_type,
                    Action=action,
                    Value=value
                )

                # Add the new promo code to the session and commit
                session.add(new_promo_code)
                await session.commit()

                return True
            except HTTPException as e:
                await CLogController.MSWriteLog(userid,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except Exception as e:
                await CLogController.MSWriteLog(userid,"Error", f"Failed to insert promo code, For: {promo_name}. {str(e)}")
                await CLogController.MSWriteLog(userid,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="There was an error processing your request. Please try again later.")
            
    @staticmethod
    async def MSGetReferralCodeByName(promo_name: str):
        async with AsyncSessionLocal() as db:
            try:
                # Execute the query to find the PromoCode with the given name
                query_result = await db.execute(
                    select(PromoCode)
                    .where(func.lower(PromoCode.PromoCodeName) == func.lower(promo_name))
                )
                promo = query_result.scalar()  # Fetch the first result as a scalar

                # If no promo code instance is found, log the event and return False
                if promo is None:
                    await CLogController.MSWriteLog(None,"Error", f"Promo code is invalid or expired. For Promo Name: {promo_name}.")
                    raise HTTPException(status_code=404, detail="Promo code is invalid or expired.")

                # Check if the promo code is expired
                current_time = datetime.now()
                if promo.ExpireTime and promo.ExpireTime < current_time:
                    promo.IsPromoExpired = True
                    await db.commit()
                    return False

                if promo.IsPromoExpired:
                    return False

                # If a promo code instance is found and is valid, return its details
                return {
                    "PromoCodeId": promo.PromoCodeId,
                    "PromoCodeName": promo.PromoCodeName,
                    "CreatedTime": str(promo.CreatedTime),
                    "ExpireTime": str(promo.ExpireTime),
                    "PromoCodeType": promo.PromoCodeType,
                    "Action": promo.Action,
                    "Value": int(promo.Value),
                    "MaxPromptCodeUseCount": promo.MaxPromptCodeUseCount,
                    "IsPromoExpired": promo.IsPromoExpired
                }

            except HTTPException as e:
                await CLogController.MSWriteLog(None,"Debug", f"Promo Name: {promo_name}, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            except SQLAlchemyError as e:
                await db.rollback()
                # Log the SQLAlchemy error and raise an HTTPException
                await CLogController.MSWriteLog(None,"Error", f"Error while Updating Database: Get Referral Code By Name, For Promo Name: {promo_name}.")
                await CLogController.MSWriteLog(None,"Debug", f"Promo Name: {promo_name}, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while retrieving the PromoCode.")
            except Exception as e:
                await CLogController.MSWriteLog(None,"Error", f"Error while Processing: Get Referral Code By Name, For Promo Name: {promo_name}.")
                await CLogController.MSWriteLog(None,"Debug", f"Promo Name: {promo_name}, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while Processing.")

    @staticmethod
    async def MSGetReferralCodeById(promoId: int):
        async with AsyncSessionLocal() as db:
            try:
                # Execute the query to find the PromoCode with the given name
                query_result = await db.execute(
                    select(PromoCode)
                    .where(PromoCode.PromoCodeId == promoId)
                )
                promo = query_result.scalar()  # Fetch the first result as a scalar

                # If no promo code instance is found, log the event and return False
                if promo is None:
                    await CLogController.MSWriteLog(None,"Error", f"Promo code is invalid or expired. [For PromoID: {promoId}].")
                    raise HTTPException(status_code=404, detail="The entered promo code has expired.")

                # Check if the promo code is expired
                current_time = datetime.now()
                if promo.ExpireTime and promo.ExpireTime < current_time:
                    promo.IsPromoExpired = True
                    await db.commit()
                    return False

                if promo.IsPromoExpired:
                    return False

                # If a promo code instance is found and is valid, return its details
                return {
                    "PromoCodeId": promo.PromoCodeId,
                    "PromoCodeName": promo.PromoCodeName,
                    "IsPromoExpired": promo.IsPromoExpired
                }

            except HTTPException as e:
                await CLogController.MSWriteLog(None,"Debug", f"For PromoID: {promoId}, \n\nTraceback: {str(traceback.format_exc())}")
                raise e
            except SQLAlchemyError as e:
                await db.rollback()
                # Log the SQLAlchemy error and raise an HTTPException
                await CLogController.MSWriteLog(None,"Error", f"Error while Updating table in Get Referral Code By ID, For For PromoID: {promoId}.")
                await CLogController.MSWriteLog(None,"Debug", f"For PromoID: {promoId}, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while Processing your Request.")
            except Exception as e:
                await CLogController.MSWriteLog(None,"Error", f"Error while Processing: Get Referral Code By ID, For For PromoID: {promoId}.")
                await CLogController.MSWriteLog(None,"Debug", f"For PromoID: {promoId}, \n\nTraceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An Error occured while Processing your Request.")
            
    @staticmethod
    async def MSGetAllReferralCodes(user_id:int, includeExpiredPromoCode: bool = False):
        async with AsyncSessionLocal() as db:
            try:
                # Build the initial query to select all PromoCodes
                query = select(PromoCode)
                
                # If not including expired promo codes, filter them out
                if not includeExpiredPromoCode:
                    query = query.where(PromoCode.IsPromoExpired == False)
                
                # Execute the query
                result = await db.execute(query)
                promo_codes = result.scalars().all()  # Fetch all results
                
                # If no promo codes are found, log the event and return an empty list
                if not promo_codes:
                    await CLogController.MSWriteLog(user_id, "Info", "No PromoCodes found.")
                    return []

                # Log successful retrieval
                await CLogController.MSWriteLog(user_id,"Info", "Successfully retrieved all PromoCodes.")
                
                # Return a list of promo code details
                return [
                    {
                        "PromoCodeId": promo.PromoCodeId,
                        "PromoCodeName": promo.PromoCodeName,
                        "CreatedTime": str(promo.CreatedTime),
                        "ExpireTime": str(promo.ExpireTime),
                        "PromoCodeType": promo.PromoCodeType,
                        "Action": promo.Action,
                        "Value": promo.Value,
                        "MaxPromptCodeUseCount": promo.MaxPromptCodeUseCount,
                        "IsPromoExpired": promo.IsPromoExpired
                    }
                    for promo in promo_codes
                ]
            
            except SQLAlchemyError as e:
                await db.rollback()
                # Log the SQLAlchemy error and raise an HTTPException
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to retrieve all PromoCodes. SQL Error: {str(e)}")
                await CLogController.MSWriteLog(user_id,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An error occurred while retrieving PromoCodes.")
            except Exception as e:
                await CLogController.MSWriteLog(user_id,"Error", f"Error occured while retrieving all PromoCodes.")
                await CLogController.MSWriteLog(user_id,"Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="Error occured while retrieving all PromoCodes.")

    @staticmethod
    async def MSIsValidReferralCode(promo_name: str) -> bool:
        """
        Check if a promo code is valid.

        Args:
            promo_name (str): The name of the promo code.

        Returns:
            bool: True if the promo code is valid, False otherwise.
        """
        async with AsyncSessionLocal() as session:
            try:
                # Query the database for the promo code
                query_result = await session.execute(
                    select(PromoCode)
                    .filter(func.lower(PromoCode.PromoCodeName) == func.lower(promo_name))
                )
                promo = query_result.scalar()  # Fetch the first result as a scalar

                if not promo:
                    return {"status": False}

                if promo.IsPromoExpired:
                    return {"status": False}
                # Ensure the count doesn't go below zero
                if promo.MaxPromptCodeUseCount == 0:
                    return {"status": False}
                    
                current_time = datetime.now()
                if promo.ExpireTime and promo.ExpireTime < current_time or promo.MaxPromptCodeUseCount == 0:
                    # Update the IsPromoExpired flag and commit the changes
                    promo.IsPromoExpired = True
                    promo.MaxPromptCodeUseCount = 0 # Reset Referral Code count to Zero if Referral Code Expired
                    await session.commit()
                    return {"status": False}

                if (Constants.dictPromoCodeMsg.get(promo.PromoCodeType,None)) is not None:
                    message = (Constants.dictPromoCodeMsg[promo.PromoCodeType]).format(promo_value = promo.Value)
                else:
                    message = "Referral code applied!"
                return {"status": True,"message":message}

            except SQLAlchemyError as e:
                await session.rollback()
                # Log the SQLAlchemy error and raise an HTTPException
                await CLogController.MSWriteLog(None,"Error", f"Error while Updating Database: Is Valid Referral Code, For Promo Name: {promo_name}.")
                await CLogController.MSWriteLog(None,"Debug", f"Promo Name: {promo_name}, \n\nTraceback: {str(traceback.format_exc())}")
                return {"status": False}
            except Exception as e:
                await CLogController.MSWriteLog(None,"Error", f"Error while Processing: Is Valid Referral Code, For Promo Name: {promo_name}.")
                await CLogController.MSWriteLog(None,"Debug", f"Promo Name: {promo_name}, \n\nTraceback: {str(traceback.format_exc())}")
                return {"status": False}
    
    @staticmethod
    async def MSProcessRefferalCode(user_id, promo_name: str) -> dict:
        """
        Process the promo code based on its type and action.

        Args:
            promo_name (str): The name of the promo code.
            user_id (int): The user ID.

        Returns:
            dict: The response message containing the status code and additional information.
        """
        try:
            from src.Controllers.auth_controller import CAuthController
            # Get promo code details
            promo_data = await CReferralCode.MSGetReferralCodeByName(promo_name)
            if not promo_data:
                await CLogController.MSWriteLog(user_id, "warning", f"Promo code not found")
                raise HTTPException(status_code=404, detail="Oops! It seems the Referral Code entered is invalid. Please verify and try again.")

            promo_code_type = promo_data.get("PromoCodeType")
            promo_value = promo_data.get("Value")
            
            # Validate that extracted values are neither None nor empty
            if not promo_code_type or not promo_value:
                await CLogController.MSWriteLog(user_id, "warning", f"Referral Code Action Details not available such as promo_code_type: {promo_code_type} , action: {action}, promo_value: {promo_value}")
                raise HTTPException(status_code=404, detail="Oops! It seems the Referral Code entered is invalid. Please verify and try again.")

            if promo_code_type == Constants.lsPromoCodeType[0]:
                await CAuthController.MSUpdateUserAPIUsage(user_id=user_id, pro_page_limit=int(promo_value), page_limit_left=int(promo_value), bOverwrite=False)
                message = f"Referral code applied! Enjoy {promo_value} extra page extractions in your free account."
                await CLogController.MSWriteLog(user_id, "INFO", message)
                # Increment Referral Code Usage
                await CReferralCode.MSUpdateRefferalCodeCount(promoId = promo_data.get("PromoCodeId"))
                return {"status_code": 200, "message": message}
            else:
                await CLogController.MSWriteLog(user_id, "ERROR", f"Referral Code promo_code_type Details not available : {promo_code_type} inside Constants.lsPromoCodeType[0]: {Constants.lsPromoCodeType[0]}")
                raise HTTPException(status_code=404, detail="Oops! It seems the Referral Code entered is invalid. Please verify and try again.")
        except HTTPException as e:
            await CLogController.MSWriteLog(user_id,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "ERROR", f"An Error occured while Processing Referal code.")
            await CLogController.MSWriteLog(user_id,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An Error occured while Processing your Request.")
    
    @staticmethod
    async def MSUpdateRefferalCodeCount(promoId: int, value: int = 1, action: str = "subtract"):
        """
        Update the MaxPromptCodeUseCount of a promo code in the database.

        :param promoId: The ID of the promo code to update.
        :param action: The action to perform, either "add" or "subtract".
        :param value: The value to add or subtract from the MaxPromptCodeUseCount.

        :return: A confirmation message of the count update.
        """
        try:
            async with AsyncSessionLocal() as db:
                # Fetch the promo code from the database
                result = await db.execute(select(PromoCode).filter(PromoCode.PromoCodeId == promoId))
                promo_code = result.scalars().first()

                if not promo_code:
                    await CLogController.MSWriteLog(None, "ERROR", f"Promo code not found in MSUpdateRefferalCodeCount")
                    raise HTTPException(status_code=404, detail="Promo code not found")

                # Perform the action based on the provided value
                if action == "add":
                    promo_code.MaxPromptCodeUseCount += value
                elif action == "subtract":
                    promo_code.MaxPromptCodeUseCount -= value
                    # Ensure the count doesn't go below zero
                    if promo_code.MaxPromptCodeUseCount < 0:
                        promo_code.MaxPromptCodeUseCount = 0
                else:
                    await CLogController.MSWriteLog(None, "ERROR", f"Invalid action. Allowed values: 'add' or 'subtract' in MSUpdateRefferalCodeCount")
                    raise HTTPException(status_code=400, detail="Invalid action. Allowed values: 'add' or 'subtract'")

                # Commit the changes to the database
                await db.commit()

                return {"detail": "MaxPromptCodeUseCount updated successfully", "new_count": promo_code.MaxPromptCodeUseCount}

        except HTTPException as e:
            await CLogController.MSWriteLog(None,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(None,"Error", f"Error while Processing Update Refferal Code Count, For PromoID: {promoId}.")
            await CLogController.MSWriteLog(None,"Debug", f"For PromoID: {promoId}, \n\nTraceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An Error Occured while Updating the Refferal code counts.")