{"type": "json_schema", "json_schema": {"name": "8_Kotak", "strict": true, "schema": {"type": "object", "properties": {"InvoiceType": {"type": "string", "description": "Type of invoice, ex. Tax Invoice, Credit Note"}, "IRN": {"type": "string", "description": "Invoice Reference Number, a unique identifier for the invoice"}, "AckNo": {"type": "string", "description": "Acknowledgment number of the invoice"}, "AckDate": {"type": "string", "description": "The date Acknowledgment date of the invoice was issued. Change Date Format to 'YYYYMMDD"}, "SellerName": {"type": "string", "description": "Name of the seller"}, "SellerAddress": {"type": "string", "description": "Address of the seller"}, "SellerGST/UIN": {"type": "string", "description": "GST/UIN number of the seller"}, "SellerStateName": {"type": "string", "description": "State name of the seller"}, "SellerStateCode": {"type": "string", "description": "State code of the seller"}, "SellerCIN": {"type": "string", "description": "Corporate Identification Number of the seller"}, "SellerEmail": {"type": "array", "description": "List of email addresses of the seller. Each email should be a valid email string.", "items": {"type": "string", "description": "Email address of the seller"}, "example": ["<EMAIL>", "<EMAIL>"]}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerGST/UIN": {"type": "string", "description": "GST/UIN number of the buyer"}, "BuyerStateName": {"type": "string", "description": "State name of the buyer"}, "BuyerStateCode": {"type": "string", "description": "State code of the buyer"}, "CreditNoteNo/InvoiceNo": {"type": "string", "description": "Credit note number or Invoice number which ever available."}, "OtherReferences": {"type": "string", "description": "Other reference numbers related to the invoice"}, "BuyersOrderNo": {"type": "string", "description": "Buyer's order number"}, "BuyersOrderDate": {"type": "string", "description": "The date Buyer's order was issued. Change Date Format to 'YYYYMMDD"}, "BLNos": {"type": "string", "description": "Bill of Lading numbers"}, "CreditDate/InvoiceDate": {"type": "string", "description": "The date of Credit note or Invoice Date was issued. Change Date Format to 'YYYYMMDD"}, "Vessel/FlightNo": {"type": "string", "description": "Vessel or flight number"}, "PlaceOfReceiptByShipper": {"type": "string", "description": "Place where the shipper received the goods"}, "CityPortOfLoading": {"type": "string", "description": "City or port where the goods were loaded"}, "CityPortOfDischarge": {"type": "string", "description": "City or port where the goods will be discharged"}, "ItemTable": {"type": "array", "items": {"type": "object", "properties": {"DescriptionOfServices": {"type": "string", "description": "Item or service name"}, "HSN/SACCode": {"type": "integer", "description": "Harmonized System Nomenclature code for the item"}, "Quantity": {"type": "integer", "description": "Quantity of the item"}, "Rate": {"type": "number", "description": "Rate of the item"}, "Per": {"type": "integer", "description": "Quantity of the item per unit"}, "Amount": {"type": "number", "description": "Amount per unit of the item"}, "SrNo": {"type": "number", "description": "Serial number of the item"}}, "required": ["DescriptionOfServices", "HSN/SACCode", "Quantity", "Rate", "Per", "Amount", "SrNo"], "additionalProperties": false}}, "FinalSummary": {"type": "object", "properties": {"TaxableValue": {"type": "number", "description": "Total Taxable value of the goods"}, "IGSTRate": {"type": "number", "description": "Final GST rate for the goods"}, "IGSTAmount": {"type": "number", "description": "Total IGST amount calculated"}, "FinalTotalTaxAmount": {"type": "number", "description": "Final Total tax amount including IGST"}}, "required": ["TaxableValue", "IGSTRate", "IGSTAmount", "FinalTotalTaxAmount"], "additionalProperties": false}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the invoice total. If the adjustment is negative, a negative value should be provided."}, "AmountChargeable": {"type": "number", "description": "Amount chargeable"}, "CompanysPAN": {"type": "string", "description": "Company's Permanent Account Number"}}, "required": ["InvoiceType", "IRN", "AckNo", "AckDate", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerGST/UIN", "SellerStateName", "SellerStateCode", "SellerCIN", "Seller<PERSON>mail", "BuyerName", "BuyerAddress", "BuyerGST/UIN", "BuyerStateName", "BuyerStateCode", "CreditNoteNo/InvoiceNo", "CreditDate/InvoiceDate", "OtherReferences", "BuyersOrderNo", "BuyersOrderDate", "BLNos", "Vessel/FlightNo", "PlaceOfReceiptByShipper", "CityPortOfLoading", "CityPortOfDischarge", "RoundingOff", "AmountChargeable", "CompanysPAN", "ItemTable", "FinalSummary"], "additionalProperties": false}}}