#!/usr/bin/env python3
import smtplib
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import subprocess
from datetime import datetime
import re
import time

# Set the CPU threshold
CPU_THRESHOLD = 70
MEMORY_THRESHOLD = 70
SAMPLE_DURATION = 30  # Duration over which to sample memory usage
SAMPLE_INTERVAL = 1  # Interval between samples

message = ""

# Get the current date and time
current_date = datetime.now().strftime("%Y-%m-%d %H:%M:%S")

# Email configuration
senderEmail = '<EMAIL>'
senderPassword = 'ADMINadmin123'
smtpServer = 'smtppro.zoho.in'
smtpPort = 587
receivers = ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"]
subject = "CPU Usage Alert"

# Function to get top 20 processes by CPU usage
def get_top_processes():
    top_processes = subprocess.check_output(['ps', 'aux', '--sort=-%cpu']).decode('utf-8').splitlines()[:40]
    return '\n'.join(top_processes)

# Function to get memory usage
def get_memory_usage():
    memory_usage = subprocess.check_output(['free', '-h']).decode('utf-8').strip()
    return memory_usage

# Function to get disk usage
def get_disk_usage():
    disk_usage = subprocess.check_output(['df', '-h']).decode('utf-8').strip()
    return disk_usage

# Function to get system uptime
def get_system_uptime():
    uptime = subprocess.check_output(['uptime', '-p']).decode('utf-8').strip()
    return uptime

# Function to get memory usage
def get_memory_usage():
    memory_output = subprocess.check_output(['/usr/bin/free', '-m']).decode('utf-8').strip()
    total_memory = int(memory_output.split('\n')[1].split()[1])
    used_memory = int(memory_output.split('\n')[1].split()[2])
    memory_usage_percent = (used_memory / total_memory) * 100
    return memory_usage_percent, memory_output

# Function to calculate average memory usage over a duration
def get_avg_memory_usage(duration, interval):
    samples = []
    for _ in range(duration // interval):
        memory_usage_percent, _ = get_memory_usage()
        samples.append(memory_usage_percent)
        time.sleep(interval)
    avg_memory_usage_percent = sum(samples) / len(samples)
    return avg_memory_usage_percent, samples

# Get average memory usage over the sampling duration
avg_memory_usage_percent, memory_samples = get_avg_memory_usage(SAMPLE_DURATION, SAMPLE_INTERVAL)
print(f"Average Memory usage over {SAMPLE_DURATION} seconds: {avg_memory_usage_percent}%")


# Function to get CPU usage
# def get_cpu_usage():
#     cpu_command = "top -bn1 | grep 'Cpu(s)'"
#     cpu_output = subprocess.check_output(cpu_command, shell=True).decode('utf-8').strip()
#     pattern = r"%Cpu\(s\):\s+(\d+\.\d+)\s+us"
#     match = re.search(pattern, cpu_output)
#     if match:
#         cpu_usage = float(match.group(1))
#     else:
#         cpu_usage = 0.0
#         print("Failed to extract CPU usage percentage.")
#     return cpu_usage
def get_cpu_usage():
    cpu_command = "vmstat 1 2 | tail -1 | awk '{print 100 - $15}'"
    cpu_output = subprocess.check_output(cpu_command, shell=True).decode('utf-8').strip()
    try:
        cpu_usage = float(cpu_output)
    except ValueError:
        cpu_usage = 0.0
        print("Failed to extract CPU usage percentage.")
    return cpu_usage

# Function to monitor CPU usage over a duration and calculate the average
def monitor_cpu_usage(duration=SAMPLE_DURATION, interval=SAMPLE_INTERVAL):
    cpu_usages = []
    end_time = time.time() + duration
    while time.time() < end_time:
        cpu_usage = get_cpu_usage()
        cpu_usages.append(cpu_usage)
        time.sleep(interval)
    avg_cpu_usage = sum(cpu_usages) / len(cpu_usages)
    return avg_cpu_usage

# Monitor CPU usage
avg_cpu_usage = monitor_cpu_usage()
print(f"Average CPU Usage over {SAMPLE_DURATION} seconds: {avg_cpu_usage:.2f}%")
cpu_usage_percent = avg_cpu_usage


# Check if CPU usage is above threshold and construct email message
if cpu_usage_percent > CPU_THRESHOLD  or avg_memory_usage_percent > MEMORY_THRESHOLD:
    if cpu_usage_percent > CPU_THRESHOLD:
        message += f"CPU usage is above {CPU_THRESHOLD}%. Current usage: {cpu_usage_percent}%\nAverage Memory usage over {SAMPLE_DURATION} seconds is {avg_memory_usage_percent}"
    if avg_memory_usage_percent > MEMORY_THRESHOLD:
        message += f"Average Memory usage over {SAMPLE_DURATION} seconds is above {MEMORY_THRESHOLD}%. Current usage: {avg_memory_usage_percent}%\n"
    
    memory_usage = get_memory_usage()
    disk_usage = get_disk_usage()
    system_uptime = get_system_uptime()
    
    # Construct HTML email using your template
    html = f"""
    <!DOCTYPE html>
    <html>
    <head>
        <style>
            body {{
                font-family: Arial, sans-serif;
                line-height: 1.6;
            }}
            .email-container {{
                max-width: 600px;
                margin: 0 auto;
                padding: 20px;
                background-color: #f9f9f9;
                border: 1px solid #ccc;
                border-radius: 5px;
            }}
            .email-header {{
                background-color: #007bff;
                color: #fff;
                padding: 10px;
                text-align: center;
                border-top-left-radius: 5px;
                border-top-right-radius: 5px;
            }}
            .email-body {{
                padding: 20px;
            }}
            .email-footer {{
                margin-top: 20px;
                text-align: center;
                color: #666;
            }}
        </style>
    </head>
    <body>
        <div class="email-container">
            <div class="email-header">
                <h2>CPU Usage Alert</h2>
            </div>
            <div class="email-body">
                <p>Dear Team,</p>
                <p>{message}</p>
                <p><strong>Memory Usage:</strong></p>
                <pre>{avg_memory_usage_percent}</pre>
                <p><strong>Disk Usage:</strong></p>
                <pre>{disk_usage}</pre>
                <p><strong>System Uptime:</strong></p>
                <pre>{system_uptime}</pre>
                <p>Best regards,<br>RiverEdge Analytics</p>
            </div>
            <div class="email-footer">
                <p>This is an automated notification.</p>
            </div>
        </div>
    </body>
    </html>
    """
    msg = MIMEMultipart()
    msg['From'] = senderEmail
    msg['To'] = ", ".join(receivers)
    msg['Subject'] = subject

    msg.attach(MIMEText(html, 'html'))

    # Attach top processes text file
    top_processes_text = get_top_processes()
    attachment = MIMEBase('application', 'octet-stream')
    attachment.set_payload(top_processes_text.encode('utf-8'))
    attachment.add_header('Content-Disposition', 'attachment', filename='top_processes.txt')
    encoders.encode_base64(attachment)
    msg.attach(attachment)

    try:
        # Setup SMTP server
        server = smtplib.SMTP(smtpServer, smtpPort)
        server.starttls()
        # Login to email server
        server.login(senderEmail, senderPassword)
        # Send email
        server.sendmail(senderEmail, receivers, msg.as_string())
        print('Email notification sent successfully!')
    except Exception as e:
        print(f'Failed to send email notification: {str(e)}')
    finally:
        # Close the SMTP server connection
        server.quit()
else:
    message = f"CPU usage is below threshold. Current usage: {cpu_usage_percent}%\nAverage Memory usage over {SAMPLE_DURATION} seconds is below {MEMORY_THRESHOLD}%. Current usage: {avg_memory_usage_percent}%\n"
    print(f"{current_date} - CPU usage is below threshold. Current usage: {cpu_usage_percent}%")

# Log the current CPU usage
with open('/var/log/cpu_monitor.log', 'a') as log_file:
    log_file.write(f"{current_date} - {message}\n")




"""
#!/bin/bash

# Set the CPU threshold
CPU_THRESHOLD=50

# Get the current CPU usage
CPU_USAGE=$(top -bn1 | grep "Cpu(s)" | sed "s/.*, *\([0-9.]*\)%* id.*/\1/" | awk '{print 100 - $1}')

# Get the current date and time
CURRENT_DATE=$(date +"%Y-%m-%d %H:%M:%S")

# Check if the CPU usage is greater than the threshold
if (( $(echo "$CPU_USAGE > $CPU_THRESHOLD" | bc -l) )); then
    # Send an email notification
    EMAIL_MESSAGE="CPU usage is above ${CPU_THRESHOLD}%. Current usage: ${CPU_USAGE}%"
    echo "$EMAIL_MESSAGE" | mail -s "CPU Usage Alert" <EMAIL>,<EMAIL>,<EMAIL>
    echo "$CURRENT_DATE - $EMAIL_MESSAGE" >> /var/log/cpu_monitor.log
else
    # Log the current CPU usage without sending an email
    echo "$CURRENT_DATE - CPU usage is below threshold. Current usage: ${CPU_USAGE}%" >> /var/log/cpu_monitor.log
fi

Every 5 minute it go check cpu monitor over period of 30 seconds 
*/5 * * * * /home/<USER>/cpu_monitor.sh

"""