import sys
sys.path.append(".")
from alembic.config import Config
from alembic import command
from datetime import datetime
import subprocess
import os
from config.db_config import engine

# import traceback

class CDBMigration:
    
    
    @staticmethod
    def backup_database():
        """
        Backs up the database using mysqldump or a similar tool, depending on the database.
        """
        try:
            print("\n\n------------------------------Starting Database Backup------------------------------\n\n")
            
            db_user = os.getenv('DATABASE_USER')
            db_pass = os.getenv('DATABASE_PASSWORD_DEV')
            db_name = os.getenv('DATABASE_NAME')
            host = os.getenv('DATABASE_URL_DEV')
            port = os.getenv('DATABASE_PORT')
            
            backup_filename = rf"Backup/{db_name}_backup_{datetime.now().strftime('%Y_%m_%d_%H_%M_%S')}.sql"
            
            os.makedirs("Backup", exist_ok=True)  # Create directory for backups if it doesn't exist
            
            # Construct the mysqldump command
            command = [
                'mysqldump',
                '-h', host,
                '-P', str(port),
                '-u', db_user,
                f'--password={db_pass}',
                db_name,
                '>', backup_filename
            ]
            
            # Running the backup command
            subprocess.run(' '.join(command), shell=True, check=True)
            
            print("\n\n-----------------------Database Backup Completed Successfully-----------------------\n\n")
        
        except subprocess.CalledProcessError as e:
            print("\n\n-----------------An error occurred while taking the database backup.--------------\n\n")
            print(str(e))
        except Exception as e:
            print("\n\n-------------An unexpected error occurred during the database backup.---------------\n\n")
            print(str(e))
            
            
    @staticmethod
    def auto_generate_migrations(engine):
        """
        Automatically generate migration scripts if there are changes in the models.
        This should be run separately and with caution.
        """
        try:
            print("\n\n*****************************Generation of Migration File Started***************************************\n\n")
            
            alembic_cfg = Config("alembic/alembic.ini")
            alembic_cfg.set_main_option("sqlalchemy.url", str(engine.url))
            alembic_cfg.set_main_option("script_location", "alembic")
            
            current_datetime = datetime.now()
            formatted_datetime = current_datetime.strftime("%Y_%m_%d_%H_%M_%S")
            message = f"{formatted_datetime}"

            command.revision(alembic_cfg, autogenerate=True, message=message)
        except Exception as e:
            print("\n\n***********************There was an error while Generating Migration File *******************************\n\n")
            print(e)
            # print(traceback.print_exc())
        
        finally:
            print("\n\n*****************************Generation of Migration File Completed***************************************\n\n")
            
            
    @staticmethod
    def apply_migrations(engine):
        """
        Apply all pending migrations to the database.
        """
        try:
            print("\n\n----------------------------------------------Applying Migrations----------------------------------------------\n\n")
            alembic_cfg = Config("alembic/alembic.ini")
            alembic_cfg.set_main_option("sqlalchemy.url", str(engine.url))
            command.upgrade(alembic_cfg, "head")
            
        except Exception as e:
            print("\n\n--------------------------------------Error Occurred While Applying Migrations----------------------------------\n\n")
            print(e)
            # print(traceback.print_exc())
        
        finally:
            print("\n\n---------------------------------------------Migration Completed-------------------------------------------------\n\n")
            
    @staticmethod
    def run(engine):
        """
        Run the database migrations.
        """
        CDBMigration.backup_database()
        CDBMigration.auto_generate_migrations(engine)
        CDBMigration.apply_migrations(engine)


if __name__ == "__main__":
    
    CDBMigration.run(engine)