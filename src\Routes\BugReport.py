from fastapi import APIRouter, Depends,UploadFile, File, Form, HTTPException
from typing import List, Optional
from src.Controllers.BugReport_Controller import CBugReportController
from src.Schemas.Bugs_Schema import BugReport
from src.middleware.checkAuth import admin_required, user_required
import traceback
from src.Controllers.Logs_Controller import CLogController

bugs = APIRouter(tags=['BugReport'], prefix="/api")

@bugs.post('/bug-report')  #iUserID: int = Depends(user_required),documents: List[UploadFile] = File(None)
async def create_bug(BugTime: Optional[str] = Form(None),
    Browser: Optional[str] = Form(None),
    AppVersion: Optional[str] = Form(None),
    Description: str = Form(...),
    OperatingSystem: Optional[str] = Form(None),
    CreatedDateTime: Optional[str] = Form(None),
    updated_at: Optional[str] = Form(None),
    iUserID: int = Depends(user_required),
    documents: List[UploadFile] = File(None)):
    try:
        request = BugReport(
            BugTime=BugTime,
            Browser=Browser,
            AppVersion=AppVersion,
            Description=Description,
            CreatedDateTime=CreatedDateTime,
            updated_at=updated_at,
            OperatingSystem=OperatingSystem)
        result = await CBugReportController.MSCreateReportBug(objrequest = request, user_id=iUserID, documents=documents)
        await CLogController.MSWriteLog(iUserID, "Info", "Bug report created successfully.")
        return result
    except HTTPException as http_exc:
        await CLogController.MSWriteLog(iUserID, "Error", f"HTTP Exception: {http_exc.detail}")
        raise http_exc
    except Exception as e:
        await CLogController.MSWriteLog(iUserID, "Error", f"Unexpected error: {str(e)}")
        await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {traceback.format_exc()}")
        raise HTTPException(status_code=500, detail="An internal server error occurred.")
