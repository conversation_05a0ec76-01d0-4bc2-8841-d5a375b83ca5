{"type": "json_schema", "json_schema": {"name": "hansgrohe_invoice_detail", "strict": true, "schema": {"type": "object", "properties": {"DespatchedFromName": {"type": "string", "description": "Name of the seller mentioned on the invoice"}, "DespatchedFromAddress": {"type": "string", "description": "Complete address of the seller"}, "DespatchedFromState": {"type": "string", "description": "State of the seller's address"}, "DespatchedFromGstNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the seller"}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyyyy format) on which the invoice was issued"}, "DocumentNumber": {"type": "string", "description": "Unique identifier for the invoice document"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"Item": {"type": "integer", "description": "The serial number of the item in the invoice"}, "Material": {"type": "string", "description": "Number of the material"}, "HSNCode": {"type": "integer", "description": "The HSN (Harmonized System of Nomenclature) code used to identify the product"}, "Quantity": {"type": "number", "description": "quantity of the product"}, "Brand": {"type": "string", "description": "Brand of the product"}, "Description": {"type": "string", "description": "A detailed description of the product, including its features and specifications"}, "UnitPrice-Price/Piece": {"type": "number", "description": "Price of product before discounts and Taxes per quantity"}, "TotalPrice-Total/Net": {"type": "number", "description": "Total Price of product before discounts and Taxes for all quantity"}, "UnitNetPrice-Price/Piece": {"type": "number", "description": "Net price of the item, after discount and before tax per quantity"}, "TotalNetPrice-Price/Piece": {"type": "number", "description": "Total Net price of the item, after discount and before tax for all quantity"}}, "required": ["<PERSON><PERSON>", "Material", "HSNCode", "Quantity", "Brand", "Description", "UnitPrice-Price/Piece", "TotalPrice-Total/Net", "UnitNetPrice-Price/Piece", "TotalNetPrice-Price/Piece"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced"}, "Subtotal": {"type": "number", "description": "Subtotal in amount"}, "TotalAmount": {"type": "number", "description": "Total amount"}, "CGSTRate": {"type": "number", "description": "Percentage of Central Goods and Services Tax (CGST)"}, "CGSTAmount": {"type": "number", "description": "Total amount of Central Goods and Services Tax (CGST)"}, "IGSTRate": {"type": "number", "description": "Percentage of Integrated Goods and Services Tax (IGST)"}, "IGSTAmount": {"type": "number", "description": "Total amount of Integrated Goods and Services Tax (IGST)"}, "SGSTRate": {"type": "number", "description": "Percentage of State Goods and Services Tax (SGST)"}, "SGSTAmount": {"type": "number", "description": "Total amount of State Goods and Services Tax (SGST)"}}, "required": ["DespatchedFromName", "DespatchedFromAddress", "DespatchedFromState", "DespatchedFromGstNo", "InvoiceDate", "DocumentNumber", "Table", "Subtotal", "TotalAmount", "CGSTRate", "CGSTAmount", "IGSTRate", "IGSTAmount", "SGSTRate", "SGSTAmount"], "additionalProperties": false}}}