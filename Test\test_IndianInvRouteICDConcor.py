"""
Test Module: test_IndianInvRouteICDConcor.py

Purpose:
    This module tests the Indian Invoice Processing API for ICD Concor (Inland Container Depot) documents.
    It processes ZIP files containing invoice data and validates the API response against expected outputs.

Test Flow:
    1. Upload ZIP file to the API endpoint
    2. Receive processed ZIP file containing XML and CSV outputs
    3. Extract and validate the outputs against reference files (when BRELEARN=False)
    4. Save outputs for future reference (when BRELEARN=True)

File Types Supported:
    - Input: ZIP files (ICD Concor invoice format)
    - Output: ZIP files containing XML and CSV data

Date: 2025
"""

import unittest
import os
import asyncio
import socket
import json
import httpx
import zipfile
from Test.CustomHelper import TestLogger, CLicenseHelper, calculate_checksum, compare_xml, compare_csv, extract_zip

# Learning Mode Configuration
# BRELEARN = True:  Save API outputs as reference files for future testing
# BRELEARN = False: Compare API outputs against existing reference files
BRELEARN = False

# Directory Structure Configuration
TEST_DIR = "Test"  # Root directory for all test-related files

# Dynamic folder structure based on current file and BRELEARN flag
CURRENT_FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]  # Gets filename without .py extension
INPUT_DIR = os.path.join(TEST_DIR, "Input", CURRENT_FILE_NAME)
REFERENCE_DIR = os.path.join(TEST_DIR, "Reference", CURRENT_FILE_NAME)
LOG_DIR = os.path.join(TEST_DIR, "Logs")
API_URL = "http://**************:8034/IndianInvTally/process_doc"
LICENSE_FILE = os.path.join(TEST_DIR, "license.lic")
LOG_FILE = f"{CURRENT_FILE_NAME}.log"

# Global input file paths
INPUT_ZIP_FILENAME = "test_ICDConcor.zip"

# Function to get dynamic paths based on BRELEARN flag and test method
def get_dynamic_paths(test_method_name):
    """
    Generate dynamic file paths based on BRELEARN flag and test method name.

    This function implements the dynamic folder structure that organizes test data
    based on the current test file and method being executed.

    Directory Structure Created:
        BRELEARN = True:  Test/Input/{current_file_name}/{test_method_name}/
        BRELEARN = False: Test/Reference/{current_file_name}/{test_method_name}/

    Args:
        test_method_name (str): Name of the test method being executed
                               (e.g., "test_ICDConcor")

    Returns:
        tuple: A tuple containing:
            - data_dir (str): Full path to the test data directory
            - input_file_path (str): Full path to the input ZIP file
    """
    # Determine target directory based on BRELEARN flag
    if BRELEARN:
        # Learning mode: Save new outputs to Input directory
        data_dir = os.path.join(INPUT_DIR, test_method_name)
    else:
        # Testing mode: Use Reference directory for comparison
        data_dir = os.path.join(REFERENCE_DIR, test_method_name)

    # Create directory if it doesn't exist (ensures clean test environment)
    os.makedirs(data_dir, exist_ok=True)

    # Construct full path to input file
    input_zip_path = os.path.join(data_dir, INPUT_ZIP_FILENAME)
    return data_dir, input_zip_path

# Initialize logger
os.makedirs(LOG_DIR, exist_ok=True)
test_logger = TestLogger(LOG_DIR, LOG_FILE)
test_logger.MSWriteLog("info", "Test logger initialized")

class TestIndianInvRouteICDConcor(unittest.IsolatedAsyncioTestCase):
    """
    Test class for ICD Concor (Inland Container Depot) document processing.

    This class contains test methods that validate the Indian Invoice Processing API
    for ICD Concor documents. It inherits from IsolatedAsyncioTestCase to support
    asynchronous test execution.

    Test Methods:
        - test_ICDConcor: Main test method for ICD Concor document processing

    Setup/Teardown:
        - asyncSetUp: Initializes test environment and creates necessary directories
        - asyncTearDown: Cleans up after test execution

    Utility Methods:
        - is_server_listening: Checks if the API server is available
    """
    async def asyncSetUp(self):
        """
        Asynchronous setup method executed before each test method.

        This method prepares the test environment by:
        1. Creating dynamic directory structure based on BRELEARN flag
        2. Setting up logging for the current test method
        3. Ensuring all necessary directories exist

        The dynamic path structure ensures that each test method has its own
        isolated directory for input files and outputs.

        Raises:
            Exception: If directory creation or logger setup fails
        """
        try:
            # Get dynamic paths based on BRELEARN flag and current test method
            # This creates either Input or Reference directory structure
            self.strCurTestCaseDirPath, self.input_file_path = get_dynamic_paths(self._testMethodName)

            # Initialize logger for this test instance
            self.logger = test_logger
            test_logger.MSWriteLog("info", f"Setup completed for: {self._testMethodName}")
            test_logger.MSWriteLog("info", f"Using directory: {self.strCurTestCaseDirPath}")
            test_logger.MSWriteLog("info", f"Input file path: {self.input_file_path}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            raise

    async def asyncTearDown(self):
        """
        Asynchronous teardown method executed after each test method.

        This method performs cleanup operations after test execution:
        1. Logs completion status
        2. Handles any cleanup errors gracefully

        Note: Currently minimal cleanup is needed as the test creates
        files that serve as either reference data or test outputs.

        Raises:
            Exception: If teardown operations fail
        """
        try:
            test_logger.MSWriteLog("info", f"Teardown completed for: {self.strCurTestCaseDirPath}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Teardown failed: {str(e)}")
            raise

    def is_server_listening(self, host="**************", port=8034, timeout=2):
        """
        Check if the API server is available and listening on the specified port.

        This utility method verifies server connectivity before attempting
        to make API calls, helping to provide clear error messages when
        the server is unavailable.

        Args:
            host (str): Server hostname or IP address (default: "**************")
            port (int): Server port number (default: 8034)
            timeout (int): Connection timeout in seconds (default: 2)

        Returns:
            bool: True if server is listening and accepting connections,
                  False if server is unreachable or not responding

        Example:
            if self.is_server_listening():
                # Proceed with API call
                response = await client.post(API_URL, ...)
            else:
                self.fail("API server is not available")
        """
        try:
            # Attempt to establish a socket connection to the server
            with socket.create_connection((host, port), timeout=timeout):
                test_logger.MSWriteLog("info", f"Server at {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            test_logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            return False

    async def test_ICDConcor(self):
        """
        Main test method for ICD Concor (Inland Container Depot) document processing.

        This test validates the complete workflow of processing ICD Concor documents:

        Test Flow:
        1. License Verification: Validates API license and extracts user token
        2. File Preparation: Sets up input ZIP file and output paths
        3. Server Connectivity: Checks if API server is available
        4. Document Upload: Sends ZIP file to API endpoint with metadata
        5. Response Processing: Receives and validates ZIP response
        6. Content Extraction: Extracts XML and CSV from ZIP file
        7. File Validation: Compares outputs with reference files (BRELEARN=False)
                           or saves as new reference files (BRELEARN=True)

        API Endpoint: POST /IndianInvTally/process_doc
        User Type: Premium user (iUserid=5)
        Input Format: ZIP file containing XLSX and PDF files
        Output Format: ZIP file containing XML and CSV files

        Expected Outputs:
        - ICDConcor.xml: Structured XML representation of ICD data
        - Report_ICDConcor.csv: Tabular CSV report of processed data

        Raises:
            AssertionError: If API response is invalid or file comparison fails
            Exception: If license verification, file operations, or API calls fail
        """
        test_logger.MSWriteLog("info", f"Executing test: {self._testMethodName}")
        try:

            test_logger.MSWriteLog("info", "Step 1: Verifying license file")

            # Check if license file exists
            if not os.path.exists(LICENSE_FILE):
                test_logger.MSWriteLog("error", f"License file not found: {LICENSE_FILE}")
                self.fail(f"License file not found: {LICENSE_FILE}")

            # Extract license data and user token
            license_data = CLicenseHelper.MSVerifyLicense(LICENSE_FILE)
            strUserToken = license_data["Token"]
            user_id = license_data.get("uid", "unknown")
            test_logger.MSWriteLog("info", f"License verified, token: {strUserToken[:10]}..., uid: {user_id}")


            test_logger.MSWriteLog("info", "Step 2: Setting up file paths")

            # Define document name and file paths
            doc_name = "Concor"  # Standardized document name derived from input filename
            strInputZipPath = self.input_file_path  # Dynamic input file path
            strInputXmlPath = os.path.join(self.strCurTestCaseDirPath, f"{doc_name}.xml")
            strInputCsvPath = os.path.join(self.strCurTestCaseDirPath, f"Report_{doc_name}.csv")
            output_dir = self.strCurTestCaseDirPath  # Dynamic output directory
            strZipPath = os.path.join(output_dir, "Content_zipFile_Concor.zip")

            test_logger.MSWriteLog("info", f"Input ZIP: {strInputZipPath}")
            test_logger.MSWriteLog("info", f"Output directory: {output_dir}")
            strOutputXmlPath = os.path.join(output_dir, f"{doc_name}.xml")
            strOutputCsvPath = os.path.join(output_dir, f"Report_{doc_name}.csv")


            test_logger.MSWriteLog("info", "Step 3: Validating input files")

            # Verify input ZIP file exists
            if not os.path.exists(strInputZipPath):
                test_logger.MSWriteLog("error", f"Input ZIP file not found: {strInputZipPath}")
                self.fail(f"Input ZIP file not found: {strInputZipPath}")

            # Verify reference files exist only in testing mode (BRELEARN=False)
            if not BRELEARN:
                test_logger.MSWriteLog("info", "Testing mode: Validating reference files")
                for file_path in [strInputXmlPath, strInputCsvPath]:
                    if not os.path.exists(file_path):
                        test_logger.MSWriteLog("error", f"Reference file not found: {file_path}")
                        self.fail(f"Reference file not found: {file_path}")
            else:
                test_logger.MSWriteLog("info", "Learning mode: Will create new reference files")

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Verify ZIP contents (XLSX and PDF)
            try:
                with zipfile.ZipFile(strInputZipPath, "r") as zip_file:
                    zip_contents = zip_file.namelist()
                    test_logger.MSWriteLog("info", f"Input ZIP contents: {zip_contents}")
                    xlsx_found = any(f.endswith(".xlsx") for f in zip_contents)
                    pdf_found = any(f.endswith(".pdf") for f in zip_contents)
                    if not xlsx_found:
                        test_logger.MSWriteLog("error", "No XLSX file found in input ZIP")
                        self.fail("No XLSX file found in input ZIP")
                    if not pdf_found:
                        test_logger.MSWriteLog("error", "No PDF file found in input ZIP")
                        self.fail("No PDF file found in input ZIP")
            except zipfile.BadZipFile as e:
                test_logger.MSWriteLog("error", f"Invalid input ZIP file: {str(e)}")
                self.fail(f"Invalid input ZIP file: {str(e)}")


            test_logger.MSWriteLog("info", "Step 4: Checking server availability")

            if not self.is_server_listening():
                test_logger.MSWriteLog("error", f"Server at {API_URL} is not available")
                self.fail(f"Server at {API_URL} is not available")


            test_logger.MSWriteLog("info", "Step 5: Preparing API request")

            # Set up authentication headers
            headers = {"Authorization": f"Bearer {strUserToken}", "iUserid": str(user_id)}
            # Calculate file checksum for integrity verification
            checksum = calculate_checksum(strInputZipPath, algorithm="md5")
            test_logger.MSWriteLog("info", f"File checksum: {checksum}")

            # Prepare document metadata for API
            lsClientDocMetaData = [{
                "filename": INPUT_ZIP_FILENAME,
                "Type": ".zip",
                "location": strInputZipPath,
                "checksum": checksum
            }]
            # Prepare API parameters
            params = {
                "bTestMode": True,                                          # Enable test mode
                "strVoucherType": "JOURNAL_VOUCHER",                       # Document type for ICD
                "lsClientDocMetaData": json.dumps(lsClientDocMetaData),    # Document metadata as JSON
                "bIsMultivendorDoc": False,                                # Single vendor document
                "strSystemName": os.getlogin(),                           # Current system user
                "strSerializeUserConfig": json.dumps({                    # System configuration
                    "Exe_version": "2.4",
                    "Exe_ReleaseDate": "2025-06-28",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [                                      # Available API endpoints
                        "http://122.170.3.105:8024/",
                        "http://**************:8034/",
                        "http://122.170.3.105:8034/",
                        "http://**************:8024/"
                    ]
                })
            }

            # Additional data payload
            data = {"checksums": checksum}


            test_logger.MSWriteLog("info", f"Step 6: Sending POST request to {API_URL}")

            try:
                # Create HTTP client with extended timeout for large file processing
                with httpx.Client(timeout=1200) as client:  # 20 minute timeout
                    response = client.post(
                        API_URL,
                        headers=headers,
                        params=params,
                        files=[("documents", (INPUT_ZIP_FILENAME, open(strInputZipPath, "rb"), "application/zip"))],
                        data=data
                    )
            except Exception as e:
                test_logger.MSWriteLog("error", f"API request failed: {str(e)}")
                self.fail(f"API request failed: {str(e)}")


            test_logger.MSWriteLog("info", "Step 7: Validating API response")

            # Check HTTP status code
            if response.status_code != 200:
                test_logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response.text}")
                self.fail(f"Expected status code 200, got {response.status_code}")

            # Verify response content type is ZIP
            expected_content_type = "application/zip"
            actual_content_type = response.headers.get("Content-Type", "")
            self.assertEqual(actual_content_type, expected_content_type,
                           f"Expected Content-Type {expected_content_type}, got {actual_content_type}")

            test_logger.MSWriteLog("info", f"Response validated: Status {response.status_code}, Content-Type {actual_content_type}")


            test_logger.MSWriteLog("info", "Step 8: Processing ZIP response")

            # Extract checksum from response headers for integrity verification
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                test_logger.MSWriteLog("error", "Checksum not received in response headers")
                self.fail("Checksum not received in response headers")

            # Save ZIP file to disk
            test_logger.MSWriteLog("info", f"Saving ZIP file to: {strZipPath}")
            with open(strZipPath, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)

            # Verify file integrity using checksum
            saved_checksum = calculate_checksum(strZipPath, algorithm="md5")
            self.assertEqual(saved_checksum, response_checksum,
                           "Checksum mismatch! The downloaded file is corrupted")
            test_logger.MSWriteLog("info", f"ZIP file integrity verified: {saved_checksum}")


            test_logger.MSWriteLog("info", "Step 9: Extracting ZIP contents")

            # Extract XML and CSV files from ZIP
            xml_path, csv_path = extract_zip(strZipPath, output_dir, "", "")

            # Verify extracted files exist
            self.assertTrue(os.path.exists(xml_path), f"XML file not found at: {xml_path}")
            test_logger.MSWriteLog("info", f"XML extracted to: {xml_path}")

            self.assertTrue(os.path.exists(csv_path), f"CSV file not found at: {csv_path}")
            test_logger.MSWriteLog("info", f"CSV extracted to: {csv_path}")


            test_logger.MSWriteLog("info", "Step 10: Standardizing file names")

            # Rename extracted files to standard naming convention
            if os.path.basename(xml_path) != f"{doc_name}.xml":
                os.rename(xml_path, strOutputXmlPath)
                xml_path = strOutputXmlPath
                test_logger.MSWriteLog("info", f"Renamed XML to: {xml_path}")

            if os.path.basename(csv_path) != f"Report_{doc_name}.csv":
                os.rename(csv_path, strOutputCsvPath)
                csv_path = strOutputCsvPath
                test_logger.MSWriteLog("info", f"Renamed CSV to: {csv_path}")


            if not BRELEARN:
                # Testing Mode: Compare outputs with reference files
                test_logger.MSWriteLog("info", "Step 11: Comparing outputs with reference files")
                # XML Comparison
                test_logger.MSWriteLog("info", f"Comparing XML: {strInputXmlPath} vs {xml_path}")
                bAreSameXml, strLogMessageXml = compare_xml(
                    self.logger,
                    strInputXmlPath,  # Reference XML file
                    xml_path,         # Generated XML file
                    ignore_tags=["VOUCHERNUMBER","AMOUNT","VATEXPAMOUNT"]    # Ignore dynamic tags for ICD documents
                )

                if not bAreSameXml:
                    test_logger.MSWriteLog("error", f"XML content mismatch: {strLogMessageXml}")
                    self.fail(f"XML content mismatch: {strLogMessageXml}")
                test_logger.MSWriteLog("info", "✓ XML content validation passed")

                # CSV Comparison
                test_logger.MSWriteLog("info", f"Comparing CSV: {strInputCsvPath} vs {csv_path}")
                bAreSameCsv, strLogMessageCsv = compare_csv(
                    self.logger,
                    strInputCsvPath,                    # Reference CSV file
                    csv_path,                          # Generated CSV file
                    ignore_columns=["Received Date"]   # Ignore dynamic date columns
                )

                if not bAreSameCsv:
                    test_logger.MSWriteLog("error", f"CSV content mismatch: {strLogMessageCsv}")
                    self.fail(f"CSV content mismatch: {strLogMessageCsv}")
                test_logger.MSWriteLog("info", "✓ CSV content validation passed")

                test_logger.MSWriteLog("info", "✓ All validations passed - Test completed successfully")
            else:
                # Learning Mode: Save outputs as new reference files
                test_logger.MSWriteLog("info", "Step 11: Learning mode - Outputs saved as reference files")
                test_logger.MSWriteLog("info", f"✓ New reference XML: {xml_path}")
                test_logger.MSWriteLog("info", f"✓ New reference CSV: {csv_path}")
                test_logger.MSWriteLog("info", "✓ Learning completed - Files ready for future testing")

        except Exception as e:
            test_logger.MSWriteLog("error", f"Test execution failed: {str(e)}")
            raise

if __name__ == "__main__":
    """
    Main execution block for running the test suite.

    This block is executed when the script is run directly (not imported).
    It initializes the unittest framework and executes all test methods
    in the TestIndianInvRouteICDConcor class.

    Usage:
        python test_IndianInvRouteICDConcor.py

    The test can be run in two modes:
    1. Learning Mode (BRELEARN=True): Creates new reference files
    2. Testing Mode (BRELEARN=False): Validates against existing reference files
    """
    try:
        test_logger.MSWriteLog("info", "=" * 80)
        test_logger.MSWriteLog("info", "STARTING ICD CONCOR TEST EXECUTION")
        test_logger.MSWriteLog("info", f"Mode: {'Learning' if BRELEARN else 'Testing'}")
        test_logger.MSWriteLog("info", f"Test File: {CURRENT_FILE_NAME}")
        test_logger.MSWriteLog("info", "=" * 80)

        # Execute all test methods using unittest framework with asyncio support
        asyncio.run(unittest.main(verbosity=2))

        test_logger.MSWriteLog("info", "=" * 80)
        test_logger.MSWriteLog("info", "TEST EXECUTION COMPLETED SUCCESSFULLY")
        test_logger.MSWriteLog("info", "=" * 80)

    except Exception as e:
        test_logger.MSWriteLog("error", f"Test execution failed: {str(e)}")
        test_logger.MSWriteLog("error", "=" * 80)
        raise