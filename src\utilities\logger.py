import logging
from logging.handlers import TimedRotatingFileHandler
from datetime import datetime
import os 
import traceback

# Function to configure the logger
def ConfigureLogger():

    strLogDir = r"Logs"
    if not os.path.exists(strLogDir):
        os.makedirs(strLogDir)
    # Create a logger
    logger = logging.getLogger()
    logger.setLevel(logging.DEBUG)

    strLogFilepath = os.path.join(strLogDir,f"Logs_{datetime.now().strftime('%Y-%m-%d')}.txt")

    # Create a file handler which logs even debug messages
    objFileHandler = TimedRotatingFileHandler(strLogFilepath,
                                 when="midnight",
                                 interval=1,
                                 backupCount=10)
    objFileHandler.setLevel(logging.DEBUG)

    # Create a formatter and add it to the handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    objFileHandler.setFormatter(formatter)

    # Add the handlers to the logger
    logger.addHandler(objFileHandler)

    return logger

# Function to log messages
def logMessage(logger, level, message):
    if level == 'info':
        logger.info(message)
    elif level == 'error':
        logger.error(message)
    elif level == 'debug':
        logger.debug(message)
    elif level == 'warning':
        logger.warning(message)
    elif level == 'critical':
        logger.critical(message)
    else:
        print("Invalid log level. Please use 'info', 'error', 'debug', 'warning', or 'critical'.")

# Example usage
if __name__ == "__main__":
    objLogger = ConfigureLogger()

    try :
        2//0
    except Exception:

        logMessage(objLogger, 'error', "IntegrityError: Error creating user")
        logMessage(objLogger, 'debug', f"Error occurred in MRegistration, Traceback_Details : "+ str(traceback.format_exc()).strip().replace('\n', ''))
    