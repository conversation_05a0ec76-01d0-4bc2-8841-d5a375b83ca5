import json
import mysql.connector
import os
# from dotenv import load_dotenv
from os.path import join, dirname

# dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
# load_dotenv(dotenv_path)
def extract_and_save_response(customer,vendorName,output_base_path,doc_ids, db_config):
    """
    Extract Response column from doc_extracted_data table for given docIds
    and save AWSExtractedText and ExtractionObject to separate files.
    
    Args:
        doc_ids (list): List of document IDs to process
        db_config (dict): Database connection configuration
    """
    output_base_path=output_base_path+f"\{customer}\{vendorName}"
    # Create output directory if it doesn't exist
    if not os.path.exists(output_base_path):
        os.makedirs(output_base_path)
    
    try:
        # Establish database connection
        conn = mysql.connector.connect(**db_config)
        cursor = conn.cursor()

        # Prepare SQL query
        query = "SELECT docId, Response FROM dbvelocity.doc_extracted_data WHERE docId IN (%s)"
        # Create placeholders for docIds
        placeholders = ','.join(['%s'] * len(doc_ids))
        query = query % placeholders

        # Execute query
        cursor.execute(query, doc_ids)
        results = cursor.fetchall()

        # Process each result
        for doc_id, response in results:
            try:
                # Parse Response JSON
                response_data = json.loads(response)
                
                # Extract AWSExtractedText and ExtractionObject
                aws_text = response_data.get('AWSExtractedText', '')
                extraction_object = response_data.get('ExtractionObject', {})
                extraction_response=json.loads(extraction_object["choices"][0]["message"]["content"])
                # Define file paths with base directory
                txt_filename = os.path.join(output_base_path, f"strUserContent_{doc_id}.txt")
                json_filename_GPT_Object = os.path.join(output_base_path, f"strGPTObject_{doc_id}.json")
                json_filename_GPT_Response = os.path.join(output_base_path, f"strGPTResponse_{doc_id}.json")
                
                # Save AWSExtractedText to txt file
                with open(txt_filename, 'w', encoding='utf-8') as txt_file:
                    txt_file.write(aws_text)
                
                # Save ExtractionObject to json file
                with open(json_filename_GPT_Object, 'w', encoding='utf-8') as json_file:
                    json.dump(extraction_object, json_file, indent=2)
                
                with open(json_filename_GPT_Response, 'w', encoding='utf-8') as json_file:
                    json.dump(extraction_response, json_file, indent=2)
                
                print(f"Successfully saved files for docId {doc_id}: {txt_filename} ,{json_filename_GPT_Response} and {json_filename_GPT_Object}")
                
            except json.JSONDecodeError as je:
                print(f"Error parsing JSON for docId {doc_id}: {je}")
            except IOError as io_err:
                print(f"Error writing files for docId {doc_id}: {io_err}")
                
    except mysql.connector.Error as db_err:
        print(f"Database error: {db_err}")
    finally:
        if cursor:
            cursor.close()
        if conn:
            conn.close()

# Example usage
if __name__ == "__main__":
    # Database configuration
    db_config = {
    'host': os.environ.get('DATABASE_URL_DEV', 'localhost'),
    'user': os.environ.get('DATABASE_USER', 'root'),
    'password': os.environ.get('DATABASE_PASSWORD_DEV', 'Real$321'),
    'database': os.environ.get('DATABASE_NAME', 'dbvelocity')
}
    print(os.environ.get('DATABASE_USER'))

    # List of docIds to process
    # doc_ids = [5660,5664,5658,5657,5655,5661,5656,5654,5663,5659]  # pegasus
    # doc_ids = [5052,5053,5054,5055,5056,5057,5058,5044,5045,5046]  # concor
    # doc_ids = [4225,4226,4223,4224,4222,5617,5615,5614,5616,5508]  #simpolo
    # doc_ids = [5577,5574,5578,5157,5144,5146,5142,5145,5143,5150]  #Hansgrohe
    doc_ids = [4001,4778,4988,4981,5349,5487,5622,5623,5646,5645]  #Kohler
    customer=input(" Enter customer name")
    vendorName=input(" Enter vendor name ")
    output_base_path=input(" Enter Output Path Folder ")
    
    # output_base_path = f"H:\DEVELOPER_PUBLIC\interns\sneha\QuickTestData\{customer}\{vendorName}"

    
    # Call the function
    extract_and_save_response(customer,vendorName,output_base_path,doc_ids, db_config)