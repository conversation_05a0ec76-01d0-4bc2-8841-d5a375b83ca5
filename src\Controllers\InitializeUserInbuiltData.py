import json
import os
from fastapi import HTTPException
from src.utilities.helperFunc import CDocument, CJSONFileReader
from src.utilities.DBHelper import CPromptTable, CDocumentTable
from src.Controllers.Vendor_Controller import CVendorController
from src.Controllers.UserAPIUsage_Controller import CUserAPIUsageData
from src.Controllers.referralCodeController import CReferralCode
from config.constants import Constants
from pathlib import Path

class CInitializeUserInbuiltData:
    INIT_MODEL_FILE_PATH = Path(r"resource/UserIntializeModelConfig.json")
    INIT_DOC_FILE_PATH = Path(r"resource/UserInitializeDocConfig.json")
    
    def __init__(self, iUserID):
        self.iUserID = iUserID
        self.dictInitModel = {}
        self.dictDemoDocs = {}
        self.dictActiveModels = {}
        self.activeModels = []
        self.dictFilterDemoDoc = {}

    @staticmethod
    def MSGetInbuiltModelData():
        # Purpose: Read and return inbuilt model data from a JSON file.
        try:
            dictInitModel = CJSONFileReader.read_json_file(CInitializeUserInbuiltData.INIT_MODEL_FILE_PATH)
            return dictInitModel
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error reading inbuilt model data.")

    @staticmethod
    def MSGetDemoDocData():
        # Purpose: Read and return demo document data from a JSON file
        try:
            dictDemoDocs = CJSONFileReader.read_json_file(CInitializeUserInbuiltData.INIT_DOC_FILE_PATH)
            return dictDemoDocs
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error reading demo document data.")

    @staticmethod
    def MSGetActiveModelList():
        # Purpose: Return a list of active model names based on the initialization status.
        try:
            active_models = []
            dictInitModel = CJSONFileReader.read_json_file(CInitializeUserInbuiltData.INIT_MODEL_FILE_PATH)
            for key, model_data in dictInitModel.items():
                if key.isdigit() and model_data.get('init_status', False):
                    active_models.append(model_data['model_name'])
            return active_models
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error getting active model list.")

    @staticmethod
    def MSActiveDemoDocData():
        # Purpose: Filter and return demo document data based on active model names.
        try:
            filterDemoDocData = {}
            dictDemoDocs = CInitializeUserInbuiltData.MSGetDemoDocData()
            active_models = CInitializeUserInbuiltData.MSGetActiveModelList()
            newIndx = 0
            for index, dictDemoDocData in dictDemoDocs.items():
                strDocModelName = dictDemoDocData.get("model_name")
                if strDocModelName in active_models:
                    filterDemoDocData[newIndx] = dictDemoDocData
                    newIndx += 1
            return filterDemoDocData
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error getting active demo document data.")

    def MGetActiveModelList(self):
        # Purpose: Populate the list of active models based on the initialization status.
        try:
            newIndx = 0
            for key, model_data in self.dictInitModel.items():
                if key.isdigit() and model_data.get('init_status', False):
                    self.activeModels.append(model_data['model_name'])
                    self.dictActiveModels[newIndx] = model_data
                    newIndx +=1
            return
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error getting active model list.")

    def MGetInbuiltModelData(self):
        # Purpose: Read inbuilt model data and populate the active model list.
        try:
            self.dictInitModel = CJSONFileReader.read_json_file(CInitializeUserInbuiltData.INIT_MODEL_FILE_PATH)
            self.MGetActiveModelList()
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error getting inbuilt model data.")

    def MGetDemoDocData(self):
        # Purpose: Read and populate demo document data from a JSON file.
        try:
            self.dictDemoDocs = CJSONFileReader.read_json_file(CInitializeUserInbuiltData.INIT_DOC_FILE_PATH)
            return
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error getting demo document data.")

    def MActiveDemoDocData(self):
        # Purpose: Filter and populate demo document data based on active model names.
        try:
            self.MGetDemoDocData()
            newIndx = 0
            for index, dictDemoDocData in self.dictDemoDocs.items():
                strDocModelName = dictDemoDocData.get("model_name")
                if strDocModelName in self.activeModels:
                    self.dictFilterDemoDoc[newIndx] = dictDemoDocData
                    newIndx += 1
            return
        except Exception as e:
            raise HTTPException(status_code=500, detail="Error filtering active demo document data.")

    async def MSSetInbuiltModelData(self):
        # Purpose: Set inbuilt model data for a user based on active models and their fields.
        try:
            self.MGetInbuiltModelData()
            for index, dictModelData in self.dictActiveModels.items():
                strModelName = dictModelData.get("model_name")
                strModelFamily = dictModelData.get("model_family")
                strModelDesc = dictModelData.get("model_description")
                await CVendorController.MSAddInvoiceFields(
                    iUserID=self.iUserID,
                    strModelName=strModelName,
                    dictFields=Constants.dictInitModelData.get(strModelName),
                    strFamilyName=strModelFamily,
                    strModelDesc= strModelDesc
                )
            return
        except HTTPException as e:
            if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            else:
                raise HTTPException(status_code=500, detail="Internal Server Error")
        except Exception as e:
            raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")

    async def MSSetDocument(self):
        # Purpose: Upload and process demo documents for a user based on active models.
        try:
            from src.Controllers.GPTResponse_controller import DocumentUploader, CGPTResponseData
            isGPTEnabled=True
            
            self.MActiveDemoDocData()
            for index, dictDemoDocData in self.dictFilterDemoDoc.items():
                strDocModelName = dictDemoDocData.get("model_name")
                strFamilyName = dictDemoDocData.get("family_name")
                dictExtractResponse = dictDemoDocData.get("extract_api_response")["choices"][0]["message"]["content"]
                content_dict = json.loads(dictExtractResponse)
                ObjUploadDoc = DocumentUploader()
                objDocument = CDocument(file_path=dictDemoDocData.get("doc_path"))
                objUploadDoc = await ObjUploadDoc.upload_document(
                    user_id=self.iUserID,
                    document=objDocument,
                    model_name=strDocModelName,
                    strFamilyName=strFamilyName,
                    strDocStatus=dictDemoDocData.get("doc_status"),
                    bUsePaidModel = isGPTEnabled,   # It only Used To Inserts GPT Data
                    DocExtractionAPIStatusCode=dictDemoDocData.get("extract_api_status_code")
                )
                await self.MSetExtractDataOnDocId(
                    objExtractResponse=dictDemoDocData.get("extract_api_response"),
                    dictExtractResponse=content_dict,
                    objUploadDoc=objUploadDoc,
                    strModelName=strDocModelName,
                    isGPTEnabled=isGPTEnabled
                )
            return
        except HTTPException as e:
            if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            else:
                raise HTTPException(status_code=500, detail="Internal Server Error")
        except Exception as e:
            raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")

    async def MSetExtractDataOnDocId(self, objExtractResponse, dictExtractResponse, objUploadDoc, strModelName, isGPTEnabled=True):
         # Purpose: Set extracted data on a document using its ID and other provided details.
        try:
            from src.Controllers.GPTResponse_controller import DocumentUploader, CGPTResponseData
            iModelId = objUploadDoc.get("ModelId")
            docId = objUploadDoc.get("DocId")
            objCurrentModelData = await CPromptTable.MSGetPromptData(
                iUserID=self.iUserID,
                objUploadDocData=objUploadDoc,
                isPaidDocExtraction=isGPTEnabled
            )
            PromptId = objCurrentModelData.get("PromptTable").get("Id")
            await CGPTResponseData.insert_gpt_result(
                iUserId=self.iUserID,
                doc_id=docId,
                obj_response=objExtractResponse,
                dict_response=dictExtractResponse,
                PromptId=PromptId,
                strModelName=strModelName,
                iModelId=iModelId,
                isGPTEnabled=isGPTEnabled
            )
        
        except Exception as e:
            raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")

    async def MSetUserApplicationUsage(self, total_allowed_page_limit):
        """
        Purpose : 
            Set the user's application usage data with initial values.

        Inputs  :  
            total_allowed_page_limit (int): The total allowed page limit for the user.

        Output  :
            None

        Example :
            await instance.MSetUserApplicationUsage(100)
        """
        try:
            await CUserAPIUsageData.MSInsertUserApiUsage(iUserID=self.iUserID, iUserApiData={
                "user_id": self.iUserID,
                "used_tokens": 0,
                "api_requested": 0,
                "page_limit_left": Constants.MaxTrialPaidDocExtractionPerUser,
                "total_allowed_page_limit": total_allowed_page_limit
            })
            return None
        except Exception as e:
           raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")

    async def MSetPromoCode(self, promoCodeStr):
        """
        Purpose : 
            Process the provided promo code and return the user registration message.

        Inputs  :  
            promoCodeStr (str): The promo code string to be processed.

        Output  :
            str: User registration message.

        Example :
            UserRegistrationMsg = await instance.MSetPromoCode("PROMO2024")
        """
        try:
            UserRegistrationMsg = ""
            referralCodeResponse = await CReferralCode.MSProcessRefferalCode(self.iUserID, promo_name=promoCodeStr)
            UserRegistrationMsg = "\n" + referralCodeResponse.get("message", "")
            return UserRegistrationMsg
        except Exception as e:
            raise HTTPException(status_code=500, detail="Oops! Your registration didn't go through. Please try again or reach out to our <NAME_EMAIL> for help.")
        