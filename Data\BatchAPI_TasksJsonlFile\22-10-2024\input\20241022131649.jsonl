{"custom_id": "4-41-2024-10-22 13:15:56", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gpt-4o-2024-08-06", "response_format": {"type": "json_schema", "json_schema": {"name": "simpolo", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerCIN": {"type": "string", "description": "Corporate Identification Number (CIN) of the seller providing the goods or services"}, "SellerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN or ) of the supplier or seller"}, "SellerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the seller"}, "SellerTel": {"type": "integer", "description": "Telephone Number of the seller providing the goods or services"}, "SellerTollFree": {"type": "integer", "description": "Toll Free Number of the seller company"}, "SellerWeb": {"type": "string", "description": "Website of the seller company"}, "SellerEmailId": {"type": "string", "description": "Email ID of the seller company"}, "SellerIRN": {"type": "string", "description": "Full Invoice Reference Number (IRN) of the issuing company"}, "Category": {"type": "string", "description": "The category or classification of the invoice, such as product type, service type, or other relevant classifications"}, "InvoiceNo": {"type": "integer", "description": "Unique identification number assigned to the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date when the invoice issued strickly convert in MMDDYYYY format from DDMMYYYY format in indian invoice"}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerState": {"type": "string", "description": "State Name of the buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "BuyerStateCode": {"type": "integer", "description": "State code of the buyer"}, "BuyerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"}, "BuyerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the buyer"}, "PlaceOfSupply": {"type": "string", "description": "State or location where the supply of goods or services is made"}, "TransporterName": {"type": "string", "description": "Name of the transporter"}, "VehicleNo": {"type": "string", "description": "Registration number of the vehicle used for transporting goods"}, "LRNo": {"type": "string", "description": "LR (Lorry Receipt) number issued by the transport company for the shipment"}, "NameOfGoods": {"type": "string", "description": "Name of the goods"}, "PORefNo": {"type": "string", "description": "Purchase Order Reference Number used for transport"}, "PODate": {"type": "integer", "description": "Purchase Order Date (In ddmmyy format)."}, "TotalQty(Box)": {"type": "integer", "description": "Total number of boxes included in the invoice"}, "TotalQty(SqMt)": {"type": "number", "description": "Total quantity in square meters included in the invoice"}, "TotalAmount": {"type": "number", "description": "Total Amount of the invoice before taxes and before any additional charges"}, "SubTotal": {"type": "number", "description": "The subtotal amount of the invoice before taxes and additional charges"}, "IGSTrateIn%": {"type": "number", "description": "IGST rate in percentage", "enum": [0, 0.1, 0.25, 3, 5, 12, 18, 28]}, "IGSTamount": {"type": "number", "description": "IGST amount"}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the total amount of the invoice"}, "GrandTotal": {"type": "number", "description": "The final total amount payable on the invoice, including all taxes and rounding adjustments"}, "GrandTotalInWords": {"type": "string", "description": "The final total amount payable on the invoice, written in words, including all taxes and rounding adjustments"}, "OutstandingAmountInclusiveOfBill": {"type": "number", "description": "The total outstanding amount in the account (Subject to realization of payment)"}, "CurrentYearBusinessWithSimpolo": {"type": "number", "description": "The total business volume or value conducted with Simpolo  (Excluding Insu., Freight, GST and this Invoice Value)"}, "OverdueOnAssignedCreditDays": {"type": "number", "description": "The number of overdue days beyond the assigned credit period, based on the company policy"}, "SalesTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the sales target set for the Growth Booster Scheme"}, "PendingTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the remaining sales target to be achieved for the Growth Booster Scheme"}, "BankName": {"type": "string", "description": "The name of the bank involved in the transaction"}, "BankA/CNo": {"type": "integer", "description": "The bank account number involved in the transaction"}, "BankIFSCCode": {"type": "string", "description": "The IFSC code of the bank branch used for the transaction"}, "BankBranch": {"type": "string", "description": "The name of the bank branch involved in the transaction"}, "AmountOfTaxSubjectToReverseCharge": {"type": "string", "description": "The total amount of tax that is subject to reverse charge"}, "ItemTable": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "The serial number of the item in the invoice"}, "Description/TradeMark": {"type": "string", "description": "Description and the trade mark or brand associated with the product or service"}, "HSNCode/Grade": {"type": "string", "description": "The HSN code or grade of the product or service"}, "Batch/Shade": {"type": "string", "description": "The batch number or shade for the product, indicating production details"}, "Qty(Box)": {"type": "integer", "description": "The quantity of the product measured in boxes"}, "Qty(SqMt)": {"type": "number", "description": "The quantity of the product measured in square meters"}, "Rate/Box": {"type": "number", "description": "The rate per box for the product"}, "BasicRate/SqFt": {"type": "number", "description": "The basic rate per square foot for the product"}, "TradeDiscUnit": {"type": "number", "description": "Trade Disc. Unit, if present (In percentage)"}, "Project/SpelDisc.": {"type": "number", "description": "Proj / Spcl Disc., if present (In percentage)"}, "SchemeDisc": {"type": "number", "description": "Scheme Disc., if present (In percentage)"}, "ProgressiveDisc": {"type": "number", "description": "Progressive Disc., if present (In percentage)"}, "LoyalyDisc": {"type": "number", "description": "Loyaly Disc., if present (In percentage)"}, "GrBoostDisc": {"type": "number", "description": "Gr. Boost Disc., if present (In percentage)"}, "CashDisc": {"type": "number", "description": "Cash Disc., if present (In percentage)"}, "NetRate/SqFt": {"type": "number", "description": "The net rate per square foot for the product after discounts"}, "Amount": {"type": "number", "description": "The total amount for the item, after applying discounts and taxes"}}, "required": ["SrNo", "Description/TradeMark", "HSNCode/Grade", "Batch/Shade", "Qty(Box)", "Qty(SqMt)", "Rate/Box", "BasicRate/SqFt", "TradeDiscUnit", "Project/SpelDisc.", "SchemeDisc", "ProgressiveDisc", "LoyalyDisc", "GrBoostDisc", "CashDisc", "NetRate/SqFt", "Amount"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced including duplicate rows"}}, "required": ["SellerName", "SellerAddress", "SellerCIN", "SellerGSTIN", "SellerPANNo", "SellerTel", "SellerTollFree", "SellerWeb", "SellerEmailId", "SellerIRN", "Category", "InvoiceNo", "InvoiceDate", "BuyerName", "BuyerAddress", "BuyerState", "BuyerStateCode", "BuyerGSTINNo", "BuyerPANNo", "PlaceOfSupply", "TransporterName", "VehicleNo", "LRNo", "NameOfGoods", "PORefNo", "PODate", "TotalQty(Box)", "TotalQty(SqMt)", "TotalAmount", "SubTotal", "IGSTrateIn%", "RoundingOff", "GrandTotal", "GrandTotalInWords", "OutstandingAmountInclusiveOfBill", "CurrentYearBusinessWithSimpolo", "OverdueOnAssignedCreditDays", "SalesTargetForGrowthBoosterSchemeAmount", "PendingTargetForGrowthBoosterSchemeAmount", "BankName", "BankA/CNo", "BankIFSCCode", "BankBranch", "AmountOfTaxSubjectToReverseCharge", "IGSTamount", "ItemTable"], "additionalProperties": false}}}, "messages": [{"role": "system", "content": "You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: \n\nStep 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. \n\nStep 2: Give output in the given structure:\n\nStep 3: Analyze UserContent completely that is given in the following JSON structure \n{\n    \"Text\" : {\n        [ActualPageNumber]: [('[ActualText]', [PageNumber], [x1], [y1], [x2], [y2]),...],...\n    }\n}\n\nTables\nHeading1, Heading2, ... HeadingN\nCell1, Cell2,..., CellN\n\nHere x1, y1, x2, y2 represents standard bounding box coordinates of actual text. \n\nStep 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. \n\nStep 5: Find relevant information from the invoice and fill out the required output JSON file. For tables, consider \"Tables\" key-value. If something is not found in the invoice then keep the respective value of the output as ''. \n\nStep 6: Check again for missing any line item in LineItemTable and strictly include all line items from all pages in the output JSON. DO NOT ASSUME ANYTHING."}, {"role": "user", "content": "Text\n{\n    \"1\": [\n        [\n            \"SIMP\",\n            1,\n            0.11239,\n            0.66838,\n            0.12683,\n            0.70682\n        ],\n        [\n            \"O VITRIFIED PRIVATE LIMITED\",\n            1,\n            0.10178,\n            0.4348,\n            0.12448,\n            0.65086\n        ],\n        [\n            \"Registered Office: Survey No. 606, Old Ghuntu Road, Tal: Morbi, Ghuntu -363642, State: GUJARAT\",\n            1,\n            0.12409,\n            0.3945,\n            0.15063,\n            0.75046\n        ],\n        [\n            \"CIN: U26914GJ2007PTC051766 GSTIN: 24**********IZU PAN NO: **********\",\n            1,\n            0.14445,\n            0.41096,\n            0.1675,\n            0.7318\n        ],\n        [\n            \"Tel.: +91 72289 22222 Toll Free: 1800 233 2223 Website: www.simpolo.net Email Id: <EMAIL>\",\n            1,\n            0.16325,\n            0.38527,\n            0.18867,\n            0.75852\n        ],\n        [\n            \"Simpolo\",\n            1,\n            0.1829,\n            0.83903,\n            0.23452,\n            0.97925\n        ],\n        [\n            \"IRN : 3006655d488f543b7a7eb8c0d81702a5c377f5a71e3780cdce8fc5b2b58de7de\",\n            1,\n            0.18446,\n            0.42625,\n            0.20574,\n            0.71484\n        ],\n        [\n            \"Tiles & Bathware\",\n            1,\n            0.23642,\n            0.85895,\n            0.25269,\n            0.95652\n        ],\n        [\n            \"TAX INVOICE\",\n            1,\n            0.22428,\n            0.59435,\n            0.24147,\n            0.68114\n        ],\n        [\n            \"CATEGORY: ENIGMA DIAMOND\",\n            1,\n            0.26569,\n            0.74409,\n            0.28846,\n            0.95674\n        ],\n        [\n            \"Invoice No : 2412009190\",\n            1,\n            0.2578,\n            0.57018,\n            0.27733,\n            0.70578\n        ],\n        [\n            \"Date / Time: 14.09.2024/14:09:10\",\n            1,\n            0.24919,\n            0.33183,\n            0.26763,\n            0.49296\n        ],\n        [\n            \"Name & Address of Receiver\",\n            1,\n            0.29294,\n            0.81661,\n            0.31306,\n            0.98845\n        ],\n        [\n            \"Details of Consignee\",\n            1,\n            0.27957,\n            0.5136,\n            0.29898,\n            0.63407\n        ],\n        [\n            \"Vhd Distributors Llp\",\n            1,\n            0.32108,\n            0.8862,\n            0.33788,\n            0.98749\n        ],\n        [\n            \"Vhd Distributors Llp\",\n            1,\n            0.3052,\n            0.53234,\n            0.32178,\n            0.63344\n        ],\n        [\n            \"201,Princess Empire, 12,Race Course Road,\",\n            1,\n            0.33822,\n            0.8048,\n            0.35749,\n            0.9869\n        ],\n        [\n            \"201, Princess Empire, 12,Race Course Road,\",\n            1,\n            0.3222,\n            0.45217,\n            0.34155,\n            0.63322\n        ],\n        [\n            \"INDORE - 452001 MADHYA PRADESH State Code : 23\",\n            1,\n            0.37239,\n            0.71148,\n            0.39508,\n            0.98629\n        ],\n        [\n            \"INDORE 452001 MADHYA PRADESH State Code: 23\",\n            1,\n            0.35663,\n            0.35899,\n            0.37913,\n            0.63303\n        ],\n        [\n            \"GSTIN No: 23**********1ZX - PAN :**********\",\n            1,\n            0.40924,\n            0.75227,\n            0.42917,\n            0.98475\n        ],\n        [\n            \"Transporter Name:Maa Narmada Logistics\",\n            1,\n            0.39704,\n            0.50914,\n            0.41871,\n            0.70221\n        ],\n        [\n            \"Name of Goods:\",\n            1,\n            0.38969,\n            0.33019,\n            0.40264,\n            0.39981\n        ],\n        [\n            \"PO Ref.No :\",\n            1,\n            0.37886,\n            0.11222,\n            0.39254,\n            0.16826\n        ],\n        [\n            \"Vehicle No:MH18BA6851 L.R. No:485\",\n            1,\n            0.41686,\n            0.51646,\n            0.43581,\n            0.70136\n        ],\n        [\n            \"Ceramic Wall/Floor/Vitrified/Other Tiles & Bathware\",\n            1,\n            0.40313,\n            0.18186,\n            0.42226,\n            0.39944\n        ],\n        [\n            \"PO Date\",\n            1,\n            0.39928,\n            0.12842,\n            0.41208,\n            0.16763\n        ],\n        [\n            \":\",\n            1,\n            0.40137,\n            0.11164,\n            0.40997,\n            0.11433\n        ],\n        [\n            \"Place of Supply : MADHYA PRADESH\",\n            1,\n            0.43201,\n            0.82124,\n            0.45061,\n            0.9843\n        ],\n        [\n            \"State Bank of India A/C No. ***********, IFSC Code SBIN0016390, Branch Lalpar (Morvi)\",\n            1,\n            0.59588,\n            0.57567,\n            0.62403,\n            0.97968\n        ],\n        [\n            \"Amount of Tax subject to reverse charge No\",\n            1,\n            0.62365,\n            0.77285,\n            0.64516,\n            0.97918\n        ],\n        [\n            \"Certified that the particular given are true & correct.\",\n            1,\n            0.64235,\n            0.74441,\n            0.66492,\n            0.97856\n        ],\n        [\n            \"Loyalty and Growth Booster Discount are applicable if there is no overdues.\",\n            1,\n            0.65663,\n            0.6328,\n            0.68534,\n            0.97808\n        ],\n        [\n            \"* Marked scheme discount are from Growth lifting scheme.\",\n            1,\n            0.67993,\n            0.71098,\n            0.70261,\n            0.97705\n        ],\n        [\n            \"Remark :\",\n            1,\n            0.71087,\n            0.93959,\n            0.722,\n            0.97697\n        ],\n        [\n            \"VIY\",\n            1,\n            0.68404,\n            0.61029,\n            0.733,\n            0.67505\n        ],\n        [\n            \"Amount (Words) :RUPEE EIGHTY-ONE THOUSAND FOUR HUNDRED SEVENTY-EIGHT ONLY\",\n            1,\n            0.77853,\n            0.50466,\n            0.81161,\n            0.97462\n        ],\n        [\n            \"Outstanding in your account inclusive of the bill is (Subject to realization of payment)\",\n            1,\n            0.82859,\n            0.5857,\n            0.85855,\n            0.97336\n        ],\n        [\n            \"81,478.00\",\n            1,\n            0.82078,\n            0.39815,\n            0.83358,\n            0.43917\n        ],\n        [\n            \"Overdue on Assigned Credit days as per company policy\",\n            1,\n            0.85966,\n            0.74482,\n            0.88107,\n            0.97271\n        ],\n        [\n            \"Sales Target For Growth Booster Scheme\",\n            1,\n            0.88378,\n            0.78478,\n            0.90439,\n            0.97187\n        ],\n        [\n            \"*********.38\",\n            1,\n            0.86628,\n            0.38142,\n            0.87829,\n            0.44007\n        ],\n        [\n            \"( Page: 1/2 )\",\n            1,\n            0.95789,\n            0.09283,\n            0.9731,\n            0.15258\n        ]\n    ]\n}\n\nTable-1\nSr No,Description Trade Mark,HSN Code Grade,Batch / Shade,Qty(Box),Qty(Sq Mt),Rate/Box,Basic Rate / Sq Ft,Trade Disc. Unit,Proj Spcl Disc.,Scheme Disc,Progressive Disc.,Loyaly Disc.,Gr.Boost. Disc.,Cash Disc.,Net Rate / Sqft,Amount\r\n1,RDK RD 45 GV400X400X12 ROCK DECK,6907.21.00 PREMIUM,29H4PRES26,220,175.975,254.00,29.50,,,,\"4,0%\",2.0%,,4.0 %,26.64,\"50,468.04\"\r\n2,RDK RD 44 GV400X400X12 ROCK DECK,6907.21.00 PREMIUM,10E4PRES20,81,64.791,254.00,29.50,,,,\"4,0 %\",\"2,0%\",,\"4,0 %\",26.64,\"18,581.42\"\r\n,,,Total,301,240.77,,,,,,,,,,,\"69,049.46\"\r\n\n\nTable-2\nSub Total,\"69,049.46\"\r\nIGST @18.00 %,\"12,428.91\"\r\nRounding Off,0.37\r\n,\r\nGrand Total,\"Q 81,478.00\"\r\n\n\nTable-3\nCurrent Year Business with Simpolo,55480972.82\r\n\"(Excluding Insu., Freight, GST and this Invoice Value)\",\r\nPending Target For Growth Booster Scheme,75392664.56\r\n\n\n"}]}}
{"custom_id": "4-42-2024-10-22 13:16:49", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gpt-4o-2024-08-06", "response_format": {"type": "json_schema", "json_schema": {"name": "simpolo", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerCIN": {"type": "string", "description": "Corporate Identification Number (CIN) of the seller providing the goods or services"}, "SellerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN or ) of the supplier or seller"}, "SellerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the seller"}, "SellerTel": {"type": "integer", "description": "Telephone Number of the seller providing the goods or services"}, "SellerTollFree": {"type": "integer", "description": "Toll Free Number of the seller company"}, "SellerWeb": {"type": "string", "description": "Website of the seller company"}, "SellerEmailId": {"type": "string", "description": "Email ID of the seller company"}, "SellerIRN": {"type": "string", "description": "Full Invoice Reference Number (IRN) of the issuing company"}, "Category": {"type": "string", "description": "The category or classification of the invoice, such as product type, service type, or other relevant classifications"}, "InvoiceNo": {"type": "integer", "description": "Unique identification number assigned to the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date when the invoice issued strickly convert in MMDDYYYY format from DDMMYYYY format in indian invoice"}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerState": {"type": "string", "description": "State Name of the buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "Meghalaya", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "Tripura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "BuyerStateCode": {"type": "integer", "description": "State code of the buyer"}, "BuyerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"}, "BuyerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the buyer"}, "PlaceOfSupply": {"type": "string", "description": "State or location where the supply of goods or services is made"}, "TransporterName": {"type": "string", "description": "Name of the transporter"}, "VehicleNo": {"type": "string", "description": "Registration number of the vehicle used for transporting goods"}, "LRNo": {"type": "string", "description": "LR (Lorry Receipt) number issued by the transport company for the shipment"}, "NameOfGoods": {"type": "string", "description": "Name of the goods"}, "PORefNo": {"type": "string", "description": "Purchase Order Reference Number used for transport"}, "PODate": {"type": "integer", "description": "Purchase Order Date (In ddmmyy format)."}, "TotalQty(Box)": {"type": "integer", "description": "Total number of boxes included in the invoice"}, "TotalQty(SqMt)": {"type": "number", "description": "Total quantity in square meters included in the invoice"}, "TotalAmount": {"type": "number", "description": "Total Amount of the invoice before taxes and before any additional charges"}, "SubTotal": {"type": "number", "description": "The subtotal amount of the invoice before taxes and additional charges"}, "IGSTrateIn%": {"type": "number", "description": "IGST rate in percentage", "enum": [0, 0.1, 0.25, 3, 5, 12, 18, 28]}, "IGSTamount": {"type": "number", "description": "IGST amount"}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the total amount of the invoice"}, "GrandTotal": {"type": "number", "description": "The final total amount payable on the invoice, including all taxes and rounding adjustments"}, "GrandTotalInWords": {"type": "string", "description": "The final total amount payable on the invoice, written in words, including all taxes and rounding adjustments"}, "OutstandingAmountInclusiveOfBill": {"type": "number", "description": "The total outstanding amount in the account (Subject to realization of payment)"}, "CurrentYearBusinessWithSimpolo": {"type": "number", "description": "The total business volume or value conducted with Simpolo  (Excluding Insu., Freight, GST and this Invoice Value)"}, "OverdueOnAssignedCreditDays": {"type": "number", "description": "The number of overdue days beyond the assigned credit period, based on the company policy"}, "SalesTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the sales target set for the Growth Booster Scheme"}, "PendingTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the remaining sales target to be achieved for the Growth Booster Scheme"}, "BankName": {"type": "string", "description": "The name of the bank involved in the transaction"}, "BankA/CNo": {"type": "integer", "description": "The bank account number involved in the transaction"}, "BankIFSCCode": {"type": "string", "description": "The IFSC code of the bank branch used for the transaction"}, "BankBranch": {"type": "string", "description": "The name of the bank branch involved in the transaction"}, "AmountOfTaxSubjectToReverseCharge": {"type": "string", "description": "The total amount of tax that is subject to reverse charge"}, "ItemTable": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "The serial number of the item in the invoice"}, "Description/TradeMark": {"type": "string", "description": "Description and the trade mark or brand associated with the product or service"}, "HSNCode/Grade": {"type": "string", "description": "The HSN code or grade of the product or service"}, "Batch/Shade": {"type": "string", "description": "The batch number or shade for the product, indicating production details"}, "Qty(Box)": {"type": "integer", "description": "The quantity of the product measured in boxes"}, "Qty(SqMt)": {"type": "number", "description": "The quantity of the product measured in square meters"}, "Rate/Box": {"type": "number", "description": "The rate per box for the product"}, "BasicRate/SqFt": {"type": "number", "description": "The basic rate per square foot for the product"}, "TradeDiscUnit": {"type": "number", "description": "Trade Disc. Unit, if present (In percentage)"}, "Project/SpelDisc.": {"type": "number", "description": "Proj / Spcl Disc., if present (In percentage)"}, "SchemeDisc": {"type": "number", "description": "Scheme Disc., if present (In percentage)"}, "ProgressiveDisc": {"type": "number", "description": "Progressive Disc., if present (In percentage)"}, "LoyalyDisc": {"type": "number", "description": "Loyaly Disc., if present (In percentage)"}, "GrBoostDisc": {"type": "number", "description": "Gr. Boost Disc., if present (In percentage)"}, "CashDisc": {"type": "number", "description": "Cash Disc., if present (In percentage)"}, "NetRate/SqFt": {"type": "number", "description": "The net rate per square foot for the product after discounts"}, "Amount": {"type": "number", "description": "The total amount for the item, after applying discounts and taxes"}}, "required": ["SrNo", "Description/TradeMark", "HSNCode/Grade", "Batch/Shade", "Qty(Box)", "Qty(SqMt)", "Rate/Box", "BasicRate/SqFt", "TradeDiscUnit", "Project/SpelDisc.", "SchemeDisc", "ProgressiveDisc", "LoyalyDisc", "GrBoostDisc", "CashDisc", "NetRate/SqFt", "Amount"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced including duplicate rows"}}, "required": ["SellerName", "SellerAddress", "SellerCIN", "SellerGSTIN", "SellerPANNo", "SellerTel", "SellerTollFree", "SellerWeb", "SellerEmailId", "SellerIRN", "Category", "InvoiceNo", "InvoiceDate", "BuyerName", "BuyerAddress", "BuyerState", "BuyerStateCode", "BuyerGSTINNo", "BuyerPANNo", "PlaceOfSupply", "TransporterName", "VehicleNo", "LRNo", "NameOfGoods", "PORefNo", "PODate", "TotalQty(Box)", "TotalQty(SqMt)", "TotalAmount", "SubTotal", "IGSTrateIn%", "RoundingOff", "GrandTotal", "GrandTotalInWords", "OutstandingAmountInclusiveOfBill", "CurrentYearBusinessWithSimpolo", "OverdueOnAssignedCreditDays", "SalesTargetForGrowthBoosterSchemeAmount", "PendingTargetForGrowthBoosterSchemeAmount", "BankName", "BankA/CNo", "BankIFSCCode", "BankBranch", "AmountOfTaxSubjectToReverseCharge", "IGSTamount", "ItemTable"], "additionalProperties": false}}}, "messages": [{"role": "system", "content": "You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: \n\nStep 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. \n\nStep 2: Give output in the given structure:\n\nStep 3: Analyze UserContent completely that is given in the following JSON structure \n{\n    \"Text\" : {\n        [ActualPageNumber]: [('[ActualText]', [PageNumber], [x1], [y1], [x2], [y2]),...],...\n    }\n}\n\nTables\nHeading1, Heading2, ... HeadingN\nCell1, Cell2,..., CellN\n\nHere x1, y1, x2, y2 represents standard bounding box coordinates of actual text. \n\nStep 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. \n\nStep 5: Find relevant information from the invoice and fill out the required output JSON file. For tables, consider \"Tables\" key-value. If something is not found in the invoice then keep the respective value of the output as ''. \n\nStep 6: Check again for missing any line item in LineItemTable and strictly include all line items from all pages in the output JSON. DO NOT ASSUME ANYTHING."}, {"role": "user", "content": "Text\n{\n    \"1\": [\n        [\n            \"SIMPOLU VEIKIFELU PRIVAIL LIMITED\",\n            1,\n            0.09116,\n            0.43265,\n            0.10367,\n            0.70852\n        ],\n        [\n            \"Registered Office: Survey No. 606, Old Ghuntu Road, Tal: Morbi, Ghuntu -363642, State: GUJARAT\",\n            1,\n            0.11068,\n            0.38677,\n            0.12579,\n            0.75402\n        ],\n        [\n            \"CIN: U26914GJ200\",\n            1,\n            0.13273,\n            0.66207,\n            0.14307,\n            0.73495\n        ],\n        [\n            \"051766 GSTIN: 24**********IZU PAN NO: **********\",\n            1,\n            0.13049,\n            0.40513,\n            0.14198,\n            0.64267\n        ],\n        [\n            \"Tel.: +91 72289 22222 Toll Free: 1800 233 2223 Website: www.simpolo.net Email Id: <EMAIL>\",\n            1,\n            0.14971,\n            0.37747,\n            0.16388,\n            0.76301\n        ],\n        [\n            \"Simpolo\",\n            1,\n            0.15327,\n            0.84444,\n            0.20128,\n            0.98995\n        ],\n        [\n            \"IRN : 32991388726ab60f775a6883d2c076d4975a57d3030c0dfc30f0112b20cee386\",\n            1,\n            0.1699,\n            0.42247,\n            0.18183,\n            0.71973\n        ],\n        [\n            \"Tiles & Bathware\",\n            1,\n            0.20626,\n            0.8674,\n            0.2204,\n            0.96798\n        ],\n        [\n            \"TAX INVOICE\",\n            1,\n            0.20356,\n            0.59545,\n            0.21816,\n            0.6848\n        ],\n        [\n            \"CATEGORY : ENIGMA DIAMOND\",\n            1,\n            0.23995,\n            0.75079,\n            0.25565,\n            0.96875\n        ],\n        [\n            \"Invoice No : 2412008444\",\n            1,\n            0.23787,\n            0.57209,\n            0.25324,\n            0.71054\n        ],\n        [\n            \"Date / Time: 01.09.2024/23:53:41\",\n            1,\n            0.23776,\n            0.33217,\n            0.25113,\n            0.49352\n        ],\n        [\n            \"GSTIN No: 23**********1ZX - PAN :**********\",\n            1,\n            0.38354,\n            0.76019,\n            0.39676,\n            1.0\n        ],\n        [\n            \"Transporter Name:AMARDEEP TRANSPORT COMPANY\",\n            1,\n            0.37875,\n            0.42965,\n            0.39549,\n            0.70976\n        ],\n        [\n            \"Name of Goods:\",\n            1,\n            0.37878,\n            0.33322,\n            0.38935,\n            0.40214\n        ],\n        [\n            \"PO Ref.No :\",\n            1,\n            0.37513,\n            0.11368,\n            0.38746,\n            0.16884\n        ],\n        [\n            \"Place of Supply : MADHYA PRADESH\",\n            1,\n            0.40434,\n            0.83166,\n            0.4183,\n            0.9995\n        ],\n        [\n            \"Vehicle No:MP04HE8212 L.R. No:622\",\n            1,\n            0.39933,\n            0.52413,\n            0.41245,\n            0.70911\n        ],\n        [\n            \"Ceramic Wall/Floor/Vitrified/Other Tiles & Bathware\",\n            1,\n            0.39691,\n            0.18307,\n            0.40907,\n            0.40196\n        ],\n        [\n            \"PO Date :\",\n            1,\n            0.39542,\n            0.11356,\n            0.4069,\n            0.16856\n        ],\n        [\n            \"State Bank of India A/C No. ***********, IFSC Code SBIN0016390, Branch Lalpar (Morvi)\",\n            1,\n            0.57743,\n            0.58821,\n            0.59313,\n            0.9986\n        ],\n        [\n            \"Amount of Tax subject to reverse charge - No\",\n            1,\n            0.5984,\n            0.78665,\n            0.61373,\n            0.99841\n        ],\n        [\n            \"Certified that the particular given are true & correct.\",\n            1,\n            0.61769,\n            0.75864,\n            0.63387,\n            0.99838\n        ],\n        [\n            \"Loyalty and Growth Booster Discount are applicable if there is no overdues\",\n            1,\n            0.63577,\n            0.64709,\n            0.65375,\n            0.99824\n        ],\n        [\n            \"* Marked scheme discount are from Growth lifting scheme.\",\n            1,\n            0.65595,\n            0.72359,\n            0.6725,\n            0.99752\n        ],\n        [\n            \"Remark\",\n            1,\n            0.68024,\n            0.9646,\n            0.69046,\n            0.99796\n        ],\n        [\n            \"Amount (Words) :RUPEE ONE LAKH SIXTY-ONE THOUSAND NINE HUNDRED FIFTY-TWO ONLY\",\n            1,\n            0.76132,\n            0.49643,\n            0.78051,\n            0.99699\n        ],\n        [\n            \"Outstanding in your account inclusive of the bill is (Subject to realization of payment)\",\n            1,\n            0.80947,\n            0.60198,\n            0.82788,\n            0.99646\n        ],\n        [\n            \"161,952.00\",\n            1,\n            0.80834,\n            0.40515,\n            0.82,\n            0.45007\n        ],\n        [\n            \"Overdue on Assigned Credit days as per company policy\",\n            1,\n            0.83543,\n            0.76441,\n            0.85044,\n            0.9964\n        ],\n        [\n            \"Sales Target For Growth Booster Scheme\",\n            1,\n            0.8581,\n            0.80415,\n            0.87376,\n            0.99613\n        ],\n        [\n            \"*********.38\",\n            1,\n            0.85468,\n            0.39448,\n            0.86523,\n            0.45259\n        ],\n        [\n            \"ORIGNAL FOR RECEIPIENT\",\n            1,\n            0.96625,\n            0.8663,\n            0.97771,\n            0.99519\n        ],\n        [\n            \"(Page: 1/2 )\",\n            1,\n            0.95759,\n            0.10157,\n            0.97106,\n            0.16533\n        ]\n    ]\n}\n\nTable-1\nName & Address of Receiver,Details of Consignee\r\n\"Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE 452001 MADHYA PRADESH State Code : 23\",\"Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE -452001 MADHYA PRADESH State Code: 23\"\r\n\n\nTable-2\nSr No,Description Trade Mark,HSN Code Grade,Batch Shade,Qty(Box),Qty(Sq Mt),Rate / Box,Basic Rate / Sq Ft,Trade Disc. Unit,Proj/Spcl Disc.,Scheme Disc.,Progressive Disc.,Loyaly Disc.,Gr.Boost. Disc.,Cash Disc.,Net Rate / Sqft,Amount\r\n1,FB DESERT SAHARA GV600X600X8.5,6907.21.00 PREMIUM,02F4PRES04,196,282.237,589.00,38.00,,,,4.0%,2.0%,,4.0%,34.32,\"104,265.33\"\r\n2,FB DESERT SAHARA GV600X600X8.5,6907.21.00 PREMIUM,29F4PRES03,62,89.279,589.00,38.00,,,,4.0%,2.0%,,4.0%,34.32,\"32,981.88\"\r\n,,,Total,258,371.52,,,,,,,,,,,\"137,247.21\"\r\n\n\nTable-3\nSub Total,\"137,247.21\"\r\nIGST @18.00 %,\"24,704.50\"\r\nRounding Off,0.29\r\n,\r\nGrand Total,\"161,952.00\"\r\n\n\nTable-4\nCurrent Year Business with Simpolo,50973247.38\r\n\"(Excluding Insu., Freight, GST and this Invoice Value)\",\r\nPending Target For Growth Booster Scheme,79900390.00\r\n\n\n"}]}}
