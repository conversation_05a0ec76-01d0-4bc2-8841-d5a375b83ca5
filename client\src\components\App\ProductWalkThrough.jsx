import PlayIcon from '../../assets/WalkThroughPlay.svg'
const WatchVideo = () => {
    const handleClick = () => {
        window.open('https://youtu.be/W0STKYyKa8Y?si=LzQ3utDZHUbXIjgz', '_blank');
    };

    return (
        <div 
            className='shadow-lg xl:h-[4rem] xl:w-[15.9rem] 2xl:h-[4rem] 2xl:w-[10.9rem] flex items-center p-[10px] rounded-full w-[200px] sticky top-[10%] mr-[1%] bg-[#003654] text-white'
            style={{ backgroundColor: "#ffffff", color: "#ffffff" , cursor: "pointer" }}
            onClick={handleClick}
        >
            <img src={PlayIcon} />
            <p style={{
                position: "absolute",
                left: "37.0%",
                right: "25.0%",
                fontFamily: "'Inter', sans-serif",
                fontStyle: "normal",
                fontWeight: 500,
                fontSize: "14.4042px",
                lineHeight: "19px",
                display: "flex",
                alignItems: "center",
                color: "#003654",
                border: "1.1003px solid #FFFFFF",
                backgroundColor: "#ffffff"
            }}
            >Product Walkthrough</p>
        </div>
    );
};

export default WatchVideo;
