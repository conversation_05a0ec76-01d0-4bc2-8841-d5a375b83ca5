# New Vendor Implementation Guide for Parag Purchase Without Inventory

This document outlines the process for integrating a new vendor into the Parag Purchase Without Inventory system. Follow the steps below to ensure accurate setup and configuration.

## Data Requirements
The following files are required for the implementation:
- **Master Ledger**: `MasterLedger.txt` file containing the company's ledger details.
- **Invoice PDFs**: PDF files of the vendor's invoices.
- **Tally Entries PDFs**: PDF files of the Tally entries of the same invoices for the vendor.
- **Tally XML Files**: XML files containing Tally entry data for the vendor.
- **NOTE**: Make Sure that File Names are same for both PDF files namely tally entries and invoice. 
- **Repo Links**: XMLAutomation_generalSchema - http://************:3000/Satyam_Tank/XMLAutomation_generalSchema.git, XML_Automation - http://************:3000/Satyam_Tank/XML_Automation.git

## Implementation Steps

### Step 1: Create Debit Config File in XMLAutomation_generalSchema
1. Create two folders in the `data/input` directory:
   - A folder named after the vendor (e.g., `Vendor<PERSON><PERSON>`) to store the PDF invoices.
   - A folder named `VendorNameTally` to store the PDF Tally entries.
2. Place the `MasterLedger.txt` file in the `config` folder.
3. In the main.py script of `XMLAutomation_generalSchema`, provide the following inputs and execute the code:
   - Vendor name
   - Path to the `MasterLedger.txt` file
4. The script will generate three output files. Select the file ending with `Debit_MappedDebitConfig.json`.
5. Manually add the SGST and CGST ledgers to the `Debit_MappedDebitConfig.json` file.
6. Copy the updated `Debit_MappedDebitConfig.json` file and the GPT Response Format to the following directory:
   ```
   Accuvelocity/Data/Customer/CompanyName/VendorName
   ```

### Step 2: Create Vendor Config File in XML_Automation
1. Place the `MasterLedger.txt` file in the `Data/MasterLedger` folder.
2. Create a vendor-specific subfolder inside `Data/XMLDir` and place the Tally XML files in it.
3. In the main script of `XML_Automation`, provide the following inputs and execute the code:
   - Vendor name
   - Path to the `MasterLedger.txt` file
   - Path to the folder containing all XML files
   - Path to a single XML file
4. The script will generate an output file starting with the vendor name in the `Data/ConfigFiles` folder.
5. Copy this file to the same `Accuvelocity/Data/Customer/CompanyName/VendorName` directory used in Step 1.

### Step 3: Create Vendor Class in Accuvelocity
1. Duplicate an existing vendor class in Accuvelocity and rename it to match the current vendor’s name.
2. Update all references to the old class name with the new vendor’s class name throughout the codebase.
3. Implement a narration method specific to the current vendor.

## Notes
- Ensure all file paths are accurate to avoid errors during execution.
- Verify that the SGST and CGST ledgers are correctly added to the `Debit_MappedDebitConfig.json` file.
- Test the new vendor class in Accuvelocity to confirm proper functionality.

For any questions or clarifications, please contact the system administrator.