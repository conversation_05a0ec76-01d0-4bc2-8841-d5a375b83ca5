#!/bin/bash

echo "Clean up before creating virtual environment..."

rm -rf 5_Env

echo "Successfully cleaned up..."

echo "Creating virtual environment..."

python -m venv 5_Env

echo "Virtual environment created successfully."

echo "Activating environment..."

source 5_Env/bin/activate

echo "Environment activated."

cd ..

echo "Change to parent directory..."

pip install -r '/home/<USER>/AccuVelocity/resource/requirements.txt'

echo "Project-required modules installed successfully."

echo "Environment is ready to be used for GPTInvoiceExtraction."

exit 0
