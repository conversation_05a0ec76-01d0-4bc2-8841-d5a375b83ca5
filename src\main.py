import os

from dotenv import load_dotenv
from os.path import join, dirname

# Load environment variables
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")
load_dotenv(dotenv_path)

disk_letter = os.getenv("DISK_LETTER", "H")

import sys
from src.utilities.PathHandler import CPathUtility, dictProjectPaths
try:
    dictStatusReport = CPathUtility.MSValidateAndCreatePath(dictProjectPaths, disk_letter)
except Exception as E:
    print(E)
    sys.exit()
import os
from fastapi import FastAPI
from config.constants import Constants
from src.utilities.helperFunc import CORSHelper
from src.utilities.SetupController import setupapp
from src.Routes.auth import auth
from src.Routes.DocumentData import DocumentRouter
from src.Routes.GPTResponse import GPTResponseRouter
from src.Routes.UserAPIUsage import UserAPIUsageRouter
from src.Routes.log_Routes import LogsRouter
from src.Routes.Role_Routes import RoleRouter
from src.Routes.Tally_Routes import TallyRouter
from src.Routes.Vendor_Routes import VendorRouter
from src.Routes.prompt_routes import prompt_router
from src.Routes.PromoCode import PromoCodeRouter
from src.Routes.StripeWebhooks import PaymentStripeWebhooks
from src.Routes.StripePayment import PaymentAppRouter
from src.Routes.Contact_us import contactus
from src.Routes.BugReport import bugs
from src.Routes.GPTBatchAPIRoute import batch_api
from src.Routes.IndianInvRoute import IndianInvoiceRoute
from src.Routes.AdvertiseImageRoute import AdvertisementApi
from src.Controllers.AVRequestDetailController import AVRequetDetailTableRouter  


# Initialize the FastAPI application for other routes on port 8000
app_main = FastAPI(
    title="GptExt app - Main",
    version=Constants.APP_VERSION,
    # docs_url=None,                # For blocking the auto documentation of fast api
    # redoc_url=None,               # For blocking the auto documentation of fast api
    # openapi_url=None              # For blocking the auto documentation of fast api
)
CORSHelper.setup_cors(app_main)
app_main.include_router(auth)
app_main.include_router(DocumentRouter)
app_main.include_router(UserAPIUsageRouter)
app_main.include_router(LogsRouter)
app_main.include_router(GPTResponseRouter)
app_main.include_router(RoleRouter)
app_main.include_router(TallyRouter)
app_main.include_router(VendorRouter)
app_main.include_router(prompt_router)
app_main.include_router(PromoCodeRouter)
app_main.include_router(PaymentStripeWebhooks)
app_main.include_router(PaymentAppRouter)
app_main.include_router(contactus)
app_main.include_router(bugs)
app_main.include_router(batch_api)
app_main.include_router(IndianInvoiceRoute)
app_main.include_router(setupapp)
app_main.include_router(AdvertisementApi)
app_main.include_router(AVRequetDetailTableRouter)

@app_main.get("/")
def read_root():
    return {"Hello": "World"}

# Turn off feature - reset page limit left 0
# @app_main.on_event("startup")
# async def startup_event():
#     """
#     Initialize database and scheduler on startup.
#     """
#     from config.db_config import engine  # Importing here to avoid circular import
#     from src.utilities.startupUtils import CStartUp
    
#     CStartUp.MSStartScheduler(engine)

if __name__ == "__main__":

    # Production Config
    # uvicorn src.main:app_main --host 0.0.0.0 --port 8010 --workers 3 ,  
    # uvicorn src.doc_exec_main:app_gpt --host 0.0.0.0 --port 9121 --workers 5 
    
    # Ubuntu Config
    # uvicorn src.main:app_main --host 0.0.0.0 --port 8011 --workers 3 ,  
    # uvicorn src.doc_exec_main:app_gpt --host 0.0.0.0 --port 9122 --workers 2 

    
    # Production Config
    # uvicorn src.main:app_main --host 0.0.0.0 --port 8034 --workers 2
    # uvicorn src.doc_exec_main:app_gpt --host 0.0.0.0 --port 9001 --workers 10

    # Tally Server 2 - Production or Development Config
    # uvicorn src.main:app_main --host 0.0.0.0 --port 8024 --workers 2 
    # uvicorn src.doc_exec_main:app_gpt --host 0.0.0.0 --port 9002 --workers 2 

    import uvicorn
    # loop = ProactorEventLoop()
    # asyncio.set_event_loop(loop)
    uvicorn.run(app_main, host="0.0.0.0", port=8025)
