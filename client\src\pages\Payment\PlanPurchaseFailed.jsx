import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import logo from '../../assets/logo.svg'; // Ensure the logo is correctly located
import { AiOutlineCloseCircle } from 'react-icons/ai'; // Import error icon from react-icons

export default function PlanPurchaseFailed() {
    const navigate = useNavigate();
    const location = useLocation();
    const [retryUrl, setRetryUrl] = useState('/UpgradePlan'); // Default URL

    useEffect(() => {
        // Redirect to /Profile after 10 seconds
        const timer = setTimeout(() => {
            navigate('/Profile', { replace: true });
        }, 10000);

        // Update retry URL based on query parameters
        const params = new URLSearchParams(location.search);
        const topUp = params.get('top_up');
        const subscription = params.get('subscription');

        if (topUp) {
            setRetryUrl('/AddOnPages');
        } else if (subscription) {
            setRetryUrl('/UpgradePlan');
        }

        // Cleanup timer on component unmount
        return () => clearTimeout(timer);
    }, [location.search, navigate]);

    const handleRetry = () => {
        navigate(retryUrl); // Navigate to the appropriate URL based on query parameters
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#ffff]">
            <div className="flex justify-center">
                <img src={logo} alt="AccuVelocity" className="h-12 w-auto mb-6" />
            </div>
            <div className="text-center">
                <div className="flex justify-center mb-6 my-10">
                    <AiOutlineCloseCircle size={100} color="#dc3545" />
                </div>
                <h3 className="text-xl font-semibold mb-2 mt-12">Plan Purchase Failed</h3>
                <p className="text-gray-400 mb-8">
                    We&apos;re sorry, but there was an issue processing your purchase. Please try again or contact support.
                </p>
                <button 
                    onClick={handleRetry} 
                    className="py-2 px-8 mt-10 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#dc3545] hover:bg-[#c82333] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#dc3545]"
                >
                    Retry Purchase
                </button>
            </div>
        </div>
    );
}
