{"type": "json_schema", "json_schema": {"name": "kohler", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "SellerAddress": {"type": "string", "description": "Address of the seller issuing the invoice"}, "SellerCity": {"type": "string", "description": "Name of city from address  of the seller issuing the invoice"}, "SellerState": {"type": "string", "description": "Name of state from address of the seller issuing the invoice"}, "SellerCountry": {"type": "string", "description": "Name of country from address of the seller issuing the invoice"}, "SellerPincode": {"type": "integer", "description": "Pincode of the seller issuing the invoice"}, "SellerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the seller"}, "BillToPartyName": {"type": "string", "description": "Name of the buyer to whom the invoice is billed"}, "BillToCode": {"type": "string", "description": "Code of the buyer"}, "BillToPartyAddress": {"type": "string", "description": "Address of the buyer to whom the invoice is billed"}, "BillToPartyGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"}, "PlaceOfSupply": {"type": "string", "description": "State or location where the supply of goods or services takes place"}, "ShipToPartyName": {"type": "string", "description": "Name of the Party to whom goods are shipped"}, "ShipToCode": {"type": "string", "description": "Code of the buyer"}, "ShipToPartyAddress": {"type": "string", "description": "Address of the buyer to whom goods are shipped"}, "ShipToPartyMob.No": {"type": "integer", "description": "Mobile number of the buyer to whom goods are shipped"}, "ShipToPartyTel.No": {"type": "integer", "description": "Telephone number of the buyer to whom goods are shipped"}, "ShipToPartyGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the Party to whom goods are shipped"}, "PlaceOfDelivery": {"type": "string", "description": "Location where goods are delivered"}, "InvoiceNo": {"type": "string", "description": "Unique identification number assigned to the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyyyy format) on which the invoice was issued."}, "SONo": {"type": "integer", "description": "Sales Order number linked to the invoice"}, "SODate": {"type": "integer", "description": "Date of the Sales Order linked to the invoice"}, "PONo": {"type": "string", "description": "Purchase Order number linked to the invoice"}, "PODate": {"type": "integer", "description": "Date of the Purchase Order linked to the invoice"}, "Transporter": {"type": "string", "description": "Name of the transporter handling the delivery of goods"}, "LR/DocketNo": {"type": "integer", "description": "Logistics Receipt or Docket number for tracking shipment"}, "VehicleNo": {"type": "string", "description": "Vehicle number used for transporting goods"}, "QuotationNo": {"type": "string", "description": "Quotation number linked to the transaction"}, "TotalQTY": {"type": "integer", "description": "Total quantity of goods or services listed in the invoice"}, "TotalFreight": {"type": "number", "description": "Total freight charges applied to the invoice"}, "TotalInsurance": {"type": "number", "description": "Total insurance charges applied to the invoice"}, "TotalPacking&ForwardingCharges": {"type": "number", "description": "Total packing and forwarding charges applied to the invoice"}, "TotalCentralTaxRate&Amount": {"type": "number", "description": "Total Central Goods and Services Tax (CGST) rate and amount applied"}, "TotalStateTaxRate&Amount": {"type": "number", "description": "Total State Goods and Services Tax (SGST) rate and amount applied"}, "TotalInvoiceValue": {"type": "number", "description": "Total value of the invoice including taxes, charges, and discounts"}, "TotalTaxableValue": {"type": "number", "description": "Total taxable value of goods or services before taxes"}, "StateTaxValue": {"type": "number", "description": "Total amount of State Goods and Services Tax (SGST) applied"}, "CentralTaxValue": {"type": "number", "description": "Total amount of Central Goods and Services Tax (CGST) applied"}, "TotalIntegratedTaxRate": {"type": "number", "description": "Total Integrated Goods and Services Tax (IGST) rate applied"}, "TotalIntegratedTaxAmount": {"type": "number", "description": "Total Integrated Goods and Services Tax (IGST) amount applied"}, "TotalInvoiceValue(InWords)": {"type": "string", "description": "Total invoice amount in words"}, "PaymentTerm": {"type": "string", "description": "Terms and conditions for payment"}, "Incoterm": {"type": "string", "description": "International Commercial Terms (Incoterms) applied to the transaction"}, "DeliveryNo": {"type": "integer", "description": "Delivery number associated with the invoice"}, "ShipmentNo": {"type": "string", "description": "Shipment number associated with the delivery"}, "ReferenceNo": {"type": "string", "description": "Reference number for tracking the transaction"}, "IRN": {"type": "string", "description": "Invoice Reference Number (IRN) for the e-invoice"}, "SellerEmail": {"type": "string", "description": "Email address of the seller"}, "SellerWebsite": {"type": "string", "description": "Website URL of the seller"}, "SellerCorporateIdentificationNumber": {"type": "string", "description": "Corporate Identification Number (CIN) of the seller"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "The serial number of the item in the invoice"}, "ProductCode": {"type": "string", "description": "Code of the product or service"}, "ProductCode&Description": {"type": "string", "description": "Code and description of the product or service"}, "HSN/SAC": {"type": "string", "description": "The HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code) for classifying the product or service"}, "Qty": {"type": "number", "description": "The quantity of the product in the invoice"}, "UOM": {"type": "string", "description": "The unit of measurement for the product (e.g., pieces, kg, liters)"}, "UnitPrice": {"type": "number", "description": "The price per unit of the product"}, "Freight": {"type": "number", "description": "Freight charges applicable for transporting the goods"}, "Insurance": {"type": "number", "description": "Insurance charges applied for the goods"}, "Packing&ForwardingCharges": {"type": "number", "description": "Charges for packing and forwarding the goods"}, "TaxableValue": {"type": "number", "description": "The value of the item before taxes are applied"}, "CentralTaxRate": {"type": "number", "description": "Rate (in %) of Central Goods and Services Tax (CGST) applied to the item"}, "CentralTaxAmount": {"type": "number", "description": "Amount of Central Goods and Services Tax (CGST) applied to the item"}, "StateTaxRate": {"type": "number", "description": "Rate (in %) of State Goods and Services Tax (SGST) applied to the item"}, "StateTaxAmount": {"type": "number", "description": "Amount of State Goods and Services Tax (SGST) applied to the item"}, "IntegratedTaxRate": {"type": "number", "description": "Rate (in%) of Integrated Goods and Services Tax (IGST) applied to the item"}, "IntegratedTaxAmount": {"type": "number", "description": "Amount of Integrated Goods and Services Tax (IGST) applied to the item"}, "TotalInvoiceValue": {"type": "number", "description": "The total value of the invoice including all taxes and charges"}}, "required": ["SrNo", "ProductCode", "ProductCode&Description", "HSN/SAC", "Qty", "UOM", "UnitPrice", "Freight", "Insurance", "Packing&ForwardingCharges", "TaxableValue", "CentralTaxRate", "CentralTaxAmount", "StateTaxRate", "StateTaxAmount", "IntegratedTaxRate", "IntegratedTaxAmount", "TotalInvoiceValue"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerState", "SellerCountry", "SellerPincode", "SellerGSTINNo", "BillToPartyName", "BillToCode", "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BillToPartyGSTIN", "PlaceOfSupply", "ShipToPartyName", "ShipToCode", "ShipToPartyAddress", "ShipToPartyMob.No", "ShipToPartyTel.No", "ShipToPartyGSTIN", "PlaceOfDelivery", "InvoiceNo", "InvoiceDate", "SONo", "SODate", "PONo", "PODate", "Transporter", "LR/DocketNo", "VehicleNo", "QuotationNo", "TotalQTY", "TotalFreight", "TotalInsurance", "TotalPacking&ForwardingCharges", "TotalCentralTaxRate&Amount", "TotalStateTaxRate&Amount", "TotalIntegratedTaxRate", "TotalIntegratedTaxAmount", "TotalInvoiceValue", "TotalTaxableValue", "StateTaxValue", "CentralTaxValue", "TotalInvoiceValue(InWords)", "PaymentTerm", "Incoterm", "DeliveryNo", "ShipmentNo", "ReferenceNo", "IRN", "Seller<PERSON>mail", "SellerWebsite", "SellerCorporateIdentificationNumber", "Table"], "additionalProperties": false}}}