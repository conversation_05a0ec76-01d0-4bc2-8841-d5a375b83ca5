# Importing libraries
from fastapi import APIRouter, Query, HTTPException, Form
from src.Controllers.Vendor_Controller import CVendorController
from src.Schemas.Vendor_Schema import ModelFieldItem, ModelTable , InvoiceFieldItemUpdate, UpdateVendor
from src.middleware.checkAuth import user_required, UserAndRoleCheck
from src.middleware.checkModel import ModelIdRequired
from fastapi import APIRouter, Depends, UploadFile, File, Path, Body, Request, status
from typing import List, Dict, Union
from src.Schemas.Vendor_Schema import GetAllModelFilterQuery
from config.constants import Constants
from typing import Optional
# from fastapi import APIRouter, Request, Security, Depends, UploadFile, File, Form, HTTPException, Query

VendorRouter = APIRouter(tags=['Model'],  prefix="/api/Model")


@VendorRouter.post('/ModelFields')
async def AddReqInvoiceFields( strModelName:str, dictFields: Dict[str, Union[List[ModelFieldItem], List[ModelTable]]], iUserID:int=Depends(user_required), strFamilyName:str=Constants.DefaultFamilyName):
    return await CVendorController.MSAddInvoiceFields(iUserID= iUserID, strModelName= strModelName, strFamilyName=strFamilyName, dictFields= dictFields)


@VendorRouter.post('/Duplicate')
async def DuplicateModel(iModelId: int,  strNewModelName: str, strFamilyName: str, iUserID:int=Depends(user_required), strModelDesc:str=""):
    return await CVendorController.MSDuplicateModel(iUserID= iUserID, iModelId= iModelId, strNewModelName=strNewModelName, strFamilyName= strFamilyName, strModelDesc=strModelDesc)

@VendorRouter.get('/ModelFields')
async def GetAllReqInvoiceFields( strModelName:str, dictRequestData:dict=Depends(ModelIdRequired)):
    return await CVendorController.GetAllReqInvoiceFields(iUserID= dictRequestData.get("UserId"), iModelId = (dictRequestData.get("ModelData")).get("ModelId"))

@VendorRouter.post('/Model')
async def AddVendor(strModelName:str,  iUserID:int=Depends(user_required),preDefinedModelDict: dict={}, strFamilyName:str=Constants.DefaultFamilyName, strModelDesc:str = ""):
    return await CVendorController.MSCreateVendor(iUserID= iUserID, strModelName= strModelName, strFamilyName=strFamilyName,preDefinedModelDict=preDefinedModelDict, strModelDesc=strModelDesc)


@VendorRouter.put('/Model')
async def UpdateVendorDetail(
                                iUserID:int=Depends(user_required), 
                                iModelId : int  = Query(...,description="Model Id required to fetch model data"),
                                strNewModelName:str = Query(None, description="New Updated Model Name."), 
                                strNewModelFamily:str = Query(None, description="New Updated Model Family Name."), 
                                strNewModelDesc:str = Query(...,description="New Updated Model Description"), 
                                preDefinedModelDict:dict={}
                            ):
    
    return await CVendorController.MSUpdateVendor(iUserID=iUserID, iModelId=iModelId, strNewModelName=strNewModelName,  strNewModelFamily=strNewModelFamily,strNewModelDesc=strNewModelDesc,preDefinedModelDict=preDefinedModelDict)


@VendorRouter.delete('/Model')
async def DeleteVendor( strModelName:str = Query(...,description="Model Name required to fetch model data"), iModelId : int  = Query(...,description="Model Id required to fetch model data"), dictRequestData:dict=Depends(ModelIdRequired)):
    return await CVendorController.MSDeleteVendor(iUserID= dictRequestData.get("UserId"), strModelName= dictRequestData.get("ModelData").get("ModelName"), iModelId =  dictRequestData.get("ModelData").get("ModelId"))


@VendorRouter.delete('/ModelFields')
async def DeleteInvoiceFields(  lsInvoiceFieldIds: List[int], strModelName:str = Query(...,description="Model Name required to fetch model data"), iModelId : int  = Query(...,description="Model Id required to fetch model data"), dictRequestData:dict=Depends(ModelIdRequired)):
    return await CVendorController.MSDeleteInvoiceFields(iUserID=  dictRequestData.get("UserId"), strModelName= dictRequestData.get("ModelData").get("ModelName"), iModelId =  dictRequestData.get("ModelData").get("ModelId"), lsInvoiceFieldIds = lsInvoiceFieldIds)


@VendorRouter.get('/Model')
async def GetVendors(filterQuery:GetAllModelFilterQuery = Depends(), dictRequestData:dict=Depends(UserAndRoleCheck), iLimit: int = Query(10, gt=0), iPage: int = Query(1, gt=0)):
    return await CVendorController.MSGetAllVendors(iUserID= dictRequestData.get("UserId"), hasAdminRights =  (dictRequestData.get("RoleData")).get("hasAdminRights"),  iLimit= iLimit, iPage= iPage, filterQuery=filterQuery)


