# src/schemas.py

from pydantic import BaseModel, EmailStr, Field
from typing import List, Optional, Union
from datetime import datetime
from enum import Enum as PydanticEnum
from config.constants import Constants
import pytz
from typing import Dict, Any
import json  # Import this to handle JSON type


class DocExtractOutputModel(str, PydanticEnum):
    GPT = "GPT"
    Gemini = "Gemini"

class GPTBatchAPIStatusEnum(str, PydanticEnum):
    in_progress = "in_progress"
    completed = "completed"
    # error - failed, error, expired, cancelled
    error = "error"
    

class StatusEnum(str, PydanticEnum):
    NotProcess = "NotProcess"
    Approved = "Approved"
    Error = "Error"
    OnHold = "OnHold"
    ToBeApproved = "ToBeApproved"
# Initializing
class check_user(BaseModel):
    username : str

class PaginationInfo(BaseModel):
    total_documents: int
    total_pages: int
    current_page: int
    per_page: int
    total_processed_status: int
    total_TallyStatus: int

class UploadedDocSchema(BaseModel):
    DocId: int
    UserId: Optional[int] = Field(None, description="User ID (optional, can be None)")
    DocName: str
    DocExtractionAPIStatusCode: Optional[int]
    DocDebugMsg: Optional[str] = Field(default="", description="Debug messages if any")
    DocErrorMsg: Optional[str] = Field(default="", description="Error messages if any")
    UploadedDateTime: datetime
    ModifiedDateTime: datetime
    Status: str
    TallyStatus: str

    class Config:
        from_attributes = True

class PaginatedDocumentsResponse(BaseModel):
    pagination: PaginationInfo
    documents: List[UploadedDocSchema]

class DocExtractedDataSchema(BaseModel):
    Id: Optional[int] = None
    DocId: int
    Response: dict
    DocExtractionPromptID: int
    class Config:
        from_attributes = True

# Pydantic schema to validate the data for UserAPIUsage
class UserAPIUsageSchema(BaseModel):
    user_id: int
    used_tokens: Optional[int] = 0
    api_requested: Optional[int] = 0
    page_limit_left: Optional[int] = int(Constants.MaxTrialPaidDocExtractionPerUser)
    class Config:
        from_attributes = True

class UserAPITokenUsage(BaseModel):
    user_id: int
    used_tokens: int
    class Config:
        from_attributes = True
        
class UserAPIRequestUsage(BaseModel):
    user_id: int
    api_requested: Optional[int] = 1
    class Config:
        from_attributes = True


class GPTJSONUpdate(BaseModel):
    UpdatedVal: dict
    bApprove: bool

class GetAllDocFilterQuery(BaseModel):
    bFileNameAsc : Optional[bool] = None
    bUploadDateAsc : Optional[bool] = None
    strDocStatus : Optional[str] = None 
    strSearchInTable : Optional[str] = None
    strStartdate : Optional[str] = None
    strEnddate : Optional[str] = None
    bDateAsc: Optional[bool] = None
    bStatusAsc: Optional[bool] = None
    bDocTypeAsc: Optional[bool] = None
    bModelNameAsc: Optional[bool] = None
    iUserId : Optional[Union[int, str]] = None
    bUserIdAsc:  Optional[bool] = None


class GetAllModelFilterQuery(BaseModel):
    bModelNameAsc : Optional[bool] = None
    bModelFamilyNameAsc : Optional[bool] = None
    bTotalDocsAsc : Optional[bool] = None
    
class PromptFieldItem(BaseModel):
    ID: int
    fieldName: str
    fieldType: str
    fieldDescription: str
    isLineItemTableField: bool

class PromptInputData(BaseModel):
    items: List[PromptFieldItem]
    
class DocExtractionAPIModel(BaseModel):
    document_id : int
    page_limit_left: Optional[int] = None
    total_allowed_page_limit: Optional[int] = None
    Document : Optional[dict] = None
    APIStatusCode: int = None
    DocErrorMsg: Optional[str] = None
    DocExtractionStatus : Optional[str] = None
    detail: Optional[str] = None
    IsPaidModel: Optional[bool] = None     

# Pydantic models for input validation and serialization
class GPTBatchAPIRecordCreate(BaseModel):
    Batch_Object: dict
    Batch_Polling_Object: Optional[dict] = None
    TaskDetails: dict
    Status: Optional[GPTBatchAPIStatusEnum] = GPTBatchAPIStatusEnum.in_progress

class GPTBatchAPIRecordUpdate(BaseModel):
    Batch_Object: Optional[dict] = None
    Batch_Polling_Object: Optional[dict] = None
    TaskDetails: Optional[dict] = None
    Status: Optional[GPTBatchAPIStatusEnum] = None