from datetime import datetime, date
from config.db_config import AsyncSessionLocal
from sqlalchemy import update
from fastapi import Depends
from datetime import datetime
import pytz
from src.Models.models import UploadedDoc
from typing import List


class CDateHelper:

    # Date Formats
    db_date_format = "%Y-%m-%d %H:%M:%S"

    # Convert string to date
    def string_to_date(date: str, date_format: str = db_date_format):
        return datetime.strptime(date, date_format).date()

    @staticmethod
    async def update_modified_time(doc_ids: List[int]):
        current_time = datetime.now(pytz.timezone('Asia/Kolkata'))
        async with AsyncSessionLocal() as db:
            stmt = update(UploadedDoc).where(UploadedDoc.DocId.in_(
                doc_ids)).values(ModifiedDateTime=current_time)
            await db.execute(stmt)
            await db.commit()
    
    @staticmethod
    # Convert to EDT format components
    def extract_edt_components(dt):
        hour = dt.hour
        minutes = dt.minute
        am_pm = "AM" if hour < 12 else "PM"
        hour = hour % 12 or 12  # Convert to 12-hour format and handle midnight (0 -> 12)

        return {
            "month": dt.strftime('%B'),
            "day": dt.day,
            "year": dt.year,
            "hours": hour,
            "minutes": minutes,
            "am_pm": am_pm
        }