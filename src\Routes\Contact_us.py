from fastapi import APIRouter, Depends, UploadFile, File, Form
from fastapi.security import <PERSON>Auth2PasswordBearer
from pydantic import BaseModel #, EmailStr
from datetime import datetime
from typing import List,Optional
from src.Controllers.ContactUs_controller import ContactUsController
from src.middleware.checkAuth import admin_required
from src.Models.models import QueryCategory, Status_ContactUsQuery

contactus = APIRouter(tags=['ContactUs'], prefix="/api")


class ContactUsResponse(BaseModel):
    id: int
    query_category: QueryCategory
    customer_name: str
    email: str
    phone_number: str = None
    country: str = None
    designation: str = None
    company_name: str = None
    message: str
    status: Status_ContactUsQuery
    created_at: datetime
    updated_at: datetime
    

    class Config:
        # orm_mode = True
        use_enum_values = True


@contactus.post("/contact-us")
async def add_contact_us(
    query_category: QueryCategory = Form(...),
    customer_name: str = Form(...),
    email: str = Form(...),
    phone_number: Optional[str] = Form(None),
    country: Optional[str] = Form(None),
    designation: Optional[str] = Form(None),
    company_name: Optional[str] = Form(None),
    message: str = Form(...),
    files:List[UploadFile] = File(None),
    status: Status_ContactUsQuery = Status_ContactUsQuery.OPEN
):
    try:
        return await ContactUsController.add_contact_us(
            query_category=query_category,
            customer_name=customer_name,
            email=email,
            phone_number=phone_number,
            country=country,
            designation=designation,
            company_name=company_name,
            message=message,
            files=files,
            status=status
        )
    except Exception as e:
        import traceback
        traceback.print_exc()
        print(e)
        return {"error": str(e)}
        
@contactus.get("/contact-us-entries", response_model=List[ContactUsResponse])
async def get_all_contact_us(admin: bool = Depends(admin_required)):
    return await ContactUsController.get_AllContactUs()