from datetime import datetime
import traceback
from fastapi import HTTPException
from config.db_config import AsyncSessionLocal
from src.Models.models import AVGRNProcessingDetails
from src.Controllers.Logs_Controller import CLogController
import pytz
import json
from sqlalchemy import select


class CAVGRNProcessingDetailsService:

    @staticmethod
    async def MSUpdateGRNProcessingDetails(
        UserID: int,
        RequestID: int,
        GRNNumber: str,
        GRNDetails: dict = None,
        AVStatus: str = None,
        XMLImportStatusCode: str = None,  
        XMLImportType: str = None,   
        ErrorMessage: str = None,
        CreatedDateTime: datetime = None,
        UpdatedDateTime: datetime = None,
        IsDeleted: bool = False,
        DeletedDateTime: datetime = None,
        XMLImportDateTime: datetime = None,
        GeneratedXML: str = None
    ):
        """
        Insert or update a GRN processing record in AVGRNProcessingDetails.
        """

        try:
            async with AsyncSessionLocal() as db:

                if CreatedDateTime is None:
                    CreatedDateTime = datetime.now()
                if UpdatedDateTime is None:
                    UpdatedDateTime = datetime.now()
                    
                if XMLImportDateTime is None and XMLImportStatusCode is not None:
                    XMLImportDateTime = datetime.now()

                stmt = (
                    select(AVGRNProcessingDetails)
                    .filter_by(UserID=UserID, RequestID=RequestID, GRNNumber=GRNNumber, IsDeleted=False)
                    .order_by(AVGRNProcessingDetails.CreatedDateTime.desc())
                    .limit(1)
                )

                result = await db.execute(stmt)
                existing_record = result.scalar_one_or_none()

                if existing_record:
                    if GRNDetails is not None:
                        existing_record.GRNDetails = GRNDetails
                    if AVStatus is not None:
                        existing_record.AVXMLStatus = AVStatus
                    if XMLImportStatusCode is not None:
                        existing_record.XML_IMPORT_STATUS_CODE = XMLImportStatusCode
                    if XMLImportType is not None:
                        existing_record.XML_IMPORT_TYPE = XMLImportType
                    if ErrorMessage is not None:
                        existing_record.ErrorMessage = ErrorMessage
                    if GeneratedXML is not None:
                        existing_record.GeneratedXML = GeneratedXML  

                    existing_record.XMLImportDateTime = XMLImportDateTime
                    existing_record.UpdatedDateTime = UpdatedDateTime
                    existing_record.IsDeleted = IsDeleted
                    existing_record.DeletedDateTime = DeletedDateTime if IsDeleted else None

                    await db.commit()

                    await CLogController.MSWriteLog(
                        UserID,
                        "INFO",
                        f"[MSUpdateGRNProcessingDetails] Updated GRN {GRNNumber} for UserID {UserID}, RequestID {RequestID}"
                    )
                    return {"status": "updated", "id": existing_record.ID}

                else:
                    new_record = AVGRNProcessingDetails(
                        UserID=UserID,
                        RequestID=RequestID,
                        GRNNumber=GRNNumber,
                        GRNDetails=json.dumps(GRNDetails) if GRNDetails else {},
                        AVXMLStatus=AVStatus,
                        XML_IMPORT_STATUS_CODE=XMLImportStatusCode,
                        XML_IMPORT_TYPE=XMLImportType,
                        ErrorMessage=ErrorMessage,
                        CreatedDateTime=CreatedDateTime,
                        UpdatedDateTime=UpdatedDateTime,
                        IsDeleted=IsDeleted,
                        DeletedDateTime=DeletedDateTime if IsDeleted else None,
                        XMLImportDateTime=XMLImportDateTime,
                        GeneratedXML=GeneratedXML
                    )

                    db.add(new_record)
                    await db.commit()
                    await db.refresh(new_record)

                    await CLogController.MSWriteLog(
                        UserID,
                        "INFO",
                        f"[MSUpdateGRNProcessingDetails] Inserted new GRN {GRNNumber} for UserID {UserID}, RequestID {RequestID}"
                    )
                    return {"status": "inserted", "id": new_record.ID}

        except Exception as exc:
            await CLogController.MSWriteLog(
                UserID,
                "ERROR",
                f"[MSUpdateGRNProcessingDetails] failure → {exc}"
            )
            await CLogController.MSWriteLog(
                UserID,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail="Error updating/inserting AVGRNProcessingDetails records")


    @staticmethod
    async def MSIsDuplicateGRN(
        UserID: int,  # The ID of the user making the request
        GRNNumber: str  # The GRN number to check for duplicates
    ):
        """
        Method to check if the GRN number exists for the specified UserID
        and whether its status is 'Success'. If found, return True;
        otherwise, return False.

        Parameters:
            UserID: int - The ID of the user making the request.
            GRNNumber: str - The GRN number to check for duplicates.

        Returns:
            bool: True if the GRN exists and its status is 'Success', False otherwise.
        """
        try:
            async with AsyncSessionLocal() as db:
                
                stmt = select(AVGRNProcessingDetails).filter_by(UserID=UserID, GRNNumber=GRNNumber, IsDeleted=False).order_by(AVGRNProcessingDetails.CreatedDateTime.desc()).limit(1)
                result = await db.execute(stmt)
                existing_record = result.scalar_one_or_none()

                # If the record exists and its status is 'Success'
                if existing_record and (
                            (existing_record.AVXMLStatus == "Success" or existing_record.AVXMLStatus == "Duplicate") or
                            (hasattr(existing_record.AVXMLStatus, "value") and (existing_record.AVXMLStatus.value == "Success" or existing_record.AVXMLStatus.value == "Duplicate"))
                        ):
                    return True

                # If no matching record is found or status is not 'Success'
                return False

        except Exception as exc:
            await CLogController.MSWriteLog(
                UserID,
                "ERROR",
                f"[MSCheckForDuplicateGRN] failure → {exc}"
            )
            await CLogController.MSWriteLog(
                UserID,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail="Error checking for duplicate GRN status")

    
    @staticmethod
    async def MSTotalTimeSavedTillNow(UserID: int, time_per_item_in_seconds: int = 30):
        """
        Calculates total time saved for a given user from GRN records by multiplying
        the number of items in GRNDetails with time_per_item_in_seconds.
        Skips records where AVXMLStatus is 'Skipped' or 'Duplicate'.

        Parameters:
            UserID (int): The user ID.
            time_per_item_in_seconds (int): Time in seconds saved per item. Defaults to 30.

        Returns:
            dict: {
                'total_unique_entries': int,
                'total_time_saved': str (human-readable time)
            }
        """
        dictStatistics = {
                    "total_unique_entries": "-",
                    "total_time_saved": "-"
                }
        try:
            async with AsyncSessionLocal() as db:

                stmt = (
                    select(AVGRNProcessingDetails)
                    .filter(
                        AVGRNProcessingDetails.UserID == UserID,
                        AVGRNProcessingDetails.AVXMLStatus.notin_(["Skipped", "Duplicate"])
                    )
                )
                result = await db.execute(stmt)
                grn_records = result.scalars().all()

                total_seconds = 0
                total_entries = 0

                for record in grn_records:
                    grn_details = record.GRNDetails if hasattr(record, "GRNDetails") else None
                    if grn_details:
                        try:
                            if isinstance(grn_details, str):
                                grn_data = json.loads(grn_details)
                            else:
                                grn_data = grn_details
                            
                            # For Machinery GRN Entries
                            if len(record.GRNNumber) > 20:
                                total_entries += 1  
                                total_seconds += time_per_item_in_seconds

                            # For all other GRN Entries
                            else:
                                items = grn_data.get("Items", []) if isinstance(grn_data, dict) else []

                                if isinstance(items, list) and len(items) > 0:
                                    total_seconds += len(items) * time_per_item_in_seconds
                                    total_entries += 1  # Count only valid records with items

                        except json.JSONDecodeError:
                            continue  # Skip malformed JSON

                # Normalize total time
                total_minutes, total_seconds = divmod(total_seconds, 60)
                total_hours, total_minutes = divmod(total_minutes, 60)

                # Format total time saved
                formatted_time = []
                if total_hours > 0:
                    formatted_time.append(f"{total_hours} hr")
                if total_minutes > 0:
                    formatted_time.append(f"{total_minutes} min")
                if total_seconds > 0 or not formatted_time:
                    formatted_time.append(f"{total_seconds} sec")
                
                dictStatistics["total_unique_entries"] = total_entries
                dictStatistics["total_time_saved"] = " ".join(formatted_time)

        except Exception as exc:
            await CLogController.MSWriteLog(
                UserID,
                "ERROR",
                f"[MSTotalTimeSavedFromGRN] failure → {exc}"
            )
            await CLogController.MSWriteLog(
                UserID,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )

        return dictStatistics
