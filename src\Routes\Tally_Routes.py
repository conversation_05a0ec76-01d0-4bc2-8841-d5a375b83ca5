# Importing libraries
from fastapi import APIRouter, HTTPException, Request, status
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.ParagTradersControllers import  CParagTraders
from src.middleware.checkAuth import user_required
from fastapi import APIRouter, Depends, UploadFile, File
from src.Schemas.Tally_Schemas import AddT<PERSON><PERSON>eader, AddTallyBody, UpdateTallyHeader
from src.Schemas.Tally_Schemas import TallyDocRecordUpdate, TallyUserConfig, TallyModelConfig
from typing import List
from pydantic import BaseModel
from fastapi import APIRouter, Depends, UploadFile, File, Query, HTTPException, Form
import traceback
from src.Controllers.CustomLogger import CLogger

class DocIdsModel(BaseModel):
    docIds: List[int]

TallyRouter = APIRouter(tags=['Tally'],  prefix="/api/tally")

@TallyRouter.post('/Initialize-Templates')
async def GetTemplates(iUserID:int = Depends(user_required)):
    return await CTallyController.MSInitializeTemplates()


@TallyRouter.get('/Templates')
async def GetTemplates(iUserID:int = Depends(user_required)):
    return await CTallyController.MSGetAllTemplates(iUserId = iUserID)

@TallyRouter.get('/Templates/{TemplateKey}')
async def GetTemplates(TemplateKey: str, iUserID:int = Depends(user_required)):
    return await CTallyController.MSGetTallyTemplateOnKey(template_key=str(TemplateKey))

# Route to update or create a TallyDocRecord
@TallyRouter.post('/DocRecords')
async def SetTallyDocRecords(tally_doc_record: TallyDocRecordUpdate, iUserID: int = Depends(user_required)):
    """
    Route to update or create a TallyDocRecord.

    Inputs:
        - tally_doc_record: TallyDocRecordUpdate (The JSON body containing the doc record data)
        - iUserID: int (Injected user ID from authentication middleware)

    Output:
        - Returns the updated/created record as a dictionary.
    """
    try:
        return await CTallyController.MSSetTallyDocRecords(
            doc_id=tally_doc_record.doc_id,
            tally_api_req=tally_doc_record.tally_api_req,
            tally_status=tally_doc_record.tally_status,
            req_date_time=tally_doc_record.req_date_time,
            tally_api_resp=tally_doc_record.tally_api_resp,
            resp_date_time=tally_doc_record.resp_date_time,
            DocErrorMsg= tally_doc_record.DocErrorMsg
        )
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

# Route to get a TallyDocRecord by DocID
@TallyRouter.get('/DocRecords/{DocID}')
async def GetTallyDocRecords(DocID: int, iUserID: int = Depends(user_required)):
    """
    Route to retrieve a TallyDocRecord by DocID.
    
    Inputs:
        - DocID: int (The DocID of the record to retrieve)
        - iUserID: int (Injected user ID from authentication middleware)

    Output:
        - Returns the record as a dictionary if found, or raises HTTPException if not found.
    """
    try:
        return await CTallyController.MSGetTallyDocRecords(doc_id=DocID)
    except HTTPException as e:
        raise e
    except Exception as e:
        raise HTTPException(status_code=500, detail=str(e))

@TallyRouter.post('/integrate')
async def AddIntegration(authKey:str, action: str = "subscribe", integration: str = "Tally", iUserID:int = Depends(user_required)):
    return await CTallyController.MSManageIntegration(  iUserID=iUserID, 
                                                        authKey=authKey, 
                                                        integration=integration,
                                                        action=action)

@TallyRouter.post('/model-config')
async def AddTallyModelConfig( config: TallyModelConfig,
                                iUserID:int = Depends(user_required)
                            ):
    
    return await CTallyController.MSAddTallyModelConfig(
                                                        iUserID=iUserID, 
                                                        templateName=config.templateName,
                                                        companyName=config.companyName,
                                                        modelId=config.modelId,
                                                        mappings=config.mappings,
                                                        GSTIN=config.GSTIN,
                                                        ExtraFields=config.extrafields,
                                                        tally_enable=config.tally_enable,
                                                        tally_ledger_config=config.tally_ledger_config,
                                                        tally_voucher_type_config=config.tally_voucher_type_config,
                                                        tally_stock_item_config=config.tally_stock_item_config
                                                    )

@TallyRouter.get('/model-config/{iModelID}')
async def GetTemplates( iModelID: int ,iUserID:int = Depends(user_required)):
    return await CTallyController.MSGetModelConfig(iModelID=iModelID, iUserID=iUserID)

@TallyRouter.get('/model-mapping')
async def GetModelMapping( iDocID: int ,iUserID:int = Depends(user_required)):
    return await CTallyController.MSFetchMappedDocumentData(iDocID=iDocID, iUserID=iUserID)

@TallyRouter.get('/config')
async def GetUserConfig( iUserID:int = Depends(user_required)):
    return await CTallyController.MSGetTallyUserConfig(iUserID=iUserID)

@TallyRouter.post('/config', status_code=status.HTTP_200_OK)
async def SetUserConfig(
    config: TallyUserConfig,
    iUserID: int = Depends(user_required)
):
    """
    Set the Tally user configuration.

    - **iUserID**: ID of the user setting the configuration.
    - **auth_key**: Authentication key.
    - **tally_enable**: Enable or disable Tally integration.
    - **tally_voucher_type_config**: Configuration for voucher types.
    - **tally_ledger_config**: Configuration for ledgers.
    - **tally_stock_item_config**: Configuration for stock items.
    """
    try:
        # Call the controller method with the provided parameters
        result = await CTallyController.MSSetTallyUserConfig(
            iUserID=iUserID,
            auth_key=config.auth_key,
            tally_enable=config.tally_enable,
            tally_voucher_type_config=config.tally_voucher_type_config,
            tally_ledger_config=config.tally_ledger_config,
            tally_stock_item_config=config.tally_stock_item_config
        )
        
        # Assuming the controller returns a dictionary with relevant information
        return {"status": "success", "data": result}
    
    except ValueError as ve:
        # Handle known errors (e.g., invalid configurations)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        # Handle unexpected errors
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while setting the configuration."
        )

@TallyRouter.post('/processDocInTally/{iDocId}', status_code=status.HTTP_200_OK)
async def SetDocInTally(
    iDocId: int,
    iUserID: int = Depends(user_required)
):
    try:
        
        objParagTraders = CParagTraders(user_id=iUserID, doc_id = iDocId)
        result = await objParagTraders.MSCallTallyAPI()
        # Assuming the controller returns a dictionary with relevant information
        return {"status": "success", "data": result}
    
    except ValueError as ve:
        # Handle known errors (e.g., invalid configurations)
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail=str(ve)
        )
    except Exception as e:
        # Handle unexpected errors
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="An unexpected error occurred while setting the configuration."
        )
# @TallyRouter.post('/header')
# async def CreateHeader(objTallyHeader:AddTallyHeader, iUserID:int = Depends(user_required)):
#     return await CTallyController.MSAddHeader(iUserID = iUserID, objTallyHeader=objTallyHeader)


# @TallyRouter.post('/body')
# async def AddBody(objTallyBody:AddTallyBody, iUserID:int= Depends(user_required)):
#     return await CTallyController.MSAddBody(iUserID = iUserID, objTallyBody=objTallyBody)


# @TallyRouter.get('/header')
# async def GetTallyHeader(iUserID:int = Depends(user_required)):
#     return await CTallyController.MSGetTallyHeader(iUserID = iUserID)


# @TallyRouter.put('/header')
# async def UpdateTallyHeader( objTallyHeader:UpdateTallyHeader, iUserID:int= Depends(user_required)):
#     return await CTallyController.MSUpdateTallyHeader(iUserID = iUserID, objTallyHeader = objTallyHeader)


# @TallyRouter.post('/upload')
# async def AddToTally(iDocID: int, iUserID:int= Depends(user_required)):
#     return await CTallyController.MSAddToTally(iUserID = iUserID, iDocID=iDocID)

@TallyRouter.post('/send-to-tally')
async def send_to_tally(doc_ids: DocIdsModel, iUserID: int = Depends(user_required)):
    return await CTallyController.MSSendToTally(iUserID=iUserID, docIds=doc_ids.docIds)


@TallyRouter.post("/Export")
async def tally_export(
    files: List[UploadFile] = File(...),
    iUserid:int = Depends(user_required)):
    try:
        return await CTallyController.MSSaveTallyExport(iUserID=iUserid, lsObjFiles=files)
    except Exception as e:
        CLogger.MCWriteLog("error", f"Error occurred in Tally Export: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal error: {str(e)}")