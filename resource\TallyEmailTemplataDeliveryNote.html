<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Invoice - Tally Posting Report</title>
    <style>
        body {{
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333333;
        }}

        .container {{
            max-width: full;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.1);
            border-radius: 8px;
        }}

        .header {{
            text-align: center;
            border-bottom: 2px solid #003654;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }}

        .header h2 {{
            color: #003654;
            margin: 10px 0;
            font-size: 26px;
        }}

        .header p {{
            font-size: 16px;
            color: #555555;
        }}

        .intro {{
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 30px;
        }}

        .highlight-section {{
            margin-bottom: 30px;
            font-size: 16px;
            line-height: 1.8;
        }}

        .highlight-section ul {{
            list-style: none;
            padding: 0;
            margin: 0;
        }}

        .highlight-section ul li {{
            margin-bottom: 10px;
            display: flex;
            justify-content: space-between;
        }}

        .highlight-section .label {{
            font-weight: bold;
            color: #003654;
        }}

        .metrics-table {{
            width: 100%;
            margin: 0 auto 20px auto;
            font-size: 14px;
            border-collapse: collapse;
        }}

        .metrics-table th, .metrics-table td {{
            text-align: left;
            padding: 10px 15px;
            border: 1px solid #dddddd;
        }}

        .metrics-table th {{
            background-color: #003654;
            color: #ffffff;
        }}

        .metrics-table td {{
            background-color: #f9f9f9;
        }}

        .styled-table {{
            border-collapse: collapse;
            margin: 20px auto;
            font-size: 14px;
            table-layout: fixed;
            width: 100%;
            overflow-x: auto;
        }}

        .styled-table th, .styled-table td {{
            padding: 12px 15px;
            text-align: center;
            border: 1px solid #dddddd;
            word-wrap: break-word;
        }}

        .styled-table th {{
            background-color: #003654;
            color: #ffffff;
            text-transform: uppercase;
        }}

        .styled-table tbody tr:nth-of-type(even) {{
            background-color: #f3f3f3;
        }}

        .styled-table tbody tr:hover {{
            background-color: #f1f1f1;
        }}

        .description {{
            font-size: 14px;
            line-height: 1.6;
            margin-bottom: 30px;
        }}

        .description ul {{
            list-style-type: disc;
            padding-left: 20px;
        }}

        .footer {{
            text-align: center;
            font-size: 14px;
            color: #777777;
            border-top: 1px solid #dddddd;
            padding-top: 20px;
        }}

        @media only screen and (max-width: 600px) {{
            .container {{
                padding: 20px;
            }}

            .header h2 {{
                font-size: 22px;
            }}

            .intro, .description, .footer {{
                font-size: 14px;
            }}

            .styled-table th, .styled-table td {{
                padding: 10px 12px;
            }}
        }}

        /* General styles for success and failure */
        .success {{
            color: green;
            font-weight: bold;
        }}

        .failure {{
            color: red;
            font-weight: bold;
        }}
        /* Tally status: PartialSuccess - blue */
        .partial-success {{
            color: #1e88e5; /* medium blue */
            font-weight: bold;
        }}


        /* Tally status: Duplicate - amber */
        .duplicate {{
            color: #ffb300; /* amber/goldenrod */
            font-weight: bold;
        }}

        /* Tally status: ValidationError - alert Yellow */
        .validation-error {{
            color: #e0e034; /* alert yellow */
            font-weight: bold;
        }}
        /* Tally status: Skipped - grey */
        .skipped {{
            color: #808080; /* grey */
            font-weight: bold;
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>Invoice - Tally Posting Report for {receiverName}</h2>
            <p><b>Date:</b> <b>{date}</b>{system_info}{time_info}</p>
        </div>

        <div class="intro">
            <p>Dear <b>{receiverName}</b>,</p>
            <p>Please find attached the invoices along with their corresponding Tally receipts for your review. Kindly take a moment to verify the details at your earliest convenience.</p>
        </div>

        <div class="highlight-section">
            <h3>Performance Summary:</h3>
            <table class="metrics-table">
                <tr>
                    <th>Metrics</th>
                    <th>Current Processing Stats</th>
                    <th>Till Now Values</th>
                </tr>
                <tr>
                    <td><b>Total Document Processed</b></td>
                    <td>{total_pages_processed_today}</td>
                    <td>{total_pages_processed_till_now}</td>
                </tr>
                <tr>
                    <td><b>Total Time Saved</b></td>
                    <td>{total_time_saved_today}</td>
                    <td>{total_time_saved_till_now}</td>
                </tr>
            </table>
        </div>

        {table}

        <div class="description">
            <h3>Table Details:</h3>
            <ul>
                <li><strong>SrNo.:</strong> Serial number of each invoice.</li>
                <li><strong>Code:</strong> The Unique ID of the item.</li>
                <li><strong>Description:</strong> The description of the item.</li>
                <li><strong>Amount:</strong> The total amount for mentioned item.</li>
                <li><strong>AVTally Status:</strong> Indicates whether the document has been entered (punched-in) into the Tally accounting system (Successful or Skip).</li>
                <li><strong>AV_Comments:</strong> Additional comments related to the processing of the document, providing clarification about skipped items or other discrepancies.</li>
            </ul>
        </div>

        <div class="footer">
            <p>Regards,<br><b>Accuvelocity Team</b></p>
        </div>
    </div>
</body>
</html>
