from config.db_config import AsyncSess<PERSON>Local
from sqlalchemy import update
import traceback
from sqlalchemy.future import select
from fastapi import HTTPException
# Make sure to import your UserAPIUsage model
from src.Models.models import UserAPIUsage, User, Role
from src.Schemas.Api_Usage_Schema import UserAPIUsageSchema
from sqlalchemy.exc import SQLAlchemyError
from src.Controllers.Logs_Controller import CLogController
from config.constants import Constants
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError

class CUserAPIUsageData:

    @staticmethod
    async def MSInsertUserApiUsage(iUserID, iUserApiData: UserAPIUsageSchema):
        '''
        Purpose : This method is used to insert user API usage data into the database.

        Inputs  :   (1)     iUserApiData   :   Data of user API usage (UserAPIUsageSchema)
                    (2)     m_objLogger    :   Logger object for logging (CLogger), default is None

        Output  : It returns the UserAPIUsage model instance with the inserted data.

        Example : await CUserAPIUsageData.MSInsertUserApiUsage(iUserApiData=UserAPIUsageSchema(...))
        '''
        try:
            
            await CLogController.MSWriteLog(iUserID, "Info", f"Process of inserting user API usage data started.")

            async with AsyncSessionLocal() as db:  # Make sure AsyncSessionLocal is correctly imported
                # Convert Pydantic schema to SQLAlchemy model
                objUserApiModel = UserAPIUsage(
                    user_id=iUserID,
                    used_tokens=iUserApiData.get("used_tokens"),
                    api_requested=iUserApiData.get("api_requested"),
                    page_limit_left = iUserApiData.get("page_limit_left"),
                    total_allowed_page_limit = iUserApiData.get("total_allowed_page_limit")
                )

                # Add the new user API usage data to the session
                db.add(objUserApiModel)

                # Commit the transaction
                await db.commit()

                # Refresh the instance with new data from the database
                await db.refresh(objUserApiModel)

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Inserted User Api Usage Data")

                # Return the inserted data
                return objUserApiModel
            
        except SQLAlchemyError as e:
            await db.rollback()
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Insert User Api Usage Data")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Unable to complete requests due to a Internal error.")

        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Insert User Api Usage Data")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="An error occurred while processing your request.")

    @staticmethod
    async def MSResetApiRequestedForUser(iUserId: int):
        '''
        Purpose : This method is used to reset the api_requested field to 0 for a specific user.

        Inputs  :   (1)     iUserId   :   The ID of the user (integer)

        Output  : None. It performs an update operation on the database.

        Example : await CUserAPIUsageData.MSResetApiRequestedForUser(iUserId=123)
        '''
        try:
            async with AsyncSessionLocal() as db:
                await CLogController.MSWriteLog(iUserId, "Info", f"Process of Resetting api started")

                # Start a transaction
                async with db.begin():
                    # Update the api_requested field for a specific user
                    await db.execute(
                        update(UserAPIUsage)
                        .where(UserAPIUsage.user_id == iUserId)
                        .values(api_requested=0)
                    )
                    # Commit the transaction
                    await db.commit()

                await CLogController.MSWriteLog(iUserId, "Info", f"Successfully Reset api Requested for user.")

        except SQLAlchemyError as e:
            # Rollback in case of error
            await db.rollback()
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to Reset api Requested for user")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Unable to complete requests due to a Internal error.")
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to Reset api Requested for user")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request.")

    @staticmethod
    async def MSUpdateUserApiRequest(iUserId: int, iAdditionalRequests: int):
        '''
        Purpose : This method is used to update the API usage data for a user.

        Inputs  :   (1)     iUserId             :   The ID of the user (integer)
                    (2)     iAdditionalRequests :   Additional requests to add (integer)

        Output  : It returns a dictionary with the details of the updated user API usage data.

        Example : await CUserAPIUsageData.MSUpdateUserApiRequest(iUserId=123,iAdditionalRequests=5)
        '''
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Process of Updating Users api usage Started.")
            async with AsyncSessionLocal() as db:  # Use your actual async session maker

                # Attempt to fetch the user's API usage data
                result = await db.execute(select(UserAPIUsage).filter(UserAPIUsage.user_id == iUserId))
                mUserApiUsage = result.scalars().first()

                # If the user's API usage record does not exist, raise an error
                if not mUserApiUsage:
                    await CLogController.MSWriteLog(iUserId, "Error", f"User API usage data not found for user_id: {iUserId}")
                    raise HTTPException(
                        status_code=404, detail="Usage Data Not Found")

                # Update the used_tokens and api_requested fields with new values
                mUserApiUsage.api_requested += iAdditionalRequests

                try:
                    # Commit the changes
                    await db.commit()
                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully Updated User API usage data for user_id: {iUserId}")
                except SQLAlchemyError as e:
                    # If there is an error during commit, rollback the session
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update user usage data due to a database error for User: {iUserId}")
                    raise HTTPException(
                        status_code=500, detail="Encountered an issue while updating user usage data due to a Internal error.")

                # Return the updated user API usage data
                return {
                    "detail": "User API usage data updated successfully",
                    "user_id": iUserId,
                    "api_requested": mUserApiUsage.api_requested,
                }
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update user API usage data for user_id: {iUserId}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request.")

    @staticmethod
    async def MSUpdateUserPageLimit(iUserId: int, strPlanType: str, iPageLimit: int, strOperation: str, total_allowed_page_limit:int = None, total_allowed_free_page_limit:int = None, active_plan_name:str = None, is_subscription_active: bool= None) -> dict:
        '''
        Purpose : This method is used to update the API usage data for a user.

        Inputs  :   (1)     iUserId    :        The ID of the user (integer)
                    (2)     iPageLimit :        The number of pages to modify the limit by.
                    (3)     strOperation :      Specifies the type of operation ('add' or 'subtract').
                    
        Output  :   dict: Contains details of the updated user API usage data including the new page limit.

        Example :
                await CUserAPIUsageData.MSUpdateUserPageLimit(123, 5, 'add') -> {'detail': 'User's page limit updated successfully', ...}
        '''
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Process of Updating Users api usage Started.")
            async with AsyncSessionLocal() as db:  # Use your actual async session maker

                # Attempt to fetch the user's API usage data
                result = await db.execute(select(UserAPIUsage).filter(UserAPIUsage.user_id == iUserId))
                mUserApiUsage = result.scalars().first()

                # If the user's API usage record does not exist, raise an error
                if not mUserApiUsage:
                    await CLogController.MSWriteLog(iUserId, "Error", f"User API usage data not found for user_id: {iUserId}")
                    raise HTTPException(
                        status_code=404, detail="Oops! Your registration didn't go through. Please try again or reach out to our support for help")

                if strPlanType == "pro":
                    if strOperation == 'add':
                        mUserApiUsage.page_limit_left += iPageLimit
                    elif strOperation == 'subtract':
                        mUserApiUsage.page_limit_left = max(mUserApiUsage.page_limit_left - iPageLimit, 0)
                    else:
                        raise ValueError("Something went wrong. Try Again!!!")

                    if total_allowed_page_limit is not None and isinstance(total_allowed_page_limit,int):
                        mUserApiUsage.total_allowed_page_limit = total_allowed_page_limit
                
                elif strPlanType == "standard":
                    if strOperation == 'add':
                        mUserApiUsage.free_page_limit_usage += iPageLimit
                    elif strOperation == 'subtract':
                        mUserApiUsage.free_page_limit_usage = max(mUserApiUsage.page_limit_left - iPageLimit, 0)
                    else:
                        raise ValueError("Something went wrong. Try Again!!!")

                    if total_allowed_free_page_limit is not None and isinstance(total_allowed_free_page_limit,int):
                        mUserApiUsage.total_allowed_free_page_limit = total_allowed_free_page_limit
                    
                    
                # 1. active plan name change when user upgrade from Free -> Premium plans , 2. Subscription gets cancel 
                if active_plan_name is not None and isinstance(active_plan_name,str):
                    mUserApiUsage.active_plan_name = active_plan_name
                # 1. is_subscription_active true  when user upgrade from Free -> Premium plans , 2. Subscription gets cancel then value would be false 
                if is_subscription_active is not None and isinstance(is_subscription_active, bool):
                    mUserApiUsage.is_subscription_active = is_subscription_active
                    
                try:
                    # Commit the changes
                    await db.commit()
                    await CLogController.MSWriteLog(iUserId, "Info", f"User page limit successfully updated for user_id: {iUserId} | Extraction Pro Mode: Used Limit={int(mUserApiUsage.total_allowed_page_limit)-int(mUserApiUsage.page_limit_left)}, Total Limit={mUserApiUsage.total_allowed_page_limit} | Extraction Lite Mode: Used Limit={int(mUserApiUsage.free_page_limit_usage)}, Total Limit={mUserApiUsage.total_allowed_free_page_limit} | Given Input: iUserId: int, strPlanType={strPlanType}, iPageLimit={iPageLimit}, strOperation= {strOperation}, total_allowed_page_limit={total_allowed_page_limit}, total_allowed_free_page_limit={total_allowed_free_page_limit}, active_plan_name= {active_plan_name}, is_subscription_active= {is_subscription_active} ")
                except SQLAlchemyError as e:
                    # If there is an error during commit, rollback the session
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserId, "Error", f"An Error occured in database for user_id: {iUserId} while updating page limit.")
                    raise HTTPException(
                        status_code=500, detail="An error occurred while updating your limit usage. Please try again or reach out to our support for help")

                # Return the updated user API usage data
                return {
                    "detail": "User page limit updated successfully",
                    "user_id": iUserId,
                    "page_limit_left": mUserApiUsage.page_limit_left,
                    "total_allowed_page_limit":mUserApiUsage.total_allowed_page_limit,
                    "free_page_limit_usage" : mUserApiUsage.free_page_limit_usage,
                    "total_allowed_free_page_limit" : mUserApiUsage.total_allowed_free_page_limit,
                    "is_subscription_active":mUserApiUsage.is_subscription_active,
                    "active_plan_name":mUserApiUsage.active_plan_name
                }
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except ValueError as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Invalid operation specified. Use 'add' or 'subtract' for user_id: {iUserId} while updating page limit.")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                        status_code=500, detail="An error occurred while updating your limit usage. Please try again or reach out to our support for help")

        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update User page limit for user_id: {iUserId}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request.")


    @staticmethod
    async def MSResetApiRequestedForAllUser(iUserId: int) -> None:
        '''
        Purpose : This method is used to reset the api_requested field to 0 for a specific user.

        Inputs  :   (1)     iUserId   :   The ID of the user (integer)

        Output  : None. It performs an update operation on the database.

        Example : await CUserAPIUsageData.MSResetApiRequestedForUser(iUserId=123)
        '''
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Process of Resetting All Users api started")

            async with AsyncSessionLocal() as db:

                # Start a transaction
                async with db.begin():
                    # Update the api_requested field for a specific user
                    await db.execute(
                        update(UserAPIUsage)
                        .where(UserAPIUsage.user_id == iUserId)
                        .values(api_requested=0)
                    )
                    # Commit the transaction
                    await db.commit()
                await CLogController.MSWriteLog(iUserId, "Info", f"Process of Resetting All Users api Successfully Completed.")

        except SQLAlchemyError as e:
            # Rollback in case of error
            await db.rollback()

            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to Reset api Requested for All user")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="Unable to complete requests due to a Internal error.")
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to Reset api Requested for All user")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request.")

    @staticmethod
    async def MSUpdateUserApiTokenUsage(iUserId: int, iAdditionalTokens: int):
        '''
        Purpose : This method is used to update the API usage data for a user.

        Inputs  :   (1)     iUserId             :   The ID of the user (integer)
                    (2)     iAdditionalTokens   :   Additional tokens to add (integer)

        Output  : It returns a dictionary with the details of the updated user API usage data.

        Example : await CUserAPIUsageData.MSUpdateUserApiTokenUsage(iUserId=123, iAdditionalTokens=10)
        '''
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Process of Updating Users api usage Started.")
            async with AsyncSessionLocal() as db:  # Use your actual async session maker

                # Attempt to fetch the user's API usage data
                result = await db.execute(select(UserAPIUsage).filter(UserAPIUsage.user_id == iUserId))
                mUserApiUsage = result.scalars().first()

                # If the user's API usage record does not exist, raise an error
                if not mUserApiUsage:
                    await CLogController.MSWriteLog(iUserId, "Error", f"User API usage data not found for user_id: {iUserId}")
                    raise HTTPException(
                        status_code=404, detail="User usage data not found.")

                # Update the used_tokens and api_requested fields with new values
                mUserApiUsage.used_tokens += iAdditionalTokens

                try:
                    # Commit the changes
                    await db.commit()
                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully Updated User API usage data for user_id: {iUserId}")

                except SQLAlchemyError as e:
                    # If there is an error during commit, rollback the session
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update user API usage data for user_id: {iUserId}")
                    await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise HTTPException(
                        status_code=500, detail="Failed to update user usage data due to a Internal error.")

                # Return the updated user API usage data
                return {
                    "detail": "User API usage data updated successfully",
                    "user_id": iUserId,
                    "used_tokens": mUserApiUsage.used_tokens,
                }
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except Exception as e:
            # Handle unexpected errors
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update user API usage data for user_id: {iUserId}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail=f"An unexpected error occurred.")

    async def get_single_user_api_usage(user_id: int):
        async with AsyncSessionLocal() as db:
            try:
                # Modify the select statement to also fetch the 'role' from the User table
                result = await db.execute(
                    select(UserAPIUsage, Role.RoleName).join(User, User.uid == UserAPIUsage.user_id).join(
                        Role, User.roleID == Role.Id).filter(UserAPIUsage.user_id == user_id)
                )

                # This will now contain both UserAPIUsage and User.role data
                user_api_usage_data = result.first()

                if not user_api_usage_data:
                    await CLogController.MSWriteLog(user_id, "Error", f"User not found for user_id: {user_id}")
                    raise HTTPException(
                        status_code=404, detail="User not found.")

                user_api_usage, user_role = user_api_usage_data

                # Construct the response to match UserAPIUsageSchema and include 'role'
                mUserApiUsageData = {
                    "user_id": user_api_usage.user_id,
                    "used_tokens": user_api_usage.used_tokens,
                    "api_requested": user_api_usage.api_requested,
                    "page_limit_left": user_api_usage.page_limit_left,
                    "total_allowed_page_limit":user_api_usage.total_allowed_page_limit,
                    "role": user_role,  # Add the role to the response data,
                    "is_subscription_active": user_api_usage.is_subscription_active,
                    "active_plan_name":user_api_usage.active_plan_name
                }

                await CLogController.MSWriteLog(user_id, "Info", f"Process of Fetching API Usage Data user_id: {user_id} Successfully Completed")

                return mUserApiUsageData
            
            except HTTPException as e:
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to get API usage data for user_id: {user_id}")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="A database error prevented retrieving the user's usage data.")
            except Exception as e:
                await CLogController.MSWriteLog(user_id, "Error", f"Failed to get API usage data for user_id: {user_id}")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail="An unexpected error occurred while processing the request.")

    @staticmethod
    async def MSGetAllUserApiUsage(iUserID: int) -> list:
        '''
        Purpose : This method is used to retrieve the API usage data for all users.

        Inputs  : None

        Output  : It returns a list of dictionaries, each containing a user's API usage data.

        Example : await CUserAPIUsageData.MSGetAllUserApiUsage()
        '''
        try:
            async with AsyncSessionLocal() as db:  # Replace with your actual session maker
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching API Usage Data For all users Started")

                result = await db.execute(select(UserAPIUsage))
                mUserApiUsages = result.scalars().all()

                if not mUserApiUsages:
                    await CLogController.MSWriteLog(iUserID, "Error", f"No Users not found.")
                    raise HTTPException(
                        status_code=404, detail="No user API usage data found.")

                # Construct the response for each user API usage
                mResponseData = [
                    {
                        "user_id": mUserApiUsage.user_id,
                        "used_tokens": mUserApiUsage.used_tokens,
                        "api_requested": mUserApiUsage.api_requested
                        # Add any additional fields as needed
                    }
                    for mUserApiUsage in mUserApiUsages
                ]

                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching API Usage Data For All Users Successfully Completed")

                # Convert each dictionary to your schema if needed, or return the list directly
                return mResponseData
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to get API usage data for All Users")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="A database error prevented retrieving the API usage data for all users.")
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to get API usage data for All Users")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise HTTPException(
                status_code=500, detail="An unexpected error occurred while processing the request.")
