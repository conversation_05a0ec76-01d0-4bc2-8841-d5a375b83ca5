{"<EMAIL>": {"metadata": {"provider": "zoho", "password": "WSuNegdfaGA5"}, "clients": {"TestSimpolo": {"senderList": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "dataDirectory": "H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyDataTest", "userId": 2, "modelName": "<PERSON><PERSON><PERSON>", "modelFamilyName": "Custom", "backupDeviceName": "INDIA", "TallyHeader": {"X-Auth-Key": "live_e4fa57395a08401faa1b27b8d538d21e", "CompanyName": "PARAG TRADERS (GST)", "Template-Key": "7", "AddAutoMaster": "0", "Automasterids": "1,2,3", "version": "1", "Content-Type": "application/json"}, "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "MaxRetryCount": 3}}}}