from src.Schemas.Log_Schema import GetAllLogFilterQuery
from config.db_config import AsyncSessionLocal
from src.Models.models import UserLogs  # Make sure to import your UserAPIUsage model
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, Query
import inspect
from sqlalchemy import select, func, cast, Date, or_
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
import math
from src.Controllers.Logs_Controller import CLogController
import traceback
from src.utilities.date_helper import CDateHelper

class CUserLogController:
    
    @staticmethod
    async def MSWriteLog(iUserID, strLogType, strLogMessage, strSection):
        """
            Input
                strLogType : Type of Log, "Information" OR "Debug" OR "Warning" OR "Error"
                strLogMessage: Log Message
                strSection: Page for which log is created

            Output
                None

            Purpose
                To write logs into database

            Example
                MSWriteLog(objLog)  --> Writes UserLogs in database
        """
        try:
            objCallingFrame = inspect.currentframe().f_back
            strCallerFunctionName = inspect.getframeinfo(objCallingFrame).function
            iLineNumber = inspect.getframeinfo(objCallingFrame).lineno
            strFileName = objCallingFrame.f_code.co_filename
            
            strFormattedFunctionName = f"{strFileName}   -->   {strCallerFunctionName}   -->   {iLineNumber}"
            async with AsyncSessionLocal() as db:  # Make sure AsyncSessionLocal is correctly imported
                # Convert Pydantic schema to SQLAlchemy model
                objLog = UserLogs(
                    UserID=iUserID,
                    LogType=strLogType,
                    LogMessage=strLogMessage,
                    Section=strSection,
                )

                db.add(objLog)
                await db.commit()
                await db.refresh(objLog)

                # Return the inserted data
                return objLog
        except SQLAlchemyError as e:
            await db.rollback()
            raise HTTPException(
                status_code=500, detail="Something Went Wrong , Please try again later!")

        except Exception as e:
            raise HTTPException(
                status_code=e.status_code, detail=e.detail)

    @staticmethod
    async def MSGetAllLogs(user_id: int, hasAdminRights: bool, page: int = 1, per_page: int = None, filterQuery: GetAllLogFilterQuery = None):
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(user_id, "Info", "Fetching User Usage Logs.")
                
                query = select(UserLogs)

                # Filter by user_id if the user does not have admin rights
                if user_id and not hasAdminRights:
                    query = query.filter(UserLogs.UserID == user_id)

                # Apply additional filters from filterQuery
                if filterQuery:
                    query = CUserLogController.apply_filters(query, filterQuery, hasAdminRights)
                    query = CUserLogController.apply_ordering(query, filterQuery)

                # Calculate total logs count
                total_logs_count = await db.scalar(select(func.count()).select_from(query.subquery()))

                # Pagination
                if per_page is not None:
                    query = query.offset((page - 1) * per_page).limit(per_page)

                logs = await db.execute(query)
                logs = logs.all()
                
                await CLogController.MSWriteLog(user_id, "Info", f"len Logs: {len(logs)}, page: {page}, total pages {math.ceil(total_logs_count / per_page) if per_page else 1}")

                return CUserLogController.format_response(logs, total_logs_count, page, per_page)
            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to Fetch All Logs.")
                raise HTTPException(status_code=500, detail="A database error occurred while retrieving logs.")
            except Exception as e:
                await CLogController.MSWriteLog(user_id, "Error", "Failed to Fetch All Logs.")
                await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail="An unexpected error occurred while retrieving logs.")

    @staticmethod
    def apply_filters(query, filterQuery, hasAdminRights):
        if filterQuery.iUserId is not None:
            query = query.filter(UserLogs.UserID == filterQuery.iUserId)
        if filterQuery.strLogType is not None:
            query = query.filter(UserLogs.LogType == filterQuery.strLogType)
        if filterQuery.strSection is not None:
            query = query.filter(UserLogs.Section == filterQuery.strSection)
        if filterQuery.strSearch is not None:
            search_term = f"%{filterQuery.strSearch}%"
            query = query.filter(
                or_(
                    UserLogs.LogMessage.ilike(search_term),
                    UserLogs.Section.ilike(search_term)
                )
            )
        if filterQuery.strStartdate is not None:
            start_date = filterQuery.strStartdate.date()  # Extract just the date component
            query = query.filter(cast(UserLogs.LogDateTime, Date) >= start_date)

        if filterQuery.strEnddate is not None:
            end_date = filterQuery.strEnddate.date()  # Extract just the date component
            query = query.filter(cast(UserLogs.LogDateTime, Date) <= end_date)
        
        return query

    @staticmethod
    def apply_ordering(query, filterQuery):
        if filterQuery.bLogDateTimeAsc is not None:
            query = query.order_by(UserLogs.LogDateTime.asc() if filterQuery.bLogDateTimeAsc else UserLogs.LogDateTime.desc())
        if filterQuery.bUserIdAsc is not None:
            query = query.order_by(UserLogs.UserID.asc() if filterQuery.bUserIdAsc else UserLogs.UserID.desc())
        if filterQuery.bSectionAsc is not None:
            query = query.order_by(UserLogs.Section.asc() if filterQuery.bSectionAsc else UserLogs.Section.desc())
        if filterQuery.bMessageAsc is not None:
            query = query.order_by(UserLogs.LogMessage.asc() if filterQuery.bMessageAsc else UserLogs.LogMessage.desc())
        return query

    @staticmethod
    def format_response(logs, total_logs_count, page, per_page):
        formatted_logs = [
            {
                "Id": log.UserLogs.Id,
                "LogType": log.UserLogs.LogType,
                "LogMessage": log.UserLogs.LogMessage,
                "LogDateTime":  CDateHelper.extract_edt_components(log.UserLogs.LogDateTime),
                "Section": log.UserLogs.Section,
                "UserID": log.UserLogs.UserID
            }
            for log in logs
        ]
        
        total_pages = (total_logs_count + per_page - 1) // per_page if per_page else 1

        response = {
            "logs": formatted_logs,
            "total": total_logs_count,
            "page": page,
            "per_page": per_page,
            "total_pages": total_pages
        }
        return response