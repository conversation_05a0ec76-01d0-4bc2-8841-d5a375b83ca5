{"type": "json_schema", "json_schema": {"name": "powergrace", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "SellerAddress": {"type": "string", "description": "Address of the seller"}, "SellerCity": {"type": "string", "description": "City where the seller is located"}, "SellerPincode": {"type": "string", "description": "Postal code of the seller's location"}, "DealerAddress": {"type": "string", "description": "Dealer or distributor address through whom the goods are sold or dispatched"}, "DealerCity": {"type": "string", "description": "City of the dealer or distributor"}, "DealerPincode": {"type": "string", "description": "Postal code of the dealer or distributor location"}, "SellerGSTIN": {"type": "string", "description": "GST Identification Number of the seller"}, "BuyerName": {"type": "string", "description": "Name of the buyer receiving the goods"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerGSTIN": {"type": "string", "description": "GST Identification Number of the buyer"}, "BuyerStateCode": {"type": "integer", "description": "State code of the buyer location"}, "DeliveryAddress": {"type": "string", "description": "Address where the goods are delivered"}, "DeliveryGSTIN": {"type": "string", "description": "GST Identification Number for the delivery address"}, "DeliveryStateCode": {"type": "integer", "description": "State code for the delivery location"}, "InvoiceNo": {"type": "string", "description": "Unique number of the invoice"}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued (format: ddmmyy)"}, "ReverseCharge": {"type": "string", "description": "Indicates if reverse charge is applicable ('Yes' or 'No')"}, "SupplyDate": {"type": "integer", "description": "Date  of supply (format: ddmmyy )"}, "PlaceOfSupply": {"type": "string", "description": "Location where the supply is made"}, "Transporter": {"type": "string", "description": "Name of the transporter handling the goods"}, "VehicleNo": {"type": "string", "description": "Vehicle registration number used for transportation"}, "LRNo": {"type": "integer", "description": "Lorry receipt number )"}, "LRDate": {"type": "integer", "description": "Lorry receipt date ( in format: ddmmyy)"}, "SalesPersonName": {"type": "string", "description": "Name of the salesperson handling the order"}, "SalesPersonContact": {"type": "string", "description": "Number of the salesperson handling the order"}, "TotalQty": {"type": "integer", "description": "Total Qty of all items"}, "TotalLessDiscAmount": {"type": "number", "description": "Total LessDiscAmount of all Items"}, "TotalAmount(Rs.)": {"type": "number", "description": "Total amount of all items"}, "CashDiscountRate": {"type": "number", "description": "Percentage of cash discount on the total amount"}, "CashDiscountAmount": {"type": "number", "description": "Amount of cash discount on the total amount"}, "SchemeDiscountRate": {"type": "number", "description": "Percentage of scheme discount on the total amount"}, "SchemeDiscountAmount": {"type": "number", "description": "Amount of scheme discount on the total amount"}, "InsuranceRate": {"type": "number", "description": "Percentage of insurance charges on the total amount"}, "InsuranceAmount": {"type": "number", "description": "Amount of insurance charges on the total amount"}, "TotalAmountBeforeTax": {"type": "number", "description": "Aggregate amount of all items before tax"}, "IGSTRate": {"type": "number", "description": "IGST rate for all items in the invoice"}, "IGSTAmount": {"type": "number", "description": "I_GST amount for all items in the invoice"}, "TotalGSTAmount": {"type": "number", "description": "Total amount for all items in the invoice"}, "RoundOff": {"type": "number", "description": "Amount to adjust the total for rounding off"}, "TotalInvoiceValue": {"type": "number", "description": "Total amount of the invoice including all taxes and adjustments"}, "TotalInvoiceValueInWords": {"type": "string", "description": "Total invoice value expressed in words"}, "GSTValueInWords": {"type": "string", "description": "Total GST amount expressed in words"}, "Bank1Name": {"type": "string", "description": "Name of the bank"}, "Bank1AccountNo": {"type": "string", "description": "Bank account number"}, "Bank1IFSCCode": {"type": "string", "description": "IFSC code of the bank branch"}, "Bank1BranchCode": {"type": "string", "description": "Branch code of the bank"}, "Bank2Name": {"type": "string", "description": "Name of the bank"}, "Bank2AccountNo": {"type": "string", "description": "Bank account number"}, "Bank2IFSCCode": {"type": "string", "description": "IFSC code of the bank branch"}, "Bank2BranchCode": {"type": "string", "description": "Branch code of the bank"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"ItemGroup": {"type": "string", "description": "The complete name of the item group formed by concatenating the prefix (e.g., 'POWER GRACE / POWER GRACE -') with the specific category (e.g., 'ADHESIVES MORTAR', 'SAND GROUTS'). Spaces or commas are removed during the concatenation."}, "ItemName": {"type": "string", "description": "Name and description of the item"}, "HSN/SAC": {"type": "string", "description": "Harmonized System of Nomenclature or Services Accounting Code of the item"}, "Pack": {"type": "integer", "description": "Packing information, such as weight or volume"}, "PackUnit": {"type": "string", "description": "Packing such as weight or volume Unit, like KG and  LTR"}, "MRP": {"type": "number", "description": "Maximum retail price of the item"}, "Qty": {"type": "integer", "description": "Quantity of the item purchased"}, "QtyUnit": {"type": "string", "description": "Quantity unit of the item purchased such as Bag,TIN,pouc or Buck,"}, "GSTRate": {"type": "number", "description": "Applicable GST percentage on the item"}, "GSTAmount": {"type": "number", "description": "GST amount applied on the item"}, "DPPrice": {"type": "number", "description": "Dealer price or discounted price"}, "Rate": {"type": "number", "description": "Rate per item before discounts"}, "LessDiscAmount": {"type": "number", "description": "Discount on the item (if applicable)"}, "Amount(Rs.)": {"type": "number", "description": "Total amount per item"}}, "required": ["ItemGroup", "ItemName", "HSN/SAC", "Pack", "PackUnit", "MRP", "Qty", "QtyUnit", "GSTRate", "GSTAmount", "DPPrice", "Rate", "LessDiscAmount", "Amount(Rs.)"], "additionalProperties": false}, "description": "List of items in the invoice with pricing details"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerPincode", "Dealer<PERSON><PERSON><PERSON>", "DealerCity", "DealerPincode", "SellerGSTIN", "BuyerName", "BuyerAddress", "BuyerGSTIN", "BuyerStateCode", "DeliveryAddress", "DeliveryGSTIN", "DeliveryStateCode", "InvoiceNo", "InvoiceDate", "Reverse<PERSON><PERSON>ge", "SupplyDate", "PlaceOfSupply", "Transporter", "VehicleNo", "LRNo", "LRDate", "SalesPersonName", "SalesPersonContact", "TotalQty", "TotalLessDiscAmount", "TotalAmount(Rs.)", "CashDiscountRate", "CashDiscountAmount", "SchemeDiscountRate", "SchemeDiscountAmount", "InsuranceRate", "InsuranceAmount", "TotalAmountBeforeTax", "IGSTRate", "IGSTAmount", "TotalGSTAmount", "RoundOff", "TotalInvoiceValue", "TotalInvoiceValueInWords", "GSTValueInWords", "Bank1Name", "Bank1AccountNo", "Bank1IFSCCode", "Bank1BranchCode", "Bank2Name", "Bank2AccountNo", "Bank2IFSCCode", "Bank2BranchCode", "Table"], "additionalProperties": false}}}