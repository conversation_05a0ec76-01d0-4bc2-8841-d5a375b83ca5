import csv
from datetime import datetime, timedelta, date
import json
import mimetypes
import os
import shutil
import socket
import tarfile
import traceback
from typing import List
import zipfile
from fastapi import HTTPException
import filetype
import pandas as pd
from src.utilities.helperFunc import Transaction<PERSON>pdater, ImprestReportGenerator, SplitMergedPDF
from src.Controllers.AbhinavInfrabuildController import CAbhinavInfrabuild, CAbhinavInfrabuildGRN
from src.Controllers.PODetailsController import CAVPOProcessingDetailsService
from src.Controllers.GRNDetailsController import CAVGRNProcessingDetailsService
from src.Controllers.GrnNoPITrackingController import CGrnNoPITracking
import platform
from src.Controllers.BankStatementsController import CProcessBankStatement
from src.Controllers.GPTResponse_controller import CGPTResponseData, DocumentProcessorUsingHTTPX, DocumentUploader
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.ParagTradersControllers import CAquant_XML, CGeberit_XML, CHansgrohe_XML, CKohler_XML, CNexion_XML, CQuotation, CSimpolo_XML, CToto_XML
from src.Controllers.TallyIndianInvoiceController import CTallyIndianInvoiceController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.GPTAPI import CGPTAPIResponse
from src.Schemas.Doc_Schema import FileSchema
from src.Schemas.Tally_Schemas import ClientDNReqObj, VoucherType
from src.utilities.AntivirusHelper import ClamAVScanner, VirusDetected
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.utilities.ExtractTextHelper import ExtractTextFromDoc
import TallyEmailSender as TallyReportSender
from src.utilities.helperFunc import CRegexMethod, CDirectoryHelper, CExcelHelper, CJSONFileReader, CommonHelper, DateHelper, Hashing, split_pdf_MultipleVendor, CAVXMLParser
from src.utilities.SplitAlgo import CSplitAlgo
from src.Controllers.AbhinavImprestJournalController import CImprestJournal
import rarfile
from src.utilities.BankStatementStatastics import MGetUserReport
from src.Controllers.AbhinavInfrabuildController import CAbhinavInfrabuild,CAbhinavInfrabuildGRN, CAbhinavInfrabuildMachineryGRN
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode
from src.Controllers.ImprestJournalDetailsController import CImprestJournalDetails
import getpass
import asyncio
from src.Controllers.CustomLogger import CLogger
import asyncio
import mimetypes
from httpx import AsyncClient
from enum import Enum
from pathlib import Path

DEVICE_NAME = socket.gethostname()  # Fetch the device name
IP_ADDRESS = socket.gethostbyname(DEVICE_NAME)  # Get the IP address of the device
# Fetch system user name
USER_NAME = getpass.getuser()
# NOTE; Debugging ON THIS FLAG
DEBUGINGMODE = False

class CIndianInvTallyController:

    _mStrTDLUserConfigPath = Path(r"resource/TDLUserConfig.json")
    _mReqReportPath = None
    _mPricelistReportPath = None
    _mstrClientResZipFile = None
    _msLsReportColumnsForPV_With_Inv = [  
                            'File Name',
                            'Received Date',
                            'Vendor Name',
                            'Invoice No',
                            'Invoice Date',
                            'Total Amount',
                            'Total Pages',
                            'Estimated Time Saved',
                            'Tally Punch-in Status',
                            'Pricelist Verified',
                            'Accuvelocity Comments']

    _msLsAdditionaDocColumnsForDeliveryNote = [
                                                "QuotationNumber",
                                                "ItemSRNO",
                                                "ClientName",
                                                "OrderTerms1",
                                                "OrderTerms2",
                                                "ClientAddress1",
                                                "ClientAddress2",
                                                "ClientAddress3",
                                                "ClientAddress4",
                                                "ClientState",
                                                "ClientCountry",
                                                "ClientPinCode",
                                                "ClientGSTRegistrationType",
                                                "ClientGSTIN",
                                                "CartageAmount"
                                            ]
    
    _msLsReportColumnsForDeliveryNote = [   
                                            "SR No",
                                            "Quotation Number",
                                            "Quotation Date",
                                            "Client Name",
                                            "AVTally Status",
                                            "AccuVelocity Comments"
                                        ]
    def __init__(self, documents, dictLsClientDocMetaData, dictExeUserDetails, CReqServerReceivedAt, strClientREQID: str, bScanForVirus=False, bIsMultivendorDoc=False, strSystemName="Unknown", strClientTallyUserName="Unknown", bTestMode=False, strVoucherType="Unknown", iUserID=None):
        # Assign all parameters to instance variables
        self.documents = documents
        self.dictTDLAttachmentDetails = dictLsClientDocMetaData
        self.dictExeUserDetails = dictExeUserDetails
        self.CReqServerReceivedAt = CReqServerReceivedAt
        self.strClientREQID = strClientREQID
        self.bScanForVirus = bScanForVirus
        self.bIsMultivendorDoc = bIsMultivendorDoc
        self.strSystemName = strSystemName
        self.strClientTallyUserName = strClientTallyUserName
        self.bTestMode = bTestMode
        self.strVoucherType = strVoucherType
        self.iUserID = iUserID
        self.Result = []
        dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)

        # Validate user ID and get data directory
        if str(iUserID) not in dictUsersConfig:
            raise HTTPException(status_code=404, detail="User ID not found in configuration.")

        self.dictAVClientConfig = dictUsersConfig[str(iUserID)]
        # You can also add any initial setup logic here
        print(f"CindianInvTallyController initialized for Client REQ ID: {self.strClientREQID}")

    async def MProcessIndianInvDocV2(self):
        try:
            CIndianInvTallyController.reset()
            CIndianInvTallyController.MSDecideServiceURL()
            CLogger.MCSetupLogging(strLogsDirPath=r"Logs\DocProcessV2")
            # Files Client Response - Report , Pricelist , Json , XML Response : use Self.Result
            strMultiVendorSplitAlgoZipFile = ""
            # config
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            # Validate user ID and get data directory
            if str(self.iUserID) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            client_config = dictUsersConfig[str(self.iUserID)]
            # Store Doc in Network Location
            # Create a deep copy
            # documents_copy = copy.deepcopy(documents)
            strClientRequestDir = self.dictExeUserDetails.get("ClientReqDir", r"")
            dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(  iUserId=self.iUserID, 
                                                                                        dictUsersConfig=dictUsersConfig, 
                                                                                        documents=self.documents,eVoucherType=self.strVoucherType, bDeveloperMode=self.bTestMode, bScanForVirus=self.bScanForVirus, bIsMultivendorDoc=self.bIsMultivendorDoc, strClientRequestDir = strClientRequestDir, strClientTallyUserName = self.strClientTallyUserName,strClientREQID=self.strClientREQID,
                                                                                        dictExeUserDetails = self.dictExeUserDetails)
            #Initialize Accuvelocity Report Parent Columns
            objAVReqDetail = await CAVRequestDetail.MSInsertAVRequestDetailsFromStoredDocs(dictStoreDocRes=dictStoreDocRes,
                strClientREQID=self.strClientREQID,
                iUserId=self.iUserID,
                strVoucherType=self.strVoucherType,
                bTestMode = self.bTestMode,
                dictExeUserDetails= self.dictExeUserDetails,
                bScanForVirus = True,
                bIsMultivendorDoc = self.bIsMultivendorDoc,
                strSystemName= self.strSystemName,User_UID=self.iUserID)
            
            # -------------------  Validate No of Request, No of Pages Allowed Per Daynote -------------------  
            current_date = datetime.now().strftime('%Y-%m-%d')
            dataDirectory = client_config["dataDirectory"]
            
            unknown_vendor_date_dir = os.path.join(dataDirectory, current_date) 
            zip_file_name = f"MultipleVendorSplitAlgo_{self.strClientREQID}.zip"
            
            strMultipleVendorSplitAlgoZipFile = os.path.join(unknown_vendor_date_dir, zip_file_name)
            dictUserValidationResponse = await CTallyController.MSValidateUserLimit(iUserId=self.iUserID, iPageLimit=self.dictExeUserDetails.get("iPageAllowedPerDay",50), iReqLimit=self.dictExeUserDetails.get("iRequestAllowedPerDay",100), strDate=current_date, iCurrReqPage = dictStoreDocRes.get("iNumPages"))
            
            bPageAllowProcess = dictUserValidationResponse.get('bPageAllowProcess', True)
            bReqAllow = dictUserValidationResponse.get('bReqAllow', True)
            iPageProcess = dictUserValidationResponse.get('iPageProcess', 0)
            iReqProcess = dictUserValidationResponse.get('iReqProcess', 0)
            iCurrPageReq = dictStoreDocRes.get("iNumPages")
            lsSplitAlgoFilesToSend = dictStoreDocRes.get("lsMultipleVendorEnableSplitDocs")
            # dictTDLAttachmentData = []
            if self.bIsMultivendorDoc and lsSplitAlgoFilesToSend:
                self.dictTDLAttachmentDetails = dictStoreDocRes.get("dictLsClientDocMetaData") # Single PDF (Multiple Vendor Merged PDF) == Splitted FileName base on VendorName details Stored to correctly Mapped with XML Attachment Solution
                
                strMultiVendorSplitAlgoZipFile = CDirectoryHelper.create_zip_file(lsSplitAlgoFilesToSend, destination_path= strMultipleVendorSplitAlgoZipFile)
                self.Result.extend(lsSplitAlgoFilesToSend)
                self.Result.append(strMultiVendorSplitAlgoZipFile)

            # Prepare message based on conditions
            if not bPageAllowProcess and not bReqAllow:
                detail = (
                    f"Your request could not be processed because:\n"
                    f"- You have exceeded the allowed page processed limit of {iPageProcess} pages per day.\n"
                    f"- Your current request to process {iCurrPageReq} pages exceeds the daily request limit by {iReqProcess} pages.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            elif not bPageAllowProcess:
                detail = (
                    f"Your request could not be processed because you have exceeded the allowed page processed limit of {iPageProcess} pages per day.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            elif not bReqAllow:
                detail = (
                    f"Your request could not be processed because your current request to process {iCurrPageReq} pages exceeds the daily request limit by {iReqProcess} pages.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            
            self.Result.extend(dictStoreDocRes.get("lsFilePath"))
            docs = dictStoreDocRes.get("Docs")
            pages_per_batch = None
            max_batch_connection = None
            # Create Batch Wise Task i.e. 50 Pages Per Batch
            
            if self.iUserID == 10: # Vedansh School
                
                try:
                    max_workers_per_client = self.dictAVClientConfig.get("BatchWiseProcessConfig",{}).get("max_worker_per_client", 4)

                    # Total pages from all documents
                    total_pages = sum(doc.PageCount for doc in docs if hasattr(doc, "PageCount"))

                    # Avoid division by zero
                    pages_per_batch = max(1, total_pages // max_workers_per_client)

                    # Cap it if needed (optional upper/lower bounds)
                    pages_per_batch = min(pages_per_batch, 100)  # don't go too high
                    max_batch_connection = max_workers_per_client  # as fixed per client
                except:
                    pages_per_batch = self.dictAVClientConfig.get("BatchWiseProcessConfig",{}).get("pages_per_batch", 50)
                    max_batch_connection = self.dictAVClientConfig.get("BatchWiseProcessConfig",{}).get("max_batch_connection", 5)
            else:
                pages_per_batch = 20
                max_batch_connection = 5
            
            print("Batcher -- Per Batch Pages : ", pages_per_batch)
            print("Extra max connection allow in One Batch ", max_batch_connection)
            lsBatches = CIndianInvTallyController.MSCreateBatches(docs, pages_per_batch = pages_per_batch, max_batch_connection = max_batch_connection)

            print("Prepared Batches Len: ", len(lsBatches))

            # Initiate Document Process -- Concurrently , max - 13 Workers 
            # call the helper:
            LsXMLFilePaths = await CIndianInvTallyController.process_batches_and_collect_xml(
                user_id            = self.iUserID,
                dfOrStrAdditionalDocData = dictStoreDocRes.get("AdditionalDocPath"),
                lsBatches          = lsBatches,
                strVoucherType     = self.strVoucherType,
                dictTDLAttachmentDetails = self.dictTDLAttachmentDetails,
                strClientREQID     = self.strClientREQID,
                bIsDeveloperMode   = self.bTestMode,
                max_workers        = 13,
                endpoint_url = CIndianInvTallyController._mstrDEBUGINGExtractService if DEBUGINGMODE else CIndianInvTallyController._mstrProductionExtractService
            )
            self.Result.extend(LsXMLFilePaths)
            # now you have one flat list of all returned XMLs ready for further processing
            CLogger.MCWriteLog("info", f"All XML paths collected: {LsXMLFilePaths}")
            # --------------------- Post Thrading Alog--------------------

            iTotalPagesProcessed =  0 
            iTotalTimeSavedMin = 0
            strTotalTimeSavedMin = "~0 min 0 secs"
            # ------------ Server Execution Time - Current Request ---------------- 
            dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                user_id=self.iUserID, 
                strClientReqID=self.strClientREQID,
                isUseRequestTime = True
            )
            strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
            
            # ------------- Current Request Processed Accountant Time Saved & Communilative Statistics ------------
            try:
                dictAPIReqStats = await CAVRequestDetail.MSGetStatistics(self.iUserID, self.strClientREQID)
                strTotalTimeSavedMin, iTotalTimeSavedMin, iTotalPagesProcessed = dictAPIReqStats.get("strTotalTimeSavedMin"), dictAPIReqStats.get("iTotalTimeSavedMin"), dictAPIReqStats.get("iTotalPagesProcessed")
                await CLogController.MSWriteLog(self.iUserID, "Debug", f"dictAPIReqStats : {dictAPIReqStats}")
            except Exception as e:
                await CLogController.MSWriteLog(self.iUserID, "Error", f"Failed to get the statistics, Error:{e}")
                await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                pass

            dictTallyUserConfig = {}
            try:
                dictTallyUserConfig = await CTallyController.MSGetTallyUserConfig(iUserID=self.iUserID)
            except Exception as e:
                pass

            iCummulativePagesProcessed = 0
            iCummulativeTimeSavedMinutes = 0
            # Update Total statistics in db
            try:
                # Update this 
                iCummulativePagesProcessed = dictTallyUserConfig.get("TotalPagesProcessed", None) + iTotalPagesProcessed
                iCummulativeTimeSavedMinutes = dictTallyUserConfig.get("TotalTimeSavedInMinutes", None) + iTotalTimeSavedMin
                await CTallyController.MSSetTallyUserConfig(iUserID=client_config['userId'],
                                                                                    iTotalPagesProcessed = iCummulativePagesProcessed,
                                                                                    iTotalTimeSaved = iCummulativeTimeSavedMinutes
                                                                                )
            except Exception as e:
                pass
            
            # User Readability Time Saved from Float or Int Type Convertion into String
            strFormattedTimeSavedTillNow = await CommonHelper.MSGetFormattedTime(iCummulativeTimeSavedMinutes)
            
            # Create AV Report 
            result = await CAVRequestDetail.MSExportAVRecordDetailsToExcel(strClientREQID=self.strClientREQID,strReqReportPath=CIndianInvTallyController._mReqReportPath)
            
            # Send EMAIL AV REPORT
            try:
                VoucherType = getattr(self.strVoucherType, "name",  self.strVoucherType) if self.strVoucherType else "Unknown Voucher Type"
                TallyReportSender.SendTallyNotificationEmail(csvReportPath=CIndianInvTallyController._mReqReportPath, 
                                                strReceiverName=client_config['customerName'] if not self.bTestMode else "AV DEVELOPER TEAM",
                                                strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not self.bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                                                strMailFrom=os.getenv('MAIL_FROM'), 
                                                lsMailTo=client_config['lsEmailReceivers'] if not self.bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                strServer=os.getenv('MAIL_SERVER'), 
                                                intPort=int(os.getenv('SMTP_PORT')), 
                                                strPassword=os.getenv('MAIL_PASSWORD'), 
                                                htmlTemplatePath=r"resource\TallyEmailTemplate.html", 
                                                lsCC=client_config['lsEmailCC'],
                                                strTotalPagesProcessedToday=iTotalPagesProcessed, 
                                                strTotalTimeSavedToday=strTotalTimeSavedMin,
                                                strTotalPagesProcessedTillNow=iCummulativePagesProcessed,
                                                strTotalTimeSavedTillNow=strFormattedTimeSavedTillNow,
                                                lsAttachmentPath=[CIndianInvTallyController._mPricelistReportPath,strMultipleVendorSplitAlgoZipFile] if strMultipleVendorSplitAlgoZipFile !="" else [CIndianInvTallyController._mPricelistReportPath],
                                                strSystemName=self.strSystemName,
                                                strExecutiontime=strExecutiontime
                                                
                                                )
            except Exception as e:
                print("ERROR --- In sending Client Tally Report", traceback.print_exc())
                pass

            strClientResZipFile = CDirectoryHelper.create_zip_file(self.Result, destination_path= CIndianInvTallyController._mstrClientResZipFile)
            
            if self.bScanForVirus:
                objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                
                if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                    raise VirusDetected(file_path=strClientResZipFile)
            
            return strClientResZipFile

        except VirusDetected as vd:
            await CLogController.MSWriteLog(self.iUserID, "Error", f"Virus Detected:{vd}")
            await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            try:
                TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not self.bTestMode else "AV DEVELOPER TEAM", 
                                                            strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                            lsMailTo=client_config['lsEmailReceivers'] if not self.bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                            lsCC=client_config['lsEmailCC'])
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")
            raise vd
        
        except Exception as e:
            print("Error Occur - ", traceback.print_exc())
            raise e

    async def process_batches_and_collect_xml(
        user_id: int,
        dfOrStrAdditionalDocData,
        lsBatches: List[List[FileSchema]],
        strVoucherType: str,
        dictTDLAttachmentDetails: dict,
        strClientREQID: str,
        bIsDeveloperMode: bool,
        max_workers: int = 13,
        endpoint_url: str = "http://************:9003/doc_exc_api/ProcessDocTallyXML"
    ) -> List[str]:
        try:
            """
            Calls the external ProcessDocTallyXML API in parallel over your page-based batches,
            and returns a flat list of all XML file paths that the server returns.
            """
            semaphore = asyncio.Semaphore(max_workers)
            LsXMLFilePaths: List[str] = []

            async with AsyncClient(timeout=1200) as client:
                async def call_one_batch(batch: List[FileSchema]):
                    files = [
                        ("docs", (doc.filename, await doc.read(), doc.content_type))
                        for doc in batch
                    ]

                    # Custom metadata for each file
                    doc_metadatas = [
                        {
                            "filename": doc.filename,
                            "HashCode": doc.HashCode,
                            "filePath": doc.filePath,
                            "DocVendorName": doc.DocVendorName,
                            "FileType": doc.FileType,
                            "strClientREQID": doc.strClientREQID,
                            "PageCount": doc.PageCount
                        }
                        for doc in batch
                    ]

                    # all data needs to be strings for form fields
                    form_data = {
                        "user_id": str(user_id),
                        "strVoucherType": str(strVoucherType.value if isinstance(strVoucherType, Enum) else strVoucherType),
                        "strClientREQID": strClientREQID,
                        "bIsDeveloperMode": json.dumps(bIsDeveloperMode),
                        "dfOrStrAdditionalDocData": json.dumps(dfOrStrAdditionalDocData),
                        "dictTDLAttachmentDetails": json.dumps(dictTDLAttachmentDetails),
                        "docMetadatas": json.dumps(doc_metadatas)
                    }

                    async with semaphore:
                        resp = await client.post(
                            endpoint_url,
                            data=form_data,     # ✅ form data instead of json=
                            files=files         # ✅ actual files
                        )
                        resp.raise_for_status()
                        return resp.json() # .get("xml_paths", [])

                # kick off all batch‐calls
                tasks = [asyncio.create_task(call_one_batch(batch)) for batch in lsBatches]
                results = await asyncio.gather(*tasks, return_exceptions=True)

            # flatten successes, log any errors
            for idx, res in enumerate(results, start=1):
                if isinstance(res, Exception):
                    # log but don’t stop the others
                    CLogger.MCWriteLog("error", f"Batch {idx} failed: {res}")
                else:
                    LsXMLFilePaths.extend(res)

            return LsXMLFilePaths
        except Exception as e:
            print("Error ", traceback.format_exc())

    async def MDocExecutor(self, dictFileData, dfOrStrAdditionalDocData, dictTDLAttachmentData=None):
        strCurrentXmlFilePath = None # Initialize to None in case it's not set
        try:
            # async with self.exec_semaphore:
            CLogger.MCWriteLog("info", f"Start MDocExecutor for REQID {self.strClientREQID} - {datetime.now()}")
            print(f"Start MDocExecutor for REQID {self.strClientREQID} - {datetime.now()}")
            # --- 1. Upload Document Process ---
            # start_upload_time = datetime.now()
            # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Upload Process - {start_upload_time}")
            # print(f"[{self.strClientREQID}] Start Upload Process - {start_upload_time}")
            # dictFileData = {} # Initialize dictFileData
            # if objFile:
            #     uploader = DocumentUploader(strVoucherType=self.strVoucherType)
            #     dictFileData = await uploader.upload_documentv2(
            #         user_id=self.iUserID,
            #         document=objFile,
            #         bAutoSelectModel=False,
            #         bUsePaidModel=True,
            #         objAdditionalDocDetails=dfOrStrAdditionalDocData,
            #         BReUseDocData=True
            #     )
            
            # end_upload_time = datetime.now()
            # upload_time_taken = (end_upload_time - start_upload_time).total_seconds()
            # CLogger.MCWriteLog("info", f"[{self.strClientREQID}] End Upload Process. Time taken: {upload_time_taken:.2f} seconds.")
            # print( f"[{self.strClientREQID}] End Upload Process. Time taken: {upload_time_taken:.2f} seconds.")
            # --- 2. Extract Document Process ---
            lsDictExtractionResult = []
            dictExtractAPIResponse = {}
            iDocID = dictFileData.get('DocId') # Safely get DocId

            if dictFileData.get('APIStatusCode') == 200 and iDocID:
                start_extract_time = datetime.now()
                CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
                print(f"[{self.strClientREQID}] Start Extract Document Process (DocID: {iDocID}) - {start_extract_time}")
                dictExtractAPIResponse = await CGPTResponseData.performDocumentExtractionFromDocumentID(
                    userid=self.iUserID,
                    document_id=iDocID,
                    isTrialPaidDocExtraction=True,
                    strClientREQID=self.strClientREQID,
                    strVoucherType=self.strVoucherType,
                    bIsDeveloperMode=self.bTestMode
                )
                end_extract_time = datetime.now()
                extract_time_taken = (end_extract_time - start_extract_time).total_seconds()
                CLogger.MCWriteLog("info", f"[{self.strClientREQID}] End Extract Document Process (DocID: {iDocID}). Time taken: {extract_time_taken:.2f} seconds.")
                print(f"[{self.strClientREQID}] End Extract Document Process (DocID: {iDocID}). Time taken: {extract_time_taken:.2f} seconds.")
            else:
                CLogger.MCWriteLog("warning", f"[{self.strClientREQID}] Document not uploaded successfully or no DocId. Skipping extraction.")
                print(f"[{self.strClientREQID}] Document not uploaded successfully or no DocId. Skipping extraction.")
                # Optionally return early or handle this case

            # --- 3. Call Tally API Step ---
            try:
                if dictExtractAPIResponse.get("APIStatusCode") == 200:
                    start_tally_time = datetime.now()
                    CLogger.MCWriteLog("info", f"[{self.strClientREQID}] Start Call Tally API Step (DocID: {iDocID}) - {start_tally_time}")
                    print(f"[{self.strClientREQID}] Start Call Tally API Step (DocID: {iDocID}) - {start_tally_time}")
                    objTallyIndianController = CTallyIndianInvoiceController(
                        user_id=self.iUserID,
                        doc_id=iDocID,
                        dictLsClientDocMetaData=dictTDLAttachmentData,
                        dictExtractedData=dictExtractAPIResponse.get("Document"), # Safely get Document
                        lsAllDocInfo=None,
                        strClientREQID=self.strClientREQID,
                        bTestMode=self.bTestMode,
                        strVoucherType=self.strVoucherType
                    )
                    TallyResult = await objTallyIndianController.MCallTallyAPI(
                        bRaiseError=False,
                        dictLsClientDocMetaData=dictTDLAttachmentData,
                        strPriceListReport=CIndianInvTallyController._mPricelistReportPath, # Assuming _mPricelistReportPath is a class attribute
                        ObjVoucherType=self.strVoucherType
                    )
                    
                    end_tally_time = datetime.now()
                    tally_time_taken = (end_tally_time - start_tally_time).total_seconds()
                    CLogger.MCWriteLog("info", f"[{self.strClientREQID}] End Call Tally API Step (DocID: {iDocID}). Time taken: {tally_time_taken:.2f} seconds.")
                    print(f"[{self.strClientREQID}] End Call Tally API Step (DocID: {iDocID}). Time taken: {tally_time_taken:.2f} seconds.")
                    strCurrentXmlFilePath = TallyResult.get("XMLFilePath")
                    if strCurrentXmlFilePath:
                        self.Result.append(strCurrentXmlFilePath)
                else:
                    await CLogController.MSWriteLog(self.iUserID, "Debug", f"dictExtractAPIResponse status not 200. Skipping Tally. Response: {dictExtractAPIResponse}")

            except Exception as e:
                await CLogController.MSWriteLog(self.iUserID, "Error", f"[{self.strClientREQID}] Failed to create the XML document, Error:{e}")
                await CLogController.MSWriteLog(self.iUserID, "Debug", f"[{self.strClientREQID}] Traceback: {str(traceback.format_exc())}")
                # Decide if you want to re-raise or just log and continue
                # For a critical failure, raising might be better.
                # For robustness, logging and returning a failure state might be preferred.
                pass # Currently just logs and continues

            final_return_path = strCurrentXmlFilePath if strCurrentXmlFilePath else "No XML file generated"
            CLogger.MCWriteLog("info", f"[{self.strClientREQID}] End MDocExecutor Process. Final XML Path: {final_return_path} - {datetime.now()}")
            print(f"[{self.strClientREQID}] End MDocExecutor Process. Final XML Path: {final_return_path} - {datetime.now()}")
            return final_return_path

        except Exception as e:
            # This outer catch block will catch errors from Upload, Extraction, or unexpected issues
            CLogger.MCWriteLog("critical", f"[{self.strClientREQID}] Error during MDocExecutor. Error: {e}")
            CLogger.MCWriteLog("critical", f"[{self.strClientREQID}] Traceback: {str(traceback.format_exc())}")
            print(f"[{self.strClientREQID}] Traceback: {str(traceback.format_exc())}")
            raise e # Re-raise the exception after logging for proper error propagation

    @staticmethod
    def MSCreateBatches(
        docs: List[FileSchema], 
        pages_per_batch: int = 50,
        max_batch_connection: int = 10
    ) -> List[List[FileSchema]]:
        """
        1) Greedily pack whole FileSchema docs into batches whose total PageCount
           does not exceed `pages_per_batch`.
        2) If a batch ends up having <= max_batch_connection docs, merge it into
           the previous batch instead of keeping it separate.
        """
        # --- Step 1: initial page-based batching ---
        raw_batches: List[List[FileSchema]] = []
        current_batch: List[FileSchema] = []
        current_pages = 0

        for doc in docs:
            pc = getattr(doc, "PageCount", 0) or 1

            # if adding this doc would overflow pages, seal the batch
            if current_pages + pc > pages_per_batch:
                if current_batch:
                    raw_batches.append(current_batch)
                current_batch = []
                current_pages = 0

            current_batch.append(doc)
            current_pages += pc

        # final trailing batch
        if current_batch:
            raw_batches.append(current_batch)

        # --- Step 2: merge any too-small batch into its predecessor ---
        merged_batches: List[List[FileSchema]] = []
        for batch in raw_batches:
            if (
                merged_batches  # there is a previous batch
                and len(batch) <= max_batch_connection
            ):
                # merge into previous
                merged_batches[-1].extend(batch)
            else:
                # start a new batch
                merged_batches.append(batch)

        return merged_batches

    @classmethod
    def reset(cls):
        """Reset all class variables to their initial values."""
        cls._mStrTDLUserConfigPath = Path(r"resource/TDLUserConfig.json")
        cls._mReqReportPath = None
        cls._mPricelistReportPath = None
        cls._mstrClientResZipFile = None
        cls._msLsReportColumnsForPV_With_Inv = [
            'File Name',
            'Received Date',
            'Vendor Name',
            'Invoice No',
            'Invoice Date',
            'Total Amount',
            'Total Pages',
            'Estimated Time Saved',
            'Tally Punch-in Status',
            'Pricelist Verified',
            'Accuvelocity Comments'
        ]
        cls._msLsAdditionaDocColumnsForDeliveryNote = [
            "QuotationNumber",
            "ItemSRNO",
            "ClientName",
            "OrderTerms1",
            "OrderTerms2",
            "ClientAddress1",
            "ClientAddress2",
            "ClientAddress3",
            "ClientAddress4",
            "ClientState",
            "ClientCountry",
            "ClientPinCode",
            "ClientGSTRegistrationType",
            "ClientGSTIN",
            "CartageAmount"
        ]
        cls._msLsReportColumnsForDeliveryNote = [
            "SR No",
            "Quotation Number",
            "Quotation Date",
            "Client Name",
            "AVTally Status",
            "AccuVelocity Comments"
        ]


    ### ---------------------------------- Service URL initialization-------------------------------------- 
    @staticmethod
    def MSDecideServiceURL():
        '''
            Purpose: To set the IP for services on Production
        '''
        # Debugging Config - DO NOT REMOVE 
        if IP_ADDRESS.split('.')[-1] == '20' or DEVICE_NAME == 'RIVER-DEV8':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9003/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9003/doc_exc_api"
        elif IP_ADDRESS.split('.')[-1] == '19' or DEVICE_NAME == 'TALLY-SER-2':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9002/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9002/doc_exc_api"
        elif IP_ADDRESS.split('.')[-1] == '23' or DEVICE_NAME == 'RIVER-DEV2':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9010/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9010/doc_exc_api"
        elif IP_ADDRESS.split('.')[-1] == '13' or DEVICE_NAME == 'TALLY-SERVER':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9001/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9001/doc_exc_api"
            CIndianInvTallyController._mstrDEBUGINGExtractService = "http://************:9003/doc_exc_api"
        elif IP_ADDRESS.split('.')[-1] == '15' or USER_NAME == 'Harshil':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9002/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9002/doc_exc_api"
        elif IP_ADDRESS.split('.')[-1] == '15' or DEVICE_NAME == 'DEV':
            CIndianInvTallyController._mstrProductionExtractService = "http://************:9010/doc_exc_api"
            CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9010/doc_exc_api"
        else:
            if platform.system().lower() == "linux":
                CIndianInvTallyController._mstrProductionExtractService = 'http://***************:9122/doc_exc_api'
                CIndianInvTallyController._mstrDevelopementExtractService = 'http://***************:9122/doc_exc_api'
            else:
                CIndianInvTallyController._mstrProductionExtractService = "http://************:9001/doc_exc_api"
                CIndianInvTallyController._mstrDevelopementExtractService = "http://************:9001/doc_exc_api"

    ### ---------------------------------- Create UploadDB: File Object --------------------------------------            
    @staticmethod
    async def MSProcessFile(filepath: str, filename: str, DocVendorName: str = "", strClientREQID:str = None):
        """
            Input:

                1) filepath: str
                The full path to the file to be processed.

                2) filename: str
                The name of the file (including extension) to be processed.

                3) DocVendorName: str, optional (default "")
                The name of the document vendor for any vendor-specific handling.

            Output:

                tuple:
                    int: The number of pages in the processed document.
                    str: The path where the processed file was saved.

            Purpose:

                To process a document file by:
                - Loading it from the specified filesystem path.
                - Counting the number of pages in the document.
                - Applying any vendor-specific transformations if DocVendorName is provided.
                - Saving the resulting file to a configured storage location.
                - Returning the page count and the saved file’s path.

        """
        iNumPages = 0
        saved_file = None

        with open(filepath, "rb") as f:
            file_data = f.read()

            # Detect content type based on file's data using filetype
            kind = filetype.guess(file_data)
            content_type = kind.mime if kind else "application/octet-stream"

            # Only run page count extraction if the document is a PDF
            if "pdf" in content_type.lower():
                dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=file_data)
                iNumPages += dictConvertedFileData.get("pageCount", 0)  # Increment the page count for PDFs
            else:
                iNumPages = 0  # Default to 0 pages for non-PDF files
                docSize = len(file_data)  # Default to file size for non-PDF files

            # Save the file
            if strClientREQID is not None:
                saved_file = FileSchema(file=file_data, filename=filename, content_type=content_type, data=file_data, filePath=filepath, DocVendorName = DocVendorName, FileType = content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=filepath), strClientREQID=strClientREQID, PageCount =iNumPages)
            else:
                saved_file = FileSchema(file=file_data, filename=filename, content_type=content_type, data=file_data, filePath=filepath, DocVendorName = DocVendorName, FileType = content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=filepath), PageCount =iNumPages)
        return iNumPages, saved_file

    ### ---------------------------------- AV Report Initialization --------------------------------------
    @staticmethod
    async def MSInitializeReport(userid, strCsvReportPath, lsColumns):
        """
            Input:

                1) userid: int
                The ID of the user for whom the report is being generated.

                2) strCsvReportPath: str
                The filesystem path to the CSV report file.

                3) lsColumns: list
                A list of column names to include as headers in the CSV.

            Output:

                bool:
                    True if the CSV file was created and headers written;
                    False if the file already existed.

            Purpose:

                To ensure a CSV report file exists with the proper header row by:
                - Checking if the file at `strCsvReportPath` already exists.
                - If not, creating the file and writing the `lsColumns` as the first (header) row.
                - Returning a flag indicating whether initialization occurred.
        """

        if not os.path.isfile(strCsvReportPath):

            # await self.log_to_db(None, "INFO", f"Creating new report file: {self.report_file}")
            with open(strCsvReportPath, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=lsColumns)
                writer.writeheader()
                
        else:
            pass
            # await self.log_to_db(None, "ERROR", f"Report file already exists: {self.report_file}")

    ### ----------------------------------AV Report Report Update --------------------------------------
    @staticmethod
    async def MSUpdateReport(strCsvReportPath, entry):
        """
            Input:

                1) strCsvReportPath: str
                The filesystem path to the CSV report file to be updated.

                2) entries: list
                A list of dictionaries, each representing a row to write, with keys:
                'File Name', 'Received Date', 'Vendor Name', 'Invoice No', 'Invoice Date',
                'Total Amount', 'Total Pages', 'Estimated Time Saved',
                'Tally Punch-in Status', 'Pricelist Verified', 'Accuvelocity Comments'.

            Output:

                bool:
                    True if the CSV file was successfully written with headers and rows;
                    False if an error occurred during the write operation.

            Purpose:

                To overwrite or create a CSV report file by:
                - Opening the file at `strCsvReportPath` in write mode with UTF-8 encoding.
                - Defining the fixed column headers for the report.
                - Writing the headers as the first row.
                - Writing all provided `entries` rows under the headers.
                - Returning a status flag indicating success or failure.
        """
        # Read existing entries
        entries = []
        bEntryAlreadyExist = False
        if os.path.isfile(strCsvReportPath):
            with open(strCsvReportPath, mode='r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # Check if the current row matches the entry by 'Invoice No' or 'File Name'
                    if (row['File Name'] == entry.get('File Name')):
                        if (row['Tally Punch-in Status'] != 'Success'):
                            # Update only provided fields and retain others
                            for key, value in entry.items():
                                if value is not None and value != "-":
                                    row[key] = value
                        bEntryAlreadyExist = True
                    
                    # Check if 'Tally Punch-in Status' is 'Skipped' and update 'Accuvelocity Comments'
                    if row['Tally Punch-in Status'] == 'Skipped' and row.get('Accuvelocity Comments', '').strip() in ["", "-"]:
                        row['Accuvelocity Comments'] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                    
                    entries.append(row)

        # If no matching entry was found, append the new entry
        if not bEntryAlreadyExist:
            if entry.get('Tally Punch-in Status') == 'Skipped' and entry.get('Accuvelocity Comments', '').strip() in ["", "-"]:
                entry['Accuvelocity Comments'] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            entries.append(entry)

        # Write back all entries
        with open(strCsvReportPath, mode='w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'File Name',
                'Received Date',
                'Vendor Name',
                'Invoice No',
                'Invoice Date',
                'Total Amount',
                'Total Pages',
                'Estimated Time Saved',
                'Tally Punch-in Status',
                'Pricelist Verified',
                'Accuvelocity Comments'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(entries)

        # await self.log_to_db(None, 'INFO', f"Report updated for file: {entry.get('Invoice No') or entry.get('File Name')}")

 ### ---------------------------------- AV Report - Quotaion Update --------------------------------------
    @staticmethod
    async def MSUpdateReportOfQuotatuon(strCsvReportPath, entries):
        """
            Input:

                1) strCsvReportPath: str
                The filesystem path to the CSV file to which quotation entries will be appended.

                2) entries: list
                A list of dictionaries, each representing a quotation row to append.

            Output:

                bool:
                    True if the entries were successfully appended (or file created and entries added);
                    False if an error occurred during the operation.

            Purpose:

                To ensure quotation entries are recorded by:
                - Checking if the CSV at `strCsvReportPath` exists.
                - If it does not exist, creating it and writing the header row.
                - Opening the file in append mode.
                - Writing each dictionary in `entries` as a new row.
        """
        try:
            # Define the keys to include in the CSV
            allowed_keys = ['SrNo', 'Code', 'Description', 'Amount', 'AVTally Status', 'AV_Comments']

            # Check if the CSV file exists
            file_exists = os.path.isfile(strCsvReportPath)

            # Use allowed_keys as fieldnames
            fieldnames = allowed_keys

            # Filter entries to include only allowed keys
            filtered_entries = [
                {key: entry.get(key, '') for key in allowed_keys}
                for entry in entries
            ]

            with open(strCsvReportPath, mode='w') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
                writer.writeheader()
                # Append each filtered entry in the list to the CSV
                for entry in filtered_entries:
                    writer.writerow(entry)

        except Exception as e:
            print(f"Error writing to CSV: {e}")

    ### ---------------------------------- AV Report - Purchase Order --------------------------------------
    @staticmethod
    def MSSavePOReport(data_list, file_path):
        """
            Input:

                1) data_list: list
                A list of dictionaries representing purchase order records. 
                Each dict may include keys like 'XMLFilePath' and 'Time_Statistics' which will be removed before saving.

                2) file_path: str
                The filesystem path to the CSV file to update or create.

            Output:

                None:
                    Prints a confirmation message on success or a warning if there is no data to write.

            Purpose:

                To persist purchase order data by:
                - Validating that there is data to write.
                - Removing non-report keys ('XMLFilePath', 'Time_Statistics') from each record.
                - Determining the CSV headers from the filtered data.
                - Checking if the target CSV exists and already has the correct headers.
                - Appending the filtered data to the CSV, writing headers if needed.
                - Informing the user of the successful update.

        """
        if not data_list:
            print("No data to write.")
            return
        
        # Remove 'XMLFilePath' key from each dictionary
        filtered_data = [
            {key: value for key, value in item.items() if key not in ["XMLFilePath", "Time_Statistics", "Items"]} 
            for item in data_list
        ]
        
        # Get the header from the first dictionary
        headers = list(filtered_data[0].keys())
        
        # Check if file exists and contains headers
        file_exists = os.path.isfile(file_path)
        file_has_headers = False
        
        if file_exists:
            with open(file_path, mode='r', encoding='utf-8') as file:
                reader = csv.reader(file)
                first_row = next(reader, None)
                if first_row and set(first_row) == set(headers):
                    file_has_headers = True
        
        # Write to CSV (append mode)
        with open(file_path, mode='a', newline='', encoding='utf-8') as file:
            writer = csv.DictWriter(file, fieldnames=headers)
            
            # Write header only if it's not already present
            if not file_has_headers:
                writer.writeheader()
            
            writer.writerows(filtered_data)
        
        print(f"CSV file updated successfully at {file_path}")

   ### ---------------------------------- Document Storing in Network Location-------------------------------------- 
    @staticmethod
    async def MSStoreDocInNetLocation(iUserId, dictUsersConfig, documents, eVoucherType: VoucherType, bDeveloperMode=False, bScanForVirus=True,bIsMultivendorDoc=False, strClientRequestDir = r"", strClientTallyUserName= "Developer",strClientREQID="",dictExeUserDetails = {}):
        """
            Input:

                1) iUserId: int
                The ID of the user for whom documents are being stored.

                2) dictUsersConfig: dict
                Configuration settings for all users, including network paths and permissions.

                3) documents: list
                A list of document objects (e.g., dicts with filename and content) to store.

                4) eVoucherType: VoucherType
                The type of voucher for these documents, determining storage subfolder.

                5) bDeveloperMode: bool, optional (default False)
                If True, enables verbose logging and skips production safeguards.

                6) bScanForVirus: bool, optional (default True)
                If True, scans each document for viruses before saving.

                7) bIsMultivendorDoc: bool, optional (default False)
                If True, splits or tags documents intended for multiple vendors.

                8) strClientRequestDir: str, optional
                A client-specified subdirectory for request-specific files.

                9) strClientTallyUserName: str, optional (default "Developer")
                The Tally user name under which client operations are recorded.

            10) strClientREQID: str, optional
                A unique identifier for the client’s request, used for tracking.

            Output:

                dict:
                    A dictionary containing:
                    - "Docs": list of saved document metadata.
                    - "lsFilePath": list of saved file system paths for client result files.
                    - "AdditionalDocPath": str path for any extra documents stored.
                    - "iNumPages": int total number of pages across all documents.
                    - "lsBankStatementFiles": list of bank statement file paths.
                    - "ReportFilePath": str path to the generated summary report.
                    - "DataDirectory": str path to the date-based storage directory.
                    - "lsMultipleVendorEnableSplitDocs": list of paths for split vendor documents.
                    - "lsPOFiles": list of purchase order file paths.
                    - "lsGRNFiles": list of GRN file paths.
                    - "dictLsClientDocMetaData": dict of modified client document metadata.

            Purpose:

                To process and store a batch of documents for a given user by:
                - Validating the user’s configuration and permissions.
                - Optionally scanning each document for viruses.
                - Handling multivendor documents if required.
                - Saving files into a structured network location based on voucher type and date.
                - Generating ancillary files (bank statements, PO/GRN extracts, summary report).
                - Collecting and returning metadata about all saved files and processing statistics.
        """
        
        CIndianInvTallyController.MSDecideServiceURL()
        strCustomerName = dictUsersConfig[str(iUserId)]['customerName'] if not bDeveloperMode else "AVDEVELOPER"
        lsMultipleVendorEnableSplitDocs = [] # FilePath
        iNumPages = 0
        lsClientResFiles = []
        lsBankStatementFiles = []
        lsPOFiles = []
        lsGRNFiles=[]
        lsAbhinavImprestFiles = []
        strAdditionalDocPath = None
        dictModifiedClientDocMetaData = {}
        strReportStatisticFilePath=""
        
        if dictExeUserDetails:
            bSmartVendorDetectAlgo = dictExeUserDetails.get("bSmartVendorDetectAlgo",False)
            iSplitPages = dictExeUserDetails.get("iSplitPages", 1)
    
        
        strVoucherType = eVoucherType.value
        # NOTE: GENERALIZE_PWI_V3
        bGeneralizeApproach = CBusinessIntelligence.MSBIsGeneralizeVCHType(iUserId = iUserId, strVoucherType = strVoucherType)
        # bRequestDocType = strRequestDocType.lower() == "multiple_vendor" # True when multiple vendors in single document
        # Validate user ID and get data directory
        if str(iUserId) not in dictUsersConfig:
            raise HTTPException(status_code=404, detail="User ID not found in configuration.")

        data_directory = dictUsersConfig[str(iUserId)]['dataDirectory']
        strcompanyname=dictUsersConfig[str(iUserId)]['company_name']
        os.makedirs(data_directory, exist_ok=True)
        if not os.path.exists(data_directory):
            raise HTTPException(status_code=500, detail="Data directory not found.")

        objTodaysDate = datetime.now().strftime("%Y_%m_%d")
        current_date_dir = os.path.join(data_directory, objTodaysDate)
    
        if bScanForVirus:
            objAVScanner = ClamAVScanner(log_dir=current_date_dir, quarantine_dir=os.path.join(current_date_dir, "QuarantinedFiles"))
    
        # Create current date directory
        if eVoucherType == VoucherType.DELIVERY_NOTE:
            current_date_dir = os.path.join(current_date_dir, "DeliveryNote")
        elif eVoucherType == VoucherType.PURCHASE_ORDER:
            current_date_dir = os.path.join(current_date_dir, "PurchaseOrder")
        elif eVoucherType == VoucherType.RECEIPT_NOTE:
            current_date_dir = os.path.join(current_date_dir, "ReceiptNote")

        timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
        strReportDirectoryPath = os.path.join(current_date_dir, "Reports")
        os.makedirs(strReportDirectoryPath, exist_ok=True)

        strReportFilePath = os.path.join(strReportDirectoryPath, f"Report_{timestamp}_{eVoucherType.value.replace('_', '-')}_V1.csv")
        lsClientResFiles.append(strReportFilePath)

        if str(eVoucherType) == str(VoucherType.PV_WITH_INVENTORY):
            strPricelistReportFilePath = os.path.join(strReportDirectoryPath, f"PriceList_Report_{timestamp}.xlsx")
            lsClientResFiles.append(strPricelistReportFilePath)
            CIndianInvTallyController._mPricelistReportPath = strPricelistReportFilePath
        else:
            CIndianInvTallyController._mPricelistReportPath = None

        CIndianInvTallyController._mstrClientResZipFile = os.path.join(current_date_dir, f"ClientResponse_{timestamp}.zip")
        if eVoucherType == VoucherType.BANK_STATEMENT: 
            CIndianInvTallyController._mstrClientBankResZipFile = os.path.join(current_date_dir, f"BankStatement{dictUsersConfig[str(iUserId)]['bankcustomerName']}_{timestamp}.zip")
            strReportStatisticFilePath = os.path.join(strReportDirectoryPath, f"Report_Statistic_{timestamp}_{eVoucherType.value.replace('_', '-')}_V1.csv")
            lsClientResFiles.append(strReportStatisticFilePath)
        
        CIndianInvTallyController._mReqReportPath = strReportFilePath

        if str(eVoucherType) == str(VoucherType.DELIVERY_NOTE):
            lsColumnsToInitialize = CIndianInvTallyController._msLsReportColumnsForDeliveryNote
        elif str(eVoucherType) == str(VoucherType.PURCHASE_ORDER):
            lsColumnsToInitialize = []
        elif str(eVoucherType) == str(VoucherType.RECEIPT_NOTE):
            lsColumnsToInitialize = []
        else:
            lsColumnsToInitialize = CIndianInvTallyController._msLsReportColumnsForPV_With_Inv

        await CIndianInvTallyController.MSInitializeReport(userid=iUserId, strCsvReportPath=strReportFilePath, lsColumns=lsColumnsToInitialize)

        saved_files: List[FileSchema] = []

        for file in documents:
            base_filename, file_extension = os.path.splitext(file.filename)
            unique_file_path = os.path.join(current_date_dir, f"{base_filename}{file_extension}")

            if file_extension.lower() in ['.zip', '.7z', '.rar', '.tar']:
                zip_temp_path = os.path.join(current_date_dir, f"{base_filename}_temp{file_extension}")
                with open(zip_temp_path, "wb") as f:
                    f.write(await file.read())
                
                if bScanForVirus:
                    dictScanResult = objAVScanner.scan(input_data=zip_temp_path)
                    if dictScanResult and dictScanResult["Scanned"] and dictScanResult["Infected"]:
                        raise VirusDetected(file_path=zip_temp_path)

                extract_files = []

                if file_extension.lower() == '.zip':
                    with zipfile.ZipFile(zip_temp_path, 'r') as zip_ref:
                        zip_ref.extractall(current_date_dir)
                        extract_files = zip_ref.namelist()

                elif file_extension.lower() == '.rar':
                    with rarfile.RarFile(zip_temp_path, 'r') as rar_ref:
                        rar_ref.extractall(current_date_dir)
                        extract_files = rar_ref.namelist()

                elif file_extension.lower() == '.tar':
                    with tarfile.open(zip_temp_path, 'r') as tar_ref:
                        tar_ref.extractall(current_date_dir)
                        extract_files = [member.name for member in tar_ref.getmembers()]

                if str(eVoucherType) == str(VoucherType.BANK_STATEMENT):
                    for name in extract_files:
                        extracted_file_path = os.path.join(current_date_dir, name)
                        try:
                            # For Gwalia Additional Dashboards File Saving Parallel to Current Directory ------------------------------------------------
                            # Check for BharatPe file type based on the columns
                            if iUserId in [4, 7] and name.endswith(".csv"):
                                with open(extracted_file_path, mode='r', newline='') as csv_file:
                                    csv_reader = csv.DictReader(csv_file)
                                    headers = csv_reader.fieldnames

                                    # Check if this is the BharatPe file based on required columns
                                    required_columns = ['Store Name', ' Settled To', ' Date & Time', ' Bank UTR ID', ' Status', ' Amount']
                                    if set(required_columns).issubset(headers):
                                        # Read the first 5 rows and check if "Store Name" starts with 'Gwalia'
                                        rows = [row for i, row in enumerate(csv_reader) if i < 5]
                                        valid_file = all(row['Store Name'].startswith('Gwalia') for row in rows)

                                        if valid_file:
                                            # If it's the correct BharatPe file, rename it and store it
                                            # Assuming the 'test_gwalia' is a variable you already have or set it here
                                            current_date = datetime.now().strftime("%d_%m_%Y")
                                            current_time = datetime.now().strftime("%d_%m_%Y_%H_%M_%S")
                                            new_file_name = f"BharatPePayouts_{current_time}.csv"
                                            destination_dir = os.path.join("prodBankStatementPrediction", "data", strcompanyname, "BOB", "input_files","BharatPe", current_date)

                                            # Ensure the directory exists
                                            os.makedirs(destination_dir, exist_ok=True)

                                            # Save the BharatPe file at the destination
                                            new_file_path = os.path.join(destination_dir, new_file_name)
                                            
                                            with open(new_file_path, mode='w', newline='') as new_csv_file:
                                                csv_writer = csv.DictWriter(new_csv_file, fieldnames=headers)
                                                csv_writer.writeheader()
                                                csv_writer.writerows(rows)

                                            
                                            continue  # Skip to the next file without adding it to lsBankStatementFiles
                                # -------------------------------------------------------------------------------------------------------------------------------
                        except Exception as e:
                            print("!! Failed to save additional info for Gwalia. !!")
                        # If it's not BharatPe, add the extracted file to lsBankStatementFiles
                        lsBankStatementFiles.append(extracted_file_path)  
                else:
                    for name in extract_files:
                        extracted_file_path = os.path.join(current_date_dir, name)

                        if bScanForVirus:
                            dictScanResult = objAVScanner.scan(input_data=extracted_file_path)
                            if dictScanResult and dictScanResult["Scanned"] and dictScanResult["Infected"]:
                                raise VirusDetected(file_path=extracted_file_path)

                        content_type = mimetypes.guess_type(extracted_file_path)[0] or "application/octet-stream"
                        if (content_type in [
                                "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                                "application/vnd.ms-excel"
                            ]):
                            
                            if ("icd" in strCustomerName.lower().strip() or bDeveloperMode ) and "av_pegasus_additionaldetail" in name.lower().strip(): 
                                strAdditionalDocPath = extracted_file_path
                            
                            elif ("parag" in strCustomerName.lower().strip() or bDeveloperMode) and "av_quotation_additionaldetails" in name.lower().strip():
                                strAdditionalDocPath = extracted_file_path

                            elif ("abhinav infrabuild" in strCustomerName.lower().strip() or bDeveloperMode):
                                iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=name,filepath=extracted_file_path, DocVendorName="", strClientREQID=strClientREQID)
                                
                                iNumPages+= iCurrDocPages
                                saved_files.append(objFileSchema)
                                lsPOFiles.append(extracted_file_path)

                        # check pdf file, if Multiple vendor then code , else
                        if extracted_file_path.lower().endswith('.pdf'):
                            if bIsMultivendorDoc and bSmartVendorDetectAlgo:
                                # Main processing block (refactored)
                                dictMultipleVendorGPTResponse = None
                                strReqDocHashed = Hashing.calculate_checksum_from_file(extracted_file_path)
                                output_dir = current_date_dir
                                
                                dictMultipleVendorGPTResponse = await CSplitAlgo.SplitAlgoGetResponse(extracted_file_path, iUserId=iUserId, strReqDocHashed=strReqDocHashed, strClientREQID=strClientREQID, bDeveloperMode=bDeveloperMode, strCustomerName=strCustomerName)
                                lsSplittedVendorDoc = split_pdf_MultipleVendor(extracted_file_path, dictMultipleVendorGPTResponse, output_dir)
                                
                                for strAbsDocPath in lsSplittedVendorDoc:
                                    strFileName = os.path.basename(strAbsDocPath)
                                    lsMultipleVendorEnableSplitDocs.append(strAbsDocPath)
                                    if "unknown" in strFileName.lower() and not bGeneralizeApproach:
                                        pass
                                    else:
                                        iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=strFileName,filepath=strAbsDocPath, DocVendorName = strFileName.split("_")[0].lower(), strClientREQID=strClientREQID)
                                        iNumPages += iCurrDocPages
                                        saved_files.append(objFileSchema)
                                        strDocCheckSum = objFileSchema.HashCode
                                        StUploadTimePeriod = f"Upload on {datetime.now().strftime('%d-%b-%y at %H:%M')}"

                                        LinkDocName=eVoucherType.value
                                        LinkOffered = strClientRequestDir
                                        TDL = os.path.join(strClientRequestDir, strFileName)
                                        lsUdfData = [
                                            {
                                                        "LinkOfferedAgg": "AccuVelocity",
                                                        "LinkDocName": LinkDocName,
                                                        "stDocNameLineno": "1.",
                                                        "StUploadTimePeriod":StUploadTimePeriod,
                                                        "stUserName": strClientTallyUserName,
                                                        "LinkOffered": LinkOffered,
                                                        "stFullpath": strFileName,
                                                        "TDL": TDL
                                                    },
                                        ]
                    
                                        if strDocCheckSum !="": # Use CheckSum Base Attachment
                                            dictModifiedClientDocMetaData[strDocCheckSum] = lsUdfData
                            elif bIsMultivendorDoc and not bSmartVendorDetectAlgo:
                                # Main processing block (refactored)
                                dictMultipleVendorGPTResponse = None
                                strReqDocHashed =  Hashing.calculate_checksum_from_file(extracted_file_path)
                                output_dir = current_date_dir
                                StUploadTimePeriod = f"Upload on {datetime.now().strftime('%d-%b-%y at %H:%M')}"
                                output_dir = current_date_dir
                                
                                dictFilePath = SplitMergedPDF(extracted_file_path, current_date_dir, iSplitPages)
                                
                                for file_name,file_path in dictFilePath.items():
                                    try:
                                        lsMultipleVendorEnableSplitDocs.append(file_path)
                                        iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=file_name,filepath=file_path, DocVendorName="", strClientREQID=strClientREQID)
                                        iNumPages+= iCurrDocPages
                                        saved_files.append(objFileSchema)
                                        strDocCheckSum = objFileSchema.HashCode

                                        LinkDocName=eVoucherType.value
                                        LinkOffered = strClientRequestDir
                                        TDL = os.path.join(strClientRequestDir, file_name)
                                        lsUdfData = [
                                            {
                                                        "LinkOfferedAgg": "AccuVelocity",
                                                        "LinkDocName": LinkDocName,
                                                        "stDocNameLineno": "1.",
                                                        "StUploadTimePeriod":StUploadTimePeriod,
                                                        "stUserName": strClientTallyUserName,
                                                        "LinkOffered": LinkOffered,
                                                        "stFullpath": file_name,
                                                        "TDL": TDL
                                                    },
                                        ]
                                        if strDocCheckSum !="": # Use CheckSum Base Attachment
                                            dictModifiedClientDocMetaData[strDocCheckSum] = lsUdfData
                                    except Exception as e:
                                        await CLogController.MSWriteLog(iUserId, "Debug", f"Split Algo Particular Doc Error :{file_name} , file_path: {file_path}, Traceback: {str(traceback.format_exc())}")
                            else:
                                iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=name,filepath=extracted_file_path, DocVendorName="", strClientREQID=strClientREQID)
                                

                            
                                iNumPages+= iCurrDocPages
                                saved_files.append(objFileSchema)
                        

                os.remove(zip_temp_path)
            else:
                counter = 1
                while os.path.exists(unique_file_path):
                    unique_file_path = os.path.join(current_date_dir, f"{base_filename}_dup{counter}{file_extension}")
                    counter += 1
                # if not (("icd" in strCustomerName.lower().strip() or iUserId==4) and "av_pegasus_additionaldetail" in name.lower().strip()): 
                with open(unique_file_path, "wb") as f:
                    file_data = await file.read()
                    f.write(file_data)
                    if bIsMultivendorDoc and bSmartVendorDetectAlgo:
                        # read json file for api response format resource\MultipleVendorGPTResponseFormat.json
                        strReqDocHashed =  Hashing.calculate_checksum_from_file(unique_file_path)

                        output_dir=current_date_dir
                    
                        dictMultipleVendorGPTResponse = await CSplitAlgo.SplitAlgoGetResponse(unique_file_path, iUserId=iUserId, strReqDocHashed=strReqDocHashed, strClientREQID=strClientREQID, bDeveloperMode=bDeveloperMode, strCustomerName=strCustomerName)

                        lsSplittedVendorDoc = split_pdf_MultipleVendor(unique_file_path, dictMultipleVendorGPTResponse, output_dir)
                        
                        for strAbsDocPath in lsSplittedVendorDoc:
                            strFileName = os.path.basename(strAbsDocPath)
                            lsMultipleVendorEnableSplitDocs.append(strAbsDocPath)
                            # Do Not Skip Unknown Vendor File when Generalize Solution Approach Used
                            if "unknown" in strFileName.lower() and not bGeneralizeApproach:
                                pass
                            else:
                                iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=strFileName,filepath=strAbsDocPath, DocVendorName= strFileName.split("_")[0].lower(), strClientREQID=strClientREQID)
                                iNumPages += iCurrDocPages
                                saved_files.append(objFileSchema)
                                strDocCheckSum = objFileSchema.HashCode
                                StUploadTimePeriod = f"Upload on {datetime.now().strftime('%d-%b-%y at %H:%M')}"

                                LinkDocName=eVoucherType.value
                                LinkOffered = strClientRequestDir
                                TDL = os.path.join(strClientRequestDir, strFileName)
                                lsUdfData = [
                                    {
                                                "LinkOfferedAgg": "AccuVelocity",
                                                "LinkDocName": LinkDocName,
                                                "stDocNameLineno": "1.",
                                                "StUploadTimePeriod":StUploadTimePeriod,
                                                "stUserName": strClientTallyUserName,
                                                "LinkOffered": LinkOffered,
                                                "stFullpath": strFileName,
                                                "TDL": TDL
                                            },
                                ]
            
                                if strDocCheckSum !="": # Use CheckSum Base Attachment
                                    dictModifiedClientDocMetaData[strDocCheckSum] = lsUdfData
                    elif bIsMultivendorDoc and not bSmartVendorDetectAlgo:
                        # Main processing block (refactored)
                        dictMultipleVendorGPTResponse = None
                        strReqDocHashed =  Hashing.calculate_checksum_from_file(unique_file_path)
                        output_dir = current_date_dir
                        StUploadTimePeriod = f"Upload on {datetime.now().strftime('%d-%b-%y at %H:%M')}"

                        dictFilePath = SplitMergedPDF(unique_file_path, current_date_dir, iSplitPages)
                        
                        for file_name,file_path in dictFilePath.items():
                            try:
                                lsMultipleVendorEnableSplitDocs.append(file_path)
                                iCurrDocPages, objFileSchema = await CIndianInvTallyController.MSProcessFile(filename=file_name,filepath=file_path, DocVendorName="", strClientREQID=strClientREQID)
                                iNumPages+= iCurrDocPages
                                strDocCheckSum = objFileSchema.HashCode
                                saved_files.append(objFileSchema)
                                
                                LinkDocName=eVoucherType.value
                                LinkOffered = strClientRequestDir
                                TDL = os.path.join(strClientRequestDir, file_name)
                                lsUdfData = [
                                    {
                                                "LinkOfferedAgg": "AccuVelocity",
                                                "LinkDocName": LinkDocName,
                                                "stDocNameLineno": "1.",
                                                "StUploadTimePeriod":StUploadTimePeriod,
                                                "stUserName": strClientTallyUserName,
                                                "LinkOffered": LinkOffered,
                                                "stFullpath": file_name,
                                                "TDL": TDL
                                            },
                                ]
                                if strDocCheckSum !="": # Use CheckSum Base Attachment
                                    dictModifiedClientDocMetaData[strDocCheckSum] = lsUdfData
                            except Exception as e:
                                await CLogController.MSWriteLog(iUserId, "Debug", f"Split Algo Particular Doc Error :{file_name} , file_path: {file_path}, Traceback: {str(traceback.format_exc())}")
                    else:
                        # Detect content type based on file's data using filetype
                        kind = filetype.guess(file_data)
                        content_type = kind.mime if kind else "application/octet-stream"

                        # Only run page count extraction if the document is a PDF
                        if "pdf" in content_type.lower():
                            dictConvertedFileData = await ExtractTextFromDoc.MSGetPDFPageCount(fileBinary=file_data)
                            iNumPages += dictConvertedFileData.get("pageCount", 0)  # Increment the page count for PDFs
                        else:
                            iNumPages = 0  # Default to 0 pages for non-PDF files
                        if str(eVoucherType) == str(VoucherType.BANK_STATEMENT):
                            lsBankStatementFiles.append(unique_file_path)
                        elif str(eVoucherType) == str(VoucherType.PURCHASE_ORDER):
                            saved_file = FileSchema(file=file_data, filename=file.filename, content_type=file.content_type, data=file_data, filePath=unique_file_path, DocVendorName="", FileType = file.content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=unique_file_path), strClientREQID=strClientREQID, PageCount =iNumPages)
                            lsPOFiles.append(saved_file)
                        elif str(eVoucherType) == str(VoucherType.RECEIPT_NOTE) and (iUserId == 11 or iUserId == 4):
                        # elif str(eVoucherType) == str(VoucherType.RECEIPT_NOTE) and iUserId == 11:
                            saved_file = FileSchema(file=file_data, filename=file.filename, content_type=file.content_type, data=file_data, filePath=unique_file_path, DocVendorName="", FileType = file.content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=unique_file_path), strClientREQID=strClientREQID, PageCount =iNumPages)
                            lsGRNFiles.append(saved_file)       
                        elif str(eVoucherType) == str(VoucherType.JOURNAL_VOUCHER) and (iUserId == 11 or iUserId == 4): 
                            saved_file = FileSchema(file=file_data, filename=file.filename, content_type=file.content_type, data=file_data, filePath=unique_file_path, DocVendorName="", FileType = file.content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=unique_file_path), strClientREQID=strClientREQID, PageCount =iNumPages)
                            lsAbhinavImprestFiles.append(saved_file)
                        # elif str(eVoucherType) == str(VoucherType.RECEIPT_NOTE) and (iUserId == 11 or iUserId == 4):
                        #     lsGRNFiles.append(unique_file_path)
                        else:
                            saved_file = FileSchema(file=file_data, filename=file.filename, content_type=content_type, data=file_data, filePath=unique_file_path, DocVendorName="", FileType = content_type, HashCode=Hashing.calculate_checksum_from_file(file_path=unique_file_path), strClientREQID=strClientREQID, PageCount=iNumPages)
                            saved_files.append(saved_file)
        
        return {"Docs": saved_files, "lsFilePath": lsClientResFiles, "AdditionalDocPath": strAdditionalDocPath, "iNumPages": iNumPages, "lsBankStatementFiles":lsBankStatementFiles, "ReportFilePath":strReportFilePath, "DataDirectory": current_date_dir, "lsMultipleVendorEnableSplitDocs":lsMultipleVendorEnableSplitDocs, "lsPOFiles": lsPOFiles,"lsGRNFiles":lsGRNFiles, "dictLsClientDocMetaData":dictModifiedClientDocMetaData, "strReportStatisticFilePath":strReportStatisticFilePath, "lsAbhinavImprestFiles":lsAbhinavImprestFiles}

    ### ---------------------------------- CLIENT Tally Exported Stock Inventory XML -------------------------------------- MSStoreClientImportedXMLResponse
    @staticmethod
    async def MSStoreClientImportedXMLResponse(iUserId, dictUsersConfig, documents, dictClientDetail):
        """
        Input:

            1) iUserId: int
                The user ID for which the XML files are being stored.

            2) dictUsersConfig: dict
                Configuration details for each user.

            3) documents: List[UploadFile]
                List of uploaded XML or compressed files.

            4) dictClientDetail: dict
                Client metadata such as ClientReqImportedAt, ClientImportedXMLType, etc.

        Output:

            dict: Metadata of stored files including path, name, and content.

        Purpose:

            Saves XML files or extracts and stores them from archives, then updates
            AVRequestDetail table by parsing strClientREQID and DocID from filename.
        """

        try:
            lsFileNames = []
            saved_files: List[FileSchema] = []
            lsFilePath = []

            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            data_directory = dictUsersConfig[str(iUserId)]['dataDirectory']
            os.makedirs(data_directory, exist_ok=True)

            current_date_dir = os.path.join(data_directory, datetime.now().strftime("%Y_%m_%d"))
            os.makedirs(current_date_dir, exist_ok=True)

            strClientTallyResponseDir = os.path.join(current_date_dir, "TallyImportResponse")
            os.makedirs(strClientTallyResponseDir, exist_ok=True)

            for file in documents:
                base_filename, file_extension = os.path.splitext(file.filename)
                unique_file_path = os.path.join(strClientTallyResponseDir, f"{base_filename}{file_extension}")

                if file_extension.lower() in ['.zip', '.7z', '.rar', '.tar']:
                    zip_temp_path = os.path.join(strClientTallyResponseDir, f"{base_filename}_temp{file_extension}")
                    with open(zip_temp_path, "wb") as f:
                        f.write(await file.read())

                    extract_files = []

                    if file_extension.lower() == '.zip':
                        with zipfile.ZipFile(zip_temp_path, 'r') as zip_ref:
                            zip_ref.extractall(strClientTallyResponseDir)
                            extract_files = zip_ref.namelist()

                    elif file_extension.lower() == '.rar':
                        with rarfile.RarFile(zip_temp_path, 'r') as rar_ref:
                            rar_ref.extractall(strClientTallyResponseDir)
                            extract_files = rar_ref.namelist()

                    elif file_extension.lower() == '.tar':
                        with tarfile.open(zip_temp_path, 'r') as tar_ref:
                            tar_ref.extractall(strClientTallyResponseDir)
                            extract_files = [m.name for m in tar_ref.getmembers()]

                    for name in extract_files:
                        extracted_file_path = os.path.join(strClientTallyResponseDir, name)
                        content_type = mimetypes.guess_type(extracted_file_path)[0] or "application/octet-stream"

                        with open(extracted_file_path, "rb") as f:
                            file_data = f.read()

                        saved_file = FileSchema(
                            file=file_data,
                            filename=name,
                            content_type=content_type,
                            data=file_data,
                            DocVendorName="",
                            FileType="XML",
                            HashCode=Hashing.calculate_checksum_from_file(file_path=extracted_file_path)
                        )
                        saved_files.append(saved_file)
                        lsFileNames.append(name)
                        lsFilePath.append(extracted_file_path)
                        
                        try:
                            if "Bank_Statement" in saved_file.filename:
                                try:
                                    # Insert Code to update Transaction
                                    strClientREQID = dictClientDetail.get("strClientREQID","")
                                    iStatementId, iTransactionId = CRegexMethod.MSExtractTransactionDetailsFromFileName(saved_file.filename)
                                    await CLogController.MSWriteLog(iUserId, "Info", f"strClientREQID: {strClientREQID}")
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999
                                    await TransactionUpdater.MSUpdateTransactionTable(
                                        intTxnId=iTransactionId,
                                        intStatementId=iStatementId,
                                        strClientREQID=strClientREQID,
                                        ClientImportedXMLStatusCode=ClientImportedXMLStatusCode,
                                        CReqIMPORTEDXMLTimeAt=dictClientDetail.get("ClientReqImportedAt", datetime.now()),
                                        CImportedXML_Type=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request")
                                    )
                                    await CAVRequestDetail.MSUpdateRecordByClientREQID(
                                    strClientREQID=strClientREQID,
                                    ClientImportedXMLStatusCode='NOT_APPLICABLE',
                                    ClientImportedXMLResponse='NOT_APPLICABLE',
                                    CReqIMPORTEDXMLTimeAt=dictClientDetail.get("ClientReqImportedAt", datetime.now()), # Take now time in case old AV Exe use by Customer
                                    CImportedXML_Type=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request")
                                )
                                except:
                                    await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")
                            else:
                                dictDocDetails = CRegexMethod.MSExtractTallyResDocDetails(name)

                                # Update Document Details
                                if dictDocDetails["Doc_Details"]:
                                    dictDocumentDetails = dictDocDetails["Doc_Details"]
                                    strClientREQID, DocID, strUploadedDocumentName = dictDocumentDetails["REQID"], dictDocumentDetails["DID"], dictDocumentDetails["DName"]
                                    await CLogController.MSWriteLog(iUserId, "Info", f"strClientREQID: {strClientREQID}, DocID: {DocID}, strUploadedDocumentName: {strUploadedDocumentName}")
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999 # ClientImportedXMLStatusCode == 200 when XML Content Present else 999 Not Found

                                    await CAVRequestDetail.MSUpdateRecord(
                                        iUserId=iUserId,
                                        strClientREQID=strClientREQID,
                                        DocID=DocID,
                                        ClientImportedXMLStatusCode=ClientImportedXMLStatusCode,
                                        ClientImportedXMLResponse=xml_content,
                                        CReqIMPORTEDXMLTimeAt=dictClientDetail.get("ClientReqImportedAt", datetime.now()), # Take now time in case old AV Exe use by Customer
                                        CImportedXML_Type=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request") # Default EXE_Initiated_Request in case of backward Compatibility
                                    )
                                    # NOTE: For Abhinav Infra purchase invoices, we are tracking the GRN number to ensure that no duplicate GRNs are used in any purchase invoice
                                    if iUserId == 11:
                                        await CGrnNoPITracking.MSUpdateTallyStatus(
                                            Doc_id = DocID,
                                            TallyImportedStatus = ClientImportedXMLStatusCode,
                                            userId=iUserId
                                        )
                                        
                                # Update PO Details
                                elif dictDocDetails["PO_Details"]:
                                    dictPODetails = dictDocDetails["PO_Details"]
                                    iReqID, Userid, strPONUM = dictPODetails["REQID"], dictPODetails["UID"], dictPODetails["PONUM"]
                                    await CLogController.MSWriteLog(iUserId, "Info", f"Request Id: {iReqID}, User ID: {Userid}, PO Number: {strPONUM}")
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999 # ClientImportedXMLStatusCode == 200 when XML Content Present else 999 Not Found

                                    await CAVPOProcessingDetailsService.MSUpdatePOProcessingDetails(UserID=Userid, 
                                                                                                    RequestID=iReqID, 
                                                                                                    PONumber=strPONUM, 
                                                                                                    XMLImportStatusCode=ClientImportedXMLStatusCode, 
                                                                                                    XMLImportType=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request"))
                                
                                # Update Machinery GRN Details
                                elif dictDocDetails["Machinery_GRN_Details"]:
                                    dictGRNDetails = dictDocDetails["Machinery_GRN_Details"]
                                    iReqID, Userid, strCostCenter, strVendorName, strMachineNumber, iDate = dictGRNDetails["REQID"], dictGRNDetails["UID"], dictGRNDetails["COSTCENTER"], dictGRNDetails["VENDOR"], dictGRNDetails["MachineNumber"], dictGRNDetails["DATE"]
                                    strMachineryGRNNumber = f"{strCostCenter}_{strMachineNumber}_{iDate}"
                                    
                                    await CLogController.MSWriteLog(iUserId, "Info", f"Request Id: {iReqID}, User ID: {Userid}, Machinery GRN Number: {strMachineryGRNNumber}")
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999 # ClientImportedXMLStatusCode == 200 when XML Content Present else 999 Not Found

                                    await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=Userid, 
                                                                                                    RequestID=iReqID, 
                                                                                                    GRNNumber=strMachineryGRNNumber, 
                                                                                                    XMLImportStatusCode=ClientImportedXMLStatusCode, 
                                                                                                    XMLImportType=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request"))
                                
                                
                                
                                # Update Other GRN Details
                                elif dictDocDetails["Other_GRN_Details"]:
                                    dictGRNDetails = dictDocDetails["Other_GRN_Details"]
                                    iReqID, Userid, strGRNNum = dictGRNDetails["REQID"], dictGRNDetails["UID"], dictGRNDetails["GRNNUM"]
                                
                                    await CLogController.MSWriteLog(iUserId, "Info", f"Request Id: {iReqID}, User ID: {Userid}, GRN Number: {strGRNNum}")
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999 # ClientImportedXMLStatusCode == 200 when XML Content Present else 999 Not Found

                                    await CAVGRNProcessingDetailsService.MSUpdateGRNProcessingDetails(UserID=Userid, 
                                                                                                    RequestID=iReqID, 
                                                                                                    GRNNumber=strGRNNum, 
                                                                                                    XMLImportStatusCode=ClientImportedXMLStatusCode, 
                                                                                                    XMLImportType=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request"))
                                
                                
                                elif dictDocDetails["Delivery_Note_Details"]:
                                    dictDeliveryNoteDetails = dictDocDetails["Delivery_Note_Details"]
                                    strClientREQID, strUploadDocumentName = dictDeliveryNoteDetails["REQID"], dictDeliveryNoteDetails["DeliveryNoteDocName"]
                                
                                    try:
                                        DictDocReqInfo =await CAVRequestDetail.MSGetReqDocNameByClientREQID(strClientREQID)
                                        
                                        await CLogController.MSWriteLog(iUserId, "Info", f"strClientREQID: {strClientREQID}, strUploadedDocumentName: {strUploadDocumentName}")
                                        xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                        ClientImportedXMLStatusCode = 200 if xml_content else 999
                                        await CAVRequestDetail.MSUpdateDeliveryNoteRecord(
                                        iUserId=iUserId,
                                        strClientREQID=strClientREQID,
                                        ReqDocName=DictDocReqInfo.get("ReqDocName",""),
                                        ClientImportedXMLStatusCode=ClientImportedXMLStatusCode,
                                        ClientImportedXMLResponse=xml_content,
                                        CReqIMPORTEDXMLTimeAt=dictClientDetail.get("ClientReqImportedAt", datetime.now()), # Take now time in case old AV Exe use by Customer
                                        CImportedXML_Type=dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request")
                                    )
                                    except:
                                        await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")
                                elif dictDocDetails["Imprest_Journal_Details"]:
                                    
                                    dictImprestDetail = dictDocDetails['Imprest_Journal_Details']
                                    xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                                    ClientImportedXMLStatusCode = 200 if xml_content else 999
                                    JID = dictImprestDetail["iJID"]
                                    Journal_ID = dictImprestDetail['iJournalID']
                                    strClientREQID = dictClientDetail.get("strClientREQID","")
                                    kwargs = {
                                        "ClientImportedXMLStatusCode":ClientImportedXMLStatusCode,
                                        "CReqIMPORTEDXMLTimeAt":dictClientDetail.get("ClientReqImportedAt", datetime.now()), 
                                        "CImportedXML_Type":dictClientDetail.get("ClientImportedXMLType","EXE_Initiated_Request")
                                    }
                                    try:
                                       
                                        await CImprestJournalDetails.MSUpdateJournalRecord(
                                        userId=iUserId,
                                        strClientREQID=strClientREQID,
                                        JID = JID,
                                        kwargs=kwargs
                                        )
                                        await CAVRequestDetail.MSUpdateRecordByClientREQID(
                                        iUserId=iUserId,
                                        strClientREQID=strClientREQID,
                                        ImprestJournalID = Journal_ID
                                        
                                    )
                                    except Exception as e:
                                        await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")
                                else:
                                    await CLogController.MSWriteLog(iUserId, "DEBUG", f"ELSE BLOCK: {saved_file.filename}")
                        except Exception as e:
                                await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")

                    os.remove(zip_temp_path)

                else:
                    counter = 1
                    while os.path.exists(unique_file_path):
                        unique_file_path = os.path.join(strClientTallyResponseDir, f"{base_filename}_{counter}{file_extension}")
                        counter += 1

                    with open(unique_file_path, "wb") as f:
                        file_data = await file.read()
                        f.write(file_data)

                    saved_file = FileSchema(
                        file=file_data,
                        filename=file.filename,
                        content_type=file.content_type or "application/octet-stream",
                        data=file_data,
                        DocVendorName="",
                        FileType="XML",
                        HashCode=Hashing.calculate_checksum_from_file(file_path=unique_file_path)
                    )
                    saved_files.append(saved_file)
                    lsFileNames.append(file.filename)
                    lsFilePath.append(unique_file_path)

                    try:
                        strClientREQID, DocID, strUploadedDocumentName = CRegexMethod.MSExtractTallyResDocDetails(file.filename)
                        await CLogController.MSWriteLog(iUserId, "Info", f"strClientREQID: {strClientREQID}, DocID: {DocID}, strUploadedDocumentName: {strUploadedDocumentName}")
                        xml_content = CAVXMLParser.isClientVoucherImported200(file_data.decode("utf-8", errors="ignore").strip())  
                        ClientImportedXMLStatusCode = 200 if xml_content else 999 # ClientImportedXMLStatusCode == 200 when XML Content Present else 999 Not Found

                        await CAVRequestDetail.MSUpdateRecord(
                            iUserId=iUserId,
                            strClientREQID=strClientREQID,
                            DocID=DocID,
                            ClientImportedXMLStatusCode=ClientImportedXMLStatusCode,
                            ClientImportedXMLResponse=xml_content,
                            CReqIMPORTEDXMLTimeAt=dictClientDetail.get("ClientReqImportedAt", datetime.now()), # Take now time in case old AV Exe use by Customer
                            CImportedXML_Type=dictClientDetail.get("ClientImportedXMLType", "EXE_Initiated_Request") # Default EXE_Initiated_Request in case of backward Compatibility
                        )
                    except Exception as e:
                        await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")
            
            await CLogController.MSWriteLog(iUserId, "Info", f"MSStoreClientImportedXMLResponse - saved_files : {saved_files}, lsFileNames : {lsFileNames}, lsFilePath : {lsFilePath}")
            return {
                "Files": saved_files,
                "FileNames": lsFileNames,
                "FilePath": lsFilePath
            }
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Traceback: {str(traceback.format_exc())}")
            return {
                    "Files": ["Error Occur in AHM Server"],
                    "FileNames": ["Error Occur in AHM Server"],
                    "FilePath": ["Error Occur in AHM Server"]
                }
    
    ### ---------------------------------- CLIENT Tally Imported XML Response -------------------------------------- MSXMLImportedResponse
    async def MSXMLImportedResponse(iUserId, documents, dictClientDetail):
        """
        Input:

            1) iUserId: Integer
            The user ID for which the XML files are being processed.

            2) documents: list
            A list of documents to be processed and stored in the network location.

        Output:

            dict: A dictionary containing the status of the operation, the received time, and the list of stored XML files.

        Purpose:

            To process XML files for a given user by:
            - Validating the user ID in the configuration.
            - Storing the XML documents in a network location.
            - Logging the operation details.

        """
        lsStoredXMLFileNames = []
        try:
            # config
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            # Validate user ID and get data directory
            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            # Store Doc in Network Location
            # documents_copy = copy.deepcopy(documents)
            dictStoreDocRes = await CIndianInvTallyController.MSStoreClientImportedXMLResponse(iUserId=iUserId, dictUsersConfig=dictUsersConfig, documents=documents, dictClientDetail=dictClientDetail)
            lsStoredXMLFileNames = dictStoreDocRes.get("FileNames")
            # Return successful response
            dictResult = {
                "Received_XML_Status": True,
                "Received_Time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Received_Files": lsStoredXMLFileNames
            }
            await CLogController.MSWriteLog(iUserId, "INFO",f"Response from Client Tally after importing XML: {dictResult}, iUserID - {iUserId}, lsStoredXMLFiles - {dictStoreDocRes.get('FilePath')} ")
            return dictResult
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print("Error Occur - ", traceback.print_exc())
            # Return failure response
            return {
                "Received_XML_Status": False,
                "Received_Time": datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
                "Received_Files": lsStoredXMLFileNames
            }

    ### ----------------------------------START: PDF Document Start Processing -------------------------------------- 
    async def MSProcessIndianInvDoc(strClientREQID, iUserId, documents, strVoucherType, bTestMode=False, dictLsClientDocMetaData={}, dictExeUserDetails={}, bScanForVirus=True,bIsMultivendorDoc=False,strSystemName="-", strClientTallyUserName="Developer", CReqServerReceivedAt= None):
        """
            Process a file located at the given filepath, extract the number of pages, and save the file.

            Input:
                1) filepath: str
                    The full path to the file to be processed.

                2) filename: str
                    The name of the file to be processed.

                3) DocVendorName: str (optional)
                    The name of the vendor associated with the document. Defaults to an empty string.

            Output:
                    Entry in tally Application and Email sent to developers.

            Purpose:
                To handle the processing of a file by extracting its page count and saving it, optionally associating it with a vendor name and punching it into the tally.

            """
        try:
            
            # Files Client Response - Report , Pricelist , Json , XML Response 
            lsClientResponseFiles = []
            strMultiVendorSplitAlgoZipFile = ""
            # config
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            # Validate user ID and get data directory
            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            client_config = dictUsersConfig[str(iUserId)]
            # Store Doc in Network Location
            # Create a deep copy
            # documents_copy = copy.deepcopy(documents)
            strClientRequestDir = dictExeUserDetails.get("ClientReqDir", r"")
            dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(  iUserId=iUserId, 
                                                                                        dictUsersConfig=dictUsersConfig, 
                                                                                        documents=documents,eVoucherType=strVoucherType, bDeveloperMode=bTestMode, bScanForVirus=bScanForVirus, bIsMultivendorDoc=bIsMultivendorDoc, strClientRequestDir = strClientRequestDir, strClientTallyUserName = strClientTallyUserName,strClientREQID=strClientREQID,
                                                                                        dictExeUserDetails = dictExeUserDetails)
            #Initialize Accuvelocity Report Parent Columns
            objAVReqDetail = await CAVRequestDetail.MSInsertAVRequestDetailsFromStoredDocs(dictStoreDocRes=dictStoreDocRes,
                strClientREQID=strClientREQID,
                iUserId=iUserId,
                strVoucherType=strVoucherType,
                bTestMode = bTestMode,
                dictExeUserDetails= dictExeUserDetails,
                bScanForVirus = True,
                bIsMultivendorDoc = bIsMultivendorDoc,
                strSystemName= strSystemName,User_UID=iUserId)
            
            # -------------------  Validate No of Request, No of Pages Allowed Per Daynote -------------------  
            current_date = datetime.now().strftime('%Y-%m-%d')
            dataDirectory = client_config["dataDirectory"]
            
            unknown_vendor_date_dir = os.path.join(dataDirectory, current_date) 
            zip_file_name = f"MultipleVendorSplitAlgo_{strClientREQID}.zip"
            
            strMultipleVendorSplitAlgoZipFile = os.path.join(unknown_vendor_date_dir, zip_file_name)
            dictUserValidationResponse = await CTallyController.MSValidateUserLimit(iUserId=iUserId, iPageLimit=dictExeUserDetails.get("iPageAllowedPerDay",50), iReqLimit=dictExeUserDetails.get("iRequestAllowedPerDay",100), strDate=current_date, iCurrReqPage = dictStoreDocRes.get("iNumPages"))
            
            bPageAllowProcess = dictUserValidationResponse.get('bPageAllowProcess', True)
            bReqAllow = dictUserValidationResponse.get('bReqAllow', True)
            iPageProcess = dictUserValidationResponse.get('iPageProcess', 0)
            iReqProcess = dictUserValidationResponse.get('iReqProcess', 0)
            iCurrPageReq = dictStoreDocRes.get("iNumPages")
            lsSplitAlgoFilesToSend = dictStoreDocRes.get("lsMultipleVendorEnableSplitDocs")

            if bIsMultivendorDoc and lsSplitAlgoFilesToSend:
                dictLsClientDocMetaData = dictStoreDocRes.get("dictLsClientDocMetaData") # Single PDF (Multiple Vendor Merged PDF) == Splitted FileName base on VendorName details Stored to correctly Mapped with XML Attachment Solution
                
                strMultiVendorSplitAlgoZipFile = CDirectoryHelper.create_zip_file(lsSplitAlgoFilesToSend, destination_path= strMultipleVendorSplitAlgoZipFile)
                lsClientResponseFiles.extend(lsSplitAlgoFilesToSend)
                lsClientResponseFiles.append(strMultiVendorSplitAlgoZipFile)

            # Prepare message based on conditions
            if not bPageAllowProcess and not bReqAllow:
                detail = (
                    f"Your request could not be processed because:\n"
                    f"- You have exceeded the allowed page processed limit of {iPageProcess} pages per day.\n"
                    f"- Your current request to process {iCurrPageReq} pages exceeds the daily request limit by {iReqProcess} pages.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            elif not bPageAllowProcess:
                detail = (
                    f"Your request could not be processed because you have exceeded the allowed page processed limit of {iPageProcess} pages per day.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            elif not bReqAllow:
                detail = (
                    f"Your request could not be processed because your current request to process {iCurrPageReq} pages exceeds the daily request limit by {iReqProcess} pages.\n"
                    f"Please contact AccuVelocity Support team for assistance.\n"
                    f"You can reach out to <NAME_EMAIL> or on +91 98989 42935."
                )
                raise HTTPException(status_code=409, detail=detail)
            
            lsClientInputDocs = dictStoreDocRes.get("Docs")
            lsClientResponseFiles.extend(dictStoreDocRes.get("lsFilePath"))
            maxConcurrentTask =5

            # Use list comprehension to create the list of dictionaries
            lsAllDocInfo = [
                        {
                            "Doc_Id": None,  # Doc_Id starts from 2388
                            "Status": "Pending",
                            "FileName": doc.filename,
                            "FilePath": doc.filePath,
                            "PageCount": 0,
                            "VendorName": doc.DocVendorName,
                            "ErrorMessage": "",
                            "HashCode":doc.HashCode,
                            "strClientREQID":strClientREQID
                        }
                        for doc in lsClientInputDocs
                    ]
            
            #1. Upload Document
            if lsClientInputDocs: #documents:
                strExtractionURL = CIndianInvTallyController._mstrProductionExtractService if not bTestMode else CIndianInvTallyController._mstrDevelopementExtractService
                
                # Initialize the document uploader with the specified maximum concurrent uploads
                uploader = DocumentUploader(max_concurrent_uploads=maxConcurrentTask, strVoucherType=strVoucherType.value)
                lsDictUploadResults= await uploader.upload_documents(user_id= iUserId, 
                                                                        documents = lsClientInputDocs, 
                                                                        strFamilyName="Demo Finance",
                                                                        model_name = "Invoice", 
                                                                        bAutoSelectModel=True, 
                                                                        bUsePaidModel = True, 
                                                                        objAdditionalDocDetails= dictStoreDocRes.get("AdditionalDocPath"),BReUseDocData=True)


                lsDocIDSForExtract = [
                                        dictFileData['DocId'] 
                                        for dictFileData in lsDictUploadResults 
                                        if (dictFileData['APIStatusCode'] == 200) and dictFileData['DocId'] 
                                    ]
                print(lsDocIDSForExtract)
                lsDictExtractionResult = []
                #2. Extract Document
                if lsDocIDSForExtract:
                    processor = DocumentProcessorUsingHTTPX(max_concurrent_tasks=maxConcurrentTask, api_base_url=strExtractionURL, strClientREQID=strClientREQID, strVoucherType=strVoucherType.value, bDevelopmentMode = bTestMode)
                    lsDictExtractionResult = await processor.process_documents(lsDocIDSForExtract, iUserId, True, None)
                    print(lsDictExtractionResult)
                print(lsDictExtractionResult)
                lsEstimatedCurrentRequestTimeSaved = []
                iTotalPagesProcessed =  0 
                iTotalTimeSavedMin = 0
                strTotalTimeSavedMin = "~0 min 0 secs"
                iTotalPagesProcessed = 0

                #3. Call Tally API CODE 
                for dictGPTAPIResponses in lsDictExtractionResult:
                    try:
                        
                        if dictGPTAPIResponses["APIStatusCode"] == 200:
                            objTallyIndianController = CTallyIndianInvoiceController(user_id=iUserId, doc_id=dictGPTAPIResponses["document_id"],dictLsClientDocMetaData=dictLsClientDocMetaData,dictExtractedData=dictGPTAPIResponses["Document"],lsAllDocInfo=lsAllDocInfo, strClientREQID=strClientREQID,bTestMode=bTestMode,strVoucherType=strVoucherType.value)
                            TallyResult = await objTallyIndianController.MCallTallyAPI(bRaiseError=False, dictLsClientDocMetaData= dictLsClientDocMetaData,strPriceListReport = CIndianInvTallyController._mPricelistReportPath, ObjVoucherType = strVoucherType)
                            
                            strCurrentXmlFilePath = TallyResult.get("XMLFilePath")
                            if strCurrentXmlFilePath:
                                lsClientResponseFiles.append(strCurrentXmlFilePath)
                            
                        else:
                           await CLogController.MSWriteLog(iUserId, "Debug", f"dictGPTAPIResponse : {dictGPTAPIResponses['APIStatusCode']} == 200: False, {dictGPTAPIResponses}")

                    except Exception as e:
                        await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
                        await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                        pass
            

                # ------------ Server Execution Time - Current Request ---------------- 
                dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                    user_id=iUserId, 
                    strClientReqID=strClientREQID,
                    isUseRequestTime = True
                )
                strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
                
                # ------------- Current Request Processed Accountant Time Saved & Communilative Statistics ------------
                try:
                    dictAPIReqStats = await CAVRequestDetail.MSGetStatistics(iUserId, strClientREQID)
                    strTotalTimeSavedMin, iTotalTimeSavedMin, iTotalPagesProcessed = dictAPIReqStats.get("strTotalTimeSavedMin"), dictAPIReqStats.get("iTotalTimeSavedMin"), dictAPIReqStats.get("iTotalPagesProcessed")
                    await CLogController.MSWriteLog(iUserId, "Debug", f"dictAPIReqStats : {dictAPIReqStats}")
                except Exception as e:
                    await CLogController.MSWriteLog(iUserId, "Error", f"Failed to get the statistics, Error:{e}")
                    await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    pass

                dictTallyUserConfig = {}
                try:
                    dictTallyUserConfig = await CTallyController.MSGetTallyUserConfig(iUserID=iUserId)
                except Exception as e:
                    pass

                iCummulativePagesProcessed = 0
                iCummulativeTimeSavedMinutes = 0
                # Update Total statistics in db
                try:
                    # Update this 
                    iCummulativePagesProcessed = dictTallyUserConfig.get("TotalPagesProcessed", None) + iTotalPagesProcessed
                    iCummulativeTimeSavedMinutes = dictTallyUserConfig.get("TotalTimeSavedInMinutes", None) + iTotalTimeSavedMin
                    await CTallyController.MSSetTallyUserConfig(iUserID=client_config['userId'],
                                                                                        iTotalPagesProcessed = iCummulativePagesProcessed,
                                                                                        iTotalTimeSaved = iCummulativeTimeSavedMinutes
                                                                                    )
                except Exception as e:
                    pass
                
                # User Readability Time Saved from Float or Int Type Convertion into String
                strFormattedTimeSavedTillNow = await CommonHelper.MSGetFormattedTime(iCummulativeTimeSavedMinutes)
                
                # Create AV Report 
                result = await CAVRequestDetail.MSExportAVRecordDetailsToExcel(strClientREQID=strClientREQID,strReqReportPath=CIndianInvTallyController._mReqReportPath)
                
                # Send EMAIL AV REPORT
                try:
                    VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"
                    TallyReportSender.SendTallyNotificationEmail(csvReportPath=CIndianInvTallyController._mReqReportPath, 
                                                    strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                                                    strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                                                    strMailFrom=os.getenv('MAIL_FROM'), 
                                                    lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>", "<EMAIL>","<EMAIL>"], 
                                                    strServer=os.getenv('MAIL_SERVER'), 
                                                    intPort=int(os.getenv('SMTP_PORT')), 
                                                    strPassword=os.getenv('MAIL_PASSWORD'), 
                                                    htmlTemplatePath=Path(r"resource/TallyEmailTemplate.html"), 
                                                    lsCC=client_config['lsEmailCC'],
                                                    strTotalPagesProcessedToday=iTotalPagesProcessed, 
                                                    strTotalTimeSavedToday=strTotalTimeSavedMin,
                                                    strTotalPagesProcessedTillNow=iCummulativePagesProcessed,
                                                    strTotalTimeSavedTillNow=strFormattedTimeSavedTillNow,
                                                    lsAttachmentPath=[CIndianInvTallyController._mPricelistReportPath,strMultipleVendorSplitAlgoZipFile] if strMultipleVendorSplitAlgoZipFile !="" else [CIndianInvTallyController._mPricelistReportPath],
                                                    strSystemName=strSystemName,
                                                    strExecutiontime=strExecutiontime
                                                    
                                                    )
                except Exception as e:
                    print("ERROR --- In sending Client Tally Report", traceback.print_exc())
                    pass
    
           
            #4. Email report Creation
            # return activity status
            # Return Zip file - Contains Report File, Price List Report, Json Responses Object or XML Response,etc 
            strClientResZipFile = CDirectoryHelper.create_zip_file(lsClientResponseFiles, destination_path= CIndianInvTallyController._mstrClientResZipFile)
            
            if bScanForVirus:
                objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                
                if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                    raise VirusDetected(file_path=strClientResZipFile)
            
            return strClientResZipFile

        except VirusDetected as vd:
            await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            try:
                TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                            strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                            lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                            lsCC=client_config['lsEmailCC'])
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")
            raise vd
        
        except Exception as e:
            print("Error Occur - ", traceback.print_exc())
            raise e
    ### ----------------------------------END PDF Document Start Processing --------------------------------------

    async def MSProcessPurchaseOrder(strClientREQID, dictExeUserDetails, iUserId, documents, strVoucherType, bTestMode=False, bScanForVirus=False, dictLsClientDocMetaData={}, strSystemName=""):
        """
            Input:

                1) strClientREQID: str
                A unique identifier for the client’s request, used for tracking and logging.

                2) iUserId: int
                The ID of the user initiating the purchase order processing.

                3) documents: list
                A list of document objects (e.g., dicts with filename and content) representing purchase order files to process.

                4) strVoucherType: str
                The type of voucher (e.g., "PO", "GRN") indicating how to handle the documents.

                5) bTestMode: bool, optional (default False)
                If True, runs the process in test mode without committing changes to storage.

                6) bScanForVirus: bool, optional (default False)
                If True, performs a virus scan on each document before processing.

                7) dictLsClientDocMetaData: dict, optional (default {})
                A dictionary of metadata for each client document, which may be updated during processing.

                8) strSystemName: str, optional (default "")
                The name of the source system invoking this operation, for audit purposes.

            Output:

                str:
                    The file path to the ZIP archive containing the processed purchase order results.

            Purpose:

                To process a batch of purchase order documents by:
                - Validating the client request ID and user configuration.
                - Optionally scanning documents for viruses.
                - Converting or extracting relevant PO data according to `strVoucherType`.
                - Packaging all processed files into a ZIP archive.
                - Returning the path to the resulting ZIP file for downstream use.

        TODO 1: Need to add Duplicate Checks
        TODO 2: Need to add Cummulative Time Saved
        TODO 3: Need to save PO Data in Database
        TODO 4: Need to store tally status in database
        """
        
        try:
            
            # config
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            
            # Validate user ID and get data directory
            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")
            

            client_config = dictUsersConfig[str(iUserId)]
            client_data_dir = client_config['dataDirectory']
            today = datetime.now().strftime("%Y_%m_%d")
            today_download_dir = os.path.join(client_data_dir, today)
            
            # Boolean To Download Tally ERP Matching File
            bDownloadERP = client_config.get('bDownloadERPMappingFile', False)
            
            # Store Doc in Network Location
            # Create a deep copy
            # documents_copy = copy.deepcopy(documents)
            dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(
                iUserId=iUserId, 
                dictUsersConfig=dictUsersConfig, 
                documents=documents,
                eVoucherType=strVoucherType, 
                # strRequestDocType=strRequestDocType,
                bDeveloperMode= bTestMode,
                bScanForVirus=bScanForVirus,
                strClientREQID=strClientREQID
            )
            strReportFilePath = dictStoreDocRes.get("ReportFilePath","")
            objPOFiles=dictStoreDocRes.get("lsPOFiles",[])

            
            # -> START-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
            dtGenerated = dictExeUserDetails.get("ClientReqGeneratedAt", datetime.now())
            strCustName = dictExeUserDetails.get("CustomerName", "Unknown")
            iRequestID = None
            for doc in objPOFiles:
                # Build the **kwargs bag for the helper
                kw  = {
                    "NetworkLocation": {"path": getattr(doc, "filePath", "")},
                    "strVoucherType": strVoucherType,
                    "ReqDocType": getattr(doc, "FileType", "pdf"),
                    "ReqDocName": getattr(doc, "filename", ""),
                    "ReqDocHashCode": getattr(doc, "HashCode", ""),
                    "MultipleVendorEnabled": False,
                    "UServerName": DEVICE_NAME,
                    "strSystemUserName": strSystemName,
                    "PriceListVerification": False,
                    "strAccuVelocityComments": "-",
                    "TracebackLogs": "",
                    "strServerEstProcessingTime": "NOT_APPLICABLE",
                    "AVXMLGeneratedStatus": "NOT_APPLICABLE", # Please note use NOT_APPLICABLE when Multiple Records in case of single Row from this enum 'Success', 'Skipped', 'PartialSuccess', 'NOT_APPLICABLE'
                    "IsRecordUIDPresent": False, # True when any ID Foreign key
                    "EstAccountantTimeSaved": "NOT_APPLICABLE",
                    "ClientImportedXMLStatusCode":"NOT_APPLICABLE", # Default ClientImportedXMLStatusCode Value
                    "ClientImportedXMLResponse":"NOT_APPLICABLE",
                    "bTestMode" : bTestMode
                }
                obj = await CAVRequestDetail.MSInsertAVRecordDetail(
                    strClientREQID=strClientREQID,
                    dtObjGeneratedTimeAt=dtGenerated,
                    iUserId=iUserId,
                    strRecievedDate=date.today(),
                    strCustomerName=strCustName,
                    **kw
                )
                iRequestID = obj.ID
            # <- END-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
            
            
            lsFilesToSend = []
            lsAllPODetails = []
            for objPOFile in objPOFiles:
                lsPOProcessingInfo = await CAbhinavInfrabuild.MSGetAllPurchaseOrderXML(iUserID=iUserId, iRequestID=iRequestID, strExcelFilePath=objPOFile.filePath, dictLsClientDocMetaData=dictLsClientDocMetaData, bDeveloperMode= bTestMode, bDownloadERP = bDownloadERP)
                
                for dictPOProcessingInfo in lsPOProcessingInfo:
                    lsAllPODetails.append(dictPOProcessingInfo)
                    lsFilesToSend.append(dictPOProcessingInfo.get("XMLFilePath"))
                 
                CIndianInvTallyController.MSSavePOReport(lsPOProcessingInfo, strReportFilePath)
        
            
        
            
            lsFilesToSend.append(strReportFilePath)
            strTotalSavedTimeToday = CommonHelper.MSTotalTimeSaved(lsAllPODetails)

            iTotalProcessedVouchers =   sum(1 for dictPODetails in lsAllPODetails if dictPODetails.get("AV_Status") == "Success")
            
            
            dictProcesingStatsTillNow = await CAVPOProcessingDetailsService.MSTotalTimeSavedTillNow(UserID=iUserId, time_per_item_in_seconds=CAbhinavInfrabuild._miTimeSavedPerPOItem)

            # ------------ Server Execution Time - Current Request ---------------- 
            dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                user_id=iUserId, 
                strClientReqID=strClientREQID,
                isUseRequestTime = True
            )
            strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
            VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"
            TallyReportSender.SendTallyNotificationEmailPurchaseOrder(
                    csvReportPath=CIndianInvTallyController._mReqReportPath, 
                    strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                    strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                    strMailFrom=os.getenv('MAIL_FROM'), 
                    lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>", "<EMAIL>"], 
                    strServer=os.getenv('MAIL_SERVER'), 
                    intPort=int(os.getenv('SMTP_PORT')), 
                    strPassword=os.getenv('MAIL_PASSWORD'), 
                    strVoucherType="Purchase Order",
                    strTodayTotalPostedVouchers=iTotalProcessedVouchers, 
                    strTotalTimeSavedToday=strTotalSavedTimeToday,
                    lsCC=client_config['lsEmailCC'],
                    lsAttachmentPath=[CIndianInvTallyController._mReqReportPath],
                    strSystemName=strSystemName,
                    strExecutiontime=strExecutiontime,
                    total_time_saved_till_now=dictProcesingStatsTillNow.get("total_time_saved"),
                    total_posted_vouchers_till_now=dictProcesingStatsTillNow.get("total_unique_entries")
                )
            
            strClientResZipFile = CDirectoryHelper.create_zip_file(
                lsFilesToSend, 
                destination_path=CIndianInvTallyController._mstrClientResZipFile
            )

            if bScanForVirus:
                objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                
                if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                    raise VirusDetected(file_path=strClientResZipFile)
                
            return strClientResZipFile
        
        except VirusDetected as vd:
            await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            try:
                TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                            strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                            lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                            lsCC=client_config['lsEmailCC'])
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")

            raise vd
        except Exception as e:
            print(f"Error: {e}")
            raise e
    ### ---------------------------------- END PO Processing--------------------------------------

    ### ---------------------------------- START GRN Processing--------------------------------------
    async def MSProcessGRN(strClientREQID, dictExeUserDetails, iUserId, documents, strVoucherType, bTestMode=False, bScanForVirus=False,strSystemName="-"):

            """
            Input:

                    1) strClientREQID: str
                    A unique identifier for the client’s request, used for tracking and logging.

                    2) iUserId: int
                    The ID of the user initiating the GRN (Goods Received Note) processing.

                    3) documents: list
                    A list of document objects (e.g., dicts with `filename` and `content`) representing GRN files to process.

                    4) strVoucherType: str
                    The type of voucher (e.g., "GRN") indicating how to handle the documents.

                    5) bTestMode: bool, optional (default False)
                    If True, runs the process in test mode without committing changes to storage.

                    6) bScanForVirus: bool, optional (default False)
                    If True, performs a virus scan on each document before processing.

                    7) strSystemName: str, optional (default "-")
                    The name of the source system invoking this operation, for audit purposes.

                Output:

                    str:
                        The file path to the ZIP archive containing the processed GRN results.

                Purpose:

                    To process a batch of Goods Received Note documents by:
                    - Validating the client request ID and user configuration.
                    - Optionally scanning documents for viruses.
                    - Extracting and validating GRN data according to `strVoucherType`.
                    - Packaging all processed GRN files into a ZIP archive.
                    - Returning the path to the resulting ZIP file for downstream use.
                TODO 1: Need to add Duplicate Checks
                TODO 2: Need to add Cummulative Time Saved
                TODO 3: Need to save PO Data in Database
                TODO 4: Need to store tally status in database
            """
            try:
                
                # config
                dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
                
                # Validate user ID and get data directory
                if str(iUserId) not in dictUsersConfig:
                    raise HTTPException(status_code=404, detail="User ID not found in configuration.")
                

                client_config = dictUsersConfig[str(iUserId)]
                client_data_dir = client_config['dataDirectory']
                today = datetime.now().strftime("%Y_%m_%d")
                
                dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(
                    iUserId=iUserId, 
                    dictUsersConfig=dictUsersConfig, 
                    documents=documents,
                    eVoucherType=strVoucherType, 
                    # strRequestDocType=strRequestDocType,
                    bDeveloperMode= bTestMode,
                    bScanForVirus=bScanForVirus
                )
                strReportFilePath = dictStoreDocRes.get("ReportFilePath","")
                lsGrnFilesObj=dictStoreDocRes.get("lsGRNFiles",[])

                
            
                # -> START-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
                dtGenerated = dictExeUserDetails.get("ClientReqGeneratedAt", datetime.now())
                strCustName = dictExeUserDetails.get("CustomerName", "Unknown")
                iRequestID = None
                for doc in lsGrnFilesObj:
                    # Build the **kwargs bag for the helper
                    kw  = {
                        "NetworkLocation": {"path": getattr(doc, "filePath", "")},
                        "strVoucherType": strVoucherType,
                        "ReqDocType": getattr(doc, "FileType", "pdf"),
                        "ReqDocName": getattr(doc, "filename", ""),
                        "ReqDocHashCode": getattr(doc, "HashCode", ""),
                        "MultipleVendorEnabled": False,
                        "UServerName": DEVICE_NAME,
                        "strSystemUserName": strSystemName,
                        "PriceListVerification": False,
                        "strAccuVelocityComments": "-",
                        "TracebackLogs": "",
                        "strServerEstProcessingTime": "NOT_APPLICABLE",
                        "AVXMLGeneratedStatus": "NOT_APPLICABLE", # Please note use NOT_APPLICABLE when Multiple Records in case of single Row from this enum 'Success', 'Skipped', 'PartialSuccess', 'NOT_APPLICABLE'
                        "IsRecordUIDPresent": False, # True when any ID Foreign key
                        "EstAccountantTimeSaved": "NOT_APPLICABLE",
                        "ClientImportedXMLStatusCode":"NOT_APPLICABLE", # Default ClientImportedXMLStatusCode Value
                        "ClientImportedXMLResponse":"NOT_APPLICABLE",
                        "bTestMode": bTestMode
                    }
                    obj = await CAVRequestDetail.MSInsertAVRecordDetail(
                        strClientREQID=strClientREQID,
                        dtObjGeneratedTimeAt=dtGenerated,
                        iUserId=iUserId,
                        strRecievedDate=date.today(),
                        strCustomerName=strCustName,
                        **kw
                    )
                    iRequestID = obj.ID
                # <- END-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
            
            
                # Boolean To Download Tally ERP Matching File
                bDownloadERP = client_config.get('bDownloadERPMappingFile', False)
                
                lsFilesToSend = []
                lsAllGRNDetails = []
                for objGRNFile in lsGrnFilesObj:
                    strFilePath = objGRNFile.filePath
                    is_html = strFilePath.lower().endswith(('.html', '.htm'))
                    
                    if is_html: 
                        #Machinery GRN Processing
                        lsGRNProcessingInfo = await CAbhinavInfrabuildMachineryGRN.MSGetGRNXMLForMachinery(iUserId, iRequestID, strFilePath, bDeveloperMode=bTestMode, bDownloadERP = bDownloadERP)
                    else:
                        # All Other GRN Processing
                        lsGRNProcessingInfo = await CAbhinavInfrabuildGRN.MSGetAllGRNXML(iUserId, iRequestID, strFilePath, bDeveloperMode=bTestMode,bDownloadERP = bDownloadERP)
                    
                    for dictGRNProcessingInfo in lsGRNProcessingInfo:
                        lsAllGRNDetails.append(dictGRNProcessingInfo)
                        lsFilesToSend.append(dictGRNProcessingInfo.get("XMLFilePath"))
                    
                    CIndianInvTallyController.MSSavePOReport(lsGRNProcessingInfo, strReportFilePath)
            
                lsFilesToSend.append(strReportFilePath)
                
                # current request and Commulative statistics
                strTotalSavedTimeToday = CommonHelper.MSTotalTimeSaved(lsAllGRNDetails)
                dictProcesingStatsTillNow = await CAVGRNProcessingDetailsService.MSTotalTimeSavedTillNow(UserID=iUserId, time_per_item_in_seconds=CAbhinavInfrabuild._miTimeSavedPerPOItem)

                iTotalProcessedVouchers =   sum(1 for dictGRNDetails in lsAllGRNDetails if dictGRNDetails.get("AV_Status") == "Success")

                # ------------ Server Execution Time - Current Request ---------------- 
                dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                    user_id=iUserId, 
                    strClientReqID=strClientREQID,
                    isUseRequestTime = True
                )
                strExecutiontime=DateHelper.get_time_difference(dictdbRecord)

            
                VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"
                TallyReportSender.SendTallyNotificationEmailPurchaseOrder(
                        csvReportPath=CIndianInvTallyController._mReqReportPath, 
                        strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                        strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                        strMailFrom=os.getenv('MAIL_FROM'), 
                        lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>", "<EMAIL>"], 
                        strServer=os.getenv('MAIL_SERVER'), 
                        intPort=int(os.getenv('SMTP_PORT')), 
                        strPassword=os.getenv('MAIL_PASSWORD'), 
                        strVoucherType="Receipt Note",
                        strTodayTotalPostedVouchers=iTotalProcessedVouchers, 
                        strTotalTimeSavedToday=strTotalSavedTimeToday,
                        lsCC=client_config['lsEmailCC'],
                        lsAttachmentPath=[CIndianInvTallyController._mReqReportPath],
                        strSystemName=strSystemName,
                        strExecutiontime=strExecutiontime,
                        total_time_saved_till_now=dictProcesingStatsTillNow.get("total_time_saved"),
                        total_posted_vouchers_till_now=dictProcesingStatsTillNow.get("total_unique_entries")
                    )
                
                strClientResZipFile = CDirectoryHelper.create_zip_file(
                    lsFilesToSend, 
                    destination_path=CIndianInvTallyController._mstrClientResZipFile
                )

                if bScanForVirus:
                    objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                    dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                    
                    if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                        raise VirusDetected(file_path=strClientResZipFile)
                    
                return strClientResZipFile
            
            except VirusDetected as vd:
                await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

                try:
                    TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                                strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                                lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                                lsCC=client_config['lsEmailCC'])
                except Exception as e:
                    print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")

                raise vd
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")
                print(f"Error: {e}")
                raise e
     ### ---------------------------------- END GRN Processing--------------------------------------    
    
    ### ---------------------------------- START Bank Stament Processing--------------------------------------
    async def MSProcessBankStatement(iUserId, documents, strVoucherType, strClientREQID, bTestMode=False, bScanForVirus=True, strSystemName="", dictExeUserDetails={}):
        """
                Input:

                    1) iUserId: int
                    The ID of the user whose bank statements are being processed.

                    2) documents: list
                    A list of document objects (each a dict with `filename` and `content`) representing bank statements to process.

                    3) strVoucherType: str
                    The voucher type (e.g., "BankStmt") determining processing rules and target folder.

                    4) strClientREQID: str
                    A unique identifier for the client’s request, used for tracking and logging.

                    5) bTestMode: bool, optional (default False)
                    If True, runs the process in test mode without persisting files to production storage.

                    6) bScanForVirus: bool, optional (default True)
                    If True, performs a virus scan on each document before any further processing.

                    7) strSystemName: str, optional (default "")
                    The name of the calling system or integration, for audit and logging purposes.

                Output:

                    str:
                        The filesystem path to the ZIP archive containing the processed bank statement results.

                Purpose:

                    To process and archive bank statement documents by:
                    - Validating the user ID and client request ID.
                    - Optionally scanning each document for viruses.
                    - Parsing and extracting transaction data from each statement.
                    - Generating a summary report of transactions.
                    - Saving processed files and reports into a structured network location based on voucher type and date.
                    - Packaging all outputs into a ZIP archive and returning its path.
        """
        try:
            strTotalPredictedTransactionsTillNow = None
            strTotalTimeSavedTillNow = None
            
            # config
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            
            # Validate user ID and get data directory
            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            client_config = dictUsersConfig[str(iUserId)]
            client_data_dir = client_config['dataDirectory']
            today = datetime.now().strftime("%Y_%m_%d")
            today_download_dir = os.path.join(client_data_dir, today)
            strReqXMLDir = os.path.join(today_download_dir, strClientREQID)
            os.makedirs(strReqXMLDir, exist_ok=True)
            # Store Doc in Network Location
            # Create a deep copy
            # documents_copy = copy.deepcopy(documents)
            dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(
                iUserId=iUserId, 
                dictUsersConfig=dictUsersConfig, 
                documents=documents,
                eVoucherType=strVoucherType, 
                bDeveloperMode= bTestMode,
                bScanForVirus=bScanForVirus
            )
            strReportFilePath = dictStoreDocRes.get("ReportFilePath","")
            lsBankStatementPath=dictStoreDocRes.get("lsBankStatementFiles",[])

             # Validate No of Request, No of Pages Allowed Per Daynote
            if (IP_ADDRESS.split('.')[-1] in ['15']) or (DEVICE_NAME in ['DEV']):
                # Development environment
                python_executable_path = Path(r"5_Env/Scripts/python.exe")
                script_to_run = Path(r"//************/user_data/MITUL/Documents/Nisarg/AccuVelocity/prodBankStatementPrediction/src/main.py")
            elif (IP_ADDRESS.split('.')[-1] in ['2']):
                # Development environment
                python_executable_path = Path(r"5_Env/Scripts/python.exe")
                script_to_run = Path(r"F:/Mitul - AV Development/Customer/REAL/AccuVelocity/prodBankStatementPrediction/src/main.py")
            else:
                if platform.system() == "Linux":
                    python_executable_path = Path(r"/home/<USER>/AV Ahemdabad Server/AccuVelocity/5_Env/bin/python")
                    script_to_run = Path(r"/home/<USER>/AV Ahemdabad Server/AccuVelocity/prodBankStatementPrediction/src/main.py")
                else:
                    # Production environment
                    python_executable_path = Path(r"D:/Customer/REAL/AccuVelocity/prodBankStatementPrediction/envProdBankStatPred/Scripts/python.exe")
                    script_to_run = Path(r"D:/Customer/REAL/AccuVelocity/prodBankStatementPrediction/src/main.py") # if client_config['bankcustomerName'].lower() == "gwalia" else r"scripts\app.py"  # Path relative to the working directory, NOTE: Change as per Meetul Aggarwal
            company_name = client_config['company_name']
            bank_name = client_config['bank_name']  
            customer_name = client_config["customerName"]
            print(f"Executable File Path: {python_executable_path} Current Directory {script_to_run}")
            print(f"company_name - {company_name}, bank_name - {bank_name}")
            
            # input_file = r"data/airen_construction/boi_1/input_files/AIREN CONSTRUCTION PVT. LTD. 01-04-2024 TO 30-06-2024.xls"
            working_dir = r"prodBankStatementPrediction"  # Set your required working directory
            print(os.path.abspath(working_dir))
            lsXMLFilePath = []
            strFileStatusForMail = ""
            lsCSVRecords = []
            strResult = {}
            for dictReceivedFileInfo in lsBankStatementPath:
                strFilePath = dictReceivedFileInfo



                if customer_name.lower() == "vedansh school":
                    
                    # Step 1 : Identify the bank account and it's related bank name ------------------------------
                    # Resource files 
                    strGPTConfigPath = Path(r"resource/GPTConfig.json")
                    strResponseFormatFilePath = Path(r"Data/General_Config/BankStatements/ResponseFormat.json")

                    strGPTModel = CJSONFileReader.read_json_file(strGPTConfigPath)["gpt-model"] 
                    dictResponseFormat = CJSONFileReader.read_json_file(strResponseFormatFilePath) 

                    # Extract the Bank Account Number From the Satement
                    strUserContent = pd.read_excel(strFilePath).head(17).to_string()
                    objGPTResponse = await CGPTAPIResponse.MSCallGPTAPI(
                                                        strSystemContent = "Fetch Bank account number from given text.",
                                                        strUserContent = strUserContent,
                                                        strModel = strGPTModel, 
                                                        dictResponseFormat = dictResponseFormat,
                                                        bIsReasoningModel = False
                                                    )
                    
                    dictGPTResponse = json.loads(objGPTResponse["choices"][0]["message"]["content"])
                    iBankNumber = dictGPTResponse["BankAccountNumber"]

                    bank_name = client_config['bank_details']["account_details"][str(iBankNumber)]
                    # -------------------------------------------------------------------------------------------
                try:
                    objAVReqDetail = await CAVRequestDetail.MSInsertAVRequestDetailsFromBankStatementPaths(lsBankStatementPath=lsBankStatementPath,
                    strClientREQID=strClientREQID,
                    iUserId=iUserId,
                    strVoucherType=strVoucherType,
                    bTestMode = bTestMode,
                    dictExeUserDetails= dictExeUserDetails,
                    bScanForVirus = True,
                    bIsMultivendorDoc = False,
                    strSystemName= strSystemName,
                    User_UID=iUserId)
                except Exception as e:
                    await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")


                strResult = await CProcessBankStatement.MSRunCMD(
                    python_path=os.path.abspath(python_executable_path),
                    script_path=script_to_run,
                    company=company_name,
                    bank=bank_name,
                    file_path=strFilePath,
                    working_directory=os.path.abspath(working_dir),
                    debug=True,
                    strXMLOutputDir = strReqXMLDir,
                    strClientREQID = strClientREQID
                )
                
                try:
                    dictAVRes = strResult.get("AVBankStatusRes",{})
                    await CAVRequestDetail.MSUpdateRecordByClientREQID(
                                strClientREQID=strClientREQID,
                                AVXMLGeneratedStatus=dictAVRes.get("AVXMLGeneratedStatus","Skipped"),
                                strCustomerName = customer_name,
                                strAccuVelocityComments=dictAVRes.get("strAccuVelocityComments","The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."),
                                TracebackLogs = dictAVRes.get("TracebackLogs","Please check logs"),
                                BANKSTATEMENT_UID = strResult.get("BankStatement_UID",None)
                            )
                except Exception as e:
                    await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    
                
                if isinstance(strResult, str):
                    strFileStatusForMail += f"{strResult}\n"
                    if os.path.exists(strResult):
                        dest_file = os.path.join(today_download_dir, os.path.basename(strResult))
                        # Copy the file to the destination directory
                        shutil.copy(strResult, dest_file)
                        print(f"File copied to {dest_file}")
                        lsXMLFilePath.append(dest_file)
                
                elif isinstance(strResult, dict):
                    lsFilePath = strResult["FilePaths"] 

                    lsCSVRecords.extend(strResult["AVSummary"].get("Transactions", []))
                    
                    if isinstance(lsFilePath, list):
                        for file_path in lsFilePath:
                            if os.path.exists(file_path):
                                # dest_file = os.path.join(today_download_dir, os.path.basename(file_path))
                                # shutil.copy(file_path, dest_file)
                                print(f"File copied to {file_path}")
                                lsXMLFilePath.append(file_path)
                            else:
                                print(f"File '{file_path}' not found.")
                                strFileStatusForMail += f"File '{file_path}' not found.\n"
                else:
                    strFileStatusForMail += f"File '{strFilePath}' not processed.\n"

            CIndianInvTallyController.MSWriteBankStatementReport(list_of_dicts=lsCSVRecords, file_path=strReportFilePath)
            lsXMLFilePath.append(strReportFilePath)
            
            strReportStatisicFilePath = dictStoreDocRes.get("strReportStatisticFilePath","")
        
            CIndianInvTallyController.MSWriteBankStatementStatisticReport(list_of_dicts=strResult["AVSummary"], file_path=strReportStatisicFilePath)
            lsXMLFilePath.append(strReportStatisicFilePath)
            
            dictbankStatastics= await MGetUserReport(iUserId)
            if dictbankStatastics is not None:
                strTotalPredictedTransactionsTillNow=dictbankStatastics.get("intTillNowTotalTxns",0)
                strTotalTimeSavedTillNow=dictbankStatastics.get("intTillNowTotalTimeSaved",0)
            else:
                strTotalPredictedTransactionsTillNow = 0
                strTotalTimeSavedTillNow = 0
            
            
            TotalPredictedTransactions, strTotalTimeSavedToday = CIndianInvTallyController.MSGetEmailStatasticsForBankStatement([strResult.get("AVSummary")])

            # ------------ Server Execution Time - Current Request ---------------- 
            dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                user_id=iUserId, 
                strClientReqID=strClientREQID,
                isUseRequestTime = True
            )
            strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
            VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"
            TallyReportSender.SendTallyNotificationEmailBankStatement(csvReportPath=CIndianInvTallyController._mReqReportPath, 
                                                                      dictSummaryData=strResult.get("AVSummary"),
                strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                strMailFrom=os.getenv('MAIL_FROM'), 
                lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>","<EMAIL>", "<EMAIL>"], 
                strServer=os.getenv('MAIL_SERVER'), 
                intPort=int(os.getenv('SMTP_PORT')), 
                strPassword=os.getenv('MAIL_PASSWORD'), 
                htmlTemplatePath=Path(r"resource/TallyEmailTemplateBankStatement.html"), 
                lsCC=client_config['lsEmailCC'],
                TotalPredictedTransactions=TotalPredictedTransactions, 
                strTotalTimeSavedToday=strTotalTimeSavedToday,
                strTotalPredictedTransactionsTillNow=strTotalPredictedTransactionsTillNow, #TODO: Please add Dynamic Value FROM DB
                strTotalTimeSavedTillNow=strTotalTimeSavedTillNow, #TODO: Please add Dynamic Value FROM DB
                lsAttachmentPath=[CIndianInvTallyController._mReqReportPath],
                strSystemName=strSystemName,
                strExecutiontime = strExecutiontime
                )
            
            
            strClientResZipFile = CDirectoryHelper.create_zip_file(
                lsXMLFilePath, 
                destination_path=CIndianInvTallyController._mstrClientBankResZipFile
            )

            if bScanForVirus:
                objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                
                if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                    raise VirusDetected(file_path=strClientResZipFile)
                
            return strClientResZipFile
        
        except VirusDetected as vd:
            await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            try:
                TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                            strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                            lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                            lsCC=client_config['lsEmailCC'])
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")

            raise vd
        except Exception as e:
            print(f"Error: {e}")
            try:
                await CAVRequestDetail.MSUpdateRecordByClientREQID(
                        strClientREQID=strClientREQID,
                        AVXMLGeneratedStatus="Skipped",
                        strAccuVelocityComments="Please Enter data manually.",
                        TracebackLogs = "-"
                    )
            except Exception as e:
                print(f"Error: {e}")
            raise e
    ### ---------------------------------- END Bank Stament Processing--------------------------------------

    ### ---------------------------------- START Delivery Note Processing--------------------------------------
    async def MSProcessIndianDeliveryNoteDoc(strClientREQID,iUserId, documents,  dictExeUserDetails={},bTestMode=False, bScanForVirus=True, strSystemName="", checksum = "", strVoucherType=None):
        """
            Input:

                1) iUserId: int
                The ID of the user for whom the delivery note documents are being processed.

                2) documents: list
                A list of document objects (e.g., dicts with `filename` and `content`) representing Indian delivery note files to process.

                3) bTestMode: bool, optional (default False)
                If True, runs the process in test mode without persisting files to production storage.

                4) bScanForVirus: bool, optional (default True)
                If True, performs a virus scan on each document before any further processing.

            Output:

                str:
                    The filesystem path to the ZIP archive containing the processed delivery note documents and any generated reports.

            Purpose:

                To process and archive Indian delivery note documents by:
                - Validating the provided user ID.
                - Optionally scanning each document for viruses.
                - Parsing delivery note details (e.g., item descriptions, quantities, dates).
                - Generating a summary report of all delivery notes processed.
                - Saving processed files and reports to a structured network location.
                - Packaging all outputs into a ZIP archive and returning its path.
        """
        try:
            dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
            dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(  iUserId=iUserId, 
                                                                                        dictUsersConfig=dictUsersConfig, 
                                                                                        documents=documents,
                                                                            eVoucherType=strVoucherType, bDeveloperMode=bTestMode, bScanForVirus=bScanForVirus)
            # Files Client Response - Report , Pricelist , Json , XML Response 
            lsClientResponseFiles = []

            # config
            
            # Validate user ID and get data directory
            if str(iUserId) not in dictUsersConfig:
                raise HTTPException(status_code=404, detail="User ID not found in configuration.")

            # Todo: For DB Record Storing in future changes
            client_config = dictUsersConfig[str(iUserId)]
            strCustomerName = dictUsersConfig[str(iUserId)]['customerName'] 

            # Store Doc in Network Location
            
            objAVReqDetail = await CAVRequestDetail.MSInsertAVRequestDetailsFromStoredDocs(dictStoreDocRes=dictStoreDocRes,
                strClientREQID=strClientREQID,
                iUserId=iUserId,
                strVoucherType=strVoucherType,
                bTestMode = bTestMode,
                dictExeUserDetails= dictExeUserDetails,
                bScanForVirus = True,
                bIsMultivendorDoc = False,
                strSystemName= strSystemName,
                User_UID=iUserId)
            lsClientInputDocs = dictStoreDocRes.get("Docs")
            lsClientResponseFiles.extend(dictStoreDocRes.get("lsFilePath"))    # Adding report file to be sent to the client for current process
            
            if not lsClientInputDocs:
                raise HTTPException(
                    status_code=400,
                    detail="No documents provided. Please upload at least one document."
                )
            
            lsQuotationsDict = [] # List of Quotation Dict
            
            lsTemplateAdditionalInfo: List[ClientDNReqObj] = []  # List to store additionalinfo objects for Quotation
            # Verify Received Files --- Initialize an empty list to store filenames
            lsFileNames = [objDocs.filename for objDocs in lsClientInputDocs]  # Optimized list comprehension

            # Exclude files that contain 'av_quotation_additionaldetails'
            filteredFileNames = [filename for filename in lsFileNames if 'av_quotation_additionaldetails' not in filename.lower()]

            # Check if the count of received files (after exclusion) is zero
            if not filteredFileNames:
                error_message = (
                    f"Please provide the Quotation Excel file for processing. "
                    f"We have only received the following file(s): {', '.join(lsFileNames)}"
                )
                raise HTTPException(status_code=404, detail=error_message)
            for objDocs in lsClientInputDocs:
                try:
                    if (objDocs.content_type in [
                            "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
                            "application/vnd.ms-excel"
                        ]):
                        
                        # If Template then parse template data                
                        if "av_quotation_additionaldetails" in objDocs.filename.lower().strip():
                            dfExcelTemplate = CExcelHelper.MSReadExcel( file_path=objDocs.filePath, 
                                                                        columns_to_verify=CIndianInvTallyController._msLsAdditionaDocColumnsForDeliveryNote, 
                                                                        bVerify=True)
                            # Replace NaN with an empty string
                            dfExcelTemplate.fillna('', inplace=True)
                            # Replace NaN with an empty string and ensure data types are preserved
                            for column in dfExcelTemplate.select_dtypes(include=['float', 'int']).columns:
                                dfExcelTemplate[column] = dfExcelTemplate[column].fillna(0)  # Replace NaN with 0 for numeric columns

                            # Replace NaN in non-numeric columns with an empty string
                            for column in dfExcelTemplate.select_dtypes(exclude=['float', 'int']).columns:
                                dfExcelTemplate[column] = dfExcelTemplate[column].fillna('')
                            for _, row in dfExcelTemplate.iterrows():
                                objQuotationAdditionalInfo = ClientDNReqObj()  # Create a new instance for each row
                                objQuotationAdditionalInfo.strQuotUniqueNumber= str(row["QuotationNumber"])       # ToDo: Update this to have actual value, where to find this 
                                objQuotationAdditionalInfo.strQuotItemSRNO = str(row["ItemSRNO"])
                                objQuotationAdditionalInfo.strTallyClientName = str(row["ClientName"])
                                objQuotationAdditionalInfo.strOrderTerms1 = str(row["OrderTerms1"])
                                objQuotationAdditionalInfo.strOrderTerms2 = str(row["OrderTerms2"])
                                objQuotationAdditionalInfo.strClientAddress1 = str(row["ClientAddress1"])
                                objQuotationAdditionalInfo.strClientAddress2 = str(row["ClientAddress2"])
                                objQuotationAdditionalInfo.strClientAddress3 = str(row["ClientAddress3"])
                                objQuotationAdditionalInfo.strClientAddress4 = str(row["ClientAddress4"])
                                objQuotationAdditionalInfo.strClientState = str(row["ClientState"])
                                objQuotationAdditionalInfo.strClientCountry = row["ClientCountry"]
                                objQuotationAdditionalInfo.strClientPinCode = str(row["ClientPinCode"])
                                objQuotationAdditionalInfo.strClientGSTRegistrationType = str(row["ClientGSTRegistrationType"])
                                objQuotationAdditionalInfo.strClientGSTIN = str(row["ClientGSTIN"])
                                
                                lsTemplateAdditionalInfo.append(objQuotationAdditionalInfo)  # Add to the list

                        # Else Assume it to be the Quotation Excel
                        else:
                            dictQuotationExcel = CQuotation.MSReadQuotationFromExcel(objDocs.filePath)
                            lsQuotationsDict.append(dictQuotationExcel)
                except Exception as e:
                    print(f"There was some error: {e}")
                    print(traceback.print_exc())
                    if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                        raise HTTPException(status_code=e.status_code, detail=e.detail)
                    else:
                        raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")

            lsTallyXMLFilePath = []
            lsReportRows = []
            for index, dictQuotationDetails in enumerate(lsQuotationsDict):
                dictRowReportEntry = {
                                        "SR No": int(index) + 1,
                                        "Quotation Number": dictQuotationDetails.get("DocumentNumber", "-"),
                                        "Quotation Date": dictQuotationDetails.get("DocumentCreatedOn", "-"),
                                        "Client Name": dictQuotationDetails.get("ClientName", "-"),  
                                        "AVTally Status": "-",
                                        "AccuVelocity Comments":"-"
                                    }
                try:
                    # Use `next()` to get the first matching object or return `None` if no match is found
                    objMatchingTemplateAdditionalInfo = next(
                                            (   objTemplateAdditionalInfo for objTemplateAdditionalInfo in lsTemplateAdditionalInfo 
                                                if objTemplateAdditionalInfo.strQuotUniqueNumber == dictQuotationDetails["DocumentNumber"]
                                            ),
                                            None
                                        )
                    
                    if not objMatchingTemplateAdditionalInfo or objMatchingTemplateAdditionalInfo is None:
                        objMatchingTemplateAdditionalInfo=ClientDNReqObj(strQuotationDocName = documents[0].filename)
                    
                    strTallyXMLFilePath, lsTransactionDetail = await CQuotation.MSCreateXMLObj(  dictGPTAPIResponse=dictQuotationDetails, 
                                                                                iUserId=iUserId, 
                                                                                objClientDNReqObj=objMatchingTemplateAdditionalInfo,
                                                                                strClientREQID=strClientREQID
                                                                            )
                    lsTallyXMLFilePath.append(strTallyXMLFilePath)
                    # Network location update
                    await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                        iUserId=iUserId,
                        dictNewData = {"XMLFilePath": [strTallyXMLFilePath]},
                        eMode = ENetworkLocationUpdateMode.APPEND,
                        strClientREQID=strClientREQID)
                    dictRowReportEntry["AVTally Status"] = CQuotation._msAVTallyStatus
                    dictRowReportEntry["AccuVelocity Comments"] = CQuotation._msAccuVelocityComments
                    if CQuotation._msAVTallyStatus in ["Success","PartialSuccess"]:
                        # Supposing the name of the Vendor is Quotation
                        strVendorName = "Quotation"
                        strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name=strVendorName, no_of_stock_items=CQuotation._miTotalTallyInsertedItem)
                    else:
                        strTimeSaved = "NOT_APPLICABLE"

                    with open(strTallyXMLFilePath, 'r') as file:
                        strXMLData = file.read()
                    await CAVRequestDetail.MSUpdateDeliveryNoteRecord(
                        iUserId=iUserId,
                        strClientREQID=strClientREQID,
                        ReqDocName=documents[0].filename,
                        strCustomerName = strCustomerName,
                        AVXMLGeneratedStatus= dictRowReportEntry["AVTally Status"],
                        strAccuVelocityComments=dictRowReportEntry["AccuVelocity Comments"],
                        TracebackLogs = CQuotation._mStrTracebackLogs, 
                        EstAccountantTimeSaved = strTimeSaved,
                        strXMLResponse = strXMLData
                    )
                    
                except Exception as e:
                    dictRowReportEntry["AVTally Status"] = "Skipped"
                    dictRowReportEntry["AccuVelocity Comments"] = CQuotation._msAccuVelocityComments
                    await CAVRequestDetail.MSUpdateDeliveryNoteRecord(
                        iUserId=iUserId,
                        strClientREQID=strClientREQID,
                        ReqDocName=documents[0].filename,
                        strCustomerName = strCustomerName,
                        AVXMLGeneratedStatus=dictRowReportEntry["AVTally Status"],
                        strAccuVelocityComments=dictRowReportEntry["AccuVelocity Comments"],
                        TracebackLogs = CQuotation._mStrTracebackLogs
                    )
                    print(f"There was some error: {e}")
                    print(traceback.print_exc())
                lsReportRows.append(dictRowReportEntry)
                    
            lsClientResponseFiles.extend(lsTallyXMLFilePath)  # Adding current xml files to be sent to client 
            
            iCummulativeDocumentProcessed = 0
            
            # Update Total statistics in db
            dictDetail = {
                "total_count":0,
                "all_time_saved":[]
            }
            try:
                dictDetail = await CTallyController.MSGetDetailSummaryDeliveryNote(iUserId, documents[0].filename)
                iCummulativeDocumentProcessed = dictDetail['total_count']
            except Exception as e:

                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to get the statistics, Error:{e}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                pass
            try:
                strFormattedTimeSavedTillNow = str(CommonHelper.MSGetTotalTimeSaved(dictDetail['all_time_saved'])[0]) # + " Minutes"
                await CIndianInvTallyController.MSUpdateReportOfQuotatuon(CIndianInvTallyController._mReqReportPath, lsTransactionDetail)
                VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"

                TallyReportSender.SendTallyNotificationEmail(csvReportPath=CIndianInvTallyController._mReqReportPath, 
                                            strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                                            strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                                            strMailFrom=os.getenv('MAIL_FROM'), 
                                            lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>", "<EMAIL>", "<EMAIL>"], 
                                            strServer=os.getenv('MAIL_SERVER'), 
                                            intPort=int(os.getenv('SMTP_PORT')), 
                                            strPassword=os.getenv('MAIL_PASSWORD'), 
                                            htmlTemplatePath=Path(r"resource/TallyEmailTemplataDeliveryNote.html"), 
                                            lsCC=client_config['lsEmailCC'],
                                            strTotalPagesProcessedToday=1, 
                                            strTotalTimeSavedToday=strTimeSaved,
                                            strTotalPagesProcessedTillNow=iCummulativeDocumentProcessed,
                                            strTotalTimeSavedTillNow=strFormattedTimeSavedTillNow,
                                            lsAttachmentPath=[CIndianInvTallyController._mReqReportPath],
                                            strSystemName=strSystemName,
                                            strExecutiontime=strTimeSaved
                                            
                                            )
            except Exception as e:
                print(traceback.print_exc())
                print(f"There was some error writing the report file for quotaion document, Error: {e}")
                pass # TODO: Verify By Dhruvin
                # await self.log_to_db(client_config['userId'], 'ERROR', f"!!  Failed to update csv report at location '{self.report_file}'. Error:{e} !!")

            strClientResZipFile = CDirectoryHelper.create_zip_file(lsClientResponseFiles, destination_path= CIndianInvTallyController._mstrClientResZipFile)
            
            if bScanForVirus:
                objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)

                if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                    raise VirusDetected(file_path=strClientResZipFile)
            
            return strClientResZipFile

        except VirusDetected as vd:
            await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            try:
                TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                            strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                            lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                            lsCC=client_config['lsEmailCC'])
            except Exception as e:
                print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")

            raise vd
        
        except Exception as e:
            CLogController.MSWriteLog(iUserId, "ERROR",f"Error Occured While Processing Delivery Note - {str(traceback.format_exc())}")
            print("Error Occur - ", traceback.print_exc())
            
            # AVRecordDetail Update  
            await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, TracebackLogs=f"Error - TALLY XML FILE SAVING: {traceback.format_exc()}")
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
            if hasattr(e, 'status_code') and hasattr(e, 'detail'):
                raise HTTPException(status_code=e.status_code, detail=e.detail)
            else:
                raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")
    ### ---------------------------------- END Delivery Note Processing--------------------------------------

    def MSWriteBankStatementStatisticReport(list_of_dicts, file_path="output.csv"):
        """
        Writes a list of dictionaries to a CSV file. Automatically detects column headers
        from the first dictionary. Handles missing keys by filling with empty strings.
        Formats datetime values as DD-MM-YYYY.

        Args:
            list_of_dicts (list): List of dictionaries to write to the CSV file.
            file_path (str): The path where the CSV file should be saved.
        """
        try:
            keys = list(list_of_dicts.keys())
            transactions_index = keys.index('Transactions')
            filtered_keys = keys[:transactions_index]
            filtered_dict = {k: list_of_dicts[k] for k in filtered_keys}

            with open(file_path, mode='w', newline='', encoding='utf-8') as csv_file:
                writer = csv.DictWriter(csv_file, fieldnames=filtered_keys)

                writer.writeheader()
                writer.writerow(filtered_dict)

            print(f"CSV file '{file_path}' has been written successfully.")
            return file_path
        except Exception as e:
            print(f"Failed to write CSV file. Error: {e}")
            raise e
        
    ### ----------------------------------Bank Statement Report Writing-------------------------------------- 
    @staticmethod
    def MSWriteBankStatementReport(list_of_dicts, file_path="output.csv"):
        """
        Writes a list of dictionaries to a CSV file. Automatically detects column headers
        from the first dictionary. Handles missing keys by filling with empty strings.
        Formats datetime values as DD-MM-YYYY.

        Args:
            list_of_dicts (list): List of dictionaries to write to the CSV file.
            file_path (str): The path where the CSV file should be saved.
        """
        try:
            if not list_of_dicts:
                raise ValueError("Cannot write CSV: list_of_dicts is empty.")

            # Get columns from keys of the first dictionary
            columns = list(list_of_dicts[0].keys())

            with open(file_path, mode='w', newline='', encoding='utf-8') as csv_file:
                writer = csv.DictWriter(csv_file, fieldnames=columns)

                writer.writeheader()

                for row_dict in list_of_dicts:
                    filled_row = {}
                    for key in columns:
                        value = row_dict.get(key, '')
                        if isinstance(value, datetime):
                            value = value.strftime('%d-%m-%Y')
                        filled_row[key] = value
                    writer.writerow(filled_row)

            print(f"CSV file '{file_path}' has been written successfully.")
            return file_path

        except Exception as e:
            print(f"Failed to write CSV file. Error: {e}")

    
    ### ----------------------------------Emailing Statistics for Bank Statement--------------------------------------
    @staticmethod
    def MSGetEmailStatasticsForBankStatement(records):
        """
            Input:

                1) records: list
                A list of email record dictionaries to analyze for bank statement transactions.

            Output:

                tuple:
                    int: The total number of predicted transactions extracted from the email records.
                    str: A summary message or additional information about the prediction process.

            Purpose:

                To analyze a collection of email records and compute statistics for bank statement transactions by:
                - Parsing each record for transaction-related content.
                - Predicting and counting the number of transactions referenced.
                - Returning both the count and a human-readable summary or status message.
        """
        total_predicted_transactions = 0
        total_seconds = 0
        try:

            for record in records:
                # Extract time and total predicted transactions
                time_saved = record['Current Processing Stats'].strip()
                predicted_transactions = record['Total No. of Predicted Transactions']

                # Split the time string into minutes and seconds
                minutes_seconds = time_saved.split(" :")
                minutes = int(minutes_seconds[0].replace("Minutes", "").strip())
                seconds = int(minutes_seconds[1].replace("Seconds", "").strip())

                # Add to the total time in seconds
                total_seconds += (minutes * 60 + seconds)
                total_predicted_transactions += predicted_transactions

            # Calculate total time in minutes and seconds
            total_time = str(timedelta(seconds=total_seconds))
            
            # Handling the case where time may have hours
            time_parts = total_time.split(":")
            if len(time_parts) == 3:
                hours, minutes, seconds = time_parts
                total_time_in_format = f"{int(hours)} Hours : {int(minutes)} Minutes : {int(seconds)} Seconds"
            elif len(time_parts) == 2:
                minutes, seconds = time_parts
                total_time_in_format = f"{int(minutes)} Minutes : {int(seconds)} Seconds"

            return total_predicted_transactions, total_time_in_format
        except Exception as e:
            print(f"Error Occurred in Get Email Statistics For Bank Statement : {traceback.format_exc()}")
            return total_predicted_transactions, ""
   
   
    @staticmethod
    async def MSProcessAbhinavImprestJournal(strClientREQID, dictExeUserDetails, iUserId, documents, strVoucherType, bTestMode=False, bScanForVirus=False,strSystemName="-",bDownloadERPFile = False):
         
         
        try:
                
                # config
                dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)
                
                # Validate user ID and get data directory
                if str(iUserId) not in dictUsersConfig:
                    raise HTTPException(status_code=404, detail="User ID not found in configuration.")
                
                client_config = dictUsersConfig[str(iUserId)]
                bDownloadERPFile = client_config.get('bDownloadERPMappingFile',False)
                
                dictStoreDocRes = await CIndianInvTallyController.MSStoreDocInNetLocation(
                    iUserId=iUserId, 
                    dictUsersConfig=dictUsersConfig, 
                    documents=documents,
                    eVoucherType=strVoucherType, 
                    # strRequestDocType=strRequestDocType,
                    bDeveloperMode= bTestMode,
                    bScanForVirus=bScanForVirus
                )
                strReportFilePath = dictStoreDocRes.get("ReportFilePath","")
                lsAbhinavImprestFiles=dictStoreDocRes.get("lsAbhinavImprestFiles",[])

                # -> START-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
                dtGenerated = dictExeUserDetails.get("ClientReqGeneratedAt", datetime.now())
                strCustName = dictExeUserDetails.get("CustomerName", "Unknown")
                for doc in lsAbhinavImprestFiles:
                    # Build the **kwargs bag for the helper
                    kw  = {
                        "NetworkLocation": {"path": getattr(doc, "filePath", "")},
                        "strVoucherType": strVoucherType,
                        "ReqDocType": getattr(doc, "FileType", "pdf"),
                        "ReqDocName": getattr(doc, "filename", ""),
                        "ReqDocHashCode": getattr(doc, "HashCode", ""),
                        "MultipleVendorEnabled": False,
                        "UServerName": DEVICE_NAME,
                        "strSystemUserName": strSystemName,
                        "PriceListVerification": False,
                        "strAccuVelocityComments": "-",
                        "TracebackLogs": "",
                        "strServerEstProcessingTime": "NOT_APPLICABLE",
                        "AVXMLGeneratedStatus": "NOT_APPLICABLE", # Please note use NOT_APPLICABLE when Multiple Records in case of single Row from this enum 'Success', 'Skipped', 'PartialSuccess', 'NOT_APPLICABLE'
                        "IsRecordUIDPresent": False, # True when any ID Foreign key
                        "EstAccountantTimeSaved": "NOT_APPLICABLE",
                        "ClientImportedXMLStatusCode":"NOT_APPLICABLE", # Default ClientImportedXMLStatusCode Value
                        "ClientImportedXMLResponse":"NOT_APPLICABLE",
                        "bTestMode": bTestMode
                    }
                    obj = await CAVRequestDetail.MSInsertAVRecordDetail(
                        strClientREQID=strClientREQID,
                        dtObjGeneratedTimeAt=dtGenerated,
                        iUserId=iUserId,
                        strRecievedDate=date.today(),
                        strCustomerName=strCustName,
                        **kw
                    )
                
                # <- END-------------- Add Current Request Details in AVRequestDetails Table -----------------------------------
            
            
            
                lsFilesToSend = []
                # NOTE: Only Process Single Excel
                for objExcelFile in lsAbhinavImprestFiles:
                    strFilePath = objExcelFile.filePath
                    strReqGenereated = str(datetime.now()) 

                    result =await CImprestJournal.MSCreateXMLS(iUserId=iUserId, strExcelPath=strFilePath, strClientREQID=strClientREQID, strReqGenereated = strReqGenereated, bDeveloperMode=bTestMode, bDownloadERPFile = bDownloadERPFile)
                

                lsFilesToSend = result.get('saved_xml_files_path',[])
                ImprestReportGenerator.create_csv_report(result.get('lsReportDict'),strReportFilePath)
                lsFilesToSend.append(strReportFilePath)
            

                # ------------ Server Execution Time - Current Request ---------------- 
                dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                    user_id=iUserId, 
                    strClientReqID=strClientREQID,
                    isUseRequestTime = True
                )
                strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
                summaryy_details =await CImprestJournalDetails.MSGetJournalSummary(strClientREQID, iUserId)
                iTotalTodaysEntries = summaryy_details['todays_entry']
                strTotalSavedTimeToday = str(CommonHelper.MSGetTotalTimeSaved(summaryy_details['filtered_est_accountant_time_saved'])[0])
                iTotalTimeSaved =str(CommonHelper.MSGetTotalTimeSaved(summaryy_details['all_est_accountant_time_saved'])[0])
                iTotalProcessedVouchers = summaryy_details['total_entries_sum']
                VoucherType = getattr(strVoucherType, "name", strVoucherType.name or strVoucherType) if strVoucherType else "Unknown Voucher Type"
                TallyReportSender.SendTallyNotificationEmailImprestJournal(
                        csvReportPath=CIndianInvTallyController._mReqReportPath, 
                        strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM",
                        strSubject=f"{client_config['customerName']} - {VoucherType} Report" if not bTestMode else f"AV DEVELOPER MODE - {VoucherType} Report", 
                        strMailFrom=os.getenv('MAIL_FROM'), 
                        lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>", "<EMAIL>","<EMAIL>"], 
                        strServer=os.getenv('MAIL_SERVER'), 
                        intPort=int(os.getenv('SMTP_PORT')), 
                        strPassword=os.getenv('MAIL_PASSWORD'), 
                        strVoucherType=VoucherType,
                        strTodayTotalPostedVouchers=iTotalTodaysEntries, 
                        strTotalTimeSavedToday=strTotalSavedTimeToday,
                        lsCC=client_config['lsEmailCC'],
                        lsAttachmentPath=[CIndianInvTallyController._mReqReportPath],
                        strSystemName=strSystemName,
                        strExecutiontime=strExecutiontime,
                        total_time_saved_till_now=iTotalTimeSaved,
                        total_posted_vouchers_till_now=iTotalProcessedVouchers
                    )
                
                strClientResZipFile = CDirectoryHelper.create_zip_file(
                    lsFilesToSend, 
                    destination_path=CIndianInvTallyController._mstrClientResZipFile
                )

                if bScanForVirus:
                    objAVScanner = ClamAVScanner(log_dir=dictStoreDocRes["DataDirectory"], quarantine_dir=os.path.join(dictStoreDocRes["DataDirectory"], "QuarantinedFiles"))
                    dictAVScanResult = objAVScanner.scan(input_data=strClientResZipFile)
                    
                    if dictAVScanResult and dictAVScanResult["Scanned"] and dictAVScanResult["Infected"]:
                        raise VirusDetected(file_path=strClientResZipFile)
                    
                return strClientResZipFile
            
        except VirusDetected as vd:
                await CLogController.MSWriteLog(iUserId, "Error", f"Virus Detected:{vd}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

                try:
                    TallyReportSender.SendVirusDetectedNotification(strReceiverName=client_config['customerName'] if not bTestMode else "AV DEVELOPER TEAM", 
                                                                strInfectedFilePath= vd.file_path if hasattr(vd, "file_path") else "", 
                                                                lsMailTo=client_config['lsEmailReceivers'] if not bTestMode else ["<EMAIL>","<EMAIL>"], 
                                                                lsCC=client_config['lsEmailCC'])
                except Exception as e:
                    print(f"!! Failed to Send Virus Detected Mail: {str(traceback.format_exc())}")

                raise vd
        except Exception as e:
                print(f"Error: {e}")
                raise e
