import { useEffect, useState } from 'react';
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';
import axios from 'axios';
import { jwtDecode } from "jwt-decode";
import {
    pageLimitLeftAtom,
    totalPageLimitLeftAtom,
    freePageLimitUsageAtom,
    totalFreePageLimitAtom,
    isTrialPaidDocExtractionAtom,
    ActivePlanNameAtom
} from '../../context/TrailUsageUserData.jsx'; // Ensure this path is correct based on your file structure
import { Tooltip } from "@material-tailwind/react";
import { useUserNameSetter } from "../../context/userData.jsx";
import liteModelSVG from "../../assets/IconModelLite.svg";
import proModelSVG from "../../assets/IconModelPro.svg";
import { useNavigate } from 'react-router-dom';
import { setCookie, getCookie } from '../../utils/cookieUtils.js';
import switchPath from '../../assets/Strings/switchPath.js'
import switchSvg1 from '../../assets/Strings/switchPath1.js'
import toast, { Toaster } from 'react-hot-toast';

const TrialUserBox = ({ initialPageLimitLeft = null, initialTotalPageLimitLeft = null, initialFreePageLimitUsage = null, initialTotalFreePageLimit = null }) => {
    const [pageLimitLeft, setPageLimitLeft] = useRecoilState(pageLimitLeftAtom);
    const [totalPageLimitLeft, setTotalPageLimitLeft] = useRecoilState(totalPageLimitLeftAtom);
    const [freePageLimitUsage, setFreePageLimitUsage] = useRecoilState(freePageLimitUsageAtom);
    const [totalFreePageLimit, setTotalFreePageLimit] = useRecoilState(totalFreePageLimitAtom);
    const [activePlanName, setActivePlanName] = useRecoilState(ActivePlanNameAtom);
    const [isTrialPaidDocExtraction, setIsTrialPaidDocExtraction] = useRecoilState(isTrialPaidDocExtractionAtom);
    const [currentToastId, setCurrentToastId] = useState(null);
    const [tooltipContent, setTooltipContent] = useState('');
    const setUserName = useUserNameSetter()
    const navigate = useNavigate();
    useEffect(() => {

        if (initialPageLimitLeft !== undefined && initialTotalPageLimitLeft !== undefined && initialPageLimitLeft && initialTotalPageLimitLeft &&
            initialFreePageLimitUsage !== undefined && initialTotalFreePageLimit !== undefined && initialFreePageLimitUsage && initialTotalFreePageLimit
        ) {
            setPageLimitLeft(initialPageLimitLeft);
            setTotalPageLimitLeft(initialTotalPageLimitLeft);
            setFreePageLimitUsage(initialFreePageLimitUsage);
            setTotalFreePageLimit(initialTotalFreePageLimit);
            setCookie("pageLimitLeft", initialPageLimitLeft, 3);
            setCookie("totalPageLimitLeft", initialTotalPageLimitLeft, 3);
            setCookie("freePageLimitUsage", initialFreePageLimitUsage, 3);
            setCookie("totalFreePageLimit", initialTotalFreePageLimit, 3);
        } else {
            const fetchData = async () => {
                try {
                    const token = localStorage.getItem("token"); // Replace with the actual token
                    const decodedToken = jwtDecode(token);
                    const userId = decodedToken.id;
                    const userResponse = await axios.get(`${import.meta.env.VITE_SERVER}/users/${userId}`, {
                        headers: {
                            "Authorization": `Bearer ${token}`,
                        },
                    });

                    const pageLimitLeft = userResponse.data.page_limit_left;
                    const totalPageLimitLeft = userResponse.data.total_allowed_page_limit;
                    const freePageLimitUsage = userResponse.data.free_page_limit_usage;
                    const totalFreePageLimit = userResponse.data.total_allowed_free_page_limit;
                    const active_plan = user.active_plan;

                    setPageLimitLeft(pageLimitLeft);
                    setTotalPageLimitLeft(totalPageLimitLeft);
                    setFreePageLimitUsage(freePageLimitUsage);
                    setTotalFreePageLimit(totalFreePageLimit);
                    setActivePlanName(active_plan)
                    setCookie("pageLimitLeft", pageLimitLeft, 3);
                    setCookie("totalPageLimitLeft", totalPageLimitLeft, 3);
                    setCookie("freePageLimitUsage", freePageLimitUsage, 3);
                    setCookie("totalFreePageLimit", totalFreePageLimit, 3);
                    const userName = userResponse.data.name;
                    setUserName(userName)
                } catch (error) {
                    console.error('Error fetching user data:', error);
                }
            };

            fetchData();
        }
    }, []);


    useEffect(() => {
        if (pageLimitLeft === undefined || pageLimitLeft === null) {
            const cookieValue = getCookie("pageLimitLeft");
            setPageLimitLeft(cookieValue ? cookieValue : 0);
        }
        if (totalPageLimitLeft === undefined || totalPageLimitLeft === null) {
            const cookieValue = getCookie("totalPageLimitLeft");
            setTotalPageLimitLeft(cookieValue ? cookieValue : 12);
        }
        if (freePageLimitUsage === undefined || freePageLimitUsage === null) {
            const cookieValue = getCookie("freePageLimitUsage");
            setFreePageLimitUsage(cookieValue ? cookieValue : 0);
        }
        if (totalFreePageLimit === undefined || totalFreePageLimit === null) {
            const cookieValue = getCookie("totalFreePageLimit");
            setTotalFreePageLimit(cookieValue ? cookieValue : 50);
        }
        if ((totalPageLimitLeft - pageLimitLeft) === (totalPageLimitLeft)) {
            setTooltipContent("You've reached your limit of pro pages.");
        }
        else {
            setTooltipContent("Pro Mode");
        }
    }, [pageLimitLeft, totalPageLimitLeft, freePageLimitUsage, totalFreePageLimit]);
    const handleTopUpPlans = () => {
        console.log(activePlanName)
        if (activePlanName == "Free") { // Assuming isPremiumUser is a flag that indicates if the user is a premium subscriber
            // Display the toast message
            if (currentToastId !== null) {
                toast.dismiss(currentToastId);
            }
            setCurrentToastId(toast.error(
                <div>
                    Top up plan is only available to our premium subscribed users. Please click
                    <a href="/upgradeplan" style={{ color: '#003654', textDecoration: 'underline', marginLeft: '5px' }}>
                        <strong>Upgrade</strong>
                    </a> {' '}
                    to subscribe.
                </div>,
                {
                    type: 'error',
                    icon: '⚠️',
                    duration: 6000
                }
            ))
            navigate("/UpgradePlan");
        } else {
            navigate("/AddOnPages")
        }
    }
    return (
        <div className='flex'>
            <div
                className="shadow-lg z-10 flex justify-between space-x-4 items-center p-[0.5rem] bg-[#003654] text-white rounded-full  sticky top-[10%]"
            >
                {/* Icon and Text Section */}
                <div className="flex justify-between items-center">
                    <div className="" style={{ backgroundColor: "#003654", color: "#003654" }}>
                        <svg width="18" height="25" viewBox="0 0 18 25" xmlns="http://www.w3.org/2000/svg" className="xl:w-[14px]" style={{ margin: "0 14px", fill: "#ffffff" }}>
                            <path d={switchSvg1} />
                        </svg>
                    </div>

                    <div className="md:text-xs lg:text-xs xl:text-xs xl:w-[4rem] 2xl:w-[4rem] 2xl:text-xs 2xl:text-[0.77rem]" style={{ color: "#ffffff", fontWeight: 500 }}>
                        {isTrialPaidDocExtraction ? `Pro Page Limit Used` : `Lite Page Limit Used`}
                    </div>
                </div>

                {/* Pages Section */}
                <div className="text-4xl font-medium md:text-xs lg:text-lg xl:text-xl 2xl:text-xl" style={{ color: "#ffffff" }}>
                    <Tooltip content={isTrialPaidDocExtraction ? `${totalPageLimitLeft - pageLimitLeft} / ${totalPageLimitLeft}`
                        : `${freePageLimitUsage} / ${totalFreePageLimit}`}>
                        <span className="md:inline lg:inline xl:inline 2xl:inline">
                            {isTrialPaidDocExtraction
                                ? `${(totalPageLimitLeft - pageLimitLeft)} / ${totalPageLimitLeft}`: `${freePageLimitUsage} / ${totalFreePageLimit}`}
                        </span>
                    </Tooltip>

                </div>

                {/* Switch Section */}
                <div
                    className={`shadow-lg lg:h-[3rem] lg:text-[0.9rem] xl:h-[4rem] 2xl:h-[3.2rem] xl:w-[15.9rem] flex items-center p-[0.8rem] bg-white text-white rounded-full w-[200px] sticky top-[10%]`}
                >
                    <div className={'pl-2 pr-3 font-semibold xl:text-sm 2xl:text-sm 2xl:leading-[0.9rem]'} style={{ color: "#003654" }}>
                        Lite Mode
                    </div>

                    <div>
                        <button
                            className={`w-14 h-[1.3rem] flex items-center  rounded-full  duration-300 ease-in-out border-2 ${isTrialPaidDocExtraction ? 'bg-[#003654]' : 'bg-[#CCCCCC]'
                                }`}
                            onClick={() => {
                                setIsTrialPaidDocExtraction(!isTrialPaidDocExtraction);
                            }}
                        >
                            <div
                                className={`bg-[#ffff] w-8 h-8 rounded-full shadow-md transform border-2 border-[#fffff] ${isTrialPaidDocExtraction ? 'translate-x-5' : ''
                                    }`}
                            >
                                <img
                                    src={isTrialPaidDocExtraction ? proModelSVG : liteModelSVG}
                                    className="w-full h-full"
                                />
                            </div>
                        </button>
                    </div>

                    <div className={'pl-5 pr-2 font-semibold xl:text-sm 2xl:text-sm 2xl:leading-[0.9rem]'} style={{ color: "#003654" }}>
                        Pro Mode
                    </div>
                </div>
            </div>
            <div className='content-center'>
                <Tooltip content={isTrialPaidDocExtraction ? "Add-on More Pages." : "Can't add more pages in light mode."}>
                    <div className='bg-white p-0.5 px-12 w-[6rem] mr-2 relative right-3'>
                        <button
                            className={`flex w-[6rem] font-semibold -ml-6 bg-white ${isTrialPaidDocExtraction ? ' text-[#003654]' : 'bg-gray-300 text-gray-500 cursor-not-allowed'}`}
                            disabled={!isTrialPaidDocExtraction}
                            onClick={handleTopUpPlans}
                        >
                            <svg width="32" height="40" viewBox="0 0 44 23" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d={switchPath} fill={isTrialPaidDocExtraction ? '#003654' : '#bcbdbd'} />
                            </svg>
                            <div className={'text-start font-bold xl:text-sm 2xl:text-sm 2xl:leading-[0.9rem] my-3'}>
                                Buy Pages
                            </div>
                        </button>
                    </div>
                </Tooltip>
            </div>
        </div>
    );
};

TrialUserBox.propTypes = {
    initialPageLimitLeft: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    initialTotalPageLimitLeft: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    initialFreePageLimitUsage: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
    initialTotalFreePageLimit: PropTypes.oneOfType([
        PropTypes.string,
        PropTypes.number
    ]),
};

export default TrialUserBox;
