from typing import Optional
from pydantic import BaseModel, validator
from src.utilities.helperFunc import ValidationHelper

class check_user(BaseModel):
    username : str

class LoginRequest(BaseModel):
    email: str
    password: str

class UserRegistrationInput(BaseModel):
    name: str
    email: str
    phoneNumber: Optional[str]
    password: str
    name: str
    roleName: Optional[str] = None
    created_at: Optional[str]  = None
    updated_at: Optional[str] = None

    _email = validator("email", allow_reuse=True)(ValidationHelper.is_email)
    _password = validator("password", allow_reuse=True)(ValidationHelper.is_valid_password)
    # _phoneNumber = validator("phoneNumber",allow_reuse=True)(ValidationHelper.is_mobile)

class UpdateUserDetails(BaseModel):
    name:Optional[str] = None
    password:Optional[str] = None
    rolename:Optional[str] = None
    email:Optional[str] = None
    
    _email = validator("email", allow_reuse=True)(ValidationHelper.is_email)
    _password = validator("password", allow_reuse=True)(ValidationHelper.is_valid_password)
    
    
class ForgotPasswordModel(BaseModel):
    email: str
    _email = validator("email", allow_reuse=True)(ValidationHelper.is_email)

class ResetPassword(BaseModel):
    password: str
    _password = validator("password", allow_reuse=True)(ValidationHelper.is_valid_password)

class UpdatePassword(BaseModel):
    CurrentPassword : str
    UpdatedPassword : str

class EmailValidation(BaseModel):
    isEmailExist: bool = None
    isValidEmail: bool = None

class CheckoutSessionRequest(BaseModel):
    userId:int
    price_id: str
    # mode would select payment when one time plan type else mode value would be subscription base
    mode: Optional[str] = 'subscription'
    quantity: Optional[int] = 1