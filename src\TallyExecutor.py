import requests
import json
import re
import xml.etree.ElementTree as ET
import os
from pathlib import Path

class CTallyExecutor:

    @staticmethod
    def sanitize_xml(raw_xml: str) -> str:
        """
        Sanitizes XML content by:
        - Escaping unescaped ampersands.
        - Removing invalid character references like &#4;.
        - Removing ASCII control characters not allowed in XML 1.0.
        """
        # Fix unescaped &
        raw_xml = re.sub(r'&(?!(amp|lt|gt|quot|apos|#\d+);)', '&amp;', raw_xml)

        # Remove invalid numeric character references (e.g., &#0;, &#4;)
        raw_xml = re.sub(r'&#(0?[0-8]|0?11|0?12|0?14|0?15|0?16|0?17|0?18|0?19|0?20|0?21|0?22|0?23|0?24|0?25|0?26|0?27|0?28|0?29|0?30|0?31);', '', raw_xml)

        # Remove raw control characters
        raw_xml = ''.join(c for c in raw_xml if c == '\n' or c == '\r' or c == '\t' or ord(c) >= 32)

        return raw_xml
    
    @staticmethod
    def MSCreateTallyLedgerJsonConfig(strOutputJsonPath="TallyLedgerConfig.json", strResponseXMLPath="response.xml", bNewXMLResponse=False, url="http://0.0.0.0:9000/"):
        if bNewXMLResponse:
            CTallyExecutor.MSTallyConnect(Path(r"resource/TallyRequestXML/GetFilteredLedgers.xml"), strResponseXMLPath, url=url)

        if not os.path.exists(strResponseXMLPath):
            raise FileNotFoundError(f"Input XML file not found: {strResponseXMLPath}")

        try:
            # Read and sanitize XML
            with open(strResponseXMLPath, "r", encoding="utf-8") as file:
                raw_xml = file.read()
            sanitized_xml = CTallyExecutor.sanitize_xml(raw_xml)

            # Parse XML
            root = ET.fromstring(sanitized_xml)
            result = {}

            # Iterate through all LEDGER entries
            for ledger in root.findall(".//LEDGER"):
                party_name_raw = ledger.get("NAME", "").strip()
                party_key = party_name_raw.lower()

                # Build ledger config
                ledger_data = {
                    "party_name": party_name_raw.upper(),
                    "address_list": [],
                    "gst_registration_type": "",
                    "gst_in": "",
                    "state_name": "",
                    "country_name": "",
                    "pin_code": ""
                }

                # Addresses
                for addr in ledger.findall(".//ADDRESS"):
                    if addr.text and addr.text.strip():
                        ledger_data["address_list"].append(addr.text.strip())

                # GST Registration Type
                elem = ledger.find("GSTREGISTRATIONTYPE")
                if elem is not None and elem.text:
                    ledger_data["gst_registration_type"] = elem.text.strip()

                # GSTIN
                elem = ledger.find("PARTYGSTIN")
                if elem is not None and elem.text:
                    ledger_data["gst_in"] = elem.text.strip()

                # State
                elem = ledger.find("PRIORSTATENAME")
                if elem is not None and elem.text:
                    ledger_data["state_name"] = elem.text.strip()

                # Country
                elem = ledger.find("COUNTRYOFRESIDENCE")
                if elem is not None and elem.text:
                    ledger_data["country_name"] = elem.text.strip()

                # Pin Code
                elem = ledger.find("PINCODE")
                if elem is not None and elem.text:
                    ledger_data["pin_code"] = elem.text.strip()

                result[party_key] = ledger_data

            # Write to JSON
            with open(strOutputJsonPath, "w", encoding="utf-8") as out_file:
                json.dump(result, out_file, indent=4)

            print(f"✅ JSON file created at: {strOutputJsonPath}")

        except ET.ParseError as e:
            print(f"❌ XML parsing failed: {str(e)}")
        except Exception as e:
            print(f"❌ Error: {str(e)}")

    @staticmethod
    def MSTallyConnect(strRequestXMLPath, strResponseXMLPath="response.xml", url="http://0.0.0.0:9000/"):
        """
        Reads an XML file and sends it to the specified Tally server URL.
        Saves the server response to an XML file.

        Args:
        strRequestXMLPath (str): The path to the XML file to send.
        url (str): The Tally server URL.
        strResponseXMLPath (str): The path where the response XML will be saved.

        Returns:
        str: Confirmation message or error details.
        """
        try:
            # Read XML content from file
            with open(strRequestXMLPath, "r", encoding="utf-8") as file:
                xml_data = file.read()

            # Define headers
            headers = {
                "Content-Type": "text/xml",
                "Cache-Control": "no-cache"
            }

            # Send POST request
            response = requests.post(url, headers=headers, data=xml_data)

            # Save the response to an XML file
            with open(strResponseXMLPath, "w", encoding="utf-8") as file:
                file.write(response.text)

            return f"Response saved to {strResponseXMLPath}"

        except FileNotFoundError:
            return "Error: The specified file was not found."
        except requests.exceptions.RequestException as e:
            return f"Error: Unable to send data to the server. {str(e)}"
        except IOError as e:
            return f"Error: Unable to save the response file. {str(e)}"
        except Exception as GenError:
            return f"Failed to Export the data,  error: {str(GenError)}"

if __name__ == "__main__":
    # Example usage
    # strRequestXMLPath = r"resource\TallyRequestXML\GetFilteredLedgers.xml"
    # strResponseXMLPath = r"Data\Customer\Abhinav InfraBuild\GetFilteredLedgers.xml"
    # url = "http://192.168.1.19:10102/"

    # result = CTallyExecutor.MSTallyConnect(strRequestXMLPath, strResponseXMLPath, url)
    # print(result)

    # XML to JSON
    CTallyExecutor.MSCreateTallyLedgerJsonConfig(strOutputJsonPath = r"Data\Customer\Abhinav InfraBuild\TallyLedgerConfig.json", strResponseXMLPath = r"Data\Customer\Abhinav InfraBuild\GetFilteredLedgers.xml")
    pass