from src.Controllers.user_Logs_Controller import <PERSON><PERSON>serLogController
from src.middleware.checkAuth import  UserAnd<PERSON><PERSON>Check
from typing import Optional
from fastapi import APIRouter, Depends,  Query
from src.Schemas.Log_Schema import GetAllLogFilterQuery

# Declaring router
LogsRouter = APIRouter(tags=['Logs'],  prefix="/api/Logs")

@LogsRouter.get("/all")
async def get_all_logs_route(   page: Optional[int] = Query(1, description="Page number starting from 1"),
                                per_page: Optional[int] = Query(None, description="Number of logs per page"),
                                dictRequestData: dict = Depends(UserAndRoleCheck),
                                filterQuery: GetAllLogFilterQuery = Depends()):
    """
    Endpoint to retrieve all logs with optional filtering and pagination.
    Accepts query parameters 'page', 'per_page', and other filters like 'log type', 'section', etc.
    """
    try:
        return await CUserLogController.MSGetAllLogs(page=page, per_page=per_page, user_id=dictRequestData.get("UserId"), filterQuery=filterQuery, hasAdminRights=dictRequestData.get("RoleData", {}).get("hasAdminRights"))
    except Exception as e:
        print("Error ", e)

