from fastapi import APIRouter, HTTPException, Depends,  status
from pydantic import BaseModel
import traceback
import datetime
from dotenv import load_dotenv
import os
from typing import List, Optional
from openai import OpenAI
from src.Schemas.schemas import GPTBatchAPIRecordCreate, GPTBatchAPIRecordUpdate
from src.Controllers.GPTBatchAPIController import CGPTBatchAPIDB
from src.Schemas.GPT_Schema import ProcessDocIDsResponse, ProcessDocIDsRequest, RetrieveBatchResultsRequest, RetrieveBatchResultsResponse
from src.Controllers.BatchAPIController import CBatchAPIController
from src.utilities.helperFunc import DateHelper
from fastapi import Depends
from src.middleware.checkAuth import admin_required, user_required
from src.Schemas.schemas import GPTBatchAPIStatusEnum
# Initialize FastAPI router
batch_api = APIRouter(tags=['Batch API'], prefix="/api")
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)
API_KEY = os.getenv('AVOPENAI_API_KEY')
client = OpenAI(api_key=API_KEY)

@batch_api.post('/batch/records')
async def create_batch_record(record: GPTBatchAPIRecordCreate):
    """
    Insert a new record into GPTBatchAPIRecords.
    """
    return await CGPTBatchAPIDB.MSInsertBatchRecord(
        batch_object=record.Batch_Object,
        batch_polling_object=record.Batch_Polling_Object,
        task_details=record.TaskDetails,
        status=record.Status
    )

@batch_api.patch('/batch/records/{record_id}')
async def update_batch_record(record_id: int, record_update: GPTBatchAPIRecordUpdate):
    """
    Update a specific record by ID.
    """
    return await CGPTBatchAPIDB.MSUpdateBatchRecord(record_id=record_id, update_fields=record_update.dict(exclude_unset=True))


@batch_api.get('/batch/records/{record_id}')
async def get_batch_records(record_id: Optional[int] = None):
    """
    Fetch one or all batch records.
    """
    return await CGPTBatchAPIDB.MSGetBatchRecords(record_id=record_id)

@batch_api.get('/batch/records')
async def get_batch_records():
    """
    Fetch one or all batch records.
    """
    return await CGPTBatchAPIDB.MSGetBatchRecords(record_id=None)


@batch_api.post(
    '/batch/process-doc-ids',
    response_model=ProcessDocIDsResponse,
    status_code=status.HTTP_201_CREATED,
    summary="Process Document IDs and create a batch job."
)
async def process_doc_ids_route(request: ProcessDocIDsRequest,
    user_id: int = Depends(user_required)):
    """
    Endpoint to process a list of Document IDs, create batch API tasks,
    upload them, and initiate a batch job with the GPT API.
    """
    try:
        
        # Call the static method to process Document IDs
        result = await CBatchAPIController.process_doc_ids(
            doc_ids=request.lsDocIds,
            user_id=user_id,
            is_trial_paid_doc_extraction=request.isTrialPaidDocExtraction,
            debug=request.bDebug
        )
        
        if result:
            
            return ProcessDocIDsResponse(
                success=True,
                message="Batch job created successfully.",
                batch_job_id=result.get("batch_job_response").get("id"),
                details = result
            )
        else:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Failed to process Document IDs."
            )
        
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions to be handled by FastAPI
        raise http_exc
    except Exception as e:
        # Log the traceback for debugging purposes
        print("ERROR - ", traceback.print_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )
    

@batch_api.post(
    '/batch/retrieve-results',
    response_model=RetrieveBatchResultsResponse,
    status_code=status.HTTP_200_OK,
    summary="Retrieve and store batch job results."
)
async def retrieve_batch_results_route(request: RetrieveBatchResultsRequest, userid: int = Depends(user_required)):
    """
    Endpoint to retrieve and store the results of a batch job.
    """
    try:
        # Fetch batch job object to check the status and request counts
        batch_job = client.batches.retrieve(request.batch_job_id)
        result_file_id = batch_job.output_file_id
        if not result_file_id:
            if batch_job.error_file_id:
                result_file_id = batch_job.error_file_id
                print(f"Error file found for batch job ID: {request.batch_job_id}. Marking status as 'error'.")
                # Update batch job status to error
                await CGPTBatchAPIDB.MSUpdateBatchRecord(
                    record_id=request.batch_job_record_id, 
                    update_fields={"Status": GPTBatchAPIStatusEnum.error}
                )
            else:
                print(f"No result file or error file found for batch job ID: {request.batch_job_id}.")
                result_file_id = None
        if result_file_id:
            results = await CBatchAPIController.retrieve_and_store_batch_results(
                userid=userid,
                result_file_id = result_file_id,
                batch_job_record_id=request.batch_job_id,
                base_directory=request.base_directory,
                bProcessTally=request.bProcessTally,
                filename_format=request.filename_format
            )
            return RetrieveBatchResultsResponse(
                success=True,
                message="Batch job completed successfully.",
                results=results.get("api_responses"),
                file_path=results.get("batch_api_reponse_jsonl")
            )
        else:
            return RetrieveBatchResultsResponse(
                success=False,
                message=f"Batch job status - {batch_job.status}.",
                results=[],
                file_path=""
            )
    except HTTPException as http_exc:
        # Re-raise HTTP exceptions to be handled by FastAPI
        raise http_exc
    except Exception as e:
        # Log the traceback for debugging purposes
        print("Traceback" , traceback.print_exc())
        raise HTTPException(
            status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail=f"An unexpected error occurred: {str(e)}"
        )