from config.db_config import AsyncSessionLocal
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from datetime import datetime
import pytz
from src.Models.models import MultipleVendorRecord

class CMultipleVendorRecordHelper:
    @staticmethod
    async def create_multiple_vendor_record(user_id: int, creq_id: str, hash_code: str, 
                                            obj_aws_response=None, gpt_user_content=None, 
                                            obj_gpt_response=None, dict_gpt_response=None, log_message=None, 
                                            aws_status_code=404, gpt_status_code=404):
        """
        Inserts a new row into the multiple_vendor_record table.
        """
        async with AsyncSessionLocal() as db:
            try:
                new_record = MultipleVendorRecord(
                    Userid=user_id,
                    CREQ_ID=creq_id,
                    HashCode=hash_code,
                    ObjAWSResponse=obj_aws_response,
                    GPTUserContent=gpt_user_content,
                    ObjGPTResponse=obj_gpt_response,
                    dictGPTResponse=dict_gpt_response,
                    LogMessage=log_message,
                    AWSStatusCode=aws_status_code,
                    GPTStatusCode=gpt_status_code,
                    CreatedDateTime=datetime.now(pytz.timezone('Asia/Kolkata')),
                    UpdatedDateTime=datetime.now(pytz.timezone('Asia/Kolkata'))
                )
                db.add(new_record)
                await db.commit()
                await db.refresh(new_record)
                return new_record
            except SQLAlchemyError as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=f"Error creating record: {str(e)}")

    @staticmethod
    async def update_multiple_vendor_record(mvr_id: int, **kwargs):
        """
        Updates an existing record in the multiple_vendor_record table based on MVR_ID.
        """
        async with AsyncSessionLocal() as db:
            try:
                result = await db.execute(select(MultipleVendorRecord).filter(MultipleVendorRecord.MVR_ID == mvr_id))
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found")

                # Update fields dynamically
                for key, value in kwargs.items():
                    if hasattr(record, key) and value is not None:
                        setattr(record, key, value)

                record.UpdatedDateTime = datetime.now(pytz.timezone('Asia/Kolkata'))  # Update timestamp

                await db.commit()
                await db.refresh(record)
                return record
            except SQLAlchemyError as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=f"Error updating record: {str(e)}")

    @staticmethod
    async def get_existing_record(user_id: int, hash_code: str):
        """
        Retrieves an existing record based on Userid and HashCode.
        """
        async with AsyncSessionLocal() as db:
            try:
                result = await db.execute(
                    select(MultipleVendorRecord).filter(
                        MultipleVendorRecord.Userid == user_id,
                        MultipleVendorRecord.HashCode == hash_code
                    )
                )
                record = result.scalars().first()

                if record:
                    return record
                else:
                    return None
            except SQLAlchemyError as e:
                raise HTTPException(status_code=500, detail=f"Error retrieving record: {str(e)}")
