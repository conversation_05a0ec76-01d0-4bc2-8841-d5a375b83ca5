import sys
sys.path.append(".")

import imaplib
import email
import os
import asyncio
import re
from abc import ABC, abstractmethod
from datetime import datetime, timedelta
from config.db_config import AsyncSessionLocal
from fastapi import HTTPException
from sqlalchemy.future import select
from src.Models.models import EmailProcessingRecord, EmailProcessingStatusEnum, Logs_EmailProcessing, TallyBackupRecord, TallyBackupStatusEnum
import json
from dotenv import load_dotenv
import zipfile
from openpyxl import Workbook
from openpyxl import load_workbook
import pytz
from sqlalchemy.exc import SQLAlchemyError
from src.Controllers.Vendor_Controller import CVendorController
from src.Controllers.GPTResponse_controller import DocumentUploader, CGPTResponseData
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.ParagTradersControllers import CHansgrohe_XML, CNexion_XML, CSimpolo_XML, <PERSON><PERSON><PERSON>ler_XML, <PERSON>oto_XML, CGeberit_XML, CAquant_XML, CQuotation, CIcon_XML
from src.Controllers.TallyIndianInvoiceController import CTallyIndianInvoiceController
from src.Controllers.BankStatementsController import CProcessBankStatement # CAiren, CParagTradersBankStatement,CGwalia
import shutil
import csv
from pathlib import Path
import magic
from src.utilities._TallyEmailProcessor import BaseEmailProcessor
import traceback
from email import header
from sqlalchemy import and_
from email.utils import parsedate_tz, mktime_tz
import httpx
import asyncio
from src.Controllers.GPTResponse_controller import CGPTResponseData, DocumentProcessorUsingHTTPX
from src.Schemas.Doc_Schema import AdditionalDocDetails
import TallyEmailSender as TallyReportSender
from src.utilities.helperFunc import CExcelHelper
from src.utilities.TallyHelper import CPriceListREPORT
load_dotenv()

class AsyncDocument:
    def __init__(self, filename, content_type, data):
        self.filename = filename
        self.content_type = content_type
        self._data = data

    async def read(self):
        return self._data

    @property
    def size(self):
        return len(self._data)
    
    def get_extension(self):
        return os.path.splitext(self.filename)[1].lower()


class EmailAttachmentProcessor(BaseEmailProcessor):
    def __init__(self, email_account, provider, password, imap_server, imap_port, clients, days_limit=7):
        super().__init__(email_account, provider, password, imap_server, imap_port)
        self.clients = clients
        self.days_limit = days_limit
        
    @staticmethod
    async def MSGetDailyStatistics(lsDictProcessedDocs, iTimePerPage):
        iTotalTimeSaved = 0
        iTotalPageProcessed = 0
        
        # Calculate TotalTime saved and Total PagesProcesed
        for dictAttachmentInfo in lsDictProcessedDocs:
            if dictAttachmentInfo["Status"] == "Added to tally":
                iTotalPageProcessed += dictAttachmentInfo["PageCount"]
                iTotalTimeSaved += dictAttachmentInfo["PageCount"] * iTimePerPage
        
        return {
            "TotalTimeSaved":iTotalTimeSaved,
            "TotalPageProcessed":iTotalPageProcessed
        }
        
    @staticmethod
    def clean_filename(filename):
        try:
            # Replace line breaks and normalize spaces
            filename = filename.replace('\r', '').replace('\n', ' ').strip()
            # Remove invalid characters for filesystems
            filename = "".join(c for c in filename if c not in r'\/:*?"<>|')
        except Exception as e:
            print(f"Failed to clean the filename, {filename}")
        return filename

    async def initialize_report(self, userid):
        """Initialize the report CSV file with headers if it doesn't exist."""
        if not os.path.isfile(self.report_file):

            await self.log_to_db(None, "INFO", f"Creating new report file: {self.report_file}")
            with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=[
                    'Received Date',
                    'Vendor Name',
                    'Invoice No',
                    'Invoice Date',
                    'Total Amount',
                    'Tally Punch-in Status',
                    'Accuvelocity Comments'
                ])
                writer.writeheader()
        else:
            await self.log_to_db(None, "ERROR", f"Report file already exists: {self.report_file}")


    async def process_filename(self, filename):
        try:
            # Decode filename if encoded
            decoded_filename, encoding = header.decode_header(filename)[0]
            if isinstance(decoded_filename, bytes):
                filename = decoded_filename.decode(encoding or 'utf-8')
            elif isinstance(decoded_filename, str) and decoded_filename.startswith('=?UTF-8?b?'):
                filename = header.decode_header(decoded_filename)[0][0].decode('utf-8')
            filename = EmailAttachmentProcessor.clean_filename(filename)

        except Exception as e:
            # Log the error if decoding fails
            await self.log_to_db(None, 'ERROR', f"Failed to decode the file name, current file name: {filename}")
        return filename


    # Function to check if the file exists in lsAllAttachmentInfo and its status
    @staticmethod
    async def is_filename_processed(filename, processed_attachments):
        for att in processed_attachments:
            if att['FileName'].lower() == filename.lower():  # Case-insensitive match
                return att['Status'] == "Added to tally"  # Returns True if status is "Added To Tally"
        return False  # Filename not found in lsAllAttachmentInfo


    async def process_email(self, email_id, client_config, uid, sender, bRetry):
        """
        Asynchronously downloads and uploads the email attachments for a specific client configuration.
        """
        bTestMode = True
        iMinutesPerPage = 2.5
        # Debugging Config - DO NOT REMOVE 
        # strUploadURL = "http://************:8024/api"
        # strExtarctionURL = "http://************:9002/doc_exc_api"
        strUploadURL = "http://localhost:8024/api"
        strExtarctionURL = "http://localhost:9002/doc_exc_api"
        bIsBankStatementApp = (
                                ("airen" in client_config['customerName'].lower() or "bankstatement" in client_config['customerName'].lower())
                                or 
                                ("paragtraders" in client_config['customerName'].lower() and "bankstatement" in client_config['customerName'].lower())
                                or
                                ("gwalia" in client_config['customerName'].lower() or "bankstatement" in client_config['customerName'].lower())
                            )

        async with AsyncSessionLocal() as db:
            
            # If current iteration is not retry then send acknowledgement mail to the sender
            if not bRetry and ('<EMAIL>' not in sender.lower()) and not bIsBankStatementApp:
                try:
                    strNotificationMailSubject="Mail Received and Invoice Processing in Progress"
                    if bTestMode:
                        strNotificationMailSubject = "(Test Mode) Mail Received and Invoice Processing in Progress"
                        
                    TallyReportSender.SendTallyAckNotificationEmail(    strSubject=strNotificationMailSubject,
                                                                        strReceiverName=client_config['customerName'], 
                                                                        lsMailTo=client_config['lsEmailReceivers'], 
                                                                        lsCC=client_config['lsEmailCC']
                                                                    )
                    
                except Exception as e:
                    await self.log_to_db(None, "ERROR", f"!!  Error sending Acknowledgement email: {str(e)}   !!")
                    
            today_date_str = None
            # Initialize Reporting 
            try:
                # Save the attachment to the client-specific directory
                client_data_dir = client_config['dataDirectory']
                # Get today's date in the desired format
                today = datetime.now().strftime("%Y_%m_%d")

                # Create the download directory with today's date
                today_download_dir = os.path.join(client_data_dir, today)
                
                self.report_dir = os.path.join(today_download_dir, "Daily_Automation_Reports")

                os.makedirs(self.report_dir, exist_ok=True)

                # Initialize report file path
                today_date_str = datetime.now().strftime('%Y_%m_%d')
                self.report_file = os.path.join(self.report_dir, f"Report_{today_date_str}_email_id_{email_id}.csv")
                self.report_pricing_file = os.path.join(self.report_dir, f"Pricing_Report_{today_date_str}_email_id_{email_id}.xlsx")
                if not bIsBankStatementApp:
                    await self.initialize_report(userid=client_config['userId'])
                
            except Exception as e:
                await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to create report and user data directory at location, '{today_date_str}'")

            try:
                # Ensure the connection is active before fetching the email
                await self.ensure_connection()

                # Fetch the email
                status, data = self.mail.fetch(email_id, '(RFC822)')
                current_time = datetime.now()
                msg = email.message_from_bytes(data[0][1])
                subject = msg.get('Subject', '')
                
                # Extract the actual received date from the email headers
                raw_received_date = msg['Date']
                if raw_received_date:
                    received_date = email.utils.parsedate_to_datetime(raw_received_date)
                else:
                    received_date = datetime.now()  # Fallback to current date if not found
                
                print(f"Sender: {sender}, Received On: {received_date}")

                lsAllAttachmentInfo = []  # List containing all the attachments that found in current email
                
                try:
                    async with AsyncSessionLocal() as db:
                        result = await db.execute(
                                                    select(EmailProcessingRecord).where(
                                                            and_(
                                                                EmailProcessingRecord.email_id == email_id,
                                                                EmailProcessingRecord.sender == sender
                                                            )
                                                        )
                                                    )
                        record = result.scalars().first() 
                        lsAllAttachmentInfo = record.attachments or []
                except Exception as e:
                    lsAllAttachmentInfo = []

                mime = magic.Magic(mime=True)
                
                # Collect all valid attachments
                upload_tasks = []
                valid_attachments = []
                objAdditionalDocDetails = AdditionalDocDetails(strICDPegasusExcelFile=None)
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = await self.process_filename(part.get_filename())
                        
                        # Skip already processed files
                        matched_attachment = next(
                            (att for att in lsAllAttachmentInfo if att['FileName'].lower() == filename.lower()),
                            None
                        )

                        if matched_attachment and await self.is_filename_processed(filename, lsAllAttachmentInfo):
                            continue

                        # Validate attachment type
                        if any(filename.lower().endswith(ext) for ext in client_config['attachmentsTypes']):
                            file_data = part.get_payload(decode=True)
                            if file_data:
                                try:
                                    file_path = os.path.join(today_download_dir, filename)

                                    with open(file_path, 'wb') as f:
                                        f.write(file_data)
                                    
                                    # Special handling for "av_pegasus_additionaldetails.xlsx"
                                    if "av_pegasus_additionaldetails".lower() in (filename.lower()).rstrip(): 
                                        # Modify the filename to remove extension and append email_id, date, hr, min
                                        base_filename = os.path.splitext(filename)[0]   #TODO: Please verify this dhruvin email id
                                        modified_filename = f"{base_filename}_{current_time.strftime('%Y%m%d_%H%M')}"
                                        file_path = os.path.join("Data", "Customer", "ICD", "1_Pegasus", f"{modified_filename}.xlsx")
                                        with open(file_path, 'wb') as f: 
                                            f.write(file_data)
                                        objAdditionalDocDetails.strICDPegasusExcelFile = file_path
                                        continue
                                except Exception as e:
                                    await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to download the file named '{filename}', at location '{file_path}', error: {e}")

                                dictCurrentFileInfo = matched_attachment if matched_attachment else {
                                    "Doc_Id": None,
                                    "FileName": filename,
                                    "FilePath":file_path,
                                    "PageCount":0,
                                    "VendorName":"",
                                    "Status": "Attachment Found",
                                    "ErrorMessage": ""
                                }

                                # Only add document info if not already there
                                if not matched_attachment:
                                    lsAllAttachmentInfo.append(dictCurrentFileInfo)

                                if dictCurrentFileInfo["Status"] == "Attachment Found" or dictCurrentFileInfo["Status"] == "Downloaded":
                                    actual_mime_type = mime.from_buffer(file_data)
                                    if actual_mime_type:
                                        content_type = actual_mime_type
                                    else:
                                        content_type = part.get_content_type()
                                        
                                    
                                    await self.log_to_db(client_config['userId'], 'INFO', f"** Adding Upload Task: FileName:'{filename}' from '{sender}' **")
                                    upload_tasks.append(("documents", (filename, file_data, content_type)))
                else:
                    pass
                
                if bIsBankStatementApp:
                    # Call hasrhil bhai's method
                    # Example usage
                    python_executable_path = Path(r"envProdBankStatPred/Scripts/python.exe")  # Replace with your Python path
                    script_to_run = Path(r"scripts/app.py")  # Path relative to the working directory
                    company_name = client_config['company_name']
                    bank_name = client_config['bank_name']
                    print(f"company_name - {company_name}, bank_name - {bank_name}")
                    # input_file = r"data/airen_construction/boi_1/input_files/AIREN CONSTRUCTION PVT. LTD. 01-04-2024 TO 30-06-2024.xls"
                    working_dir = r"BankStatementSrc"  # Set your required working directory
                    lsAttachmentPath = []
                    strFileStatusForMail = ""
                    
                    for dictReceivedFileInfo in lsAllAttachmentInfo:
                        strFilePath = dictReceivedFileInfo["FilePath"]
                        # NOTE: Make Process Bank Statement General for All Customer Until & Unless any New Parameters are not going to add for specific to customer
                        # if "paragtraders" in client_config['customerName'].lower():
                        #     strResult = CParagTradersBankStatement.MSRunCMD(python_path=os.path.abspath(python_executable_path),
                        #                                         script_path=script_to_run,
                        #                                         company=company_name,
                        #                                         bank=bank_name,
                        #                                         file_path=strFilePath,
                        #                                         working_directory=os.path.abspath(working_dir),
                        #                                         debug=True
                        #                                         )

                        # elif "airen" in client_config['customerName'].lower():
                        # # Run the command
                        #     strResult = CAiren.MSRunCMD(
                        #         python_path=os.path.abspath(python_executable_path),
                        #         script_path=script_to_run,
                        #         company=company_name,
                        #         bank=bank_name,
                        #         file_path=strFilePath,
                        #         working_directory=os.path.abspath(working_dir),
                        #         debug=True
                        #     )
                        # elif "gwalia" in client_config['customerName'].lower():
                        # Run the command
                        strResult = CProcessBankStatement.MSRunCMD(
                            python_path=os.path.abspath(python_executable_path),
                            script_path=script_to_run,
                            company=company_name,
                            bank=bank_name,
                            file_path=strFilePath,
                            working_directory=os.path.abspath(working_dir),
                            debug=True
                        )
                        if isinstance(strResult, str):
                            strFileStatusForMail += f"{strResult}\n"
                            if os.path.exists(strResult):
                                dest_file = os.path.join(today_download_dir, os.path.basename(strResult))
                                # Copy the file to the destination directory
                                shutil.copy(strResult, dest_file)
                                print(f"File copied to {dest_file}")
                                lsAttachmentPath.append(dest_file)
                                # strFileStatusForMail += ""
                        else:
                            strFileStatusForMail += f"File '{strFilePath}' not processed.\n"

                        
                    # Store record in db so that it do not go for re processing
                    try:
                        async with AsyncSessionLocal() as db:
                            # Step 1: Check if a record with the same email_id and sender already exists
                            existing_record = await db.execute(
                                select(EmailProcessingRecord).where(
                                    and_(
                                        EmailProcessingRecord.email_id == email_id,
                                        EmailProcessingRecord.sender == sender
                                    )
                                )
                            )
                            record = existing_record.scalars().first()
                            
                            if record:
                                # Update the existing record
                                record.attachments = lsAllAttachmentInfo
                                record.status = EmailProcessingStatusEnum.PROCESSED
                               
                            else:
                                # Create a new record
                                record = EmailProcessingRecord(
                                    email_id=email_id,
                                    user_id=client_config['userId'],
                                    sender=sender,
                                    attachments=lsAllAttachmentInfo,
                                    received_date=received_date,
                                    status=EmailProcessingStatusEnum.PROCESSED,
                                    retry_count=0
                                )
                                db.add(record)
                            
                            await db.commit()
                    except Exception as e:
                        await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to add the email processing status into the database due to error: {e}")
                    
                    strHtmlContent = """
                                        <html>
                                            <body>
                                                <p>Dear User,</p>
                                                <p>Your document has been processed successfully! 🎉</p>
                                                <p>Please find the attachments below:</p>
                                                <p>If you have any questions or need further assistance, feel free to reach out to us at 
                                                <strong><EMAIL></strong> or call us at <strong>+91 98989 42935</strong>.</p>
                                                <p>Below are the steps to import the data into Tally:</p>
                                                <ol>
                                                    <li>Download the <strong>.xlsx</strong> file to your system.</li>
                                                    <li>Open Tally and navigate to the <strong>Import</strong> option in the top menu.</li>
                                                    <li>Select the option <strong>Transaction</strong> from the Import menu.</li>
                                                    <li>Configure the settings in the import popup:
                                                        <ul>
                                                            <li>Set <strong>File Format</strong> to <strong>Excel Spreadsheet</strong>.</li>
                                                            <li>Choose the <strong>File Path</strong> of the downloaded file.</li>
                                                            <li>Select <strong>Default</strong> as the Mapping Template.</li>
                                                            <li>Enable <strong>Preview Import Summary</strong> and <strong>Backup Company Data Before Import</strong>.</li>
                                                        </ul>
                                                    </li>
                                                    <li>Press <strong>Enter</strong> or <strong>Ctrl + A</strong> to view the Import Summary.</li>
                                                    <li>Verify all details in the summary, then click <strong>Import</strong>.</li>
                                                    <li>When prompted, click <strong>Yes</strong> to create a backup before importing data.</li>
                                                    <li>If any exceptions occur, click the <strong>Exceptions</strong> button to view and manually correct them.</li>
                                                </ol>
                                                <p>Thank you for using AccuVelocity!</p>
                                                <p>Best regards,<br>The AccuVelocity Team</p>
                                            </body>
                                        </html>
                                        """


                    if not lsAttachmentPath:
                        # If no attachments were found, send a mail to the user with the email content
                        strHtmlContent = """
                                            <html>
                                                <body>
                                                    <p>Dear User,</p>
                                                    <p>We regret to inform you that your document could not be processed successfully.</p>
                                                    <p>For assistance, please contact our support team at <strong><EMAIL></strong> or call us at 
                                                    <strong>+91 98989 42935</strong>.</p>
                                                    <p>We apologize for the inconvenience and appreciate your patience.</p>
                                                    <p>Best regards,<br>The AccuVelocity Team</p>
                                                </body>
                                            </html>
                                        """
                        
                    # Send Email 
                    # Send the email
                    TallyReportSender.EmailSender.send_email(
                        from_address=os.getenv('MAIL_FROM'),
                        password=os.getenv('MAIL_PASSWORD'),
                        to_addresses=client_config['lsEmailReceivers'],
                        cc_addresses=client_config['lsEmailCC'],
                        subject="Accuvelocity Document Processing",
                        html_content=strHtmlContent,
                        smtp_server=os.getenv('MAIL_SERVER'),
                        smtp_port=int(os.getenv('SMTP_PORT')),
                        lsAttachmentPath=lsAttachmentPath
                    )
                    

                else:

                    # 1 Perform Upload
                    try:
                        if upload_tasks:
                            async with httpx.AsyncClient(timeout=100) as client:
                                response = await client.post(f"{strUploadURL}/multi-threading-upload-documents/?user_id={client_config['userId']}&strModelName=&strFamilyName=&bAutoSelectModel=true&bUsePaidModel=true&objAdditionalDocDetails={objAdditionalDocDetails.strICDPegasusExcelFile}", files=upload_tasks)

                            # Handle the response
                            if response.status_code == 200:
                                for dictCurResult in response.json():
                                    strFileName = dictCurResult.get('filename', '')
                                    strVendorName = dictCurResult.get('ModelName', '')
                                    iDocID = dictCurResult.get('DocId', None)
                                    matching_info = next((info for info in lsAllAttachmentInfo if info["FileName"].lower() == strFileName.lower()), None)
                                    if dictCurResult['APIStatusCode'] == 200:
                                        matching_info['Doc_Id'] = iDocID
                                        matching_info['Status'] = "Uploaded"
                                        matching_info['PageCount'] = dictCurResult.get("PageCount", 0)
                                        matching_info['VendorName'] = strVendorName
                                        await self.log_to_db(client_config['userId'], 'INFO', f"** File Named '{strFileName}' with DocId '{iDocID}' successfully Uploaded. **")

                                    else:
                                        matching_info['ErrorMessage'] = dictCurResult['DocErrorMsg']
                                        await self.log_to_db(client_config['userId'], 'ERROR', f"** Failed to upload File Named '{strFileName}'.Error:{matching_info['ErrorMessage']} **")
                            else:
                                await self.log_to_db(client_config['userId'], 'ERROR', f"!!  Failed to upload attachment  !!")
                    except Exception as e:
                        await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to upload attachment")


                    lsDocIDSForExtract = [
                                            dictFileData['Doc_Id'] 
                                            for dictFileData in lsAllAttachmentInfo 
                                            if dictFileData['Status'] == "Uploaded" and dictFileData['Doc_Id'] 
                                        ]
                    
                    # 2 Perform Extrcation
                    try:
                        if lsDocIDSForExtract:
                            await self.log_to_db(client_config['userId'], 'INFO', f"** Starting the Document Extraction Process. **")

                            # Initialize the document processor with the specified maximum concurrent tasks
                            processor = DocumentProcessorUsingHTTPX(max_concurrent_tasks=15, api_base_url=strExtarctionURL)
                            result = await processor.process_documents(lsDocIDSForExtract, client_config['userId'], True, None)
                            for dictExtractedDocInfo in result:
                                try:
                                    matching_info = next((info for info in lsAllAttachmentInfo if info["Doc_Id"] == dictExtractedDocInfo['document_id']), None)
                                    iStatusCode = dictExtractedDocInfo['APIStatusCode']

                                    if iStatusCode == 200:
                                        matching_info['Status'] = 'Extracted'
                                        await self.log_to_db(client_config['userId'], 'INFO', f"** Document with id '{dictExtractedDocInfo['document_id']}' Successfully Extracted. **")

                                    else:
                                        matching_info['ErrorMessage'] = dictExtractedDocInfo['DocErrorMsg']
                                        await self.log_to_db(client_config['userId'], 'ERROR', f"!! Failed to Extract Document with id '{dictExtractedDocInfo['document_id']}'. !!")
                                except Exception as e:
                                    await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to Perform extraction, Error:{e}, currentData:{dictExtractedDocInfo}")

                    except Exception as e:
                        await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to Perform extraction, Error:{e}")


                    # 3 Add to tally
                    for dictAttachmentInfo in lsAllAttachmentInfo:
                        report_entry = {
                                        'File Name': dictAttachmentInfo["FileName"],
                                        'Received Date': f"{received_date.day}/{received_date.month}/{received_date.year}",
                                        'Vendor Name': "-",
                                        'Invoice No':'-',
                                        'Invoice Date':'-',
                                        'Total Amount':'-',
                                        'Total Pages':"-",
                                        'Estimated Time Saved':'-',
                                        'Tally Punch-in Status':'Skipped',
                                        'Pricelist Verified': ("Yes" if client_config.get("bVerifyWithPriceList", False) else "No"),
                                        'Accuvelocity Comments':'-'
                                        }
                        
                        if dictAttachmentInfo["Status"] == "Extracted":
                            matching_info = next((info for info in lsAllAttachmentInfo if info["Doc_Id"] == dictAttachmentInfo["Doc_Id"]), None)
                            try:
                                dictDocData = await CGPTResponseData.MSGetDocumentByIdForAWSExtraction(iUserID=client_config['userId'], iDocId=dictAttachmentInfo["Doc_Id"])
                                strInvoiceNo = dictDocData["DocExtractedData"].get("InvoiceNo", 
                                                                                    dictDocData["DocExtractedData"].get("DocumentNumber", 
                                                                                    dictDocData["DocExtractedData"].get("DocRefNo", dictDocData["DocExtractedData"].get("InvoiceNumber"))))
                                
                                strInvoceDate = dictDocData["DocExtractedData"]["InvoiceDate"]
                                strInvoceDate = await CTallyIndianInvoiceController.MSConvertIntToDateFromDDMMYYYY(strInvoceDate)
                                strTotalAmount = dictDocData["DocExtractedData"].get("GrandTotal", 
                                                                                    dictDocData["DocExtractedData"].get("TotalAmount",
                                                                                    dictDocData["DocExtractedData"].get("TotalValue",
                                                                                    dictDocData["DocExtractedData"].get("TotalAmountINR", 
                                                                                    dictDocData["DocExtractedData"].get("TotalInvoiceValue", dictDocData["DocExtractedData"].get("NetAmount")) ))))

                                try:
                                    # Split the original date into month, day, and year
                                    month, day, year = strInvoceDate.split(".")

                                    # Reformat the date to day/month/year
                                    strInvoceDate = f"{day}/{month}/{year}"
                                except Exception as e:
                                    await self.log_to_db(client_config['userId'], "Error", "Failed to format invoice data as '{day}/{month}/{year}'.")


                                report_entry['Vendor Name'] = matching_info["VendorName"]
                                report_entry['Invoice No'] = strInvoiceNo
                                report_entry['Invoice Date'] = strInvoceDate
                                report_entry['Total Amount'] = strTotalAmount
                                report_entry['Total Pages'] = matching_info["PageCount"]

                                # 3.1 Call Tally API, Mapping of extracted data and Callling Tally API
                                await self.log_to_db(client_config['userId'], "Info", "Starting Tally Data Mapping and Calling Tally API.")
                                
                                if client_config['bAddToTally']:
                                    try:
                                        objParagTraders = CTallyIndianInvoiceController(user_id=client_config['userId'], doc_id=dictAttachmentInfo["Doc_Id"])
                                        result = await objParagTraders.MCallTallyAPI(bTestMode=bTestMode, bVerifyBackup = client_config['bVerifyBackup'])
                                        strAVComments = "-"
                                        strTallyStatusForReport = "Success"
                                        strTallyStatusForDB = "Added to tally"
                                        strTimeSaved = f"~{matching_info['PageCount'] * iMinutesPerPage} Minutes"
                                        if isinstance(result, dict):
                                            strAVComments = result.get("AVComments", "-")
                                            strTallyStatus = result.get("TallyStatus", "Skipped")
                                            if strAVComments and strAVComments != "-":
                                                if "duplicate entry" in strAVComments.lower():
                                                    strTallyStatusForReport = strTallyStatus
                                                    strTallyStatusForDB = "Skipped"
                                                    strTimeSaved = "-"
                                                else:
                                                    strTallyStatusForReport = strTallyStatus
                                                matching_info['ErrorMessage'] = str(strAVComments)
                                            else:
                                                strTallyStatusForReport = strTallyStatus
                                            
                                        matching_info["Status"] = strTallyStatusForDB
                                        report_entry['Accuvelocity Comments'] = strAVComments
                                        report_entry['Estimated Time Saved'] = strTimeSaved
                                        report_entry['Tally Punch-in Status'] = strTallyStatusForReport

                                        
                                        await self.log_to_db(client_config['userId'], "Info", f"**  Invoice numbered '{strInvoiceNo}' Date: '{strInvoceDate}' and Doc id '{dictAttachmentInfo['Doc_Id']}' successfully added to Tally.  **")
                                        
                                        if report_entry['Tally Punch-in Status'].lower() != "skipped":
                                            # Create Pricelist Report
                                            try:
                                                await EmailAttachmentProcessor.create_pricelist_report(iUserId=client_config['userId'], dictExtractedData=dictDocData["DocExtractedData"],
                                                                                strVendorName=matching_info["VendorName"], strReportFilePath=self.report_pricing_file)
                                            except ValueError as e:
                                                report_entry['Pricelist Verified'] = 'No'
                                        else:
                                            report_entry['Pricelist Verified'] = 'No'
                                            
                                    except ValueError as ve:
                                        
                                        if str(ve) == "Tally XML: Duplicate Entry Found in AccuVelocity.":
                                            matching_info["Status"] = "Added to tally"  # ! So that current document gets marked as success
                                            matching_info['ErrorMessage'] = str(ve)
                                            report_entry['Pricelist Verified'] = 'No'
                                            report_entry['Accuvelocity Comments'] = str(ve)
                                            
                                    except Exception as e:
                                        matching_info['ErrorMessage'] = str(e)
                                        report_entry['Pricelist Verified'] = 'No'
                                        report_entry['Accuvelocity Comments'] = str(e)
                                        await self.log_to_db(client_config['userId'], "Error", f"Failed to add to tally {str(e)}")
                            except Exception as e:
                                await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to add document with id '{dictAttachmentInfo['Doc_Id']}' to tally, Error:{str(e)}")
                                matching_info['ErrorMessage'] = str(e)


                        # Update CSV Report
                        try:
                            await self.update_report(report_entry)
                        except Exception as e:
                            await self.log_to_db(client_config['userId'], 'ERROR', f"!!  Failed to update csv report at location '{self.report_file}'. Error:{e} !!")


                    processed_file_count = len([
                                                att for att in lsAllAttachmentInfo 
                                                if any(att['FileName'].lower().endswith(ext) for ext in client_config['attachmentsTypes'])   # Remove this lines as we do not need to check for file extentions as processes attachments contains valid attachments only
                                                and(att['Status'] == "Added to tally" or att['Status'] == "Skipped")
                                            ])                
                    
                    intended_file_count = len([
                                    part for part in msg.walk() 
                                    if part.get_content_disposition() == 'attachment' 
                                    and any(
                                        header.decode_header(part.get_filename())[0][0].decode(header.decode_header(part.get_filename())[0][1] or 'utf-8').lower().endswith(ext)
                                        if isinstance(header.decode_header(part.get_filename())[0][0], bytes) 
                                        else header.decode_header(part.get_filename())[0][0].lower().endswith(ext)
                                        for ext in client_config['attachmentsTypes']
                                    )
                                    and (client_config['customerName'] != "ICD" or not part.get_filename().lower().endswith(".xlsx"))
                                ])
                    
                    bProcessed = processed_file_count == intended_file_count
                    bMaxRetryExceed = False
                    
                    try:
                        async with AsyncSessionLocal() as db:
                            # Step 1: Check if a record with the same email_id and sender already exists
                            existing_record = await db.execute(
                                select(EmailProcessingRecord).where(
                                    and_(
                                        EmailProcessingRecord.email_id == email_id,
                                        EmailProcessingRecord.sender == sender
                                    )
                                )
                            )
                            record = existing_record.scalars().first()
                            
                            if record:
                                # Update the existing record
                                record.attachments = lsAllAttachmentInfo

                                # Update status based on file count
                                if bProcessed:
                                    record.status = EmailProcessingStatusEnum.PROCESSED
                                else:
                                    record.status = EmailProcessingStatusEnum.FAILED_TO_PROCESS
                                    bMaxRetryExceed = (record.retry_count == client_config["MaxRetryCount"])
                                    record.retry_count += 1  # Increment retry count if failed
                            else:
                                # Create a new record
                                record = EmailProcessingRecord(
                                    email_id=email_id,
                                    user_id=client_config['userId'],
                                    sender=sender,
                                    attachments=lsAllAttachmentInfo,
                                    received_date=received_date,
                                    status=EmailProcessingStatusEnum.PROCESSED if bProcessed else EmailProcessingStatusEnum.FAILED_TO_PROCESS,
                                    retry_count=0 if bProcessed else 1
                                )
                                db.add(record)
                            
                            await db.commit()
                    except Exception as e:
                        await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to add the email processing status into the database due to error: {e}")

                    # Send Report in email
                    try:
                        if bProcessed or bMaxRetryExceed:
                            
                            # To make sure only send email report method when mail is not from '<EMAIL>' which could be backup email
                            if ('<EMAIL>' not in sender.lower()) and client_config['bAddToTally']:
                                
                                dictDailyStatistics = await EmailAttachmentProcessor.MSGetDailyStatistics(lsDictProcessedDocs=lsAllAttachmentInfo,
                                                                    iTimePerPage=iMinutesPerPage
                                                                )
                    
                                # Convert results to strings as required
                                strTotalPagesProcessed = dictDailyStatistics.get('TotalPageProcessed', 0)

                                # Get total time saved in minutes
                                total_time_saved_minutes = dictDailyStatistics.get('TotalTimeSaved', 0)
                                dictTallyUserConfig = {}
                                try:
                                    dictTallyUserConfig = await CTallyController.MSGetTallyUserConfig(iUserID=client_config['userId'])
                                except Exception as e:
                                    await self.log_to_db(client_config['userId'], 'ERROR', f"!! Failed to get tally data for total statistics, error: {e}")


                                iCummulativePagesProcessed = 0
                                iCummulativeTimeSavedMinutes = 0
                                # Update Total statistics in db
                                try:
                                    iCummulativePagesProcessed = dictTallyUserConfig.get("TotalPagesProcessed", None) + strTotalPagesProcessed
                                    iCummulativeTimeSavedMinutes = dictTallyUserConfig.get("TotalTimeSavedInMinutes", None) + total_time_saved_minutes
                                    await CTallyController.MSSetTallyUserConfig(iUserID=client_config['userId'],
                                                                                                        iTotalPagesProcessed = iCummulativePagesProcessed,
                                                                                                        iTotalTimeSaved = iCummulativeTimeSavedMinutes
                                                                                                    )
                                except Exception as e:
                                    await self.log_to_db(client_config['userId'], 'ERROR', f"!! Failed to update data for total statistics, error: {e}")


                                strFormattedTimeSavedToday = await EmailAttachmentProcessor.get_formatted_time(total_time_saved_minutes)
                                strFormattedTimeSavedTillNow = await EmailAttachmentProcessor.get_formatted_time(iCummulativeTimeSavedMinutes)
                                
                                strSubject = "Tally Invoice Posting Report"
                                if bTestMode:
                                    strSubject = "(Test Mode) Tally Invoice Posting Report"
                                TallyReportSender.SendTallyNotificationEmail(csvReportPath=self.report_file, 
                                                                strReceiverName=client_config['customerName'],
                                                                strSubject=strSubject, 
                                                                strMailFrom=os.getenv('MAIL_FROM'), 
                                                                lsMailTo=client_config['lsEmailReceivers'], 
                                                                strServer=os.getenv('MAIL_SERVER'), 
                                                                intPort=int(os.getenv('SMTP_PORT')), 
                                                                strPassword=os.getenv('MAIL_PASSWORD'), 
                                                                htmlTemplatePath=Path(r"resource/TallyEmailTemplate.html"), 
                                                                lsCC=client_config['lsEmailCC'],
                                                                strTotalPagesProcessedToday=strTotalPagesProcessed, 
                                                                strTotalTimeSavedToday=strFormattedTimeSavedToday,
                                                                strTotalPagesProcessedTillNow=iCummulativePagesProcessed,
                                                                strTotalTimeSavedTillNow=strFormattedTimeSavedTillNow,
                                                                lsAttachmentPath=[self.report_pricing_file]
                                                                )
                    except Exception as e:
                        await self.log_to_db(client_config['userId'], 'ERROR', f"!! Failed to send the report email, error: {e}")

            except Exception as e:
                await self.log_to_db(client_config['userId'], 'ERROR', f"Failed to process email from sender '{sender}': {e}")



    async def update_report(self, entry):
        """
        Update the report CSV with a new entry.
        If an entry for the same filename or invoice number exists, update only the provided fields
        while keeping the existing data for other fields intact.
        """
        # Read existing entries
        entries = []
        bEntryAlreadyExist = False
        if os.path.isfile(self.report_file):
            with open(self.report_file, mode='r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # Check if the current row matches the entry by 'Invoice No' or 'File Name'
                    if (row['File Name'] == entry.get('File Name')):
                        if (row['Tally Punch-in Status'] != 'Success'):
                            # Update only provided fields and retain others
                            for key, value in entry.items():
                                if value is not None and value != "-":
                                    row[key] = value
                        bEntryAlreadyExist = True
                    entries.append(row)

        # If no matching entry was found, append the new entry
        if not bEntryAlreadyExist:
            entries.append(entry)

        # Write back all entries
        with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
            
            fieldnames = [
                'File Name',
                'Received Date',
                'Vendor Name',
                'Invoice No',
                'Invoice Date',
                'Total Amount',
                'Total Pages',
                'Estimated Time Saved',
                'Tally Punch-in Status',
                'Pricelist Verified',
                'Accuvelocity Comments'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(entries)

        await self.log_to_db(None, 'INFO', f"Report updated for file: {entry.get('Invoice No') or entry.get('File Name')}")


    async def fetch_and_process_emails(self):
        while True:
            try:
                email_ids = await self.fetch_emails(self.days_limit)
                relevant_emails = []

                async with AsyncSessionLocal() as db:
                    # Ensure the connection is active before fetching the email
                    await self.ensure_connection()
                        
                    for email_id in email_ids:
                        # Fetch the email's unique identifier (UID)
                        status, data = self.mail.fetch(email_id, '(UID RFC822)')
                        if status != 'OK':
                            await self.log_to_db(None, 'ERROR', f"Failed to fetch email ID {email_id.decode()}")
                            continue

                        # Extract the UID correctly
                        uid_line = data[0][0].decode()
                        match = re.search(r'UID (\d+)', uid_line)
                        uid = match.group(1) if match else None

                        if not uid:
                            await self.log_to_db(None, 'ERROR', f"Failed to extract UID from email ID {email_id.decode()}")
                            continue

                        # Extract sender information
                        raw_email = data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        sender = msg.get('From')

                        # Check if the email is from a relevant sender for any client
                        for client_name, client_config in self.clients.items():
                            if any(s.lower() in sender.lower() for s in client_config['senderList']):
                                # Check the status of this email in the database
                                result = await db.execute(
                                    select(EmailProcessingRecord).where(
                                        and_(
                                            EmailProcessingRecord.email_id == email_id,
                                            EmailProcessingRecord.sender == sender
                                        )
                                    )
                                )
                                record = result.scalars().first()

                                if not record or (record.status == EmailProcessingStatusEnum.FAILED_TO_PROCESS and record.retry_count <= client_config["MaxRetryCount"]):
                                    bRetry=False  # To specify weather current iteration is an retry or not
                                    if record:
                                        bRetry = True
                                    relevant_emails.append((email_id, client_config, uid, sender, bRetry))

                # Process relevant emails
                for email_id, client_config, uid, sender, bRetry in relevant_emails:
                    await self.process_email(email_id, client_config, uid, sender, bRetry)
                
                # lsAttachmentsEmailProcessing = [self.process_email(email_id=email_id, client_config=client_config, uid=uid, sender=sender) for email_id, client_config, uid, sender in relevant_emails]
                
                # asyncio.gather(*lsAttachmentsEmailProcessing, return_exceptions=True)

            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to Etch and process email: {e}")   
            await asyncio.sleep(5)     # Added 10 Minutes of sleep between polling new emails with pdf attachments

    @staticmethod
    async def create_pricelist_report(iUserId, dictExtractedData, strVendorName, strReportFilePath):
        lsRows = []
        if strVendorName.lower() == "hansgrohe":
            lsRows = await CHansgrohe_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)

        elif strVendorName.lower() == "geberit":
            lsRows = await CGeberit_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "nexion":
            lsRows = await CNexion_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "simpolo":
            lsRows = await CSimpolo_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData, strversion="v3")
            
        elif strVendorName.lower() == "toto":
            lsRows = await CToto_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
            
        elif strVendorName.lower() == "kohler":
            lsRows = await CKohler_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        elif strVendorName.lower() == "aquant":
            lsRows = await CAquant_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)    
        else:
            raise ValueError("No Pricelist found for the given vendor.")
        
        # Call Function to write the csv report 
        if lsRows:
            await CPriceListREPORT.update_pricing_list_report(lsReportRows=lsRows, report_pricing_file=strReportFilePath)
            

    @staticmethod
    async def get_formatted_time(iMinutes):
        
        strFormattedString = ""
        
        try:
            # Convert time to hours and minutes format if needed
            if iMinutes >= 60:
                hours = int(iMinutes // 60)
                minutes = int(iMinutes % 60)
                strFormattedString = f"{hours} Hours {minutes} Minutes"
            else:
                strFormattedString = f"{iMinutes} Minutes"
        except Exception as e:
            pass
        
        return strFormattedString
                            
    
    @staticmethod
    async def update_pricing_list_report(lsReportRows, report_pricing_file):
        """
        Update the pricing list report.
        Enhances formatting for better readability.
        """
        try:
            print(f"Saving Pricing List Report at location: {report_pricing_file}")

            # Define field names
            TilesFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property',
                'Qty(Box)', 'Rate / Box', 'Basic Rate / Sq Ft', 'Discount (%)',
                'Item Amount', 'PriceList Rate / Box', 'Ex-Fac./Sft.', 'Pcs/Box',
                'Sft./Box', 'PriceList Rate / Box Matched', 'PriceList Basic Rate / Sq Ft Matched',
                'Accuvelocity Comments'
            ]
            SanitaryFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property', 'Item Rate',
                'Qty', 'Discount (%)', 'Item Amount', 'PriceList Amount',
                'PriceList Rate Matched', 'Accuvelocity Comments'
            ]

            # Load or create workbook
            try:
                wb = load_workbook(report_pricing_file)
                ws_tiles = wb["Tiles"]
                ws_sanitary = wb["Sanitary"]
            except FileNotFoundError:
                wb = Workbook()
                ws_tiles = wb.active
                ws_tiles.title = "Tiles"
                ws_sanitary = wb.create_sheet(title="Sanitary")

                # Write headers
                ws_tiles.append(TilesFieldNames)
                ws_sanitary.append(SanitaryFieldNames)

            # Process entries and update rows
            def find_and_update_row(sheet, criteria, entry, field_names):
                headers = list(sheet.iter_rows(min_row=1, max_row=1, values_only=True))[0]
                for row_index, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                    if all(row[headers.index(key)] == value for key, value in criteria.items() if key in headers):
                        for col_index, field in enumerate(field_names, start=1):
                            sheet.cell(row=row_index, column=col_index, value=entry.get(field, sheet.cell(row=row_index, column=col_index).value))
                        return True
                return False

            # Vendor categories
            sanitary_vendors = {"Hansgrohe", "Geberit", "Toto", "Kohler", "Aquant"}
            tiles_vendors = {"Ispira", "Nexion", "Simpolo"}

            for report_row in lsReportRows:
                entry = {
                    'VendorName': report_row.strVendorName,
                    'Price List Effective Start Date': report_row.strPriceListEffectiveStartDate,
                    'InvoiceNumber': report_row.strInvoiceNo,
                    'InvoiceDate': report_row.strInvoiceDate,
                    'Unique Item Key': report_row.strItemId,
                    'Item Name': report_row.strItemName,
                    'Matched PricelistItem Property': report_row.strMatchedItemProperty,
                    'Qty(Box)': report_row.strItemQty,
                    'Qty': report_row.strItemQty,
                    'Item Rate': report_row.strItemRate,
                    'Rate / Box': report_row.strItemRatePerBox,
                    'Basic Rate / Sq Ft': report_row.strItemRatePerSqFt,
                    'Discount (%)': report_row.strDiscount,
                    'Item Amount': report_row.strItemAmount,
                    'PriceList Rate / Box': report_row.strPriceListAmount,
                    'PriceList Amount': report_row.strPriceListAmount,
                    'PriceList Rate Matched': report_row.strIsSanitaryItemRateMatch,
                    'Pcs/Box': report_row.strPCSPerBox,
                    'Sft./Box': report_row.strPriceListRatePerSqFt,
                    'Ex-Fac./Sft.': report_row.strExFacAmount,
                    'PriceList Rate / Box Matched': report_row.strIsPriceListRatePerBoxMatch,
                    'PriceList Basic Rate / Sq Ft Matched': report_row.strIsPriceListRatePerSqFtMatch,
                    'Accuvelocity Comments': report_row.strAccuvelocityComments
                }

                if report_row.strVendorName in sanitary_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Item Rate': entry['Item Rate'],
                        'Item Name':  entry['Item Name'],
                    }
                    sheet = ws_sanitary
                    field_names = SanitaryFieldNames
                elif report_row.strVendorName in tiles_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Rate / Box': entry['Rate / Box'],
                        'Item Name':  entry['Item Name']
                    }
                    sheet = ws_tiles
                    field_names = TilesFieldNames
                else:
                    continue

                if not find_and_update_row(sheet, criteria, entry, field_names):
                    sheet.append([entry.get(field, "") for field in field_names])

            # Apply formatting
            ws_tiles= CExcelHelper.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_tiles,  lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})
            ws_sanitary = CExcelHelper.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_sanitary, lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],  column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})

            # Save workbook
            wb.save(report_pricing_file)
            print(f"Pricing list report successfully updated at location: {report_pricing_file}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print("Error:", e)


async def runAttachmentsPolling():
    # Load configuration
    try:
        strConfigurationPath = Path(r"resource/TallyUserConfigV3_Development.json")

        with open(fr"{strConfigurationPath}", 'r') as file:
            tally_config = json.load(file)
    except Exception as e:
        print(f"Failed to load Tally User Config: {e}")
        return

    print(f"******** Running Tally In Development Mode, Using Configuration File: {strConfigurationPath} ********")
    
    # Create a downloader for each email account
    tasks = []
    for email_account, config in tally_config.items():
        processor = EmailAttachmentProcessor(
            email_account=email_account,
            provider=config['metadata']['provider'],
            password=config['metadata']['password'],
            imap_server= os.getenv('ZOHO_IMAP_SERVER') if config['metadata']['provider'] == 'zoho' else os.getenv('GMAIL_IMAP_SERVER'),
            imap_port=int(os.getenv('IMAP_PORT')),
            clients=config['clients'],
            days_limit=1
        )
        
        tasks.append(processor.fetch_and_process_emails())

    await asyncio.gather(*tasks, return_exceptions=True)


class BackupEmailProcessor(BaseEmailProcessor):
    def __init__(self, email_account, provider, password, imap_server, imap_port, backup_clients, days_limit=7):
        super().__init__(email_account, provider, password, imap_server, imap_port)
        self.backup_clients = backup_clients
        self.days_limit = days_limit

    @staticmethod
    def extract_zip_attachment(zip_file_path, extract_to=None):
        """
        Extracts the contents of a zip file to a specified directory.
        """
        try:
            with zipfile.ZipFile(zip_file_path, 'r') as zip_ref:
                if extract_to is None:
                    extract_to = os.path.dirname(zip_file_path)
                zip_ref.extractall(extract_to)
            print(f"Extraction complete. Files extracted to {extract_to}")
            return True
        except Exception as e:
            print(f"An error occurred while extracting the zip file: {e}")
            return False
    
    @staticmethod
    async def MSGetTextFileContent(file_path):
        """
        Reads the contents of a text file and returns it as a string.

        Parameters:
        file_path (str): The path to the text file.

        Returns:
        str: The content of the text file.
        """
        try:
            with open(file_path, 'r', encoding='utf-16') as file:
                content = file.read()
            return content
        except Exception as e:
            print(f"An error occurred while reading the file: {e}")
            return None


    @staticmethod
    async def MSExtractBackupStatus(log_content):
        """
        Extracts the backup status from the log content and checks if the backup was successful.

        Parameters:
        log_content (str): The log content as a string.

        Returns:
        dict: A dictionary containing the backup success status and a message.
        """
        # Find all backup completion lines
        status_pattern = re.compile(r'(\d{4}-\d{2}-\d{2} \d{2}:\d{2}) \*\* Backup done for the task.*?Errors: (\d+).*?Processed files: (\d+).*?Backed up files: (\d+)', re.MULTILINE)
        matches = status_pattern.findall(log_content)
        
        if matches:
            # Get the last match
            last_match = matches[-1]
            timestamp, errors, processed_files, backed_up_files = last_match

            errors = int(errors)
            processed_files = int(processed_files)
            backed_up_files = int(backed_up_files)

            # Check backup success criteria
            if errors == 0 and processed_files > 0 and backed_up_files > 0:
                return {
                    'status': True,
                    'message': f"Backup was successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
            else:
                return {
                    'status': False,
                    'message': f"Backup was not successful on {timestamp}. Errors: {errors}, Processed Files: {processed_files}, Backed Up Files: {backed_up_files}.",
                    'logLine':last_match
                }
        else:
            return {
                'status': False,
                'message': "No backup status found in the log."
            }

    async def process_email(self, email_id, backup_client_config, uid, sender):
        """
        Process backup-related email to extract and store backup status.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Ensure the connection is active before fetching the email
                await self.ensure_connection()

                # Fetch the email
                status, data = self.mail.fetch(email_id, '(RFC822)')
                msg = email.message_from_bytes(data[0][1])

                try:
                    # Extract the 'Date' header from the email to get the actual received date
                    email_received_date = msg.get('Date')

                    # If the 'Date' header is available, parse it, else fall back to 'Received' or the current time
                    if email_received_date:
                        # Parse the 'Date' header to get the received date in the correct timezone
                        parsed_date = parsedate_tz(email_received_date)
                        if parsed_date:
                            email_received_date = datetime.fromtimestamp(mktime_tz(parsed_date))
                        else:
                            raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                    else:
                        # If 'Date' header is missing, fall back to 'Received' header (usually present)
                        received_headers = msg.get_all('Received', [])
                        if received_headers:
                            # Take the first Received header and parse the date
                            first_received = received_headers[0]
                            parsed_date = parsedate_tz(first_received)
                            if parsed_date:
                                email_received_date = datetime.fromtimestamp(mktime_tz(parsed_date))
                            else:
                                raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                        else:
                            raise ValueError(f"Failed to parse received date of email, Date:{email_received_date}")
                except Exception as e:
                    raise ValueError(f"Failed to parse received date of email, Error: {e}")

                # Extract attachment zip file
                for part in msg.walk():
                    if part.get_content_disposition() == 'attachment':
                        filename = part.get_filename()

                        # Proceed only if filename ends with .zip (assuming backups are zipped)
                        if filename and filename.lower().endswith('.zip'):
                            file_data = part.get_payload(decode=True)
                            if file_data:
                                try:
                                    # Create the download directory using email_received_date
                                    email_date_dir = email_received_date.strftime("%Y_%m_%d")  # Adjust format as needed
                                    client_data_dir = backup_client_config['dataDirectory']

                                    backup_download_dir = os.path.join(client_data_dir, email_date_dir, "Tally Backup")
                                    os.makedirs(backup_download_dir, exist_ok=True)
                                                                        
                                    # Save zip attachment temporarily
                                    temp_zip_path = os.path.join(backup_download_dir, filename)
                                    with open(temp_zip_path, 'wb') as f:
                                        f.write(file_data)

                                    # Extract zip
                                    self.extract_zip_attachment(temp_zip_path)

                                    # Format email_received_date to match log file naming convention
                                    log_date_str = email_received_date.strftime("%Y-%m-%d")  # Adjust format if logs use different conventions
                                    # Process extracted files to find logs matching email_received_date
                                    for root, dirs, files in os.walk(backup_download_dir):
                                        for file in files:
                                            if log_date_str in file:  # Match log file to email's received date
                                                log_file_path = os.path.join(root, file)
                                                try:
                                                    log_file_content = await BackupEmailProcessor.MSGetTextFileContent(log_file_path)
                                                    dict_backup_info = await BackupEmailProcessor.MSExtractBackupStatus(log_file_content)

                                                    # Determine backup status
                                                    status_enum = TallyBackupStatusEnum.SUCCESS if dict_backup_info['status'] else TallyBackupStatusEnum.FAILED

                                                    # Save record to TallyBackupRecord model
                                                    log_record = TallyBackupRecord(
                                                        email_id=uid,
                                                        user_id=backup_client_config['userId'],
                                                        sender=sender,
                                                        received_date=email_received_date,
                                                        logFileContent=json.dumps(log_file_content),
                                                        status=status_enum,
                                                        logMessage=json.dumps(dict_backup_info['logLine'])
                                                    )

                                                    db.add(log_record)
                                                    await db.commit()
                                                    await self.log_to_db(None, 'INFO', f"Backup status processed for {log_date_str}")
                                                except Exception as e:
                                                    await self.log_to_db(None, 'ERROR', f"Failed to process log file {file}: {e}")

                                except Exception as e:
                                    await self.log_to_db(None, 'ERROR', f"Failed to process backup email {filename}: {e}")
                await self.log_to_db(None, 'INFO', f"Processed backup email from {sender}")
            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to process backup email: {e}")

    async def fetch_and_process_emails(self):
        while True:
            try:
                email_ids = await self.fetch_emails(self.days_limit)
                relevant_emails = []

                async with AsyncSessionLocal() as db:
                    # Ensure the connection is active before fetching the email
                    await self.ensure_connection()
                        
                    for email_id in email_ids:
                        # Fetch the email's unique identifier (UID)
                        status, data = self.mail.fetch(email_id, '(UID RFC822)')
                        if status != 'OK':
                            await self.log_to_db(None, 'ERROR', f"Failed to fetch email ID {email_id.decode()}")
                            continue

                        # Extract the UID correctly
                        uid_line = data[0][0].decode()
                        match = re.search(r'UID (\d+)', uid_line)
                        uid = match.group(1) if match else None

                        if not uid:
                            await self.log_to_db(None, 'ERROR', f"Failed to extract UID from email ID {email_id.decode()}")
                            continue

                        # Check if this email UID has already been processed
                        existing_record = await db.execute(
                            select(TallyBackupRecord).where(TallyBackupRecord.email_id == uid)
                        )
                        record = existing_record.scalars().first()

                        if record:
                            continue

                        # Extract sender information and subject
                        raw_email = data[0][1]
                        msg = email.message_from_bytes(raw_email)
                        sender = msg.get('From')
                        subject = msg.get('Subject', '')

                        # Extract device name from subject
                        device_name_match = re.search(r"Cobian Backup 11 \((.*?)\)", subject)
                        if not device_name_match:
                            continue

                        extracted_device_name = device_name_match.group(1)

                        # Check if the email is from a relevant backup sender and has matching device name
                        for client_name, client_config in self.backup_clients.items():
                            if any(s.lower() in sender.lower() for s in client_config['senderList']) and \
                                    client_config.get('backupDeviceName', '').lower() == extracted_device_name.lower():
                                relevant_emails.append((email_id, client_config, uid, sender))

                # Process relevant emails
                for email_id, client_config, uid, sender in relevant_emails:
                    await self.process_email(email_id, client_config, uid, sender)

                # lsBackupEmailProcessingTask = [self.process_email(email_id=email_id, backup_client_config=client_config, uid=uid, sender=sender) for email_id, client_config, uid, sender in relevant_emails]
                # asyncio.gather(*lsBackupEmailProcessingTask, return_exceptions=True)
            
            except Exception as e:
                await self.log_to_db(None, 'ERROR', f"Failed to fetch and process backup emails: {e}")
            
            await asyncio.sleep(5)     # Added 10 Minutes of sleep between polling Backup status emails

async def runBackupPolling():
    # Load configuration
    try:
        strConfigurationPath = Path(r"resource/TallyUserConfigV3_Development.json")

        with open(fr"{strConfigurationPath}", 'r') as file:
            tally_config = json.load(file)
    except Exception as e:
        print(f"Failed to load Tally User Config: {e}")
        return

    print(f"******** Running Tally In Development Mode, Using Configuration File: {strConfigurationPath} ********")
    
    # Create a downloader for each email account
    tasks = []
    for email_account, config in tally_config.items():
        backup_processor = BackupEmailProcessor(
            email_account=email_account,
            provider=config['metadata']['provider'],
            password=config['metadata']['password'],
            imap_server=os.getenv('ZOHO_IMAP_SERVER') if config['metadata']['provider'] == 'zoho' else os.getenv('GMAIL_IMAP_SERVER'),
            imap_port=int(os.getenv('IMAP_PORT')),
            backup_clients=config['clients'],
            days_limit=1
        )
        
        tasks.append(backup_processor.fetch_and_process_emails())

    await asyncio.gather(*tasks, return_exceptions=True)



async def run():
    """
    Runs Email Attachments polling and Backup Status polling at the same time
    """
    try:
        await asyncio.gather(
            runBackupPolling(),        # Poll for backup emails
            runAttachmentsPolling(),  # Poll for emails with attachments
            return_exceptions=True
        )
    except KeyboardInterrupt:
        print("Program interrupted.")
    except Exception as e:
        print(f"An error occurred: {e}")


if __name__ == '__main__':
    # pass
    
    # **** 1 For Polling Emails with attachments and to upload them to db *****

    # try:
    #     asyncio.run(runAttachmentsPolling())     # For polling email and downloading and uploading attachments to db
    # except KeyboardInterrupt:
    #     print("Program interrupted.")

    # ****                                                                *****


    # **** 2 For Polling for Backup Emails                                *****
    # try:
    #     asyncio.run(runBackupPolling())     # For polling email and downloading and uploading attachments to db
    # except KeyboardInterrupt:
    #     print("Program interrupted.")

    # ****                                                                *****


    # **** 3 For Running Both Attachments Polling and Backup Status Polling*****
    try:
        asyncio.run(run())
    except KeyboardInterrupt:
        print("Program interrupted.")
    except Exception as e:
        print(f"An error occurred: {e}")
    
    # ****                                                                *****

