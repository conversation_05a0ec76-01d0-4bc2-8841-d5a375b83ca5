### Command Reference for Managing Your FastAPI Application with Nginx and Supervisor
0. **<PERSON><PERSON> repo from GitHub**
   ```   
   Open github
      username:<EMAIL>
      pass:riveredge@303
   Navigate to Accuvelocity repo
      Click on code and then copy URL
      open terminal
         git clone [copied URL]
         username:drivetest721
         password:****************************************
   Done
   ```
   
1. **Check Nginx Status**
   ```sh
   sudo systemctl status nginx
   ```

2. **Install NPM Packages and Build Project**
   ```sh
   npm i
   npm run build
   ```

3. **Install Python Requirements**
   ```sh
   pip install -r resource/requirement.txt
   ```

   **Note:** Always ensure you install the latest requirements before upgrading the application version.

4. **Manage Supervisor for FastAPI**

   - **Reload Supervisor**
     ```sh
     sudo supervisorctl reload
     ```

   - **Edit Supervisor Configuration**
     ```sh
     sudo nano /etc/supervisor/conf.d/myfastapiapp.conf
     ```

   - **Start FastAPI Application**
     ```sh
     sudo supervisorctl start myfastapiapp
     ```

   - **Stop FastAPI Application**
     ```sh
     sudo supervisorctl stop myfastapiapp
     ```

   - **Restart FastAPI Application**
     ```sh
     sudo supervisorctl restart myfastapiapp
     ```

5. **Edit Log Files**

   - **Error Log**
     ```sh
     sudo nano /var/log/myfastapiapp/myfastapiapp.err.log
     ```

   - **Output Log**
     ```sh
     sudo nano /var/log/myfastapiapp/myfastapiapp.out.log
     ```

6. **View Last 50 Lines of Log Files**

   - **Error Log**
     ```sh
     sudo tail -50 /var/log/myfastapiapp/myfastapiapp.err.log
     ```

   - **Output Log**
     ```sh
     sudo tail -50 /var/log/myfastapiapp/myfastapiapp.out.log
     ```

### Upgrading Your Application

1. **Stop FastAPI Application**
   Use the stop command to stop the `myfastapiapp` service:
   ```sh
   sudo supervisorctl stop myfastapiapp
   ```

2. **Upgrade Backend and Frontend Environment**
   Ensure you install the latest requirements and rebuild the frontend:
   ```sh
   pip install -r resource/requirement.txt
   npm i
   npm run build
   ```

3. **Start FastAPI Application**
   After upgrading, start the `myfastapiapp` service:
   ```sh
   sudo supervisorctl start myfastapiapp
   ```

By following these steps, you can efficiently manage and upgrade your FastAPI application, ensuring it runs smoothly and any changes are properly deployed.