import asyncio
import aiohttp
import numpy as np
import pandas as pd

# Your OpenAI API Key
API_KEY = '********************************************************************************************************************************************************************'

async def getEmbeddingsFromOpenAI(strLedgerName, session):
    """
    Asynchronous function to get embeddings from OpenAI API.

    Args:
        strLedgerName (str): The ledger name for which to get embeddings.
        session (aiohttp.ClientSession): The aiohttp session to use for the request.

    Returns:
        dict: The response data containing the embeddings.
    """
    url = "https://api.openai.com/v1/embeddings" 
    headers = {
        "Authorization": f"Bearer {API_KEY}",
        "Content-Type": "application/json",
    }
    json_data = {
        "input": strLedgerName,
        "model": "text-embedding-3-large",
        "dimensions": 256
    }
    
    async with session.post(url, headers=headers, json=json_data) as response:
        response_data = await response.json()
        return response_data

async def getEmbeddingsForSeries(dsInput):
    """
    Asynchronous function to get embeddings for a series of ledger names.

    Args:
        dsInput (pd.Series): A series of ledger names.

    Returns:
        list: A list of embeddings for the input ledger names.
    """
    listEmbeddings = []
    
    # Create a session to use for all requests
    async with aiohttp.ClientSession() as session:
        # Create a list of coroutines to run asynchronously
        coroutines = [getEmbeddingsFromOpenAI(strName, session) for strName in dsInput]
        
        # Run the coroutines concurrently
        responses = await asyncio.gather(*coroutines)
        
        # Process responses and extract embeddings
        for response in responses:
            listEmbedding = response['data'][0]['embedding']
            listEmbeddings.append(listEmbedding)

    return listEmbeddings

def main(dsInput):
    """
    Runs the asynchronous function to get embeddings for a series of ledger names.

    Args:
        dsInput (pd.Series): A series of ledger names.

    Returns:
        list: A list of embeddings for the input ledger names.
    """
    return asyncio.run(getEmbeddingsForSeries(dsInput))