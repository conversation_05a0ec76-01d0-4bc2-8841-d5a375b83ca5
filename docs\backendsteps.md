### Step-by-Step Guide to Set Up and Configure Your FastAPI Application with Nginx and Supervisor

0. **<PERSON><PERSON> repo from GitHub**
   ```   
   Open github
      username:<EMAIL>
      pass:riveredge@303
   Navigate to Accuvelocity repo
      Click on code and then copy URL
      open terminal
         git clone [copied URL]
         username:drivetest721
         password:****************************************
   Done
   ```

0.5 **Create envirnment**
   ```
   Backend:
      open createUbuntuEnv.sh and ensure this path (pip install -r '/home/<USER>/AccuVelocity/resource/requirements.txt')
      open terminal in AccuVelocity folder and run below command
         source createUbuntuEnv.sh
   Frontend:
      open terminal in AccuVelocity/client
      run "npm i" command in terminal
      run "npm run build" to create dist folder for production deployement 

   Create .env file inside Accuvelocity/client directory and paste this below content
      ```
      VITE_SERVER=https://backend.accuvelocity.com/api
      # https://backend.accuvelocity.com/api  , http://localhost:8000/api
      VITE_SITE_KEY =  6LeQpcYpAAAAADwmy5jDIBkfnfw2U1H_uMAlmNoW # Development 6Lc2ksYpAAAAAJx7BsK1G9G9O4O75-wvzDAj2pk9 , Production 6LeQpcYpAAAAADwmy5jDIBkfnfw2U1H_uMAlmNoW      #change this key as per your registered domain
      MaxTrialPaidDocExtractionPerUser = 12

      test_user_email="<EMAIL>"
      TEST_USER_PASS="Ridham@123"
      ```
   
   Create .env file inside Accuvelocity/ directory and paste this below content
      ```
      ENV=development
      LOG_LEVEL=info
      CORS_DOMAIN=
      DATABASE_NAME=DBVelocity      # change database name as per your application
      DATABASE_USER=riveredge       # change username as per your system
      DATABASE_PASSWORD_DEV=real123 # your mysql username password
      DATABASE_URL_DEV=localhost 
      DATABASE_PORT=3306
      JWT_SECRET=ea7634b4c4b4c23a42218d4b0c8148699ee4f53797be593699cd6239256a68ad
      # DATABASE_PASSWORD_DEV=3k2HlQOQV6z8nTEaCcRM

      MAIL_USERNAME=<EMAIL>
      MAIL_PASSWORD=WSuNegdfaGA5
      MAIL_FROM=<EMAIL>
      MAIL_PORT=465
      MAIL_SERVER=smtppro.zoho.in
      MAIL_TLS=False
      MAIL_SSL=True
      MAIL_FROM_NAME=AccuVelocity
      FORGET_PASSWORD_URL=https://app.accuvelocity.com/recovery-password/        # change this as per your frontend domain
      #in minutes
      ForgetEmailExpiryTime = 10
      GEMINIAPI1="AIzaSyBGLajk9kkRaHozY7HTS992OXylhSPj7YA"
      VITE_SERVER = https://backend.accuvelocity.com/api                         # change this as per your backend domain

      # Google Document OCR Service Account Key
      GOOGLE_APPLICATION_CREDENTIALS = "resource/GoogleDocOCRServiceConfig.json"
      s3_bucket_access_key = ********************
      S3_bucket_secret_key = DZYmiGY8Ou4pOLBe8Rl8DC/3JY/Y3e6ieYZj/HUn
      s3_bucket_name = "accuvelocity"
      ```
   ```


1. **Create Supervisor Configuration for FastAPI**
   - **Edit Supervisor Configuration**
     ```sh
     sudo nano /etc/supervisor/conf.d/myfastapiapp.conf
     ```
   - **Ensure the paths reflect your setup (e.g., `/home/<USER>/path_to_your_project`).**
      ```
      [program:myfastapiapp]
      directory=/home/<USER>/path_to_your_project/
      command=/home/<USER>/path_to_your_project/5_Env/bin/uvicorn src.main:app --host 0.0.0.0 --port 8000 --workers 6
      autostart=true
      autorestart=true
      stderr_logfile=/var/log/myfastapiapp/myfastapiapp.err.log
      stdout_logfile=/var/log/myfastapiapp/myfastapiapp.out.log
      ```

2. **Create Log Files for Supervisor**
   Create the necessary log directory and files.

   ```bash
   sudo mkdir /var/log/myfastapiapp
   sudo touch /var/log/myfastapiapp/myfastapiapp.err.log
   sudo touch /var/log/myfastapiapp/myfastapiapp.out.log
   ```

3. **Configure Nginx for FastAPI Backend**
   Edit the Nginx configuration file for your FastAPI backend domain.

   ```sh
   sudo nano /etc/nginx/sites-available/backend.accuvelocity.com
   ```

   Paste the following server block configuration:

   ```nginx
   server {
       listen 80;
       server_name backend.accuvelocity.com;
       client_max_body_size 22M;  # Add this line to increase the limit to 22 MB

       location / {
           proxy_pass http://localhost:8000;
           proxy_set_header Host $host;
           proxy_set_header X-Real-IP $remote_addr;
           proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
           proxy_set_header X-Forwarded-Proto $scheme;
       }
   }

   then update supervisor
      sudo supervisorctl update

   ```

4. **Enable the Site and Test the Configuration**
   Create a symbolic link to enable the site and test the Nginx configuration.

   ```bash
   sudo ln -s /etc/nginx/sites-available/backend.accuvelocity.com /etc/nginx/sites-enabled/
   sudo nginx -t
   sudo systemctl reload nginx
   ```

5. **Set Up SSL with Certbot**
   Obtain and install an SSL certificate with Certbot.

   ```bash
   sudo certbot --nginx -d backend.accuvelocity.com
   ```

6. **Clean Up Old Configuration Files**
   If necessary, remove old configuration files from Nginx.

   ```bash
   sudo rm /etc/nginx/sites-available/backend.accuvelocity.in
   sudo rm /etc/nginx/sites-enabled/backend.accuvelocity.in
   ```

By following these steps, your FastAPI application will be managed by Supervisor and served securely by Nginx. This setup ensures your application is monitored, can auto-restart if necessary, and is accessible via a domain with proper SSL configuration.