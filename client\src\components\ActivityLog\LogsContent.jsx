import PropTypes from 'prop-types';
import { TbLogs } from 'react-icons/tb';
import { AiOutlineCloudUpload } from "react-icons/ai";
import { IoDocumentTextOutline } from "react-icons/io5";
import { MdOutlineBarChart } from "react-icons/md";
import { FaUser } from "react-icons/fa6";
import BillingPath from '../../assets/Strings/BillingPath.js'

const LogsContent = ({ isOpen, onClose, content }) => {
    if (!isOpen) return null;

    const formatDateEDT = (dateObj) => {
        // Extract components from the date object
        const { month, day, year, hours, minutes, am_pm } = dateObj;

        // Format the hours and minutes to ensure two digits
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        // Construct the formatted date string
        return (
            <div className="flex flex-col">
                <span className="text-base font-semibold">{`${formattedHours}:${formattedMinutes} ${am_pm} EDT`}</span>
                <span className="text-sm text-gray-700">{`${month} ${day}, ${year}`}</span>
            </div>
        );
    };
    const BillingIcon = (
        <svg width="20" height="16" className="mr-1.5" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d={BillingPath} />
        </svg>
    )
    const SectionMap = {
        "Upload": <AiOutlineCloudUpload className="h-5 w-5 mr-1.5" />,
        "MyDocuments": <IoDocumentTextOutline className="h-5 w-5 mr-1.5" />,
        "MyModels": <MdOutlineBarChart className="h-5 w-5 mr-1.5" />,
        "Billing": BillingIcon,
        "Profile": <FaUser className="h-4 w-5 mr-1.5" />,
    }
    return (
        <div className="fixed inset-0 flex items-center justify-center z-50">
            <div className="fixed inset-0 bg-black opacity-50" onClick={onClose}></div>
            <div className="bg-white rounded-lg p-6 z-10 w-2/3 max-h-[80vh] overflow-y-auto shadow-lg">
                <div className="flex justify-between items-center mb-4">
                    <h2 className="text-2xl font-bold text-[#003654] flex"><TbLogs className="h-6 w-6 mt-1 mr-1" />Log Details</h2>
                    <button onClick={onClose} className="text-3xl font-bold text-[#003654]">&times;</button>
                </div>
                <div className="mt-2 space-y-4">
                    <div>
                        <p className="text-lg font-semibold text-[#003654]">Date:</p>
                        {formatDateEDT(content.LogDateTime)}
                    </div>
                    <div>
                        <p className="text-lg font-semibold text-[#003654]">Message:</p>
                        <p className="text-base text-gray-700">{content.LogMessage}</p>
                    </div>
                    <div>
                        <p className="text-lg font-semibold text-[#003654]">Section:</p>
                        <p className="text-base text-gray-700"><div className="flex items-center">{SectionMap[content.Section]}{content.Section}</div></p>
                    </div>
                </div>
            </div>
        </div>
    );
};

export default LogsContent;

LogsContent.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    onClose: PropTypes.func.isRequired,
    content: PropTypes.object.isRequired,
};