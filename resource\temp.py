s = "{\"InvoiceNumber\":\"2024-25/DD/1095\",\"InvoiceDate\":251024,\"JobNumber\":[\"JOB NO. 12278\"],\"DeliveryNoteDate\":251024,\"SellerName\":\"Pegasus Inland Container Depot Pvt.Ltd.\",\"SellerAddress\":\"SURVEY NO.981, PITHAMPUR-INDORE AIRPORT ROAD, Village Dhannad,Indore -453001\",\"SellerGSTINNo\":\"23**********1Z9\",\"SellerStateName\":\"Madhya Pradesh\",\"SellerStatecode\":23,\"SellerCINNo\":\"U00630MP2006PTC018604\",\"SellerEmail\":\"<EMAIL>\",\"BuyerName\":\"FAIRDEAL INTERNATIONAL (CHA)\",\"BuyerAddress\":\"Seventh Floor, 702 Apollo Premier, A.B. Road, Vijay agar Square, Indore\",\"BuyerGSTIN\":\"23AAAFF6157B1ZZ\",\"City/PortOfLoading\":\"\",\"City/PortOfDischarge\":\"\",\"ItemTotal\":8330.00,\"ContainerNo\":\"ILCU5301161\",\"ContainerSize\":\"40\",\"ShippingBill/BOENo\":0,\"ShippingBill/BOEDate\":0,\"CGSTAmount\":499.80,\"SGSTAmount\":499.80,\"IGSTAmount\":0,\"RoundOff\":0.40,\"NetAmount\":9330.00,\"FinancialYear\":\"2024-25\",\"PaymentDiscrepancyTimeline\":\"7 days\",\"Remarks\":\"BEING CONT NO. ILCU5301161/40 JOB NO. 12278 A/C VOLVO GROUP INDIA PRIVATE (ICD IN CONT CMAU6205893) BL NO. ********** INVOICE NO. 2024-25/7620\",\"PAN\":\"**********\",\"PreparedBy\":\"MILAN\",\"Table\":[{\"Sl.No\":\"1\",\"DescriptionofServices\":\"SHORTLEAD TRANSPORTATION\",\"HSN/SAC\":996511,\"GSTRate\":12,\"Amount\":8330.00,\"TaxableAmount\":999},{\"Sl.No\":\"0\",\"DescriptionofServices\":\"Total\",\"HSN/SAC\":0,\"GSTRate\":0,\"Amount\":3000.00,\"TaxableAmount\":0}]}"

import json
di = json.loads(s)
with open("temp.json", "w") as d:
    json.dump(di, d)
