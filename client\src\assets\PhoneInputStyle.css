/* Custom styles for react-phone-number-input */

/* Remove the right border from the country select to make it merge with the input */
.PhoneInputCountry {
  border-color: rgb(224 224 224 / 1);
  height: 45px; /* Matching height */
  border-radius: 0.375rem 0 0 0.375rem; /* Rounded corners on the left side */
  box-shadow: none;
  outline: none;
  padding: 0.5rem;
  border-right: none !important; /* This will help to merge with the input field */
}

/* Apply styles to the phone input to make it merge with the country select */
.PhoneInputInput {
  border-color: rgb(224 224 224 / 1);
  height: 45px !important; /* Matching height */
  box-shadow: none !important;
  outline: none;
  border-radius: 0 0.375rem 0.375rem 0; /* Rounded corners on the right side */
  padding: 0.5rem; /* You can adjust this value to align text vertically */
}

/* Ensure that the overall container of the phone input is styled to match */
.PhoneInput {
  display: flex;
  align-items: center;
  border-radius: 0.375rem;
  border-color: rgb(224, 224, 224);
  overflow: hidden; /* This will cut off any border that sticks out inside the container */
}

/* Apply styles to the phone input to make it merge with the country select */
.PhoneInputInput {
  border: none; /* Remove individual border */
  height: 45px; /* Matching height */
  box-shadow: none;
  outline: none;
  padding: 0.5rem; /* You can adjust this value to align text vertically */
}

/* Focus styles for the input fields within the PhoneInput */
.PhoneInput input:focus {
  outline: none; /* Remove default focus outline */
}

/* Focus styles for the entire PhoneInput container */
.PhoneInput:focus-within {
  border-color: #3182ce; /* Blue border for focus */
}

.PhoneInputCountryFlagContainer {
  border-right: none !important; /* This will help to merge with the input field */
}

/* Ensure that the overall container of the phone input is styled to match */
.PhoneInput {
  display: flex;
  align-items: center;
  border-radius: 0.375rem;
  border: 1px solid; /* Add border to the whole container */
  border-color: rgb(224 224 224 / 1);
  overflow: hidden; /* This will cut off any border that sticks out inside the container */
}
