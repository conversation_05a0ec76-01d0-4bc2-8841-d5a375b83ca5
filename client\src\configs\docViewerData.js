import Finance_Invoice from "../assets/PDF/1_Finance_Invoice.pdf";
import Finance_Receipt from "../assets/PDF/2_Finance_Receipt.pdf";
import Finance_AccountSatement from "../assets/PDF/3_Finance_AccountSatement.pdf";
import HR_Resume from "../assets/PDF/4_HR_Resume.pdf";
import HR_Job_Description from "../assets/PDF/5_HR_Job_Description.pdf";
import Medical_Insurance_Medical_Invoice from "../assets/PDF/6_Medical_Insurance_Medical_Invoice.pdf";
import Medical_Insurance_Medical_Record from "../assets/PDF/7_Medical_Insurance_Medical_Record.pdf";
import Medical_Insurance_Reconsideration from "../assets/PDF/8_Medical_Insurance_Reconsideration.pdf";
import Medical_Insurance_EOR from "../assets/PDF/9_Medical_Insurance_EOR.pdf";

export const DOCUMENTS = [
  {
    id: "Finance_Invoice",
    type: "Finance",
    title: "Invoice",
  },
  {
    id: "Finance_Receipt",
    type: "Finance",
    title: "Receipt",
  },
  {
    id: "Finance_AccountSatement",
    type: "Finance",
    title: "Account Satement",
  },
  {
    id: "HR_Resume",
    type: "HR",
    title: "Resume",
  },
  {
    id: "HR_Job_Description",
    type: "HR",
    title: "HR Job Description",
  },
  {
    id: "Medical_Insurance_Medical_Invoice",
    type: "Medical Insurance",
    title: "MedicalInvoice",
  },
  {
    id: "Medical_Insurance_Medical_Record",
    type: "Medical Insurance",
    title: "Medical Record",
  },
  {
    id: "Medical_Insurance_Reconsideration",
    type: "Medical Insurance",
    title: "Reconsideration",
  },
  {
    id: "Medical_Insurance_EOR",
    type: "Medical Insurance",
    title: "EOR",
  },
];

export const DOCUMENTS_DOC_VIEWER_DATA = {
  Finance_Invoice: {
    file: Finance_Invoice,
    highlightCoordinates: [
      {
        page: 2,
        x1: 93,
        y1: 181,
        x2: 132,
        y2: 192,
      },
      {
        page: 1,
        x1: 539,
        y1: 97,
        x2: 583,
        y2: 108,
      },
      {
        page: 1,
        x1: 529,
        y1: 108,
        x2: 581,
        y2: 119,
      },
      {
        page: 2,
        x1: 502,
        y1: 177,
        x2: 548,
        y2: 188,
      },
      {
        page: 1,
        x1: 498,
        y1: 411,
        x2: 527,
        y2: 420,
      },
      {
        page: 1,
        x1: 498,
        y1: 391,
        x2: 523,
        y2: 401,
      },
      {
        page: 1,
        x1: 449,
        y1: 153,
        x2: 540,
        y2: 180,
      },
    ],
    currentValue: {
      VendorName: "MeyerPT",
      InvoiceNumber: "11428979",
      InvoiceDate: "06/04/2024",
      DueDate: "07/04/2024",
      TotalInvoiceAmountWithTax: "$711.61",
      TotalTax: "$29.61",
      ShipToOrServiceToLocation:
        "HEALTHPLUS MEDICAL GROUP, 789 OAK LANE, SUITE 200, METROPOLIS, NY, 54321",
    },
    tablesData: {
      LineItemTable: [
        {
          SerialNumber: "1",
          Description: "THERA-BAND LATEX-FREE PROFESSIONAL",
          Quantity: "1",
          Rate: "$415.3000",
          Amount: "$415.30",
        },
        {
          SerialNumber: "2",
          Description: "THERA-BAND LATEX-FREE PROFESSIONAL",
          Quantity: "1",
          Rate: "$99.3000",
          Amount: "$99.30",
        },
        {
          SerialNumber: "3",
          Description: "BODY SPORT HAND THERAPY PUTTY, GREEN",
          Quantity: "10",
          Rate: "$83.3700",
          Amount: "$83.37",
        },
        {
          SerialNumber: "4",
          Description: "BODY SPORT HAND THERAPY PUTTY, RED",
          Quantity: "10",
          Rate: "$83.3700",
          Amount: "$83.37",
        },
      ],
    },
    meta_data: {
      page_width: 612.0,
      page_height: 792.0,
    },
  },
  Finance_Receipt: {
    file: Finance_Receipt,
    highlightCoordinates: [
      {
        page: 1,
        x1: 81,
        y1: 53,
        x2: 162,
        y2: 71,
      },
      {
        page: 1,
        x1: 63,
        y1: 85,
        x2: 184,
        y2: 108,
      },
      {
        page: 1,
        x1: 63,
        y1: 541,
        x2: 194,
        y2: 554,
      },
      {
        page: 1,
        x1: 63,
        y1: 541,
        x2: 194,
        y2: 554,
      },
      {
        page: 1,
        x1: 38,
        y1: 499,
        x2: 221,
        y2: 511,
      },
      {
        page: 1,
        x1: 124,
        y1: 429,
        x2: 231,
        y2: 443,
      },
      {
        page: 1,
        x1: 207,
        y1: 442,
        x2: 231,
        y2: 450,
      },
      {
        page: 1,
        x1: 143,
        y1: 452,
        x2: 232,
        y2: 463,
      },
    ],
    currentValue: {
      CompanyName: "Walmart",
      CompanyAddress: "882 S. STATE ROAD 136, GREENWOOD IN 46143",
      TypeOfRecipt: "Retail",
      TransactionDate: "04-27-2019",
      Time: "12:59:46",
      ReceiptNumber: "0783 5080 4072 3416 2495 6",
      PaymentMethod: "",
      Subtotal: "139.44",
      Tax: "4.58",
      Discounts: "",
      FinalAmount: "144.02",
    },
    tablesData: {
      ItemsPurchased: [
        {
          ItemName: "TATER TOTS",
          ItemQuantity: "1",
          ItemPrice: "2.96",
          TotalPrice: "2.96",
        },
        {
          ItemName: "HARD/PROV/DC",
          ItemQuantity: "2",
          ItemPrice: "2.68",
          TotalPrice: "5.36",
        },
        {
          ItemName: "SNACK BARS",
          ItemQuantity: "1",
          ItemPrice: "4.98",
          TotalPrice: "4.98",
        },
        {
          ItemName: "HRI CL CHS",
          ItemQuantity: "2",
          ItemPrice: "5.88",
          TotalPrice: "11.76",
        },
        {
          ItemName: "HRI CL CHS",
          ItemQuantity: "1",
          ItemPrice: "6.88",
          TotalPrice: "-6.88",
        },
        {
          ItemName: "HRI 12 U SG",
          ItemQuantity: "1",
          ItemPrice: "5.88",
          TotalPrice: "5.88",
        },
        {
          ItemName: "HRI CL PEP",
          ItemQuantity: "1",
          ItemPrice: "6.88",
          TotalPrice: "6.88",
        },
        {
          ItemName: "EARBUDS",
          ItemQuantity: "1",
          ItemPrice: "4.88",
          TotalPrice: "4.88",
        },
        {
          ItemName: "SC BCN CHOOR",
          ItemQuantity: "1",
          ItemPrice: "6.98",
          TotalPrice: "6.98",
        },
        {
          ItemName: "ABF THINBRST",
          ItemQuantity: "1",
          ItemPrice: "9.72",
          TotalPrice: "9.72",
        },
        {
          ItemName: "DV RSE OIL M",
          ItemQuantity: "1",
          ItemPrice: "5.94",
          TotalPrice: "5.94",
        },
        {
          ItemName: "APPLE 3 BAG",
          ItemQuantity: "1",
          ItemPrice: "6.47",
          TotalPrice: "6.47",
        },
        {
          ItemName: "STOK LT SWT",
          ItemQuantity: "1",
          ItemPrice: "4.42",
          TotalPrice: "4.42",
        },
        {
          ItemName: "PEANUT BUTTR",
          ItemQuantity: "1",
          ItemPrice: "6.44",
          TotalPrice: "6.44",
        },
        {
          ItemName: "AVO VERDE",
          ItemQuantity: "1",
          ItemPrice: "2.98",
          TotalPrice: "2.98",
        },
        {
          ItemName: "ROLLS",
          ItemQuantity: "1",
          ItemPrice: "1.28",
          TotalPrice: "1.28",
        },
        {
          ItemName: "BTS DRY BLON",
          ItemQuantity: "1",
          ItemPrice: "6.58",
          TotalPrice: "6.58",
        },
        {
          ItemName: "GALE",
          ItemQuantity: "1",
          ItemPrice: "32.00",
          TotalPrice: "32.00",
        },
        {
          ItemName: "TR HS FRM",
          ItemQuantity: "1",
          ItemPrice: "2.74",
          TotalPrice: "2.74",
        },
        {
          ItemName: "BAGELS",
          ItemQuantity: "1",
          ItemPrice: "4.56",
          TotalPrice: "4.56",
        },
        {
          ItemName: "GV SLIDERS",
          ItemQuantity: "1",
          ItemPrice: "2.98",
          TotalPrice: "2.98",
        },
        {
          ItemName: "ACCESSORY",
          ItemQuantity: "1",
          ItemPrice: "0.97",
          TotalPrice: "0.97",
        },
        {
          ItemName: "CHEEZE IT",
          ItemQuantity: "1",
          ItemPrice: "4.00",
          TotalPrice: "4.00",
        },
        {
          ItemName: "RITZ",
          ItemQuantity: "1",
          ItemPrice: "2.78",
          TotalPrice: "2.78",
        },
        {
          ItemName: "RUFFLES",
          ItemQuantity: "1",
          ItemPrice: "2.50",
          TotalPrice: "2.50",
        },
        {
          ItemName: "GV HNY GRMS",
          ItemQuantity: "1",
          ItemPrice: "1.28",
          TotalPrice: "1.28",
        },
      ],
    },
    meta_data: {
      page_width: 269.0,
      page_height: 678.0,
    },
  },
  Finance_AccountSatement: {
    file: Finance_AccountSatement,
    highlightCoordinates: [
      {
        page: 1,
        x1: 11,
        y1: 76,
        x2: 124,
        y2: 85,
      },
      {
        page: 1,
        x1: 11,
        y1: 93,
        x2: 163,
        y2: 102,
      },
      {
        page: 1,
        x1: 14,
        y1: 170,
        x2: 159,
        y2: 179,
      },
    ],
    currentValue: {
      BankName: "Pacific Valley Bank",
      BranchName: "P.O. Box 3648 Salinas, CA",
      AccountHolder: "PINNACLE MEDICAL GROUP INC",
      AccountNumber: "9645",
      StatementPeriod: "11/01/2023-11/30/2023",
      CurrencyUnit: "USD",
      OpeningBalance: "$4,969.61",
      ClosingBalance: "$4,813.79",
      TotalCredits: "$15,953.33",
    },
    tablesData: {
      Transactions: [
        {
          Date: "11/01/2023",
          Description: "PNC MERCHANT DEPOSIT ************",
          Amount: "$64.48",
        },
        {
          Date: "11/02/2023",
          Description:
            "HCCLAIMPMT VPAY E027517091 TRN* 1* ********** **********\\",
          Amount: "$13.99",
        },
        {
          Date: "11/06/2023",
          Description:
            "HCCLAIMPMT VPAY E027658173 TRN* 1* ********* **********\\",
          Amount: "$153.93",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027673380 TRN* 1* ********* **********\\",
          Amount: "$32.02",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027676273 TRN* 1* 01893436* **********\\",
          Amount: "$90.00",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027676273 TRN* 1* 01893469* **********\\",
          Amount: "$90.00",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027678124 TRN* 1* 01892953* **********\\",
          Amount: "$90.00",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027678652 TRN* 1* 01894510* **********\\",
          Amount: "$90.00",
        },
        {
          Date: "11/07/2023",
          Description:
            "HCCLAIMPMT VPAY E027679547 TRN* 1* 01894835* **********\\",
          Amount: "$90.00",
        },
        {
          Date: "11/08/2023",
          Description:
            "HCCLAIMPMT VPAY E027700793 TRN* 1* 139119330* 1362685608\\",
          Amount: "$180.00",
        },
        {
          Date: "11/08/2023",
          Description:
            "HCCLAIMPMT VPAY E027711727 TRN* 1* 138041938* 1362685608\\",
          Amount: "$180.00",
        },
        {
          Date: "11/09/2023",
          Description:
            "HCCLAIMPMT VPAY E027747760 TRN* 1* 138387751* 1362685608\\",
          Amount: "$180.00",
        },
        {
          Date: "11/09/2023",
          Description:
            "HCCLAIMPMT VPAY E027764337 TRN* 1* 139120001* 1362685608\\",
          Amount: "$270.00",
        },
        {
          Date: "11/10/2023",
          Description: "MERCHANT BNKCD DEPOSIT 359200119881",
          Amount: "$270.00",
        },
        {
          Date: "11/13/2023",
          Description: "MERCHANT BNKCD DEPOSIT 359200119881",
          Amount: "$270.00",
        },
        {
          Date: "11/13/2023",
          Description: "MERCHANT BNKCD DEPOSIT 359200119881",
          Amount: "$632.15",
        },
        {
          Date: "11/13/2023",
          Description: "PNC MERCHANT DEPOSIT ************",
          Amount: "$77.44",
        },
        {
          Date: "11/13/2023",
          Description: "MERCHANT BNKCD DEPOSIT 359200119881",
          Amount: "$294.08",
        },
        {
          Date: "11/02/2023",
          Description: "VPAY F027527770 V75240522 752405220",
          Amount: "$39.50",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359200119881",
          Amount: "$2.59",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD INTERCHNG 359200119881",
          Amount: "$12.85",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359161480884",
          Amount: "$27.95",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359161483888",
          Amount: "$27.95",
        },
        {
          Date: "11/13/2023",
          Description: "PNC MERCHANT DEPOSIT ************",
          Amount: "$77.44",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD FEE 359200119881",
          Amount: "$35.56",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359161481882",
          Amount: "$44.95",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359161482880",
          Amount: "$44.95",
        },
        {
          Date: "11/03/2023",
          Description: "MERCHANT BNKCD DISCOUNT 359161484886",
          Amount: "$44.95",
        },
        {
          Date: "11/06/2023",
          Description: "PNC MERCHANT DISCOUNT ************",
          Amount: "$40.39",
        },
        {
          Date: "11/06/2023",
          Description: "PGANDE WEB ONLINE 69993630101923",
          Amount: "$380.02",
        },
        {
          Date: "11/13/2023",
          Description: "BK OF AMER VI/MC ONLINE PMT CKF242250070POS",
          Amount: "$100.00",
        },
        {
          Date: "11/15/2023",
          Description: "FDMS FDMS PYMT 052-1633099-000",
          Amount: "$28.28",
        },
        {
          Date: "11/15/2023",
          Description: "FDMS FDMS PYMT 052-1633305-000",
          Amount: "$28.28",
        },
        {
          Date: "11/15/2023",
          Description: "FDMS FDMS PYMT 052-1633166-000",
          Amount: "$28.41",
        },
        {
          Date: "11/15/2023",
          Description: "FDMS FDMS PYMT 052-1632693-000",
          Amount: "$28.41",
        },
        {
          Date: "11/15/2023",
          Description: "FDMS FDMS PYMT 052-1633304-000",
          Amount: "$28.41",
        },
        {
          Date: "11/28/2023",
          Description: "PGANDE WEB ONLINE 72795293111623",
          Amount: "$57.68",
        },
        {
          Date: "11/28/2023",
          Description: "PINNACLE MEDICAL Trfr 2BofA 121143833",
          Amount: "$15,000.00",
        },
        {
          Date: "11/30/2023",
          Description: "SERVICE CHARGE",
          Amount: "$108.02",
        },
      ],
    },
    meta_data: {
      page_width: 612.0,
      page_height: 792.0,
    },
  },
  HR_Resume: {
    file: HR_Resume,
    highlightCoordinates: [
      {
        page: 1,
        x1: 36,
        y1: 41,
        x2: 220,
        y2: 67,
      },
      {
        page: 1,
        x1: 36,
        y1: 127,
        x2: 439,
        y2: 137,
      },
      {
        page: 1,
        x1: 36,
        y1: 127,
        x2: 439,
        y2: 137,
      },
      {
        page: 1,
        x1: 36,
        y1: 109,
        x2: 382,
        y2: 119,
      },
      {
        page: 1,
        x1: 36,
        y1: 109,
        x2: 382,
        y2: 119,
      },
      {
        page: 1,
        x1: 36,
        y1: 109,
        x2: 382,
        y2: 119,
      },
      {
        page: 1,
        x1: 36,
        y1: 127,
        x2: 439,
        y2: 137,
      },
      {
        page: 1,
        x1: 36,
        y1: 174,
        x2: 578,
        y2: 226,
      },
      {
        page: 1,
        x1: 54,
        y1: 471,
        x2: 380,
        y2: 534,
      },
      {
        page: 2,
        x1: 54,
        y1: 500,
        x2: 250,
        y2: 599,
      },
      {
        page: 2,
        x1: 54,
        y1: 435,
        x2: 430,
        y2: 464,
      },
      {
        page: 2,
        x1: 54,
        y1: 634,
        x2: 108,
        y2: 681,
      },
      {
        page: 2,
        x1: 54,
        y1: 101,
        x2: 393,
        y2: 681,
      },
    ],
    currentValue: {
      FullName: "Michael T. Smith",
      Gender: "Male",
      BirthDate: "August 12, 1985",
      EmailAddress: "<EMAIL>",
      Address: "5678 Oak Avenue, Rivertown, NY 10501",
      ContactNumber: "(914) 947-6543",
      LinkedInProfileURL:
        "https://www.linkedin.com/in/michael-t-smith-a719481a",
      ObjectiveStatement:
        "Highly motivated Project Manager and Technical Consultant with a passion for empowering non-profit organizations. Seeks to leverage a unique blend of technical expertise (Visual Basic, SQL, etc.) and strategic planning to automate workflows and deliver efficient, scalable solutions. Proven ability to build strong client relationships and ensure project alignment with organizational goals.",
      Skills:
        "Visual Basic, SQL, C, C++, Java, Windows NT/ 2000/ XP/ Vista, Linux, Unix, SQL Server, Microsoft (MS) Access, Oracle, JCL, SQL, DB2, MS Visio, MS Excel, MS FrontPage, and MS word",
      Certifications:
        "Project Management Professional (PMP), CompTIA Project+, Microsoft SQL Server Certification, Python Institute - Python 3 Certification, NTEN Cybersecurity Fundamentals, C++ Institute - C++ Certified Programmer",
      Achievements:
        "Dean’s List (3 semesters); Presidential Scholar (2 semesters), Women in Technology (August 2019 – May 2021); Intramural Soccer (Spring 2019)",
      LanguagesKnown: "English, Spanish, French",
      TechnologiesUsed:
        "Visual Basic, SQL, C, C++, Java, HTML, Flash, Unix Hp/Ux 11i, UNIX - SCO",
    },
    tablesData: {
      EducationDetails: [
        {
          Degree: "Bachelor of Science in Computer Science",
          InstitutionName: "Southeastern Louisiana University",
          GraduationYear: "May-2021",
          GPAMarksPercent: "3.84/4.00",
        },
      ],
      WorkExperience: [
        {
          CompanyName: "SLU Computer Science Department",
          Role: "Blog Writer",
          StartYear: "2020",
          EndYear: "Present",
          DescriptionResponsibility: "Designed a student portal website",
        },
      ],
    },
    meta_data: {
      page_width: 595.0,
      page_height: 842.0,
    },
  },
  HR_Job_Description: {
    file: HR_Job_Description,
    highlightCoordinates: [
      {
        page: 1,
        x1: 40,
        y1: 80,
        x2: 300,
        y2: 100,
      },
      {
        page: 1,
        x1: 40,
        y1: 120,
        x2: 300,
        y2: 140,
      },
      {
        page: 1,
        x1: 40,
        y1: 160,
        x2: 300,
        y2: 180,
      },
    ],
    currentValue: {
      JobTitle: "Software Developer",
      Department: "Information Technology",
      JobSummary:
        "Responsible for developing, testing, and maintaining software applications according to company standards.",
      JobResponsibilities:
        "Develop software solutions by studying information needs; conferring with users; studying systems flow, data usage, and work processes.",
      RequiredQualifications:
        "Bachelor’s degree in Computer Science or related field. 2+ years of software development experience.",
      PreferredQualifications:
        "Experience with Java, Python, and SQL. Strong problem-solving skills.",
    },
    tablesData: {
      JobResponsibilitiesTable: [
        {
          Responsibility:
            "Develop software solutions by studying information needs; conferring with users; studying systems flow, data usage, and work processes.",
        },
        {
          Responsibility:
            "Investigate problem areas and follow the software development lifecycle.",
        },
      ],
      RequiredQualificationsTable: [
        {
          Qualification:
            "Bachelor’s degree in Computer Science or related field.",
        },
        {
          Qualification: "2+ years of software development experience.",
        },
      ],
      PreferredQualificationsTable: [
        {
          Qualification: "Experience with Java, Python, and SQL.",
        },
        {
          Qualification: "Strong problem-solving skills.",
        },
      ],
    },
    meta_data: {
      page_width: 595.0,
      page_height: 842.0,
    },
  },
  Medical_Insurance_Medical_Invoice: {
    file: Medical_Insurance_Medical_Invoice,
    highlightCoordinates: [
      {
        page: 1,
        x1: 72,
        y1: 53,
        x2: 140,
        y2: 65,
      },
      {
        page: 1,
        x1: 72,
        y1: 89,
        x2: 140,
        y2: 101,
      },
      {
        page: 1,
        x1: 72,
        y1: 125,
        x2: 140,
        y2: 137,
      },
      {
        page: 1,
        x1: 72,
        y1: 161,
        x2: 140,
        y2: 173,
      },
      {
        page: 1,
        x1: 72,
        y1: 197,
        x2: 140,
        y2: 209,
      },
    ],
    currentValue: {
      PatientName: "John Doe",
      PatientID: "**********",
      ProviderName: "Health Clinic",
      InvoiceNumber: "INV-001234",
      InvoiceDate: "03/25/2024",
      DueDate: "04/25/2024",
      TotalAmount: "$200.00",
    },
    tablesData: {
      LineItemTable: [
        {
          ServiceDescription: "Consultation",
          ServiceDate: "03/20/2024",
          ServiceCode: "C001",
          ServiceQuantity: "1",
          ServiceRate: "$150.00",
          ServiceAmount: "$150.00",
        },
        {
          ServiceDescription: "X-Ray",
          ServiceDate: "03/20/2024",
          ServiceCode: "X002",
          ServiceQuantity: "1",
          ServiceRate: "$50.00",
          ServiceAmount: "$50.00",
        },
      ],
    },
    meta_data: {
      page_width: 595.0,
      page_height: 842.0,
    },
  },
  Medical_Insurance_Medical_Record: {
    file: Medical_Insurance_Medical_Record,
    highlightCoordinates: [
      {
        page: 1,
        x1: 50,
        y1: 50,
        x2: 100,
        y2: 60,
      },
      {
        page: 1,
        x1: 50,
        y1: 80,
        x2: 100,
        y2: 90,
      },
    ],
    currentValue: {
      PatientName: "John Doe",
      PatientID: "**********",
      DateOfBirth: "01/01/1980",
      Gender: "Male",
      ProviderName: "Health Clinic",
      VisitDate: "03/20/2024",
      Diagnosis: "Hypertension",
      TreatmentPlan: "Medication: Lisinopril 10mg daily. Follow-up in 1 month.",
    },
    tablesData: {
      VitalSignsTable: [
        {
          Date: "03/20/2024",
          Time: "10:00 AM",
          BloodPressure: "140/90",
          HeartRate: "80",
          RespiratoryRate: "16",
          Temperature: "98.6 F",
        },
      ],
      MedicationTable: [
        {
          MedicationName: "Lisinopril",
          Dosage: "10mg",
          Frequency: "Once daily",
        },
      ],
    },
    meta_data: {
      page_width: 595.0,
      page_height: 842.0,
    },
  },
  Medical_Insurance_Reconsideration: {
    file: Medical_Insurance_Reconsideration,
    highlightCoordinates: [
      {
        page: 1,
        x1: 60,
        y1: 100,
        x2: 150,
        y2: 110,
      },
      {
        page: 1,
        x1: 60,
        y1: 130,
        x2: 150,
        y2: 140,
      },
      {
        page: 1,
        x1: 60,
        y1: 160,
        x2: 150,
        y2: 170,
      },
    ],
    currentValue: {
      PatientName: "John Doe",
      PatientID: "**********",
      ProviderName: "Health Clinic",
      DateOfService: "03/20/2024",
      ClaimNumber: "CLM-001234",
      DeniedServiceDescription: "Consultation",
      DeniedServiceCode: "C001",
      ReasonForDenial: "Service not covered by insurance policy.",
      RequestedAction: "Reconsideration of claim based on medical necessity.",
    },
    tablesData: {
      ReconsiderationDetailsTable: [
        {
          Detail:
            "Request for reconsideration due to the medical necessity of the consultation service provided on 03/20/2024.",
        },
      ],
    },
    meta_data: {
      page_width: 595.0,
      page_height: 842.0,
    },
  },
  Medical_Insurance_EOR: {
    file: Medical_Insurance_EOR,
    highlightCoordinates: [
      {
        page: 1,
        x1: 1115,
        y1: 342,
        x2: 1248,
        y2: 361,
      },
      {
        page: 2,
        x1: 291,
        y1: 377,
        x2: 414,
        y2: 395,
      },
      {
        page: 1,
        x1: 1110,
        y1: 408,
        x2: 1234,
        y2: 427,
      },
    ],
    currentValue: {
      PatientName: "YANG, RONG",
      PatientID: "**********",
      ProviderName: "Health Clinic",
      DateOfService: "01/23/2023",
      ClaimNumber: "CLM-001234",
      DeniedServiceDescription: "Consultation",
      DeniedServiceCode: "C001",
      ReasonForDenial: "Service not covered by insurance policy.",
      RequestedAction: "Reconsideration of claim based on medical necessity.",
    },
    tablesData: {
      ReconsiderationDetailsTable: [
        {
          Detail:
            "Request for reconsideration due to the medical necessity of the consultation service provided on 01/25/23.",
        },
      ],
    },
    meta_data: {
      page_width: 1715.0,
      page_height: 2331.0,
    },
  },
};
