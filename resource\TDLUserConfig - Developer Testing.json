{"2": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "Parag Traders", "dataDirectory": "H:\\AI Data\\DailyData\\ParagTraders", "userId": 2, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3}, "4": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "ICD", "dataDirectory": "H:\\AI Data\\DailyData\\DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3}, "5": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf", "xlsx"], "customerName": "ICD", "dataDirectory": "H:\\AI Data\\DailyData\\ICD", "userId": 5, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3}, "12": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "Prem Textiles", "dataDirectory": "H:\\AI Data\\DailyData\\AV_DEV", "userId": 12, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3}}