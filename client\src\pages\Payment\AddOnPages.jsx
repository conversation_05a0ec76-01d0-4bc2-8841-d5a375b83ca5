import { useEffect, useState } from 'react';
import { jwtDecode } from "jwt-decode";
import axios from 'axios';
import toast, { Toaster } from "react-hot-toast";
import PropTypes from 'prop-types';
import { ModelComponent } from '../../components/App'; // Import the ModelComponent
import { ActivePlanNameAtom, ActivePlanTypeAtom } from '../../context/TrailUsageUserData';
import { useRecoilState } from 'recoil';
import { useUserNameSetter } from "../../context/userData";
import addOnPage from '../../assets/SVGs/addOnPage.svg';
import fetchData from '../../utils/fetchData';
const AddOnPages = ({ showAlert }) => {
    const [ActivePlanName, setActivePlanName] = useRecoilState(ActivePlanNameAtom);
    const [ActivePlanType, setActivePlanType] = useRecoilState(ActivePlanTypeAtom);
    const [currentToastId, setCurrentToastId] = useState(null);
    const setUserName = useUserNameSetter()
    const [pageLimitLeft] = useState(() => {
        const cookieValue = getCookie("pageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalPageLimitLeft] = useState(() => {
        const cookieValue = getCookie("totalPageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [freePageLimitUsage] = useState(() => {
        const cookieValue = getCookie("freePageLimitUsage");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalFreePageLimit] = useState(() => {
        const cookieValue = getCookie("totalFreePageLimit");
        return cookieValue !== undefined ? cookieValue : undefined;
    });


    // const pricePerPage = 0.27;
    // const totalPrice = (pages * pricePerPage).toFixed(2);
    // const totalPrice = 2
    const stripe_livemode = `${import.meta.env.VITE_STRIPE_LIVEMODE}`
    let topUpPlans;
    if (stripe_livemode.toLowerCase() == "true") {
        topUpPlans = {
            "top_up": {
                "monthly": [
                    {
                        "Starter": {
                            "ProductID": "prod_QWnxFG4C89Dzc1",
                            "PriceID": "price_1PgoPtD9i0KVpTcafEqozDBB",
                            "description": "Price per page for the starter monthly plan",
                            "currency": "usd",
                            "amount": 0.35,
                            "pageCount": 1,
                            "minpages": 30,
                            "maxpages": 290
                        }
                    },
                    {
                        "Professional": {
                            "ProductID": "prod_QWnzow5dZX39pm",
                            "PriceID": "price_1Pgp52D9i0KVpTcaGZaN4yim",
                            "description": "Price per page for the Professional monthly plan",
                            "currency": "usd",
                            "amount": 0.26,
                            "pageCount": 1,
                            "minpages": 385,
                            "maxpages": 3850
                        }
                    }
                ],
                "yearly": [
                    {
                        "Starter": {
                            "ProductID": "prod_QWnxFG4C89Dzc1",
                            "PriceID": "price_1PgoRXD9i0KVpTcaxASOmh8S",
                            "description": "Price per page for the Professional yearly plan",
                            "currency": "usd",
                            "amount": 0.27,
                            "pageCount": 1,
                            "minpages": 30,
                            "maxpages": 290
                        }
                    },
                    {
                        "Professional": {
                            "ProductID": "prod_QWnzow5dZX39pm",
                            "PriceID": "price_1Pgp5gD9i0KVpTca8BqKFDNX",
                            "description": "Price per page for the Professional yearly plan",
                            "currency": "usd",
                            "amount": 0.20,
                            "pageCount": 1,
                            "minpages": 385,
                            "maxpages": 3850
                        }
                    }
                ]
            }
        };
    }
    else {
        topUpPlans = {
            "top_up": {
                "monthly": [
                    {
                        "Starter": {
                            "ProductID": "prod_QWnxFG4C89Dzc1",
                            "PriceID": "price_1PgoPtD9i0KVpTcafEqozDBB",
                            "description": "Price per page for the starter monthly plan",
                            "currency": "usd",
                            "amount": 0.35,
                            "pageCount": 1,
                            "minpages": 30,
                            "maxpages": 290
                        }
                    },
                    {
                        "Professional": {
                            "ProductID": "prod_QWnzow5dZX39pm",
                            "PriceID": "price_1Pgp52D9i0KVpTcaGZaN4yim",
                            "description": "Price per page for the Professional monthly plan",
                            "currency": "usd",
                            "amount": 0.26,
                            "pageCount": 1,
                            "minpages": 385,
                            "maxpages": 3850
                        }
                    }
                ],
                "yearly": [
                    {
                        "Starter": {
                            "ProductID": "prod_QWnxFG4C89Dzc1",
                            "PriceID": "price_1PgoRXD9i0KVpTcaxASOmh8S",
                            "description": "Price per page for the Professional yearly plan",
                            "currency": "usd",
                            "amount": 0.27,
                            "pageCount": 1,
                            "minpages": 30,
                            "maxpages": 290
                        }
                    },
                    {
                        "Professional": {
                            "ProductID": "prod_QWnzow5dZX39pm",
                            "PriceID": "price_1Pgp5gD9i0KVpTca8BqKFDNX",
                            "description": "Price per page for the Professional yearly plan",
                            "currency": "usd",
                            "amount": 0.20,
                            "pageCount": 1,
                            "minpages": 385,
                            "maxpages": 3850
                        }
                    }
                ]
            }
        };

    }
    const minPages = getPlanMinPages();
    const maxPages = getPlanMaxPages();
    const [pages, setPages] = useState(minPages || 0);

    useEffect(() => {
        if (minPages !== null) {
            setPages(minPages);
        }
    }, [minPages]);

    const decodedToken = jwtDecode(localStorage.getItem('token'));
    const userId = decodedToken.id;
    const pricePerPage = getPricePerPage();
    const totalPrice = (pages * pricePerPage).toFixed(2);
    function getPricePerPage() {
        const plan = ActivePlanName; // fetch from user DB table
        const isAnnual = ActivePlanType ? ActivePlanType.toLowerCase() === "yearly" : false; // default to false if null
        const planType = isAnnual ? 'yearly' : 'monthly';
        const selectedPlan = topUpPlans["top_up"][planType].find(p => Object.keys(p)[0] === `${plan}`);
        if (selectedPlan) {
            return selectedPlan[plan].amount;
        } else {
            return 0.35; // or handle error appropriately
        }
    };
    function getPlanMinPages() {
        const plan = ActivePlanName; // fetch from user DB table
        const isAnnual = ActivePlanType ? ActivePlanType.toLowerCase() === "yearly" : false; // default to false if null
        const planType = isAnnual ? 'yearly' : 'monthly';
        const selectedPlan = topUpPlans["top_up"][planType].find(p => Object.keys(p)[0] === plan);

        if (selectedPlan) {

            return selectedPlan[plan].minpages;
        } else {
            return 30; // or handle error appropriately
        }
    }
    function getPlanMaxPages() {
        const plan = ActivePlanName; // fetch from user DB table
        const isAnnual = ActivePlanType ? ActivePlanType.toLowerCase() === "yearly" : false; // default to false if null
        const planType = isAnnual ? 'yearly' : 'monthly';
        const selectedPlan = topUpPlans["top_up"][planType].find(p => Object.keys(p)[0] === plan);

        if (selectedPlan) {
            return selectedPlan[plan].maxpages;
        } else {
            return 290; // or handle error appropriately
        }
    }

    useEffect(() => {
        const fetchUserData = async () => {
            try {
                const user = await fetchData(userId)
                const active_plan = user.active_plan;
                setActivePlanName(active_plan)
                setActivePlanType(user.active_plan_type)
                const userName = user.name;
                setUserName(userName);
            } catch (error) {
                if (error.response && error.response.data && error.response.data.detail) {
                    toast.error(error.response.data.detail);
                } else {
                    toast.error("An unexpected error occurred"); // Fallback message
                }
                console.error('Error fetching user data:', error);
            }
        };
        fetchUserData();
    }, [userId]);

    function getCookie(name) {
        const value = `; ${document.cookie}`;
        const parts = value.split(`; ${name}=`);
        if (parts.length === 2) return parts.pop().split(';').shift();
    }

    const handlePlanSelection = async (plan, isAnnual) => {
        const planType = isAnnual ? 'yearly' : 'monthly';
        const selectedPlan = topUpPlans["top_up"][planType].find(p => Object.keys(p)[0] === `${plan}`);
        const priceID = selectedPlan ? selectedPlan[`${plan}`].PriceID : null;

        if (priceID && pages) {
            try {
                const response = await axios.post(`${import.meta.env.VITE_SERVER}/create-checkout-session`, {
                    price_id: priceID,
                    quantity: pages,
                    mode: 'payment'
                }, {
                    headers: {
                        "Authorization": `Bearer ${localStorage.getItem('token')}`
                    }
                });
                if (response.data) {
                    console.log('Checkout session created:', response.data);
                    window.location.href = response.data.url;
                }
            } catch (error) {
                console.error("Oops! We couldn't create the checkout session right now. Please try again later.", error);
            }
        } else {
            console.error('Price ID not found for the selected plan and billing period.');
        }
    };

    const handleBuyNow = () => {
        const planName = ActivePlanName; // fetch from user DB table
        const isAnnual = ActivePlanType ? ActivePlanType.toLowerCase() === "yearly" : false; // default to false if null
        if (planName.toLowerCase() == "free") { // Assuming isPremiumUser is a flag that indicates if the user is a premium subscriber
            // Display the toast message
            if (currentToastId !== null) {
                toast.dismiss(currentToastId);
            }
            setCurrentToastId(toast.error(
                <div>
                    Top up plan is only available to our premium subscribed users. Please click
                    <a href="/upgradeplan" style={{ color: '#f5a623', textDecoration: 'underline', marginLeft: '5px' }}>
                        Upgrade
                    </a> {' '}
                    to subscribe.
                </div>,
                {
                    type: 'error',
                    icon: '⚠️',
                    duration: 6000
                }
            ))
        } else {
            handlePlanSelection(planName, isAnnual);
        }
    };

    return (
        <div style={{ overflow: 'hidden', fontFamily: 'Inter, sans-serif' }} className={`${showAlert ? 'max-h-[95vh]' : 'max-h-[98vh]'}`}>
            <Toaster position="top-center"></Toaster>
            <div className="flex pl-[1.5rem] py-2 pt-4">
                <div className="flex-grow">
                    <h1 className="text-3xl font-semibold xl:text-2xl text-[#3F3F3F]">Buy Pages</h1>
                </div>
                <ModelComponent
                    initialPageLimitLeft={pageLimitLeft}
                    initialTotalPageLimitLeft={totalPageLimitLeft}
                    initialFreePageLimitUsage={freePageLimitUsage}
                    initialTotalFreePageLimit={totalFreePageLimit}
                />
            </div>
            <div className="bg-white p-8 rounded-lg shadow-md max-w-full mx-auto mr-5 ml-5 mt-10" style={{ minHeight: '700px' }}>
                <h2 className="text-5xl font-bold mb-4 mt-8 text-center" style={{ color: '#003654', fontWeight: 700 }}>Buy Pages</h2>
                <p className="text-gray-600 mb-6 text-center">Personalize your experience by adding pages tailored to your specific requirements.</p>

                {/* <div className="bg-[#003654] p-4 rounded-2xl mx-auto mt-10" style={{ width: '630px' }}> */}
                <div className="w-full bg-[#003654] p-4 rounded-xl mx-auto mt-10 md" style={{ width: '715px' }}>
                    <div className="flex justify-between items-center text-white">
                        <div className="flex flex-col">
                            <span className="text-lg">Your Current Plan</span>
                            <span className="font-bold text-2xl" style={{ color: 'white' }}>{ActivePlanName}</span>
                        </div>
                        <div className="flex flex-col items-end">
                            <span className="text-lg">Price Per Page</span>
                            <span className="font-bold text-2xl">${pricePerPage.toFixed(2)}</span>
                        </div>
                    </div>
                </div>
                <div className="w-full mx-auto mt-10 flex flex-col items-center justify-center mb-20" style={{ width: '680px', position: 'relative', }}>
                    <span className="text-xl text-[#003654] text-center font-medium">Number of pages needed</span>
                    <input
                        type="range"
                        min={minPages}
                        max={maxPages}
                        step="5"
                        value={pages}
                        onChange={(e) => setPages(e.target.value)}
                        className="w-full mt-4 h-2.5 rounded-lg cursor-pointer bg-gray-200"
                        style={{
                            border: '1px solid #EFF0F6',
                            background: `linear-gradient(to right, #003654 0%, #003654 ${(pages - minPages) / (maxPages - minPages) * 100}%, transparent ${(pages - minPages) / (maxPages - minPages) * 100}%, transparent 100%)`,
                            borderRadius: '10px',
                            outline: 'none',
                            cursor: 'pointer',
                            appearance: 'none'
                        }}
                    />
                    <div
                        style={{
                            position: 'absolute',
                            top: '60px', // Position below the slider
                            left: `calc(${(pages - minPages) / (maxPages - minPages) * 100}% - 25px)`, // Align with the slider thumb
                            backgroundColor: '#003654',
                            transform: 'translateX(-50%)',
                            color: 'white',
                            padding: '5px 10px',
                            borderRadius: '20px',
                            minWidth: '50px',
                            textAlign: 'center'
                        }}
                    >
                        {pages}
                    </div>
                </div>
                {/* </div> */}
                <div className="flex flex-col items-center justify-center mr-5 mt-10" style={{ position: 'relative' }}> {/* 5% width */}
                    <p
                        className="text-center text-gray-500 mb-3 text-lg"
                        style={{ fontFamily: 'Inter, sans-serif', fontStyle: 'italic', fontWeight: 400, lineHeight: '20px' }}
                    >
                        Total Price = No. of Pages X ${pricePerPage.toFixed(2)}
                    </p>
                    <span className="text-[#003654] font-bold text-5xl mb-15" style={{ fontFamily: 'Inter, sans-serif' }}>${totalPrice}</span>
                    {/* <button className="bg-[#003654] text-white font-medium py-2 px-10 rounded-md hover:bg-[#002d4a] transition" style={{fontFamily: 'Inter, sans-serif',position: 'absolute',width: '300px',height: '65px',fontSize: '30px' ,top: '115px'}} onClick={handleBuyNow}>Buy Now
                        </button> */}
                    {/* rounded-2xl */}
                    <button className="bg-[#003654] text-white font-medium py-2 px-10 rounded-xl hover:bg-[#002d4a] transition flex items-center justify-between text-3xl mt-8" style={{ fontFamily: 'Inter, sans-serif' }} onClick={handleBuyNow}>
                        <img src={addOnPage} />
                        <span className="ml-2">Buy Now</span>
                    </button>
                </div>
            </div>
        </div>
    );

};

export default AddOnPages;

AddOnPages.propTypes = {
    showAlert: PropTypes.bool.isRequired,
};