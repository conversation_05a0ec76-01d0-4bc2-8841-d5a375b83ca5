import sys
sys.path.append(".")

from datetime import datetime, date
from fastapi import HTTP<PERSON>x<PERSON>
from sqlalchemy.future import select
from typing import Optional, Dict, Any
from sqlalchemy.exc import SQLAlchemyError
import os
import pandas as pd
# Database imports
from config.db_config import AsyncSessionLocal
from src.Models.models import AVRequestDetail, UploadedDoc
import socket
import traceback
# Logging
from src.Controllers.Logs_Controller import CLogController
from src.utilities.helperFunc import Date<PERSON><PERSON><PERSON>, CommonHelper
from sqlalchemy import func
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode
from sqlalchemy import and_, or_
from sqlalchemy import outerjoin

from fastapi import APIRouter, HTTPException, Depends,  status
# Initialize FastAPI router
AVRequetDetailTableRouter = APIRouter(tags=['Batch API'], prefix="/api")
DEVICE_NAME = socket.gethostname()  # Fetch the device name



class CAVRequestDetail:
    """
    Purpose:
        This class handles all database operations related to the `AVRequestDetail` table.
        It provides functionality to update and retrieve AV request records using defined
        combinations of primary identifiers such as `strClientREQID` with `DocID` or `ReqDocHashCode`.
    """

    @staticmethod
    async def create_av_request_record(dictData: dict, userId: int):
        """
        Adds a new AVRequestDetail record to the database.
        """
        async with AsyncSessionLocal() as db:
            try:
                objNewRequest = AVRequestDetail(**dictData)
                db.add(objNewRequest)
                await db.commit()
                await db.refresh(objNewRequest)

                await CLogController.MSWriteLog(userId, "Info", f"AV Request for ClientREQID: {dictData.get('strClientREQID')} saved successfully.")

                return {
                    "APIStatusCode": 200,
                    "detail": "Request record saved successfully",
                    "RequestID": objNewRequest.ID
                }
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", "Failed to save AV Request record to DB")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="Unhandled error occurred.")

    @staticmethod
    async def MSGetAVRequestByUserID(
        user_id: int,
        start_date: Optional[date] = None,
        end_date: Optional[date] = None,
        strVoucherType: Optional[str] = None,
        tallySuccessFullyPunchIn: Optional[bool] = None,
        AVTallySuccessStatus: Optional[str] = None,
        strSystemUserName: Optional[str] = None
    ):
        async with AsyncSessionLocal() as db:
            try:
                filters = [AVRequestDetail.User_UID == user_id]

                # Handle date filtering logic
                if start_date:
                    if not end_date:
                        end_date = date.today()
                    filters.append(AVRequestDetail.RecievedDate.between(start_date, end_date))

                # VoucherType filter
                if strVoucherType:
                    filters.append(AVRequestDetail.strVoucherType == strVoucherType)

                # XML Success Code check
                if tallySuccessFullyPunchIn is True:
                    filters.append(AVRequestDetail.ClientImportedXMLStatusCode == '200')

                # AVXMLGeneratedStatus filter
                if AVTallySuccessStatus:
                    filters.append(AVRequestDetail.AVXMLGeneratedStatus == AVTallySuccessStatus)

                # System Username filter
                if strSystemUserName:
                    filters.append(AVRequestDetail.strSystemUserName == strSystemUserName)

                # Compose the query
                stmt = select(AVRequestDetail).where(and_(*filters))
                result = await db.execute(stmt)
                records = result.scalars().all()

                return {
                    "APIStatusCode": 200,
                    "count": len(records),
                    "data": [
                        {k: v for k, v in record.__dict__.items() if k not in {"_sa_instance_state", "strXMLResponse", "FileContent","TracebackLogs"}}
                        for record in records
                    ]
                }

            except Exception as e:
                raise HTTPException(status_code=500, detail=f"Error retrieving records: {str(e)}")
            
    @staticmethod
    async def MSUpdateRecord(iUserId: int, strClientREQID: str, DocID: int = None, ReqDocHashCode: str = None, **kwargs):
        """
        Input:

            1) iUserId: int
            The ID of the user performing the update operation.

            2) strClientREQID: str
            The unique client request ID that identifies the AV request record.

            3) DocID: int, optional
            The document ID. Used as part of combination1: (strClientREQID, DocID).

            4) ReqDocHashCode: str, optional
            The document hash code. Used as part of combination2: (strClientREQID, ReqDocHashCode).

            5) kwargs: dict
            Dictionary of fields to update with their new values.

        Output:

            dict: Status of the update operation, success boolean, and message.

        Purpose:

            To update an existing AV request record using either:
            - Combination 1: (strClientREQID, DocID)
            - Combination 2: (strClientREQID, ReqDocHashCode)
            Updates only fields passed via `kwargs`.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(AVRequestDetail).where(
                    AVRequestDetail.strClientREQID == strClientREQID
                )
                if DocID is not None:
                    query = query.where(AVRequestDetail.DOC_UID == DocID)
                elif ReqDocHashCode is not None:
                    query = query.where(AVRequestDetail.ReqDocHashCode == ReqDocHashCode)
                else:
                    raise HTTPException(status_code=400, detail="Either DocID or ReqDocHashCode is required.")

                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found for the provided identifiers.")

                for key, value in kwargs.items():
                    if hasattr(record, key):
                        setattr(record, key, value)

                await db.commit()
                return {"success": True, "message": "Record updated successfully."}

            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to update AVRequestDetail: {str(e)}")
                raise HTTPException(status_code=500, detail="Error updating record.")

        
    
    @staticmethod
    async def MSUpdateDeliveryNoteRecord(
        iUserId: int,
        strClientREQID: str,
        ReqDocName: str = None,
        ReqDocHashCode: str = None,
        **kwargs
    ):
        """
        Update an AVRequestDetail record by (strClientREQID, ReqDocName) 
        or, if provided, by (strClientREQID, ReqDocHashCode).

        For Delivery Note

        Inputs:
            iUserId          – the user performing the update
            strClientREQID   – client’s unique request ID
            ReqDocName       – the document’s name (new lookup key)
            ReqDocHashCode   – optional hash code fallback
            kwargs           – fields to update on the record

        Returns:
            {"success": True, "message": "..."} on success;
            raises HTTPException on error.
        """
        async with AsyncSessionLocal() as db:
            try:
                # base filter on client request ID
                query = select(AVRequestDetail).where(
                    AVRequestDetail.strClientREQID == strClientREQID
                )

                # prefer ReqDocName lookup
                if ReqDocName is not None:
                    query = query.where(AVRequestDetail.ReqDocName == ReqDocName)
                # fallback on hash code if name not given
                elif ReqDocHashCode is not None:
                    query = query.where(AVRequestDetail.ReqDocHashCode == ReqDocHashCode)
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="Either ReqDocName or ReqDocHashCode must be provided."
                    )

                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(
                        status_code=404,
                        detail="No record found for the given identifiers."
                    )

                # apply updates only for valid attributes
                for key, value in kwargs.items():
                    if hasattr(record, key):
                        setattr(record, key, value)

                await db.commit()
                return {"success": True, "message": "Record updated successfully."}

            except HTTPException:
                # re-raise known HTTP errors
                raise
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(
                    iUserId,
                    "Error",
                    f"MSUpdateDeliveryNoteRecord failed: {e}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Internal error updating record."
                )

    @staticmethod
    async def MSGetReqDocNameByClientREQID(strClientREQID: str):
        """
        Retrieves the ReqDocName for a given strClientREQID.

        Input:
            strClientREQID – client’s unique request ID

        Returns:
            {"success": True, "ReqDocName": "..."} on success;
            raises HTTPException if no record found or on error.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(AVRequestDetail.ReqDocName).where(
                    AVRequestDetail.strClientREQID == strClientREQID
                )

                result = await db.execute(query)
                req_doc_name = result.scalar()

                if not req_doc_name:
                    raise HTTPException(
                        status_code=404,
                        detail="No document name found for the given request ID."
                    )

                return {"success": True, "ReqDocName": req_doc_name}

            except Exception as e:
                await CLogController.MSWriteLog(
                    None,
                    "Error",
                    f"MSGetReqDocNameByClientREQID failed: {e}"
                )
                raise HTTPException(
                    status_code=500,
                    detail="Internal error retrieving document name."
                )


    @staticmethod
    async def MSGetRecord(iUserId: int, strClientREQID: str, DocID: int = None, ReqDocHashCode: str = None):
        """
        Input:

            1) iUserId: int
            The ID of the user requesting the record.

            2) strClientREQID: str
            The unique client request ID that identifies the AV request record.

            3) DocID: int, optional
            The document ID. Used in combination1: (strClientREQID, DocID).

            4) ReqDocHashCode: str, optional
            The document hash code. Used in combination2: (strClientREQID, ReqDocHashCode).

        Output:

            dict: A dictionary with the record data or error message if not found.

        Purpose:

            To retrieve an AV request record using either:
            - Combination 1: (strClientREQID, DocID)
            - Combination 2: (strClientREQID, ReqDocHashCode)
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(AVRequestDetail).where(
                    AVRequestDetail.strClientREQID == strClientREQID
                )
                if DocID is not None:
                    query = query.where(AVRequestDetail.DOC_UID == DocID)
                elif ReqDocHashCode is not None:
                    query = query.where(AVRequestDetail.ReqDocHashCode == ReqDocHashCode)
                else:
                    raise HTTPException(status_code=400, detail="Either DocID or ReqDocHashCode is required.")

                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(status_code=404, detail="Record not found.")

                return {
                    "success": True,
                    "data": {column.name: getattr(record, column.name) for column in record.__table__.columns}
                }

            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to fetch AVRequestDetail: {str(e)}")
                raise HTTPException(status_code=500, detail="Error fetching record.")

    @staticmethod
    async def MSGetStatistics(iUserId: int, strClientREQID: str):
        """
        Returns statistics such as total pages processed, total time saved, and total
        estimated time saved based on the provided `strClientREQID` and `iUserId`.
        """
        async with AsyncSessionLocal() as db:
            # Initialize the statistics variables
            total_pages_processed = 0
            total_time_saved_str = "~0 min 0 secs"
            total_time_saved_min = 0
            try:
                # Filter records based on `strClientREQID`, `iUserId` and `AVXMLGeneratedStatus`
                stmt = select(AVRequestDetail).filter(
                    AVRequestDetail.strClientREQID == strClientREQID,
                    AVRequestDetail.User_UID == iUserId,
                    AVRequestDetail.AVXMLGeneratedStatus.in_(['Success', 'PartialSuccess', 'ValidationError'])
                )
                result = await db.execute(stmt)
                records = result.scalars().all()

                # Get all document IDs from the records
                doc_ids = [record.DOC_UID for record in records if record.DOC_UID is not None]

                # If there are document IDs, fetch their page counts
                if doc_ids:
                    # Query to get page counts for the document IDs
                    page_count_stmt = select(UploadedDoc.DocId, UploadedDoc.PageCount).filter(
                        UploadedDoc.DocId.in_(doc_ids)
                    )
                    page_count_result = await db.execute(page_count_stmt)
                    page_counts = {doc_id: page_count for doc_id, page_count in page_count_result}

                    # Sum up the page counts
                    total_pages_processed = sum(page_counts.values())

                # Loop through the records and calculate the total time saved
                lsEstimatedCurrentRequestTimeSaved = []
                for record in records:
                    # Estimated Time
                    estimated_time = record.EstAccountantTimeSaved
                    if estimated_time and estimated_time != "NOT_APPLICABLE":
                        lsEstimatedCurrentRequestTimeSaved.append(estimated_time)

                if lsEstimatedCurrentRequestTimeSaved:
                    total_time_saved_str, total_time_saved_min = CommonHelper.MSGetTotalTimeSaved(lsEstimatedCurrentRequestTimeSaved)

                # Return the statistics
                return {
                    "iTotalTimeSavedMin": total_time_saved_min,
                    "strTotalTimeSavedMin": total_time_saved_str,
                    "iTotalPagesProcessed": total_pages_processed
                }

            except Exception as e:
                await CLogController.MSWriteLog(iUserId, "Error", f"Error occurred while fetching statistics: {str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise HTTPException(status_code=500, detail="Error occurred while fetching statistics.")

    @staticmethod
    async def MSInsertAVRequestDetailsFromStoredDocs(
        dictStoreDocRes: dict,
        strClientREQID: str,
        iUserId: int,
        strVoucherType: str,
        bTestMode: bool = False,
        dictExeUserDetails: dict = {},
        bScanForVirus: bool = True,
        bIsMultivendorDoc: bool = False,
        strSystemName: str = "-",
        User_UID: int = None
    ):
        """
        Input:

            1) dictStoreDocRes: dict
            Output from MSStoreDocInNetLocation containing stored document metadata.

            2) strClientREQID: str
            Client request ID to be logged against the request.

            3) iUserId: int
            The ID of the user making the request.

            4) strVoucherType: str
            Voucher type associated with the stored documents.

            5) Other parameters: Contextual info for audit and tracking (Test mode, System name, etc.)

        Output:

            dict: A dictionary with the inserted request count and a list of inserted record IDs.

        Purpose:

            Inserts one `AVRequestDetail` entry per document in `Docs` of `dictStoreDocRes`.
            Leverages metadata like vendor name, file name, and additional parameters for record creation.
        """
        try:
            inserted_ids: list[int] = []
            dtGenerated = dictExeUserDetails.get("ClientReqGeneratedAt", datetime.now())
            strCustName = dictExeUserDetails.get("CustomerName", "Unknown")

            for doc in dictStoreDocRes.get("Docs", []):
                # Build the **kwargs bag for the helper
                kw  = {
                    "NetworkLocation": {"path": getattr(doc, "filePath", "")},
                    "strVoucherType": strVoucherType,
                    "ReqDocType": getattr(doc, "FileType", "pdf"),
                    "ReqDocName": getattr(doc, "filename", ""),
                    "ReqDocHashCode": getattr(doc, "HashCode", ""),
                    "MultipleVendorEnabled": bIsMultivendorDoc,
                    "UServerName": DEVICE_NAME,
                    "strSystemUserName": strSystemName,
                    "PriceListVerification": False,
                    "strAccuVelocityComments": "-",
                    "TracebackLogs": "",
                    "strServerEstProcessingTime": "NOT_APPLICABLE",
                    "AVXMLGeneratedStatus": "Skipped", # Please note use NOT_APPLICABLE when Multiple Records in case of single Row from this enum 'Success', 'Skipped', 'PartialSuccess', 'NOT_APPLICABLE'
                    "IsRecordUIDPresent": False, # True when any ID Foreign key
                    "EstAccountantTimeSaved": "NOT_APPLICABLE",
                    "ClientImportedXMLStatusCode":999, # Default ClientImportedXMLStatusCode Value
                    "bTestMode": bTestMode
                }
                obj = await CAVRequestDetail.MSInsertAVRecordDetail(
                    strClientREQID=strClientREQID,
                    dtObjGeneratedTimeAt=dtGenerated,
                    iUserId=iUserId,
                    strRecievedDate=date.today(),
                    strCustomerName=strCustName,
                    **kw
                )

                inserted_ids.append(obj.ID)
            await CLogController.MSWriteLog(
                iUserId,
                "INFO",
                f"[MSInsertAVRequestDetailsFromStoredDocs] "
                f"Inserted {len(inserted_ids)} rows for REQID={strClientREQID}"
            )
            return {
                "InsertedCount": len(inserted_ids),
                "InsertedIDs": inserted_ids
            }

        except Exception as exc:
            await CLogController.MSWriteLog(
                iUserId,
                "ERROR",
                f"[MSInsertAVRequestDetailsFromStoredDocs] failure → {exc}"
            )
            await CLogController.MSWriteLog(
                iUserId,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail="Error inserting AVRequestDetail records")

    @staticmethod
    async def MSInsertAVRequestDetailsFromBankStatementPaths(
        lsBankStatementPath: list,
        strClientREQID: str,
        iUserId: int,
        strVoucherType: str,
        bTestMode: bool = False,
        dictExeUserDetails: dict = {},
        bScanForVirus: bool = True,
        bIsMultivendorDoc: bool = False,
        strSystemName: str = "-",
        User_UID: int = None
    ):
        """
        Inserts AVRequestDetail entries from a list of bank statement file paths.

        Args:
            lsBankStatementPath (list): List of full file paths for bank statements.
            strClientREQID (str): Client request ID.
            iUserId (int): User ID performing the insertion.
            strVoucherType (str): Voucher type (typically BANK_STATEMENT).
            bTestMode (bool): Whether the operation is in test mode.
            dictExeUserDetails (dict): Execution context/user metadata.
            bScanForVirus (bool): If True, documents are scanned for viruses.
            bIsMultivendorDoc (bool): Flag for multi-vendor handling.
            strSystemName (str): Name of the system executing the request.
            User_UID (int): Redundant user ID for audit.

        Returns:
            dict: Contains the count and IDs of inserted records.
        """
        try:
            inserted_ids: list[int] = []
            dtGenerated = dictExeUserDetails.get("ClientReqGeneratedAt", datetime.now())
            strCustName = dictExeUserDetails.get("CustomerName", "Unknown")

            for file_path in lsBankStatementPath:
                # Extract file name and extension
                file_name = os.path.basename(file_path)
                file_ext = os.path.splitext(file_name)[1].lstrip(".") or "pdf"

                kw = {
                    "NetworkLocation": {"path": file_path},
                    "strVoucherType": strVoucherType,
                    "ReqDocType": file_ext,
                    "ReqDocName": file_name,
                    "ReqDocHashCode": "",
                    "MultipleVendorEnabled": bIsMultivendorDoc,
                    "UServerName": DEVICE_NAME,
                    "strSystemUserName": strSystemName,
                    "PriceListVerification": False,
                    "strAccuVelocityComments": "-",
                    "TracebackLogs": "",
                    "strServerEstProcessingTime": "NOT_APPLICABLE",
                    "AVXMLGeneratedStatus": "NOT_APPLICABLE",
                    "IsRecordUIDPresent": False,
                    "EstAccountantTimeSaved": "NOT_APPLICABLE",
                    "ClientImportedXMLStatusCode": "NOT_APPLICABLE",
                    "ClientImportedXMLResponse":"NOT_APPLICABLE",
                    "bTestMode": bTestMode
                }

                obj = await CAVRequestDetail.MSInsertAVRecordDetail(
                    strClientREQID=strClientREQID,
                    dtObjGeneratedTimeAt=dtGenerated,
                    iUserId=iUserId,
                    strRecievedDate=date.today(),
                    strCustomerName=strCustName,
                    **kw
                )

                inserted_ids.append(obj.ID)

            await CLogController.MSWriteLog(
                iUserId,
                "INFO",
                f"[MSInsertAVRequestDetailsFromBankStatementPaths] "
                f"Inserted {len(inserted_ids)} rows for REQID={strClientREQID}"
            )

            return {
                "InsertedCount": len(inserted_ids),
                "InsertedIDs": inserted_ids
            }

        except Exception as exc:
            await CLogController.MSWriteLog(
                iUserId,
                "ERROR",
                f"[MSInsertAVRequestDetailsFromBankStatementPaths] failure → {exc}"
            )
            await CLogController.MSWriteLog(
                iUserId,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail="Error inserting AVRequestDetail records from bank statements")


    @staticmethod
    async def MSUpdateRecordByClientREQID(
        strClientREQID: str = None,
        RequestID: int = None,
        **kwargs
    ):
        """
        Updates AVRequestDetail records using either RequestID (preferred) or strClientREQID.

        Args:
            RequestID (int, optional): Primary key ID of the request to update.
            strClientREQID (str, optional): Alternate key to identify the request.
            **kwargs: Fields and values to update.

        Returns:
            dict: {"success": True, "message": "..."} on success.

        Raises:
            HTTPException if no record is found or on internal error.
        """
        async with AsyncSessionLocal() as db:
            try:
                if RequestID is not None:
                    query = select(AVRequestDetail).where(AVRequestDetail.ID == RequestID)
                elif strClientREQID:
                    query = select(AVRequestDetail).where(AVRequestDetail.strClientREQID == strClientREQID)
                else:
                    raise HTTPException(
                        status_code=400,
                        detail="Either RequestID or strClientREQID must be provided."
                    )

                result = await db.execute(query)
                record = result.scalars().first()

                if not record:
                    raise HTTPException(
                        status_code=404,
                        detail=f"No record found for REQID='{RequestID}'  ,REQESTSTRING={strClientREQID} "
                    )

                for key, value in kwargs.items():
                    if hasattr(record, key):
                        setattr(record, key, value)

                await db.commit()
                return {"success": True, "message": "Record updated successfully."}

            except HTTPException:
                raise
            except Exception as e:
                await db.rollback()
            
                raise HTTPException(
                    status_code=500,
                    detail="Internal error updating record."
                )


    @staticmethod
    async def MSInsertAVRecordDetail(                       # noqa: N802  (keep name style)
        strClientREQID: str,
        dtObjGeneratedTimeAt:datetime,
        iUserId: int,
        strRecievedDate:str = None,
        strCustomerName:str = "AVDEVELOPER",
        **kwargs: Any
    ) -> AVRequestDetail:
        """
        Insert ONE row in AVRequestDetail.

        Parameters
        ----------
        iUserId : int
            UID from `users` table.
        strClientREQID : str
            External request-ID supplied by the client.
        dbSession : AsyncSession
            Injected SQLAlchemy async session (dependency-injected in FastAPI).
        **kwargs : Any
            Any extra model columns you want to override, e.g.:
            - ReqDocName="invoice_123.pdf"
            - ReqDocHashCode="abc123…"
            - NetworkLocation={"bucket": "...", "key": "..."}
            - AVXMLGeneratedStatus="Success"
            - etc.

        Returns
        -------
        AVRequestDetail
            The persisted ORM instance.
        """

        try:
            async with AsyncSessionLocal() as db:
                # -- mandatory timestamps -----------------------------------------------------
                dtNowUtc = datetime.now()

                objNew: AVRequestDetail = AVRequestDetail(
                    strClientREQID=strClientREQID,
                    User_UID=iUserId,
                    CReqGeneratedTimeAt=dtObjGeneratedTimeAt,
                    RecievedDate=date.today() if strRecievedDate is None else strRecievedDate,
                    strCustomerName=strCustomerName,
                    **kwargs                                             # everything else is optional
                )

                db.add(objNew)
                await db.commit()
                await db.refresh(objNew)

                await CLogController.MSWriteLog(
                    iUserId,
                    "INFO",
                    f"MSInsertAVRecordDetail → inserted ID={objNew.ID}, "
                    f"strClientREQID={strClientREQID}"
                )
                return objNew

        except Exception as exc:
            await db.rollback()
            await CLogController.MSWriteLog(
                iUserId,
                "ERROR",
                f"MSInsertAVRecordDetail → failed for strClientREQID={strClientREQID}. "
                f"Error: {exc}"
            )
            raise

    
    
    @staticmethod
    async def GetAVRecordDetailForParticularRequestID(strClientREQID: str):
        """
        Input:

            1) strClientREQID: str
            The client request ID used to fetch related AVRequestDetail records.

        Output:

            list: A list of dictionaries, each representing a document with enriched information
                from AVRequestDetail, UploadedDoc, and TallyDocRecords.

        Purpose:

            To retrieve all AVRequestDetail records for a given client request ID and enrich
            each record with related document and tally status details. If no matching document
            or tally record exists, default values ("-") are used.
        """
        try:
            async with AsyncSessionLocal() as session:
                av_result = await session.execute(
                    select(AVRequestDetail).where(AVRequestDetail.strClientREQID == strClientREQID)
                )
                av_records = av_result.scalars().all()

                if not av_records:
                    raise HTTPException(status_code=404, detail="No records found for the given ClientREQID.")

                doc_ids = [rec.DOC_UID for rec in av_records if rec.DOC_UID]

                doc_result = await session.execute(
                    select(UploadedDoc).where(UploadedDoc.DocId.in_(doc_ids))
                )
                doc_map = {doc.DocId: doc for doc in doc_result.scalars().all()}

                output = []
                for idx, rec in enumerate(av_records, start=1):
                    doc = doc_map.get(int(rec.DOC_UID)) if rec.DOC_UID else None

                    if rec.EstAccountantTimeSaved == "NOT_APPLICABLE":
                        EstimatedTimeSaved = "-"
                    else:
                        EstimatedTimeSaved = rec.EstAccountantTimeSaved or "-"

                    output.append({
                        "Sr. No.": idx,
                        "Received Date": rec.RecievedDate.strftime("%d-%m-%Y") if rec.RecievedDate else "-",
                        "Vendor Name": (getattr(doc, "DocVendorName", None) or "Unknown").upper() if doc else "-",
                        "File Name": getattr(doc, "DocName", rec.ReqDocName or "-") if doc else rec.ReqDocName or "-",
                        "Unique Doc No": "-" if doc and doc.DocUniqueNo is None else (doc.DocUniqueNo if doc else "-"),
                        "Document Date": "-" if doc and doc.DocDate is None else (doc.DocDate if doc else "-"),
                        "Total Amount": "-" if doc and doc.DocTotalAmount is None else (doc.DocTotalAmount if doc else "-"),
                        "Total Pages": getattr(doc, "PageCount", "-") if doc else "-",
                        "Model Used": "-" if doc and doc.ModelNameAlias is None else (doc.ModelNameAlias if doc else "-"),
                        "Document Format": "Scanned" if getattr(doc, "is_scanned_document", 0) == 1 else ("Digital" if doc else "-"),# NOTE: Only for PDF Documents Applicable
                        "Estimated Time Saved": EstimatedTimeSaved,
                        "AVTally Status": rec.AVXMLGeneratedStatus if rec.AVXMLGeneratedStatus else "-",
                        "PriceList Verification": "Yes" if rec.PriceListVerification else "No",
                        "AccuVelocity Comments": rec.strAccuVelocityComments or "-"
                    })

                return output

        except Exception as e:
            await CLogController.MSWriteLog(
                None,
                "ERROR",
                f"[GetAVRecordDetailForParticularRequestID] failure → {e}"
            )
            await CLogController.MSWriteLog(
                None,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail=f"Error retrieving AV record details: {str(e)}")


    @staticmethod
    async def MSExportAVRecordDetailsToExcel(strClientREQID: str,strReqReportPath: str):
        """
        Input:

            1) strClientREQID: str
            Client request ID to fetch and export data for.

            2) iUserId: int
            The user performing the export (used for logging or metadata if needed).

        Output:

            dict: Contains status and path of the exported Excel report.

        Purpose:

            Fetches AV record details by REQID and saves them into an Excel file
            at `CIndianInvTallyController._mReqReportPath`.
        """
        try:

            result = await CAVRequestDetail.GetAVRecordDetailForParticularRequestID(strClientREQID=strClientREQID)
            if not result:
                return {"success": False, "message": "No data found to export."}

            df = pd.DataFrame(result)

            # Ensure directory exists
            os.makedirs( os.path.dirname(strReqReportPath) , exist_ok=True)

            # Check file extension
            _, ext = os.path.splitext(strReqReportPath)
            ext = ext.lower()
            if ext == ".xlsx" or ext == ".xls":
                df.to_excel(strReqReportPath, index=False)
            elif ext == ".csv":
                df.to_csv(strReqReportPath, index=False)
            else:
                raise ValueError(f"Unsupported file format: {ext}")

            return {
                "success": True,
                "message": f"Excel report saved successfully.",
                "filePath": strReqReportPath
            }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"Failed to export AV details to Excel: {str(e)}")

    @staticmethod
    async def MSUpdateTimeFieldsForRequestID(
        iUserId: int,
        strClientREQID: str,
        CReqGeneratedTimeAt: datetime = None,
        CReqDeliveredTimeAt: datetime = None,
        CReqServerReceivedAt: datetime = None,
        CReqServerCompletedAt: datetime = None,
        CReqIMPORTEDXMLTimeAt:datetime = None,
        strServerEstProcessingTime:str = None
    ):
        """
        Input:

            1) iUserId: int
            The ID of the user performing the update operation.

            2) strClientREQID: str
            The client request ID for which to update records.

            3–6) Optional datetime values to update:
                - CReqGeneratedTimeAt
                - CReqDeliveredTimeAt
                - CReqServerReceivedAt
                - CReqServerCompletedAt

        Output:

            dict: Status of the update operation including count of updated records.

        Purpose:

            To bulk update time fields in AVRequestDetail for all records under the same strClientREQID.
            Only fields with non-null values are updated.
        """
        async with AsyncSessionLocal() as db:
            try:
                query = select(AVRequestDetail).where(
                    AVRequestDetail.strClientREQID == strClientREQID
                )
                result = await db.execute(query)
                records = result.scalars().all()

                if not records:
                    await CLogController.MSWriteLog(iUserId, "Warning", f"No records found for RequestID: {strClientREQID}")
                    return {
                        "success": False,
                        "message": "No records found for the provided RequestID",
                        "updated_count": 0
                    }

                update_count = 0
                for record in records:
                    if CReqGeneratedTimeAt:
                        record.CReqGeneratedTimeAt = CReqGeneratedTimeAt
                    if CReqDeliveredTimeAt:
                        record.CReqDeliveredTimeAt = CReqDeliveredTimeAt
                    if CReqServerReceivedAt:
                        record.CReqServerReceivedAt = CReqServerReceivedAt
                    if CReqServerCompletedAt:
                        record.CReqServerCompletedAt = CReqServerCompletedAt
                    if CReqIMPORTEDXMLTimeAt:
                        record.CReqIMPORTEDXMLTimeAt = CReqIMPORTEDXMLTimeAt
                    if strServerEstProcessingTime:
                        record.strServerEstProcessingTime = strServerEstProcessingTime
                    update_count += 1

                await db.commit()

                await CLogController.MSWriteLog(
                    iUserId,
                    "Info",
                    f"Updated {update_count} records for RequestID: {strClientREQID} with provided time fields."
                )

                return {
                    "success": True,
                    "message": f"Updated {update_count} records",
                    "updated_count": update_count
                }

            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(
                    iUserId,
                    "Error",
                    f"Failed to update time fields for RequestID {strClientREQID}: {str(e)}"
                )
                raise HTTPException(
                    status_code=500,
                    detail=f"Error updating time fields: {str(e)}"
                )

    @staticmethod
    async def MSGetServerExecutionTime(user_id: int, strClientReqID: str, isUseRequestTime=False):
        """
        Purpose : This method retrieves a unique TDL processing record grouped by user and client request ID.

        Inputs  :
            - user_id: int
                User ID of the processing record.
            - strClientReqID: str
                Client request ID to locate the record.
            - isUseRequestTime: bool
                If True, current time is used for ResponseAt instead of stored CReqServerCompletedAt.

        Returns :
            - A dictionary containing the grouped record's details if found, otherwise raises an HTTPException.
        """
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(
                    select(
                        AVRequestDetail.User_UID,
                        AVRequestDetail.strClientREQID,
                        func.min(AVRequestDetail.CReqGeneratedTimeAt).label("created_at"),
                        func.max(AVRequestDetail.CReqServerCompletedAt).label("completed_at"),
                        func.min(AVRequestDetail.ID).label("any_id")
                    )
                    .where(AVRequestDetail.User_UID == user_id)
                    .where(AVRequestDetail.strClientREQID == strClientReqID)
                    .group_by(AVRequestDetail.User_UID, AVRequestDetail.strClientREQID)
                )
                record = result.fetchone()

                if not record:
                    await CLogController.MSWriteLog(
                        user_id,
                        "Error",
                        f"No AVRequestDetail record found for REQID: {strClientReqID}"
                    )
                    return {
                        "success": False,
                        "message": "No records found for the provided RequestID",
                        "updated_count": 0
                    }

                return {
                    "Id": record.any_id,
                    "UserId": record.User_UID,
                    "REQID": record.strClientREQID,
                    "created_at": record.created_at,
                    "ResponseAt": datetime.now() if isUseRequestTime else record.completed_at
                }

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while retrieving the record: {str(e)}")


    @staticmethod
    async def MSOnReqCompleted(iUserId: int, strClientREQID: str, CReqServerReceivedAt:datetime, dictExeUserDetails:dict):
        """
        Purpose : This method retrieves an existing TDL processing record.

        Inputs  :
            - user_id: int (User ID of the processing record.)
            - strClientReqID: str (Client request ID to locate the record.)

        Returns :
            - A dictionary containing the record's details if found, otherwise raises an HTTPException.
        """
        try:
            # AV Report Columns Update == Base on Client REQ ID , Client Request Generate time , Server Recieved Request At , Server Completed Request At
            await CAVRequestDetail.MSUpdateTimeFieldsForRequestID(iUserId=iUserId, strClientREQID = strClientREQID, CReqGeneratedTimeAt=dictExeUserDetails.get("ClientReqGeneratedAt"),CReqServerReceivedAt = CReqServerReceivedAt, CReqServerCompletedAt=datetime.now())
            dictdbRecord=await CAVRequestDetail.MSGetServerExecutionTime(
                user_id=iUserId,
                strClientReqID=strClientREQID
            )
            strExecutiontime=DateHelper.get_time_difference(dictdbRecord)
            # AVRecordDetail strServerEstProcessingTime for Every Row holds same strClientREQID (current request estimated execution time)
            await CAVRequestDetail.MSUpdateTimeFieldsForRequestID(iUserId=iUserId, strClientREQID=strClientREQID, strServerEstProcessingTime=strExecutiontime)

        except Exception as e:
            raise HTTPException(status_code=500, detail=f"An error occurred while retrieving the record: {str(e)}")

    @staticmethod
    async def MSUpdateNetworkLocation(                          # noqa: N802
        iUserId: int,
        dictNewData: dict[str, Any],
        eMode: ENetworkLocationUpdateMode = ENetworkLocationUpdateMode.APPEND,
        *, # ← everything after this is keyword-only
        strClientREQID: str | None = None,
        docId: int | None = None,
        strHashCode: str | None = None,
    ) -> int:
        """
        Update the JSON column `NetworkLocation` **exactly on ONE row**.

        Returns the primary-key ID of the updated record.
        """
        if not any([strClientREQID, docId, strHashCode]):
            raise ValueError("Provide at least one locator (strClientREQID, docId, or strHashCode)")

        try:
            async with AsyncSessionLocal() as db:
                # ---------- dynamic WHERE clause --------------------
                stmt = select(AVRequestDetail).filter(AVRequestDetail.User_UID == iUserId)
                if strClientREQID is not None:
                    stmt = stmt.filter(AVRequestDetail.strClientREQID == strClientREQID)
                if docId is not None:
                    stmt = stmt.filter(AVRequestDetail.DOC_UID == docId)
                if strHashCode is not None:
                    stmt = stmt.filter(AVRequestDetail.ReqDocHashCode == strHashCode)

                # Expect 0-or-1, not a list
                row = (await db.execute(stmt)).scalar_one_or_none()
                if row is None:
                    raise HTTPException(status_code=404, detail="AVRequestDetail row not found")

                # ---------- patch JSON ------------------------------
                old_json = row.NetworkLocation or {}
                row.NetworkLocation = (
                    {**old_json, **dictNewData}   # APPEND
                    if eMode is ENetworkLocationUpdateMode.APPEND
                    else dictNewData              # REPLACE
                )

                await db.commit()

                await CLogController.MSWriteLog(
                    iUserId,
                    "INFO",
                    f"[MSUpdateNetworkLocation] {eMode.value.upper()} {dictNewData} "
                    f"on row ID={row.ID}"
                )
                return row.ID

        except Exception as exc:
            await db.rollback()
            await CLogController.MSWriteLog(
                iUserId,
                "DEBUG",
                f"Traceback:\n{traceback.format_exc()}"
            )
            raise HTTPException(status_code=500, detail="Error inserting AVRequestDetail records")





from fastapi import FastAPI, HTTPException, Depends
from fastapi.responses import StreamingResponse
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy import select
from sqlalchemy import and_
from pydantic import BaseModel
from typing import List, Optional
from datetime import date, datetime
import zipfile
import io
import json
import os
import enum
import logging
from src.Models.models import AVRequestDetail

# Configure logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

# app = FastAPI(
#     title="Document History API",
#     description="API for retrieving and downloading document history, with filtering and pagination. Restricted for user_id=11.",
#     version="1.0.0"
# )

# Dependency for async database session
async def get_db():
    async with AsyncSessionLocal() as db:
        try:
            yield db
        finally:
            await db.close()

# Enums
class VoucherType(str, enum.Enum):
    """Valid voucher types for document filtering."""
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"

class AVXMLGeneratedStatus(str, enum.Enum):
    """Valid statuses for XML generation process."""
    Success = "Success"
    Skipped = "Skipped"
    PartialSuccess = "PartialSuccess"
    Duplicate = "Duplicate"
    NOT_APPLICABLE = "NOT_APPLICABLE"
    ValidationError = "ValidationError"

class TallyXMLImportStatus(str, enum.Enum):
    """Valid statuses for Tally XML import (SUCCESS for status code 200, FAILED for 999 or other)."""
    SUCCESS = "SUCCESS"
    FAILED = "FAILED"

# Pydantic models for request/response
class DocumentFilter(BaseModel):
    start_date: Optional[date] = None
    end_date: Optional[date] = None
    voucher_type: Optional[VoucherType] = None
    status: Optional[AVXMLGeneratedStatus] = None
    document_name: Optional[str] = None
    tally_status: Optional[TallyXMLImportStatus] = None

    class Config:
        use_enum_values = True

class DocumentResponse(BaseModel):
    ID: int
    strVoucherType: VoucherType
    AVDocumentPtocessedStatus: AVXMLGeneratedStatus
    TallyXMLImportStatus: TallyXMLImportStatus
    ReqDocName: Optional[str]
    ReqDocType: Optional[str]
    CReqGeneratedTimeAt: datetime
    strCustomerName: str
    UserName: str
    has_xml: bool
    EstAccountantTimeSaved: Optional[str]
    # DocUniqueNo: Optional[str]  = None
    # DocDate: Optional[str] = None
    # DocTotalAmount:Optional[str] = None
    # PageCount:Optional[str] = None
    # DocVendorName:Optional[str] = None

class PaginatedDocumentResponse(BaseModel):
    documents: List[DocumentResponse]
    total_records: int
    total_pages: int
    current_page: int

# Helper function to extract file name from NetworkLocation
def get_file_name(network_location: Optional[any], fallback: str) -> str:
    """Extract file name from NetworkLocation (JSON string or dict) or return fallback."""
    if not network_location:
        logger.debug(f"No network_location provided, using fallback: {fallback}")
        return fallback
    try:
        if isinstance(network_location, dict):
            data = network_location
        else:
            data = json.loads(network_location)
        if isinstance(data, dict) and "XMLFilePath" in data:
            if isinstance(data["XMLFilePath"], list) and len(data["XMLFilePath"]) > 0:
                # Extract file name from the path
                logger.debug(f"Extracting file name from XMLFilePath: {data['XMLFilePath'][0]}")
                file_name = os.path.basename(data["XMLFilePath"][0])
                return file_name
        logger.debug(f"No 'XMLFilePath' in network_location, using fallback: {fallback}")
        return fallback
    except (json.JSONDecodeError, TypeError) as e:
        logger.error(f"Error parsing network_location: {str(e)}, using fallback: {fallback}")
        return fallback

# API to fetch document history with pagination
@AVRequetDetailTableRouter.post(
    "/documents/history",
    response_model=PaginatedDocumentResponse,
    summary="Fetch paginated document history",
    description="Retrieve a paginated list of documents for a user, filtered by date range, voucher type, XML generation status, document name, and Tally import status. Results are sorted by creation time (descending). Restricted for user_id=11.",
    response_description="A paginated response containing document details, total records, total pages, and current page."
)
async def get_document_history(
    user_id: int,
    filters: DocumentFilter,
    page: int = 1,
    page_size: int = 10,
    db: AsyncSession = Depends(get_db)
):
    if user_id == 11:
        raise HTTPException(status_code=403, detail="This feature is not available for your account.")

    if page < 1:
        raise HTTPException(status_code=400, detail="Page must be greater than 0")
    if page_size < 1 or page_size > 100:
        raise HTTPException(status_code=400, detail="Page size must be between 1 and 100")

    # Build count query
    count_query = select(AVRequestDetail).where(AVRequestDetail.User_UID == user_id)
    if filters.start_date:
        count_query = count_query.where(AVRequestDetail.RecievedDate >= filters.start_date)
    if filters.end_date:
        count_query = count_query.where(AVRequestDetail.RecievedDate <= filters.end_date)
    if filters.voucher_type:
        count_query = count_query.where(AVRequestDetail.strVoucherType == filters.voucher_type)
    if filters.status:
        count_query = count_query.where(AVRequestDetail.AVXMLGeneratedStatus == filters.status)
    if filters.document_name:
        count_query = count_query.where(AVRequestDetail.ReqDocName.ilike(f"%{filters.document_name}%"))
    if filters.tally_status:
        status_code = "200" if filters.tally_status == TallyXMLImportStatus.SUCCESS else "999"
        count_query = count_query.where(AVRequestDetail.ClientImportedXMLStatusCode == status_code)

    count_result = await db.execute(count_query)
    total_records = len(count_result.scalars().all())
    total_pages = (total_records + page_size - 1) // page_size

    # Build data query
    data_query = select(AVRequestDetail).where(AVRequestDetail.User_UID == user_id)

    # Join with uploaded_docs
    # data_query = (
    #     select(AVRequestDetail, UploadedDoc)
    #     .outerjoin(UploadedDoc, UploadedDoc.DocId == AVRequestDetail.DOC_UID)
    #     .where(
    #         and_(
    #             AVRequestDetail.User_UID == user_id
    #         )
    #     )
    # )

    if filters.start_date:
        data_query = data_query.where(AVRequestDetail.RecievedDate >= filters.start_date)
    if filters.end_date:
        data_query = data_query.where(AVRequestDetail.RecievedDate <= filters.end_date)
    if filters.voucher_type:
        data_query = data_query.where(AVRequestDetail.strVoucherType == filters.voucher_type)
    if filters.status:
        data_query = data_query.where(AVRequestDetail.AVXMLGeneratedStatus == filters.status)
    if filters.document_name:
        data_query = data_query.where(AVRequestDetail.ReqDocName.ilike(f"%{filters.document_name}%"))
    if filters.tally_status:
        status_code = "200" if filters.tally_status == TallyXMLImportStatus.SUCCESS else "999"
        data_query = data_query.where(AVRequestDetail.ClientImportedXMLStatusCode == status_code)

    data_query = data_query.order_by(AVRequestDetail.CReqGeneratedTimeAt.desc())
    data_query = data_query.offset((page - 1) * page_size).limit(page_size)
    result = await db.execute(data_query)
    # records = result.all() #  returns list of tuples: (AVRequestDetail, UploadedDocs)
    records = result.scalars().all()


    response = []
    for record in records:
        response.append(DocumentResponse(
            ID=record.ID,
            strVoucherType=record.strVoucherType,
            AVDocumentPtocessedStatus=record.AVXMLGeneratedStatus or AVXMLGeneratedStatus.NOT_APPLICABLE,
            TallyXMLImportStatus=TallyXMLImportStatus.SUCCESS if str(record.ClientImportedXMLStatusCode) == "200" else TallyXMLImportStatus.FAILED,
            ReqDocName=record.ReqDocName or f"Request_{record.ID}",
            ReqDocType=record.ReqDocType or "Request",
            CReqGeneratedTimeAt=record.CReqGeneratedTimeAt,
            strCustomerName=record.strCustomerName or f"Customer_{record.ID}",
            UserName=record.strSystemUserName or "Unknown",
            has_xml=bool(record.strXMLResponse),
            EstAccountantTimeSaved=record.EstAccountantTimeSaved,
            # DocUniqueNo=doc.DocUniqueNo if doc else None,
            # DocDate=doc.DocDate if doc else None,
            # DocTotalAmount=doc.DocTotalAmount if doc else None,
            # PageCount=str(doc.PageCount) if doc and doc.PageCount is not None else None,
            # DocVendorName=doc.DocVendorName if doc else None
        ))

    logger.info(f"Fetched {len(response)} documents for user_id={user_id}, page={page}")
    return PaginatedDocumentResponse(
        documents=response,
        total_records=total_records,
        total_pages=total_pages,
        current_page=page
    )

# API to download XML files as ZIP
@AVRequetDetailTableRouter.post(
    "/documents/download",
    response_model=None,
    summary="Download XML files as ZIP",
    description="Download XML files for the specified request IDs as a ZIP archive. Only includes AVRequestDetail XMLs. Restricted for user_id=11.",
    response_description="A ZIP file containing XML documents, named using NetworkLocation or fallback."
)
async def download_xml_files(
    user_id: int,
    request_ids: List[int],
    db: AsyncSession = Depends(get_db)
):
    if user_id == 11:
        raise HTTPException(status_code=403, detail="This feature is not available for your account.")

    if not request_ids:
        raise HTTPException(status_code=400, detail="At least one request_id must be provided")

    try:
        buffer = io.BytesIO()
        with zipfile.ZipFile(buffer, "w", zipfile.ZIP_DEFLATED) as zip_file:
            query = select(AVRequestDetail).where(
                and_(
                    AVRequestDetail.User_UID == user_id,
                    AVRequestDetail.ID.in_(request_ids)
                )
            )
            result = await db.execute(query)
            records = result.scalars().all()

            if not records:
                logger.warning(f"No documents found for user_id={user_id}, request_ids={request_ids}")
                raise HTTPException(status_code=404, detail="No documents found")

            for record in records:
                if record.strXMLResponse:
                    file_name = get_file_name(record.NetworkLocation, f"AVRequestDetail_{record.ID}.xml")

                    zip_file.writestr(file_name, record.strXMLResponse)
                    logger.debug(f"Added XML for ID={record.ID} as {file_name}")

        if not zip_file.namelist():
            logger.warning(f"No valid XMLs found for user_id={user_id}, request_ids={request_ids}")
            raise HTTPException(status_code=404, detail="No valid XMLs found")

        buffer.seek(0)
        logger.info(f"Generated ZIP with {len(zip_file.namelist())} files for user_id={user_id}")
        return StreamingResponse(
            buffer,
            media_type="application/zip",
            headers={"Content-Disposition": f"attachment; filename=documents_{user_id}.zip"}
        )
    except Exception as e:
        logger.error(f"Error generating ZIP for user_id={user_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")

# API for single XML download
@AVRequetDetailTableRouter.get(
    "/documents/download/single",
    response_model=None,
    summary="Download single XML file",
    description="Download a single XML file for a specific request ID. Only supports doc_type='Request'. Restricted for user_id=11.",
    response_description="An XML file named using NetworkLocation or fallback."
)
async def download_single_xml(
    user_id: int,
    doc_type: str,
    record_id: str,
    db: AsyncSession = Depends(get_db)
):
    if user_id == 11:
        raise HTTPException(status_code=403, detail="This feature is not available for your account.")

    if doc_type != "Request":
        raise HTTPException(status_code=400, detail="Invalid document type. Only 'Request' is supported.")

    try:
        xml_content = None
        filename = None

        result = await db.execute(
            select(AVRequestDetail).where(
                and_(
                    AVRequestDetail.User_UID == user_id,
                    AVRequestDetail.ID == int(record_id)
                )
            )
        )
        record = result.scalars().first()
        if record and record.strXMLResponse:
            xml_content = record.strXMLResponse
            filename = get_file_name(record.NetworkLocation, f"AVRequestDetail_{record_id}.xml")
        else:
            logger.warning(f"No XML found for user_id={user_id}, record_id={record_id}")
            raise HTTPException(status_code=404, detail="XML not found")

        logger.info(f"Downloaded XML for user_id={user_id}, record_id={record_id}, filename={filename}")
        return StreamingResponse(
            io.BytesIO(xml_content.encode('utf-8')),
            media_type="application/xml",
            headers={"Content-Disposition": f"attachment; filename={filename}"}
        )
    except ValueError:
        logger.error(f"Invalid record_id={record_id} for user_id={user_id}")
        raise HTTPException(status_code=400, detail="Invalid document ID")
    except Exception as e:
        logger.error(f"Error downloading XML for user_id={user_id}, record_id={record_id}: {str(e)}")
        raise HTTPException(status_code=500, detail=f"Internal server error: {str(e)}")