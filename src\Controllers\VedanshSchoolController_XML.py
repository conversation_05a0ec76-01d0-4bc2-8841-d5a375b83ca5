import sys
sys.path.append("")
import traceback
import os
import pytz
from fastapi import HTTPException
import asyncio
import json
from datetime import datetime
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, PartyDetailsSchema, ConsigneeDetailsSchema, LedgerEntrySchema, TallyPurchaseVoucherSchema
from src.utilities.helperFunc import DateHelper, CFileHandler
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.CVedanshJournalGeneralClass import CSouthEastAsia, CGrurKripaPertroleum, CTrophyWala, CSanghiBrothers, CRKEnterprises, CMahaveerKiranaStores, CRonakRudraSecurity, CGhanshyamBahiniya, CInfinityCars, CMendwellAgencies, CVedanshDiscount
from src.Controllers.AVRequestDetailController import CAVRequestDetail
import re
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode 
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
import xml.etree.ElementTree as ET
from src.utilities.helperFunc import CJSONFileReader, CSimilarityHelper
from src.utilities.PathHandler import dictProjectPaths
from pathlib import Path

class CVedanshInternationSchool:
    _mStrStoragePath = dictProjectPaths.get("strVedanshSchool_StoragePath", r"H:/AI Data/DailyData/VedanshSchool") 

    _mDictCompanyData = {
                            "company_name": "Vidhyarambh Education Society (2024-2025)",
                            "gst_registration_type": "",
                            "state_name": "Madhya Pradesh",
                            "country_name": "India",
                            "gst_in":"",
                    }
    
    _mDictCosigneeData = {
                        "address_list": ["Chota Bangarda Road,", "Near Airport ," , "Indore (M.P.)"],
                        "gst_in": "",
                        "mailing_name": "VIDHYARAMBH",
                        "pin_code": "",
                        "state_name": "Madhya Pradesh",
                        "country_name": "India"
                    }
    
    def __init__(self, user_id, doc_id):
        self.iUserId = user_id
        self.DocId = doc_id
        self.TallyUserConfigObj = None
        self.UploadDocObj = None
        self.ExtractedDocObj = None
        self.tally_NotExistStockItems = {}
        self.dictProcessData = {}
        self.tally_AccuvelocityComments = "-"
        self.tally_Tally_status = "Skipped"
    
    async def MInitialize(self):
        try:
            await CLogController.MSWriteLog(self.iUserId, "Info", "Initializing the Simpolo model.")

            # Fetch Tally User Configuration
            self.TallyUserConfigObj = await CTallyController.MSGetTallyUserConfig(iUserID=self.iUserId)
            await CLogController.MSWriteLog(self.iUserId, "Info", "Fetched Tally user configuration.")

            
            # Validate Tally Enable
            if not self.TallyUserConfigObj.get("TallyEnable"):
                await CLogController.MSWriteLog(self.iUserId, "Error", "Tally is not enabled for the user.")
                raise HTTPException(
                    status_code=400, 
                    detail="Tally is not enabled for you. Please contact the developer team."
                )

            # Get Upload Document Attributes
            self.UploadDocObj = await CDocumentData.MSGetDocById(
                user_id=self.iUserId, 
                docId=self.DocId,
                isBinaryDataRequired=False
            )
            await CLogController.MSWriteLog(self.iUserId, "Info", f"Fetched document details for DocId: {self.DocId}.")

            # Validate Model Name
            model_name = self.UploadDocObj.get("ModelName", "").lower()
            

            # TODO: Use this vendor name to decide whcih child instance needs to be created
            self.strVendorName = model_name
        
            await CLogController.MSWriteLog(self.iUserId, "Info", "Initialization completed successfully.")

        except HTTPException as he:
            await CLogController.MSWriteLog(self.iUserId, "Error", f"HTTPException during initialization: {he.detail}")
            await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        except Exception as e:
            await CLogController.MSWriteLog(self.iUserId, "Error", "Failed to initialize the Simpolo model.")
            await CLogController.MSWriteLog(self.iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            print("Error Occurred - ", traceback.print_exc())
            raise
    

    @staticmethod
    async def MGenerateTallyXML(iUserId, iDocId, dictExtractedData, strVendorName, bRaiseError=False,lsUdfData=[{}],strClientREQID=None, ObjVoucherType = None, bIsDeveloperMode= False):
        dictResponse = {
            "AVComments":"",
            "TallyStatus":"",
            "XMLFilePath":"",
            "iSupplierInvoiceNumber":None,
            "TallyAPIResp": None,
            "DocErrorMsg": None
        }
        iSupplierInvoiceNumber = ""
        dictXMLProcessingResult = None
        bIsDuplicate = False
        try:
            await CLogController.MSWriteLog(iUserId, "Info", "Starting XML Generation for ICD.")
            
            iSupplierInvoiceNumber = dictExtractedData.get("InvoiceNo")
            bIsInvoiceNoEmpty = (str(iSupplierInvoiceNumber)).strip() == ""
            dictResponse["iSupplierInvoiceNumber"] = iSupplierInvoiceNumber
            strSellerName = dictExtractedData.get("SellerDetails", {}).get("SellerName", "")
            if strSellerName.strip() == "":
                strSellerName = dictExtractedData.get("BuyersDetails", {}).get("BuyerName", "")
            
            dictImplementedVendorName = {
                    "the vedansh international school":"the vedansh international school",
                    "south asia fm ltd":"south asia fm ltd",
                    "guru kripa petroleum":"guru kripa petroleum",
                    "trophy wala":"trophy wala",
                    "r k enterprises":"r k enterprises",
                    "sanghi brothers":"sanghi brothers",
                    "mahavir kirana stores":"mahavir kirana stores",
                    "inifinty cars pvt ltd":"inifinty cars pvt ltd",
                    "mendwell agencies":"mendwell agencies",
                    "ghanshyam bahiniya":"ghanshyam bahiniya",
                    "ronak rudra security & management services":"ronak rudra security & management services"
                }
            strMatchedVendorName = CSimilarityHelper.MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig =dictImplementedVendorName, strInputText = strSellerName)
            
            if not bIsDeveloperMode and not bIsInvoiceNoEmpty and strMatchedVendorName.lower() != "guru kripa petroleum":
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocumentNumber=iSupplierInvoiceNumber,objVoucherType = ObjVoucherType)
            # Developer MODE: Ignore Duplicate Check
            if not bIsDeveloperMode and not bIsInvoiceNoEmpty and bIsDuplicate:
                dictResponse["TallyStatus"] = "Duplicate"
                dictResponse["AVComments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                dictResponse["TallyAPIResp"] = {"status": f"Failed to Create the Tally xml : Tally XML: Duplicate Entry Found in AccuVelocity."},
                dictResponse["DocErrorMsg"] = "AccuVelocity Duplicate Validation Entry Found."
                await CLogController.MSWriteLog(iUserId, "Info", f"Duplicate entry found for invoice numbered '{iSupplierInvoiceNumber}'.")
                
                # AVRecordDetail -- AVXMLGeneratedStatus, TracebackLogs, strAccuVelocityComments Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE', AVXMLGeneratedStatus=dictResponse["TallyStatus"],TracebackLogs= "WARNING - Duplicate Entry Detected in our AccuVelocity Software", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments=dictResponse["AVComments"])

                # await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                #     iDocID=iDocId,
                #     iUserID=iUserId,
                #     strREQID = strClientREQID,
                #     invoice_no=iSupplierInvoiceNumber,
                #     av_tally_xml_status=dictResponse["TallyStatus"],
                #     tally_api_resp=dictResponse["TallyAPIResp"],
                #     resp_date_time=datetime.now(),
                #     DocErrorMsg=dictResponse["DocErrorMsg"],
                #     strAVComments=dictResponse["AVComments"] 
                # )

                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")

            # Get Upload Document Attributes
            objUploadedDocs = await CDocumentData.MSGetDocById(
                                                                user_id=iUserId, 
                                                                docId=iDocId,
                                                                isBinaryDataRequired=False
                                                            )
            await CLogController.MSWriteLog(iUserId, "Info", f"Fetched document details for DocId: {iDocId}.")

            try:
                #TODO: please add new vendor
                strXmlData = ""
                
                if strMatchedVendorName.lower() == "the vedansh international school":
                    dictXMLProcessingResult = await CVedansh.MSCreateXML(iUserId, dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "south asia fm ltd":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/South Asia FM Limited/VedanshSouthEastAsiaFMConfig.json")
                    dictXMLProcessingResult = await CSouthEastAsia.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)
                
                elif strMatchedVendorName.lower() == "guru kripa petroleum":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/GuruKripaPetroleum/VedanshGuruKripaPetroleumConfig.json")
                    dictXMLProcessingResult = await CGrurKripaPertroleum.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "trophy wala":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/Trophy Wala/VedanshTrophyWalaConfig.json")

                    dictXMLProcessingResult = await CTrophyWala.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "r k enterprises":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/RKEnterprises/VedanshRKEnterprisesConfig.json")

                    dictXMLProcessingResult = await CRKEnterprises.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "sanghi brothers":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/SanghiBrothers/VedanshSanghiBrothersConfig.json")

                    dictXMLProcessingResult = await CSanghiBrothers.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "mahavir kirana stores":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/MahaveerKiranaStores/VedanshMahaveerKiranaConfig.json")

                    dictXMLProcessingResult = await CMahaveerKiranaStores.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "ronak rudra security & management services":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/RonakRudraSecurity/VedanshRonakRudraSecurityaConfig.json")

                    dictXMLProcessingResult = await CRonakRudraSecurity.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "ghanshyam bahiniya":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/GhansyamBahiniya/VedanshGhanshyamBahiniyaConfig.json")

                    dictXMLProcessingResult = await CGhanshyamBahiniya.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strMatchedVendorName.lower() == "inifinty cars pvt ltd":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/InfinityCars/VedanshInfinityCarsConfig.json")

                    dictXMLProcessingResult = await CInfinityCars.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)
                
                elif strMatchedVendorName.lower() == "mendwell agencies":
                    strConfigFilePath = Path(r"Data/Customer/VedanshSchool/MendwellAgencies/VedanshMendwellAgenciesConfig.json")

                    dictXMLProcessingResult = await CMendwellAgencies.MSCreateXML(strConfigFilePath,iUserId,dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

        
                else:
                    raise ValueError(f"Vendor '{strMatchedVendorName}' is not supported, please contact accuvelocity team for more info.")
                
                if "ValidationError" in dictXMLProcessingResult["AVComments"]:
                    dictResponse["TallyStatus"] = "ValidationError"    
                    raise ValueError(f"{dictXMLProcessingResult['AVComments']}")
                
                dictResponse["DocErrorMsg"] =  f"{dictXMLProcessingResult['DocErrorMsg']}"
                dictResponse["AVComments"] = f"{dictXMLProcessingResult['AVComments']}"
                dictResponse["TallyStatus"] = dictXMLProcessingResult["TallyStatus"]
                if dictXMLProcessingResult["TallyStatus"] in ["Success"]:
                    strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name=strMatchedVendorName, no_of_stock_items=dictXMLProcessingResult.get("iTotalTallyInsertedItem",0))
                else:
                    strTimeSaved = "~ 0 min 0 sec"
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus=dictXMLProcessingResult["TallyStatus"],TracebackLogs=f"Info - TALLY XML: {dictXMLProcessingResult['AVComments']}",strAccuVelocityComments=f"{dictXMLProcessingResult['AVComments']}", EstAccountantTimeSaved = strTimeSaved)
            except ValueError as ve:
               
                if "ValidationError" in dictXMLProcessingResult["AVComments"]:
                    dictResponse["TallyStatus"] = "ValidationError"
                else: 
                    dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = str(ve)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                dictResponse["DocErrorMsg"] = str(ve)
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus=dictXMLProcessingResult["TallyStatus"],TracebackLogs=f"Error - TALLY XML: {traceback.format_exc()}",strAccuVelocityComments=f"{dictXMLProcessingResult['AVComments']}")
                # await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                #     iDocID=iDocId,
                #     iUserID=iUserId,
                #     strREQID = strClientREQID,
                #     invoice_no=dictResponse["iSupplierInvoiceNumber"],
                #     av_tally_xml_status=dictResponse["TallyStatus"],
                #     tally_api_resp=dictResponse["TallyAPIResp"],
                #     resp_date_time=datetime.now(),
                #     DocErrorMsg=dictResponse["DocErrorMsg"],
                #     strAVComments=dictResponse["AVComments"] 
                # )
                raise ve
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(e)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus=dictXMLProcessingResult["TallyStatus"],TracebackLogs=f"Error - TALLY XML: {traceback.format_exc()}",strAccuVelocityComments=f"{dictXMLProcessingResult['AVComments']}")
                # await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                #     iDocID=iDocId,
                #     iUserID=iUserId,
                #     strREQID = strClientREQID,
                #     invoice_no=dictResponse["iSupplierInvoiceNumber"],
                #     av_tally_xml_status=dictResponse["TallyStatus"],
                #     tally_api_resp=dictResponse["TallyAPIResp"],
                #     resp_date_time=datetime.now(),
                #     DocErrorMsg=dictResponse["DocErrorMsg"],
                #     strAVComments=dictResponse["AVComments"] 
                # )
                raise e
            
            try:
                if dictXMLProcessingResult["xmlContent"] is not None:
                # Base directory path
                    strDownloadDirPath = CVedanshInternationSchool._mStrStoragePath
                    today_date = datetime.today().strftime('%Y_%m_%d')
                    strXmlData = dictXMLProcessingResult.get("xmlContent","")
                    # Create the full directory path with the date-wise folder
                    strDownloadDirPath = os.path.join(strDownloadDirPath, today_date)

                    # Ensure the directory exists
                    os.makedirs(strDownloadDirPath, exist_ok=True)

                    strUploadedDocumentName = os.path.splitext(objUploadedDocs.get('DocName', ''))[0]
                    strXMLFileName = f"{strClientREQID}_DID{iDocId}_DName{strUploadedDocumentName}.xml"
                    strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                    bIsFileWritten = CFileHandler.MSWriteFile(  strFilePath=strTodaysXmlFilePath, 
                                                                fileContent=strXmlData, 
                                                                strWriteMode="w", 
                                                                strEncoding=None) 
                if bIsFileWritten: 
                    dictResponse["XMLFilePath"] = strTodaysXmlFilePath
                    dictResponse["TallyStatus"] = "Success"
                    dictResponse["AVComments"] = "-"
                    dictResponse["TallyAPIResp"] = {"status": f"Successfully created the tally xml at location: {strTodaysXmlFilePath}."}
                    
                    # AVRecordDetail Update  
                    await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                        iUserId=iUserId,
                        dictNewData = {"XMLFilePath": [strTodaysXmlFilePath]},
                        eMode = ENetworkLocationUpdateMode.APPEND,
                        strClientREQID=strClientREQID,
                        docId=iDocId)
                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully stored xml file at location '{strTodaysXmlFilePath}'.")
                # Update the status of document processing
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                # await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                #     iDocID=iDocId,
                #     iUserID=iUserId,
                #     strREQID = strClientREQID,
                #     invoice_no=dictResponse["iSupplierInvoiceNumber"],
                #     av_tally_xml_status=dictResponse["TallyStatus"],
                #     tally_api_resp=dictResponse["TallyAPIResp"],
                #     resp_date_time=datetime.now(),
                #     DocErrorMsg=dictResponse["DocErrorMsg"],
                #     strAVComments=dictResponse["AVComments"] 
                # )
                
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

        except HTTPException as he:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{he}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he
        
        except ValueError as ve:
            raise ve
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            print(f"An unexpected error occurred: {e}")

            if bRaiseError:
                raise e
        
        # await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
        #     iDocID=iDocId,
        #     iUserID=iUserId,
        #     strREQID = strClientREQID,
        #     invoice_no=dictResponse["iSupplierInvoiceNumber"],
        #     av_tally_xml_status=dictResponse["TallyStatus"],
        #     tally_api_resp=dictResponse["TallyAPIResp"],
        #     resp_date_time=datetime.now(),
        #     DocErrorMsg=dictResponse["DocErrorMsg"],
        #     strAVComments=dictResponse["AVComments"]
        # )
        return dictResponse
    
    import xml.etree.ElementTree as ET



class CVedansh:
    
    # 1) Basic Party Info for Vendash School
    _mStrLedgerName = ""  # Name of the ledger
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [],
        "gst_registration_type": "",
        "gst_in": "",
        "state_name": "Madhya Pradesh",
        "country_name": "India",
        "pin_code":""
    }
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    _mstrDocErrorMsg = ""
    _miUserId = None
    _mDictVendorConfig = None

    @classmethod
    def reset(cls):
        cls._mStrLedgerName = ""
        cls._mDictPartyData = {
            "party_name": "",
            "address_list": [],
            "gst_registration_type": "",
            "gst_in": "",
            "state_name": "Madhya Pradesh",
            "country_name": "India",
            "pin_code": ""
        }
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""
        cls._miUserId = None
        cls._mDictVendorConfig = None

    @staticmethod
    def getLedgerConfig():
        CVedansh._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/VedanshSchool/TallyLedgerConfig.json"))
        return CVedansh._mDictVendorConfig

    @staticmethod
    async def MSGetCreditLedgerInfo(iUserId, dictExtractedData, bIsAdmissionForm=False):
        """
        Returns the ledger entry for the vendor .
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).
       
        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Generating Credit Ledger")
            if bIsAdmissionForm:
                strNarration = f"{dictExtractedData.get('BuyersDetails','').get('BuyerName','')} Class {dictExtractedData.get('Class','')}"
            else:
                strNarration = None
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CVedansh._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
                "is_party_ledger": True,
                "str_narration": strNarration
            }
       
            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", e)
            CVedansh._mStrTracebackLogs = "MSGetCreditLedgerInfo:\n" + str(traceback.format_exc())
            raise e
 
       
 
    @staticmethod
    async def MSGetDebitLedgerInfo(iUserId, dictExtractedData, bIsAdmissionFee = False):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Generating Debit Ledger")
            ledger_details = {}
            ledger_name =await CVedansh.mTransactionMapping(iUserId, dictExtractedData.get("ModeOfTransaction"), dictExtractedData.get("Class"))
           
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
               
            ledger_details[ledger_name]["amount"] += -dictExtractedData.get("TotalAmount",0)
           
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", e)
            CVedansh._mStrTracebackLogs = "MSGetDebitLedgerInfo:\n" + str(traceback.format_exc())
            
            raise e
 
    @staticmethod
    async def MSGetLedgerInformation(iUserId, dictExtractedData, bIsAdmissionForm = False):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(iUserId, "Info", f"Generating Credit and Debit Ledger")
            lsLedgersInfo = []
            if bIsAdmissionForm:
                CVedansh._mStrLedgerName = "ADMISSION FORM FEE (2025-26)"
            else:
                strPartialLedgerName = dictExtractedData.get("BuyersDetails", {}).get("BuyerName") +"_"+ str(dictExtractedData.get("OnlineRegNo"))
                # Search LedgerName in Client Tally Ledger Data
                strMatchedLedgerName =  CSimilarityHelper.MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = CVedansh._mDictVendorConfig, strInputText = strPartialLedgerName)
                # if Match Not Found then Use Formated Ledger Name
                if strMatchedLedgerName.strip() == "":
                    CVedansh._mStrLedgerName = strPartialLedgerName
                else:
                    CVedansh._mStrLedgerName = strMatchedLedgerName 

 
            lsCreditLedgers = await CVedansh.MSGetCreditLedgerInfo(iUserId, dictExtractedData,bIsAdmissionForm)
            lsDebitLedgers = await CVedansh.MSGetDebitLedgerInfo(iUserId, dictExtractedData)
 
            lsLedgersInfo.extend(lsCreditLedgers)
            lsLedgersInfo.extend(lsDebitLedgers)
 
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", e)
            CVedansh._mStrTracebackLogs = "MSGetLedgerInformation:\n" + str(traceback.format_exc())
            raise e
 
    @staticmethod
    async def MSCreateXML(iUserId, dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Bhavya Sales.
        """
        dictXMLResponse = {
            "xmlContent" : None,
            "ErrorMsg" : None,
            "AVComments":None,
            "DocErrorMsg":None
        }
        try:
            CVedansh.reset()
            CVedansh._miUserId = iUserId
            CVedansh.getLedgerConfig()
            strDicountXml={}
            xml_str = ""
            if dictExtractedData.get("TotalAmount", 0) == 0:
                raise ValueError("ValidationError Tally XML: The fee receipt cannot be processed as the total amount is ₹0.")
            if len(dictExtractedData.get("Products/GoodsNameList","")) == 1 and dictExtractedData.get("Products/GoodsNameList","")[0].get("Product/GoodsName","").strip().lower() == "ADMISSION FORM (VIS)".lower():
                
                # 1) Basic invoice info
                # strInvoiceNumber = f"AV/AdmissionFee/{datetime.now().strftime('%d%m%Y-%H%M%S')}"
                strInvoiceNumber = str(dictExtractedData.get("InvoiceNo",f"AV/AdmissionFee/{datetime.now().strftime('%d%m%Y-%H%M%S')}"))

                # e.g. 220125 => 22-Jan-2025 if your helper function is the same
                iInvoiceDate = str(dictExtractedData.get("InvoiceDate", "NA"))

                # 3) Gather ledger entries
                lsLedgerEntries =await CVedansh.MSGetLedgerInformation(iUserId, dictExtractedData, bIsAdmissionForm=True)
                strNarration = f'BEING FEE AMOUNT RECEIVED OF NEW SESSION 2025-26 (ADMISSION FORM SOLD OF {dictExtractedData.get("BuyersDetails","").get("BuyerName")} Class {dictExtractedData.get("Class","")}), Punched On: {datetime.now().strftime("%d-%m-%Y %H:%M:%S")} For ADMISSION FORM'

                # 4) Construct Tally schema objects (Pydantic or otherwise)
                objCompanyInfo = CompanyInfoSchema(**CVedanshInternationSchool._mDictCompanyData)
                objPartyDetails = PartyDetailsSchema(**CVedansh._mDictPartyData)
                objConsigneeDetails = ConsigneeDetailsSchema(**CVedanshInternationSchool._mDictCosigneeData)

                # Convert each ledger dict to LedgerEntrySchema
                lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
                lsUdfData=lsUdfData

                # 5) Now build your TallyPurchaseVoucherSchema
                objPurchaseVoucher = TallyPurchaseVoucherSchema(
                    company_info=objCompanyInfo,
                    party_details=objPartyDetails,
                    consignee_details=objConsigneeDetails,
                    voucher_number=strInvoiceNumber,
                    invoice_date=str(iInvoiceDate),
                    invoice_no=strInvoiceNumber,
                    narration=strNarration,
                    voucher_type = "Other Receipt",
                    cost_center_name="",
                    ledger_entries=lsLedgerEntries,
                    udf_data=lsUdfData  
                )

            # Code To Handle Fee Receipt
            else:
                
                # 1) Basic invoice info
                # strInvoiceNumber = f"AV/FeeReceipt/{datetime.now().strftime('%d%m%Y-%H%M%S')}" # {CVedansh._mDictPartyData['party_name']}
                
                strInvoiceNumber = str(dictExtractedData.get("InvoiceNo",f"AV/FeeReciept/{datetime.now().strftime('%d%m%Y-%H%M%S')}"))
                
                # e.g. 220125 => 22-Jan-2025 if your helper function is the same
                iInvoiceDate = str(dictExtractedData.get("InvoiceDate", "NA"))
                # iInvoiceDate = DateHelper.MSConvertIntToDateFromYYYYMMDD(invoice_date=iExtractedInvoiceDate)

                # 2) Typically you might create a narration or keep it simple
                # strNarration = CBhavyaSales.MSGenerateNarration(dictExtractedData["Table"])

                # 3) Gather ledger entries
         
                lsLedgerEntries = await CVedansh.MSGetLedgerInformation(iUserId, dictExtractedData)

                strNarration = f"To {lsLedgerEntries[0]['ledger_name']}, {lsLedgerEntries[0]['amount']} | By {lsLedgerEntries[1]['ledger_name']}, Mode of Transaction: {dictExtractedData.get('ModeOfTransaction')}, Punched On: {datetime.now().strftime('%d-%m-%Y %H:%M:%S')} For Fee Reciept"

                # 4) Construct Tally schema objects (Pydantic or otherwise)
                #    You mentioned you have similar schemas for CompanyInfoSchema, PartyDetailsSchema, etc.
                objCompanyInfo = CompanyInfoSchema(**CVedanshInternationSchool._mDictCompanyData)
                objPartyDetails = PartyDetailsSchema(**CVedansh._mDictPartyData)
                objConsigneeDetails = ConsigneeDetailsSchema(**CVedanshInternationSchool._mDictCosigneeData)

                # Convert each ledger dict to LedgerEntrySchema
                lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
                lsUdfData=lsUdfData

                # Adding Narration
                

                # 5) Now build your TallyPurchaseVoucherSchema
                objPurchaseVoucher = TallyPurchaseVoucherSchema(
                    company_info=objCompanyInfo,
                    party_details=objPartyDetails,
                    consignee_details=objConsigneeDetails,
                    voucher_number=strInvoiceNumber,
                    invoice_date=str(iInvoiceDate),
                    invoice_no=strInvoiceNumber,
                    narration=strNarration,
                    voucher_type = "Fees Receipt",
                    cost_center_name="",
                    ledger_entries=lsLedgerEntries,
                    udf_data=lsUdfData,
                    strTaxUnitState = "Madhya Pradesh",
                    strPlaceOfSupply = "Madhya Pradesh"  
                )

                # Code To Handle Discount Entry
                if len(dictExtractedData.get("Discounts","")) != 0:
                    strDicountXml =await CVedanshDiscount.MSCreateXML(iUserId=iUserId, dictExtractedData=dictExtractedData, lsUdfData=lsUdfData)


            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True,strVoucherViewMode="Accounting Voucher View")
            xml_str = await CVedansh.MSCleanTallySXML(xml_str)
            xml_str = xml_str + "\n" + strDicountXml.get("xmlContent", "")
            CVedansh._msTallyStatus = "Success"
            dictXMLResponse['xmlContent'] = xml_str
            dictXMLResponse['TallyStatus'] = CVedansh._msTallyStatus
            dictXMLResponse['AVComments'] = CVedansh._msStrAccuVelocityComments
            await CLogController.MSWriteLog(iUserId, "Info", "XML File Created")
            return dictXMLResponse

        except Exception as exc:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", exc)
            CVedansh._msTallyStatus = "Skipped"
            CVedansh._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CVedansh._msStrAccuVelocityComments = CVedansh._msStrAccuVelocityComments if "ValidationError" in CVedansh._msStrAccuVelocityComments else "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CVedansh._msTallyStatus
            dictXMLResponse["AVComments"] =  CVedansh._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CVedansh._mStrTracebackLogs
        return dictXMLResponse
    
    @staticmethod
    async def mTransactionMapping(iUserId, strTransactionType: str, strClass: str) -> str:
        """
        Maps transaction type and class to a specific string based on given conditions.
        
        :param strTransactionType: The type of transaction (Cash, UPI, Check, Online, etc.)
        :param strClass: The class input, which should ideally start with an integer.
        :return: A mapped string based on the conditions.
        """
        # Check if strClass starts with an integer
        if re.match(r'^[0-9]+', strClass):
            iClassNumber = int(re.match(r'^[0-9]+', strClass).group())
        else:
            # Automatically call the else part for the respective transaction type
            if strTransactionType.lower() == "cash":
                return "J New Cash"
            elif strTransactionType.lower() in ["upi", "cheque"]:
                return "HDFC BANK J NEW 3852"
            elif strTransactionType.lower() == "online":
                return "Kotak Mahindra Bank-0893 J Fee"
            else:
                await CLogController.MSWriteLog(CVedansh._miUserId, "Error", "ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally") 
                CVedansh._mStrTracebackLogs = "TransactionMapping:\n" + str(traceback.format_exc())
                CVedansh._msStrAccuVelocityComments += "ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally\n"
                raise ValueError("ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally") 
        
        # Determine mapping based on transaction type and class
        if strTransactionType.lower() == "cash":
            return "S New Cash" if 1 <= iClassNumber <= 12 else "J New Cash"
        
        elif strTransactionType.lower() in ["upi", "cheque"]:
            return "HDFC BANK S NEW 3648" if 1 <= iClassNumber <= 12 else "HDFC BANK J NEW 3852"
        
        elif strTransactionType.lower() == "online":
            return "Kotak Mahindra Bank-0879 S Fee" if 1 <= iClassNumber <= 12 else "Kotak Mahindra Bank-0893 J Fee"
        
        else:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", "ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally") #TODO: ask nisarg why its selected this ledger ?
            CVedansh._mStrTracebackLogs = "TransactionMapping:\n" + str(traceback.format_exc())
            CVedansh._msStrAccuVelocityComments +="ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally\n"
            raise ValueError("ValidationError Tally XML: Your entry doesn't match any of the approved accounts — 'J New Cash', 'HDFC BANK J NEW 3852', or 'Kotak Mahindra Bank-0893 J Fee'. Please Enter it Manually In Tally") 

    @staticmethod
    async def MSCleanTallySXML(xml_str: str) -> str:
    # Parse the XML string
        try:
            root = ET.fromstring(xml_str)

            # Define tags to remove (with or without namespaces)
            tags_to_remove = [
                "REFERENCE",
                "VCHENTRYMODE",
                "VATEXPAMOUNT",
                "BASICBASEPARTYNAME",
                "BASICBUYERNAME",
                "PLACEOFSUPPLY",
                "PARTYNAME",
                "PARTYMAILINGNAME",
                "CONSIGNEEMAILINGNAME",
                "CONSIGNEESTATENAME",
                "CONSIGNEECOUNTRYNAME",
                "REFERENCEDATE"
            ]

            # Remove all matching tags in the tree
            for tag in tags_to_remove:
                for elem in root.findall(f".//{tag}"):
                    parent = root.find(f".//{tag}/..")
                    if parent is not None:
                        parent.remove(elem)

            # Remove BASICBUYERADDRESS.LIST and its children
            for elem in root.findall(".//BASICBUYERADDRESS.LIST"):
                parent = root.find(".//BASICBUYERADDRESS.LIST/..")
                if parent is not None:
                    parent.remove(elem)

            # Modify ISCOSTCENTRE tag value to No
            for elem in root.findall(".//ISCOSTCENTRE"):
                elem.text = "No"
            
            for elem in root.findall(".//ISCOMMONPARTY"):
                elem.text = "No"
            # Convert back to string
            cleaned_xml = ET.tostring(root, encoding='unicode')
            return cleaned_xml
        except Exception as e:
            await CLogController.MSWriteLog(CVedansh._miUserId, "Error", e)
            CVedansh._mStrTracebackLogs = "MSCleanTallySXML:\n" + str(traceback.format_exc())
            CVedansh._msStrAccuVelocityComments = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n"
            raise

async def main():
    api_response_dir=r"D:\Nisarg\IndianInvoice\data\apiResponses\Vedansh New Format Fee Receipt"
    output_dir=r"GitIgnore\Vedansh New Format Fee Receipt"
    
    for filename in os.listdir(api_response_dir):
        try:
            if filename.endswith("_gptResponse.json"):
                #
                json_file_path = os.path.join(api_response_dir, filename)
                
            
                with open(json_file_path, "r") as file:
                    api_response = json.load(file)
                
            
                content = api_response['choices'][0]['message']['content']
                content = json.loads(content)
                
                XmlOutput =await CVedansh.MSCreateXML(1,content)
                
            
                invoice_no = content.get("InvoiceNo", "NA")
                invoice_no = invoice_no.replace("/", "_")
                
                
                xml_file_name = f"Invoiceno_{invoice_no}_xmlfile.xml"
                xml_file_path = os.path.join(output_dir, xml_file_name)
                
                
                
                with open(xml_file_path, "w") as xml_file:
                    xml_file.write(XmlOutput.get("xmlContent"))
        except Exception as e:
            print("Error Occur - ", traceback.format_exc())        



if __name__ == "__main__":
    # asyncio.run(main())
    with open(r"Data\Customer\VedanshSchool\TallyLedgerConfig.json") as f:
        dictLedgerConfig = json.load(f)
    print(CSimilarityHelper.MSFindNearestMatch(dictLedgerConfig = dictLedgerConfig, strInputText="HEER RAHUL ARYA "))
