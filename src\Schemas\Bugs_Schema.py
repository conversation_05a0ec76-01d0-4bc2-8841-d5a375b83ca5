from typing import Optional,List
from pydantic import BaseModel
from datetime import datetime
from fastapi import Form
from fastapi import APIRouter, Depends,UploadFile, File

class BugReport(BaseModel):
    BugTime: Optional[datetime] = Form(None)
    Browser: Optional[str] = Form(None)
    OperatingSystem: Optional[str] = Form(None)
    AppVersion: Optional[str] = Form(None)
    Description: str = Form(...),
    CreatedDateTime: Optional[datetime] = Form(None)
    updated_at: Optional[datetime] = Form(None)