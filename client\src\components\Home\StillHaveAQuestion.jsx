import {
  useToast,
  useState,
} from "react";
import SectionTitle from "./SectionTitle";
import emailIcon from "../../assets/mail.svg";
import phoneIcon from "../../assets/phone.svg";
import youtubeIcon from "../../assets/youtubeIcon.svg";
import twitterXIcon from "../../assets/twitterXIcon.svg";
import linkedInIcon from "../../assets/linkedInIcon.svg";
import facebookIcon from "../../assets/facebookIcon.svg";
import instagramIcon from "../../assets/instagramIcon.svg";
import { FAQS_DATA } from "../../configs/faqs";
import TextInput from "../Inputs/TextInput";
import PhoneInput, { parsePhoneNumber } from "react-phone-number-input";
import toast from 'react-hot-toast';
import { validateEmail } from "../../utils/misc";
import axios from 'axios'

const SOCIAL_MEDIA_LINKS = [
  {
    title: "LinkedIn",
    link: "https://www.linkedin.com/company/accuvelocity/posts/?feedView=all",
    icon: <img src={linkedInIcon} alt="LinkedIn" />,
  },
  {
    title: "Twitter / X",
    link: "https://x.com/AccuVelocity",
    icon: <img src={twitterXIcon} alt="TwitterX" />,
  },
  {
    title: "Facebook",
    link: "https://www.facebook.com/people/AccuVelocity/61558441675742/",
    icon: <img src={facebookIcon} alt="Facebook" />,
  },
  {
    title: "Instagram",
    link: "https://www.instagram.com/AccuVelocity",
    icon: <img src={instagramIcon} alt="Instagram" />,
  },
  {
    title: "Youtube",
    link: "https://www.youtube.com/channel/UCZzHEIfdg9Zp8vqdbDyQZ7g",
    icon: <img src={youtubeIcon} alt="YouTube" />,
  },
];

const FAQ_TYPE = [
  {
    id: 1,
    title: "Process",
  },
  {
    id: 2,
    title: "Price",
  },
  {
    id: 3,
    title: "Automation",
  },
  {
    id: 4,
    title: "Other",
  },
];

const StillHaveAQuestion = () => {
  const [faqType, setFaqType] = useState(Object.keys(FAQS_DATA)[0]);
  const [showLoader, setShowLoader] = useState(false);
  const [showThankYou, setShowThankYou] = useState(false);
  const [inquiryFormData, setInquiryFormData] = useState({
    name: "",
    email: "",
    designation: "",
    company: "",
    message: "",
    categoryIcon: [],
  });
  let [phoneNumber, setPhoneNumber] = useState("");
  let [errorObj, setErrorObj] = useState({});

  const handleChangeBusinessDetailsData = (key, value) => {
    setInquiryFormData({
      ...inquiryFormData,
      [key]: value,
    });
  };

  const validate = () => {
    const errors = {};
    if (!inquiryFormData.name?.trim() || inquiryFormData.name.length < 3 || inquiryFormData.name.length > 100) {
      errors.name = "A user name must have more than 3 characters or less than 100 characters";
    }
  
    if ( inquiryFormData.designation.length > 50) {
      errors.designation = "A user designation must be less than 50 characters";
    }
  
    if ( inquiryFormData.company.length > 100) {
      errors.company = "A user company must be less than 100 characters";
    }
  
    if ( inquiryFormData.message.length > 500) {
      errors.message = "A user message must be less than 500 characters";
    }
  
    if (!validateEmail(inquiryFormData.email)) {
      errors.email = "A user must have a valid email";
    }

    // Validate phone number
  try {
    const parsedNumber = parsePhoneNumber(phoneNumber);
    if (!parsedNumber.isValid()) {
      errors.whatsAppNumber = "A user must have a valid phone number";
    }
  } catch {
    errors.whatsAppNumber = "A user must have a valid phone number";
  }
  
    setErrorObj(errors);
    return errors;
  };

  const submitData = async () => {
    setShowLoader(true);

    // Validate fields
    const errors = validate();
    // Check if phone number is provided
    // if (!phoneNumber) {
    //   errors.phoneNumber = "Phone number is required";
    // }

    // Check if any errors exist
    if (Object.keys(errors).length > 0) {
      setErrorObj(errors);
      setShowLoader(false);
      toast.error("Please fill out the required fields")
      return;
    }

    try {
      // Extract country and phone number details
      const parsedNumber = parsePhoneNumber(phoneNumber);
      const country = parsedNumber.country;
      const phone = parsedNumber.number;
  
      const response = await axios.post(
        `${import.meta.env.VITE_SERVER}/contact-us`,
        {
          query_category: faqType,
          customer_name: inquiryFormData.name,
          email: inquiryFormData.email,
          phone_number: phone,
          country: country,
          designation: inquiryFormData.designation,
          company_name: inquiryFormData.company,
          message: inquiryFormData.message,
        }
      );
  
      setShowThankYou(true);
      toast.success("Message sent successfully!");
    } catch (error) {
      toast.error("Failed to send message");
    }
  
    setShowLoader(false);
  };

  return (
    <div className="pt-2 mb-10 lg:mb-20 xl:mb-36" id="StillHaveAQuestion">
      <SectionTitle title={"Still have a question?"} />
      <p className="font-normal text-center text-gray-600 mx-auto max-w-[90vw] md:max-w-[60vw] leading-5 md:leading-6 lg:leading-6 xl:leading-7">
        If you cannot find answer what you were looking in our FAQs.
      </p>
      <p className="font-normal text-center text-gray-600 mx-auto max-w-[90vw] md:max-w-[60vw] leading-5 md:leading-6 lg:leading-6 xl:leading-7">
        Write to us at{" "}
        <a href="mailto:<EMAIL>" className="text-blue-400">
          <EMAIL>
        </a>
      </p>
      <div className="bg-blue-900 rounded-xl leading-5 mt-5 md:mt-8 lg:mt-10 xl:mt-12 p-5 md:p-8 lg:p-10 xl:p-12">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-5 md:gap-8 lg:gap-10 xl:gap-12">
          <div className="flex flex-col justify-center pt-0 md:pt-10 lg:pt-20 pb-5 md:pb-8 lg:pb-10 xl:pb-12">
            <p className="text-white font-light text-2xl md:text-3xl lg:text-4xl leading-10 md:leading-12 lg:leading-14">
              Let’s talk on
            </p>
            <p className="text-white font-bold text-2xl md:text-3xl lg:text-4xl leading-10 md:leading-12 lg:leading-14">
              something great
            </p>
            <p className="text-white font-light text-2xl md:text-3xl lg:text-4xl leading-10 md:leading-12 lg:leading-14">
              together
            </p>
            <div className="mt-5 md:mt-8 lg:mt-12 flex items-center">
              <img className="mr-5" src={emailIcon} alt="Email" />
              <a href="mailto:<EMAIL>" className="text-lg text-white">
                <EMAIL>
              </a>
            </div>
            <div className="mt-5 md:mt-8 lg:mt-12 flex items-center">
              <img className="mr-5" src={phoneIcon} alt="Phone" />
              <a href="tel:+34123456789" className="text-lg text-white">
                +34 123 456 789
              </a>
            </div>
            <p className="mt-5 md:mt-8 lg:mt-12 text-lg font-semibold text-white leading-6">
              For the latest updates,
            </p>
            <p className="text-lg text-white">
              follow us on...
            </p>
            <div className="flex items-center gap-2.5 mt-2.5 md:mt-5 lg:mt-7 xl:mt-9">
              {SOCIAL_MEDIA_LINKS.map((item) => {
                return (
                  <div key={item.title} className="mb-2">
                    <a
                      href={item.link}
                      className="hover:text-blue-300 transition-colors duration-200"
                      target="_blank"
                      rel="noopener noreferrer"
                    >
                      {item.icon}
                    </a>
                  </div>
                );
              })}
            </div>
          </div>
          {showThankYou && (
            <div className="bg-white rounded-lg p-5 pb-15 grid place-items-center">
              <div>
                <p className="font-extrabold text-blue-900 text-center text-6xl leading-tight">
                  Thank You!
                </p>
                <p className="font-medium text-blue-900 text-center mt-7 text-lg">
                  {"We've received your message and our experts will be in touch within 48 hours to assist you."}
                </p>
                <p className="font-medium text-blue-900 text-center mt-7 text-lg">
                  In the meantime, you can explore our{" "}
                  <span className="font-extrabold">
                    FAQ section{" "}
                  </span>
                  or browse our{" "}
                  <span className="font-extrabold">
                    blogs{" "}
                  </span>
                  for helpful information that might answer your question
                </p>
                <p className="font-semibold text-blue-900 text-center mt-7 text-lg">
                  {"You will also receive a confirmation email with a ticket number for your reference."}
                </p>
              </div>
            </div>
          )}
          {!showThankYou && (
            <div className="bg-white rounded-lg p-5 pb-15">
              <p className="font-medium text-blue-900 text-lg">
                Need help in :<span className="text-red-500">*</span>
              </p>
              <div className="mt-2.5 mb-3.5">
                {FAQ_TYPE.map((type) => {
                  return (
                    <button
                      key={type.id}
                      className={`mx-1 mb-2.5 px-4 py-2.5 rounded-full border border-blue-900 ${faqType === type.title ? "bg-blue-900 text-white" : "text-blue-900"}`}
                      onClick={() => setFaqType(type.title)}
                    >
                      {type.title}
                    </button>
                  );
                })}
              </div>
              <div className="mb-6">
                <p className="font-medium text-blue-900 text-lg">
                  Your name<span className="text-red-500">*</span>
                </p>
                <TextInput
                  placeholder="Name"
                  value={inquiryFormData.name}
                  onChange={(e) =>
                    handleChangeBusinessDetailsData("name", e.target.value)
                  }
                />
                {errorObj.name && (
                  <p className="text-red-500 mb-1.5">
                    {errorObj.name}
                  </p>
                )}
              </div>
              <div className="mb-6">
                <p className="font-medium text-blue-900 text-lg">
                  Email<span className="text-red-500">*</span>
                </p>
                <TextInput
                  placeholder="<EMAIL>"
                  value={inquiryFormData.email}
                  onChange={(e) =>
                    handleChangeBusinessDetailsData("email", e.target.value)
                  }
                />
                {errorObj.email && (
                  <p className="text-red-500 mb-1.5">
                    {errorObj.email}
                  </p>
                )}
              </div>
              <div className="grid grid-cols-2 gap-5">
                <div className="mb-6">
                  <p className="font-medium text-blue-900 text-lg">
                    Phone<span className="text-red-500">*</span>
                  </p>
                  <div className="relative">
                    <PhoneInput
                      international
                      className="phoneInput"
                      name="whatsAppNumber"
                      placeholder="Enter Your Whatsapp Number"
                      defaultCountry="IN"
                      value={phoneNumber}
                      onChange={(value) => {
                        setPhoneNumber(value);
                      }}
                    />
                  </div>
                  {errorObj.whatsAppNumber && (
                    <p className="text-red-500 mb-1.5">
                      {errorObj.whatsAppNumber}
                    </p>
                  )}
                </div>
                <div className="mb-6">
                  <p className="font-medium text-blue-900 text-lg">
                    Designation
                  </p>
                  <TextInput
                    placeholder="Designation"
                    value={inquiryFormData.designation}
                    onChange={(e) =>
                      handleChangeBusinessDetailsData(
                        "designation",
                        e.target.value
                      )
                    }
                  />
                  {errorObj.designation && (
                    <p className="text-red-500 mb-1.5">
                      {errorObj.designation}
                    </p>
                  )}
                </div>
              </div>
              <div className="mb-6">
                <p className="font-medium text-blue-900 text-lg">
                  Company
                </p>
                <TextInput
                  placeholder="Xyz.inc"
                  value={inquiryFormData.company}
                  onChange={(e) =>
                    handleChangeBusinessDetailsData("company", e.target.value)
                  }
                />
                {errorObj.company && (
                  <p className="text-red-500 mb-1.5">
                    {errorObj.company}
                  </p>
                )}
              </div>
              <div className="mb-6">
                <p className="font-medium text-blue-900 text-lg mb-2.5">
                  Your Message
                </p>
                <textarea
                  className="w-full h-32 border-2 border-gray-300 rounded-lg focus:outline-none focus:border-blue-900 p-2.5"
                  placeholder="Designation"
                  value={inquiryFormData.message}
                  onChange={(e) =>
                    handleChangeBusinessDetailsData("message", e.target.value)
                  }
                />
                {errorObj.message && (
                  <p className="text-red-500 mb-1.5">
                    {errorObj.message}
                  </p>
                )}
              </div>
              <button
                onClick={() => submitData()}
                className="w-full h-11 bg-blue-900 text-white rounded-full"
              >
                {showLoader ? <div className="spinner-border text-sm" role="status"><span className="sr-only">Loading...</span></div> : "Send message"}
              </button>
            </div>
          )}
        </div>
      </div>
    </div>
  );
  
};

export default StillHaveAQuestion;
