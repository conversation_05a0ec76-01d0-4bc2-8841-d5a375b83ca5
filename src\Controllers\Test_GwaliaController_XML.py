import sys
sys.path.append("")

from src.Controllers.GwaliaController_XML import CGwalia, CBhavyaSales, CKarnavati, CSheebaDairy, CSygnia
import requests
import json
import os 

class CTallyImportHelper:

    @staticmethod
    def MSExportTallyData(strRequestXMLPath, strResponseXMLPath="response.xml", url="http://192.168.1.25:10049/"):
        """
        Reads an XML file and sends it to the specified Tally server URL.
        Saves the server response to an XML file.

        Args:
        strRequestXMLPath (str): The path to the XML file to send.
        url (str): The Tally server URL.
        strResponseXMLPath (str): The path where the response XML will be saved.

        Returns:
        str: Confirmation message or error details.
        """
        try:
            strFileName =os.path.basename(strResponseXMLPath) 
            # Read XML content from file
            with open(strRequestXMLPath, "r", encoding="utf-8") as file:
                xml_data = file.read()

            # Define headers
            headers = {
                "Content-Type": "text/xml",
                "Cache-Control": "no-cache"
            }

            # Send POST request
            response = requests.post(url, headers=headers, data=xml_data)

            # Save the response to an XML file
            with open(strResponseXMLPath, "w", encoding="utf-8") as file:
                file.write(response.text)

            print(f"Response saved to {strResponseXMLPath}")

        except FileNotFoundError:
            print("Error: The specified file was not found.")
        except requests.exceptions.RequestException as e:
            print(f"Error: Unable to send data to the server. {str(e)}")
        except IOError as e:
            print(f"Error: Unable to save the response file. {str(e)}")
        except Exception as GenError:
            print(f"Failed to Export the data,  error: {str(GenError)}")


class CTestGwaliaXML:
    
    @staticmethod
    def MS_GPT_Extracted_Data_To_Tally_Import(directory_path, vendor_name):
        """
        Iterates through all subdirectories, reads JSON files, converts them to XML, 
        saves them in 'tallyxml' folder, and calls `MSExportTallyData()`.
        """
        for root, _, files in os.walk(directory_path):
            for file in files:
                if file.endswith(".json"):
                    json_file_path = os.path.join(root, file)

                    # Read JSON data
                    with open(json_file_path, "r") as json_file:
                        json_data = json.load(json_file)

                    # Ensure vendor matches (optional)
                    if vendor_name.lower() == "karnavati":
                        strXMLData = CKarnavati.MSCreateXML(json_data)

                    elif vendor_name.lower() == "sheeba":
                        strXMLData = CSheebaDairy.MSCreateXML(json_data)

                    elif vendor_name.lower() == "bhavyasales":
                        strXMLData = CBhavyaSales.MSCreateXML(json_data)
                        
                    elif vendor_name.lower() == "sygnia":
                        strXMLData = CSygnia.MSCreateXML(json_data)

                    else:
                        print(f"Vendor '{vendor_name}' is not supported.")
                        return

                    # Create 'tallyxml' directory if not exists
                    tallyxml_dir = os.path.join(root, "TallyXML")
                    os.makedirs(tallyxml_dir, exist_ok=True)

                    # Save XML file in 'tallyxml' folder
                    xml_file_name = os.path.splitext(file)[0] + ".xml"
                    response_file_name = os.path.splitext(file)[0] + "_Response.xml"
                    xml_file_path = os.path.join(tallyxml_dir, xml_file_name)

                    with open(xml_file_path, "w", encoding="utf-8") as xml_file:
                        xml_file.write(strXMLData)

                    print(f"Saved XML: {xml_file_path}")

                    # Call `MSExportTallyData()` to send XML to Tally
                    CTallyImportHelper.MSExportTallyData(xml_file_path, response_file_name)

if __name__ == "__main__":
    
    strExtracteDataDirPath = r"H:\AI Data\26_Gwalia\16_Sygnia\gptResponse\ContentOnly"
    strVendorName = "sygnia"
    CTestGwaliaXML.MS_GPT_Extracted_Data_To_Tally_Import(directory_path= strExtracteDataDirPath, vendor_name=strVendorName)
    