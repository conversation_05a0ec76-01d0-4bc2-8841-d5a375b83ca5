<?xml version="1.0" ?>
<ENVELOPE>
    <HEADER>
        <TALLYREQUEST>Import Data</TALLYREQUEST>
    </HEADER>
    <BODY>
        <IMPORTDATA>
            <REQUESTDESC>
                <REPORTNAME>Vouchers</REPORTNAME>
                <STATICVARIABLES>
                    <SVCURRENTCOMPANY>Abhinav Infrabuild Pvt.Ltd.(24-26)</SVCURRENTCOMPANY>
                </STATICVARIABLES>
            </REQUESTDESC>
            <REQUESTDATA>
                <TALLYMESSAGE xmlns:UDF="TallyUDF">
                    <VOUCHER VCHTYPE="AV-GRN" ACTION="Create" OBJVIEW="Invoice Voucher View">
                        <ADDRESS.LIST TYPE="String">
                            <ADDRESS>273/2/2, Chakh Kamed, Agar Road</ADDRESS>
                            <ADDRESS>Ujjain</ADDRESS>
                        </ADDRESS.LIST>
                        <BASICBUYERADDRESS.LIST TYPE="String">
                            <BASICBUYERADDRESS>207-208 Industry House,A.B.Road Indore(M.P.)</BASICBUYERADDRESS>
                            <BASICBUYERADDRESS>Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)</BASICBUYERADDRESS>
                            <BASICBUYERADDRESS>E-Mail : <EMAIL></BASICBUYERADDRESS>
                        </BASICBUYERADDRESS.LIST>
                        <DATE>20250702</DATE>
                        <REFERENCEDATE>20250702</REFERENCEDATE>
                        <VCHSTATUSDATE>20250702</VCHSTATUSDATE>
                        <EFFECTIVEDATE>20250702</EFFECTIVEDATE>
                        <STATENAME>Madhya Pradesh</STATENAME>
                        <COUNTRYOFRESIDENCE>India</COUNTRYOFRESIDENCE>
                        <PARTYGSTIN/>
                        <PLACEOFSUPPLY>Madhya Pradesh</PLACEOFSUPPLY>
                        <PARTYNAME>Smart Safety Co.</PARTYNAME>
                        <GSTREGISTRATION TAXTYPE="GST" TAXREGISTRATION="23AAHCA9425D1ZY">Madhya Pradesh Registration</GSTREGISTRATION>
                        <CMPGSTIN>23AAHCA9425D1ZY</CMPGSTIN>
                        <NARRATION>Party Bill No: (ssc/2526/524)</NARRATION>
                        <VOUCHERTYPENAME>AV-GRN</VOUCHERTYPENAME>
                        <PARTYLEDGERNAME>Smart Safety Co.</PARTYLEDGERNAME>
                        <VOUCHERNUMBER>BPMV-292</VOUCHERNUMBER>
                        <REFERENCE>BPMV-292</REFERENCE>
                        <BASICBUYERNAME>Abhinav Infrabuild Pvt.Ltd.</BASICBUYERNAME>
                        <CMPGSTREGISTRATIONTYPE>Regular</CMPGSTREGISTRATIONTYPE>
                        <PARTYMAILINGNAME>Smart Safety Co.</PARTYMAILINGNAME>
                        <CONSIGNEEGSTIN>23AAHCA9425D1ZY</CONSIGNEEGSTIN>
                        <CONSIGNEEMAILINGNAME>Abhinav Infrabuild Pvt.Ltd.</CONSIGNEEMAILINGNAME>
                        <CONSIGNEEPINCODE>452001</CONSIGNEEPINCODE>
                        <CONSIGNEESTATENAME>Madhya Pradesh</CONSIGNEESTATENAME>
                        <CMPGSTSTATE>Madhya Pradesh</CMPGSTSTATE>
                        <CONSIGNEECOUNTRYNAME>India</CONSIGNEECOUNTRYNAME>
                        <BASICBASEPARTYNAME>Smart Safety Co.</BASICBASEPARTYNAME>
                        <NUMBERINGSTYLE>Auto Retain</NUMBERINGSTYLE>
                        <FBTPAYMENTTYPE>Default</FBTPAYMENTTYPE>
                        <PERSISTEDVIEW>Invoice Voucher View</PERSISTEDVIEW>
                        <VCHSTATUSTAXADJUSTMENT>Default</VCHSTATUSTAXADJUSTMENT>
                        <VCHSTATUSVOUCHERTYPE>AV-GRN</VCHSTATUSVOUCHERTYPE>
                        <VCHSTATUSTAXUNIT>Madhya Pradesh Registration</VCHSTATUSTAXUNIT>
                        <COSTCENTRENAME>BPMV</COSTCENTRENAME>
                        <DIFFACTUALQTY>No</DIFFACTUALQTY>
                        <ISOPTIONAL>No</ISOPTIONAL>
                        <ISINVOICE>No</ISINVOICE>
                        <HASDISCOUNTS>No</HASDISCOUNTS>
                        <ISCOSTCENTRE>Yes</ISCOSTCENTRE>
                        <VOUCHERNUMBERSERIES>Default</VOUCHERNUMBERSERIES>
                        <BASICDUEDATEOFPYMT>45 Days</BASICDUEDATEOFPYMT>
                        <INVOICEORDERLIST.LIST>
                            <BASICORDERDATE>********</BASICORDERDATE>
                            <ORDERTYPE>Purchase Order</ORDERTYPE>
                            <BASICPURCHASEORDERNO>BPMV-85</BASICPURCHASEORDERNO>
                        </INVOICEORDERLIST.LIST>
                        <ALLINVENTORYENTRIES.LIST>
                            <BASICUSERDESCRIPTION.LIST TYPE="String">
                                <BASICUSERDESCRIPTION>Item: RAIN COAT LABOUR | Quantity: 15.0 Nos | Rate: 250.00/Nos | Amount: 3750.0</BASICUSERDESCRIPTION>
                            </BASICUSERDESCRIPTION.LIST>
                            <STOCKITEMNAME>Raincoat (Labour)</STOCKITEMNAME>
                            <GSTOVRDNTAXABILITY>Taxable</GSTOVRDNTAXABILITY>
                            <GSTSOURCETYPE>Stock Item</GSTSOURCETYPE>
                            <GSTITEMSOURCE>Raincoat (Labour)</GSTITEMSOURCE>
                            <HSNSOURCETYPE>Stock Item</HSNSOURCETYPE>
                            <HSNITEMSOURCE>Raincoat (Labour)</HSNITEMSOURCE>
                            <GSTOVRDNTYPEOFSUPPLY>Goods</GSTOVRDNTYPEOFSUPPLY>
                            <GSTRATEINFERAPPLICABILITY>As per Masters/Company</GSTRATEINFERAPPLICABILITY>
                            <GSTHSNNAME/>
                            <GSTHSNINFERAPPLICABILITY>As per Masters/Company</GSTHSNINFERAPPLICABILITY>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <RATE>250.00/Nos.</RATE>
                            <AMOUNT>-3750.00</AMOUNT>
                            <ACTUALQTY>15.0 Nos.</ACTUALQTY>
                            <BILLEDQTY>15.0 Nos.</BILLEDQTY>
                            <BATCHALLOCATIONS.LIST>
                                <GODOWNNAME>Main Location</GODOWNNAME>
                                <BATCHNAME>Primary Batch</BATCHNAME>
                                <ORDERNO>BPMV-85</ORDERNO>
                                <TRACKINGNUMBER>BPMV-292</TRACKINGNUMBER>
                                <AMOUNT>-3750.00</AMOUNT>
                                <ACTUALQTY>15.0 Nos.</ACTUALQTY>
                                <BILLEDQTY>15.0 Nos.</BILLEDQTY>
                                <ORDERDUEDATE>********</ORDERDUEDATE>
                            </BATCHALLOCATIONS.LIST>
                            <ACCOUNTINGALLOCATIONS.LIST>
                                <LEDGERNAME>Purchase - GST - Contract</LEDGERNAME>
                                <GSTCLASS>Not Applicable</GSTCLASS>
                                <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                <AMOUNT>-3750.00</AMOUNT>
                                <CATEGORYALLOCATIONS.LIST>
                                    <CATEGORY>Primary Cost Category</CATEGORY>
                                    <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                    <COSTCENTREALLOCATIONS.LIST>
                                        <NAME>BPMV</NAME>
                                        <AMOUNT>-3750.00</AMOUNT>
                                    </COSTCENTREALLOCATIONS.LIST>
                                </CATEGORYALLOCATIONS.LIST>
                            </ACCOUNTINGALLOCATIONS.LIST>
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>CGST</GSTRATEDUTYHEAD>
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>
                                <GSTRATE>9.0</GSTRATE>
                            </RATEDETAILS.LIST>
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>SGST/UTGST</GSTRATEDUTYHEAD>
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>
                                <GSTRATE>9.0</GSTRATE>
                            </RATEDETAILS.LIST>
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>IGST</GSTRATEDUTYHEAD>
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>
                                <GSTRATE>18.0</GSTRATE>
                            </RATEDETAILS.LIST>
                        </ALLINVENTORYENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <LEDGERNAME>Smart Safety Co.</LEDGERNAME>
                            <GSTCLASS>Not Applicable</GSTCLASS>
                            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
                            <AMOUNT>3750.00</AMOUNT>
                            <VATEXPAMOUNT>3750.00</VATEXPAMOUNT>
                            <CATEGORYALLOCATIONS.LIST>
                                <CATEGORY>Primary Cost Category</CATEGORY>
                                <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>
                                <COSTCENTREALLOCATIONS.LIST>
                                    <NAME>BPMV</NAME>
                                    <AMOUNT>3750.00</AMOUNT>
                                </COSTCENTREALLOCATIONS.LIST>
                            </CATEGORYALLOCATIONS.LIST>
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <LEDGERNAME>Round off</LEDGERNAME>
                            <GSTCLASS>Not Applicable</GSTCLASS>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <AMOUNT>-0.00</AMOUNT>
                            <VATEXPAMOUNT>-0.00</VATEXPAMOUNT>
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <LEDGERNAME>CGST</LEDGERNAME>
                            <GSTCLASS>Not Applicable</GSTCLASS>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <AMOUNT>-337.50</AMOUNT>
                            <VATEXPAMOUNT>-337.50</VATEXPAMOUNT>
                            <CATEGORYALLOCATIONS.LIST>
                                <CATEGORY>Primary Cost Category</CATEGORY>
                                <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                <COSTCENTREALLOCATIONS.LIST>
                                    <NAME>BPMV</NAME>
                                    <AMOUNT>-337.50</AMOUNT>
                                </COSTCENTREALLOCATIONS.LIST>
                            </CATEGORYALLOCATIONS.LIST>
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <LEDGERNAME>SGST</LEDGERNAME>
                            <GSTCLASS>Not Applicable</GSTCLASS>
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                            <AMOUNT>-337.50</AMOUNT>
                            <VATEXPAMOUNT>-337.50</VATEXPAMOUNT>
                            <CATEGORYALLOCATIONS.LIST>
                                <CATEGORY>Primary Cost Category</CATEGORY>
                                <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>
                                <COSTCENTREALLOCATIONS.LIST>
                                    <NAME>BPMV</NAME>
                                    <AMOUNT>-337.50</AMOUNT>
                                </COSTCENTREALLOCATIONS.LIST>
                            </CATEGORYALLOCATIONS.LIST>
                        </LEDGERENTRIES.LIST>
                    </VOUCHER>
                </TALLYMESSAGE>
            </REQUESTDATA>
        </IMPORTDATA>
    </BODY>
</ENVELOPE>
