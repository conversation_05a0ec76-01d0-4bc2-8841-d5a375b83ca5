import { useState, useEffect } from 'react';
import logo from '../../assets/logo.svg'; // Ensure the logo is correctly located
import { IoIosArrowDown as DownArrowIcon } from 'react-icons/io'; // Import down arrow icon from react-icons
import axios from 'axios';
import { useIsUserInitializedValue, useIsUserInitialized} from "../../context/userData";

export default function CompanyDetails() {
    const [designation, setDesignation] = useState('');
    const [companyName, setCompanyName] = useState('');
    const [companyURL, setCompanyURL] = useState('');
    const [isButtonDisabled, setIsButtonDisabled] = useState(false);
    const [error, setError] = useState('');
    const [teamStrength, setTeamStrength] = useState('');
    const [invoicesProcessed, setInvoicesProcessed] = useState('');
    const userId = localStorage.getItem("userId");
    const isUserInitializedValue = useIsUserInitializedValue();
    const setIsUserInitialized = useIsUserInitialized();

    useEffect(() => {
        const initializeUserData = async () => {
            
            try {
                const formData = new FormData();
                formData.append("iUserID", userId);

                await axios.post(`${import.meta.env.VITE_SERVER}/initialize`, formData);
                setIsUserInitialized(true);
            } catch (error) {
                console.error('User Registration Failed, Please try again later');
            }
        };

        if (!isUserInitializedValue) {
            initializeUserData();
        }
    }, []);
    useEffect(() => {
        console.log("companyURL", companyURL);
        if (companyURL === null || companyURL === undefined || companyURL.trim() === "") {
            setIsButtonDisabled(false);
        }
    }, [companyURL]);
    const handleSubmit = async (e) => {
        e.preventDefault();
        let formattedDesignation = designation.trim();
        let formattedCompanyName = companyName.trim();

        if (formattedDesignation.length === 0) {
            alert("Please enter your designation. This field cannot be empty.");
            return;
        }
        else if (formattedCompanyName.length === 0) {
            alert("Please enter your company name. This field cannot be empty.");
            return;
        }


        // Collect form data
        const formData = new FormData();
        formData.append("user_id", userId); // Include user_id in the form data
        formData.append("strDesignation", designation);
        formData.append("company_name", companyName);
        formData.append("company_url", companyURL);
        formData.append("team_strength", teamStrength);
        formData.append("invoices_processed_per_day", invoicesProcessed);

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/register_organization`, formData);
            if (response.status === 200) {
                localStorage.setItem("Authtoken", localStorage.getItem("NotOrgDetailsToken"));
                localStorage.removeItem("NotOrgDetailsToken");

                localStorage.removeItem("userId");

                // Verify deletion
                const isTokenDeleted = !localStorage.getItem("NotOrgDetailsToken");

                if (isTokenDeleted) {
                    window.location.href = '/WelcomePage';

                } else {
                    console.error("Failed to delete 'NotOrgDetailsToken'");
                }
            }
        } catch (error) {
            alert("Please select mandatory fields ")
            console.error('Error registering organization:', error);
        }
    };
    const validateURL = (url) => {
        const regex = /^(https?:\/\/)?([\da-z.-]+)\.([a-z.]{2,6})([/\w .-]*)*\/?$/;
        return regex.test(url);
    };
    const handleChange = (e) => {
        const url = e.target.value;
        if (!validateURL(url) && url.trim()!=="") {
            setError('Please enter a valid URL.');
            setIsButtonDisabled(true);
          } else {
            setError('');
            setIsButtonDisabled(false);
          }
        setCompanyURL(url);
    };

    const handleCompanyNameChange = (Userinput) => {
        let input = Userinput;
    
        // Only allow alphabetic characters, spaces, and numbers
        input = input.replace(/[^a-zA-Z0-9\s]/g, ''); // Remove non-allowed characters
    
        // Limit input to a maximum of 50 characters
        const truncatedInput = input.slice(0, 50);
    
        // Convert to Title Case (Upper Camel Case)
        const titleCasedInput = truncatedInput.replace(/\b\w/g, (char) => char.toUpperCase());
    
        setCompanyName(titleCasedInput);
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#ffff]">
            <form onSubmit={handleSubmit} className="w-full max-w-[25.2rem]">
                <div className="flex justify-center">
                    <img src={logo} alt="AccuVelocity" className="h-12 w-auto mb-6" />
                </div>
                <h2 className="text-2xl font-light text-center mb-2 leading-10">
                    Setup your Account
                </h2>

                <div className="mb-4">
                    <label htmlFor="company-name" className="block text-gray-700 text-sm font-bold mb-2">
                        Your Designation<span className="text-red-500">*</span>
                    </label>
                    <input
                        className="shadow appearance-none border border-gray-400 rounded-lg w-full py-2 px-3 text-[#000] leading-tight focus:outline-none focus:ring-blue-500 focus:shadow-outline focus:border-blue-500"
                        id="designation"
                        type="text"
                        placeholder="Your Designation"
                        value={designation}
                        required
                        onChange={(e) => {
                            setDesignation(e.target.value);
                        }}
                        onInput={(e) => {
                            const input = e.target.value;
                            const lettersOnly = input.replace(/[^a-zA-Z\s]/g, ''); // Allow only letters and spaces
                            if (input !== lettersOnly) {
                                e.target.value = lettersOnly;
                                setDesignation(lettersOnly);
                            }
                        }}
                        maxLength={50} // Enforce a maximum input size of 50 characters
                    />
                </div>

                <div className="mb-4">
                    <label htmlFor="company-name" className="block text-gray-700 text-sm font-bold mb-2">
                        Company Name<span className="text-red-500">*</span>
                    </label>
                    <input
                        className="shadow appearance-none border border-gray-400 rounded-lg w-full py-2 px-3 text-[#000] leading-tight focus:outline-none focus:ring-blue-500 focus:shadow-outline focus:border-blue-500"
                        id="company-name"
                        type="text"
                        placeholder="Company Name"
                        required
                        value={companyName}
                        onChange={(e) => {

                            handleCompanyNameChange(e.target.value)
                        }}
                        maxLength={50} // Enforce a maximum input size of 50 characters
                    />
                </div>
                
                <div className="mb-4">
                    <label htmlFor="company-name" className="block text-gray-700 text-sm font-bold mb-2">
                        Company Website
                    </label>
                    <input
                        className="shadow appearance-none border border-gray-400 rounded-lg w-full py-2 px-3 text-[#000] leading-tight focus:outline-none focus:ring-blue-500 focus:shadow-outline focus:border-blue-500"
                        id="company-url"
                        type="text"
                        placeholder="Company Website"
                        value={companyURL}
                        onChange={handleChange}
                        maxLength={50} // Enforce a maximum input size of 50 characters
                    />
                    {error && <p className="text-red-500 text-xs mt-2">{error}</p>}
                   
                </div>


                <div className="mb-4 relative">
                    <label htmlFor="team-strength" className="block text-gray-700 text-sm font-bold mb-2">
                        Team Strength<span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                        <select
                            className={`block appearance-none w-full border border-gray-400 hover:border-gray-500 px-4 py-2 rounded-lg   shadow leading-tight focus:outline-none focus:shadow-outline focus:border-blue-500 ${teamStrength ? 'text-black' : 'text-gray-500'}`}
                            id="team-strength"
                            required
                            value={teamStrength}
                            onChange={(e) => setTeamStrength(e.target.value)}
                        >
                            <option value="" disabled>Select Team Strength</option>
                            <option value="1-10">1-10 Employees</option>
                            <option value="11-50">11-50 Employees</option>
                            <option value="51-200">51-200 Employees</option>
                            <option value="201-500">201-500 Employees</option>
                            <option value="501-1000">501-1000 Employees</option>
                            <option value="1000+">1000+ Employees</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <DownArrowIcon className="fill-current h-4 w-4" />
                        </div>
                    </div>
                </div>

                <div className="mb-6 relative">
                    <label htmlFor="invoices-processed" className="block text-gray-700 text-sm font-bold mb-2">
                        No of Documents processed in a day<span className="text-red-500">*</span>
                    </label>
                    <div className="relative">
                        <select
                            className={`block appearance-none w-full border border-gray-400 hover:border-gray-500 px-4 py-2 rounded-lg   shadow leading-tight focus:outline-none focus:shadow-outline focus:border-blue-500 ${invoicesProcessed ? 'text-black' : 'text-gray-500'}`}
                            id="invoices-processed"
                            required
                            value={invoicesProcessed}
                            onChange={(e) => setInvoicesProcessed(e.target.value)}
                        >
                            <option value="" disabled>No. of Documents / Day</option>
                            <option value="1-10">1-10</option>
                            <option value="11-50">11-50</option>
                            <option value="51-200">51-200</option>
                            <option value="201-500">201-500</option>
                            <option value="501-1000">501-1000</option>
                            <option value="1000+">1000+</option>
                        </select>
                        <div className="pointer-events-none absolute inset-y-0 right-0 flex items-center px-2 text-gray-700">
                            <DownArrowIcon className="fill-current h-4 w-4" />
                        </div>
                    </div>
                </div>

                <div className="flex items-center justify-center">
                <button
        className={`mt-4 p-2 w-full flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#002744] ${isButtonDisabled ? 'bg-gray-400 cursor-not-allowed' : 'bg-[#002744] cursor-pointer'}`}
        disabled={isButtonDisabled}
      >
        Continue
      </button>
                </div>
            </form>
        </div>
    );
}
