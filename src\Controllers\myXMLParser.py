import sys
sys.path.append(".")
import json
import xml.etree.ElementTree as ET
from typing import List, Dict
import os
from src.Controllers.CustomLogger import CLogger
import pandas as pd
from collections import Counter
import re

class CXmlParser:

    @staticmethod
    def MSSetupLogging(logDirectory: str="logs_xml", logFileNameWithoutExtention: str="log"):
        """
        sets up logging, creates necessary folders and files if it's not already configured.
        
        """
        try:
            CLogger.MCSetupLogging(strLogsDirPath=logDirectory, strLogFileNameWithoutExtension=logFileNameWithoutExtention)
        except Exception as e:
            print(f"Failed to setup logger, error: {str(e)}")


    @staticmethod
    def MSLog(log_type: str, log_message: str):
        """
        Verifies if logging is set up; if not, initializes it.
        Then logs the message based on the log type.
        """
        try:
            if not CLogger.strLogFilePath:
                print("Logger is not set up, creating logger with default settings")
                CXmlParser.MSSetupLogging()
                
            # Call the logging method
            CLogger.MCWriteLog(log_type, log_message)
            
        except Exception as e:
            print(f"Failed to write log message in log file. Exception:{str(e)}")
            print(log_message)


    @staticmethod
    def _MSGetElementByPath(root: ET.Element, path: str) -> ET.Element:
        """
        Recursively traverse the XML tree to find an element by its full or partial XPath,
        supporting attribute-based filtering and index-based selection.

        Args:
            root (ET.Element): The root XML element to start the search.
            path (str): The XPath to locate the desired element. Use '[index]' for index selection.

        Returns:
            ET.Element: The located element, or None if not found.
        """
        if not path:
            return None

        # Split path into parts
        path_parts = path.split("/")
        # Remove Root tag if path contains child elements also
        if len(path_parts) > 1:
            path_parts = path_parts[1:]
            
        current_element = root
        
        for part in path_parts:
            if not part:
                continue

            # Extract tag and optional index or attribute filter
            tag = part
            index = None
            attr_filter = None

            if "[" in part and "]" in part:
                tag, filter_part = part.split("[", 1)
                filter_part = filter_part.strip("]")

                if filter_part.isdigit():  # Index-based filtering
                    index = int(filter_part)
                else:  # Attribute-based filtering
                    attr_filter = filter_part.split("=")
                    attr_name = attr_filter[0].strip()
                    attr_value = attr_filter[1].strip("\"'")

            # Search for the matching child node
            found_elements = []
            for child in current_element:
                if child.tag == tag:
                    if attr_filter:  # Attribute filtering
                        if child.attrib.get(attr_name) == attr_value:
                            found_elements.append(child)
                    else:  # No attribute filtering, add all matching tags
                        found_elements.append(child)

            if index is not None:  # Index-based filtering
                if 0 <= index < len(found_elements):
                    current_element = found_elements[index]
                else:
                    return None
            elif found_elements:  # No index, use the first match
                current_element = found_elements[0]
            else:
                return None

        return current_element



    

    @staticmethod
    def MSConvertXMLToXLSX(xmlFilePath: str, outputFilePath: str = None, tagName: str = None, parentXPath: str = None) -> str:
        """
        Converts an XML file to an Excel file in a flat table format.

        Args:
            xmlFilePath (str): Path to the XML file.
            outputFilePath (str, optional): Path to save the Excel file. Defaults to None.
            tagName (str, optional): The tag to extract data from. Defaults to None (converts entire XML).
            parentXPath (str, optional): XPath to the parent node containing the tag. Defaults to None.

        Returns:
            str: Path to the created Excel file.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ValueError: If the specified tag or parent node is not found.
        """
        CXmlParser.MSLog("INFO", f"Request received to convert XML file '{xmlFilePath}' to Excel. Tag: '{tagName}', ParentXPath: '{parentXPath}'.")

        if not os.path.isfile(xmlFilePath):
            CXmlParser.MSLog("ERROR", f"File not found: '{xmlFilePath}'. Aborting operation.")
            raise FileNotFoundError(f"File not found: '{xmlFilePath}'.")

        if outputFilePath is None:
            outputFilePath = xmlFilePath.replace(".xml", ".xlsx")
            CXmlParser.MSLog("INFO", f"No output file path provided. Using default: '{outputFilePath}'.")

        try:
            # Read and sanitize XML content
            with open(xmlFilePath, "r", encoding="utf-8") as file:
                raw_xml = file.read()
            
            sanitized_xml = CXmlParser.MSSanitizeXML(raw_xml)

            # Parse sanitized XML content
            root = ET.fromstring(sanitized_xml)

            # tree = ET.parse(sanitized_xml)
            # root = tree.getroot()

            # Determine the parent node
            parent = None
            if parentXPath:
                parent = CXmlParser._MSGetElementByPath(root, parentXPath)
                if parent is None:
                    CXmlParser.MSLog("ERROR", f"Parent node '{parentXPath}' not found in the XML. Aborting operation.")
                    raise ValueError(f"Parent node '{parentXPath}' not found in the XML.")
            else:
                parent = root

            # Collect data for conversion
            rows = []
            if tagName:
                CXmlParser.MSLog("INFO", f"Extracting data for tag '{tagName}' under parent '{parentXPath or root.tag}'.")
                for element in parent.findall(tagName):
                    rows.extend(CXmlParser._MSExtractAllElements(element))
            else:
                CXmlParser.MSLog("INFO", f"Extracting data for all children under parent '{parentXPath or root.tag}'.")
                for child in list(parent):
                    rows.extend(CXmlParser._MSExtractAllElements(child))

            # Convert rows to DataFrame
            if not rows:
                CXmlParser.MSLog("WARNING", f"No data found for tag '{tagName}' under parent '{parentXPath or root.tag}'.")
                raise ValueError(f"No data found for tag '{tagName}' under the specified parent.")

            df = pd.DataFrame(rows)
            df.to_excel(outputFilePath, index=False, engine='openpyxl')
            CXmlParser.MSLog("INFO", f"Excel file created successfully. Saved to: '{outputFilePath}'.")

        except ET.ParseError as parseError:
            CXmlParser.MSLog("ERROR", f"XML parsing error for file '{xmlFilePath}': {parseError}. Aborting operation.")
            raise ValueError(f"Error parsing XML file: {parseError}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise

        return outputFilePath
        
    @staticmethod
    def _MSExtractAllElements(element: ET.Element, parent_path: str = "") -> List[Dict]:
        """
        Recursively extracts data from an XML element and its children into a flat structure.

        Args:
            element (ET.Element): The XML element to extract data from.
            parent_path (str): The hierarchical path to the current element.

        Returns:
            List[Dict]: A list of dictionaries where each dictionary represents a row.
        """
        rows = []
        current_row = {"Tag Name": element.tag}

        # Extract attributes
        for attr_name, attr_value in element.attrib.items():
            current_row[attr_name] = attr_value

        # Extract text
        if element.text and element.text.strip():
            current_row["Text"] = element.text.strip()

        rows.append(current_row)

        # Recursively process child elements
        for child in element:
            rows.extend(CXmlParser._MSExtractAllElements(child))

        return rows


    @staticmethod
    def MSSanitizeXML(xml_content: str) -> str:
        """
        Removes invalid XML characters from the content.

        Args:
            xml_content (str): Raw XML content.

        Returns:
            str: Sanitized XML content.
        """
        # Remove invalid characters using a regex pattern
        return re.sub(r"&#[0-8];|&amp;", "", xml_content)

    @staticmethod
    def MSGetXMLToJson(xml_file_path: str, output_json_file_path: str = None) -> str:
        """
        Converts an XML file to a JSON file.

        Args:
            xml_file_path (str): Path to the XML file.
            output_json_file_path (str, optional): Path to save the JSON file. Defaults to None.

        Returns:
            str: Path to the created JSON file.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ET.ParseError: If the XML file is malformed.
        """
        if not os.path.isfile(xml_file_path):
            CXmlParser.MSLog("ERROR", f"XML file not found: {xml_file_path}. Aborting operation.")
            raise FileNotFoundError(f"XML file not found: {xml_file_path}")

        if output_json_file_path is None:
            output_json_file_path = f"converted_{os.path.splitext(os.path.basename(xml_file_path))[0]}.json"
            CXmlParser.MSLog("INFO", f"No output file path provided. Using default: '{output_json_file_path}'.")

        try:
            # Read and sanitize XML content
            with open(xml_file_path, "r", encoding="utf-8") as file:
                raw_xml = file.read()
            
            sanitized_xml = CXmlParser.MSSanitizeXML(raw_xml)

            # Parse sanitized XML content
            root = ET.fromstring(sanitized_xml)

            def xml_to_dict(element):
                """
                Recursively converts an XML element and its children to a dictionary.

                Args:
                    element (ET.Element): The XML element to convert.

                Returns:
                    dict: The dictionary representation of the XML element.
                """
                node = {
                    "tag": element.tag,
                    "attributes": element.attrib,
                    "text": element.text.strip() if element.text else None,
                    "children": [xml_to_dict(child) for child in list(element)]
                }
                return node

            json_data = xml_to_dict(root)

            with open(output_json_file_path, "w", encoding="utf-8") as json_file:
                json.dump(json_data, json_file, indent=4, ensure_ascii=False)

            CXmlParser.MSLog("INFO", f"JSON file created successfully: {output_json_file_path}")

        except ET.ParseError as e:
            CXmlParser.MSLog("ERROR", f"XML parsing error: {e}. Aborting operation.")
            raise

        return output_json_file_path


    @staticmethod
    def MSAddTags(xmlFilePath: str, tag_data: dict, parentNode: str, outputFilePath: str = None) -> str:
        """
        Adds multiple tags under the specified parent node or as siblings of the root if no parent node is provided,
        supporting nested child elements.

        Args:
            xmlFilePath (str): Path to the XML file.
            tag_data (dict): A dictionary representing a tag and its attributes.
            parentNode (str): XPath to the parent node where the tags will be added.
            outputFilePath (str, optional): Path to save the updated XML file. Defaults to None.

        Returns:
            str: Path to the updated XML file.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ValueError: If the parent node is not found.
        """
        CXmlParser.MSLog("INFO", f"Request received to add tags to XML file: '{xmlFilePath}' under parent node: '{parentNode}'.")

        if not os.path.isfile(xmlFilePath):
            CXmlParser.MSLog("ERROR", f"File not found: '{xmlFilePath}'. Aborting operation.")
            raise FileNotFoundError(f"File not found: '{xmlFilePath}'.")

        if not parentNode:
            CXmlParser.MSLog("ERROR", "Parent node not provided. Aborting operation.")
            raise ValueError("Parent node not provided, please provide a parent node inside which you want to insert the data.")

        if outputFilePath is None:
            outputFilePath = f"updated_{os.path.basename(xmlFilePath)}"
            CXmlParser.MSLog("INFO", f"No output file path provided. Using default: '{outputFilePath}'.")

        def add_children(parent_element, children_data):
            """
            Recursively adds children to a given parent XML element.

            Args:
                parent_element (ET.Element): The parent XML element.
                children_data (list): List of child elements data to add.
            """
            for child_data in children_data:
                child_tag = child_data.get("tag")
                if not child_tag:
                    CXmlParser.MSLog("ERROR", "Each child must include a 'tag' key. Aborting operation.")
                    raise ValueError("Each child must include a 'tag' key.")

                child_attributes = child_data.get("attributes", {})
                child_text = child_data.get("text")
                child_children = child_data.get("children", [])

                CXmlParser.MSLog("DEBUG", f"Adding child tag '{child_tag}' with attributes: {child_attributes}.")
                child_element = ET.SubElement(parent_element, child_tag)
                for attr_name, attr_value in child_attributes.items():
                    child_element.set(attr_name, str(attr_value))
                if child_text:
                    child_element.text = child_text
                    CXmlParser.MSLog("DEBUG", f"Added text for child tag '{child_tag}': {child_text}")

                if child_children:
                    CXmlParser.MSLog("DEBUG", f"Child tag '{child_tag}' has {len(child_children)} nested children. Recursively adding them.")
                    add_children(child_element, child_children)

        try:
            tree = ET.parse(xmlFilePath)
            root = tree.getroot()

            CXmlParser.MSLog("INFO", f"Successfully parsed XML file. Root tag: '{root.tag}'.")

            # Determine parent node
            parent = None
            if root.tag.lower() == parentNode.lower():  # Check if parentNode matches the root tag
                CXmlParser.MSLog("INFO", f"Parent node '{parentNode}' matches the root tag.")
                parent = root
            else:
                parent = CXmlParser._MSGetElementByPath(root, parentNode)
                if parent is None:
                    CXmlParser.MSLog("ERROR", f"Parent node '{parentNode}' not found in the XML. Aborting operation.")
                    raise ValueError(f"Parent node '{parentNode}' not found in the XML.")

            # Add new tags to the parent node
            tag_name = tag_data.get("tag")
            if not tag_name:
                CXmlParser.MSLog("ERROR", "Each entry in tag_data must include a 'tag' key. Aborting operation.")
                raise ValueError("Each entry in tag_data must include a 'tag' key.")

            CXmlParser.MSLog("INFO", f"Adding new tag '{tag_name}' to parent node '{parentNode}'.")
            new_element = ET.Element(tag_name)
            attributes = tag_data.get("attributes", {})
            for attr_name, attr_value in attributes.items():
                new_element.set(attr_name, str(attr_value))
            CXmlParser.MSLog("DEBUG", f"Added attributes to tag '{tag_name}': {attributes}.")

            # Add text content if provided
            text_content = tag_data.get("text")
            if text_content:
                new_element.text = text_content
                CXmlParser.MSLog("DEBUG", f"Added text content to tag '{tag_name}': {text_content}.")

            # Recursively add child elements if provided
            children = tag_data.get("children", [])
            if children:
                CXmlParser.MSLog("INFO", f"Tag '{tag_name}' has {len(children)} child elements. Adding them recursively.")
                add_children(new_element, children)

            # Append the new element to the parent
            parent.append(new_element)
            CXmlParser.MSLog("INFO", f"Successfully added tag '{tag_name}' to parent node '{parentNode}'.")

            # Save the updated XML to a new file
            tree.write(outputFilePath, encoding="utf-8", xml_declaration=True)
            CXmlParser.MSLog("INFO", f"XML file updated successfully. Saved to: '{outputFilePath}'.")

        except ET.ParseError as parseError:
            CXmlParser.MSLog("ERROR", f"XML parsing error for file '{xmlFilePath}': {parseError}. Aborting operation.")
            raise ValueError(f"Error parsing XML file: {parseError}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise

        return outputFilePath


    @staticmethod
    def MSDeleteTag(xmlFilePath: str, tagName: str, parentXPath: str = None, outputFilePath: str = None, count: int = -1) -> str:
        """
        Deletes a tag and all its child elements from an XML file.

        Args:
            xmlFilePath (str): Path to the XML file.
            tagName (str): The tag to delete.
            parentXPath (str, optional): XPath to the parent node containing the tag. If None, the root is searched. Defaults to None.
            outputFilePath (str, optional): Path to save the updated XML file. Defaults to None.
            count (int, optional): Number of matching tags to delete. Default is -1, meaning delete all matching tags.

        Returns:
            str: Path to the updated XML file.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ValueError: If the specified tag or parent node is not found.
        """
        CXmlParser.MSLog("INFO", f"Request received to delete tag '{tagName}' from XML file: '{xmlFilePath}'. Count: {count}")

        if not os.path.isfile(xmlFilePath):
            CXmlParser.MSLog("ERROR", f"File not found: '{xmlFilePath}'. Aborting operation.")
            raise FileNotFoundError(f"File not found: '{xmlFilePath}'.")

        if not tagName:
            CXmlParser.MSLog("ERROR", "Tag name to delete not provided. Aborting operation.")
            raise ValueError("Tag name to delete not provided. Please specify the tag to delete.")

        if not parentXPath:
            CXmlParser.MSLog("ERROR", "Parent node not provided. Aborting operation.")
            raise ValueError("Parent node not provided, please provide a parent node from which you want to delete the data.")

        if outputFilePath is None:
            outputFilePath = f"updated_{os.path.basename(xmlFilePath)}"
            CXmlParser.MSLog("INFO", f"No output file path provided. Using default: '{outputFilePath}'.")

        try:
            tree = ET.parse(xmlFilePath)
            root = tree.getroot()

            # Determine parent node
            parent = None
            if root.tag.lower() == (parentXPath or "").lower():  # Check if parentXPath matches the root tag
                CXmlParser.MSLog("INFO", f"Parent XPath '{parentXPath}' matches the root tag.")
                parent = root
            else:
                parent = CXmlParser._MSGetElementByPath(root, parentXPath or "")
                if parent is None:
                    CXmlParser.MSLog("ERROR", f"Parent node '{parentXPath}' not found in the XML. Aborting operation.")
                    raise ValueError(f"Parent node '{parentXPath}' not found in the XML.")

            # Find and delete the tags
            deleted_count = 0
            for child in list(parent):
                if child.tag == tagName:
                    CXmlParser.MSLog("INFO", f"Tag '{tagName}' found. Deleting it and its children.")
                    parent.remove(child)
                    deleted_count += 1

                    # Stop deleting if we've reached the specified count
                    if count != -1 and deleted_count >= count:
                        break

            if deleted_count == 0:
                CXmlParser.MSLog("WARNING", f"No matching tags '{tagName}' found under parent '{parentXPath or root.tag}'. No changes made.")
                raise ValueError(f"No matching tags '{tagName}' found under the specified parent.")
            else:
                CXmlParser.MSLog("INFO", f"Deleted {deleted_count} instances of tag '{tagName}' from parent '{parentXPath or root.tag}'.")

            # Save the updated XML to a new file
            tree.write(outputFilePath, encoding="utf-8", xml_declaration=True)
            CXmlParser.MSLog("INFO", f"XML file updated successfully. Saved to: '{outputFilePath}'.")

        except ET.ParseError as parseError:
            CXmlParser.MSLog("ERROR", f"XML parsing error for file '{xmlFilePath}': {parseError}. Aborting operation.")
            raise ValueError(f"Error parsing XML file: {parseError}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise

        return outputFilePath


    @staticmethod
    def MSSummarizeXML(xmlFilePath: str) -> Dict:
        """
        Provides a summary and structure of the XML file.

        Args:
            xmlFilePath (str): Path to the XML file.

        Returns:
            Dict: Summary and structure of the XML file, including root tag, element count, tag frequencies, attributes, and more.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ValueError: If there is an error parsing the XML file.
        """
        CXmlParser.MSLog("INFO", f"Request received to summarize XML file: '{xmlFilePath}'.")

        if not os.path.isfile(xmlFilePath):
            CXmlParser.MSLog("ERROR", f"File not found: '{xmlFilePath}'. Aborting operation.")
            raise FileNotFoundError(f"File not found: '{xmlFilePath}'.")

        try:
            tree = ET.parse(xmlFilePath)
            root = tree.getroot()
            
            summary = {
                "Root Tag": root.tag,
                "Total Elements": 0,
                "Tag Frequencies": {},
                "Unique Attributes": set(),
                "Has Text Content": False,
                "Structure": {}
            }

            def traverse(element, structure):
                # Increment element count
                summary["Total Elements"] += 1

                # Update tag frequencies
                if element.tag in summary["Tag Frequencies"]:
                    summary["Tag Frequencies"][element.tag] += 1
                else:
                    summary["Tag Frequencies"][element.tag] = 1

                # Record attributes
                summary["Unique Attributes"].update(element.attrib.keys())

                # Check if there is text content
                if element.text and element.text.strip():
                    summary["Has Text Content"] = True

                # Add the current element to the structure
                if element.tag not in structure:
                    structure[element.tag] = []

                # Create a dictionary for the current element's children
                child_structure = {}
                structure[element.tag].append({
                    "Attributes": element.attrib,
                    "Children": child_structure
                })

                # Recursively traverse children
                for child in element:
                    traverse(child, child_structure)

            # Start traversal from the root and build structure
            traverse(root, summary["Structure"])

            # Convert unique attributes set to a list for JSON compatibility
            summary["Unique Attributes"] = list(summary["Unique Attributes"])

            CXmlParser.MSLog("INFO", f"XML summary and structure created successfully for file: '{xmlFilePath}'.")
            return summary

        except ET.ParseError as parseError:
            CXmlParser.MSLog("ERROR", f"XML parsing error for file '{xmlFilePath}': {parseError}.")
            raise ValueError(f"Error parsing XML file: {parseError}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise


    @staticmethod
    def MSCreateXMLFromDict(data: dict, output_file_path: str) -> str:
        """
        Creates an XML file from a given dictionary structure.

        Args:
            data (dict): A dictionary representing the XML structure.
            output_file_path (str): Path to save the created XML file.

        Returns:
            str: Path to the created XML file.

        Raises:
            ValueError: If the provided data structure is invalid.
        """
        def add_children(parent_element, children_data):
            """
            Recursively adds children to a given parent XML element.

            Args:
                parent_element (ET.Element): The parent XML element.
                children_data (list): List of child elements data to add.
            """
            for child_data in children_data:
                child_tag = child_data.get("tag")
                if not child_tag:
                    CXmlParser.MSLog("ERROR", "Each child must include a 'tag' key. Aborting operation.")
                    raise ValueError("Each child must include a 'tag' key.")

                child_attributes = child_data.get("attributes", {})
                child_text = child_data.get("text")
                child_children = child_data.get("children", [])

                CXmlParser.MSLog("DEBUG", f"Adding child tag '{child_tag}' with attributes: {child_attributes}.")
                child_element = ET.SubElement(parent_element, child_tag)
                for attr_name, attr_value in child_attributes.items():
                    child_element.set(attr_name, str(attr_value))
                if child_text:
                    child_element.text = child_text
                    CXmlParser.MSLog("DEBUG", f"Added text for child tag '{child_tag}': {child_text}")

                if child_children:
                    CXmlParser.MSLog("DEBUG", f"Child tag '{child_tag}' has {len(child_children)} nested children. Recursively adding them.")
                    add_children(child_element, child_children)

        try:
            if not data or not isinstance(data, dict):
                CXmlParser.MSLog("ERROR", "Invalid data structure. Must be a dictionary. Aborting operation.")
                raise ValueError("Invalid data structure. Must be a dictionary.")

            CXmlParser.MSLog("INFO", "Creating XML root.")
            root_tag = data.get("tag")
            if not root_tag:
                CXmlParser.MSLog("ERROR", "Root must include a 'tag' key. Aborting operation.")
                raise ValueError("Root must include a 'tag' key.")

            root_attributes = data.get("attributes", {})
            root_children = data.get("children", [])

            root = ET.Element(root_tag)
            for attr_name, attr_value in root_attributes.items():
                root.set(attr_name, str(attr_value))

            if root_children:
                CXmlParser.MSLog("INFO", f"Root tag '{root_tag}' has {len(root_children)} child elements. Adding them recursively.")
                add_children(root, root_children)

            tree = ET.ElementTree(root)
            tree.write(output_file_path, encoding="utf-8", xml_declaration=True)

            CXmlParser.MSLog("INFO", f"XML file created successfully: {output_file_path}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise

        return output_file_path


    @staticmethod
    def MSCreateXMLFromJSON(json_file_path: str, output_file_path: str) -> str:
        """
        Reads JSON data from a file and creates an XML file.

        Args:
            json_file_path (str): Path to the JSON file.
            output_file_path (str): Path to save the created XML file.

        Returns:
            str: Path to the created XML file.

        Raises:
            FileNotFoundError: If the JSON file does not exist.
            ValueError: If the JSON data structure is invalid.
        """
        
        if not os.path.isfile(json_file_path):
            CXmlParser.MSLog("ERROR", f"JSON file not found: {json_file_path}. Aborting operation.")
            raise FileNotFoundError(f"JSON file not found: {json_file_path}")

        try:
            with open(json_file_path, "r") as json_file:
                data = json.load(json_file)

            return CXmlParser.MSCreateXMLFromDict(data, output_file_path)
        except Exception as e: 
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}.")
            raise e
        
    # ! Need to update the method to update the tag name also, and when attributes and children are given, only updates the children   
    @staticmethod
    def MSUpdateTag(xmlFilePath: str, tagName: str, parentXPath: str = None, updateData: Dict = None, outputFilePath: str = None, count: int = -1) -> str:
        """
        Updates a tag and its child elements in an XML file.

        Args:
            xmlFilePath (str): Path to the XML file.
            tagName (str): The tag to update.
            parentXPath (str, optional): XPath to the parent node containing the tag. If None, the root is searched. Defaults to None.
            updateData (Dict, optional): Data to update the tag. Includes attributes, text, and children.
            outputFilePath (str, optional): Path to save the updated XML file. Defaults to None.
            count (int, optional): Number of matching tags to update. Default is -1, meaning update all matching tags.

        Returns:
            str: Path to the updated XML file.

        Raises:
            FileNotFoundError: If the XML file does not exist.
            ValueError: If the specified tag or parent node is not found.
        """
        CXmlParser.MSLog("INFO", f"Request received to update tag '{tagName}' in XML file: '{xmlFilePath}'. Count: {count}")

        if not os.path.isfile(xmlFilePath):
            CXmlParser.MSLog("ERROR", f"File not found: '{xmlFilePath}'. Aborting operation.")
            raise FileNotFoundError(f"File not found: '{xmlFilePath}'.")

        if not tagName:
            CXmlParser.MSLog("ERROR", "Tag name to update not provided. Aborting operation.")
            raise ValueError("Tag name to update not provided. Please specify the tag to update.")

        if not updateData:
            CXmlParser.MSLog("ERROR", "No update data provided. Aborting operation.")
            raise ValueError("No update data provided. Please specify attributes, text, or children to update.")

        if outputFilePath is None:
            outputFilePath = f"updated_{os.path.basename(xmlFilePath)}"
            CXmlParser.MSLog("INFO", f"No output file path provided. Using default: '{outputFilePath}'.")

        try:
            tree = ET.parse(xmlFilePath)
            root = tree.getroot()

            # Determine parent node
            parent = None
            if root.tag.lower() == (parentXPath or "").lower():  # Check if parentXPath matches the root tag
                CXmlParser.MSLog("INFO", f"Parent XPath '{parentXPath}' matches the root tag.")
                parent = root
            else:
                parent = CXmlParser._MSGetElementByPath(root, parentXPath or "")
                if parent is None:
                    CXmlParser.MSLog("ERROR", f"Parent node '{parentXPath}' not found in the XML. Aborting operation.")
                    raise ValueError(f"Parent node '{parentXPath}' not found in the XML.")

            # Find and update the tags
            updated_count = 0
            for child in list(parent):
                if child.tag == tagName:
                    CXmlParser.MSLog("INFO", f"Tag '{tagName}' found. Updating it with provided data.")
                    
                    # Update attributes
                    if "attributes" in updateData:
                        for attr_name, attr_value in updateData["attributes"].items():
                            child.set(attr_name, str(attr_value))
                        CXmlParser.MSLog("DEBUG", f"Updated attributes for tag '{tagName}': {updateData['attributes']}")

                    # Update text
                    if "text" in updateData:
                        child.text = updateData["text"]
                        CXmlParser.MSLog("DEBUG", f"Updated text for tag '{tagName}': {updateData['text']}")

                    # Update children
                    if "children" in updateData:
                        CXmlParser.MSLog("INFO", f"Updating children for tag '{tagName}'.")
                        # child.clear()  # Clear existing children
                        CXmlParser.MSLog("DEBUG", f"Existing children cleared for tag '{tagName}'.")
                        for child_data in updateData["children"]:
                            new_child = ET.SubElement(child, child_data["tag"])
                            if "attributes" in child_data:
                                for attr_name, attr_value in child_data["attributes"].items():
                                    new_child.set(attr_name, str(attr_value))
                            if "text" in child_data:
                                new_child.text = child_data["text"]
                            CXmlParser.MSLog("DEBUG", f"Added/Updated child '{child_data['tag']}' for tag '{tagName}'.")

                    updated_count += 1

                    # Stop updating if we've reached the specified count
                    if count != -1 and updated_count >= count:
                        break

            if updated_count == 0:
                CXmlParser.MSLog("WARNING", f"No matching tags '{tagName}' found under parent '{parentXPath or root.tag}'. No changes made.")
                raise ValueError(f"No matching tags '{tagName}' found under the specified parent.")
            else:
                CXmlParser.MSLog("INFO", f"Updated {updated_count} instances of tag '{tagName}' under parent '{parentXPath or root.tag}'.")

            # Save the updated XML to a new file
            tree.write(outputFilePath, encoding="utf-8", xml_declaration=True)
            CXmlParser.MSLog("INFO", f"XML file updated successfully. Saved to: '{outputFilePath}'.")

        except ET.ParseError as parseError:
            CXmlParser.MSLog("ERROR", f"XML parsing error for file '{xmlFilePath}': {parseError}. Aborting operation.")
            raise ValueError(f"Error parsing XML file: {parseError}")

        except Exception as e:
            CXmlParser.MSLog("ERROR", f"Unexpected error occurred: {e}. Aborting operation.")
            raise

        return outputFilePath

    @staticmethod
    def MSSanitizeXML(xml_content: str) -> str:
        """
        Removes invalid XML characters from the content.

        Args:
            xml_content (str): Raw XML content.

        Returns:
            str: Sanitized XML content.
        """
        # Remove invalid characters using a regex pattern
        return re.sub(r"&#[0-8];|&amp;", "", xml_content)
    
    @staticmethod
    def MSExportSpecificXMLTagsToExcel(input_xml_path, output_excel_path, start_path, target_tags, nested_tags=None):
        """
        Converts specified XML data to an Excel file.

        :param input_xml_path: Path to the input XML file.
        :param output_excel_path: Path where the Excel file will be saved.
        :param start_path: The XML path from where to start extracting data (e.g., 'ENVELOPE/BODY/DATA/COLLECTION').
        :param target_tags: List of flat tags to extract from each target element.
        :param nested_tags: Dictionary where keys are parent tags and values are lists of nested tags to extract.
        """
        # Check if the input XML file exists
        if not os.path.exists(input_xml_path):
            print(f"Error: The file {input_xml_path} does not exist.")
            return

        # Parse the XML file
        try:
            # Read and sanitize XML content
            with open(input_xml_path, "r", encoding="utf-8") as file:
                raw_xml = file.read()
            
            sanitized_xml = CXmlParser.MSSanitizeXML(raw_xml)

            # Parse sanitized XML content
            root = ET.fromstring(sanitized_xml)
            # tree = ET.parse(input_xml_path)
            # root = tree.getroot()
        except ET.ParseError as e:
            print(f"Error parsing XML file: {e}")
            return

        # Navigate to the starting path
        current_element = root
        for tag in start_path.split('/'):
            found = current_element.find(tag)
            if found is not None:
                current_element = found
            else:
                print(f"Error: Tag '{tag}' not found in the XML structure.")
                return

        # Collect data
        data = []
        for item in current_element.findall('STOCKITEM'):
            item_data = {}
            
            # Extract flat tags
            for tag in target_tags:
                if tag == 'STOCKITEM':
                    # 'STOCKITEM' corresponds to the 'NAME' attribute
                    item_data['STOCKITEM'] = item.get('NAME', '').replace('&amp;', '&')  # Handle XML entity
                else:
                    element = item.find(tag)
                    if element is not None and element.text is not None:
                        item_data[tag] = element.text.strip()
                    else:
                        item_data[tag] = ''

            # Extract nested tags
            if nested_tags:
                for parent_tag, child_tags in nested_tags.items():
                    parent_element = item.find(parent_tag)
                    if parent_element is not None:
                        for child_tag in child_tags:
                            # Create a unique key for nested tags, e.g., 'HSN_APPLICABLEFROM'
                            unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                            child_element = parent_element.find(child_tag)
                            if child_element is not None and child_element.text is not None:
                                item_data[unique_key] = child_element.text.strip()
                            else:
                                item_data[unique_key] = ''
                    else:
                        # If the parent tag is missing, set all child tags as empty
                        for child_tag in child_tags:
                            unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                            item_data[unique_key] = ''

            data.append(item_data)

        # Define the column order
        columns = target_tags.copy()
        if nested_tags:
            for parent_tag, child_tags in nested_tags.items():
                for child_tag in child_tags:
                    unique_key = f"{parent_tag.replace('.', '_')}_{child_tag}"
                    columns.append(unique_key)

        # Create DataFrame
        df = pd.DataFrame(data, columns=columns)

        # Write to Excel
        try:
            df.to_excel(output_excel_path, index=False)
            print(f"Successfully wrote data to {output_excel_path}")
        except Exception as e:
            print(f"Error writing to Excel: {e}")

if __name__ == "__main__":
    
    strLogDirNFileName = "logs_xml"
    CLogger.MCSetupLogging(strLogsDirPath=strLogDirNFileName, strLogFileNameWithoutExtension=strLogDirNFileName)
    
    xml_path = r"ParagTraders_Quotation_Template_DeliveryNoteImport_V1.xml"
    
    # 1. ************ Get XML Data as JSON ************
    # lsXMLData = CXmlParser.MSGetXMLToJson(xml_path)
    # print(lsXMLData)
    
    
    # 2. ************ Add data to xml file ************
    # tags_to_add = [
    #     {
    #         "tag": "Stoc",
    #         "attributes": {"name": "I142", "id": "4"},
    #         "children": [
    #                         {
    #                             "tag": "StockItem",
    #                             "attributes": {"name": "Item 1", "id": "1"},
    #                         },
    #                         {
    #                             "tag": "StockItem",
    #                             "attributes": {"name": "Item", "id": "2"},
    #                         },
    #                         {
    #                             "tag": "StockItem",
    #                             "attributes": {"name": "I142", "id": "4"},
    #                             "children": [
    #                                 {
    #                                     "tag": "itemDetails",
    #                                     "attributes": {"Detail": "Item Detail 123"},
    #                                     "text": "These are the details",
    #                                 }
    #                             ]
    #                         }
    #                     ]
    #     }
    # ]
    
    # parent_xpath = "BODY/DESC"
    # xml_path = r"D:\Customer\Real\Velocity - GitHub\AccuVelocity\updated_updated_AbstractExport_27_12_2024_16_01_04.xml"

    # updated_file = CXmlParser.MSAddTags(xml_path, tags_to_add, parent_xpath)
    # print(f"Updated XML saved to: {updated_file}")
    
    
    # 3. ************ Delete the Tag from the XML file ************
    # parent_xpath = "ENVELOPE/BODY/DATA/COLLECTION"

    # CXmlParser.MSDeleteTag(r"D:\Customer\Real\Velocity - GitHub\AccuVelocity\updated_updated_AbstractExport_27_12_2024_16_01_04.xml", "STOCKITEM", parent_xpath)
    
    
    # 4. ************ Update the Tag from the XML File ************
    # update_data = {
    #                 "attributes": {"updated": "true", "Name":None, "RESERVEDNAME":None},
    #                 "text": "Updated Detail",
    #                 "children": [
    #                         {"tag": "SUBDETAIL", "attributes": {"type": "example"}, "text": "New Subdetail"}
    #                     ]
    #                 }
    # parent_xpath = "ENVELOPE/BODY/DATA/COLLECTION"
    # xml_path = r"D:\Customer\Real\Velocity - GitHub\AccuVelocity\updated_updated_AbstractExport_27_12_2024_16_01_04.xml"
    
    # CXmlParser.MSUpdateTag(xmlFilePath=xml_path, tagName="STOCKITEM", parentXPath=parent_xpath, updateData=update_data)
    
    
    
    # 5. ************************ Convert xml to xlsx ************************
    # xml_path = r"H:\AI Data\TallyExports\20_01_2025\FilteredStockItemExport_20_01_2025_17_51_12.xml"
    # outputFilePath = r"H:\AI Data\17_ParagTraders\StockItemsDB\FilteredStockItemExport_20_01_2025_18_19_39.xlsx"
    # tag_name = "STOCKITEM"
    # parent_xpath = "ENVELOPE/BODY/DATA/COLLECTION"
    # CXmlParser.MSConvertXMLToXLSX(xmlFilePath=xml_path, tagName=tag_name, parentXPath=parent_xpath)
    
    
    # 6. ************ Summarize xml  ************
    # xml_path = r"h:\AI Data\TallyExports\02_01_2025\AbstractExport_02_01_2025_12_06_39.xml"
    # # tag_name = "STOCKITEM"
    # # parent_xpath = "ENVELOPE/BODY/DATA/COLLECTION"
    # CXmlParser.MSSummarizeXML(xmlFilePath=xml_path)
    
    
    
    # 7. ************************ Create XML from Dictionary  ************************
    # data = {
    #             "tag": "Stoc",
    #             "attributes": {"name": "I142", "id": "4"},
    #             "children": [
    #                             {
    #                                 "tag": "StockItem",
    #                                 "attributes": {"name": "Item 1", "id": "1"},
    #                             },
    #                             {
    #                                 "tag": "StockItem",
    #                                 "attributes": {"name": "Item", "id": "2"},
    #                             },
    #                             {
    #                                 "tag": "StockItem",
    #                                 "attributes": {"name": "I142", "id": "4"},
    #                                 "children": [
    #                                     {
    #                                         "tag": "itemDetails",
    #                                         "attributes": {"Detail": "Item Detail 123"},
    #                                         "text": "These are the details",
    #                                     }
    #                                 ]
    #                             }
    #                         ]
    #         }

    # output_path = "output.xml"
    # CXmlParser.MSCreateXMLFromDict(data, output_path)
    
    
    
    # 8. ************************ Create XML from Json File  ************************
    # strJsonFilePath = "final_processed_ParagTraders_Quotation_Template_DeliveryNoteImport_V1.json"
    # strXMLFilePath = "output_temp.xml"
    
    # strOutputXMLFilePath = CXmlParser.MSCreateXMLFromJSON(json_file_path=strJsonFilePath, output_file_path=strXMLFilePath)
    # print(f"The Xml from json is saved at location: '{strOutputXMLFilePath}'.")
