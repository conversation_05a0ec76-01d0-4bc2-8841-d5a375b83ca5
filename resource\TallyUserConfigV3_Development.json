{"<EMAIL>": {"metadata": {"provider": "zoho", "password": "WSuNegdfaGA5"}, "clients": {"TestSimpolo": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "Parag Traders", "dataDirectory": "H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyDataTest", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 1}, "airen": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["xlsx", "xls"], "customerName": "Airen", "dataDirectory": "H:\\AI Data\\25_Airen\\DailyData", "userId": null, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": false, "bVerifyWithPriceList": false, "MaxRetryCount": 1}, "gwalia": {"senderList": [], "attachmentsTypes": ["xlsx", "xls"], "customerName": "gwalia", "dataDirectory": "H:\\AI Data\\26_Gwalia\\BankStatements\\DailyData", "userId": 7, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": false, "bVerifyWithPriceList": false, "MaxRetryCount": 1, "company_name": "test_gwalia", "bank_name": "BOB"}, "Testgwalia": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["xlsx", "xls"], "customerName": "gwalia", "dataDirectory": "H:\\AI Data\\26_Gwalia\\BankStatements\\TestDailyData", "userId": 7, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": false, "bVerifyWithPriceList": false, "MaxRetryCount": 1, "company_name": "test_gwalia", "bank_name": "BOB"}}}}