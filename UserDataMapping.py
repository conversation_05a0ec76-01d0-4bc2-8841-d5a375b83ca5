from fuzzywuzzy import process

def find_best_match(target, choices):
    """Find the best match for a given target from a list of choices."""
    best_match, score = process.extractOne(target, choices)
    return best_match if score > 80 else None

def map_fields(user_fields, doc_fields):
    """Map user fields to document fields using fuzzy matching."""
    mapped_fields = []

    doc_keys = [list(fields.keys())[0] for fields in doc_fields] # Adapted to handle list of dicts
    for user_field in user_fields:
        best_match = find_best_match(user_field['FieldName'], doc_keys)
        if best_match:
            # Extract the value associated with the best match key from doc_fields
            matched_field = next(item for item in doc_fields if best_match in item)
            mapped_fields.append({user_field['FieldName']: matched_field[best_match]})
    return mapped_fields

def map_Table_fields(user_fields, doc_fields):
    """Map user fields to document fields using fuzzy matching."""
    mapped_fields = []
    
    doc_keys = list(doc_fields.keys())
    for user_field in user_fields:
        best_match = find_best_match(user_field['FieldName'], doc_keys)
        if best_match:
            mapped_fields.append({user_field['FieldName']: doc_fields[best_match]})
    return mapped_fields

def map_tables(user_tables, doc_tables):
    """Map user tables to document tables using fuzzy matching."""
    mapped_tables = []
    doc_table_keys = list(doc_tables.keys())
    for user_table in user_tables:
        table_name = user_table['TableName']
        best_table_match = find_best_match(table_name.replace(" ", ""), doc_table_keys)
        if best_table_match:
            doc_table_data = doc_tables[best_table_match]
            mapped_table_data = [map_Table_fields(user_table['Fields'], doc_row) for doc_row in doc_table_data]
            mapped_tables.append({table_name: [item for sublist in mapped_table_data for item in sublist]})
    return mapped_tables

def map_extracted_to_user_format(user_data, extracted_data):
    """Map extracted document data to the user-provided data format."""
    mapped_data = {
        'Fields': map_fields(user_data['Fields'], extracted_data['Document']['Fields']),
        'Tables': map_tables(user_data['Tables'], extracted_data['Document']['Tables'])
    }
    return mapped_data

# Example usage
user_provided_data ={
    "Fields":[
                {
                    "FieldName": "Name",
                    "FieldCategory":"Text Only",
                    "FieldFormat": "Text",
                    "FieldDescription": "Just a Text only",
                    "FieldNotes":"This is just one note"
                },
                {
                    "FieldName": "Address",
                    "FieldCategory":"Date",
                    "FieldFormat": "",
                    "FieldDescription": "",
                    "FieldNotes":""
                }
    ],

    "Tables": [
                {
                    "TableName": "Skills",
                    "Fields": [
                        {
                            "FieldName": "Skill Name",
                            "FieldCategory":"Text Only",
                            "FieldFormat": "",
                            "FieldDescription": "",
                            "FieldNotes":""
                        },
                        {
                            "FieldName": "Proficciency Level",
                            "FieldCategory":"Numbers only",
                            "FieldFormat": "",
                            "FieldDescription": "",
                            "FieldNotes":""
                        }
                    ]
                },
                {
                    "TableName": "Education Details",
                    "Fields": [
                        {
                            "FieldName": "Degree Name",
                            "FieldCategory":"Text Only",
                            "FieldFormat": "",
                            "FieldDescription": "",
                            "FieldNotes":""
                        },
                        {
                            "FieldName": "Marks Obtained",
                            "FieldCategory":"Text Only",
                            "FieldFormat": "",
                            "FieldDescription": "",
                            "FieldNotes":""
                        }
                    ]
                }
            ]
}

extracted_document_data = {
    "Document": {
                    "Fields":[
                                {"Address": "123 Titanium Square, Sola"},
                                {"Name": "Rani R. Raja"}
                                
                    ],
                    "Tables":{
                            "EducationDetailsInStr": [
                                                {
                                                    "DegreeNameInString": "MBA",
                                                    "MarksObtainedInFloat": "98.20"
                                                },
                                                {
                                                    "DegreeNameInString": "MCA",
                                                    "MarksObtainedInFloat": "86.64" 
                                                }
                                            ],
                            "Skills": [
                                            {
                                                "SkillNAME": "Skill 1",
                                                "Proficciency_Level": "null"
                                            },
                                            {
                                                "SkillNAME": "Skill2",
                                                "Proficciency_Level": "null"
                                            }
                                        ]
                }
    }
}

mapped_data = map_extracted_to_user_format(user_provided_data, extracted_document_data)
print(mapped_data)
