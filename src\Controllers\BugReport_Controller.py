from src.Models.models import BugTracking
from src.Schemas.Bugs_Schema import BugReport
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import IntegrityError
from datetime import datetime
from typing import List, Optional
from fastapi import HTTPException, File,UploadFile
from sqlalchemy.future import select
import traceback,os,json,random
from src.utilities.S3BucketHelper import CAWSS3Storage
from config.db_config import AsyncSessionLocal
from config.constants import Constants
from src.Controllers.Logs_Controller import CLogController
from src.utilities.email_utils import CEmailer
from src.utilities.DBHelper import CUserTable

class CBugReportController:
    """
    Controller for handling bug report operations, including creating and retrieving bug reports.
    """
    @staticmethod 
    async def MSCreateReportBug(objrequest: BugReport, user_id :int, documents:Optional[List[UploadFile]]) :
        try:
            strBugTracking = CBugReportController.generateBugTrackingID()
            BugS3ObjectKeysJson = {}
            if documents:
                total_size = sum(doc.size for doc in documents)
                max_size_mb = 50 * 1024 * 1024  # 50MB in bytes

                if len(documents) > 5:
                    raise HTTPException(status_code=400, detail="Maximum number of documents exceeded (5 allowed).")

                if total_size > max_size_mb:
                    raise HTTPException(status_code=400, detail="Total size of documents exceeds 50MB.")
                
                file_types = [Constants.allowed_content_types_for_bug.get(document.content_type) for document in documents]

                if not all(file_types):
                    raise HTTPException(status_code=400, detail="Invalid file type : Please upload a valid file type")


                BugS3ObjectKeys = []

                for document,file_type in zip(documents, file_types):
                    file_data = await document.read()
                    file_name = document.filename
                    
                    S3ObjName = CAWSS3Storage.MSGenerateUniqueObjecID()
                    objCAWSS3Storage = CAWSS3Storage(S3ObjName,user_id=user_id, bucket_name=os.getenv('s3_bucket_name_for_bugs'))

                    response = await objCAWSS3Storage.MSetS3Object(file_data, metadata={"x-amz-meta-filename": str(file_name),"x-amz-meta-user_id": str(user_id), "x-amz-meta-file_type":str(file_type), "x-amz-meta-bug_id":str(strBugTracking)})

                    BugS3ObjectKeys.append(S3ObjName)
                    await CLogController.MSWriteLog(user_id, "Info", f"File {file_name} uploaded to S3 with key {S3ObjName}")

                BugS3ObjectKeysJson = json.dumps(BugS3ObjectKeys)
            async with AsyncSessionLocal() as session:
                db_bug = BugTracking(
                    BugTime=objrequest.BugTime if objrequest.BugTime is not None else None,
                    Browser=objrequest.Browser if objrequest.Browser is not None else None,
                    AppVersion=objrequest.AppVersion if objrequest.AppVersion is not None else None,
                    OperatingSystem=objrequest.OperatingSystem if objrequest.OperatingSystem is not None else None,
                    Description=objrequest.Description if objrequest.Description is not None else None,
                    UserID = user_id,
                    BugTrackingID = strBugTracking if strBugTracking is not None else None,
                    BugS3ObjectKey = BugS3ObjectKeysJson if BugS3ObjectKeysJson is not None else None
                )
                session.add(db_bug)
                await session.commit()
                await CLogController.MSWriteLog(user_id, "Info", f"Bug report created with ID {db_bug.BugID}")
                emailer = CEmailer()
                data = await CUserTable.MSGetUser(user_id=user_id)
                await emailer.send_email(
                    to=Constants.lsContectUsList,
                    template_name='BugReportNotification',
                    template_data={
                        'Customer_Name': data["name"],
                        'Ticket_ID': db_bug.BugTrackingID,
                        'Description': db_bug.Description,
                        'Date_Submitted': db_bug.CreatedDateTime,
                        'Response_Time_Frame': "24 hours",
                        'SupportPhoneNumber': os.getenv("CONTACTNUMBER")
                    }
                )
                return {"message": "BUG entry added successfully"}
        except IntegrityError as e:
            await session.rollback()
            print(f"Integrity Error: {str(e)}")
            print(traceback.format_exc())
            raise HTTPException(status_code=400, detail="Database integrity error.")
        except HTTPException as e:
            print(f"HTTP Exception: {str(e.detail)}")
            raise
        except Exception as e:
            print(f"Error: {str(e)}")
            print(traceback.format_exc())
            raise HTTPException(status_code=500, detail="An unexpected error occurred.")
        
    @staticmethod      
    async def get_AllBugs(isAdmin: None):
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(BugTracking))
                BugList = result.scalars().all()
                await CLogController.MSWriteLog("System", "Info", f"Retrieved {len(BugList)} bugs from the database")
                return BugList
        except Exception as e:
            await CLogController.MSWriteLog("System", "Error", f"Error retrieving bugs: {str(e)}")
            await CLogController.MSWriteLog("System", "Debug", f"Traceback: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="An error occurred while retrieving bugs.")
        
    @staticmethod    
    def generateBugTrackingID():
        IntRandomNumber = random.randint(100000, 999999)
        # Format the string with the required pattern
        strBugTracking = f"#AVB{IntRandomNumber}"
        return strBugTracking