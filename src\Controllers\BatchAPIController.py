from typing import Any, List, Dict
from fastapi import APIRouter, HTTPException, Depends,  status
import json

import traceback
import sys
sys.path.append(".")
from config.constants import Constants
from src.Controllers.DocumentData_controller import CDocumentData
from pathlib import Path
# from src.Controllers.auth_controller import get_single_user_api_usage
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.auth_controller import CAuthController
from src.Controllers.ApplicationLayerProcess import ApplicationLayerProcessing
from src.utilities.DBHelper import CPromptTable,CExtractedValidationTable
from dotenv import load_dotenv
import os
from typing import List, Dict
from src.Schemas.GPT_Schema import CreateBatchAPITask
from src.utilities.helperFunc import DateHelper
from datetime import datetime
from typing import List, Dict
# import openai
from openai import OpenAI
from src.utilities.DBHelper import CPromptTable
from src.Controllers.GPTBatchAPIController import CGPTBatchAPIDB
from src.Schemas.schemas import GPT<PERSON>atchAP<PERSON>tatusEnum
from src.Controllers.GPTResponse_controller import CGPTResponseData
from src.utilities.ExtractTextHelper import ExtractTextFromDoc
from src.Controllers.ParagTradersControllers import CParagTraders

# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)
API_KEY = os.getenv('AVOPENAI_API_KEY')
client = OpenAI(api_key=API_KEY)

class CBatchAPIController:
    @staticmethod
    async def reset_data_on_doc_id(user_id: int, doc_id: int, prompt_id: int) -> bool:
        """
        Resets data associated with a specific document ID.

        :param user_id: ID of the user.
        :param doc_id: ID of the document.
        :param prompt_id: ID of the prompt.
        :return: True if successful, False otherwise.
        """
        try:
            await CDocumentData.MSResetDocumentData(iUserID=user_id, iDocId=doc_id)
            await CExtractedValidationTable.MSDeleteExtractValidatedData(
                user_id=user_id, doc_id=doc_id, prompt_id=prompt_id
            )
            return True
        except Exception as e:
            # Consider logging the exception here
            return False
    
    @staticmethod
    async def process_doc_id(doc_id: int, user_id:int, is_trial_paid_doc_extraction: bool = False, debug: bool = True) -> Dict[str, Any]:
        """
        Processes a single document ID for batch API tasks.

        :param doc_id: ID of the document to process.
        :param is_trial_paid_doc_extraction: Flag indicating if the extraction is trial or paid.
        :param debug: Flag to enable debug mode.
        :return: Dictionary containing processed document properties.
        """
        prompt_id = None  # Default value to use if keys are missing or if there is no "Id"
        perform_aws_extraction = True
        response_format = {"type": "json_object"}

        try:
            # Get User Configuration Data
            user_data = await CAuthController.MSGetSingleUser(user_id=user_id)
            is_gpt_enabled = is_trial_paid_doc_extraction and user_data.get("isTrialPaidDocExtraction", False)

            # please insert this below statement inside if condition,  Call AWS extraction if tally is configured
            if user_data.get("integration_config") and user_data["integration_config"].get("isTallyConfigured"):
                perform_aws_extraction = True
                is_gpt_enabled = True
                
            dict_document_data = await CDocumentData.MSGetDocById(
                user_id=user_id,
                docId=doc_id,
                isBinaryDataRequired=True,
                isUserMetaData=perform_aws_extraction
            )


            # Update whether a paid model is used or not for current extraction
            await CDocumentData.MSUpdateDoc(
                iUserID=user_id,
                iDocId=doc_id,
                bUsedPaidModel=is_gpt_enabled
            )

            # Get Prompt and Model Table Data based on UserID, DocId, is Trial Doc Extraction
            dict_prompt_data = await CPromptTable.MSGetPromptDataBaseOnDocID(
                iUserID=user_id,
                docID=doc_id,
                isPaidDocExtraction=is_gpt_enabled
            )

            # Get prompt ID which is used for that document
            prompt_id = dict_prompt_data.get("PromptTable", {}).get("Id")
            model_name = dict_prompt_data.get("ModelTable", {}).get("ModelName")
            doc_prompt = dict_prompt_data.get("PromptTable", {}).get("Prompt")
            
            obj_doc_metadata = ExtractTextFromDoc(
                dictUserData=user_data,
                dictDocumentData=dict_document_data
            )

            # Extract text from Document
            obj_doc_metadata_response = await obj_doc_metadata.MExtractTxtFromDoc(
                isGPTEnabled=is_gpt_enabled,
                bPerformAWSExtraction=perform_aws_extraction
            )


            # Reset Document Data
            reset_success = await CBatchAPIController.reset_data_on_doc_id(
                user_id=user_id,
                doc_id=doc_id,
                prompt_id=prompt_id
            )
            if not reset_success:
                raise Exception("Failed to reset data on document ID.")

            if perform_aws_extraction:
                if "simpolo" in model_name.lower():
                    response_format_path = Path(r"Data/Customer/17_ParagTraders/1_Simpolo/GPTResponseFormat.json")
                    with open(response_format_path) as f:
                        response_format = json.load(f)
                else:
                    raise HTTPException(
                        status_code=404,
                        detail=f"No valid response format found for model '{model_name}'"
                    )

            timestamp = DateHelper.get_current_timestamp(format_str="%Y-%m-%d %H:%M:%S")
            task_id = f"{user_id}-{doc_id}-{timestamp}"

            return {
                "user_id": user_id,
                "doc_id": doc_id,
                "raw_extract_data": obj_doc_metadata_response.get("extracted_text"),
                "prompt_id": prompt_id,
                "model_name": model_name,
                "prompt_table":dict_prompt_data.get("PromptTable", {}),
                "model_table":dict_prompt_data.get("ModelTable", {}),
                "model_id":dict_prompt_data.get("ModelTable", {}).get("ModelId"),
                "gpt_system_content": doc_prompt,
                "task_id": task_id,
                "gpt_response_format": response_format,
                "is_gpt_enabled":is_gpt_enabled,
                "perform_aws_extraction":perform_aws_extraction
            }

        except HTTPException as e:
            await CLogController.MSWriteLog(user_id, "Error", "Failed to Process the PDF")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CDocumentData.MSUpdateDocumentStatus(
                iUserID=user_id,
                iDocId=doc_id,
                eNewStatus="Error",
                strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}",
                strDocErrorMsg=e.detail
            )
            await CGPTResponseData.MSLogExtractionStatus(
                strLogType="Error",
                iUserID=user_id,
                iDocId=doc_id,
                isTrialPaidDocExtraction=is_trial_paid_doc_extraction,
                isEndOfExtraction=False
            )
            response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(
                user_id=user_id,
                document_id=doc_id,
                dictAPIRespnse={}
            )
            return response
        except Exception as e:
            await CLogController.MSWriteLog(user_id, "Error", "Failed to Process the PDF")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            await CGPTResponseData.MSLogExtractionStatus(
                strLogType="Error",
                iUserID=user_id,
                iDocId=doc_id,
                isTrialPaidDocExtraction=is_trial_paid_doc_extraction,
                isEndOfExtraction=False
            )
            await CDocumentData.MSUpdateDocumentStatus(
                iUserID=user_id,
                iDocId=doc_id,
                eNewStatus="Error",
                strDocDebugMsg=f"Traceback: {str(traceback.format_exc())}",
                strDocErrorMsg=str(e),
                DocExtractionAPIStatusCode=getattr(e, 'status_code', None)
            )
            response = await ApplicationLayerProcessing.prepareDocumentExtractionResponse(
                user_id=user_id,
                document_id=doc_id,
                dictAPIRespnse={}
            )
            return response
        
    @staticmethod
    async def create_batch_api_tasks_for_doc_ids(
        doc_ids: List[str],
        user_id:int, 
        is_trial_paid_doc_extraction: bool = False,
        debug: bool = True
    ) -> Dict[str, Any]:
        """
        Creates batch API tasks for a list of document IDs.

        :param doc_ids: List of document IDs.
        :param is_trial_paid_doc_extraction: Flag indicating trial or paid extraction.
        :param debug: Flag for debug mode.
        :return: Dictionary containing list of tasks and task details.
        """
        try:
            batch_api_tasks = []
            batch_api_task_details = []
            for doc_id in doc_ids:
                dict_batch_api_task_details = {}
                if debug:
                    print("Processing DocId:", doc_id)
                
                doc_properties = await CBatchAPIController.process_doc_id(
                    doc_id=doc_id,
                    user_id=user_id,
                    is_trial_paid_doc_extraction=is_trial_paid_doc_extraction,
                    debug=debug
                )
                create_batch_api_task = CreateBatchAPITask(
                    strSystemContent=doc_properties.get("gpt_system_content"),
                    strUserContent=doc_properties.get("raw_extract_data"),
                    strModel=Constants.GPTAPIVersion,
                    intSeed=33,
                    dictResponseFormat=doc_properties.get("gpt_response_format"),
                    task_id= doc_properties.get("task_id")
                )
                batch_api_task = CBatchAPIController.create_single_batch_api_task(
                    create_batch_api_task=create_batch_api_task
                )
                # Define the keys you want to extract
                selected_keys = ["user_id", "doc_id", "prompt_id", "model_name", 
                                "model_id", "prompt_table", "model_table", 
                                "perform_aws_extraction", "is_gpt_enabled"]

                # Extract the selected key-value pairs from doc_properties
                dict_batch_api_task_details[doc_properties.get("task_id")] = {
                    key: doc_properties[key] for key in selected_keys if key in doc_properties
                }
                
                batch_api_tasks.append(batch_api_task)
                batch_api_task_details.append(dict_batch_api_task_details)
            return {
                "lsTasks": batch_api_tasks,
                "task_details": batch_api_task_details
            }
        except Exception as e:
            traceback.print_exc()
            raise e
    
    @staticmethod
    async def process_doc_ids(doc_ids: List[int], user_id: int, is_trial_paid_doc_extraction: bool = False, debug: bool = True) -> Dict[str, Any]:
        """
        Processes multiple document IDs to create and initiate a batch job.

        :param doc_ids: List of document IDs to process.
        :param is_trial_paid_doc_extraction: Flag indicating if the extraction is trial or paid.
        :param debug: Flag to enable debug mode.
        :return: Dictionary containing file path, upload response, batch job response, and task details.
        """
        try:
            batch_api_data = await CBatchAPIController.create_batch_api_tasks_for_doc_ids(
                doc_ids=doc_ids,
                user_id=user_id,
                is_trial_paid_doc_extraction=is_trial_paid_doc_extraction,
                debug=debug
            )
            batch_api_tasks = batch_api_data.get("lsTasks")
            task_details = batch_api_data.get("task_details")

            sub_dir_name = os.path.join(
                DateHelper.get_current_timestamp("%d-%m-%Y"),
                "input"
            )
            sub_dir_name = os.path.normpath(sub_dir_name)  # Normalize path separators

            jsonl_file_path = CBatchAPIController.create_file(
                tasks=batch_api_tasks,
                base_directory=Path(r"Data/BatchAPI_TasksJsonlFile"),
                sub_directory=sub_dir_name,
                filename_format="%Y%m%d%H%M%S.jsonl"
            )

            upload_response = CBatchAPIController.upload_file(
                file_path=jsonl_file_path,
                api_key=API_KEY,
                purpose="batch"
            )

            batch_job_response = CBatchAPIController.create_batch_job(
                file_id=upload_response.get("id"),
                api_key=API_KEY,
                endpoint="/v1/chat/completions",
                completion_window=Constants.GPTBatchAPICompletionWindow
            )

            insertedResult = await CGPTBatchAPIDB.MSInsertBatchRecord(
                userid =user_id,
                batch_object=batch_job_response,
                batch_polling_object=None,
                task_details=task_details,
                status=GPTBatchAPIStatusEnum.in_progress
            )

            return {
                "batch_job_record_id": insertedResult.get("id"),
                "userid":user_id,
                "jsonl_file_path": jsonl_file_path,
                "upload_response": upload_response,
                "batch_job_response": batch_job_response,
                "task_details": task_details
            }
        except Exception as e:
            print("Traceback - ", traceback.format_exc())
            raise e

    @staticmethod
    def create_single_batch_api_task(create_batch_api_task: CreateBatchAPITask):
        """
        Creates a single batch API task dictionary from a CreateBatchAPITask object.

        :param create_batch_api_task: CreateBatchAPITask object containing task details.
        :return: Dictionary representing a single batch API task.
        """
        try:
            task_id = create_batch_api_task.task_id
            gpt_api_model = create_batch_api_task.strModel
            response_format = create_batch_api_task.dictResponseFormat
            user_content = create_batch_api_task.strUserContent
            system_content = create_batch_api_task.strSystemContent

            task = {
                "custom_id": task_id,
                "method": "POST",
                "url": "/v1/chat/completions",
                "body": {
                    "model": gpt_api_model,
                    "response_format": response_format,
                    "messages": [
                        {
                            "role": "system",
                            "content": system_content
                        },
                        {
                            "role": "user",
                            "content": user_content
                        }
                    ],
                }
            }
            return task
        except Exception as e:
            print("Error - ", traceback.print_exc())
    
    
    @staticmethod
    def create_file(
        tasks: List[Dict],
        base_directory: str,
        sub_directory: str = "",
        filename_format: str = "%Y%m%d%H%M%S.jsonl"
    ) -> str:
        """
        Creates a JSONL file with tasks in the specified directory.
        The filename is based on the current timestamp.

        :param tasks: A list of dictionaries to be written as JSON lines.
        :param base_directory: The base directory where files will be created.
        :param sub_directory: Sub-directory within the base directory.
        :param filename_format: Timestamp format for the filename.
        :return: The path to the created file.
        """
        # Determine the target directory
        target_directory = os.path.join(base_directory, sub_directory)
        os.makedirs(target_directory, exist_ok=True)

        # Generate the filename based on the current timestamp
        timestamp = datetime.now().strftime(filename_format)
        file_path = os.path.join(target_directory, timestamp)

        # Ensure the file has a .jsonl extension
        if not file_path.endswith('.jsonl'):
            file_path += '.jsonl'

        # Write tasks to the file
        try:
            with open(file_path, 'w', encoding='utf-8') as file:
                for obj in tasks:
                    json_line = json.dumps(obj)
                    file.write(json_line + '\n')
            print(f"File created at: {file_path}")
        except Exception as e:
            print(f"Error creating file: {e}")
            raise

        return file_path

    @staticmethod
    def upload_file(file_path: str, api_key: str, purpose: str = "batch") -> Dict:
        """
        Uploads a file to the OpenAI GPT API.

        :param file_path: The path to the file to be uploaded.
        :param api_key: Your OpenAI API key.
        :param purpose: The purpose of the file (e.g., "fine-tune", "batch").
        :return: A dictionary containing the uploaded file's properties.
        """
        # openai.api_key = api_key

        try:
            with open(file_path, "rb") as file:
                response = client.files.create(
                    file=file,
                    purpose=purpose
                )
            print(f"File uploaded: {response}")
            return response.to_dict()
        except Exception as e:
            print(f"Error uploading file: {e}")
            raise

    @staticmethod
    def create_batch_job(
        file_id: str,
        api_key: str,
        endpoint: str = "/v1/chat/completions",
        completion_window: str = "24h"
    ) -> Dict:
        """
        Creates a batch job for the specified file ID.

        :param file_id: The ID of the uploaded file.
        :param api_key: Your OpenAI API key.
        :param endpoint: The API endpoint for the batch job.
        :param completion_window: The completion window duration.
        :return: A dictionary containing the batch job's properties.
        """
        
        # openai.api_key = api_key

        try:
            response = client.batches.create(
                input_file_id=file_id,
                endpoint=endpoint,
                completion_window=completion_window
            )
            print(f"Batch job created: {response}")
            return response.to_dict()
        except Exception as e:
            print(f"Error creating batch job: {e}")
            raise
    
    @staticmethod
    def findDocId(lsTaskDetails, strCustomTaskID):
        # Iterate through the dictionary to find the matching key
        matching_key = None
        for dictTaskDetail in lsTaskDetails:
            for key, value in dictTaskDetail.items():
                if key == strCustomTaskID:
                    matching_key = dictTaskDetail
                    break
        if matching_key:
            print(f"Matching key found: {matching_key}")
        else:
            print("No matching key found.")
        return matching_key
    
    @staticmethod
    async def retrieve_and_store_batch_results(
        userid:int,
        batch_job_record_id: int,
        result_file_id: int,
        
        base_directory: str,
        filename_format : str = "%Y%m%d%H%M%S.jsonl",
        bProcessTally :bool= False
    ) -> List[Dict]:
        """
        Retrieves the batch job status, waits until completion,
        downloads the result file, stores it, and loads the data.

        :param batch_job_id: The ID of the batch job to retrieve.
        :param base_directory: The base directory where the result file will be stored.
        :param poll_interval: Time in seconds between status checks.
        :param timeout: Maximum time in seconds to wait for completion.
        :return: A list of dictionaries containing the results.
        """
        
        # fetch batch job id - properties from DB
        objBatchRecords = None
        if batch_job_record_id is not None:
            objBatchRecords = await CGPTBatchAPIDB.MSGetBatchRecord(record_id=batch_job_record_id)

        try:
            # Retrieve the result file content
            # result_file = openai.File.retrieve(result_file_id)
            # result_content = openai.File.download(result_file_id).read()
            result_content = client.files.content(result_file_id).content
            
            # Define the sub-directory for output
            strSubDirName = os.path.join(
                DateHelper.get_current_timestamp("%d-%m-%Y"),
                "output"
            )
            strSubDirName = os.path.normpath(strSubDirName)  # Normalize path separators

            # Generate the filename based on the current timestamp
            timestamp = datetime.now().strftime(filename_format)
            file_path = os.path.join(base_directory, strSubDirName)

            os.makedirs(file_path, exist_ok=True)
            file_path += os.sep + timestamp
            # Ensure the file has a .jsonl extension
            if not file_path.endswith('.jsonl'):
                file_path += '.jsonl'
                
            # Write the result content to the file
            with open(file_path, 'wb') as file:
                file.write(result_content)
            print(f"Result file saved at: {file_path}")

            # Load data from the saved file
            results = []
            with open(file_path, 'r', encoding='utf-8') as file:
                for line in file:
                    json_object = json.loads(line.strip())
                    results.append(json_object)
            print(f"Loaded {len(results)} results from the result file.")

            lsTaskDetails = objBatchRecords.get("TaskDetails")
            # 1. iterate through each batch job task id
            for result in results:
                strCurrentCustomId = result.get("custom_id")

                try:
                    dictDocExtractionResponse = json.loads(result.get("response", {}).get("body", {}).get("choices", [])[0].get("message", {}).get("content"))
                except (IndexError, AttributeError, json.JSONDecodeError, TypeError) as e:
                    dictDocExtractionResponse = None
                selectedTaskDetail = CBatchAPIController.findDocId(lsTaskDetails=lsTaskDetails, strCustomTaskID=strCurrentCustomId)
                singleTaskDetail = selectedTaskDetail[strCurrentCustomId]
                document_id = int(singleTaskDetail.get("doc_id"))
                strModelName = singleTaskDetail.get("model_name")
                PromptId = singleTaskDetail.get("prompt_id")
                isGPTEnabled = singleTaskDetail.get("is_gpt_enabled")
                bPerformAWSExtraction = singleTaskDetail.get("perform_aws_extraction")
                iModelId = singleTaskDetail.get("model_id")
                
                # 2 save extracted data into DB
                await CGPTResponseData.insert_gpt_result(iUserId=userid, doc_id=document_id, strModelName= strModelName, obj_response=result, dict_response= dictDocExtractionResponse, PromptId=PromptId,iModelId=iModelId,isGPTEnabled=isGPTEnabled, bAwsExtraction=bPerformAWSExtraction)

                if bProcessTally:
                    # 3 Add to Tally
                    objParagTraders = CParagTraders(user_id=userid, doc_id = document_id)
                    tally_response = await objParagTraders.MSCallTallyAPI()
                    print("Tally Response",tally_response)
                    
            # update batch api job - status 
            await CGPTBatchAPIDB.MSUpdateBatchRecord(record_id= batch_job_record_id, update_fields={"Status":GPTBatchAPIStatusEnum.completed})
            
            return {
                "api_responses":results,
                "batch_api_reponse_jsonl":file_path,
                }
            

        except Exception as e:
            print(f"Error retrieving or storing batch results: {e}")
            raise e 

    @staticmethod
    def process_result_data(results: List[Dict],  num_results: int = 5):
        """
        Processes the result data by reading each task's extracted data
        and printing relevant information.

        :param results: A list of dictionaries containing the results.
        :param num_results: Number of results to process and display.
        """


        print(f"Processing the first {len(results)} results:")

        for res in results:
            try:
                task_id = res.get('custom_id')
                if not task_id:
                    print("Task ID not found in the result.")
                    continue

                # Extract index from task_id
                index_str = task_id.split('-')[-1]
                index = int(index_str)

                # Extract the result content
                result = res.get('response', {}).get('body', {}).get('choices', [])[0].get('message', {}).get('content', "")
                if not result:
                    print(f"No result content found for task ID: {task_id}")
                    continue


                # Print the formatted result
                print(f"\n\nRESULT: {result}")
                print("\n\n----------------------------\n\n")

            except Exception as e:
                print(f"Error processing result: {e}")
                continue

import asyncio

async def main():
    # Call your async method and capture the result
    # result = await CGPTBatchAPIDB.getBatchRecordsWithStatusFilter(status_list=["in_progress"])
    # print(result)
    # result = await CGPTBatchAPIDB.getBatchRecordsFromBatchId(batch_id="batch_67164e841778819084f285ecfbf06c52")
    # print(result)
    # result = await CGPTBatchAPIDB.MSGetBatchRecords(record_id=7)
    
    result = await CBatchAPIController.process_doc_ids(
            doc_ids=["41"],
            user_id=4,
            is_trial_paid_doc_extraction=True,
            debug=True
        )
    print("Insert Batch Job",result)
    # results = await CBatchAPIController.retrieve_and_store_batch_results(
    #         userid=result.get("userid"),
    #         batch_job_record_id=result.get("batch_job_record_id"),
    #         batch_job_id=result.get("batch_job_response").get("id"),
    #         base_directory="Data\\BatchAPI_TasksJsonlFile",
    #         bProcessTally=True,
    #         filename_format="%Y%m%d%H%M%S.jsonl"
    #     )
    # results = await CBatchAPIController.retrieve_and_store_batch_results(
    #         userid=4,
    #         batch_job_record_id=9,
    #         batch_job_id="batch_67174dc982d881909177ac70262a0561",
    #         base_directory="Data\\BatchAPI_TasksJsonlFile",
    #         bProcessTally=True,
    #         filename_format="%Y%m%d%H%M%S.jsonl"
    #     )
    # print("Retrieve Batch Job",results)

if __name__ == "__main__":
    # Run the async main function
    asyncio.run(main())
    
