from pydantic import BaseModel
from typing import Optional
from typing import List, Optional
from pydantic import BaseModel, Field


class GPTJSONUpdate(BaseModel):
    UpdatedVal: dict
    bApprove: bool
    
class DocExtractionAPIModel(BaseModel):
    document_id : int
    page_limit_left: Optional[int] = None
    total_allowed_page_limit: Optional[int] = None
    Document : Optional[dict] = None
    APIStatusCode: int = None
    DocErrorMsg: Optional[str] = None
    DocExtractionStatus : Optional[str] = None
    detail: Optional[str] = None
    IsPaidModel: Optional[bool] = None     

class CreateBatchAPITask(BaseModel):
    strSystemContent: str = "You are a helpful assistant designed to output JSO<PERSON>.",
    strUserContent: Optional[str] = "Who won the world series in 2020?",
    strModel: Optional[str] = "gpt-4o-2024-08-06", 
    intSeed: Optional[int] = 33,
    dictResponseFormat: dict = {"type": "json_object"}
    task_id : Optional[str]

class ProcessDocIDsRequest(BaseModel):
    """
    Request model for processing Document IDs.
    """
    lsDocIds: List[str] = Field(..., example=["docid1", "docid2", "docid3"])
    isTrialPaidDocExtraction: Optional[bool] = Field(
        False, description="Flag to indicate trial or paid document extraction."
    )
    bDebug: Optional[bool] = Field(
        False, description="Enable debug mode for detailed logs."
    )

class ProcessDocIDsResponse(BaseModel):
    """
    Response model for processing Document IDs.
    """
    success: bool = Field(..., example=True)
    message: Optional[str] = Field(None, example="Batch job created successfully.")
    batch_job_id: Optional[str] = Field(None, example="batch-job-12345")
    details: Optional[dict] = Field(None, description="Additional details or error information.")


class RetrieveBatchResultsRequest(BaseModel):
    """
    Request model for retrieving and storing batch results.
    """
    batch_job_record_id: int  = Field(None, example="1")
    batch_job_id: str = Field(..., example="batch-job-12345")
    base_directory: Optional[str] = Field(
        "Data\\BatchAPI_TasksJsonlFile",
        description="Base directory where the result file will be stored."
    )
    bProcessTally: Optional[bool] = Field(
        False,
        description="Process To Tally Flag."
    )
    filename_format: Optional[str] = Field(
        "%Y%m%d%H%M%S.jsonl",
        description="Timestamp format for the result filename."
    )

class RetrieveBatchResultsResponse(BaseModel):
    """
    Response model for retrieving and storing batch results.
    """
    success: bool = Field(..., example=True)
    message: Optional[str] = Field(None, example="Batch job completed successfully.")
    results: Optional[List] = Field(
        None,
        description="List of result dictionaries retrieved from the batch job."
    )
    file_path: Optional[str] = Field(
        None,
        description="Path to the saved result file."
    )