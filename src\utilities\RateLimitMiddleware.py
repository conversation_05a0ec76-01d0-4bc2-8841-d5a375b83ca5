from fastapi import Depends
import requests
from fastapi import Request, HTTPException, status
from jose import JWTError, jwt
from fastapi.responses import JSONResponse
from starlette.middleware.base import BaseHTTPMiddleware
from config.constants import Constants
from datetime import datetime
from datetime import timedelta
from collections import defaultdict
import asyncio
import os

# Your secret key and algorithm might differ
SECRET_KEY = os.getenv('JWT_SECRET')
ALGORITHM = "HS256"


user_request_counts = defaultdict(lambda: (0, datetime.min))
lock = asyncio.Lock()

class RateLimitingMiddleware(BaseHTTPMiddleware):
    def __init__(self, rate_limit_requests: int = Constants.GeneralRateLimitForDocExtractionService, rate_limit_duration: timedelta = timedelta(minutes=1)):
        self.rate_limit_requests = rate_limit_requests
        self.rate_limit_duration = rate_limit_duration
        asyncio.create_task(self.reset_request_counts())

    async def reset_request_counts(self):
        """
        This method is responsible for resetting the request counts for each user in the rate-limiting mechanism.
        It runs as an infinite loop, sleeping for the duration of the rate limit, and then iterates over the user_request_counts dictionary.
        For each user, it checks if the elapsed time since their last request exceeds the rate limit duration or if their request count is zero.
        If either condition is met, it deletes the user's entry from the dictionary.
        """
        while True:
            await asyncio.sleep(self.rate_limit_duration.total_seconds())
            async with lock:
                for key in list(user_request_counts.keys()):
                    request_count, last_request = user_request_counts[key]
                    elapsed_time = datetime.now() - last_request
                    if elapsed_time > self.rate_limit_duration or request_count == 0:
                        del user_request_counts[key]

    async def dispatch(self, request: Request):
        """
        This method is responsible for handling incoming requests and applying rate limiting based on the user's IP address and user ID.
        
        Inputs:
            (1) request: A FastAPI Request object representing the incoming request.
        
        Outputs:
            (1) A response object representing the result of processing the request.
        
        Exceptions:
            If any exception occurs during the processing of the request, raise a HTTPException with the corresponding status code and detail.
        """
        try:
            authorization: str = request.headers.get("Authorization")
            token_prefix, token = authorization.split()
            if token_prefix.lower() != "bearer":
                raise HTTPException(
                    status_code=status.HTTP_401_UNAUTHORIZED,
                    detail="Invalid authentication credentials, Please try logging in again.",
                    headers={"WWW-Authenticate": "Bearer"},
                )

            payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
            iUserID = payload.get("id", "anonymous")
            # Get public IP address using external service
            public_ip = self.get_public_ip()
            key = f"{public_ip}:{iUserID}"
            async with lock:
                request_count, last_request = user_request_counts[key]

                elapsed_time = datetime.now() - last_request

                if elapsed_time > self.rate_limit_duration:
                    request_count = 1
                else:
                    if request_count >= self.rate_limit_requests:
                        return JSONResponse(
                            status_code=429,
                            content={"message": "Rate limit exceeded. Please try again later."}
                        )
                    request_count += 1

                user_request_counts[key] = (request_count, datetime.now())
                return JSONResponse(
                            status_code=200,
                            content={"message": "User is allowed to access the resource."}
                        )
        except HTTPException as e:
            raise HTTPException(status_code=e.status_code, detail=e.detail)

        except Exception as e:
            raise HTTPException(status_code=500, detail="There was some unexpected error occurred, Please try again later.")

    def get_public_ip(self):
        response = requests.get('https://httpbin.org/ip')
        return response.json()['origin']
    