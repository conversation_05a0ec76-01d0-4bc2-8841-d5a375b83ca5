const TermsAndConditions = () => {
  return (
    <div className="container mx-auto p-4">
      <h1 className="text-2xl font-bold text-center mb-4">Terms and Conditions</h1>
      
      <p className="text-justify mb-2">
        Welcome to Accuvelocity.com! These terms and conditions outline the rules and regulations for the use of Accuvelocity.com's Website, located at www.accuvelocity.com.
      </p>
      
      <p className="text-justify mb-2">
        By accessing this website we assume you accept these terms and conditions. Do not continue to use Accuvelocity.com if you do not agree to take all of the terms and conditions stated on this page.
      </p>
      
      <h2 className="text-xl font-semibold mt-4 mb-2">Cookies</h2>
      <p className="text-justify mb-2">
        The website uses cookies to help personalize your online experience. By accessing Accuvelocity.com, you agreed to use the required cookies.
      </p>
      
      <h2 className="text-xl font-semibold mt-4 mb-2">License</h2>
      <p className="text-justify mb-2">
        Unless otherwise stated, Accuvelocity.com and/or its licensors own the intellectual property rights for all material on Accuvelocity.com. All intellectual property rights are reserved.
      </p>

      {/* Insert further sections as required */}
      
      <h2 className="text-xl font-semibold mt-4 mb-2">User Comments</h2>
      <ol className="list-decimal ml-5 mb-2">
        <li>This Agreement shall begin on the date hereof.</li>
        <li>Parts of this website offer an opportunity for users to post and exchange opinions and information. Accuvelocity.com does not filter, edit, publish or review Comments prior to their presence on the website.</li>
        {/* ... more items */}
      </ol>
      
      <h2 className="text-xl font-semibold mt-4 mb-2">Hyperlinking to our Content</h2>
      <p className="text-justify mb-2">
        The following organizations may link to our Website without prior written approval:
        <ul className="list-disc ml-5 mb-2">
          <li>Government agencies;</li>
          <li>Search engines;</li>
          <li>News organizations;</li>
          {/* ... more items */}
        </ul>
      </p>

      {/* Footer with the effective date of the terms */}
      <footer className="text-center mt-8">
        <p className="text-sm">
          These terms and conditions were Last updated April 08, 2024.
        </p>
      </footer>
    </div>
  );
};

export default TermsAndConditions;