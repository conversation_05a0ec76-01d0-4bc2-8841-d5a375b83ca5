import { useNavigate } from 'react-router-dom';
import { isActivePaidPlanAtom } from '../../context/TrailUsageUserData'
import { useRecoilState } from 'recoil';
import PropTypes from 'prop-types';

const Alert = ({ onDismiss }) => {
    const navigate = useNavigate();

    // Retrieve the account creation date from localStorage
    const createdDate = localStorage.getItem('created');
    const startDate = new Date(createdDate);
    const currentDate = new Date();

    // Calculate the difference in days
    const differenceInTime = currentDate.getTime() - startDate.getTime();
    const differenceInDays = Math.ceil(differenceInTime / (1000 * 3600 * 24));

    // Calculate days left for the trial
    const trialPeriod = 10; // Free trial period in days
    const daysLeft = trialPeriod - differenceInDays;
    const [isActivePlan, _] = useRecoilState(isActivePaidPlanAtom);

    // Handle upgrade button click
    const handleUpgrade = () => {
        navigate('/UpgradePlan');
    };
    // Determine the message to display
    let message;
    if (daysLeft > 0) {
        message = (
            <>
                🎉 <strong>{daysLeft} days</strong> left in your free trial!{" "}
                <button onClick={handleUpgrade} className="underline text-white hover:text-yellow-400">Upgrade now</button> to keep enjoying our premium features.
            </>
        );
    } else {
        message = (
            <>
                Your free trial has ended.{" "}
                <button onClick={handleUpgrade} className="underline text-white">Upgrade now</button> to continue enjoying our services.
            </>
        );
    }

    return (
        <>
        {(isActivePlan== "false" || isActivePlan == false) ?(<div className="flex h-10 z-20 justify-between sticky top-0 items-center bg-[#003654] text-[#fff] text-[10px] lgo:text-base w-screen group whitespace-nowrap">
            <span className="flex-grow text-center 2xl:text-base xl:text-xs">{message}</span>
            <button onClick={onDismiss} className="text-sm mr-8 text-white 2xl:text-base xl:text-xs">X</button>
        </div>):null}
        </>
    );
};

Alert.propTypes = {
    onDismiss: PropTypes.func.isRequired
};

export default Alert;
