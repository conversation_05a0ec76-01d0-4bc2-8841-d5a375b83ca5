import { useState, useEffect } from "react";
import DevLogo from '../../assets/MaintenancePage.svg';
import UnderMaintainance from '../../assets/SVGs/UnderMaintainance.svg'
const MaintenancePage = () => {
    const initialMinutes = 9; // Initial countdown minutes
    const initialSeconds = 0; // Initial countdown seconds

    const [minutes, setMinutes] = useState(() => {
        const savedMinutes = localStorage.getItem('minutes');
        return savedMinutes !== null ? JSON.parse(savedMinutes) : initialMinutes;
    });
    const [seconds, setSeconds] = useState(() => {
        const savedSeconds = localStorage.getItem('seconds');
        return savedSeconds !== null ? JSON.parse(savedSeconds) : initialSeconds;
    });

    useEffect(() => {
        const timerId = setInterval(() => {
            setSeconds((prevSeconds) => {
                if (prevSeconds > 0) {
                    return prevSeconds - 1;
                } else {
                    if (minutes > 0) {
                        setMinutes((prevMinutes) => prevMinutes - 1);
                        return 59;
                    } else {
                        clearInterval(timerId);
                        return 0;
                    }
                }
            });
        }, 1000);

        return () => clearInterval(timerId);
    }, [minutes]);

    useEffect(() => {
        localStorage.setItem('minutes', JSON.stringify(minutes));
        localStorage.setItem('seconds', JSON.stringify(seconds));
    }, [minutes, seconds]);

    return (
        <div className="flex h-full flex-col items-center justify-center bg-white min-h-screen">

            <div className=" mb-30">
                {/* SVG For Accuvelocity */}
                <img src={UnderMaintainance} />
            </div>

            <div>
                <img src={DevLogo} alt="AccuVelocity" className="h-[50vh] w-[30vw]" />
            </div>

            <p className="text-4xl mb-8 text-[#003654]" style={{ fontWeight: '600' }}>We&apos;ll be back soon!</p>

            <p className="text-2xl text-[#003654] text-center w-[60vw] mt-8" style={{ fontWeight: '300' }}>
                Sorry for the inconvenience. We&apos;re performing some maintenance at the
                moment.
            </p>
        </div>
    );
};

export default MaintenancePage;