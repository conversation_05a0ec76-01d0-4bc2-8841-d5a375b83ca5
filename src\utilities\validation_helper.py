import re
from fastapi import HTTPException
import i18n
from config.constants import Constants



class ValidationHelper:

    def is_mobile(cls, v):
        if not re.fullmatch(Constants.NUMBER_REGEX, v):
            message = i18n.t(key='Please enter valid mobile number')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_password(cls, v):
        if not re.fullmatch(Constants.PASSWORD_REGEX, v):
            message = i18n.t(key='The password must be at least six characters long')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_email(cls, v):
        if not re.fullmatch(Constants.EMAIL_REGEX, v):
            message = i18n.t(key= "Please enter valid email.")
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_lang(cls, v):
        if not re.fullmatch(Constants.LANG_REGEX, v):
            message = i18n.t(key='INVALID_LANG')
            raise HTTPException(status_code=400, detail=message)
        return v

    def is_valid_comment_type(cls, v):
        if not re.fullmatch(Constants.COMMENT_TYPE, v):
            message = i18n.t(key='INVALID_COMMENT_TYPE')
            raise HTTPException(status_code=400, detail=message)
        return v
