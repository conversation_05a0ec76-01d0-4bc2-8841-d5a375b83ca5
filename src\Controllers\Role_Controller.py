from sqlalchemy.exc import SQLAlchemyError
import traceback
from fastapi import HTTPException, Query
from sqlalchemy.future import select
from config.db_config import AsyncSessionLocal
from src.Schemas.Role_Schema import CreateRole, UpdateRole
from src.Models.models import Role
from src.Controllers.Logs_Controller import CLogController


class CRoleController:

    @staticmethod
    async def MSCreateRole(iUserID: int, objCreateRole: CreateRole):
        """
        Inputs  :   (1) objCreateRole   :   Object Of Role Data 

        Output  :   Json Containing Info about Newly Created Role 

        Purpose :   To Create A New Role

        Example :   await MSCreateRole(objCreateRole)
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Creation of Role {objCreateRole.RoleName} Started.")

            async with AsyncSessionLocal() as db:
                try:
                    objNewRole = Role(
                        RoleName=objCreateRole.RoleName,
                        RolePriority=objCreateRole.RolePriority
                    )

                    db.add(objNewRole)
                    await db.commit()
                    await db.refresh(objNewRole)
                    await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Created of Role {objCreateRole.RoleName}.")

                    return {"Role Data: ": objNewRole}

                except Exception as e:
                    await db.rollback()
                    await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Create Role {objCreateRole.RoleName}.")
                    raise HTTPException(
                        status_code=500, detail="Error occurred while Creating Role")

        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Create Role {objCreateRole.RoleName}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Error occurred while Creating Role")

    async def MUpdateRoleDetails(iUserID, iRoleId: int, objRoleData: UpdateRole):
        """
        Inputs  :   (1) iRoleId         :   Integer Role Id Of the Role to be updated
                    (2) objCreateRole   :   Object Of Role Data 

        Output  :   Json Containing Info about Updated Role Details 

        Purpose :   To Update Role Details 

        Example :   await MUpdateRoleDetails(iRoleId, objRoleData)   -> {
                                                                            "RoleId": 1,
                                                                            "RoleName": Admin,
                                                                            "Priority": 0
                                                                        }
        """
        try:
            async with AsyncSessionLocal() as session:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Role With RoleID {iRoleId} Started.")

                result = await session.execute(select(Role).filter(Role.Id == iRoleId))
                role = result.scalars().first()

                if not role:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Role With RoleID {iRoleId} Not Found.")
                    raise HTTPException(
                        status_code=404, detail=f"No such Role not found")

                for field, value in objRoleData.dict(exclude_unset=True).items():
                    if hasattr(role, field) and value is not None:
                        setattr(role, field, value)

                await session.commit()
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Updated Role With RoleID {iRoleId}.")

                return {
                    "RoleId": role.Id,
                    "RoleName": role.RoleName,
                    "Priority": role.RolePriority
                }
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed TO Update Role With Role ID {iRoleId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail=f"Failed to Update Role.")

    @staticmethod
    async def MSGetAllRoles(iUserID: int, iLimit: int = Query(10, gt=0), iOffset: int = Query(0, ge=0)) -> list:
        '''
        Purpose : This method is used to retrieve all the Roles.

        Inputs  : 
                    1) iLimit: The number of Roles to retrieve
                    2) iOffset: The offset from where to start the query

        Output  : list of Roles

        Example : await MSGetAllRoles()   --> [

                                                ]
        '''
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Process for Fetching All Roles Started.")

            async with AsyncSessionLocal() as db:  # Replace with your actual session maker

                result = await db.execute(select(Role).offset(iOffset).limit(iLimit))
                lsObjRoles = result.scalars().all()

                if not lsObjRoles:
                    await CLogController.MSWriteLog(iUserID, "Error", f"No Roles found.")

                    raise HTTPException(
                        status_code=404, detail="No Roles found.")

                mResponseData = [
                    {
                        "Id": objRole.Id,
                        "RoleName": objRole.RoleName,
                        "RolePriority": objRole.RolePriority
                        # Add any additional fields as needed
                    }
                    for objRole in lsObjRoles
                ]
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched All Roles.")

                return mResponseData
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch All Roles.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail="Failed To Retrieve All Roles Data.")
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"An Error occured while Retrieving Roles.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail=f"An Error occured while Retrieving Roles.")
        

    @staticmethod
    async def MSGetRoleById(iUserID, iRoleId: int):
        """
        Inputs  :   (1) iRoleId : Integer ID of the role to retrieve.

        Output  :   Json containing information about the specified role.

        Purpose :   To retrieve information of a specific role by ID.

        Example :   await MSGetRoleById(iRoleId)   -> {
                                                            "RoleId": 1,
                                                            "RoleName": "Admin",
                                                            "Priority": 0
                                                        }
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching Role by ID {iRoleId} started.")

            async with AsyncSessionLocal() as session:
                result = await session.execute(select(Role).filter(Role.Id == iRoleId))
                role = result.scalars().first()

                if not role:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Role with ID {iRoleId} not found.")
                    raise HTTPException(
                        status_code=404, detail=f"Role not found")

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched Role with ID {iRoleId}.")
                return {
                    "RoleId": role.Id,
                    "RoleName": role.RoleName,
                    "Priority": role.RolePriority
                }
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Fetch Role with ID {iRoleId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail=f"Failed to retrieve Role.")

    @staticmethod
    async def MSDeleteRole(iUserID, iRoleId: int):
        """
        Inputs  :   (1) iRoleId : Integer ID of the role to be deleted.

        Output  :   (dict) Confirmation message of deletion.

        Purpose :   To remove a specific role by ID.

        Example :   await MSDeleteRole(iRoleId)   -> {"detail": "Role successfully deleted."}
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", f"Process of Deleting Role with ID {iRoleId} Started.")

            async with AsyncSessionLocal() as session:
                result = await session.execute(select(Role).filter(Role.Id == iRoleId))
                roleToDelete = result.scalars().first()

                if not roleToDelete:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Role with ID {iRoleId} Not Found.")

                    raise HTTPException(
                        status_code=404, detail=f"Role does not exist")

                await session.delete(roleToDelete)
                await session.commit()
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Deleted Role with ID {iRoleId}.")

                return {"detail": f"Role with ID {iRoleId} successfully deleted."}
            
        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID,"Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Delete Role with ID {iRoleId}.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500, detail=f"Failed to delete Role with ID {iRoleId}. Error: {e}")
