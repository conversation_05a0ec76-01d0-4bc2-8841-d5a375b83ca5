// TrailUsageUserData.jsx
import { atom, useSetRecoilState, useRecoilValue } from 'recoil';

export const freePageLimitUsageAtom = atom({
  key: 'freePageLimitUsage',
  default: 0
});

export const totalFreePageLimitAtom = atom({
  key: 'totalFreePageLimit',
  default: 50
});

export const pageLimitLeftAtom = atom({
  key: 'pageLimitLeft',
  default: 0
});

export const totalPageLimitLeftAtom = atom({
  key: 'totalPageLimitLeft',
  default: 12
});

export const isTrialPaidDocExtractionAtom = atom({
  key: 'isTrialPaidDocExtraction',
  default: false
});

export const isTrialExpiredAtom = atom({
  key: 'isTrialExpired',
  default: false
});

export const isActivePaidPlanAtom = atom({
  key: 'isActivePaidPlan',
  default: false
});

export const ActivePlanNameAtom = atom({
  key: 'ActivePlanName',
  default: 'Trial'
});

export const ActivePlanTypeAtom = atom({
  key: 'ActivePlanType',
  default: 'monthly'
});


// Getter hooks to get current value of the state
export const useFreePageLimitUsageValue = () => useRecoilValue(freePageLimitUsageAtom);
export const useTotalFreePageLimitValue = () => useRecoilValue(totalFreePageLimitAtom);
export const usePageLimitLeftValue = () => useRecoilValue(pageLimitLeftAtom);
export const useTotalPageLimitLeftValue = () => useRecoilValue(totalPageLimitLeftAtom);
export const useIsTrialPaidDocExtractionValue = () => useRecoilValue(isTrialPaidDocExtractionAtom);
export const useIsTrialExpiredValue = () => useRecoilValue(isTrialExpiredAtom);
export const useIsActivePaidPlanValue= () => useRecoilValue(isActivePaidPlanAtom);
export const useActivePlanNameValue = () => useRecoilValue(ActivePlanNameAtom);
export const useActivePlanTypeValue = () => useRecoilValue(ActivePlanTypeAtom);

// Setter hooks to set state outside of components
export const useFreePageLimitUsage = () => useSetRecoilState(freePageLimitUsageAtom);
export const useTotalFreePageLimit = () => useSetRecoilState(totalFreePageLimitAtom);
export const usePageLimitLeft = () => useSetRecoilState(pageLimitLeftAtom);
export const useTotalPageLimitLeft = () => useSetRecoilState(totalPageLimitLeftAtom);
export const useIsTrialPaidDocExtraction = () => useSetRecoilState(isTrialPaidDocExtractionAtom);
export const useIsTrialExpired = () => useSetRecoilState(isTrialExpiredAtom);
export const useIsActivePaidPlan= () => useSetRecoilState(isActivePaidPlanAtom);
export const useActivePlanName = () => useSetRecoilState(ActivePlanNameAtom);
export const useActivePlanType = () => useSetRecoilState(ActivePlanTypeAtom);