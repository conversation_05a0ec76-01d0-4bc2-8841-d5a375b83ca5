{"cells": [{"cell_type": "code", "execution_count": 7, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Input: 'APUANE STATUARIO(Fu Lappato) 1200x2400'\n", "Output: 'APUANE STATUARIO (F.L.)'\n", "\n", "Input: 'TEXTURE STATUARIO RIGATO 1200x2400'\n", "Output: 'TEXTURE STATUARIO RIGATO'\n", "\n", "Input: 'CONIWOOD ABETE(6 Pcs) - 200x1200'\n", "Output: 'CONIWOOD ABETE'\n", "\n", "Input: 'APUANE STATUARIO(NATURALE) 600x1200'\n", "Output: 'APUANE STATUARIO (NATURAL)'\n", "\n", "Input: 'ACCADEMIA ONICE ARIA BEIGE(FULL LAPPATO) - 1200x2400'\n", "Output: 'ACCADEMIA ONICE ARIA BEIGE'\n", "\n"]}], "source": ["import re\n", "\n", "def MSGetBatchName(strItemDesc):\n", "        # Modify regex to ignore text in parentheses and stop before dimension or size indicator\n", "        batch_name_match = re.search(r'^(.*?)(?:\\s*\\(.*?\\))?(?=\\s*\\d+[Xx]\\d+|\\s*\\d+\\s*Pcs|\\s*-\\s*\\d+)', strItemDesc)\n", "        if batch_name_match:\n", "            batch_name = batch_name_match.group(1).strip()\n", "        else:\n", "            raise ValueError(\"Unable to find batch number.\")\n", "        \n", "        # Determine the code in parentheses based on keywords in description\n", "        if \"SOFT MATT\" in strItemDesc:\n", "            code = \"(S.M.)\"\n", "        elif \"LAPPATO MATT\" in strItemDesc:\n", "            code = \"(L.M.)\"\n", "        elif \"NATURALE\" in strItemDesc:\n", "            code = \"(NATURAL)\"\n", "        elif \"POLISHED\" in strItemDesc:\n", "            code = \"(PG)\"\n", "        <PERSON>if \"<PERSON> Lappato\" in strItemDesc:\n", "            code = \"(F.L.)\"\n", "        elif \"TECH MATT\" in strItemDesc:\n", "            code = \"(T.M.)\"\n", "        else:\n", "            code = \"\"\n", "\n", "        # Append the code to the batch name\n", "        batch_name = f\"{batch_name} {code}\".strip()\n", "        return batch_name\n", "\n", "# Test cases\n", "test_inputs = [\n", "    \"APUANE STATUARIO(Fu Lappato) 1200x2400\",\n", "    \"TEXTURE STATUARIO RIGATO 1200x2400\",\n", "    \"CONIWOOD ABETE(6 Pcs) - 200x1200\",\n", "    \"APUANE STATUARIO(NATURALE) 600x1200\",\n", "    \"ACCADEMIA ONICE ARIA BEIGE(FULL LAPPATO) - 1200x2400\"\n", "]\n", "\n", "# Run tests and print results\n", "for input_str in test_inputs:\n", "    try:\n", "        result = MSGetBatchName(input_str)\n", "        print(f\"Input: '{input_str}'\\nOutput: '{result}'\\n\")\n", "    except ValueError as e:\n", "        print(f\"Input: '{input_str}'\\nError: {e}\\n\")"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["Updated: {'Item Name': 'Item A', 'Rate': 10, 'QTY': 7, 'Amount': 70}\n", "[{'Item Name': 'Item A', 'Rate': 10, 'QTY': 7, 'Amount': 70}, {'Item Name': 'Item B', 'Rate': 20, 'QTY': 3, 'Amount': 60}]\n"]}], "source": ["# Assuming these are the structure of tally_body_obj and tally_entry\n", "# For example:\n", "tally_body_obj = [\n", "    {\"Item Name\": \"Item A\", \"Rate\": 10, \"QTY\": 5, \"Amount\": 50},\n", "    {\"Item Name\": \"Item B\", \"Rate\": 20, \"QTY\": 3, \"Amount\": 60},\n", "]\n", "\n", "# Example tally_entry\n", "tally_entry = {\"Item Name\": \"Item A\", \"Rate\": 10, \"QTY\": 2, \"Amount\": 20}\n", "\n", "# Function to update or append entry in tally_body_obj\n", "def update_or_append_entry(tally_body_obj, tally_entry):\n", "    # Check if there's an existing entry with the same \"Item Name\" and \"Rate\"\n", "    for obj in tally_body_obj:\n", "        if obj[\"Item Name\"] == tally_entry[\"Item Name\"] and obj[\"Rate\"] == tally_entry[\"Rate\"]:\n", "            # If found, update the QTY and Amount\n", "            obj[\"QTY\"] += tally_entry[\"QTY\"]\n", "            obj[\"Amount\"] += tally_entry[\"Amount\"]\n", "            print(f\"Updated: {obj}\")\n", "            return\n", "    \n", "    # If no match found, append the new entry\n", "    tally_body_obj.append(tally_entry)\n", "    print(f\"Appended: {tally_entry}\")\n", "\n", "# Test the function\n", "update_or_append_entry(tally_body_obj, tally_entry)\n", "print(tally_body_obj)  # To see the updated list\n"]}, {"cell_type": "markdown", "metadata": {}, "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Tally Data Processing Steps\n", "\n", "## Step 1: Execute Python Script\n", "- Run the following Python file to start the process: src\\utilities\\TallyEmailUtilsV3.py\n", "\n", "\n", "## Step 2: Open the Daily Data Folder for the Document Type\n", "- Based on the customer and document type, open the relevant folder. For example:\n", "- **Customer Name**: Parag Traders\n", "- **Document Type**: <PERSON><PERSON><PERSON>\n", "- **Location**:\n", "  ```\n", "  H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyData\n", "  ```\n", "\n", "## Step 3: Select the Current Date Directory\n", "- Open the directory for today’s date. Locate the JSON file of the document you need to process.\n", "\n", "## Step 4: Validate the Document\n", "- Ensure the document data is accurate and complete.\n", "\n", "## Step 5: Edit CSV Report File (If Needed)\n", "- If edits are required for the CSV report:\n", "- **Customer Name**: Parag Traders\n", "- **Document Type**: <PERSON><PERSON><PERSON>\n", "- **Location**:\n", "  ```\n", "  H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyData\\{current date (e.g., 2024_11_06)}\\Daily_Automation_Reports\n", "  ```\n", "\n", "## Optional Step: Resolve Failed or Error Documents\n", "- For any documents that failed or encountered errors, manually adjust the respective JSON file.\n", "\n", "## Step 6: <PERSON> <PERSON><PERSON> to Tally Software\n", "- Run below Single or Multiple Document Code using cmd => Alt + Enter  \n", "\n", "## Step 7: Generate the Test Report\n", "- Run any necessary tests and compile the report for review.\n", "\n", "\n", "## Step 8: For Final Validation Approve By <PERSON><PERSON><PERSON>  then Customer Submission\n", "- After validating the report, send it to the respective customer.\n", "\n", "## Company Stock Item Group -\n", "- Customer : Pararg Traders , \n", "- Type: <PERSON><PERSON><PERSON>\n", "- Location: \"W:\\AI Data\\17_ParagTraders\\1_simpolo\\SIMPOLO STOCK SUMMERY.pdf\" \n"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["from src.utilities.TallyEmailUtilsV3_Production import runBackupPolling, runAttachmentsPolling\n", "import os\n", "from dotenv import load_dotenv\n", "load_dotenv()\n", "import asyncio\n", "from  TallyEmailSender import SendTallyNotificationEmail \n", "from src.Controllers.ParagTradersControllers import CSimpoloModel\n", "import nest_asyncio\n", "nest_asyncio.apply()"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Single Document - Insert To Tally Software "]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/plain": ["[{'status': 'skipped', 'message': 'No backup record found for today.'}]"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["\n", "dictDummyTallyHeader = {\n", "                \"X-Auth-Key\": \"live_e4fa57395a08401faa1b27b8d538d21e\",\n", "                \"Template-Key\": \"7\",\n", "                \"CompanyName\": \"PARAG TRADERS (GST)\",\n", "                \"AddAutoMaster\": \"0\",\n", "                \"Automasterids\": \"1,2,3\",\n", "                \"version\": \"1\",\n", "                \"Content-Type\": \"application/json\"\n", "            }\n", "# Now run your asyncio.gather call\n", "await asyncio.gather(CSimpoloModel.MSCallTallyAPI( iUserID=4, \n", "                                        strTallyFilePath=r\"H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyData\\2024_11_06\\2411010165.json\", \n", "                                        tally_header_obj=dictDummyTallyHeader))"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": []}, {"cell_type": "markdown", "metadata": {}, "source": ["# Multiple Document - Insert To Tally Software"]}, {"cell_type": "code", "execution_count": 5, "metadata": {}, "outputs": [{"data": {"text/plain": ["[[[{'Result': 'Records 1 Processed Successfully'}]]]"]}, "execution_count": 5, "metadata": {}, "output_type": "execute_result"}], "source": ["import nest_asyncio\n", "nest_asyncio.apply()\n", "dictDummyTallyHeader = {\n", "                \"X-Auth-Key\": \"live_e4fa57395a08401faa1b27b8d538d21e\",\n", "                \"Template-Key\": \"7\",\n", "                \"CompanyName\": \"PARAG TRADERS (GST)\",\n", "                \"AddAutoMaster\": \"0\",\n", "                \"Automasterids\": \"1,2,3\",\n", "                \"version\": \"1\",\n", "                \"Content-Type\": \"application/json\"\n", "            }\n", "# Now run your asyncio.gather call\n", "await asyncio.gather(CSimpoloModel.InsertTallyRecordForDocs(directory_path = r\"H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyData\\2024_11_06\", iUserID=4, tally_header_obj=dictDummyTallyHeader))"]}, {"cell_type": "markdown", "metadata": {}, "source": ["# Send Email Report "]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [], "source": ["currentCSVReportPath = r\"H:\\AI Data\\17_ParagTraders\\1_simpolo\\DailyData\\2024_11_06\\Daily_Automation_Reports\\Report_2024-11-06.csv\"\n", "\n", "MAIL_USERNAME=os.getenv('MAIL_USERNAME'),\n", "    \n", "SendTallyNotificationEmail(csvReportPath=currentCSVReportPath, \n", "                            strReceiverName=\"Parag Traders\",\n", "                            strSubject=\"Tally Invoice Posting Report\", \n", "                            strMailFrom=os.getenv('MAIL_FROM'), \n", "                            lsMailTo=[\"<EMAIL>\", \"<EMAIL>\"], \n", "                            strServer=os.getenv('MAIL_SERVER'), \n", "                            intPort=int(os.getenv('SMTP_PORT')), \n", "                            strPassword=os.getenv('MAIL_PASSWORD'), \n", "                            htmlTemplatePath=r\"resource\\TallyEmailTemplate.html\", \n", "                            lsCC=[])"]}, {"cell_type": "code", "execution_count": 3, "metadata": {}, "outputs": [{"data": {"text/plain": ["{'Header': {'X-Auth-Key': 'live_e4fa57395a08401faa1b27b8d538d21e',\n", "  'Template-Key': '7',\n", "  'CompanyName': 'PARAG TRADERS (GST)',\n", "  'AddAutoMaster': '0',\n", "  'Automasterids': '1,2,3',\n", "  'version': '1',\n", "  'Content-Type': 'application/json'},\n", " 'body': {'body': [{'Invoice Date': '10.27.2024',\n", "    'Invoice No': 'AV/Purc/29102024-112118',\n", "    'Voucher Type': 'PURCHASE A/C',\n", "    'Supplier Name': 'SIMPOLO VITRIFIED PVT.LTD.',\n", "    'Purchase Ledger': 'GST INTERSTATE PURCHASE (18%)',\n", "    'Tax Rate': 18,\n", "    'UOM': 'Box',\n", "    'IGST Ledger': 'IGST INPUT A/C',\n", "    'IGST Amount': 9393.55,\n", "    'Supplier Invoice No': 2421009387,\n", "    'Supplier Invoice Date': '10.27.2024',\n", "    'Godown': 'Godown',\n", "    'Round off Ledger': 'Rounding Off (PURCHASE)',\n", "    'Round off Amount': 0.03999999999359716,\n", "    'Consignor From Name': 'PARAG TRADERS (GST)',\n", "    'Consignor From Add 1': '(A UNIT OF VHD DISTRIBUTORS LLP)',\n", "    'Consignor From Add 2': '12/4,RACE COURSE ROAD,INDORE',\n", "    'Consignor From Add 3': 'PH:0731-2535659/2535660',\n", "    'Consignor From State': 'Madhya Pradesh, Code : 23',\n", "    'Consignor From Place': 'Madhya Pradesh',\n", "    'Tin No': 'TIN-23579075743',\n", "    'Consignor From GSTIN': '23AAKFV4306N1ZX',\n", "    'GSTIN/UIN': '24AALCS1872N1ZU',\n", "    'Item Name': 'SIMPOLO TILES 800 X 2400 I',\n", "    'Batch No': 'WARM GREY (PG)',\n", "    'QTY': 27,\n", "    'Rate': 1642.91,\n", "    'Discount': 15.0,\n", "    'Amount': 52186.41}]}}"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["import json\n", "json.loads(\"{\\\"Header\\\": {\\\"X-Auth-Key\\\": \\\"live_e4fa57395a08401faa1b27b8d538d21e\\\", \\\"Template-Key\\\": \\\"7\\\", \\\"CompanyName\\\": \\\"PARAG TRADERS (GST)\\\", \\\"AddAutoMaster\\\": \\\"0\\\", \\\"Automasterids\\\": \\\"1,2,3\\\", \\\"version\\\": \\\"1\\\", \\\"Content-Type\\\": \\\"application/json\\\"}, \\\"body\\\": {\\\"body\\\": [{\\\"Invoice Date\\\": \\\"10.27.2024\\\", \\\"Invoice No\\\": \\\"AV/Purc/29102024-112118\\\", \\\"Voucher Type\\\": \\\"PURCHASE A/C\\\", \\\"Supplier Name\\\": \\\"SIMPOLO VITRIFIED PVT.LTD.\\\", \\\"Purchase Ledger\\\": \\\"GST INTERSTATE PURCHASE (18%)\\\", \\\"Tax Rate\\\": 18, \\\"UOM\\\": \\\"Box\\\", \\\"IGST Ledger\\\": \\\"IGST INPUT A/C\\\", \\\"IGST Amount\\\": 9393.55, \\\"Supplier Invoice No\\\": 2421009387, \\\"Supplier Invoice Date\\\": \\\"10.27.2024\\\", \\\"Godown\\\": \\\"Godown\\\", \\\"Round off Ledger\\\": \\\"Rounding Off (PURCHASE)\\\", \\\"Round off Amount\\\": 0.03999999999359716, \\\"Consignor From Name\\\": \\\"PARAG TRADERS (GST)\\\", \\\"Consignor From Add 1\\\": \\\"(A UNIT OF VHD DISTRIBUTORS LLP)\\\", \\\"Consignor From Add 2\\\": \\\"12/4,RACE COURSE ROAD,INDORE\\\", \\\"Consignor From Add 3\\\": \\\"PH:0731-2535659/2535660\\\", \\\"Consignor From State\\\": \\\"Madhya Pradesh, Code : 23\\\", \\\"Consignor From Place\\\": \\\"Madhya Pradesh\\\", \\\"Tin No\\\": \\\"TIN-23579075743\\\", \\\"Consignor From GSTIN\\\": \\\"23AAKFV4306N1ZX\\\", \\\"GSTIN/UIN\\\": \\\"24AALCS1872N1ZU\\\", \\\"Item Name\\\": \\\"SIMPOLO TILES 800 X 2400 I\\\", \\\"Batch No\\\": \\\"WARM GREY (PG)\\\", \\\"QTY\\\": 27, \\\"Rate\\\": 1642.91, \\\"Discount\\\": 15.0, \\\"Amount\\\": 52186.41}]}}\")"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["{\n", "    \"body\": [\n", "        {\n", "\"Invoice Date\": \"11.06.2024\",\n", "\"Invoice No\": \"AV/Purc/29102024-112118\",\n", "\"Voucher Type\": \"PURCHASE A/C\",\n", "\"Supplier Name\": \"SIMPOLO VITRIFIED PVT.LTD.\",\n", "\"Purchase Ledger\": \"GST INTERSTATE PURCHASE (18%)\",\n", "\"Tax Rate\": 18,\n", "\"UOM\": \"Box\",\n", "\"IGST Ledger\": \"IGST INPUT A/C\",\n", "\"IGST Amount\": 9393.55,\n", "\"Supplier Invoice No\": 2421009387,\n", "\"Supplier Invoice Date\": \"10.27.2024\",\n", "\"Godown\": \"Godown\",\n", "\"Round off Ledger\": \"Rounding Off (PURCHASE)\",\n", "\"Round off Amount\": 0.03999999999359716,\n", "\"Consignor From Name\": \"PARAG TRADERS (GST)\",\n", "\"Consignor From Add 1\": \"(A UNIT OF VHD DISTRIBUTORS LLP)\",\n", "\"Consignor From Add 2\": \"12/4,RACE COURSE ROAD,INDORE\",\n", "\"Consignor From Add 3\": \"PH: 0731-2535659/2535660\",\n", "\"Consignor From State\": \"Madhya Pradesh, Code : 23\",\n", "\"Consignor From Place\": \"Madhya Pradesh\",\n", "\"Tin No\": \"TIN-23579075743\",\n", "\"Consignor From GSTIN\": \"23AAKFV4306N1ZX\",\n", "\"GSTIN/UIN\": \"24AALCS1872N1ZU\",\n", "\"Item Name\": \"SIMPOLO TILES 800 X 2400 I\",\n", "\"Batch No\": \"WARM GREY (PG)\",\n", "\"QTY\": 27,\n", "\"Rate\": 1642.91,\n", "\"Discount\": 15.0,\n", "\"Amount\": 52186.41\n", "        }\n", "    ]\n", "}"]}], "metadata": {"kernelspec": {"display_name": "5_Env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}