from sqlalchemy import Column, Integer, String, Float, DateTime, ForeignKey, Text, LargeBinary, Enum, Table, Boolean, Double, Date, func
# from sqlalchemy.sql.sqltypes import DateTime, Integer, String
from sqlalchemy.orm import relationship
from config.db_config import Base, engine
from sqlalchemy.dialects.mysql import LONGBLOB, MEDIUMBLOB, LONGTEXT, MEDIUMTEXT
import enum
from datetime import datetime
import pytz
from sqlalchemy.types import JSON  # Import JSON here
from config.constants import Constants
from sqlalchemy.ext.declarative import declarative_base
import os
from os.path import join, dirname
from dotenv import load_dotenv


# Load environment variables from .env file
dotenv_path = join(os.path.dirname(dirname(__file__)), ".env")

load_dotenv(dotenv_path)

# * ------------------------ Enums ------------------------

class VoucherType(str, enum.Enum):
    PV_WITH_INVENTORY = "PV_WITH_INVENTORY"
    PV_WITHOUT_INVENTORY = "PV_WITHOUT_INVENTORY"
    DELIVERY_NOTE = "DELIVERY_NOTE"
    JOURNAL_VOUCHER = "JOURNAL_VOUCHER"
    BANK_STATEMENT = "BANK_STATEMENT"
    RECEIPT_NOTE = "RECEIPT_NOTE"
    PURCHASE_ORDER = "PURCHASE_ORDER"


class TDLProcessingRecordStatusEnum(enum.Enum):
    Failed = "Failed"
    Completed = "Completed"
    Processing = "Processing"
    Pending = "Pending"

class TallyStatusEnum(enum.Enum):
    Success = "Success"
    Error = "Error"
    NotProcess = "NotProcess"
    Processing = "Processing"
    Skipped = "Skipped"
    PartialSuccess = "PartialSuccess"
    Duplicate = "Duplicate"
    ValidationError = "ValidationError"
    NOT_APPLICABLE = 'NOT_APPLICABLE'

class StatusEnum(enum.Enum):
    Approved = "Approved"
    Error = "Error"
    NotProcess = "NotProcess"
    OnHold = "OnHold"
    ToBeApproved = "ToBeApproved"
    Processing = "Processing"

class LogTypeEnum(enum.Enum):
    INFO = "Information"
    WARNING = "Warning"
    DEBUG = "Debug"
    ERROR = "Error"

class QueryCategory(enum.Enum):
    PRICE = "Price"
    AUTOMATION = "Automation"
    OTHER = "Other"
    PROCESS = "Process"

class Status_ContactUsQuery(enum.Enum):
    OPEN = "Open"
    CLOSE = "Close"
    IN_PROGRESS = "In Progress"

class UserLogSectionEnum(enum.Enum):
    Upload = "Upload"
    MyDocuments = "MyDocuments"
    MyModels = "MyModels"
    Billing = "Billing"
    Profile = "Profile"

# Enum for the Status field
class GPTBatchAPIStatusEnum(enum.Enum):
    in_progress = "in_progress"
    completed = "completed"
    error = "error"

class EmailProcessingStatusEnum(enum.Enum):
    FAILED_TO_PROCESS = "Failed_to_Process"
    PROCESSED = "Processed"
    Failed = "Failed"
    Completed = "Completed"
    Processing = "Processing"
    Pending = "Pending"

class TallyBackupStatusEnum(enum.Enum):
    SUCCESS = "Success"
    FAILED = "Failed"

class XMLImportTypeEnum(enum.Enum):
    Manual_Import   = "Manual_Import"
    EXE_Initiated_Request = "EXE_Initiated_Request"
    NOT_APPLICABLE = "NOT_APPLICABLE"
    
# * ------------------------ Models Definition ------------------------
# Initializing


class User(Base):
    __tablename__ = "users"
    uid = Column(Integer, primary_key=True)
    name = Column(String(50), nullable=False)
    password = Column(String(255), nullable=False)
    roleID = Column(Integer, ForeignKey('role.Id', ondelete="CASCADE"), nullable=False, default=lambda: Role.query.filter_by(RoleName='General').first().Id)
    email = Column(String(255), nullable=False, unique=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    phoneNumber = Column(String(20))
    profilePicture = Column(MEDIUMBLOB, nullable=True)
    Country = Column(String(60), nullable=True)
    usePaidOCR = Column(Boolean, default=False)
    usePaidDocExtractor = Column(Boolean, default=False)
    

class UserAPIUsage(Base):
    __tablename__ = "user_api_usage"

    id = Column(Integer, primary_key=True, index=True)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"),nullable=False)
    active_plan_name = Column(String(50),nullable=False, default="Free")
    is_subscription_active = Column(Boolean, default=False)
    active_plan_type = Column(Enum('monthly', 'yearly'),nullable=True)
    used_tokens = Column(Integer, nullable=False, default=0)
    api_requested = Column(Integer, nullable=False, default=0)
    page_limit_left = Column(Integer, nullable=False, default=0)
    total_allowed_page_limit = Column(Integer, nullable=False, default=12)
    free_page_limit_usage = Column(Integer, nullable=False, default=0)
    total_allowed_free_page_limit = Column(Integer, nullable=False, default=50)
    modified_time = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
        pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

# Defining the PasswordReset ModelTable


class PasswordReset(Base):
    __tablename__ = 'password_resets'
    email = Column(String(255), primary_key=True, nullable=False)
    token = Column(String(255), nullable=False)
    created_at = Column(DateTime(timezone=True), index=True,
                        default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class UploadedDoc(Base):
    __tablename__ = 'uploaded_docs'
    DocId = Column(Integer, primary_key=True)
    UserId = Column(Integer, ForeignKey('users.uid'), index=True)
    ModelId = Column(Integer, ForeignKey('modeltable.Id', ondelete="CASCADE"))
    DocName = Column(String(200), nullable=False)
    PageCount = Column(Integer, default=0, nullable=True)
    file_type =  Column(String(50), nullable=False)
    is_scanned_document = Column(Integer, default=0)
    UsedPaidModel = Column(Boolean, nullable=True) 
    DocDebugMsg = Column(LONGTEXT, nullable=True)
    DocErrorMsg = Column(LONGTEXT, nullable=True)
    DocExtractionAPIStatusCode = Column(Integer, nullable=True, comment="HTTP status code from the document extraction API")
    # Ensure this is compatible with your database dialect
    # DocBinaryData = Column(LONGBLOB)
    UploadedDateTime = Column(DateTime(timezone=True), index=True,
                              default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
        pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    Status = Column(Enum(StatusEnum))
    TallyStatus = Column(Enum(TallyStatusEnum)) # TODO: REmove this column Discarded From Version 1.19 
    DocS3ObjectKey = Column(String(500), nullable=False)
    Comment = Column(String(2000), nullable=False)
    CommentByUserId = Column(Integer, ForeignKey('users.uid', ondelete="SET NULL"), nullable=True)
    ApprovedByUserId = Column(Integer, ForeignKey('users.uid', ondelete="SET NULL"), nullable=True)
    DocumentRawTxt = Column(JSON, nullable=True)
    DocumentRawTxtObject = Column(JSON, nullable=True)
    DocumentRawTxtExtractBy = Column(String(50), nullable=True)
    isDeleted = Column(Boolean, default=False)  # Add this line
    GPTResponseForModel = Column(JSON, nullable=True)
    AdditionalDocDetails = Column(JSON, nullable=True)
    # Assuming "doc_extracted_data" is the correct relationship name to match DocExtractedData's back_populates
    doc_extracted_data = relationship(
        "DocExtractedData", back_populates="uploaded_doc")
    CheckSum = Column(String(200), nullable=True)
    DocVendorName =  Column(String(200), nullable=True)  # Document Name
    DocUniqueNo = Column(String(200), nullable=True)     # Document Unique Number
    DocDate = Column(String(50), nullable=True)          # Document Date
    DocTotalAmount = Column(String(50), nullable=True)   # Total Amount of Document
    DocRetryCount = Column(Integer, nullable=False, default=1)
    DocRetryDetails = Column(JSON, nullable=True)
    ModelNameAlias = Column(String(200), nullable=True)
    
class AVRequestDetail(Base):
    __tablename__ = 'AVRequestDetail'

    ID = Column(Integer, primary_key=True, autoincrement=True)
    strClientREQID = Column(String(255), nullable=False)
    NetworkLocation = Column(JSON,nullable=True)
    ClientImportedXMLStatusCode = Column(String(50), nullable=True, default=999)
    ClientImportedXMLResponse = Column(Text, nullable=True, default=None)
    strVoucherType = Column(Enum(VoucherType), nullable=True)
    CImportedXML_Type = Column(Enum('Manual_Import', 'EXE_Initiated_Request','NOT_APPLICABLE'), nullable=True, default='NOT_APPLICABLE')
    CReqGeneratedTimeAt = Column(DateTime(timezone=True), nullable=False)
    CReqIMPORTEDXMLTimeAt = Column(DateTime(timezone=True), nullable=True, default=None)
    CReqDeliveredTimeAt = Column(DateTime(timezone=True), nullable=True, default=None)
    CReqServerReceivedAt = Column(DateTime(timezone=True), nullable=True)
    CReqServerCompletedAt = Column(DateTime(timezone=True), nullable=True, default=None)
    AVXMLGeneratedStatus = Column(Enum('Success', 'Skipped', 'PartialSuccess', 'Duplicate','NOT_APPLICABLE','ValidationError'), nullable=True, default='NOT_APPLICABLE')
    ReqDocType = Column(String(255), default="", nullable=True)
    ReqDocName = Column(String(255), default="", nullable=True)
    ReqDocHashCode = Column(String(255), default=None, nullable=True)
    MultipleVendorEnabled = Column(Boolean, default=False)
    UServerName = Column(String(255), default="TallyServer1")
    strSystemUserName = Column(String(255), default="Unknown")
    PriceListVerification = Column(Boolean, default=False)
    strAccuVelocityComments = Column(LONGTEXT, default="-")
    TracebackLogs = Column(LONGTEXT, default="")
    RecievedDate = Column(Date, default=datetime.now().date(), nullable=False)
    strServerEstProcessingTime = Column(String(255), default="NOT_APPLICABLE", nullable=True)
    strCustomerName = Column(String(255), default="Unknown", nullable=False)
    EstAccountantTimeSaved = Column(String(255), default="NOT_APPLICABLE", nullable=True)
    strXMLResponse = Column(LONGTEXT, default="")
    FileContent = Column(JSON, nullable=True)
    GRN_UID = Column(String(255), nullable=True, default=None)
    PO_UID = Column(String(255), nullable=True, default=None)
    DOC_UID = Column(String(255), nullable=True, default=None)
    BANKSTATEMENT_UID = Column(String(255), nullable=True, default=None)
    IsRecordUIDPresent = Column(Boolean, default=False)
    User_UID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"))
    bTestMode = Column(Boolean, nullable=True, default=False)
    ImprestJournalID = Column(Integer, nullable=True)

class AbhinavDayBook(Base):
    __tablename__ = 'abhinav_day_book'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, nullable=True)
    bDevMode = Column(Boolean, nullable = True)
    previous_day_data = Column(LargeBinary, nullable=True)
    current_day_data = Column(LargeBinary, nullable=True)
    created_at = Column(DateTime, nullable=False, default=datetime.now())
    updated_at = Column(DateTime, nullable=True)
    

class GRNNoPITracking(Base):
    __tablename__ = 'grn_no_pi_tracking'
    __table_args__ = {
        'comment': 'For Abhinav Infra purchase invoices, we are tracking the GRN number to ensure that no duplicate GRNs are used in any purchase invoice.'
    }
    id = Column(Integer, primary_key=True, autoincrement=True)
    GrnNo = Column(JSON, nullable=False)
    Doc_id = Column(Integer, ForeignKey('uploaded_docs.DocId'), nullable=False)
    VendorName = Column(String(200), nullable=False)
    TallyImportedStatus = Column(String(50), nullable=True, default=999)


class ImprestJournalDetails(Base):
    __tablename__ = 'ImprestJournalDetails'


    JVID = Column(Integer, primary_key=True,comment="Journal Voucher ID that Uniquely Identifies Each Journal Entry")
    strClientREQID = Column(String(255), nullable=False)
    JournalDataFrame = Column(LargeBinary, nullable=True)
    JVSheetNo = Column(Integer, nullable=False)  # Constraint for range 1-100 handled in validation
    EmprestHolder = Column(String(255), nullable=False)
    SiteName = Column(String(255), nullable=False)
    EmailAddress = Column(String(255), nullable=False)
    DateOfExpensePeriod = Column(String(255), nullable=True)
    TotalEntries = Column(Integer, nullable=True)
    TotalAmount = Column(Float, nullable=False)
    AVXMLGeneratedStatus = Column(Enum(TallyStatusEnum), nullable=True, default=TallyStatusEnum.Skipped)
    AVComments = Column(Text, nullable=True)
    RecievedDate = Column(DateTime(timezone=True), nullable=True, default=func.now())
    CReqGeneratedTimeAt = Column(DateTime(timezone=True), nullable=True)
    EstAccountantTimeSaved = Column(String(255), nullable=True)
    CImportedXML_Type = CImportedXML_Type = Column(Enum(XMLImportTypeEnum), nullable=True)
    CReqIMPORTEDXMLTimeAt = Column(DateTime(timezone=True), nullable=True)
    ClientImportedXMLStatusCode = Column(String(255), nullable=True)
    strXMLResponse = Column(Text, nullable=True)
    TracebackLogs = Column(Text, nullable=True)
    ImprestFileID = Column(Integer, nullable=True, comment="An unique ID that uniquley identifies each input Excel File")

class Prompt(Base):
    __tablename__ = 'prompt'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"))
    ModelId = Column(Integer, ForeignKey('modeltable.Id', ondelete="CASCADE"))
    ModelSeries = Column(String(200), unique=False, nullable=False)  # Ensure this is unique if used as a foreign key reference
    prompt = Column(LONGTEXT, nullable=False)
    
    CreatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class ModelTable(Base):

    __tablename__ = 'modeltable'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"))
    preDefinedModelDict = Column(JSON, nullable=False)
    Name = Column(String(255), nullable=False)
    FamilyName = Column(String(255), nullable=False)
    Description = Column(String(255), nullable=True)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                            default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class ModelFields(Base):
    __tablename__ = 'model_fields_data'
    Id = Column(Integer, primary_key=True)
    ModelId = Column(Integer, ForeignKey('modeltable.Id', ondelete="CASCADE"), nullable=False) 
    FieldData = Column(JSON, nullable=False)  # This will hold a JSON with all fields
    CreatedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class DocExtractedData(Base):
    __tablename__ = 'doc_extracted_data'
    Id = Column(Integer, primary_key=True)
    DocId = Column(Integer, ForeignKey('uploaded_docs.DocId', ondelete="CASCADE"))
    Response = Column(JSON)
    DocExtractionPromptID = Column(Integer, ForeignKey('prompt.Id', ondelete="CASCADE"))
    DocVerifiedData = Column(JSON)
    DocApprovedData = Column(JSON)
    CreatedTime = Column(DateTime(timezone=True), index=True,
                         default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
        pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    # Ensure "uploaded_doc" matches UploadedDoc's back_populates
    uploaded_doc = relationship(
        "UploadedDoc", back_populates="doc_extracted_data")



class Logs(Base):
    __tablename__ = 'logs'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    LogType = Column(Enum(LogTypeEnum), nullable=False)
    CallerFunction = Column(MEDIUMTEXT, nullable=False)
    LogMessage = Column(LONGTEXT, nullable=False)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))



class Role(Base):

    __tablename__ = 'role'
    Id = Column(Integer, primary_key=True)
    RoleName = Column(String(255), unique=True, nullable=False)
    RolePriority = Column(Integer, nullable=False)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class UpCommingUsers(Base):

    __tablename__ = 'up_comming_users'
    Id = Column(Integer, primary_key=True)
    email = Column(String(255), nullable=False, unique=True)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class OrganizationDetails(Base):
    __tablename__ = 'organization_details'
    
    # Define columns for the table
    Id = Column(Integer, primary_key=True)
    User_Id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    CompanyName = Column(String(255), nullable=False)
    CompanyURL = Column(String(255), nullable=True)
    Designation = Column(String(255), nullable=False)
    TeamStrength = Column(String(255), nullable=False)
    InvoicesProcessedPerDay = Column(String(255), nullable=False)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')),
                             onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    

class PromoCode(Base):
    __tablename__ = "promo_codes"

    PromoCodeId = Column(Integer, primary_key=True, autoincrement=True)
    PromoCodeName = Column(String(Constants.MaxPromoCodeLength), primary_key=True, index=True)
    CreatedTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ExpireTime = Column(DateTime)
    PromoCodeType = Column(String(255))  # Length specified
    Action = Column(String(255))  # Length specified if needed
    Value = Column(String(255))  # Length specified if needed
    MaxPromptCodeUseCount = Column(Integer, default=Constants.DefaultMaxPrompoCodeUseCount)
    IsPromoExpired = Column(Boolean, default=False)

class PromoCodeUsage(Base):
    __tablename__ = "promo_codes_usage"
    promocode_usage_id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    promo_code_id = Column(Integer, ForeignKey('promo_codes.PromoCodeId', ondelete="CASCADE"), nullable=False)
    used_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class Plan(Base):
    __tablename__ = 'plans'

    id = Column(Integer, primary_key=True, autoincrement=True)
    stripe_price_id = Column(String(255), nullable=False)
    stripe_product_id = Column(String(255), nullable=False)
    plan_name = Column(String(255), nullable=False)
    plan_price = Column(Float, nullable=False)
    plan_currency = Column(String(10), nullable=False)
    plan_active = Column(Boolean, default=True)
    payment_type = Column(Enum('one_time', 'recurring', name='payment_type_enum'), nullable=False)
    plan_type = Column(Enum('monthly', 'yearly', 'top_up', name='plan_type_enum'), nullable=False)
    description = Column(String(255), nullable=True)
    pageCount = Column(Integer, nullable=False)
    LIVEMODE = Column(Boolean, default=bool(os.getenv('LIVEMODE') == "true"))
    created_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=datetime.now(pytz.timezone('Asia/Kolkata')))


class StripeUser(Base):
    __tablename__ = 'stripe_user'

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    stripe_customer_id = Column(String(255), nullable=False)
    created_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=datetime.now(pytz.timezone('Asia/Kolkata')))
    LIVEMODE = Column(Boolean, default=bool(os.getenv('LIVEMODE') == "true"))
    stripeResponse =  Column(JSON, nullable=False)
    user = relationship('User')


class UserSubscription(Base):
    __tablename__ = 'users_subscription'

    id = Column(Integer, primary_key=True, autoincrement=True)
    stripe_id = Column(Integer, ForeignKey('stripe_user.id', ondelete="CASCADE"), nullable=True)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    plan_id = Column(Integer, ForeignKey('plans.id', ondelete="CASCADE"), nullable=False)
    subscription_id = Column(String(255), nullable=True)
    plan_start_date = Column(DateTime, nullable=False)
    plan_end_date = Column(DateTime, nullable=False)
    payment_type = Column(Enum('one_time', 'recurring', name='payment_type_enum'), nullable=False)
    current_status = Column(String(50), nullable=False)
    created_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime, default=datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=datetime.now(pytz.timezone('Asia/Kolkata')))
    LIVEMODE = Column(Boolean, default=bool(os.getenv('LIVEMODE') == "true"))
    stripeResponse =  Column(JSON, nullable=False)
    plan = relationship('Plan')
    stripe_user = relationship('StripeUser')
    user = relationship('User')


class ContactUs(Base):
    __tablename__ = "contact_us"
    id = Column(Integer, primary_key=True)
    query_category = Column(Enum(QueryCategory), nullable=False)
    customer_name = Column(String(100), nullable=False)
    email = Column(String(255), nullable=False)
    phone_number = Column(String(20), nullable=True)
    country = Column(String(60), nullable=True)
    designation = Column(String(100), nullable=True)
    company_name = Column(String(100), nullable=True)
    message = Column(Text, nullable=False)
    s3ObjectKey = Column(JSON, nullable=True)
    status = Column(Enum(Status_ContactUsQuery), default=Status_ContactUsQuery.OPEN, nullable=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class BugTracking(Base):
    __tablename__ = 'bug_tracking'
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"),nullable=True)
    BugID = Column(Integer, primary_key=True, autoincrement=True)
    BugTime = Column(DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    Browser = Column(String(255), nullable=True)
    OperatingSystem = Column(String(255), nullable=True)
    BugTrackingID = Column(String(255), nullable=True)
    AppVersion = Column(String(255), nullable=True)
    Description = Column(Text, nullable=False)
    CreatedDateTime = Column(DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    BugS3ObjectKey = Column(JSON, nullable=True)

    
class UserLogs(Base):
    __tablename__ = 'user_log_data'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    LogType = Column(Enum(LogTypeEnum), default=LogTypeEnum.INFO)
    LogMessage = Column(Text, nullable=True)     # Description
    Section = Column(Enum(UserLogSectionEnum), nullable=False)
    LogDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


# *************** Tally Related Schemas *************** #

# Table for storing Tally Templates with their dummy data
class TallyTemplate(Base):

    __tablename__ = 'tally_template'
    Id = Column(Integer, primary_key=True)
    TemplateKey = Column(Enum("1", "2", "3", "4", "5", "7", "8",
                        "10", "11", "12", "13", "15", "16", "18"), nullable=False, unique=True)
    Name = Column(String(255), unique=True, nullable=False)
    Header = Column(JSON, nullable=False)        # Tally Template Dummy Header Data
    Body = Column(JSON, nullable=False)          # Tally Template Dummy Body Data
    CreatedDateTime =Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
        pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class TallyDocRecords(Base):
    __tablename__ = 'tally_doc_records'
    Id = Column(Integer, primary_key=True)
    DocID = Column(Integer, ForeignKey('uploaded_docs.DocId', ondelete="CASCADE"), nullable=True)
    Userid = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    InvoiceNo = Column(String(200), nullable=True)
    InvoiceDate = Column(String(200), nullable=True)
    TallyAPIReq = Column(JSON, nullable=False)
    TallyStatus = Column(Enum(TallyStatusEnum))  # Client Tally Status from Version 1.19, 2025-03-05
    AVTallyXMLStatus = Column(Enum(TallyStatusEnum))  # AccuVelocity Tally Status
    ReqDateTime = Column(DateTime(timezone=True))
    TallyAPIResp = Column(JSON, nullable=False)
    DocErrorMsg = Column(String(2000), nullable=True)
    RespDateTime = Column(DateTime(timezone=True), index=True)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                            default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    AVComments = Column(LONGTEXT, nullable=False)  # While Tally Processing, AV Comments
    REQID = Column(String(255), nullable=False)  # REQ_CSCUSTOMERNAME_TSHHMMSS
    VoucherType = Column(Enum(VoucherType), nullable=True)
    
class TallyModelConfig(Base):
    
    __tablename__ = 'tally_model_config'
    Id = Column(Integer, primary_key=True)
    TallyTemplateID = Column(Integer, ForeignKey('tally_template.Id', ondelete="CASCADE"), nullable=False)
    CompanyName = Column(String(255), nullable=False)    
    ModelId = Column(Integer, ForeignKey('modeltable.Id', ondelete="CASCADE"))
    GSTIN = Column(String(255), nullable=True)    
    Mappings = Column(JSON, nullable=False)
    ExtraFields = Column(JSON, nullable=True)          # to store the tally field that user want to add  
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                                default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
        pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class TallyUserConfig(Base):
    __tablename__ = 'tally_user_config'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    AuthKey = Column(String(255), nullable=False)
    TallyEnable = Column(Boolean, default=False, nullable=True)
    TallyHeaderObj = Column(JSON, nullable=False)
    TallyLedgerConfig = Column(JSON, nullable=True)
    TallyStockItemConfig = Column(JSON, nullable=True)
    TotalPagesProcessed = Column(Integer,default=0, nullable=True)
    TotalTimeSavedInMinutes = Column(Double,default=0,  nullable=True)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                             default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
                             pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    
# To get the details that which integration is activated by user
class IntegrationConfig(Base):
    
    __tablename__ = 'integration_config'
    Id = Column(Integer, primary_key=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    Tally = Column(Boolean, default=False)
    CreatedDateTime = Column(DateTime(timezone=True), index=True,
                                default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(
                                pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

# Define the table schema
class GPTBatchAPIRecords(Base):
    __tablename__ = 'GPTBatchAPIRecords'
    
    id = Column(Integer, primary_key=True, index=True)
    userid = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    Batch_Object = Column(JSON, nullable=False)
    Batch_Polling_Object = Column(JSON, nullable=True)
    TaskDetails = Column(JSON, nullable=False)
    Status = Column(Enum(GPTBatchAPIStatusEnum), nullable=False, default=GPTBatchAPIStatusEnum.in_progress)
    CreatedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    ModifiedDateTime = Column(DateTime(timezone=True), index=True, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class EmailProcessingRecord(Base):
    __tablename__ = 'email_processing_records'
    
    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(String(255), nullable=False)  # Unique identifier for the email (e.g., UID or Message-ID)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    sender = Column(String(255), nullable=False)
    received_date = Column(DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    attachments = Column(JSON, nullable=True)  # JSON field to track processed attachment names
    status = Column(Enum(EmailProcessingStatusEnum))
    retry_count = Column(Integer, default=0, nullable=False)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class TallyBackupRecord(Base):
    __tablename__ = 'tally_backup_records'

    id = Column(Integer, primary_key=True, autoincrement=True)
    email_id = Column(String(255), nullable=False)  # Unique identifier for the email (e.g., UID or Message-ID)
    user_id = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    sender = Column(String(255), nullable=False)
    received_date = Column(DateTime, default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    logFileContent = Column(LONGTEXT, nullable=True)  # JSON field to track processed attachment names
    status = Column(Enum(TallyBackupStatusEnum))
    logMessage = Column(LONGTEXT)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


class Logs_EmailProcessing(Base):
    __tablename__ = 'logs_emailprocessing'
    
    Id = Column(Integer, primary_key=True, autoincrement=True)
    UserId = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
    LogType = Column(Enum(LogTypeEnum), default=LogTypeEnum.INFO)
    LogMessage = Column(LONGTEXT, nullable=False)
    CreatedAt = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))


    
    
class TallyExports(Base):
    __tablename__ = "tally_exports"

    id = Column(Integer, primary_key=True, autoincrement=True)
    user_id = Column(Integer, ForeignKey("users.uid", ondelete="CASCADE"), nullable=False)
    files = Column(JSON, nullable=True)
    message = Column(LONGTEXT, nullable=True)
    created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class MultipleVendorRecord(Base):
    __tablename__ = 'multiple_vendor_record'
    
    MVR_ID = Column(Integer, primary_key=True, autoincrement=True)
    Userid = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"))
    CREQ_ID = Column(String, nullable=False)
    ObjAWSResponse = Column(JSON, nullable=True)
    GPTUserContent = Column(JSON, nullable=True)
    ObjGPTResponse = Column(JSON, nullable=True)
    dictGPTResponse = Column(JSON, nullable=True)
    LogMessage = Column(String, nullable=True)
    HashCode = Column(String, nullable=False)
    AWSStatusCode = Column(Integer, default=404)
    GPTStatusCode = Column(Integer, default=404)
    CreatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))

class AVPOProcessingDetails(Base):
    __tablename__ = 'po_processing_details'

    ID = Column(Integer, primary_key=True, autoincrement=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    RequestID = Column(Integer, ForeignKey('AVRequestDetail.ID', ondelete="CASCADE"), nullable=True)
    PONumber = Column(String(255), nullable=False)
    PODetails = Column(JSON, nullable=True)
    AVXMLStatus = Column(Enum(TallyStatusEnum), nullable=False, default=TallyStatusEnum.Skipped)
    GeneratedXML = Column(LONGTEXT, nullable=True, comment="Stores the generated XML content")
    XML_IMPORT_STATUS_CODE = Column(String(50), nullable=True, default=999, comment="Whether XML generation/import succeeded -> 200 or failed -> 999")
    XML_IMPORT_TYPE = Column(Enum(XMLImportTypeEnum),nullable=False,default=XMLImportTypeEnum.EXE_Initiated_Request, comment="How this record was imported (Auto vs Manual)")
    XMLImportDateTime = Column(DateTime(timezone=True), nullable=True, comment="Timestamp for when XML import occurred")
    ErrorMessage = Column(LONGTEXT, nullable=True)
    CreatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    IsDeleted = Column(Boolean, default=False)
    DeletedDateTime = Column(DateTime(timezone=True), nullable=True)
    request_detail = relationship("AVRequestDetail", backref="po_processing_details")
    

class AVGRNProcessingDetails(Base):
    __tablename__ = 'grn_processing_details'

    ID = Column(Integer, primary_key=True, autoincrement=True)
    UserID = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=False)
    RequestID = Column(Integer, ForeignKey('AVRequestDetail.ID', ondelete="CASCADE"), nullable=True)
    GRNNumber = Column(String(255), nullable=False)
    GRNDetails = Column(JSON, nullable=True)
    AVXMLStatus = Column(Enum(TallyStatusEnum), nullable=False, default=TallyStatusEnum.Skipped)
    GeneratedXML = Column(LONGTEXT, nullable=True, comment="Stores the generated XML content")
    XML_IMPORT_STATUS_CODE = Column(String(50), nullable=True, default=999, comment="Whether XML generation/import succeeded -> 200 or failed -> 999")
    XML_IMPORT_TYPE = Column(Enum(XMLImportTypeEnum), nullable=True, default=XMLImportTypeEnum.EXE_Initiated_Request,comment="How this record was imported (Auto vs Manual)")
    XMLImportDateTime = Column(DateTime(timezone=True), nullable=True, comment="Timestamp for when XML import occurred")
    ErrorMessage = Column(Text, nullable=True)
    CreatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    UpdatedDateTime = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
    IsDeleted = Column(Boolean, default=False)
    DeletedDateTime = Column(DateTime(timezone=True), nullable=True)

    request_detail = relationship("AVRequestDetail", backref="grn_processing_details")



# DISCARDED TABLES

# Version 'origin/AV_bVersion1.29' Discarded Table List: [TDLProcessingRecords]

# class TDLProcessingRecords(Base):
#     __tablename__ = 'tdl_processing_records'

#     Id = Column(Integer, primary_key=True, autoincrement=True)
#     UserId = Column(Integer, ForeignKey('users.uid', ondelete="CASCADE"), nullable=True)
#     attachments = Column(JSON, nullable=True)  # JSON field to track processed attachment names
#     status = Column(Enum(EmailProcessingStatusEnum))
#     LogMessage = Column(LONGTEXT, nullable=False)
#     retry_count = Column(Integer, default=0, nullable=False)
#     created_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
#     updated_at = Column(DateTime(timezone=True), default=lambda: datetime.now(pytz.timezone('Asia/Kolkata')), onupdate=lambda: datetime.now(pytz.timezone('Asia/Kolkata')))
#     REQID = Column(String(255), nullable=False)  # REQ_CUSTOMERNAME_TSHHMMSS
#     ResponseAt = Column(DateTime(), nullable=True) # Request REsponse At 