from fastapi import HTTPException
import pytz
import traceback
import json
from src.Controllers.Logs_Controller import CLogController
from config.db_config import AsyncSessionLocal
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException
from config.constants import Constants
from src.Controllers.Vendor_Controller import CVendorController
from src.Controllers.prompt_controller import CPromptController
from src.Controllers.StripePaymentController import CPaymentApp
from src.Models.models import DocExtractedData, ModelTable, UploadedDoc, User, Role, UserAPIUsage
from sqlalchemy import select, update, and_

from sqlalchemy.exc import NoResultFound
class CUserTable:
    
    @staticmethod
    async def MSGetAllUsersWithoutFilters(iUserID):
        """
        Retrieves a list of all users without pagination.
        """
        try:
            await CLogController.MSWriteLog(iUserID, "Info", "Fetching all users started.")
            async with AsyncSessionLocal() as session:
                # Execute query to fetch all users
                result = await session.execute(
                    select(User, Role, UserAPIUsage)
                    .join(Role, User.roleID == Role.Id)
                    .join(UserAPIUsage, User.uid == UserAPIUsage.user_id)
                )

                lsUsersNRolesNApiUsage = result.all()

                await CLogController.MSWriteLog(iUserID, "Info", "Successfully fetched all users.")

                # Convert user data and profile picture to Base64 (if exists)
                users_data = [
                    {
                        "UserId": objUser.uid,
                        "email": objUser.email,
                        "name": objUser.name,
                        "role": objRole.RoleName,
                        "phoneNumber": objUser.phoneNumber,
                        "Country": objUser.Country,
                        "usePaidOCR": "Yes" if objUser.usePaidOCR else "No",
                        "usePaidDocExtractor": "Yes" if objUser.usePaidDocExtractor else "No",
                        "used_tokens": objAPIUsage.used_tokens,
                        "api_requested": objAPIUsage.api_requested,
                        "page_limit_left": objAPIUsage.page_limit_left
                    } for objUser, objRole, objAPIUsage in lsUsersNRolesNApiUsage
                ]

                return users_data
        except Exception as e:
            await CLogController.MSWriteLog(iUserID, "Error", "Failed to retrieve all users.")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Failed to retrieve all user data. Traceback: {str(traceback.format_exc())}.")
            raise HTTPException(status_code=500, detail=f"An error occurred while retrieving all users: {str(e)}")
    @staticmethod
    async def MSGetUser(user_id):
        """
        Creates a user object from the given user ID, ensuring necessary details are present.
        
        Parameters:
            user_id (int): The ID of the user.
            auth_controller (object): The authentication controller object providing user data.
            
        Returns:
            dict: A dictionary containing the user's configuration data.
            
        Raises:
            HTTPException: If necessary user details are missing.
        """
        try:
            from src.Controllers.auth_controller import CAuthController
            # Get user configuration data
            user_data = await CAuthController.MSGetSingleUser(user_id=user_id)

            # Check for necessary details
            required_keys = ["usePaidDocExtractor", "usePaidOCR", "page_limit_left","isTrialPaidDocExtraction"]
            
            if not all(key in user_data for key in required_keys):
                raise HTTPException(status_code=500, detail="Missing necessary user details.")
            
            return user_data

        except HTTPException as e:
            # Propagate HTTPException for necessary user details
            raise HTTPException(status_code=e.status_code, detail=e.detail)

        except Exception as e:
            # Catch any other exceptions that may arise
            raise HTTPException(status_code=500, detail=str(e))
    

class CModelTable:
    
    @staticmethod
    async def MSIsModelExistsBaseOnUserId(modelId: int, userId: int) -> bool:
        """
        Check if a model with the given modelId and userId exists in the database.

        Parameters:
            modelId (int): The ID of the model to check.
            userId (int): The ID of the user to whom the model should belong.

        Returns:
            bool: True if the model exists, otherwise False.
        """
        async with AsyncSessionLocal() as db:
            # Execute the query to find the model
            result = await db.execute(select(ModelTable).filter(ModelTable.Id == modelId, ModelTable.UserID == userId))
            model = result.scalars().first()

            # Return True if the model exists, False otherwise
            return model is not None
    
    @staticmethod
    async def MSIsModelExistsBaseOnModelId(modelId: int) -> bool:
        """
        Check if a model with the given modelId exists in the database.

        Parameters:
            modelId (int): The ID of the model to check.

        Returns:
            bool: True if the model exists, otherwise False.
        """
        async with AsyncSessionLocal() as db:
            # Execute the query to find the model
            result = await db.execute(select(ModelTable).filter(ModelTable.Id == modelId))
            model = result.scalars().first()

            # Return True if the model exists, False otherwise
            return model is not None
    
    @staticmethod
    async def MSUpdateModelPreDefinedColumn(model_id: int, user_id: int , updated_dict: dict):
        async with AsyncSessionLocal() as db:
            try:
                # Fetch the existing model entry
                query = select(ModelTable).where(ModelTable.Id == model_id, ModelTable.UserID == user_id)
                result = await db.execute(query)
                model = result.scalars().first()
                
                if not model:
                    raise HTTPException(status_code=404, detail="Model not found")

                # Update the preDefinedModelDict with new data
                current_dict = model.preDefinedModelDict
                # current_dict.update(updated_dict) use replacement of dict instead of update
                model.preDefinedModelDict = json.dumps(updated_dict, ensure_ascii=False) if isinstance(updated_dict, dict) else updated_dict 
                
                db.add(model)
                await db.commit()
                
                return {
                    "message": "Model updated successfully",
                    "updated_model": model.preDefinedModelDict
                }
            except SQLAlchemyError as e:
                await db.rollback()
                raise HTTPException(status_code=500, detail=f"Database error: {str(e)}")
            finally:
                await db.close()

class CPromptTable:
    
    @staticmethod
    async def MSGetPromptDataBaseOnDocID(iUserID: int, docID: int, isPaidDocExtraction : bool) -> dict:
        """
        Retrieves prompt data based on a document ID and model name for a specific user.

        This method first retrieves document metadata based on the document ID. Then, it fetches
        model and prompt details specific to the document, taking into account whether the document
        is a scanned one.

        Args:
            iUserID (int): The ID of the user who is requesting the prompt data.
            docID (int): The ID of the document for which prompt data is being requested.

        Returns:
            dict: A dictionary containing the model and prompt details for the document.

        Raises:
            HTTPException: If the retrieval process encounters any issues.
        """
        # Establish a database session
        async with AsyncSessionLocal() as db:
            try:
                # Retrieve document metadata based on the document ID
                objUploadDocData = await CDocumentTable.MSGetDocumentData(iUserID, docID, isBinaryDataRequired=False)
                file_type = objUploadDocData.get("file_type")
                isScannedDocument = bool(objUploadDocData.get("is_scanned_document"))
                iDocModelId = objUploadDocData.get("ModelId")
                dictModelData = await CVendorController.MSGetModelByID(iUserID = iUserID, modelId = iDocModelId)
                strModelName = dictModelData.get("ModelName")
                FamilyName = dictModelData.get("FamilyName")
                
                if isPaidDocExtraction:
                    strAPIModelName = "GPT"
                else:
                    # not Paid Extraction
                    strAPIModelName = "Gemini"
                strAPIDocExtractionType = Constants.GPTAPISuffix if  strAPIModelName == Constants.GPTAPIModelName else Constants.GeminiAPISuffix

                ModelSeries = f"{FamilyName}_{strModelName}_{strAPIModelName}_{strAPIDocExtractionType}"
                # Fetch Prompt Data
                dictDocPromptData = await CPromptController.get_prompt_by_model_series(iUserID=iUserID, ModelId=iDocModelId, ModelSeries=ModelSeries)
            
                # Log the successful retrieval of prompt data
                await CLogController.MSWriteLog(iUserID, "Info", f"Prompt data retrieved based on document ID {docID}.")

                # Return the model and prompt details for the document
                return {"UserId":iUserID,"DocId":docID,"ModelTable":dictModelData, "PromptTable":dictDocPromptData, "ExtractionAPIModel":strAPIModelName, "isReasoningEnabled": True if  strModelName.lower() in [ "rollin logistics","somani brothers", "agarwal suppliers", "shri ram enterprises", "the ahmedabad coop dept stories ltd", "shree foods","the vedansh international school","nakoda trading","shree dutt krupa lamination","shivambica sales corporation","sachi products","govind tours and travels","i i traders","11 traders","sovereign sales","dharm sales company","r k trading company","r k plast (india)","uma converter ltd","radhe agency",  "amar traders","mahavir international","gas guys","zeel pest solution llp","shri arihant sales agency","diamond private security investigation services","savnath enterprise llp","unicorn enterprise","gurukrupa traders","grains and more","shivay enterprise","yash tradelink","palladium","satyam steel house","south asia fm ltd","guru kripa petroleum", "trophy wala", "r k enterprises", "sanghi brothers", "mahavir kirana stores", "ronak rudra security & management services", "ghanshyam bahiniya", "inifinty cars pvt ltd", "mendwell agencies", "kiron electricals", "bharat sanchar nigam limited", "airtel","karnavati","regenta m foods", "bhagwati trading co","a.a.khambati & sons","narayan marketing","v.s.agencies","ashkelon enterprises","mangal hardware and paints", "anl singapore pte. ltd. c/o ccai","anl singapore pte. ltd","united liner shipping services llp",
                            "gulab hardware stores",
                            "mohit enterprises",
                            "sainath engineering services",
                            "cd multi media gallery",] else False}  #TODO: Reasoning Model please add vendorName
            
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt data for document ID {docID}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=e.status_code, detail=e.detail)

            except SQLAlchemyError as e:
                # Rollback in case of database transaction failure
                await db.rollback()
                # Log the error and traceback
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt data for document ID {docID}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                # Raise an HTTPException indicating the server-side error
                raise HTTPException(status_code=500, detail="An error occurred while retrieving prompt data.")
    
    @staticmethod
    async def MSGetPromptData(iUserID: int, objUploadDocData, isPaidDocExtraction : bool) -> dict:
        """
        Retrieves prompt data based on a document ID and model name for a specific user.

        This method first retrieves document metadata based on the document ID. Then, it fetches
        model and prompt details specific to the document, taking into account whether the document
        is a scanned one.

        Args:
            iUserID (int): The ID of the user who is requesting the prompt data.
            objUploadDocData (object): upload doc object

        Returns:
            dict: A dictionary containing the model and prompt details for the document.

        Raises:
            HTTPException: If the retrieval process encounters any issues.
        """
        docID = None
        # Establish a database session
        async with AsyncSessionLocal() as db:
            try:
                docID = objUploadDocData.get("DocId")
                file_type = objUploadDocData.get("file_type")
                isScannedDocument = bool(objUploadDocData.get("is_scanned_document"))
                iDocModelId = objUploadDocData.get("ModelId")
                dictModelData = await CVendorController.MSGetModelByID(iUserID = iUserID, modelId = iDocModelId)
                strModelName = dictModelData.get("ModelName")
                FamilyName = dictModelData.get("FamilyName")
                
                # Retrieve prompt details specific to the document
                #  Select Gemini when Paid Extraction False and found Scanned Document
                if isScannedDocument and not isPaidDocExtraction:
                    # WARN : change when intergate GPT API strickly use Gemini if OCR
                    strAPIModelName = "Gemini"
                elif isPaidDocExtraction:
                    strAPIModelName = "GPT"
                else:
                    # not scanned Document && not Paid Extraction
                    strAPIModelName = "Gemini"
                strAPIDocExtractionType = Constants.GPTAPISuffix if  strAPIModelName == Constants.GPTAPIModelName else Constants.GeminiAPISuffix

                ModelSeries = f"{FamilyName}_{strModelName}_{strAPIModelName}_{strAPIDocExtractionType}"
                # Fetch Prompt Data
                dictDocPromptData = await CPromptController.get_prompt_by_model_series(iUserID=iUserID, ModelId=iDocModelId, ModelSeries=ModelSeries)
                

                # Log the successful retrieval of prompt data
                await CLogController.MSWriteLog(iUserID, "Info", f"Prompt data retrieved based on document ID {docID}.")

                # Return the model and prompt details for the document
                return {"UserId":iUserID,"DocId":docID,"ModelTable":dictModelData, "PromptTable":dictDocPromptData, "ExtractionAPIModel":strAPIModelName}
            
            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt data for document ID {docID}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=e.status_code, detail=e.detail)

            except SQLAlchemyError as e:
                # Rollback in case of database transaction failure
                await db.rollback()
                # Log the error and traceback
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve prompt data for document ID {docID}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                # Raise an HTTPException indicating the server-side error
                raise HTTPException(status_code=500, detail="An error occurred while retrieving prompt data.")

class CDocumentTable:
    
    @staticmethod
    async def MSIsDocExistsBaseOnUserId(docId: int, userId: int) -> bool:
        """
        Check if a document with the given docId and userId exists in the database.

        Parameters:
            docId (int): The ID of the document to check.
            userId (int): The ID of the user to whom the document should belong.

        Returns:
            bool: True if the document exists, otherwise False.
        """
        async with AsyncSessionLocal() as db:
            # Execute the query to find the document
            result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == docId, UploadedDoc.UserId == userId))
            document = result.scalars().first()

            # Return True if the document exists, False otherwise
            return document is not None
    
    @staticmethod
    async def MSIsDocExistsBaseOnDocId(docId: int) -> bool:
        """
        Check if a document with the given docId and userId exists in the database.

        Parameters:
            docId (int): The ID of the document to check.

        Returns:
            bool: True if the document exists, otherwise False.
        """
        async with AsyncSessionLocal() as db:
            # Execute the query to find the document
            result = await db.execute(select(UploadedDoc).filter(UploadedDoc.DocId == docId))
            document = result.scalars().first()

            # Return True if the document exists, False otherwise
            return document is not None
    @staticmethod
    async def MSGetDocumentData(user_id, doc_id, isBinaryDataRequired=False):
        """
        Retrieves document metadata based on the provided document ID.
        
        Parameters:
            user_id (int): The ID of the user.
            doc_id (str): The ID of the document.
            isBinaryDataRequired (bool): True if the want to fetch binary data of document.
            document_data_controller (object): The controller object for document data retrieval.
            
        Returns:
            dict: A dictionary containing the document metadata.
            
        Raises:
            HTTPException: If no document is found or other errors occur.
        """
        try:
            from src.Controllers.DocumentData_controller import CDocumentData
            # Retrieve document metadata
            doc_metadata = await CDocumentData.MSGetDocById(user_id=user_id, docId=doc_id, isBinaryDataRequired = isBinaryDataRequired)

            if doc_metadata is None:
                raise HTTPException(status_code=404, detail=f"No document found with ID {doc_id}.")

            return doc_metadata

        except HTTPException as e:
            # Propagate HTTPException for missing document case
            raise HTTPException(status_code=e.status_code, detail=e.detail)

        except Exception as e:
            # Catch any other exceptions that may arise
            raise HTTPException(status_code=500, detail=str(e))

    @staticmethod
    async def MSUpdateModelForDocID(user_id:int, doc_id:int, model_id:int, strModelName:str):
        """
        Retrieves model ID based on user ID and model name, and updates ModelId in the UploadedDoc table.

        Parameters:
            user_id (int): The ID of the user.
            doc_id (int): The ID of the document.
            model_name (str): The name of the model.

        Returns:
            bool: True if update successful, False otherwise.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Execute the update query
                await db.execute(
                    update(UploadedDoc)
                    .where(and_(UploadedDoc.DocId == doc_id))
                    .values(ModelId=model_id)
                )

                # Commit the transaction
                await db.commit()
                await CLogController.MSWriteLog(user_id, "Info", f"Great news! We've successfully updated the model name for your document to {strModelName}.")
                return True

            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Oops! We couldn't update the model for your document right now. Please try again later {str(e)}")
                raise HTTPException(status_code=500, detail="Oops! We couldn't update the model for your document right now. Please try again later")
                return False

class CExtractionTable:
    
    @staticmethod
    async def MSGetLatestExtractedData(user_id: int,
                                      doc_id: int , bRaiseError:bool = True):
        async with AsyncSessionLocal() as db:
            try:
                # Prepare and execute the query
                query = select(DocExtractedData).filter(DocExtractedData.DocId == doc_id).order_by(DocExtractedData.ModifiedDateTime.desc())
                result = await db.execute(query)
                document = result.scalars().first()
                
                if document is None:
                    await CLogController.MSWriteLog(user_id, "Error", f"Failed to retrieve prompt data for document ID {doc_id}.")
                    if bRaiseError:
                        raise HTTPException(status_code=404, detail="Document not found")
                    else:
                        return None
                # Deserialize Json Verified and Approved Extracted Document Data
                jsonDeserializerVerifiedData = json.loads(document.DocVerifiedData) if isinstance(document.DocVerifiedData, str) else document.DocVerifiedData
                jsonDeserializerApprovedData = json.loads(document.DocApprovedData) if isinstance(document.DocApprovedData, str) else document.DocApprovedData

                # Return the necessary data
                return {
                    "DocId": doc_id,
                    "ModifiedDateTime": document.ModifiedDateTime,
                    "DocExtractionPromptID": document.DocExtractionPromptID,
                    "DocVerifiedData": jsonDeserializerVerifiedData,
                    "DocApprovedData": jsonDeserializerApprovedData
                }
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Oops! We hit a snag while trying to retrieve the GPTJson documents. Please try again later {str(e)}")
                raise HTTPException(status_code=500, detail="Oops! We hit a snag while trying to retrieve the GPTJson documents. Please try again later")
            finally:
                await db.close()

class CExtractedValidationTable:
    
    @staticmethod
    async def MSDeleteExtractValidatedData(doc_id: int, prompt_id: int, user_id: int) -> dict:
        """
        Delete all document rows matching the given doc_id and prompt_id.

        Args:
            doc_id (int): The ID of the document to delete.
            prompt_id (int): The ID of the extraction prompt associated with the document.
            user_id (int): The ID of the user making the request.

        Returns:
            dict: A response message indicating the outcome of the operation.
        """
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(user_id, "Info", f"Deletion for Document with DocID {doc_id} and PromptID {prompt_id} initiated.")

                # Fetch all matching rows to ensure they exist
                result = await db.execute(
                    select(DocExtractedData)
                    .filter(DocExtractedData.DocId == doc_id, DocExtractedData.DocExtractionPromptID == prompt_id)
                )

                document = result.scalars().first()

                if not document:
                    await CLogController.MSWriteLog(user_id, "Info", f"Document with DocId {doc_id} and PromptID {prompt_id} not found.")
                    return {"message": f"No rows matching DocID {doc_id} and PromptID {prompt_id} have been Found."}
                
                # ! Update Below Logic To Remove Validated Data
                document.DocVerifiedData = None
                
                await db.commit()

                await CLogController.MSWriteLog(user_id, "Info", f"Successfully deleted all matching Documents with DocID {doc_id} and PromptID {prompt_id}.")

                return {"message": f"All rows matching DocID {doc_id} and PromptID {prompt_id} have been successfully deleted."}

            except NoResultFound:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Document with DocID {doc_id} and PromptID {prompt_id} not found.")
                raise HTTPException(status_code=404, detail=f"Document with DocId {doc_id} and PromptID {prompt_id} not found.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(user_id, "Error", f"Error deleting Documents with DocID {doc_id} and PromptID {prompt_id}. {str(e)}")
                raise HTTPException(status_code=500, detail="Internal Server Error")

class CPaymentHelper:
    
    @staticmethod
    async def MSInitializeAppPlans(payment_type:str="recurring", liveMode:bool=False):
        """
        purpose :- Initialize the application plans from the PaymentConfig.json file
        Input:- 1. strPlansConfig, list of application plans
                2. payment_type, enum - recurring, one_time
        Output:- None
        Example:- await CPaymentApp.MSInitializeAppPlans()
        """
        try:
            if liveMode:
                strPlansConfig = Constants.ProdPaymentConfig
            else:
                strPlansConfig = Constants.DevPaymentConfig
            with open(strPlansConfig, 'r') as file:
                config = json.load(file)

            plans = config.get("plans", {})
            topUp = config.get("top_up",{})
            for plan_type, plan_list in plans.items():
                for plan_data in plan_list:
                    for plan_name, details in plan_data.items():
                        # Extract the plan details
                        stripe_price_id = details.get("PriceID")
                        stripe_product_id = details.get("ProductID")
                        plan_price = details.get("amount")
                        plan_currency = details.get("currency")
                        description = details.get("description")
                        pageCount = details.get("pageCount")
                        # Call the MSSetPlans method to add the plan
                        await CPaymentApp.MSSetPlans(
                            stripe_price_id=stripe_price_id,
                            stripe_product_id=stripe_product_id,
                            plan_name=plan_name,
                            plan_price=plan_price,
                            plan_currency=plan_currency,
                            payment_type=payment_type,
                            plan_type=plan_type,
                            description=description,
                            pageCount=pageCount
                        )
            else:
                for plan_type, plan_list in topUp.items():
                    for plan_data in plan_list:
                        for plan_name, details in plan_data.items():
                            # Extract the plan details
                            stripe_price_id = details.get("PriceID")
                            stripe_product_id = details.get("ProductID")
                            plan_price = details.get("amount")
                            plan_currency = details.get("currency")
                            description = details.get("description")
                            pageCount = details.get("pageCount")
                            # Call the MSSetPlans method to add the plan
                            await CPaymentApp.MSSetPlans(
                                stripe_price_id=stripe_price_id,
                                stripe_product_id=stripe_product_id,
                                plan_name=plan_name,
                                plan_price=plan_price,
                                plan_currency=plan_currency,
                                payment_type="one_time", # one time payment
                                plan_type="top_up",
                                description=description,
                                pageCount=pageCount
                            )

            await CLogController.MSWriteLog(None, "Info", "All plans initialized from PaymentConfig.json")
        except Exception as e:
            await CLogController.MSWriteLog(None, "Error", f"Error initializing plans: {str(e)}")
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="An error occurred while initializing the plans")