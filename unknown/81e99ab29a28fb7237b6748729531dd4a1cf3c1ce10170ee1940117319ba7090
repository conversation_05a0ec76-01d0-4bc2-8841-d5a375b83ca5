{"type": "json_schema", "json_schema": {"name": "10_MSC", "strict": true, "schema": {"type": "object", "properties": {"CompanyName": {"type": "string", "description": "The name of the company issuing or receiving the invoice"}, "CompanyAddress": {"type": "string", "description": "The address of the company issuing the invoice"}, "InvoiceDate": {"type": "string", "description": "The date the invoice was issued. Change Date Format to 'YYYYMMDD'"}, "email": {"type": "string", "description": "Email address associated with the invoice"}, "GSTNCode": {"type": "string", "description": "Goods and Services Tax Identification Number"}, "InvoiceNo": {"type": "string", "description": "Serial number of the invoice"}, "InvoiceType": {"type": "string", "description": "The type or category of the invoice"}, "ACCode": {"type": "string", "description": "Account code"}, "CustomerCode": {"type": "string", "description": "Customer-specific identification code"}, "IRN": {"type": "string", "description": "Invoice Reference Number, a unique identifier for the invoice"}, "PAN": {"type": "string", "description": "Permanent Account Number for taxation"}, "ReceiverName": {"type": "string", "description": "The name of the receiver"}, "ReceiverAddress": {"type": "string", "description": "The address of the receiver"}, "ReceiverState": {"type": "string", "description": "The state of the receiver"}, "StateCode": {"type": "string", "description": "The state code of the receiver"}, "ReceiverGSTN": {"type": "string", "description": "GSTN of the receiver"}, "ReceiverPAN": {"type": "string", "description": "PAN number of the receiver"}, "B/LNo": {"type": "string", "description": "Bill of Lading Number, a unique identifier for shipping documents"}, "ItemTable1": {"type": "array", "items": {"type": "object", "properties": {"Description": {"type": "string", "description": "Item or Service Name"}, "HSNCode": {"type": "integer", "description": "Harmonized System Nomenclature code for the item"}, "Curr": {"type": "string", "description": "Currency type"}, "ExRate": {"type": "number", "description": "Exchange rate"}, "Qty": {"type": "integer", "description": "Quantity of the item"}, "Rate": {"type": "number", "description": "Rate per unit of the item"}, "USD": {"type": "number", "description": "Total amount in USD"}, "INR": {"type": "number", "description": "Total amount in INR"}, "NonTaxableValue": {"type": "number", "description": "Total non-taxable value"}, "TaxableAmount": {"type": "number", "description": "Total taxable value"}, "SGSTRate": {"type": "number", "description": "SGST rate"}, "SGSTAmount": {"type": "number", "description": "SGST amount"}, "CGSTRate": {"type": "number", "description": "CGST rate"}, "CGSTAmount": {"type": "number", "description": "CGST amount"}, "IGSTRate": {"type": "number", "description": "IGST rate"}, "IGSTAmount": {"type": "number", "description": "IGST amount"}}, "required": ["Description", "HSNCode", "<PERSON><PERSON><PERSON>", "ExRate", "Qty", "Rate", "USD", "INR", "NonTaxableValue", "TaxableAmount", "SGSTRate", "SGSTAmount", "CGSTRate", "CGSTAmount", "IGSTRate", "IGSTAmount"], "additionalProperties": false}}, "FinalSummary": {"type": "object", "properties": {"Sum": {"type": "number", "description": "Total sum of all items"}, "TotalGST": {"type": "number", "description": "Total GST amount"}, "FinalTotalAmountINR": {"type": "number", "description": "Final invoice total amount"}, "TaxableValue": {"type": "number", "description": "Total taxable value"}}, "required": ["Sum", "TotalGST", "FinalTotalAmountINR", "TaxableValue"], "additionalProperties": false}, "LsContainerNumber/Size": {"type": "array", "description": "Contains container numbers and sizes in the format 'cont_no/size'. This list should contain unique values.", "example_values": ["FFAU3647502/40", "KKFU8071020/40"], "items": {"type": "string", "description": "Container number and size combined as 'cont_no/size'. Example: 'FFAU3647502/40'."}}}, "required": ["CompanyName", "CompanyAddress", "InvoiceDate", "email", "GSTNCode", "InvoiceNo", "InvoiceType", "ACCode", "CustomerCode", "IRN", "PAN", "ReceiverName", "Receiver<PERSON><PERSON><PERSON>", "ReceiverState", "StateCode", "ReceiverGSTN", "ReceiverPAN", "B/LNo", "ItemTable1", "FinalSummary", "LsContainerNumber/Size"], "additionalProperties": false}}}