{"type": "json_schema", "json_schema": {"name": "Tax_Invoice", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "SellerAddress": {"type": "string", "description": "Address of the seller issuing the invoice"}, "SellerCity": {"type": "string", "description": "City of the seller issuing the invoice"}, "SellerPincode": {"type": "number", "description": "Pincode of the seller issuing the invoice"}, "SellerFSSAILICNO": {"type": "string", "description": "FSSAI License Number of the seller issuing the invoice"}, "SellerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the seller issuing the invoice"}, "SellerStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the seller issuing the invoice"}, "SellerStateCode": {"type": "number", "description": "State code of the seller issuing the invoice"}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerCity": {"type": "string", "description": "City of the buyer"}, "BuyerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the buyer"}, "BuyerPAN/IT": {"type": "string", "description": "PAN/IT number of the buyer"}, "BuyerStateName": {"type": "string", "description": "State Name of the buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "BuyerStateCode": {"type": "number", "description": "State code of the buyer"}, "InvoiceNo": {"type": "integer", "description": "Unique invoice number"}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "DeliveryNote": {"type": "string", "description": "Note about the delivery "}, "ReferenceNo & Date": {"type": "string", "description": "Reference number and date of delivery "}, "OtherReference": {"type": "string", "description": "Other reference about delivery "}, "BuyerOrderNO": {"type": "string", "description": "Buyer order number "}, "DispatchDocNO": {"type": "string", "description": "Dispatch document number "}, "DeliveryNoteDate": {"type": "integer", "description": "Date of the delivery note in ddmmyy format "}, "DispatchedThrough": {"type": "string", "description": "Name of the carrier or service used for dispatch "}, "Destination": {"type": "string", "description": "Destination address "}, "BillOfLanding/LR-RR No": {"type": "string", "description": "Bill of lading number it is in format in dt. 31-jan-25 likes"}, "MotorVehicalNO No": {"type": "string", "description": "Motor vehicle number it is like GJ 01 LT 4388"}, "OutputCGSTRate@2.5": {"type": "number", "description": "Central Goods and Services Tax (CGST) rate(s) applied to the invoice"}, "OutputCGSTAmount@2.5": {"type": "number", "description": "The total amount of CGST calculated based on the CGST rate(s)"}, "OutputSGSTRate@2.5": {"type": "number", "description": "State Goods and Services Tax (SGST) rate(s) applied to the invoice."}, "OutputSGSTAmount@2.5": {"type": "number", "description": "The total amount of SGST calculated based on the SGST rate(s)."}, "OutputCGSTRate@6.0": {"type": "number", "description": "Central Goods and Services Tax (CGST) rate(s) applied to the invoice"}, "OutputCGSTAmount@6.0": {"type": "number", "description": "The total amount of CGST calculated based on the CGST rate(s)"}, "OutputSGSTRate@6.0": {"type": "number", "description": "State Goods and Services Tax (SGST) rate(s) applied to the invoice."}, "OutputSGSTAmount@6.0": {"type": "number", "description": "The total amount of SGST calculated based on the SGST rate(s)."}, "RoundingOFF": {"type": "number", "description": "Rounding off for the invoice"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Unique serial number for each item. This should be a sequential integer (e.g., 1, 2, 3)."}, "DescriptionOfGoods": {"type": "string", "description": "Detailed description of the item or goods. The description should be comprehensive and clear (e.g., 'LOOSE SADA MAWA (MADE FROM BUFFALO MILK)'. Avoid abbreviations or truncations."}, "HSN/SAC": {"type": "string", "description": "The HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code) code for the item, which is used for tax purposes."}, "Quantity": {"type": "number", "description": "Quantity of the item don't add word like Ltr,Kg.,etc.."}, "Rate": {"type": "number", "description": "Rate per unit of the item"}, "Per": {"type": "string", "description": "Unit of measure (e.g., per kg, per piece)"}, "Amount": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "DescriptionOfGoods", "HSN/SAC", "Quantity", "Rate", "Per", "Amount"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice. The required fields must be provided, and no additional fields are allowed."}}, "TotalInvoiceAmount": {"type": "number", "description": "Total amount for the invoice"}, "TotalChargeable(In Words)": {"type": "string", "description": "Total Chargeable values in word. "}, "TotalTaxableValue": {"type": "number", "description": "Taxable value Consider total of taxable alues from this table"}, "TotalCGST": {"type": "number", "description": "Total CGST  consider from this table's total cgst"}, "TotalSGST/UTGST": {"type": "number", "description": "Total SGST or UTGST applied to the invoice"}, "TotalTaxAmount": {"type": "number", "description": "Total tax amount after applying all taxes"}, "TotalTaxAmount(INWORD)": {"type": "string", "description": "Total tax amount in words"}, "Table1": {"type": "array", "items": {"type": "object", "properties": {"HSN/SAC": {"type": "string", "description": "The HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code) code for the item, which is used for tax purposes."}, "TaxableValue": {"type": "number", "description": "Taxable value Consider  from this table"}, "CGSTRate": {"type": "number", "description": " CGSTRate  consider from this table's total cgst"}, "CGSTAmount": {"type": "number", "description": " CGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTRate": {"type": "number", "description": " SGST/UTGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTAmount": {"type": "number", "description": " SGS/UTGSTAmount  consider from this table's total cgst"}, "TotalTaxAmount": {"type": "number", "description": "Final     for the item after discounts and taxes"}}, "required": ["HSN/SAC", "TaxableValue", "CGSTRate", "CGSTAmount", "SGST/UTGSTRate", "SGST/UTGSTAmount", "TotalTaxAmount"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice. The required fields must be provided, and no additional fields are allowed."}}, "SellerPanNO": {"type": "string", "description": "<PERSON><PERSON>'s PAN card number"}, "SellerBankName": {"type": "string", "description": "<PERSON><PERSON>'s bank name"}, "SellerA/c No": {"type": "string", "description": "<PERSON><PERSON>'s bank account number"}, "SellerBankBranch": {"type": "string", "description": "<PERSON><PERSON>'s bank branch name"}, "SellerBankIFSCode": {"type": "string", "description": "<PERSON><PERSON>'s bank IFSC code"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerPincode", "SellerFSSAILICNO", "SellerGSTIN/UIN", "SellerStateName", "SellerStateCode", "BuyerName", "BuyerAddress", "BuyerCity", "BuyerGSTIN/UIN", "BuyerPAN/IT", "BuyerStateName", "BuyerStateCode", "InvoiceNo", "InvoiceDate", "DeliveryNote", "ReferenceNo & Date", "OtherReference", "BuyerOrderNO", "DispatchDocNO", "DeliveryNoteDate", "DispatchedThrough", "Destination", "BillOfLanding/LR-RR No", "MotorVehicalNO No", "OutputCGSTRate@2.5", "OutputCGSTAmount@2.5", "OutputSGSTRate@2.5", "OutputSGSTAmount@2.5", "OutputCGSTRate@6.0", "OutputCGSTAmount@6.0", "OutputSGSTRate@6.0", "OutputSGSTAmount@6.0", "RoundingOFF", "Table", "TotalInvoiceAmount", "TotalChargeable(In Words)", "TotalTaxableValue", "TotalCGST", "TotalSGST/UTGST", "TotalTaxAmount", "TotalTaxAmount(INWORD)", "Table1", "SellerPanNO", "SellerBankName", "SellerA/c No", "SellerBankBranch", "SellerBankIFSCode"], "additionalProperties": false}}}