import sys
sys.path.append("")
import traceback
import os
import pytz
from fastapi import HTTPException
import asyncio
import json
from pydantic import BaseModel, Field, conlist
from typing import List, Optional
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
from datetime import datetime
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, PartyDetailsSchema, ConsigneeDetailsSchema, LedgerEntrySchema, TallyPurchaseVoucherSchema
from src.Schemas.TallyJournalVoucherXMLSchema import CTallyJournalVoucherTemplate, TallyJournalVoucherInputSchema, CompanyInfoSchema, LedgerEntrySchema
from src.utilities.helperFunc import DateHelper, CFileHandler
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from datetime import datetime, timedelta
from dateutil.relativedelta import relativedelta
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.AVRequestDetailController import CAVRequestDetail
import math
import uuid
import re

  

class CGeneralClass:

    _mStrLedgerName = ""
    _mDictCompanyData = {
                            "company_name": "",
                            "gst_registration_type": "",
                            "state_name": "",
                            "country_name": "",
                            "gst_in":"",
                        }
    _mstrVoucherTypeName = ""
    _mstrVocuherEntryMode = ""
    _mstrRoundOffLedger = ""
    _mstrDebitLedgerName = "" 
    _mboolTDSApplied = False
    _mstrCategory = None
    _mstrCostCenterLocation = None
    _mIUserId = 4

    @classmethod
    def MCResetAttributes(cls):
        cls._mStrLedgerName = ""
        cls._mDictCompanyData = {
            "company_name": "",
            "gst_registration_type": "",
            "state_name": "",
            "country_name": "",
            "gst_in": "",
        }
        cls._mstrVoucherTypeName = ""
        cls._mstrVocuherEntryMode = ""
        cls._mstrRoundOffLedger = ""
        cls._mstrDebitLedgerName = ""
        cls._mboolTDSApplied = False
        cls._mstrCategory = None
        cls._mstrCostCenterLocation = None
        cls._mIUserId = 4

    @classmethod
    async def MCSetAttributes(cls, strConfigFilePath: str, iUserId: int,boolTDSApplied = False, strCategory = None, strCostCenterLocation = None):

        """
            Load and initialize class attributes from a configuration JSON file.

            This method reads a JSON file specified by the path `strConfigFilePath`, extracts various settings,
            and assigns them to class-level attributes. It also logs the process and handles errors related to
            file reading, JSON parsing, and missing keys.

            Parameters:
                strConfigFilePath (str): Path to the configuration JSON file.
                iUserId (int): The user ID for logging and tracking purposes.
                boolTDSApplied (bool, optional): Indicates whether TDS is applied. Defaults to False.
                strCategory (str, optional): Optional category tag to be assigned. Defaults to None.
                strCostCenterLocation (str, optional): Optional cost center location. Defaults to None.
            
            Returns: None
           
        """

        try:
            cls.MCResetAttributes()
            CGeneralClass._mIUserId = iUserId
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Loading Config file and Initializing attributes")
            # Open and load the JSON file
            with open(strConfigFilePath, 'r') as file:
                data = json.load(file)
            
            # Set the ledger name from DebitLedger.PurchaseLedgerName
            cls._mStrLedgerName = data["VendorName"]
            cls._mstrDebitLedgerName = data["DebitLedger"]["PurchaseLedgerName"]
            # Extract CompanyData for easier access
            company_data = data["CompanyData"]
            # Update the company data dictionary
            cls._mDictCompanyData["company_name"] = company_data["ComapanyName"]
            cls._mDictCompanyData["gst_registration_type"] = company_data["GstRegistrationType"]
            cls._mDictCompanyData["state_name"] = company_data["StateName"]
            cls._mDictCompanyData["country_name"] = company_data["CountryName"]
            cls._mDictCompanyData["gst_in"] = company_data["GstIn"]
            
            # Set other string attributes
            cls._mstrVoucherTypeName = data["VoucherTypeName"]
            cls._mstrVocuherEntryMode = data["VCHEntryMode"]
            cls._mstrRoundOffLedger = data["RoundOffLedger"]
            cls._mboolTDSApplied = boolTDSApplied
            cls._mstrCategory = strCategory
            cls._mstrCostCenterLocation = strCostCenterLocation
        except FileNotFoundError:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Error",f"The file '{strConfigFilePath}' was not found.")
            raise Exception(f"The file '{strConfigFilePath}' was not found.")
        except json.JSONDecodeError as e:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Error",f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
            raise Exception(f"Invalid JSON format in '{strConfigFilePath}': {str(e)}")
        except KeyError as e:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Error",f"Missing required field in JSON: {str(e)}")
            raise Exception(f"Missing required field in JSON: {str(e)}")


    @staticmethod
    async def MGetNarration(invoice_no: str, todays_date: str) -> str:
        """
            Generate a formatted narration string using the invoice number and date.

            Extracts the numeric invoice portion from a formatted invoice string (e.g., "TW-1140/2024-25")
            and combines it with the provided date to produce a standardized narration for record keeping.

            Args:
                invoice_no (str): Invoice number string (e.g., "TW-1140/2024-25").
                todays_date (str): Date string in 'YYYYMMDD' format (e.g., "20250404").

            Returns:
                str: Narration string in the format:
        """
        try:
            match = re.search(r'-(\d+)/', invoice_no)
            return f'BILL NO {match.group(1)} DATED {datetime.strptime(todays_date, "%Y%m%d").strftime("%d/%m/%Y")} FOR TROPHY' if match else ""
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            raise 


    @staticmethod
    async def MSSelectPartyLedgerName():
       
        """
             Asynchronously retrieves the party ledger name for the current vendor.

            This method fetches the debit ledger name (usually set during configuration) from the class-level attribute
            and returns it. It also logs the retrieval action for auditing or debugging purposes.

            Returns:
                str: The debit ledger name associated with the current vendor.
        """
       
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Getting Debit Ledger")
            strPartyName = CGeneralClass._mstrDebitLedgerName #Changes Per Vendor
            return strPartyName
        except Exception as objException:
           await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
           raise
    
    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData: dict) -> list:
        """
            Generates credit ledger entries based on the extracted data.

            If TDS is not applicable, a single credit ledger entry is created for the full amount.
            If TDS is applicable, the total is split into a main credit entry and a separate TDS entry.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                list: A list of one or more ledger entry dictionaries.
        """
       
        """
        Generates credit ledger entries.
        If TDS is applicable (via an external function), add a TDS entry.
        """
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Getting Credit Ledger")
            if not CGeneralClass._mboolTDSApplied:
                credit_ledgers = []
                fTotalAmount = 0
                fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
                main_credit = {
                    "ledger_name": CGeneralClass._mStrLedgerName,  # Fixed for Adani Hazira
                    "amount": fTotalAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }
                credit_ledgers.append(main_credit)
                return main_credit
            else:
                credit_ledgers = []
                fTotalAmount = 0
                fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
                fTDSAmount = math.ceil(fTotalAmount * 0.01)
                fAmount = fTotalAmount - fTDSAmount

                main_credit = {
                    "ledger_name": CGeneralClass._mStrLedgerName,  # Fixed for Adani Hazira
                    "amount": fAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }

                tds_entry = {
                    "ledger_name": "Tds on Contractors",  # Fixed for Adani Hazira
                    "amount": fTDSAmount,
                    "is_deemed_positive": False,  # Credit
                    "is_party_ledger": True,
                }

                return [tds_entry, main_credit]
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            raise
    
    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates a debit ledger entry based on the extracted data and cost center settings.

            If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """

        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Getting Debit Ledger")
            debit_ledger = []
            fTotalAmount = 0
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
            if CGeneralClass._mstrCostCenterLocation == None and CGeneralClass._mstrCategory == None:
                ledger = {
                # "ledger_name": "J School Function Expenses",
                "ledger_name": await CGeneralClass.MSSelectPartyLedgerName(),
                "amount": -fTotalAmount,
                "is_deemed_positive": True,
                "is_party_ledger": False,
            }
                debit_ledger.append(ledger)
                
            else:
                cost_center_allocations = []
                cost_center_allocations.append({"name": CGeneralClass._mstrCostCenterLocation, "amount": -fTotalAmount})
                ledger = {
                "ledger_name":await CGeneralClass.MSSelectPartyLedgerName(),
                "amount":-fTotalAmount,
                "is_deemed_positive": True,  # Debit entry
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": CGeneralClass._mstrCategory,
                "cost_center_allocations": cost_center_allocations,
            }
            return ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Error",objException)
            raise objException

    @staticmethod
    async def MSCleanTallySXML(xml_str: str) -> str:
    # Parse the XML string
        try:
            root = ET.fromstring(xml_str)

            # Define tags to remove (with or without namespaces)
            tags_to_remove = [
                "GSTOVRDNTAXABILITY",
                "GSTOVRDNTYPEOFSUPPLY"
            ]

            # Remove all matching tags in the tree
            for tag in tags_to_remove:
                for elem in root.findall(f".//{tag}"):
                    parent = root.find(f".//{tag}/..")
                    if parent is not None:
                        parent.remove(elem)
       
            cleaned_xml = ET.tostring(root, encoding='unicode')
            return cleaned_xml
        except Exception as e:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", e)
            raise


class CSouthEastAsia(CGeneralClass):

    _mfGstAndOtherChargesRate = 0.847
    _mfTDSRate = 0.02
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._mfGstAndOtherChargesRate = 0.847
        cls._mfTDSRate = 0.02
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, invoice_date: str) -> str:
        '''
            Generates Vendor Specific Narration
        '''

        try:
            # Convert string date to datetime object
            inv_date = datetime.strptime(invoice_date, "%Y%m%d")
            
            # Calculate next month date
            next_date = inv_date + relativedelta(months=1)
            
            # Format dates as DD/MM/YYYY
            formatted_inv_date = inv_date.strftime("%d/%m/%Y")
            formatted_next_date = next_date.strftime("%d/%m/%Y")
            
            return f'BILL No {invoice_no} DATED  D {formatted_inv_date} FOR {formatted_inv_date} TO {formatted_next_date}'
        except Exception as objExecption:
             await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objExecption)
             CSouthEastAsia._mStrTracebackLogs = "MSGetNarration: \n" + str(traceback.format_exc())
            
             raise
            
    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData: dict) -> list:
        '''
            Generates Vendor Spefic Credit Ledger
        '''
        
        try:
            
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Getting Credit Ledger")
            
            fTotalAmount = 0
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
            fDebitUpdated = fTotalAmount * CSouthEastAsia._mfGstAndOtherChargesRate
            fTDSAmount = round(fDebitUpdated * CSouthEastAsia._mfTDSRate)
            fAmount = fTotalAmount - fTDSAmount

            main_credit = {
                "ledger_name": CGeneralClass._mStrLedgerName,  # Fixed for Adani Hazira
                "amount": fAmount,
                "is_deemed_positive": False,  # Credit
                "is_party_ledger": True,
            }

            tds_entry = {
                "ledger_name": "Tds on Contractors",  # Fixed for Adani Hazira
                "amount": fTDSAmount,
                "is_deemed_positive": False,  # Credit
                "is_party_ledger": True,
            }
            return [tds_entry, main_credit]

        except Exception as objExecption:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objExecption)
            CSouthEastAsia._mStrTracebackLogs = "MSGenerateCreditLedger: \n" + str(traceback.format_exc())
            
            raise 
        

    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        '''
            Generates Vendor Spefic Debit Ledger
        '''
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Getting Debit Ledger")
            fTotalAmount = 0
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
            cost_center_allocations = []
            cost_center_allocations.append({"name": "Red Fm", "amount": -fTotalAmount})
            ledger = {
            "ledger_name": await CGeneralClass.MSSelectPartyLedgerName(),
            "amount":-fTotalAmount,
            "is_deemed_positive": True,  # Debit entry
            "is_party_ledger": False,
            "gst_taxability": "Taxable",
            "gst_type_of_supply": "Services",
            "cost_center_category": "ADVERTISEMENT",
            "cost_center_allocations": cost_center_allocations
            }
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Debit Ledger Created")
            return ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CSouthEastAsia._mStrTracebackLogs = "MSGenerateDebitLedger: \n" + str(traceback.format_exc())
           
            raise   
            
    
    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int,dictExtractedData,lsUdfData=None):
        '''
            Create XML code

            returns:
                strXMLCode
        '''
        dictXMLResponse = {
            "xmlContent" : None,
            "strTracebackLogs" : None,
            "AVComments":None,
            "DocErrorMsg":None
        }
        try:
            CSouthEastAsia.reset()
            await CLogController.MSWriteLog(CGeneralClass._mIUserId,"Info","Started Creating XML")
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            xml_str = ""
            ledger_entries = []
            credit_ledger = await CSouthEastAsia.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CSouthEastAsia.MGenerateDebitLedger(dictExtractedData=dictExtractedData)
            for objItem in credit_ledger:
                ledger_entries.append(LedgerEntrySchema(**objItem))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))
            
            invoice_date = dictExtractedData.get("InvoiceDate","")
            invoice_no = dictExtractedData.get("InvoiceNo","")
            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CSouthEastAsia._mDictCompanyData.get("company_name",""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CSouthEastAsia.MSGetNarration(str(invoice_no),invoice_date),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference = "",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 

            )
            
            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            xml_str = await CGeneralClass.MSCleanTallySXML(xml_str)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "XML File Created")
            dictXMLResponse['xmlContent'] = xml_str
            CSouthEastAsia._msTallyStatus = "Success"
            dictXMLResponse['TallyStatus'] = CSouthEastAsia._msTallyStatus
            dictXMLResponse['AVComments'] = CSouthEastAsia._msStrAccuVelocityComments
            dictXMLResponse['strTracebackLogs'] = CSouthEastAsia._mStrTracebackLogs
            return dictXMLResponse
        except Exception as objExecption:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objExecption)
            CSouthEastAsia._mStrTracebackLogs = "MSCreateXML: \n" + str(traceback.format_exc())
            CSouthEastAsia._msStrAccuVelocityComments=("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.")
            CSouthEastAsia._msTallyStatus = "Skipped"
            dictXMLResponse['TallyStatus'] = CSouthEastAsia._msTallyStatus
            dictXMLResponse['AVComments'] = CSouthEastAsia._msStrAccuVelocityComments
            dictXMLResponse['strTracebackLogs'] = CSouthEastAsia._mStrTracebackLogs
            dictXMLResponse['xmlContent'] = xml_str
            return dictXMLResponse


class CGrurKripaPertroleum(CGeneralClass):
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(todays_date: str) -> str:
        '''
            Generate Vendor Specific Narration
        '''
        try:
            todays_date_obj = datetime.strptime(todays_date, "%Y%m%d")
            month_diff = (todays_date_obj.month - 2) % 12 + 1
            year_diff = todays_date_obj.year if todays_date_obj.month > 1 else todays_date_obj.year - 1
            previous_date = todays_date_obj.replace(month=month_diff, year=year_diff)
            formatted_prev_date = previous_date.strftime("%d/%m/%Y")
            formatted_today_date = todays_date_obj.strftime("%d/%m/%Y")
            return f'BILL FROM DATED {formatted_prev_date} TO {formatted_today_date}'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CGrurKripaPertroleum._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code
            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CGrurKripaPertroleum.reset()
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)

            ledger_entries = []
            credit_ledger = await CGrurKripaPertroleum.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CGrurKripaPertroleum.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = str(dictExtractedData.get("InvoiceDate")) if dictExtractedData.get("InvoiceDate") else datetime.today().strftime("%Y%m%d")
            invoice_no = dictExtractedData.get("InvoiceNo", "") or str(uuid.uuid4().int)[:4]

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CGrurKripaPertroleum._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CGrurKripaPertroleum.MSGetNarration(invoice_date),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CGrurKripaPertroleum._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CGrurKripaPertroleum._msTallyStatus
            dictXMLResponse["AVComments"] = CGrurKripaPertroleum._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CGrurKripaPertroleum._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CGrurKripaPertroleum._msTallyStatus = "Skipped"
            CGrurKripaPertroleum._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CGrurKripaPertroleum._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CGrurKripaPertroleum._msTallyStatus
            dictXMLResponse["AVComments"] = CGrurKripaPertroleum._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CGrurKripaPertroleum._mStrTracebackLogs
            return dictXMLResponse

class CTrophyWala(CGeneralClass):
    _msTallyStatus = "Skipped"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "Skipped"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, todays_date: str) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            match = re.search(r'-(\d+)/', invoice_no)
            if match:
                formatted_date = datetime.strptime(todays_date, "%Y%m%d").strftime("%d/%m/%Y")
                return f'BILL NO {match.group(1)} DATED {formatted_date} FOR TROPHY'
            else:
                return ""
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CTrophyWala._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CTrophyWala.reset()
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)

            ledger_entries = []
            credit_ledger = await CTrophyWala.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CTrophyWala.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "") or str(uuid.uuid4().int)[:4]

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CTrophyWala._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CTrophyWala.MSGetNarration(str(invoice_no), invoice_date),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CTrophyWala._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CTrophyWala._msTallyStatus
            dictXMLResponse["AVComments"] = CTrophyWala._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CTrophyWala._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CTrophyWala._msTallyStatus = "Skipped"
            CTrophyWala._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CTrophyWala._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CTrophyWala._msTallyStatus
            dictXMLResponse["AVComments"] = CTrophyWala._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CTrophyWala._mStrTracebackLogs
            return dictXMLResponse

class CRKEnterprises(CGeneralClass):

    _msStrAccuVelocityComments = "-"
    _msTallyStatus = "Skipped"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msStrAccuVelocityComments = "-"
        cls._msTallyStatus = "Skipped"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, dictExtracted: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            strNarration = ""
            for objItem in dictExtracted.get("Products/GoodsNameList", []):
                strNarration += objItem.get("Product/GoodsName", "")
            return f'BILL NO {invoice_no} FOR {strNarration}'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRKEnterprises._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code
            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CRKEnterprises.reset()
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CRKEnterprises.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CRKEnterprises.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
                ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CRKEnterprises._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CRKEnterprises.MSGetNarration(invoice_no, dictExtractedData),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CRKEnterprises._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CRKEnterprises._msTallyStatus
            dictXMLResponse["AVComments"] = CRKEnterprises._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CRKEnterprises._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRKEnterprises._msTallyStatus = "Skipped"
            CRKEnterprises._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CRKEnterprises._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CRKEnterprises._msTallyStatus
            dictXMLResponse["AVComments"] = CRKEnterprises._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CRKEnterprises._mStrTracebackLogs
            return dictXMLResponse

class CSanghiBrothers(CGeneralClass):
    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_date: str, dictExtractedData: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            return f'BILL DATED {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d/%m/%Y")} FOR WINGER NO {dictExtractedData.get("VehicleRegistrationNumber", "")} FOR SERVICE KM {dictExtractedData.get("Kms", "")} V Driver'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CSanghiBrothers._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CSanghiBrothers.reset()
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CSanghiBrothers.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CSanghiBrothers.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CSanghiBrothers._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CSanghiBrothers.MSGetNarration(invoice_date=str(invoice_date), dictExtractedData=dictExtractedData),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CSanghiBrothers._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CSanghiBrothers._msTallyStatus
            dictXMLResponse["AVComments"] = CSanghiBrothers._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CSanghiBrothers._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CSanghiBrothers._msTallyStatus = "Skipped"
            CSanghiBrothers._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CSanghiBrothers._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CSanghiBrothers._msTallyStatus
            dictXMLResponse["AVComments"] = CSanghiBrothers._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CSanghiBrothers._mStrTracebackLogs
            return dictXMLResponse

class CMahaveerKiranaStores(CGeneralClass):
    _msTallyStatus = "-"
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msTallyStatus = "-"
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, invoice_date: str) -> str:
        """
        Generates Vendor Specific Narration.
        """
        try:
            return f'BILL NO {invoice_no} DATED {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d/%m/%Y")} FOR GROCERRY'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CMahaveerKiranaStores._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserID: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CMahaveerKiranaStores.reset()
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserID)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CMahaveerKiranaStores.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CMahaveerKiranaStores.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CMahaveerKiranaStores._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CMahaveerKiranaStores.MSGetNarration(invoice_no, str(invoice_date)),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            

            CMahaveerKiranaStores._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CMahaveerKiranaStores._msTallyStatus
            dictXMLResponse["AVComments"] = CMahaveerKiranaStores._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CMahaveerKiranaStores._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CMahaveerKiranaStores._msTallyStatus = "Skipped"
            CMahaveerKiranaStores._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CMahaveerKiranaStores._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CMahaveerKiranaStores._msTallyStatus
            dictXMLResponse["AVComments"] = CMahaveerKiranaStores._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CMahaveerKiranaStores._mStrTracebackLogs
            return dictXMLResponse

class CRonakRudraSecurity(CGeneralClass):
    _msStrAccuVelocityComments = "-"
    _msTallyStatus = "Skipped"
    _mfTDSRate = 0.01
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        cls._msStrAccuVelocityComments = "-"
        cls._msTallyStatus = "Skipped"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, invoice_date: str, dictExtracted: dict) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            inv_date = datetime.strptime(invoice_date, "%Y%m%d")
            formatted_inv_date = inv_date.strftime("%d/%m/%Y")

            strNarration = ""
            for objItem in dictExtracted.get("Products/GoodsNameList", []):
                strNarration += objItem.get("Product/GoodsName", "")

            return f'BILL No {invoice_no} DATED  D {formatted_inv_date} FOR {strNarration}'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRonakRudraSecurity._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData: dict) -> list:
        """
            Generates Vendor Specific Credit Ledger
        """
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Getting Credit Ledger")
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
            fTDSAmount = round(fTotalAmount * CRonakRudraSecurity._mfTDSRate)
            fAmount = fTotalAmount - fTDSAmount

            main_credit = {
                "ledger_name": CGeneralClass._mStrLedgerName,
                "amount": fAmount,
                "is_deemed_positive": False,
                "is_party_ledger": True,
            }

            tds_entry = {
                "ledger_name": "Tds on Contractors",
                "amount": fTDSAmount,
                "is_deemed_positive": False,
                "is_party_ledger": True,
            }

            return [tds_entry, main_credit]
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRonakRudraSecurity._mStrTracebackLogs = "\nMSGenerateCreditLedger:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates Vendor Specific Debit Ledger
        """
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Getting Debit Ledger")
            fTotalAmount = round(dictExtractedData.get("TotalAmount", 0))
            cost_center_allocations = [{"name": "Red Fm", "amount": -fTotalAmount}]
            ledger = {
                "ledger_name": await CGeneralClass.MSSelectPartyLedgerName(),
                "amount": -fTotalAmount,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": "ADVERTISEMENT",
                "cost_center_allocations": cost_center_allocations
            }
            return ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRonakRudraSecurity._mStrTracebackLogs = "\nMGenerateDebitLedger:\n" + str(traceback.format_exc())
           
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData: dict, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CRonakRudraSecurity.reset()
            await CGeneralClass.MCSetAttributes(strConfigFilePath=strConfigPath, iUserId=iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CRonakRudraSecurity.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CRonakRudraSecurity.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            for objItem in credit_ledger:
                ledger_entries.append(LedgerEntrySchema(**objItem))
            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CRonakRudraSecurity._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CRonakRudraSecurity.MSGetNarration(str(invoice_no), invoice_date, dictExtractedData),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

			 # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CRonakRudraSecurity._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CRonakRudraSecurity._msTallyStatus
            dictXMLResponse["AVComments"] = CRonakRudraSecurity._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CRonakRudraSecurity._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CRonakRudraSecurity._msTallyStatus = "Skipped"
            CRonakRudraSecurity._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CRonakRudraSecurity._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CRonakRudraSecurity._msTallyStatus
            dictXMLResponse["AVComments"] = CRonakRudraSecurity._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CRonakRudraSecurity._mStrTracebackLogs
            return dictXMLResponse

class CGhanshyamBahiniya(CGeneralClass):
    _msStrAccuVelocityComments = "-"
    _msTallyStatus = "Skipped"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        """Reset class variables to their default values."""
        cls._msStrAccuVelocityComments = "-"
        cls._msTallyStatus = "Skipped"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, invoice_date: str) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            return f'BILL NO {invoice_no} DATED {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d/%m/%Y")}'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CGhanshyamBahiniya._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CGhanshyamBahiniya.reset()
            CGeneralClass._mboolTDSApplied = True  # Explicitly set as in original
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CGhanshyamBahiniya.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CGhanshyamBahiniya.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))

            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CGhanshyamBahiniya._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CGhanshyamBahiniya.MSGetNarration(invoice_no, str(invoice_date)),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

			# ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CGhanshyamBahiniya._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CGhanshyamBahiniya._msTallyStatus
            dictXMLResponse["AVComments"] = CGhanshyamBahiniya._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CGhanshyamBahiniya._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CGhanshyamBahiniya._msTallyStatus = "Skipped"
            CGhanshyamBahiniya._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CGhanshyamBahiniya._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CGhanshyamBahiniya._msTallyStatus
            dictXMLResponse["AVComments"] = CGhanshyamBahiniya._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CGhanshyamBahiniya._mStrTracebackLogs
            return dictXMLResponse

class CInfinityCars(CGeneralClass):
    _msStrAccuVelocityComments = "-"
    _msTallyStatus = "Skipped"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        cls._msStrAccuVelocityComments = "-"
        cls._msTallyStatus = "Skipped"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(invoice_no: str, invoice_date: str) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            return f'BILL NO {invoice_no} DATED {datetime.strptime(invoice_date, "%Y%m%d").strftime("%d/%m/%Y")} FOR SERVICE'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CInfinityCars._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                dictXMLResponse
        '''
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CInfinityCars.reset()
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            CGeneralClass._mstrCostCenterLocation = "BMW"
            CGeneralClass._mstrCategory = "CAR"

            credit_ledger = await CInfinityCars.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CInfinityCars.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))

            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CInfinityCars._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CInfinityCars.MSGetNarration(invoice_no, str(invoice_date)),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            xml_str = await CGeneralClass.MSCleanTallySXML(xml_str)
            CInfinityCars._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CInfinityCars._msTallyStatus
            dictXMLResponse["AVComments"] = CInfinityCars._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CInfinityCars._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CInfinityCars._msTallyStatus = "Skipped"
            CInfinityCars._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CInfinityCars._msStrAccuVelocityComments = ("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CInfinityCars._msTallyStatus
            dictXMLResponse["AVComments"] = CInfinityCars._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CInfinityCars._mStrTracebackLogs
            return dictXMLResponse

class CMendwellAgencies(CGeneralClass):
    _msStrAccuVelocityComments = "-"
    _msTallyStatus = "Skipped"
    _mStrTracebackLogs = ""

    @classmethod
    def reset(cls):
        cls._msStrAccuVelocityComments = "-"
        cls._msTallyStatus = "Skipped"
        cls._mStrTracebackLogs = ""

    @staticmethod
    async def MSGetNarration(dictExtracted) -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            strNarration = ""
            for objItem in dictExtracted.get("Products/GoodsNameList", []):
                strNarration += objItem.get("Product/GoodsName", "")
            return f'BILL FOR {strNarration}'
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CMendwellAgencies._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
           
            raise

    @staticmethod
    async def MSCreateXML(strConfigPath: str, iUserId: int, dictExtractedData, lsUdfData=None):
        """
            Create XML code

            returns:
                dictXMLResponse
        """
        dictXMLResponse = {
            "xmlContent": None,
            "TallyStatus": None,
            "AVComments": None,
            "strTracebackLogs": None,
            "DocErrorMsg":None
        }

        try:
            CMendwellAgencies.reset()
            await CGeneralClass.MCSetAttributes(strConfigPath, iUserId)
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Started Creating XML")

            ledger_entries = []
            credit_ledger = await CMendwellAgencies.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CMendwellAgencies.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            if CGeneralClass._mboolTDSApplied:
                for objItem in credit_ledger:
                    ledger_entries.append(LedgerEntrySchema(**objItem))
            else:
                ledger_entries.append(LedgerEntrySchema(**credit_ledger))

            ledger_entries.append(LedgerEntrySchema(**debit_ledger))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = dictExtractedData.get("InvoiceNo", "")

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name=CMendwellAgencies._mDictCompanyData.get("company_name", ""),
                    gst_in=dictExtractedData.get("SellerDetails", {}).get("SellerGST", "").strip(),
                    state_name=dictExtractedData.get("SellerDetails", {}).get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerDetails", {}).get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CMendwellAgencies.MSGetNarration(dictExtractedData),
                voucher_number=f"AV/Purc/{datetime.now().strftime('%d%m%Y')}-{datetime.now().strftime('%H%M%S')}",
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData 
            )

            # ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)

            CMendwellAgencies._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["TallyStatus"] = CMendwellAgencies._msTallyStatus
            dictXMLResponse["AVComments"] = CMendwellAgencies._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CMendwellAgencies._mStrTracebackLogs
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CMendwellAgencies._msTallyStatus = "Skipped"
            CMendwellAgencies._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CMendwellAgencies._msStrAccuVelocityComments =("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.\n")

            dictXMLResponse["xmlContent"] = None
            dictXMLResponse["TallyStatus"] = CMendwellAgencies._msTallyStatus
            dictXMLResponse["AVComments"] = CMendwellAgencies._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CMendwellAgencies._mStrTracebackLogs
            return dictXMLResponse

class CVedanshDiscount(CGeneralClass):
    _msStrAccuVelocityComments = "-"
    _mStrTracebackLogs = ""
    _msTallyStatus = "Skipped"

    @classmethod
    def reset(cls):
        cls._msStrAccuVelocityComments = "-"
        cls._mStrTracebackLogs = ""
        cls._msTallyStatus = "Skipped"

    @staticmethod
    async def MSGenerateCreditLedger(dictExtractedData: dict) -> list:
        """
           
            Generates credit ledger entries based on the extracted data.

            If TDS is not applicable, a single credit ledger entry is created for the full amount.
            If TDS is applicable, the total is split into a main credit entry and a separate TDS entry.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                list: A list of one or more ledger entry dictionaries.
        """

        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Getting Credit Ledger")
            fTotalAmount = dictExtractedData.get("Discounts", [])[0].get("DiscountAmount", 0)
            if fTotalAmount == 0:
                raise ValueError("ValidationError Tally XML: The fee receipt cannot be processed as the total amount is ₹0.")
            main_credit = {
                "ledger_name": dictExtractedData.get("BuyersDetails", {}).get("BuyerName", "") + "_" + str(dictExtractedData.get("OnlineRegNo", "")),
                "amount": fTotalAmount,
                "is_deemed_positive": False,
                "is_party_ledger": True,
            }
            return [main_credit]
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CVedanshDiscount._mStrTracebackLogs = "\nMSGenerateCreditLedger:\n" + str(traceback.format_exc())
           
            raise

    @staticmethod
    async def MGenerateDebitLedger(dictExtractedData: dict):
        """
            Generates a debit ledger entry based on the extracted data.
			If no cost center or category is defined, creates a simple debit entry.
            If either is defined, includes GST and cost center allocation details.

            Args:
                dictExtractedData (dict): Dictionary containing extracted data, including "TotalAmount".

            Returns:
                dict: A single debit ledger entry as a dictionary.
        """
        try:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Info", "Getting Debit Ledger")
            fTotalAmount = dictExtractedData.get("Discounts", [])[0].get("DiscountAmount", 0)
            strClass = dictExtractedData.get("Class", "")
            if re.match(r'^[0-9]+', strClass):
                strLedgerName = "S Lum Sum Fee Discount"
            else:
                strLedgerName = "J Lum Sum Fee Discount"

            cost_center_allocations = []
            cost_center_allocations.append({"name": CGeneralClass._mstrCostCenterLocation, "amount": -fTotalAmount})
            ledger = {
                "ledger_name": strLedgerName,
                "amount": -fTotalAmount,
                "is_deemed_positive": True,
                "is_party_ledger": False,
                "gst_taxability": "Taxable",
                "gst_type_of_supply": "Services",
                "cost_center_category": CGeneralClass._mstrCategory,
                "cost_center_allocations": cost_center_allocations,
            }
            return ledger
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CVedanshDiscount._mStrTracebackLogs = "\nMGenerateDebitLedger:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSGetNarration() -> str:
        """
            Generates Vendor Specific Narration
        """
        try:
            return "Processed By Accuvelocity"
        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            CVedanshDiscount._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())
            
            raise

    @staticmethod
    async def MSCreateXML(iUserId: int, dictExtractedData, lsUdfData=None):
        '''
            Create XML code

            returns:
                strXMLCode
        '''
        try:
            dictXMLResponse = {
                "xmlContent": None,
                "TallyStatus": None,
                "AVComments": None,
                "strTracebackLogs": None,
                "DocErrorMsg":None
            }
            ledger_entries = []
            strClass = dictExtractedData.get("Class","")
            if re.match(r'^[0-9]+', strClass):
                strCostCentre = "S Discount"
            else:
                strCostCentre = "J Discount"
            CGeneralClass._mstrCostCenterLocation = strCostCentre  # Two Possibel Values J Discount S Discount
            CGeneralClass._mstrCategory="DISCOUNT"
            ledger_entries = []
            credit_ledger = await CVedanshDiscount.MSGenerateCreditLedger(dictExtractedData=dictExtractedData)
            debit_ledger = await CVedanshDiscount.MGenerateDebitLedger(dictExtractedData=dictExtractedData)

            ledger_entries.append(LedgerEntrySchema(**debit_ledger))
            for obj in credit_ledger:
                ledger_entries.append(LedgerEntrySchema(**obj))

            invoice_date = dictExtractedData.get("InvoiceDate", "")
            invoice_no = "DISCOUNT"

            voucher_input = TallyJournalVoucherInputSchema(
                company_info=CompanyInfoSchema(
                    company_name="Vidhyarambh Education Society (2024-2025)",
                    gst_in=dictExtractedData.get("SellerGSTIN", "").strip(),
                    state_name=dictExtractedData.get("SellerState", "").strip(),
                    country_name=dictExtractedData.get("SellerCountry", "India").strip(),
                ),
                voucher_date=invoice_date,
                narration=await CVedanshDiscount.MSGetNarration(),
                voucher_number=invoice_no,
                voucher_type="Journal",
                reference="",
                effective_date=invoice_date,
                ledger_entries=ledger_entries,
                udf_data=lsUdfData
            )

			# ----------------------- Generate XML via the Template --------------------------
            voucher_template = CTallyJournalVoucherTemplate(voucher_input)
            xml_str = voucher_template.to_xml(pretty=True)
            CVedanshDiscount._msTallyStatus = "Success"
            dictXMLResponse["xmlContent"] = xml_str
            dictXMLResponse["AVComments"] = CVedanshDiscount._msStrAccuVelocityComments
            dictXMLResponse["strTracebackLogs"] = CVedanshDiscount._mStrTracebackLogs
            dictXMLResponse["TallyStatus"] = CVedanshDiscount._msTallyStatus
            return dictXMLResponse

        except Exception as objException:
            await CLogController.MSWriteLog(CGeneralClass._mIUserId, "Error", objException)
            dictXMLResponse["TallyStatus"] = CVedanshDiscount._msTallyStatus
            CVedanshDiscount._mStrTracebackLogs = "\nMSCreateXML:\n" + str(traceback.format_exc())
            CVedanshDiscount._msStrAccuVelocityComments =("The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.")
            raise


async def main():
    api_response_dir=r"D:\Nisarg\IndianInvoice\data\apiResponses\Infinity Cars"
    output_dir=r"GitIgnore\\Infinity Cars"
    strConfigFilePath = r"Data\Customer\VedanshSchool\InfinityCars\VedanshInfinityCarsConfig.json"
    for filename in os.listdir(api_response_dir):
        try:
            if filename.endswith("_gptResponse.json"):
                #
                json_file_path = os.path.join(api_response_dir, filename)
                
            
                with open(json_file_path, "r") as file:
                    api_response = json.load(file) 
            
                content = api_response['choices'][0]['message']['content']
                content = json.loads(content)
                # CGeneralClass.MCSetAttributes(strConfigFilePath, 4)
                XmlOutput = await CInfinityCars.MSCreateXML(strConfigFilePath, 4, content)
                
            
                invoice_no = content.get("InvoiceNo", "NA")
                invoice_no = invoice_no.replace("/", "_")
                
                
                xml_file_name = f"Invoiceno_{invoice_no}_xmlfile.xml"
                xml_file_path = os.path.join(output_dir, xml_file_name)
                
      
                with open(xml_file_path, "w") as xml_file:
                    xml_file.write(XmlOutput.get("xmlContent"))
        except Exception as e:
            print("Error Occur - ", traceback.format_exc())        

if __name__ == "__main__":
    asyncio.run(main())