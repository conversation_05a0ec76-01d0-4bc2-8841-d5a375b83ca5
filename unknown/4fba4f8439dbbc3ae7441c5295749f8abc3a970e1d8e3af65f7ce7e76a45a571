{"type": "json_schema", "json_schema": {"name": "6_<PERSON>OD<PERSON>CH", "strict": true, "schema": {"type": "object", "properties": {"CompanyName": {"type": "string", "description": "The name of the company issuing or receiving the invoice"}, "CompanyAddress": {"type": "string", "description": "The address of the company issuing the invoice"}, "GSTNCode": {"type": "string", "description": "Goods and Services Tax Network code of the company issuing the invoice"}, "IRN": {"type": "string", "description": "Invoice Reference Number, a unique identifier for the invoice"}, "SellerAddress": {"type": "string", "description": "The address of the seller receiving the invoice"}, "SellerGSTNCode": {"type": "string", "description": "Goods and Services Tax Network code of the seller"}, "SellerPAN": {"type": "string", "description": "Permanent Account Number (PAN) of the seller"}, "InvoiceNo.": {"type": "string", "description": "The invoice number assigned to this specific transaction"}, "InvoiceDate": {"type": "string", "description": "The date the invoice was issued. Change Date Format to 'YYYYMMDD'"}, "InvoiceType": {"type": "string", "description": "The type or category of the invoice."}, "RecieptPlace": {"type": "string", "description": "The location of the receipt's generation. "}, "DueDate": {"type": "string", "description": "The date by which payment for the invoice is due. Change Date Format to 'YYYYMMDD'"}, "Principal": {"type": "string", "description": "The entity involved in the transaction"}, "B/LNo": {"type": "string", "description": "Bill of Lading Number, a unique identifier for shipping documents"}, "B/LDate": {"type": "string", "description": "The date of the Bill of Lading. Change Date Format to 'YYYYMMDD'"}, "BookingId": {"type": "string", "description": "A unique identifier for the booking or reservation"}, "BookingParty": {"type": "string", "description": "The entity or individual responsible for making the booking"}, "Shipper": {"type": "string", "description": "The party responsible for shipping the goods"}, "Commodity": {"type": "string", "description": "The type of goods or commodity being shipped or invoiced"}, "Weights": {"type": "string", "description": "The weight of the goods being shipped"}, "NoOfPkgs": {"type": "string", "description": "Number of packages included in the shipment"}, "Vessel": {"type": "string", "description": "The name of the vessel transporting the goods"}, "VoyageNo": {"type": "string", "description": "The voyage number associated with the shipment"}, "PointOfOrigin": {"type": "string", "description": "The starting location of the shipment"}, "PortOfLoading": {"type": "string", "description": "The port where the goods are loaded onto the vessel"}, "PortOfDischarg": {"type": "string", "description": "The port where the goods are unloaded (likely a typo, should be 'PortOfDischarge')"}, "DateOfSailing": {"type": "string", "description": "The date the vessel departs. Change Date Format to 'YYYYMMDD'"}, "DateOfSupply": {"type": "string", "description": "The date the goods or services were supplied. Change Date Format to 'YYYYMMDD'"}, "PlaceofSupply": {"type": "string", "description": "The location where the supply of goods or services occurs"}, "NoOfContainers": {"type": "string", "description": "The number of containers used in the shipment"}, "ContainerNo's": {"type": "string", "description": "The identification numbers of the containers (likely should be 'ContainerNos')"}, "PANNo": {"type": "string", "description": "Permanent Account Number, a unique tax identifier"}, "FinalSummary": {"type": "object", "description": "The final summary row containing total values.", "properties": {"TotalAmountInWords": {"type": "string", "description": "Total amount in words."}, "Qty": {"type": "integer", "description": "Total quantity of items."}, "UnitRate": {"type": "number", "description": "Total unit rate (if applicable)."}, "UnitRareInUSD": {"type": "number", "description": "Total unit rate in USD."}, "TaxableAmount": {"type": "number", "description": "Total taxable amount."}, "SGST": {"type": "number", "description": "Total State Goods and Services Tax."}, "CGST": {"type": "number", "description": "Total Central Goods and Services Tax."}, "FinalTotalAmountINR": {"type": "number", "description": "Final total amount including taxes."}}, "required": ["TotalAmountInWords", "Qty", "UnitRate", "UnitRareInUSD", "TaxableAmount", "SGST", "CGST", "FinalTotalAmountINR"], "additionalProperties": false}, "ItemTable1": {"type": "array", "items": {"type": "object", "properties": {"Description": {"type": "string", "description": "A description of the item or service"}, "HSNCode": {"type": "integer", "description": "Harmonized System Nomenclature code for the item"}, "Qty": {"type": "integer", "description": "The quantity of the item"}, "UnitRate": {"type": "number", "description": "The rate per unit of the item in the local currency."}, "UnitRareInUSD": {"type": "number", "description": "The rate per unit of the item in USD."}, "TaxableAmount": {"type": "number", "description": "The amount subject to taxation for the item"}, "RATE": {"type": "number", "description": "The total rate or cost per item."}, "SGST": {"type": "number", "description": "State Goods and Services Tax amount"}, "CGST": {"type": "number", "description": "Central Goods and Services Tax amount"}, "AmountINR": {"type": "number", "description": "The total amount for the item in Indian Rupees"}, "Total": {"type": "number", "description": "The total cost including taxes or additional charges"}}, "required": ["Description", "HSNCode", "Qty", "UnitRate", "UnitRareInUSD", "RATE", "SGST", "CGST", "AmountINR", "Total", "TaxableAmount"], "additionalProperties": false}}}, "required": ["CompanyName", "CompanyAddress", "GSTNCode", "IRN", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerGSTNCode", "SellerPAN", "InvoiceNo.", "InvoiceDate", "InvoiceType", "RecieptPlace", "DueDate", "Principal", "B/LNo", "B/LDate", "BookingId", "BookingParty", "Shipper", "Commodity", "Weights", "NoOfPkgs", "<PERSON><PERSON><PERSON>", "VoyageNo", "PointOfOrigin", "PortOfLoading", "PortOfDischarg", "DateOfSailing", "DateOfSupply", "PlaceofSupply", "NoOfContainers", "ContainerNo's", "PANNo", "ItemTable1", "FinalSummary"], "additionalProperties": false}}}