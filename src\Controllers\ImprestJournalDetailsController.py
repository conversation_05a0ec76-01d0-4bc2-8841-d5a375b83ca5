from config.db_config import AsyncSessionLocal
from sqlalchemy import update
from sqlalchemy.exc import SQLAlchemyError
from src.Models.models import ImprestJournalDetails
from src.Controllers.Logs_Controller import CLogController
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.orm import sessionmaker
from sqlalchemy.sql import update, select, func
from sqlalchemy.ext.declarative import declarative_base
import traceback
import pickle
import asyncio
import pandas as pd

Base = declarative_base()

class CImprestJournalDetails:
    """
    Purpose:
        This class handles database operations for the `ImprestJournalDetails` table.
        It provides functionality to create and update records in the `ImprestJournalDetails` table
        using primary identifiers such as `UID` and flexible key-value pairs provided via kwargs.
    """
    
    @staticmethod
    async def MSGetNextJID(iUserID:int) -> int:
        """
        Retrieves the next JID value by finding the maximum JID in the ImprestJournalDetails table
        and incrementing it by 1. Returns 0 if no records exist.

        Returns:
            int: The next JID value to be used for a new record.

        Raises:
            SQLAlchemyError: If a database error occurs during the query.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Query the maximum JID value
                result = await db.execute(select(func.max(ImprestJournalDetails.JVID)))
                max_jid = result.scalar()
                
                # If no records exist, return 0; otherwise, return max JID + 1
                return 0 if max_jid is None else max_jid + 1
            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve next JID: {str(e)}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred while retrieving JID.")
    
    @staticmethod
    async def MSGetNextJournalID(iUserID: int) -> int:
        """
        Retrieves the next JID value by finding the maximum JID in the ImprestJournalDetails table
        and incrementing it by 1. Returns 0 if no records exist.

        Returns:
            int: The next JID value to be used for a new record.

        Raises:
            SQLAlchemyError: If a database error occurs during the query.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Query the maximum JID value
                result = await db.execute(select(func.max(ImprestJournalDetails.ImprestFileID)))
                max_journal_id = result.scalar()
                
                # If no records exist, return 0; otherwise, return max ImprestJournalID + 1
                return 0 if max_journal_id is None else max_journal_id + 1
            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve next ImprestJournalID: {str(e)}")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred while retrieving ImprestJournalID.")
    
    
    @staticmethod
    async def MSInsertJournalRecord(dictData: dict, userId: int):
        """
        Adds a new ImprestJournalDetails record to the database.

        Args:
            dictData (dict): Dictionary containing column-value pairs for the new record.
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing APIStatusCode, detail, and the UID of the created record.

        Raises:
            Exception: If a database error or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # In dictData Add JID dictData
                objNewRecord = ImprestJournalDetails(**dictData)
                loop = asyncio.get_running_loop()

                db.add(objNewRecord)
                await db.commit()
                await db.refresh(objNewRecord)

                await CLogController.MSWriteLog(userId, "Info", f"ImprestJournalDetails record for ClientREQID: {dictData.get('strClientREQID')} saved successfully.")

                return {
                    "APIStatusCode": 200,
                    "detail": "Record saved successfully",
                    "JVID": objNewRecord.JVID
                }
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Failed to save ImprestJournalDetails record to DB")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")

          
    @staticmethod
    async def MSUpdateJournalRecord(strClientREQID: str, JID: int, userId: int, kwargs):
        """
        Updates an existing ImprestJournalDetails record in the database based on strClientREQID and JID.
        
        Args:
            strClientREQID (str): The client request ID to identify the record.
            JID (int): The journal id  to identify the record.
            userId (int): The ID of the user performing the update for logging purposes.
            **kwargs: Arbitrary key-value pairs corresponding to columns in ImprestJournalDetails to update.
        
        Returns:
            dict: A dictionary containing APIStatusCode and detail about the update operation.
        
        Raises:
            Exception: If a database error, validation error, or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                loop = asyncio.get_running_loop()
                # Construct the update statement
                stmt = update(ImprestJournalDetails).where(
                    ImprestJournalDetails.strClientREQID == strClientREQID,
                    ImprestJournalDetails.JVID == JID
                ).values(**kwargs)
                
                # Execute the update
                result = await db.execute(stmt)
                
                # Check if any rows were affected
                if result.rowcount == 0:
                    await CLogController.MSWriteLog(userId, "Warning", f"No ImprestJournalDetails record found with strClientREQID: {strClientREQID} and JVSheetNo: {JID}")
                    raise Exception("Record not found.")
                
                await db.commit()
                
                await CLogController.MSWriteLog(userId, "Info", f"ImprestJournalDetails record with strClientREQID: {strClientREQID} and JVSheetNo: {JID} updated successfully.")
                
                return {
                    "APIStatusCode": 200,
                    "detail": "Record updated successfully",
                }
            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Failed to update ImprestJournalDetails record with strClientREQID: {strClientREQID}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")

    @staticmethod
    async def MSGetJournalSummary(strClientREQID: str,userId):
        
        """
        Retrieves summary information from the ImprestJournalDetails table asynchronously.

        This method queries the database to compute the sum of TotalEntries, retrieve all
        EstAccountantTimeSaved values, and fetch EstAccountantTimeSaved values where
        strClientREQID matches the provided parameter, using an async session.

        Parameters
        ----------
        strClientREQID : str
            The client request ID to filter EstAccountantTimeSaved values.
        AsyncSessionLocal : async_sessionmaker[AsyncSession]
            The async session factory for database connections.

        Returns
        -------
        dict
            A dictionary containing:
            - total_entries_sum: Sum of TotalEntries across all records (0 if no records).
            - all_est_accountant_time_saved: List of all EstAccountantTimeSaved values ('-' for NULL).
            - filtered_est_accountant_time_saved: List of EstAccountantTimeSaved values where
            strClientREQID matches ('-' for NULL).

        Raises
        ------
        Exception
            If database query execution encounters an error, logs the error
            using CLogController.MSWriteLog and raises the exception.

        Examples
        --------
        >>> from sqlalchemy.ext.asyncio import async_sessionmaker, AsyncSession
        >>> result = await CImprestJournal.MSGetJournalSummary(
        ...     strClientREQID="REQ123",
        ...     AsyncSessionLocal=AsyncSessionLocal
        ... )
        >>> print(result)
        {
            'total_entries_sum': 10,
            'all_est_accountant_time_saved': ['2 hours', '-', '1 hour'],
            'filtered_est_accountant_time_saved': ['2 hours']
        }
        """
        try:
            await CLogController.MSWriteLog(userId, "Info", 
                f"Querying ImprestJournalDetails for strClientREQID: {strClientREQID}")
            
            async with AsyncSessionLocal() as db:
                # Query sum of TotalEntries
                total_entries_sum = await db.execute(
                select(func.count())
                .filter(ImprestJournalDetails.AVXMLGeneratedStatus == 'Success')
                )
                total_entries_sum = total_entries_sum.scalar() or 0

                # Query all EstAccountantTimeSaved values
                all_times_saved = await db.execute(
                    select(func.coalesce(ImprestJournalDetails.EstAccountantTimeSaved, '-'))
                )
                all_times_saved = [row[0] for row in all_times_saved.fetchall()]

                # Query EstAccountantTimeSaved where strClientREQID matches
                filtered_times_saved = await db.execute(
                    select(func.coalesce(ImprestJournalDetails.EstAccountantTimeSaved, '-'))
                    .filter(ImprestJournalDetails.strClientREQID == strClientREQID)
                )
                filtered_times_saved = [row[0] for row in filtered_times_saved.fetchall()]
                
                 # Query Total processed Journals where strClientREQID matches
                result = await db.execute(
                select(func.count())
                .filter(ImprestJournalDetails.strClientREQID == strClientREQID)
                .filter(ImprestJournalDetails.AVXMLGeneratedStatus == 'Success')
            )
                todays_entry = result.scalar() or 0

            # Return results
            return {
                "total_entries_sum": total_entries_sum,
                "all_est_accountant_time_saved": all_times_saved,
                "filtered_est_accountant_time_saved": filtered_times_saved,
                "todays_entry":todays_entry
            }

        except Exception as e:
            await CLogController.MSWriteLog(userId, "Error", 
                f"Error querying ImprestJournalDetails: {str(e)}")
            raise e
        
    
    @staticmethod
    async def MSGetJournalDataFrames(userId: int) -> dict:
        """
        Retrieves all JournalDataFrame values from the ImprestJournalDetails table asynchronously.

        This method queries the database to fetch all JournalDataFrame values using an async session.

        Args:
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing:
                - journal_dataframes: List of all JournalDataFrame values ('-' for NULL).

        Raises:
            SQLAlchemyError: If a database error occurs during the query.
            Exception: If an unhandled exception occurs, logs the error and raises an Exception.

        Examples:
            >>> result = await CImprestJournalDetails.MSGetJournalDataFrames(userId=1)
            >>> print(result)
            {
                'journal_dataframes': ['dataframe1', '-', 'dataframe2']
            }
        """
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(userId, "Info", 
                    f"Querying all JournalDataFrame values from ImprestJournalDetails")

                # Query all JournalDataFrame values
                all_dataframes = await db.execute(
                    select(func.coalesce(ImprestJournalDetails.JournalDataFrame, '-'))
                )
                all_dataframes = [row[0] for row in all_dataframes.fetchall()]
                
                # Filter out '-' or b'-' values and decode bytes if necessary
                filtered_dataframes = []
                for item in all_dataframes:
                    if isinstance(item, bytes) and item == b'-':
                        await CLogController.MSWriteLog(userId, "Info", 
                            f"Skipping b'-' value in JournalDataFrame")
                        continue
                    if isinstance(item, str) and item == '-':
                        await CLogController.MSWriteLog(userId, "Info", 
                            f"Skipping '-' value in JournalDataFrame")
                        continue
                    filtered_dataframes.append(item)
                
                # Return results
                return {
                    "journal_dataframes": filtered_dataframes
                }

            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(userId, "Error", 
                    f"Failed to query JournalDataFrame: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", 
                    f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred while retrieving JournalDataFrame.")
            except Exception as e:
                await CLogController.MSWriteLog(userId, "Error", 
                    f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", 
                    f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")
    
    @staticmethod
    async def MSGetUnpickledJournalDataFrames(userId: int) -> list:
        """
        Retrieves all JournalDataFrame values from the ImprestJournalDetails table, unpickles them,
        and returns a list of pandas DataFrames.

        This method calls MSGetJournalDataFrames to fetch pickled JournalDataFrame values,
        unpickles each non-NULL value, and converts them into pandas DataFrames.

        Args:
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            list: A list of pandas DataFrames created from unpickled JournalDataFrame values.
                  Non-DataFrame or NULL ('-') values are skipped.

        Raises:
            SQLAlchemyError: If a database error occurs during the query.
            pickle.PickleError: If unpickling a JournalDataFrame value fails.
            Exception: If an unhandled exception occurs, logs the error and raises an Exception.

        Examples:
            >>> dfs = await CImprestJournalDetails.MSGetUnpickledJournalDataFrames(userId=1)
            >>> print(len(dfs))
            2
            >>> print(type(dfs[0]))
            <class 'pandas.core.frame.DataFrame'>
        """
        try:
            await CLogController.MSWriteLog(userId, "Info", 
                f"Retrieving and unpickling JournalDataFrame values from ImprestJournalDetails")

            # Fetch pickled JournalDataFrame values
            result = await CImprestJournalDetails.MSGetJournalDataFrames(userId)
            pickled_dataframes = result.get("journal_dataframes", [])

            # Unpickle and convert to pandas DataFrames
            dataframes = []
            for idx, item in enumerate(pickled_dataframes):
                if item == '-':
                    await CLogController.MSWriteLog(userId, "Info", 
                        f"Skipping NULL JournalDataFrame at index {idx}")
                    continue
                try:
                    # Unpickle the item
                    df = pickle.loads(item)
                    if isinstance(df, pd.DataFrame):
                        dataframes.append(df)
                    else:
                        await CLogController.MSWriteLog(userId, "Warning", 
                            f"Item at index {idx} is not a pandas DataFrame after unpickling")
                except pickle.PickleError as e:
                    await CLogController.MSWriteLog(userId, "Error", 
                        f"Failed to unpickle JournalDataFrame at index {idx}: {str(e)}")
                    await CLogController.MSWriteLog(userId, "Debug", 
                        f"Traceback: {traceback.format_exc()}")
                    continue  # Skip invalid pickles and continue processing

            await CLogController.MSWriteLog(userId, "Info", 
                f"Successfully unpickled {len(dataframes)} JournalDataFrame values")

            return dataframes

        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(userId, "Error", 
                f"Database error while retrieving JournalDataFrame: {str(e)}")
            await CLogController.MSWriteLog(userId, "Debug", 
                f"Traceback: {traceback.format_exc()}")
            raise Exception("Database error occurred while retrieving JournalDataFrame.")
        except Exception as e:
            await CLogController.MSWriteLog(userId, "Error", 
                f"Unhandled exception: {str(e)}")
            await CLogController.MSWriteLog(userId, "Debug", 
                f"Traceback: {traceback.format_exc()}")
            raise Exception("Unhandled error occurred.")
    
    @staticmethod
    async def MSIsDataFrameInJournal(df: pd.DataFrame, userId: int) -> bool:
        """
        Checks if a given pandas DataFrame exists in the list of unpickled JournalDataFrame values.

        This method retrieves the list of DataFrames from MSGetUnpickledJournalDataFrames and
        checks if the provided DataFrame matches any of them using pandas' equals method.

        Args:
            df (pd.DataFrame): The pandas DataFrame to check for existence.
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            bool: True if the DataFrame exists in the JournalDataFrame values, False otherwise.

        Raises:
            SQLAlchemyError: If a database error occurs during the query.
            Exception: If an unhandled exception occurs, logs the error and raises an Exception.

        """
        try:
            await CLogController.MSWriteLog(userId, "Info", 
                f"Checking if provided DataFrame exists in JournalDataFrame values")
            
            required_columns = ['Timestamp', 'Email address', 'Site Name', 'Emprest Holder', 'Date of Exp.', 'Amount Rs.']
            
            # Validate input DataFrame
            if not isinstance(df, pd.DataFrame):
                await CLogController.MSWriteLog(userId, "Error", 
                    f"Provided input is not a pandas DataFrame")
                raise Exception("Input must be a pandas DataFrame")

            # Fetch unpickled DataFrames
            dataframes = await CImprestJournalDetails.MSGetUnpickledJournalDataFrames(userId)
            
            # Converting Time Date
            df['Timestamp'] = pd.to_datetime(df['Timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')
            
            # Check if the provided DataFrame matches any in the list
            for idx, stored_df in enumerate(dataframes):
                # Ensure stored DataFrame has required columns
                if not all(col in stored_df.columns for col in required_columns):
                    await CLogController.MSWriteLog(userId, "Info", 
                    f"Stored DataFrame at index {idx} missing required columns, skipping")
                    continue
                
                # Select only the required columns from both DataFrames
                df_subset = df[required_columns]
                stored_df_subset = stored_df[required_columns]
                stored_df_subset['Timestamp'] = pd.to_datetime(stored_df_subset['Timestamp']).dt.strftime('%Y-%m-%d %H:%M:%S')
                
                # Check for any row match
                for _, row in df_subset.iterrows():
                    # Check if the row exists in stored_df_subset
                    if stored_df_subset.eq(row).all(axis=1).any():
                        await CLogController.MSWriteLog(userId, "Info", 
                            f"Matching row found in JournalDataFrame at index {idx}")
                        return True
            await CLogController.MSWriteLog(userId, "Info", 
                f"DataFrame not found in JournalDataFrame values")
            return False

        except SQLAlchemyError as e:
            await CLogController.MSWriteLog(userId, "Error", 
                f"Database error while checking DataFrame: {str(e)}")
            await CLogController.MSWriteLog(userId, "Debug", 
                f"Traceback: {traceback.format_exc()}")
            raise Exception("Database error occurred while checking DataFrame.")
        except Exception as e:
            await CLogController.MSWriteLog(userId, "Error", 
                f"Unhandled exception: {str(e)}")
            await CLogController.MSWriteLog(userId, "Debug", 
                f"Traceback: {traceback.format_exc()}")
            raise Exception("Unhandled error occurred.")