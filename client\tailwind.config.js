/** @type {import('tailwindcss').Config} */
import withMT from "@material-tailwind/react/utils/withMT";
import { default: flattenColorPalette } from "tailwindcss/lib/util/flattenColorPalette"

module.exports = withMT({
  content: ["./index.html",
    "./src/**/*.{vue,js,ts,jsx,tsx}",
    "path-to-your-node_modules/@material-tailwind/react/components/**/*.{js,ts,jsx,tsx}",
    "path-to-your-node_modules/@material-tailwind/react/theme/components/**/*.{js,ts,jsx,tsx}",],
  theme: {
    fontFamily: {
      sans: ['Inter'],
    },
    extend: {
      screens: {
        'sm': { 'min': '640px', 'max': '767px' },
        'smo': { 'min': '640px' },
        'md': { 'min': '768px', 'max': '1023px' },
        'mdo': { 'min': '768px' },
        'lg': { 'min': '1024px', 'max': '1279px' },
        'lgo': { 'min': '1024px' },
        'xl': { 'min': '1280px', 'max': '1600px' },  // Custom breakpoint for laptops
        'xlo': { 'min': '1280px' },  // Custom breakpoint for laptops
        '2xl': { 'min': '1601px' }, // For desktops with 1920x1080 or more
        '2xlo': { 'min': '1536px' }, // For desktops with 1920x1080 or more
      },
      colors: {
        'primary': '#003654',
      },
      fontSize: {
        'custom': ['25.91px', '32.38px'], // [fontSize, lineHeight]
      },
      keyframes: {
        fadeAnim: {
          from: { opacity: 0 },
          to: { opacity: 1 },
        },
      },
      animation: {
        fadeAnim: 'fadeAnim 0.5s ease-out',
      },
      boxShadow: {
        boxshadow: '0 0 5px 3px rgba(0,0,0,.07)',
        boxshadow_2: 'rgba(149, 157, 165, 0.2) 0px 8px 24px'
      },
      variants: {
        extend: {
          backgroundColor: ['even', 'odd', 'hover'],
        },
      },
    },

  },
  plugins: [
    require("@tailwindcss/aspect-ratio"),
    addVariablesForColors,
  ],
});

// This plugin adds each Tailwind color as a global CSS variable, e.g. var(--gray-200).
function addVariablesForColors({ addBase, theme }) {
  let allColors = flattenColorPalette(theme("colors"));
  let newVars = Object.fromEntries(
    Object.entries(allColors).map(([key, val]) => [`--${key}`, val])
  );

  addBase({
    ":root": newVars,
  });
}