import { <PERSON>ton, ButtonGroup } from "@material-tailwind/react";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import PropTypes from 'prop-types';
import { useCallback, useEffect, useState } from "react";
import loading from '/animations/loading.svg';
import { RiDownloadLine } from "react-icons/ri";
import toast, { Toaster } from "react-hot-toast";
import axios from "axios";
import { Tooltip } from "@material-tailwind/react";
import _ from 'lodash'
import { jwtDecode } from "jwt-decode";
import Papa from 'papaparse';
import { saveAs } from 'file-saver';
import { MdOutlineBarChart, MdOutlineClear, MdRemoveRedEye } from "react-icons/md";
import { FiSearch } from "react-icons/fi";
import { setCookie, getCookie } from '../../utils/cookieUtils.js';
import { AiOutlineCloudUpload } from "react-icons/ai";
import { IoDocumentTextOutline } from "react-icons/io5";
import { FaUser } from "react-icons/fa6";
import { TbLogs } from "react-icons/tb";
import BillingPath from '../../assets/Strings/BillingPath.js'
import fetchData from "../../utils/fetchData.js";
import { LogsContent } from "../../components/ActivityLog";

const ActivityLog = ({ showAlert }) => {
    const [activeTab, setActiveTab] = useState('today');
    const [activePage, setActivePage] = useState(1);
    // Filters and search state
    const [activeFilter, setActiveFilter] = useState('all');
    const [search, setSearch] = useState({ data: '' });
    const [searchTerm, setSearchTerm] = useState('');
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [isLoading, setIsLoading] = useState(true);
    const [data, setData] = useState([]);
    const [UserRegistered, setUserRegistered] = useState('');
    const [isSearching, setIsSearching] = useState(false);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [sorting, setSorting] = useState({
        bSectionAsc: null,
        bMessageAsc: null,
        bLogDateTimeAsc: 0,
    });
    const [totalPages, setTotalPages] = useState(0)

    const [modalIsOpen, setModalIsOpen] = useState(false);
    const [modalContent, setModalContent] = useState(null);

    const openModal = (row) => {
        setModalContent(row);
        setModalIsOpen(true);
    };

    const closeModal = () => {
        setModalIsOpen(false);
        setModalContent(null);
    };

    const flattenData = (data) => {
        return data.map(item => ({
            LogType: item.LogType,
            LogMessage: item.LogMessage,
            LogDateTime: `${item.LogDateTime.month} ${item.LogDateTime.day}, ${item.LogDateTime.year} ${item.LogDateTime.hours}:${item.LogDateTime.minutes} ${item.LogDateTime.am_pm}`,
            Section: item.Section,
        }));
    };

    const formatDateEDT = (dateObj) => {
        // Extract components from the date object
        const { month, day, year, hours, minutes, am_pm } = dateObj;

        // Format the hours and minutes to ensure two digits
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        // Construct the formatted date string
        return (
            <span className="flex xl:text-xs 2xl:text-[1rem] flex-col ml-2">
                <span>
                    {`${formattedHours}:${formattedMinutes} ${am_pm} EDT`}
                </span>
                <span className="xl:text-xs 2xl:text-md 2xl:mr-[0.120rem]" style={{ color: '#9C9AA5' }}>
                    {`${month} ${day}, ${year}`}
                </span>
            </span>
        );
    };

    const prevPage = () => {
        setActivePage(activePage - 1);
    };

    const nextPage = () => {
        setActivePage(activePage + 1);
    };

    const timeZone = 'America/New_York'; // Specify your timezone

    const isXLScreen = () => {
        return window.innerWidth >= 1280 && window.innerWidth < 1600;
    };

    const formatDateForActiveTab = (dateString) => {
        const date = new Date(dateString);
        if (isNaN(date)) return 'Invalid Date';

        const options = isXLScreen()
            ? { year: 'numeric', month: 'short', day: 'numeric', timeZone }
            : { year: 'numeric', month: 'long', day: 'numeric', timeZone };

        const dateStringFormatted = new Intl.DateTimeFormat('en-US', options).format(date);

        const dayOptions = isXLScreen()
            ? { weekday: 'short', timeZone }
            : { weekday: 'long', timeZone };

        const dayString = new Intl.DateTimeFormat('en-US', dayOptions).format(date);

        return `${dateStringFormatted} (${dayString})`;
    };

    const formatYmdInTimeZone = (dateString) => {
        const date = new Date(dateString);
        if (isNaN(date)) return 'Invalid Date';

        const options = { year: 'numeric', month: '2-digit', day: '2-digit', timeZone };
        const formattedDate = new Intl.DateTimeFormat('en-US', options).format(date);
        const [month, day, year] = formattedDate.split('/');
        return `${year}-${month}-${day}`;
    };

    // Fetch all documents once
    const fetchLogs = useCallback(async () => {
        let startDate;
        let endDate;
        let displayStartDate;
        let queryString = `?page=${activePage}`;

        const today = new Date();
        switch (activeTab) {
            case 'today': {
                startDate = endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'yesterday': {
                const yesterdayDate = new Date(today);
                const yesterday = new Date(today);
                displayStartDate = yesterday.setDate(yesterday.getDate() - 1);
                yesterdayDate.setUTCDate(yesterdayDate.getUTCDate() - 1);
                startDate = endDate = formatYmdInTimeZone(yesterdayDate);
                break;
            }
            case 'last7days': {
                const last7Days = new Date(today);
                const last7 = new Date(today);
                displayStartDate = last7.setDate(last7.getDate() - 6);
                last7Days.setUTCDate(last7Days.getUTCDate() - 6);
                startDate = formatYmdInTimeZone(last7Days);
                endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'last30days': {
                const last30Days = new Date(today);
                const last30 = new Date(today);
                displayStartDate = last30.setDate(last30.getDate() - 29);
                last30Days.setUTCDate(last30Days.getUTCDate() - 29);
                startDate = formatYmdInTimeZone(last30Days);
                endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'All': {
                const last5Years = new Date(today);
                const last5 = new Date(today);
                displayStartDate = last5.setDate(last5.getUTCFullYear() - 5);
                last5Years.setUTCFullYear(last5Years.getUTCFullYear() - 5);
                startDate = formatYmdInTimeZone(last5Years);
                endDate = formatYmdInTimeZone(today);
                break;
            }
        }

        setStartDate(activeTab === 'today' ? formatDateForActiveTab(today) : activeTab === 'All' ? formatDateForActiveTab(UserRegistered) : formatDateForActiveTab(displayStartDate));
        if (!endDate) {
            setEndDate((activeTab === 'yesterday' ? '' : formatDateForActiveTab(today)));
        } else {
            setEndDate((activeTab === 'yesterday' ? '' : formatDateForActiveTab(today)));
        }

        try {
            if (startDate) {
                queryString += `&strStartdate=${startDate}`;
            }
            if (endDate) {
                queryString += `&strEnddate=${endDate}`;
            }
            if (itemsPerPage !== 'All') {
                queryString += `&per_page=${itemsPerPage}`;
            }

            // status filter based on activeFilter
            switch (activeFilter) {
                case 'Upload':
                    queryString += '&strSection=Upload';
                    break;
                case 'MyDocuments':
                    queryString += '&strSection=MyDocuments';
                    break;
                case 'MyModels':
                    queryString += '&strSection=MyModels';
                    break;
                case 'Billing':
                    queryString += '&strSection=Billing';
                    break;
                case 'Profile':
                    queryString += '&strSection=Profile';
                    break;
            }

            // search and sorting parameters to query string
            if (searchTerm && searchTerm.trim()) { queryString += `&strSearch=${encodeURIComponent(searchTerm.trim())}` }

            if (sorting.bSectionAsc !== null) {
                queryString += `&bSectionAsc=${sorting.bSectionAsc ? '1' : '0'}`;
            }
            if (sorting.bMessageAsc !== null) {
                queryString += `&bMessageAsc=${sorting.bMessageAsc ? '1' : '0'}`;
            }
            if (sorting.bLogDateTimeAsc !== null) {
                queryString += `&bLogDateTimeAsc=${sorting.bLogDateTimeAsc ? '1' : '0'}`;
            }

            const response = await axios.get(`${import.meta.env.VITE_SERVER}/Logs/all${queryString}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            setData(response.data.logs);
            setIsLoading(false);
            setTotalPages(response.data.total_pages);

            // Save filters to cookies
            setCookie('activeTabLog', activeTab, 1);
            setCookie('itemsPerPageLog', itemsPerPage, 1);
            setCookie('searchTerm', searchTerm, 1);
            setCookie('activePageLog', activePage, 1);
            setCookie('totalPagesLog', totalPages, 1);
            setCookie('activeFilterLog', activeFilter, 1);

        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Failed to fetch documents', error);
            setIsLoading(false);
        }
    }, [activePage, itemsPerPage, activeTab, sorting, totalPages, search.data, activeFilter]);

    const downloadData = () => {
        try {
            const flattenedData = flattenData(data);
            const csv = Papa.unparse(flattenedData);

            const blob = new Blob([csv], { type: 'text/csv;charset=utf-8;' });
            saveAs(blob, 'Logs-data.csv');
        } catch (error) {
            console.error('Error generating CSV', error);
        }
    };

    // Initialization using useEffect
    useEffect(() => {
        const decoded = jwtDecode(localStorage.getItem('token'));

        const fetchUserData = async () => {
            const userResponse = await fetchData(decoded.id)

            setUserRegistered(userResponse.created_at);
        }

        fetchUserData();

        const savedSearchTerm = getCookie('searchTerm');
        if (savedSearchTerm) {
            setSearchTerm(savedSearchTerm);
            setIsSearching(true);
        }

        const savedPage = getCookie('activePageLog') || 1;
        setActivePage(savedPage);

        const savedTotalPage = getCookie('totalPagesLog') || 0;
        setTotalPages(savedTotalPage);

        const savedItemsPerPage = getCookie('itemsPerPageLog') || 10;

        setItemsPerPage(savedItemsPerPage);

        if (data.length > 0) {
            // Reset activeTab and activeFilter
            setActiveTab('today');
        } else {
            const savedTab = getCookie('activeTabLog') || 'today';
            setActiveTab(savedTab);
            const savedFilter = getCookie('activeFilterLog') || 'all';
            setActiveFilter(savedFilter);
        }

    }, []);

    const toggleSort = (field) => {
        // Set the current field to the toggled value and others to null
        setSorting({
            bSectionAsc: field === 'bSectionAsc' ? !sorting.bSectionAsc : null,
            bMessageAsc: field === 'bMessageAsc' ? !sorting.bMessageAsc : null,
            bLogDateTimeAsc: field === 'bLogDateTimeAsc' ? !sorting.bLogDateTimeAsc : null,
        });
    };

    // Debounced search handler
    const debouncedSearch = useCallback(
        _.debounce((searchValue) => {
            setSearch((prevFilters) => {
                const newFilters = { ...prevFilters };
                if (searchValue?.trim() === '') {
                    delete newFilters.data; // Remove the search filter
                    setIsSearching(false)
                } else {
                    newFilters.data = searchValue; // Set the search filter
                }
                return newFilters;
            });

            setActivePage(1); // Reset to page 1 when performing a search
        }, 300), // 300 ms debounce delay
        [] // This tells React the debounce function doesn't depend on any props or state
    );

    // Add the following useEffect to listen to changes in searchTerm
    useEffect(() => {
        if (searchTerm?.trim() === '') {
            setSearch((prevFilters) => {
                const newFilters = { ...prevFilters };
                delete newFilters.data; // Remove the search filter
                return newFilters;
            });
        } else {
            setSearch((prevFilters) => {
                const newFilters = { ...prevFilters };
                newFilters.data = searchTerm; // Set the search filter
                return newFilters;
            });
        }
    }, [searchTerm]);

    useEffect(() => {
        // This will run when the component unmounts
        return () => {
            debouncedSearch.cancel();
        };
    }, []);

    const handleInputChange = (e) => {
        // Input change handler that sets the searchTerm and triggers the debounced search
        const value = e.target.value;
        setSearchTerm(value);
        setIsSearching(true);
        debouncedSearch(value);
    };

    // Effect that triggers a fetch when either activePage or filters change
    useEffect(() => {
        // Only call fetchData if isDone is true
        setIsLoading(true)
        fetchLogs();
        setTimeout(() => {
            setIsLoading(false)
        }, 1000)
        // }
    }, [fetchLogs]);

    const sortingIcon = (
        <svg width="8" height="10" viewBox="0 0 8 10" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d="M0.44873 6.32196L3.84547 9.55695L7.24222 6.32196L0.44873 6.32196Z" fill="#003654" />
            <path d="M0.44873 4.05762L3.84547 0.822623L7.24222 4.05762L0.44873 4.05762Z" fill="#003654" />
        </svg>
    )

    const BillingIcon = (
        <svg width="20" height="16" className="mr-1.5" viewBox="0 0 20 16" fill="none" xmlns="http://www.w3.org/2000/svg">
            <path d={BillingPath} fill={activeFilter === 'Billing' ? '#1C1C1E' : '#A6A6A6'} />
        </svg>
    )

    const SectionMap = {
        "Upload": <AiOutlineCloudUpload className="h-5 w-5 mr-1.5" />,
        "MyDocuments": <IoDocumentTextOutline className="h-5 w-5 mr-1.5" />,
        "MyModels": <MdOutlineBarChart className="h-5 w-5 mr-1.5" />,
        "Billing": BillingIcon,
        "Profile": <FaUser className="h-4 w-5 mr-1.5" />,
    }

    return (
        <>
            <LogsContent isOpen={modalIsOpen} onClose={closeModal} content={modalContent} />
            <Toaster position="top-center"></Toaster>
            <div style={{ overflow: 'hidden' }} className={`${showAlert ? 'max-h-[95vh]' : 'max-h-[98vh]'}`}>
                <Toaster position="top-center"></Toaster>
                <div className="flex px-6 py-2 pt-4">
                    <div className="flex-grow">
                        <h1 className="text-3xl font-semibold xl:text-2xl text-[#3F3F3F]">Activity Log</h1>
                    </div>
                </div>
                <div className={`pb-6 bg-[#ffff] mt-3 mx-6 ${showAlert ? 'max-h-[86vh]' : 'max-h-[96vh]'}  rounded-xl`}>
                    <div className="flex lg:flex-row items-center bg-[#f4f7fe] justify-between mb-6 space-y-3 lg:space-y-0 lg:space-x-3">

                        {/* Filter Section */}
                        <div className="flex flex-wrap space-x-3 xl:mt-1">
                            <button
                                onClick={() => { setActiveTab('today'); setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'today' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'today' && isSearching ? 'opacity-70' : 'opacity-100'} `}
                            >
                                Today
                            </button>
                            <button
                                onClick={() => { setActiveTab('yesterday'); setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'yesterday' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'yesterday' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                            >
                                Yesterday
                            </button>
                            <button
                                onClick={() => { setActiveTab('last7days'); setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'last7days' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'last7days' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                            >
                                Last 7 Days
                            </button>
                            <button
                                onClick={() => { setActiveTab('last30days'); setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'last30days' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'last30days' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                            >
                                Last 30 Days
                            </button>
                            <button
                                onClick={() => { setActiveTab('All'); setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'All' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'All' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                            >
                                ALL
                            </button>
                            <button
                                onClick={() => { setActivePage(1) }}
                                className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 bg-[#fff] text-[#373739] rounded-t-xl ${isSearching ? '' : 'hidden'}`}
                            >
                                Results
                            </button>
                        </div>

                        {/* Date Range Display */}
                        <div style={{
                            marginTop: '1.99rem',
                        }} className="text-[#373739] p-3 xl:mt-7 bg-[#fff] rounded-t-xl sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem]">
                            {startDate === endDate || !endDate ? startDate : `${startDate} - ${endDate}`}
                        </div>

                        {/* Search bar container */}
                        <div className="flex flex-grow 2xl:max-w-[20rem] xl:px-3 xl:max-w-[160px] bg-[#fff] text-[#373739] items-center justify-center rounded-t-xl lg:mt-7 xl:p-1.5 2xl:p-0.5">
                            <div className="flex items-center justify-center py-1 mt-4">
                                <input
                                    type="text"
                                    className="2xl:p-2 xl:p-1 w-full bg-[#F5F5F5] xl:text-sm 2xl:text-md rounded-l-lg focus:outline-none focus:ring-0 hover:border-gray-300"
                                    placeholder="Search..."
                                    value={searchTerm}
                                    onChange={handleInputChange}
                                />
                                {searchTerm ? (
                                    <Tooltip content="Clear Results">
                                        <button
                                            className="2xl:p-[11px] xl:p-[5px] border border-gray-300 rounded-r-lg focus:outline-none hover:border-gray-300"
                                            onClick={() => {
                                                setSearchTerm('');
                                                setIsSearching(false);
                                            }}
                                        >
                                            <MdOutlineClear />
                                        </button>
                                    </Tooltip>
                                ) : (
                                    <Tooltip content="Search Results">
                                        <button
                                            className="2xl:p-[11px] xl:p-[5px] border border-gray-300 rounded-r-lg focus:outline-none hover:border-gray-300"
                                        >
                                            <FiSearch />
                                        </button>
                                    </Tooltip>
                                )}
                            </div>
                        </div>
                    </div>

                    <div className='ml-4 flex justify-between'>
                        <div style={{ display: 'flex', fontSize: '0.950rem' }} className='space-x-1 text-[#A6A6A6] xl:text-sm 2xl:text-[1rem]'>
                            <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'all' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => setActiveFilter('all')} style={{ fontSize: '0.950rem' }}>
                                <TbLogs className="h-5 w-5 mr-1.5" />
                                All
                            </div>
                            <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'Upload' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('Upload'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                                {SectionMap["Upload"]}
                                Upload Document
                            </div>
                            <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'MyDocuments' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('MyDocuments'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                                {SectionMap["MyDocuments"]}My Documents
                            </div>
                            <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'MyModels' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('MyModels'); setActivePage(1); }} style={{ fontSize: '0.950rem' }}>
                                {SectionMap["MyModels"]}
                                My Models
                            </div>

                            <div className={`flex cursor-pointer items-center text-sm px-2  ${activeFilter === 'Billing' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('Billing'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                                {SectionMap["Billing"]}
                                Billing
                            </div>
                            <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'Profile' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('Profile'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                                {SectionMap["Profile"]}
                                Profile
                            </div>
                        </div>

                        {/* Buttons container, not growing and aligning right */}
                        <div className=" flex-shrink-0 flex items-center space-x-4 mr-4">
                            <Tooltip content={"Click to download the CSV Data."} placement="bottom">
                                <button
                                    onClick={() => downloadData("CSV")}
                                    className={`bg-[#003654] hover:bg-[#002744] font-bold text-white py-2 px-3 rounded-2xl mt-3 flex items-center text-xs lg:text-sm xl:text-2xs 2xl:text-sm cursor-pointer`}
                                    style={{ fontSize: '0.950rem' }}
                                >
                                    <RiDownloadLine className="h-5 w-4 mr-1" />
                                    Request CSV
                                </button>
                            </Tooltip>
                        </div>
                    </div>

                    <>
                        <div className={`mt-4 relative mx-4 ${showAlert ? '2xl:max-h-[65vh] xl:max-h-[46vh]' : '2xl:max-h-[68vh]'}`}>
                            <div className="overflow-hidden rounded-lg">
                                <div className={`overflow-auto ${showAlert ? 'max-h-[60vh]' : 'max-h-[62vh]'}`}>
                                    <table className="min-w-full table-fixed">
                                        <thead className="sticky top-0 z-10 bg-[#FAFAFA]">
                                            <tr>
                                                <th className="px-2 py-2 w-[13rem]">
                                                    <div className="flex justify-start cursor-pointer" onClick={() => toggleSort('bLogDateTimeAsc')}>
                                                        <label className="text-[#003654] mr-2 font-normal cursor-pointer">Date</label>
                                                        <div className="mt-2">{sortingIcon}</div>
                                                    </div>
                                                </th>
                                                <th className="px-2 py-2">
                                                    <div className="flex justify-start cursor-pointer" onClick={() => toggleSort('bMessageAsc')}>
                                                        <label className="text-[#003654] mr-2 font-normal cursor-pointer">Event</label>
                                                        <div className="mt-2">{sortingIcon}</div>
                                                    </div>
                                                </th>
                                                <th className="px-2 py-2 w-[12rem] xl:w-[8rem]">
                                                    <div className="flex justify-start cursor-pointer" onClick={() => toggleSort('bSectionAsc')}>
                                                        <label className="text-[#003654] mr-2 font-normal cursor-pointer">Section</label>
                                                        <div className="mt-2">{sortingIcon}</div>
                                                    </div>
                                                </th>
                                                <th className="px-2 py-2 w-[8rem] xl:w-[8rem]">
                                                    <div className="flex justify-start cursor-pointer">
                                                        <label className="text-[#003654] mr-2 font-normal cursor-pointer">Review</label>
                                                    </div>
                                                </th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y">
                                            {isLoading ? (
                                                <tr>
                                                    <td colSpan="3">
                                                        <div className="flex justify-center items-center">
                                                            <img src={loading} className="max-h-full max-w-full" alt="Loading..." />
                                                        </div>
                                                    </td>
                                                </tr>
                                            ) : data.length === 0 ? (
                                                <tr>
                                                    <td colSpan="3">
                                                        <div className="flex justify-center items-center h-[40vh]">
                                                            <img src={`animations/noData.png`} className="max-h-full max-w-full" alt="No data available" />
                                                        </div>
                                                    </td>
                                                </tr>
                                            ) : (
                                                data.map((row, index) => (
                                                    <tr key={index} className={`border-b border-[#939393] ${index % 2 === 0 ? "bg-white" : "bg-[#F8F9FC]"}`}>
                                                        <td className="px-2 py-1 border-b border-[#939393]">
                                                            {formatDateEDT(row.LogDateTime)}
                                                        </td>
                                                        <td className="px-2 py-1 border-b border-[#939393] xl:text-xs 2xl:text-[1rem]  xl:max-w-[160px] 2xl:max-w-[40vw] cursor-pointer truncate"
                                                        onClick={() => openModal(row)}
                                                        >
                                                         {row.LogMessage} </td>
                                                        <td className="px-2 py-1 text-[#003654] border-b border-[#939393] xl:text-xs 2xl:text-[1rem]"><div className="flex items-center">
                                                            {SectionMap[row.Section]}
                                                            {row.Section}
                                                        </div></td>
                                                        <Tooltip content="Click to view the document contents.">
                                                        <td className="px-2 py-1 text-[#003654] border-b border-[#939393] xl:text-xs 2xl:text-[1rem]">
                                                            <MdRemoveRedEye
                                                                className={`mx-4 h-6 w-6 text-primary cursor-pointer`}
                                                                onClick={() => openModal(row)}
                                                            />
                                                        </td>
                                                        </Tooltip>
                                                    </tr>
                                                ))
                                            )}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>

                        <div className="flex justify-between items-center pt-4 sticky bottom-5 bg-white z-10 mt-5">
                            {/* Rows per Page Selector */}
                            <div className='mx-4'>
                                <ButtonGroup variant='outlined' size="sm">
                                    <Button onClick={() => { setItemsPerPage(10); setActivePage(1) }} className={`${itemsPerPage == 10 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                        10
                                    </Button>
                                    <Button onClick={() => { setItemsPerPage(20); setActivePage(1) }} className={`${itemsPerPage == 20 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                        20
                                    </Button>
                                    <Button onClick={() => { setItemsPerPage(50); setActivePage(1) }} className={`${itemsPerPage == 50 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                        50
                                    </Button>
                                    <Button onClick={() => { setItemsPerPage('All'); setActivePage(1) }} className={`${itemsPerPage === 'All' ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                        All
                                    </Button>
                                </ButtonGroup>
                            </div>

                            {/* Pagination Controls */}
                            <div className="flex items-center">
                                <Button
                                    variant="text"
                                    onClick={prevPage}
                                    disabled={activePage == 1}
                                    className="text-xs xl:text-xs"
                                >
                                    <IoIosArrowBack strokeWidth={2} className="h-4 w-4" />
                                </Button>

                                {totalPages <= 3 ? (
                                    Array.from({ length: totalPages }, (_, index) => (
                                        <button
                                            key={index}
                                            className={`px-3 py-1 ${activePage == index + 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-xs`}
                                            onClick={() => setActivePage(index + 1)}
                                        >
                                            {index + 1}
                                        </button>
                                    ))
                                ) : (
                                    <>
                                        <button
                                            className={`px-3 py-1 ${activePage == 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-xs`}
                                            onClick={() => setActivePage(1)}
                                        >
                                            1
                                        </button>
                                        {activePage > 3 && <span className="px-2 text-xs xl:text-sm">...</span>}
                                        {Array.from({ length: 3 }, (_, idx) => activePage - 1 + idx)
                                            .filter(page => page > 1 && page < totalPages)
                                            .map(page => (
                                                <button
                                                    key={page}
                                                    className={`px-3 py-1 ${activePage == page ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-sm`}
                                                    onClick={() => setActivePage(page)}
                                                >
                                                    {page}
                                                </button>
                                            ))
                                        }
                                        {activePage < totalPages - 2 && <span className="px-2 text-xs xl:text-sm">...</span>}
                                        <button
                                            className={`px-3 py-1 ${activePage == totalPages ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-sm`}
                                            onClick={() => setActivePage(totalPages)}
                                        >
                                            {totalPages}
                                        </button>
                                    </>
                                )}

                                <Button
                                    variant="text"
                                    onClick={nextPage}
                                    disabled={activePage == totalPages || totalPages == 0}
                                >
                                    <IoIosArrowForward strokeWidth={2} className="h-4 w-4" />
                                </Button>
                            </div>
                        </div>
                    </>
                </div>
            </div >
        </>
    );
}

ActivityLog.propTypes = {
    showAlert: PropTypes.bool.isRequired
};

export default ActivityLog;