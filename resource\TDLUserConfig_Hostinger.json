{"2": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "bankcustomerName": "Parag Traders", "customerName": "Parag Traders", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/ParagTraders", "userId": 2, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "company_name": "ParagTraders", "bank_name": "boi_1", "bVerifyBackup": false, "bUseGeneralizeGPTSchema": true, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "305A3A4B0C6A", "TallyUserName": "KANUNGO", "UserName": "<PERSON><PERSON><PERSON>"}, {"UUID": "704D7B70FB04", "TallyUserName": "KANUNGO", "UserName": "<PERSON><PERSON>"}, {"UUID": "PLEASE  GET", "TallyUserName": "KANUNGO", "UserName": "RAHUL"}]}, "8": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "AV Dev", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bUseGeneralizeGPTSchema": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": true, "UUID_DETAILS": [{"UUID": "08BFB81AE174", "TallyUserName": "HENIL", "UserName": "Developer"}], "company_name": "airen_construction", "bank_name": "boi_1"}, "5": {"senderList": ["<EMAIL>"], "attachmentsTypes": ["pdf", "xlsx"], "customerName": "ICD", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/ICD", "userId": 5, "backupDeviceName": "INDIA", "company_name": "ICD", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "7": {"senderList": [], "attachmentsTypes": ["pdf"], "bankcustomerName": "Gwalia", "customerName": "Gwalia", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/Gwalia", "userId": 7, "backupDeviceName": "INDIA", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bUseGeneralizeGPTSchema": false, "company_name": "gwalia", "bank_name": "BOB", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "9": {"senderList": ["<EMAIL>", "<EMAIL>"], "attachmentsTypes": ["pdf"], "customerName": "Prem Textiles", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/DeveloperTeam", "bUseGeneralizeGPTSchema": false, "userId": 9, "backupDeviceName": "INDIA", "company_name": "Prem Textiles", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "lsEmailCC": [], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": true, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "10": {"senderList": [], "attachmentsTypes": ["xls", "pdf", "xlsx"], "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/VedanshSchool", "userId": 10, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bankcustomerName": "NO NEED", "customerName": "Vedansh School", "company_name": "test_vis", "bank_name": "NO NEED", "bank_details": {"account_details": {"**************": "HDFC_2732", "**************": "HDFC_3648"}}, "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": true, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "4": {"senderList": [], "attachmentsTypes": ["pdf", "xlsx"], "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/DeveloperTeam", "userId": 4, "backupDeviceName": "INDIA", "lsEmailReceivers": [], "lsEmailCC": ["<EMAIL>"], "bank_details": {"account_details": {"**************": "HDFC_2732", "**************": "HDFC_3648"}}, "bUseGeneralizeGPTSchema": false, "bankcustomerName": "ICICI", "customerName": "<PERSON><PERSON><PERSON><PERSON>frabuild", "company_name": "test_abhinavInfra", "bank_name": "ICICI", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bSmartVendorDetectAlgo": false, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "11": {"senderList": [], "attachmentsTypes": ["xls", "pdf", "xlsx"], "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/AbhinavInfrabuild", "userId": 11, "backupDeviceName": "", "lsEmailReceivers": ["<EMAIL>", "<EMAIL>"], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bankcustomerName": "ICICI", "customerName": "<PERSON><PERSON><PERSON><PERSON>frabuild", "company_name": "test_abhinavInfra", "bank_name": "ICICI", "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bDownloadERPMappingFile": false, "bUseGeneralizeGPTSchema": true, "bSmartVendorDetectAlgo": false, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}]}, "12": {"senderList": [], "attachmentsTypes": ["pdf"], "bankcustomerName": "", "customerName": "Dhanuka Traders", "dataDirectory": "/home/<USER>/AV Ahemdabad Server/AI Data/DailyData/DhanukaTraders", "userId": 12, "backupDeviceName": "", "lsEmailReceivers": [], "lsEmailCC": ["<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>", "<EMAIL>"], "bVerifyBackup": false, "bAddToTally": true, "bVerifyWithPriceList": false, "MaxRetryCount": 3, "bLicenseValidationEnabled": false, "bUseGeneralizeGPTSchema": true, "bSmartVendorDetectAlgo": true, "iSplitPages": 1, "UUID_DETAILS": [{"UUID": "", "TallyUserName": "", "UserName": ""}], "company_details": {"company_tally_name": "Dhanuka Traders - (from 1-Apr-22) - (from 1-Apr-23) - (from 1-Apr-24)", "company_address": ["57, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>"], "company_gstin": "23ABPPD6898K1Z0", "company_state": "Madhya Pradesh", "gst_registration_type": "Unregistered/Consumer", "cmp_gst_registration_type": "Regular"}}}