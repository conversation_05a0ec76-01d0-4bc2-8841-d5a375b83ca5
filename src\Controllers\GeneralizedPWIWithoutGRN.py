"""
Generalized Purchase with Inventory Class for handling multiple vendors.
This class provides a unified interface for generating Tally XML for different vendors.
"""
import re
import pandas as pd
from datetime import datetime
from pathlib import Path
import traceback
import os
import json
from fuzzywuzzy import process, fuzz
import asyncio
from decimal import Decimal, ROUND_HALF_UP, ROUND_DOWN
from typing import Dict, List, Any, Optional, Union
import sys
sys.path.append(".")
from src.Schemas.Tally_XML_Schema import (
    BillAllocationSchema,
    CompanyInfoSchema,
    ConsigneeDetailsSchema,
    PartyDetailsSchema,
    InventoryEntrySchema,
    LedgerEntrySchema,
    BatchAllocationSchema,
    AccountingAllocationSchema,
    CategoryAllocationSchema,
    CostCenterAllocationSchema,
    RateDetailSchema,
    TallyPurchaseInventoryVoucherSchema,
    InvoiceDelNotesSchema,
    InvoiceOrderListSchema
)
from src.Controllers.GoogleDriveController import GoogleDriveService
from PWIMatchingOpenAI.matchRunner import C<PERSON>edgerMatcher, run_matcher_subprocess
from PWIMatchingOpenAI import helperFunctionsInAsync
from src.Controllers.Logs_Controller import CLogController
from src.utilities.helperFunc import CJSONFileReader, CExcelHelper, CDirectoryHelper, DateHelper 
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.Controllers.GrnNoPITrackingController import CGrnNoPITracking
from fastapi import HTTPException
from src.utilities.PurchaseOrderUtilities import CPOUtilities
from src.utilities.GRNUtilities import CGRNUtilities
from src.utilities.PathHandler import dictProjectPaths
import decimal


class CGeneralizePurchaseWithInventoryWithoutGRN:
    """
    Generalized class for handling purchase with inventory tasks across different vendors.
    This class provides static methods that can be used for all vendors.
    """

    OPENAIMODEL = "gpt-4.1"
    # PWI V3 Config
    SELLER_MAP_PATH = Path(r"SellerName.json")
    PYTHON_EXECUTABLE_PATH = Path(r"5_Env/Scripts/python.exe")
    WORKING_DIR = Path(r"PWIMatchingOpenAI")
    TDL_USER_CONFIG_PATH = Path(r"resource/TDLUserConfig.json")
    
    _mbUseFuzzyMatching = True     # NOTE: IF TRUE, FINDS THE MATHCING ITEM FROM TALLY DATA
        
    # I want to add flag that decides if the invoice purchased by coompany is sales or purchase like for abhinav user id 11 it is purchase and for dhanuka and userid 12 it is sales, so write me code that if that follows:
    # ISDEEMEDPOSITIVE == Yes (Debit) if the invoice is a purchase
    # ISDEEMEDPOSITIVE == No (Credit) if the invoice is a sales
        # AMOUNT == Negative Sign (- Amount)
        # nested tags

        # Sales Category then 
        # ISDEEMEDPOSITIVE == No  (Credit)
        # AMOUNT == Postive Sign (Amount)


    def __init__(self, iUserID, dictExtractedData = None, iDocId = None, strClientREQID=None, bDebug = False):
        self.iUserID = iUserID
        self.dictExtractedData = dictExtractedData
        self.iDocId = iDocId
        self.strClientREQID = strClientREQID
        # Tally Related Client Config
        self._mStrTallyCompanyName = None
        self._mStrClientName = None
        self._mDictVendorConfig = {}
        self._mGSTRegistrationType = "Regular"
        self._mGSTIN = None
        self._iTotalTaxAmout = 0

        # Class variables to track stock items
        self._mlsTotalStockItems = []       # Tracks all stock items from the invoice
        self._mlsNonExistingStockItems = [] # Tracks non-existing stock items
        self._mStrTracebackLogs = ""
        self._mAVTallyStatus = "Skipped"
        self._mLsAccuVelocityComments = []
        self._miSRNO = 0
        self.bDebug = bDebug
        self.strGRNLatestFile = None
        self.strPOLatestFile = None

    # Method to remove tags from a xml
    def MRemoveTags(self, strXML):
        """
        Remove all occurrences of specific tags from the XML string.
        
        Args:
            strXML (str): The XML string from which to remove tags.
        
        Returns:
            str: The XML string with the specified tags removed.
        """
        try:
            # List of tag names to remove
            tags_to_remove = [
                "REFERENCEDATE",  
                "REFERENCE",
                "GSTLEDGERSOURCE",
                "GSTOVRDNINELIGIBLEITC"
            ]
            for strTagName in tags_to_remove:
                strPattern = f"<{strTagName}.*?>.*?</{strTagName}>"
                strXML = re.sub(strPattern, "", strXML, flags=re.DOTALL)
            return strXML
        except Exception as objException:
            CLogController.MSWriteLog(None, "Error", objException)
            self._mStrTracebackLogs = "\nMRemoveTags:\n" + str(traceback.format_exc())
            raise HTTPException(status_code=500, detail="Error removing tags from XML")
   
    async def MSGenerteNarration(self):
        """
        Generate a narration string for the Tally XML based on the current date and time.
            Returns:
            - str: A formatted narration string.
        """
        try:
            narration = ""
            narration = "Processed By Accuvelocity on " + str(datetime.now().strftime("%d-%m-%Y %H:%M:%S")) + "\n"
            return narration
        except Exception as objException:
            await CLogController.MSWriteLog(None, "Error", objException)
            self._mStrTracebackLogs = "\nMSGetNarration:\n" + str(traceback.format_exc())  
            # raise
            self._mLsAccuVelocityComments.append("Please Add Narration Manually.")
            return ""        

    def MSetUserAttributes(self):
        
        if self.iUserID == 11:
            """Client Prerequisites:

                1. Ledger Configuration – "AV Select Ledger"

                    Group: Sundry Creditors

                    State: Madhya Pradesh

                    Country: India

                2. Stock Item Setup – "AV Select Item"

                    Category: Stock Item Creation

                    Under: Primary

                    Units: Nos

                    Taxability: Taxable

                    GST Rate: 18%

                    Type of Supply: Goods

                3. Voucher Type – "AV PURCHASE"

                    Base Type: Purchase (do not reuse existing Purchase voucher type)

                    Method of Voucher Numbering: Automatic with Manual Override

                    Settings: Allow voucher date editing and narration
            """
            self._mStrTallyCompanyName = "Abhinav Infrabuild Pvt.Ltd.(24-26)"
            self._mStrClientName = "abhinav"
            self._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/Abhinav InfraBuild/TallyLedgerConfig.json"))
            self._mGSTIN = "23AAHCA9425D1ZY"
            self._mGSTRegistrationType = "Regular"
            self._mStateName = "Madhya Pradesh"
            self._mCountryName = "India"
            self._mLsAddress = ["207-208 Industry House,A.B.Road Indore(M.P.)",
                "Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)",
                "E-Mail : <EMAIL>"
            ]
            self._mPinCode = ""
            self._mStrVoucherClassName = "CSLV"
            self._mStrDueDateDays = "45 Days"
            self._mStrPurchaseLedgerName = "Purchase - GST - Contract"
            self._mInvoiceDate = self.dictExtractedData.get("InvoiceDate", "") 
            self._mEffectiveDate = CBusinessIntelligence.MSRoundDateToNext5Days(self.dictExtractedData.get("InvoiceDate", ""))
            self.bIsVoucherDateSameAsInvoiceDate = False
        elif self.iUserID == 2:
            self._mStrTallyCompanyName = "PARAG TRADERS (24-25)"
            self._mStrClientName = "parag"
            self._mGSTIN = "23AAKFV4306N1ZX"
            self._mGSTRegistrationType = "Regular"
            self._mStateName = "Madhya Pradesh"
            self._mCountryName = "India"
            self._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/17_ParagTraders/TallyLedgerConfig.json"))
            self._mLsAddress = []
            self._mPinCode = ""
            self._mStrVoucherClassName = ""
            self._mStrDueDateDays = ""
            self._mStrPurchaseLedgerName = "GST INTERSTATE PURCHASE (18%)"
            self._mInvoiceDate = self.dictExtractedData.get("InvoiceDate", "")
            self._mEffectiveDate = self.dictExtractedData.get("InvoiceDate", "")
            self.bIsVoucherDateSameAsInvoiceDate = True

        elif self.iUserID == 7:
            self._mStrTallyCompanyName = "Gwalia Sweets Pvt Ltd - (24-25)"
            self._mStrClientName = "gwalia"
            
        elif self.iUserID == 12 or self.iUserID == 4:
            
            # Handling if client's TDL user data is not fetched             
            if not self.client_config :
                dictUsersConfig = CJSONFileReader.read_json_file(CGeneralizePurchaseWithInventoryWithoutGRN.TDL_USER_CONFIG_PATH)
                client_config = dictUsersConfig[str("12")]
            company_details = client_config.get("company_details", {})
            self._mStrTallyCompanyName = company_details.get("company_tally_name", "Dhanuka Traders - (from 1-Apr-22) - (from 1-Apr-23) - (from 1-Apr-24)")
            self._mStrClientName = "dhanuka"
            self._mGSTIN = company_details.get("company_gstin", "23ABPPD6898K1Z0")
            self._mGSTRegistrationType = company_details.get("gst_registration_type", "Unregistered/Consumer")
            self._mStateName = company_details.get("company_state", "Madhya Pradesh")
            self._mCountryName = "India"
            self._mDictVendorConfig = CJSONFileReader.read_json_file(Path(r"Data/Customer/Dhanuka/TallyLedgerConfig.json"))
            self._mLsAddress = company_details.get("company_address", ["57, Fawarra Chowk, Ujjain", ])
            self._mPinCode = ""
            self._mStrVoucherClassName = "BIS"
            self._mStrDueDateDays = ""
            self._mStrPurchaseLedgerName = "Sales 18%"
            self._mInvoiceDate = self.dictExtractedData.get("InvoiceDate", "")
            self._mEffectiveDate = self.dictExtractedData.get("InvoiceDate", "")
            self._mCMPGSTRegistrationType = company_details.get("cmp_gst_registration_type", "Regular")
            self.bIsVoucherDateSameAsInvoiceDate = True
            # Cost Center
            self._mStrCostCenterName = "Parle 7"
            self._mstrVoucherType = "AV Biscuits Sales"
            self._mGSTSourceType = "Stock Item"  # For Dhanuka Traders, Parle
            self._mGSTHSNName = "19059020"  # For Dhanuka Traders, Parle, Same for all stock items
            self._mVatDealerType = "Regular"  # For Dhanuka Traders, Parle, as per Tally 
        
        else:
            print("User ID not found in configuration : GeneralizePurchaseWithInventory")
        
        return {
            "strTallyCompanyName": self._mStrTallyCompanyName,
            "strClientName": self._mStrClientName,
            "dictTallyLedgerConfig": self._mDictVendorConfig
        }

    def MCreateCompanyInfo(self, dictVendorWisePartyInfo):
        # Validate required instance attributes
        missing_fields = []
        
        if not getattr(self, "_mStrTallyCompanyName", None):
            missing_fields.append("Tally Company Name (_mStrTallyCompanyName)")
        if not getattr(self, "_mGSTRegistrationType", None):
            missing_fields.append("GST Registration Type (_mGSTRegistrationType)")
        if not getattr(self, "_mGSTIN", None):
            missing_fields.append("GSTIN (_mGSTIN)")
        if not getattr(self, "_mStateName", None):
            missing_fields.append("State Name (_mStateName)")
        if not getattr(self, "_mCountryName", None):
            missing_fields.append("Country Name (_mCountryName)")

        if missing_fields:
            raise ValueError(f"[❌] Missing required company info fields: {', '.join(missing_fields)}")

        # All fields validated — proceed to create schema
        
        # As dhanuka has different GST Registration and CMP Registration we are using new variable
        if dictVendorWisePartyInfo:
            self.company = CompanyInfoSchema(
                company_name=dictVendorWisePartyInfo["party_name"],
                gst_registration_type=self._mGSTRegistrationType,
                gst_in=self._mGSTIN,
                state_name=self._mStateName,
                country_name=self._mCountryName,
                cmp_gst_registration_type=self._mCMPGSTRegistrationType
            )
            return self.company
        else:
            self.company = CompanyInfoSchema(
                company_name=self._mStrTallyCompanyName,
                gst_registration_type=self._mGSTRegistrationType,
                gst_in=self._mGSTIN,
                state_name=self._mStateName,
                country_name=self._mCountryName,
                cmp_gst_registration_type=self._mCMPGSTRegistrationType
            )
            

    def MCreateConsigneeInfo(self, dictVendorWisePartyInfo = None):
        # Validate required instance attributes
        missing_fields = []
        
        if not getattr(self, "_mStrTallyCompanyName", None):
            missing_fields.append("Tally Company Name (_mStrTallyCompanyName)")
        if not getattr(self, "_mGSTIN", None):
            missing_fields.append("GSTIN (_mGSTIN)")
        if not getattr(self, "_mStateName", None):
            missing_fields.append("State Name (_mStateName)")
        if not getattr(self, "_mCountryName", None):
            missing_fields.append("Country Name (_mCountryName)")
        if not getattr(self, "_mLsAddress", None):
            missing_fields.append("Address (_mLsAddress)")

        if missing_fields:
            raise ValueError(f"[❌] Missing required consignee info fields: {', '.join(missing_fields)}")
        
        # This code is only applicable to only dhanuka  as it is seller     
        if dictVendorWisePartyInfo:
            self.consignee = ConsigneeDetailsSchema(
            mailing_name=dictVendorWisePartyInfo['party_name'],
            address_list=dictVendorWisePartyInfo['address_list'],
            gst_in=self._mGSTIN,
            state_name=self._mStateName,
            country_name=self._mCountryName, 
            pin_code=self._mPinCode
        )
            return self.consignee

        # All fields validated — proceed to create schema
        self.consignee = ConsigneeDetailsSchema(
            mailing_name=self._mStrTallyCompanyName,
            address_list=self._mLsAddress,
            gst_in=self._mGSTIN,
            state_name=self._mStateName,
            country_name=self._mCountryName, 
            pin_code=self._mPinCode
        )
        return self.consignee

    # PURCHASE LEDGER
    def MCreatePartyInfo(self, dictVendorConfig: Dict):
        """
        Creates a PartyDetailsSchema instance from the given vendor config.
        """
        required_keys = [
            "party_name", "address_list", "gst_registration_type",
            "gst_in", "state_name", "country_name", "pin_code"
        ]

        missing = [key for key in required_keys if key not in dictVendorConfig]
        if missing:
            raise KeyError(f"[❌] Missing vendor config keys: {', '.join(missing)}")
        
        self.party = PartyDetailsSchema(
            party_name=dictVendorConfig["party_name"],
            address_list=dictVendorConfig["address_list"],
            gst_registration_type=dictVendorConfig["gst_registration_type"],
            gst_in=dictVendorConfig["gst_in"],
            state_name=dictVendorConfig["state_name"],
            country_name=dictVendorConfig["country_name"],
            pin_code=dictVendorConfig["pin_code"]
        )
        return self.party
    
    def MGetVendorConfig(self) -> Dict:
        """
        Retrieves the vendor configuration based on the extracted seller name.
        Validates existence in vendor config and handles normalization.
        """
        self.strVendorName = self.dictExtractedData.get("SellerDetails").get("SellerName", "").strip()

        if not self.strVendorName:
            raise ValueError("[❌] SellerName is missing in extracted data.")

        # Normalize vendor name
        matchKey = CGeneralizePurchaseWithInventoryWithoutGRN.MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = self._mDictVendorConfig, strInputText = self.strVendorName)

        if matchKey == " ":
            raise ValueError(
                f"[❌] Vendor '{self.strVendorName}' is not supported. Please contact the AccuVelocity team."
            )

        # Return original key's config
        return self._mDictVendorConfig[matchKey]
    
    async def MGetAllStockItemInfo(self, bUseFuzzy=False):
        """
        Get all stock item information for the invoice.

        Returns:
            list: List of InventoryEntrySchema objects
        """
        lsResult = []
        lsInventoryItemDetails = []
        try:
           
            if bUseFuzzy:
                # Create a temporary JSON file with the extracted data
                os.makedirs(Path("GitIgnore/GeneralizePWIV3/Temp"), exist_ok=True)
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                temp_json_path = os.path.join(Path("GitIgnore/GeneralizePWIV3/Temp"), f"temp_items_{timestamp}.json")

                # Save the item details to a temporary JSON file
                with open(temp_json_path, "w", encoding="utf-8") as f:
                    json.dump(self.dictExtractedData, f, indent=4)
                
                if self.bDebug:
                    print(f"Saved temporary item details to {temp_json_path}")
                    # Run the matcher subprocess and await its result
                    print("Running matcher subprocess...")

                await CLogController.MSWriteLog(self.iUserID, "Debug", f"Running matcher subprocess with JSON file: {temp_json_path}")
                lsResult = run_matcher_subprocess(
                    working_dir=CGeneralizePurchaseWithInventoryWithoutGRN.WORKING_DIR,
                    python_env_path=CGeneralizePurchaseWithInventoryWithoutGRN.PYTHON_EXECUTABLE_PATH,
                    json_path=temp_json_path,
                    company_name=self._mStrClientName,
                    seller_map_path=CGeneralizePurchaseWithInventoryWithoutGRN.SELLER_MAP_PATH,
                    bGlobalSearch = False # Note: Enable this to search globally in client stock item data
                )
                await CLogController.MSWriteLog(self.iUserID, "Debug", f"Matcher subprocess completed with {len(lsResult) if lsResult else 0} results")
                if self.bDebug:
                    print(f"Matcher subprocess completed with {len(lsResult) if lsResult else 0} results")

                # If no results, return empty list
                if not lsResult:
                    await CLogController.MSWriteLog(self.iUserID, "Debug", "No matching results found")
                    if self.bDebug:
                        print("No matching results found")
                    # lsResult = ["AV Select Item"] * len(self.dictExtractedData.get("ItemDetails", []))
                   
            for dictItemDetail  in lsResult:
                try:
                    # Use fuzzy matched name or direct item name
                    strStockItemName = dictItemDetail.get("bestMatch", "AV Select Item").strip()
                    if strStockItemName in ["None", "None."] or not strStockItemName:
                        strStockItemName = "AV Select Item"
                        self._mlsNonExistingStockItems.append("Item not found"+ f" : {dictItemDetail['itemName']}. Please add it manually in Tally.")

                    # Getting Item Details from dictExtractedData
                    item_name = dictItemDetail['itemName']
                    item_detail = next(
                    (item for item in self.dictExtractedData["ItemDetails"] if item.get("ItemName") == item_name),
                    None
                )
                    iItemAmount = float(item_detail.get("NetAmount", "0"))
                    
                    # Determine if invoice is purchase or sales, set amount sign and is_deemed_positive accordingly
                    is_deemed_positive, signed_amount = self.get_deemed_positive_and_amount(iItemAmount)
                    
                    # Batch Allocation
                    batch_alloc = BatchAllocationSchema(
                        godownname="Main Location",
                        batchname="Primary Batch",
                        amount=signed_amount,
                        actual_qty=str(item_detail.get("Quantity", "0")),
                        billed_qty=str(item_detail.get("Quantity", "0")),
                        order_no="Not Applicable",
                        tracking_no="Not Applicable"
                    )

                    # Accounting Allocation
                    accounting_alloc = AccountingAllocationSchema(
                        ledgername=self._mStrPurchaseLedgerName,
                        amount=signed_amount,
                        is_deemed_positive=is_deemed_positive,
                        category_allocations=[
                            CategoryAllocationSchema(
                                category="Primary Cost Category",
                                is_deemed_positive=is_deemed_positive,
                                cost_center_allocations=[
                                    CostCenterAllocationSchema(name=self._mStrCostCenterName, amount=signed_amount)
                                ]
                            )
                        ]
                    )

                    # GST Rate Details
                    lsRateDetails = [
                        RateDetailSchema(
                            gstrate_duty_head="CGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=9
                        ),
                        RateDetailSchema(
                            gstrate_duty_head="SGST/UTGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=9
                        ),
                        RateDetailSchema(
                            gstrate_duty_head="IGST",
                            gstrate_valuation_type="Based on Value",
                            gstrate=18
                        )
                    ]

                    # Final Inventory Entry
                    inv_entry = InventoryEntrySchema(
                        stockitemname=strStockItemName,
                        rate=str(item_detail.get("Rate", "0")),
                        amount=signed_amount,
                        actual_qty=str(item_detail.get("Quantity", "0")),
                        billed_qty=str(item_detail.get("Quantity", "0")),
                        batch_allocations=[batch_alloc],
                        hsnitemsource=strStockItemName,
                        gst_hsnname= self._mGSTHSNName,
                        is_deemed_positive = is_deemed_positive,
                        # For Dhanuka Traders, Parle
                        gstsourcetype = self._mGSTSourceType,
                        gstledgersource=self._mStrPurchaseLedgerName,
                        accounting_allocations=[accounting_alloc],
                        gst_ovrd_stored_nature=None,
                        description=strStockItemName,
                        rate_details=lsRateDetails
                    )

                    lsInventoryItemDetails.append(inv_entry)
                
                except Exception as e:
                    print(f"Error processing stock item {dictItemDetail} : {e}")
                    await CLogController.MSWriteLog(self.iUserID, "Error", f"Error processing stock item {dictItemDetail} : {e}")
                    self._mStrTracebackLogs += f"XML Error: Row Details : {dictItemDetail} \n" + traceback.format_exc()
                    self._miSRNO += 1
                    self._mLsAccuVelocityComments.append(f"{self._miSRNO}. Manually Punch In Item: {dictItemDetail.get('ItemName')}")
                    self._mlsNonExistingStockItems.append(dictItemDetail)

        except Exception as e:
            print("Error in matcher subprocess:", str(e))
            print("Traceback:", traceback.format_exc())
            await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in matcher subprocess: {str(e)}")
            await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            
            # TODO: 
            lsResult = ["AV Select Item"] * len(self.dictExtractedData.get("ItemDetails", []))
            # return lsInventoryItemDetails


        
        return lsInventoryItemDetails

    async def MGetTallyXMLWithoutGRN(self, lsUDFData=None, iDocId = None,bDownloadERP = False, client_config = None):
        """
        Generate Tally XML for the given vendor and extracted data.

        Returns:
            dict: Dictionary containing XML content and other information
        
            1. Unknown VendorName = AV Select Ledger
        """
        dictAPIResponse = {}
        bISDeveloperMode = True if self.iUserID is None or self.strClientREQID is None or self.iDocId is None else False
        try:
            # Set default UDF data if not provided
            if lsUDFData is None:
                lsUDFData = [{}]
            
            strNarration = ""
            lsinvoice_del_notes_schema = []
            lsinvoice_order_list_schema = []
            
            # Assigning values to TDL User
            if client_config:
                self.client_config = client_config
            else:
                self.client_config  = None

           
            # initialize user attributes
            self.MSetUserAttributes()
            
            # Use this to get basic buyer as dhanuka has basic byer name different 
            dictVendorWisePartyInfo = self.MGetVendorConfig()


            # Create company info
            if self.iUserID == 12 or self.iUserID == 4:
                self.MCreateCompanyInfo(dictVendorWisePartyInfo)
            else:
                self.MCreateCompanyInfo() 

            # Create party ledger
           
            self.MCreatePartyInfo(dictVendorConfig = dictVendorWisePartyInfo)

            # Create consignee details (same as company)
            if self.iUserID == 12 or self.iUserID == 4:
                self.MCreateConsigneeInfo(dictVendorWisePartyInfo)
            else:
                self.MCreateConsigneeInfo()
                       
            # Calculating the Tax Amount
            # self.MGetTaxAmount()
            
            # Get inventory items
            lsStockItemInfo = await self.MGetAllStockItemInfo(bUseFuzzy=CGeneralizePurchaseWithInventoryWithoutGRN._mbUseFuzzyMatching)
            if not lsStockItemInfo:
                self._mAVTallyStatus = "Skipped"
                dictAPIResponse = self.MCreateResponse(XMLData=None, strAVComments="Error – Tally XML: Unable to generate XML for all stock items due to an unknown error. Please enter the items manually.")
                if not bISDeveloperMode:
                    await CAVRequestDetail.MSUpdateRecord(
                        iUserId=self.iUserID,
                        strClientREQID=self.strClientREQID,
                        DocID=self.iDocId,
                        AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                        TracebackLogs=dictAPIResponse["TracebackLogs"],
                        strAccuVelocityComments=dictAPIResponse["AVComments"]
                    )
                return dictAPIResponse

            # Get total amount
            fTotalAmount = self.dictExtractedData.get("TotalAmount", 0)

            # Determine sign and is_deemed_positive for party ledger entry
            is_deemed_positive_party, signed_party_amount = self.get_deemed_positive_and_amount(fTotalAmount)
            
            
            # Reversing the sign for signed party amount as it will be always be opposite of the current company's Debit / Credit
            
            
            # Create party bill allocation
            partyBillAllocation = BillAllocationSchema(
                name="Biscuit/"+str(self.dictExtractedData.get("InvoiceNo", "")),
                billtype="New Ref",
                amount=-fTotalAmount
                # billcreditperiod="45 Days" # Static Value Specified By Abhinav Infra
            )
         
            is_deemed_positive_party, signed_party_amount = self.get_deemed_positive_and_amount(fTotalAmount)

            # Create party ledger entry
            party_ledger_entry = LedgerEntrySchema(
                ledger_name=self.party.party_name,
                amount=-fTotalAmount,
                is_deemed_positive=True,
                is_party_ledger=True,
                bill_allocation=partyBillAllocation
            )

            # Create tax ledger entries
            ledger_entries = [party_ledger_entry]

            # Add CGST entry if available
            if "Taxes" in self.dictExtractedData and "MainTaxes" in self.dictExtractedData["Taxes"] and self.dictExtractedData["Taxes"]["ApplicableGstTaxType"] == "CGST+SGST":
                
                # Fetch CGST amount from MainTaxes
                cgst_amount = next(
                    (tax.get("TaxAmount", 0) for tax in self.dictExtractedData["Taxes"]["MainTaxes"] if tax.get("TaxName") == "CGST"),
                    0
                )
                self._iTotalTaxAmout += cgst_amount
                
                # Fetch SGST amount from MainTaxes
                sgst_amount = next(
                    (tax.get("TaxAmount", 0) for tax in self.dictExtractedData["Taxes"]["MainTaxes"] if tax.get("TaxName") == "SGST"),
                    0
                )
                self._iTotalTaxAmout += sgst_amount
                
                # Apply purchase/sales logic for CGST
                is_deemed_positive_cgst, signed_cgst_amount = self.get_deemed_positive_and_amount(cgst_amount)
                cgst_entry = LedgerEntrySchema(
                    ledger_name="CGST",
                    amount=abs(signed_cgst_amount),
                    is_deemed_positive=False,
                    is_party_ledger=False
                )
                ledger_entries.append(cgst_entry)

                # Apply correct sign and is_deemed_positive for SGST
                is_deemed_positive_sgst, signed_sgst_amount = self.get_deemed_positive_and_amount(sgst_amount)
                sgst_entry_obj = LedgerEntrySchema(
                    ledger_name="SGST",
                    amount=abs(signed_sgst_amount),
                    is_deemed_positive=False,
                    is_party_ledger=False
                )
                ledger_entries.append(sgst_entry_obj)
            elif "MainTaxes" in self.dictExtractedData["Taxes"] and self.dictExtractedData["Taxes"]["ApplicableGstTaxType"] == "IGST":
                igst_amount = next(
                    (tax.get("TaxAmount", 0) for tax in self.dictExtractedData["Taxes"]["MainTaxes"] if tax.get("TaxName") == "IGST"),
                    0
                )
                is_deemed_positive_igst, signed_igst_amount = self.get_deemed_positive_and_amount(igst_amount)
                igst_entry = LedgerEntrySchema(
                    ledger_name="IGST", # Client Tally Tax Ledger Name
                    amount=abs(signed_igst_amount),
                    is_deemed_positive=False,
                    is_party_ledger=False
                )
                ledger_entries.append(igst_entry)
            
            # Getting the roundoff amount
            roundoff_amount = (self.dictExtractedData.get("TotalAmount", 0) - self.dictExtractedData.get("SubTotal", 0) - self._iTotalTaxAmout)
            
            roundoff_entry = LedgerEntrySchema(
                ledger_name="Round Off",
                amount=-float(round(roundoff_amount,2)),  # Use float for serialization if needed
                is_deemed_positive=True,
                is_party_ledger=False,
                round_type= "Normal Rounding"
            )
            ledger_entries.append(roundoff_entry)

            # Generate Narration
            strNarration = await self.MSGenerteNarration()

            invoice_no = self.dictExtractedData.get("InvoiceNo", "")
            
            # Create voucher
            voucher = TallyPurchaseInventoryVoucherSchema(
                company_info=self.company,
                voucher_class=self._mStrVoucherClassName, # Client Specific Voucher Class Name
                party_details=self.party,
                consignee_details=self.consignee,
                voucher_number=invoice_no,
                invoice_date=self._mInvoiceDate,
                voucher_type=self._mstrVoucherType, 
                ledger_entries=ledger_entries,
                inventory_entries=lsStockItemInfo,
                udf_data=lsUDFData,
                strCompGSTIN = self._mGSTIN,
                basicduedateofpymt = self._mStrDueDateDays,
                bIsVoucherDateSameAsInvoiceDate = self.bIsVoucherDateSameAsInvoiceDate,
                narration = strNarration,
                invoice_del_notes_schema = lsinvoice_del_notes_schema,
                invoice_order_list_schema = lsinvoice_order_list_schema,
                cost_center_name=self._mStrCostCenterName,
                vat_dealer_type = self._mVatDealerType
            )

            # Generate XML content
            strXMLContent = voucher.to_string(pretty=True)
            strXMLContent = self.MRemoveTags(strXMLContent)

            # Save XML content to GitIgnore directory
            if self.bDebug:
                try:
                    os.makedirs(Path("GitIgnore/GeneralizePWIV3/XML"), exist_ok=True)
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
                    xml_file_path = Path(f"GitIgnore/GeneralizePWIV3/XML/xml_output_{timestamp}.xml")
                    with open(xml_file_path, "w", encoding="utf-8") as xml_file:
                        xml_file.write(strXMLContent)
                    print(f"XML content saved to {xml_file_path}")
                except Exception as e:
                    if self.bDebug:
                        print(f"Error saving XML content: {str(e)}")
                    await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in Saving XML: {str(e)}")
                    await CLogController.MSWriteLog(self.iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                    raise

            if len(self._mlsNonExistingStockItems) > 0:
                self._mAVTallyStatus = "PartialSuccess"
                strAVComments = "PartialSuccess - Tally XML: ".join(self._mLsAccuVelocityComments)
                dictAPIResponse = self.MCreateResponse(XMLData=strXMLContent, strAVComments=strAVComments)
            else:
                self._mAVTallyStatus = "Success"
                if self._mLsAccuVelocityComments:
                    strAVComments = "NOTE - Tally XML: " + " | ".join(self._mLsAccuVelocityComments)
                else:
                    strAVComments = "-"
                dictAPIResponse = self.MCreateResponse(XMLData=strXMLContent, strAVComments=strAVComments)
            
        except Exception as e:
            # Check if the exception has a 'detail' attribute and starts with 'ValidationError'
            if hasattr(e, 'detail') and isinstance(e.detail, str) and e.detail.startswith("ValidationError"):
                self._mAVTallyStatus = "ValidationError"
                strAVComments = e.detail
            elif str(e).startswith("ValidationError"):
                self._mAVTallyStatus = "ValidationError"
                strAVComments = str(e)
            else:
                self._mAVTallyStatus = "Skipped"
                strAVComments = "Error Tally XML: The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
            # Handle exceptions
            # strErrorMsg = f"Error in MGetTallyXML: {str(e)}\n{traceback.format_exc()}"
            self._mStrTracebackLogs += "MGetTallyXML: " + traceback.format_exc()
            if self.bDebug:
                print("Error Occur while Creating XML File: ",str(traceback.format_exc()))
            
            await CLogController.MSWriteLog(self.iUserID, "Error", f"Error in MGetTallyXML: {str(e)}")
            dictAPIResponse = self.MCreateResponse(strAVComments=strAVComments)
        
        if not bISDeveloperMode:
            if dictAPIResponse["TallyStatus"] in ["Success","PartialSuccess"]:

                strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name="Unknown", no_of_stock_items=len(self._mlsTotalStockItems) - len(self._mlsNonExistingStockItems), strVoucherType="PV_WITH_INVENTORY")
            else:
                strTimeSaved = "NOT_APPLICABLE"
                
            await CAVRequestDetail.MSUpdateRecord(
                iUserId=self.iUserID,
                strClientREQID=self.strClientREQID,
                DocID=self.iDocId,
                AVXMLGeneratedStatus=dictAPIResponse["TallyStatus"],
                TracebackLogs=self._mStrTracebackLogs,
                strAccuVelocityComments=dictAPIResponse["AVComments"], 
                EstAccountantTimeSaved = strTimeSaved
            )
        return dictAPIResponse

    
    
    def MCreateResponse(self, XMLData = None, strAVComments=None):
        
        return {
            "xmlContent": XMLData,
            "allStockItems": self._mlsTotalStockItems,
            "nonExistingStockItems": self._mlsNonExistingStockItems,
            "TallyStatus": self._mAVTallyStatus,
            "AVComments": strAVComments,
            "TracebackLogs": self._mStrTracebackLogs
        }
    
    @staticmethod
    def MSFindNearestMatch(strLedgerConfigFile = None, dictLedgerConfig = {}, strInputText = "shyam traders"):
        try:
            # Open the JSON file and load the data
            if strLedgerConfigFile is not None:
                with open(strLedgerConfigFile, "r") as file:
                    dictLedgerConfig = json.load(file)
            
            inp = strInputText.lower().strip()
            
            if not inp:
                return " "
            else:
                # Naive search: Simple substring search
                lsNaiveSearch = []
                for key in dictLedgerConfig.keys():
                    if inp in key.lower():
                        lsNaiveSearch.append(key)
                print("Naive Search Results:", lsNaiveSearch)
            
                # Fuzzy search using fuzzywuzzy with token_sort_ratio for better matching
                lsFuzzySearch = process.extract(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                threshold = 70
                filtered_fuzzy_results = [match for match in lsFuzzySearch if match[1] >= threshold]
                print("Filtered Fuzzy Search Results:", filtered_fuzzy_results)
            
                # Best match using extractOne
                best_match = process.extractOne(inp, dictLedgerConfig.keys(), scorer=fuzz.token_sort_ratio)
                print("Best Fuzzy Match:", best_match[0])
                return best_match[0]
        except Exception as e:
            return " "

    @staticmethod
    def MSSaveXMLToGitIgnore(dictResponse, strPrefix="xml_output"):
        """
        Save XML content to GitIgnore directory.

        Args:
            dictResponse: Dictionary containing XML content
            strPrefix: Prefix for the filename (default: "xml_output")

        Returns:
            str: Path to the saved file
        """
        try:
            # Create GitIgnore directory if it doesn't exist
            os.makedirs(Path("GitIgnore/Abhinav Infra/XML"), exist_ok=True)

            # Generate a timestamp for the filename
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Save the XML content to a file in the GitIgnore directory
            if dictResponse and dictResponse.get("xmlContent"):
                xml_file_path = Path(f"GitIgnore/Abhinav Infra/XML/{strPrefix}_{timestamp}.xml")
                with open(xml_file_path, "w", encoding="utf-8") as xml_file:
                    xml_file.write(dictResponse["xmlContent"])
                print(f"XML content saved to {xml_file_path}")
                return xml_file_path
            else:
                error_file_path = Path(f"GitIgnore/Abhinav Infra/XML/{strPrefix}_error_{timestamp}.txt")
                with open(error_file_path, "w", encoding="utf-8") as error_file:
                    error_file.write(f"Error: {dictResponse.get('ErrorMsg', 'Unknown error')}")
                print(f"Error occurred. Details saved to {error_file_path}")
                return error_file_path
        except Exception as e:
            print(f"Error saving XML to GitIgnore: {str(e)}")
            return None

    def is_sales_invoice(self):
        """
        Returns True if the invoice is a sales invoice, else False (purchase).
        UserID 12 and 4 are sales, 11 is purchase.
        """
        return self.iUserID in [12, 4]

    def get_deemed_positive_and_amount(self, amount):
        """
        Returns (is_deemed_positive, signed_amount) based on invoice type.
        For purchase: is_deemed_positive=True, amount negative.
        For sales: is_deemed_positive=False, amount positive.
        """
        if self.is_sales_invoice():
            return False, abs(amount)
        else:
            return True, -abs(amount)


if __name__ == "__main__":
    import asyncio
    import json
    import os
    from datetime import datetime

    # Create GitIgnore directory if it doesn't exist
    os.makedirs("GitIgnore\\Abhinav Infra", exist_ok=True)

    # Load the JSON data
    with open(r"\\192.168.1.15\user_data\MITUL\Documents\Nisarg\Grok Response Format and System Prompt for PWI\Grok Extracted Data\bills-23_gptResponse_GPT_content.json") as f:
        dictExtractedData = json.load(f)

    # # Call the method to get the XML content
    # dictResponse = asyncio.run(CGeneralizePurchaseWithInventoryWithoutGRN.MGetTallyXML(
    #     iUserId=4,
    #     dictExtractedData=dictExtractedData,
    #     strVendorName="ASHKELON ENTERPRISES",
    #     lsUDFData=None,
    #     strClientREQID=None,
    #     iDocId=None,
    #     strCustomerName="Abhinav InfraBuild"
    # ))

    objGeneralizePWIV3 = CGeneralizePurchaseWithInventoryWithoutGRN(iUserID  = 4, dictExtractedData = dictExtractedData, iDocId = None, strClientREQID = None, bDebug=True)
    dictTallyResponse = asyncio.run( objGeneralizePWIV3.MGetTallyXMLWithoutGRN(lsUDFData=None, iDocId = 2))
    XMLData = dictTallyResponse.get("xmlContent")
    CGeneralizePurchaseWithInventoryWithoutGRN.MSSaveXMLToGitIgnore(dictTallyResponse, "test_output")
    # print(CGeneralizePurchaseWithInventoryWithoutGRN.MSReadPOFile())
    
    
    
    ''' Current Issues:
    1. Basic Buyer Details is not dhanuka traders, but the client name.
    2. 
    '''