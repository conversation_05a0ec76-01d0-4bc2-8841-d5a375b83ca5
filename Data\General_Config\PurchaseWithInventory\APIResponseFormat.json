{"type": "json_schema", "json_schema": {"name": "generalv3", "schema": {"type": "object", "properties": {"InvoiceNo": {"type": "string", "description": "The unique identifier assigned to the invoice by the seller or service provider. This is typically a numerical or alphanumeric code."}, "InvoiceDate": {"type": "string", "description": "The date when the invoice was generated and issued by the seller, in the format 'YYYYMMDD'."}, "InvoiceTime": {"type": "string", "description": "The exact time when the invoice was generated, in the format 'HHMMSS'."}, "SellerDetails": {"type": "object", "description": "The Details of Seller / Vendor / Service provider / Bill from", "properties": {"SellerName": {"type": "string", "description": "The full name of the seller or service provider."}, "SellerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the seller or service provider, if listed on the invoice."}, "SellerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the seller, if mentioned."}, "SellerEntityBasedOnPanOrGst": {"type": "string", "description": "Classification of seller entity based on the fourth character of the PAN or the sixth character of the GST. Possible values: Association of Persons (AOP), Body of Individuals (BOI), Company, Limited Liability Partnership (LLP), Firm (Partnership), Government, nexionu Undivided Family (HUF), Artificial Juridical Person, Local Authority, Individual, Trust.", "enum": ["Association of Persons (AOP)", "Body of Individuals (BOI)", "Company", "Limited Liability Partnership (LLP)", "Firm (Partnership)", "Government", "aquantu Undivided Family (HUF)", "Artificial Juridical Person", "Local Authority", "Individual", "Trust"]}, "SellerContactNumber": {"type": "string", "description": "The contact phone number of the seller, if available."}, "SellerEmail": {"type": "string", "description": "The email address of the seller, if provided."}, "SellerAddress": {"type": "string", "description": "The full physical address of the seller, if mentioned."}, "SellerState": {"type": "string", "description": "State of seller", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "additionalProperties": false, "required": ["<PERSON><PERSON><PERSON><PERSON>", "SellerGST", "SellerPAN", "SellerEntityBasedOnPanOrGst", "SellerContactNumber", "Seller<PERSON>mail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerState"]}, "BuyersDetails": {"type": "object", "description": "The Details of Buyer / Purchaser / Service receiver / Bill to", "properties": {"BuyerName": {"type": "string", "description": "The full legal name of the buyer or service recipient as stated on the invoice."}, "BuyerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the buyer, if mentioned."}, "BuyerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the buyer, if mentioned."}, "BuyerEntityBasedOnPanOrGst": {"type": "string", "description": "Classification of buyer entity based on the fourth character of the PAN or the sixth character of the GST. Possible values: Association of Persons (AOP), Body of Individuals (BOI), Company, Limited Liability Partnership (LLP), Firm (Partnership), Government, aquantu Undivided Family (HUF), Artificial Juridical Person, Local Authority, Individual, Trust.", "enum": ["Association of Persons (AOP)", "Body of Individuals (BOI)", "Company", "Limited Liability Partnership (LLP)", "Firm (Partnership)", "Government", "aquantu Undivided Family (HUF)", "Artificial Juridical Person", "Local Authority", "Individual", "Trust"]}, "BuyerContactNumber": {"type": "string", "description": "The contact phone number of the buyer, if mentioned."}, "BuyerEmail": {"type": "string", "description": "The email address of the buyer listed on the invoice, if mentioned."}, "BuyerAddress": {"type": "string", "description": "The full physical address of the buyer, if mentioned."}, "BuyerState": {"type": "string", "description": "State of buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "additionalProperties": false, "required": ["BuyerName", "BuyerGST", "BuyerPAN", "BuyerEntityBasedOnPanOrGst", "BuyerContactNumber", "BuyerEmail", "BuyerAddress", "BuyerState"]}, "ItemDetails": {"type": "array", "description": "List of items purchased in the invoice.", "items": {"type": "object", "properties": {"ItemName": {"type": "string", "description": "Name of the purchased item."}, "ItemCode": {"type": "string", "description": "Unique identifier or SKU of the item, if present"}, "HSNCode": {"type": "string", "description": "HSN code for tax classification, if present"}, "Size": {"type": "string", "description": "The size of the purchased item, which could represent dimensions such as small, medium, large, or specific measurements like 10x12 inches, or based on a predefined size scale (e.g., S, M, L, XL), if present"}, "Quantity": {"type": "number", "description": "Quantity of the item purchased."}, "Rate": {"type": "number", "description": "Rate per unit of the item."}, "AmountBeforeTaxesAndDiscounts": {"type": "number", "description": "Total amount for the item before taxes and discounts."}, "DiscountRate": {"type": "number", "description": "Discount rate percentage applied on the item, If multiple discount are present, combine them into a single consolidated rate, if present"}, "DiscountAmount": {"type": "number", "description": "Discount amount applied on the item, If multiple discount are present, combine them into a single consolidated value, if present"}, "Taxes": {"type": "object", "description": "Only include if present in item table", "properties": {"ApplicableGstTaxType": {"type": "string", "description": "Fetch buyer state. then fetch seller state. then if these both are same then 'CGST & SGST' applies otherwise 'IGST'. 'null' if item wise tax values are not present", "enum": ["CGST+SGST", "IGST", "null"]}, "MainTaxes": {"anyOf": [{"type": "array", "description": "The CGST/SGST rate and amount,  if supply of goods or service is within state, i.e. intrastate transactions, CGST/SGST applies. If transaction is intrastate but wrongly IGST is mentioned in input(User prompt) then convert to CGST+SGST. Also include exempted TaxableAmount values (With TaxRate '0' and TaxAmount '0'), if there are any, by cross checking Amount-1 with Amount-2 mismatch. Where Amount-1 is SubTotal - Discounts + Charges and Amount-2 is total CGST+SGST Taxable amount", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate", "enum": ["CGST", "SGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "CGST/SGST rate percentage", "enum": [0.0, 0.125, 1.5, 2.5, 3.0, 6.0, 9.0, 14.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount occurs for selected TaxName and TaxRate, Generally called as Taxable amount"}, "TaxAmount": {"type": "number", "description": "Calculate the total CGST/SGST amount for the given TaxName and TaxRate. If the Tax Amount is not provided, skip the calculation."}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}, {"type": "object", "description": "The IGST rate and amount,  if supply of goods or service is out of state, i.e. interstate transactions or If goods or services are supplied to an SEZ unit, IGST applies. If transaction is interstate but wrongly CGST+SGST is mentioned in input (User prompt) then convert to IGST", "properties": {"IGST": {"type": "array", "description": "Give only if transaction is in different state", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate", "enum": ["IGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "IGST rate percentage", "enum": [0.0, 0.25, 3.0, 5.0, 12.0, 18.0, 28.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount occurs for selected TaxName and TaxRate, Generally called as Taxable amount"}, "TaxAmount": {"type": "number", "description": "Total IGST amount for selected TaxName and TaxRate If the Tax Amount is not provided, skip the calculation."}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}}, "additionalProperties": false, "required": ["IGST"]}, {"type": "object", "description": "Reply with this if line item wise GST details are not present", "properties": {"NolineWiseGSTDetails": {"type": "string", "description": "Give only if transaction is in different state", "enum": ["NoLineWiseGST", "null"]}}, "additionalProperties": false, "required": ["NolineWiseGSTDetails"]}]}, "OtherTaxes": {"type": "object", "description": "Taxes other than CGST, SGST, IGST", "properties": {"TaxName": {"type": "string", "description": "Name of tax"}, "TaxRate": {"type": "number", "description": "IGST rate percentage"}, "TaxAmount": {"type": "number", "description": "Amount"}}, "required": ["TaxName", "TaxRate", "TaxAmount"], "additionalProperties": false}}, "additionalProperties": false, "required": ["ApplicableGstTaxType", "MainTaxes", "OtherTaxes"]}, "NetAmount": {"type": "number", "description": "Net amount of item after applying taxes and discounts."}, "FreightAmount": {"type": "number", "description": "Freight charge applied on the item, if present"}, "InsuranceAmount": {"type": "number", "description": "Insurance charge applied to the item, if present"}, "PackingAndForwardingAmount": {"type": "number", "description": "Packing and forwarding charge applied to the item, if present"}, "Unit": {"type": "string", "description": "Unit of measurement for the item (e.g., box, kg, unit, meter)."}, "Grade": {"type": "string", "description": "Grade of the item, if applicable (e.g., A, B, C), if present"}, "Material": {"type": "string", "description": "Material type or number associated with the item, if present"}, "Brand": {"type": "string", "description": "Brand name of the item, if present"}, "ArticleNo": {"type": "string", "description": "Article number or product code for the item, if present"}, "Category": {"type": "string", "description": "Category of the item (e.g., Electronics, Furniture), if present"}, "DesignName": {"type": "string", "description": "Design name or reference for the item, if present"}, "OtherColumns": {"type": "array", "description": "A list of dynamic extra columns as key-value pairs. Each object should capture a column name that is not defined above and its respective value.", "items": {"type": "object", "properties": {"ColumnName": {"type": "string", "description": "The name of the extra column."}, "ColumnValue": {"description": "The value for the extra column. Its type can vary depending on the data provided.", "type": ["string", "number", "boolean", "null"]}}, "required": ["ColumnName", "ColumnValue"], "additionalProperties": false}}}, "required": ["ItemName", "ItemCode", "HSNCode", "Size", "Quantity", "Rate", "AmountBeforeTaxesAndDiscounts", "DiscountRate", "DiscountAmount", "Taxes", "NetAmount", "FreightAmount", "InsuranceAmount", "PackingAndForwardingAmount", "Unit", "Grade", "Material", "Brand", "ArticleNo", "Category", "DesignName", "OtherColumns"], "additionalProperties": false}}, "SubTotal": {"type": "number", "description": "The subtotal of the invoice before any taxes, discounts, or additional charges, if specified. If not mentioned, this should be set to '0'."}, "Discounts": {"type": "array", "description": "All types of discounts applied on subtotal amount, not on separate items. Reply with applicable discount only.", "items": {"type": "object", "properties": {"DiscountName": {"type": "string", "description": "Name of discount for selected DiscountRate and DiscountAmount", "enum": ["Trade Discount", "Cash Discount", "Quantity Discount", "Seasonal Discount", "Promotional Discount", "<PERSON>yal<PERSON> Discount", "Early/Advance Payment Discount", "Special Product Discount", "Display Discount", "Employee Discount", "Festival Discount", "Digital Payment Discount", "Bundle/Combo Discount", "Clearance Discount", "Other Discount"]}, "DiscountRate": {"type": "number", "description": "Discount rate percentage, if provided."}, "DiscountAmount": {"type": "number", "description": "Total discount (without minus sign) amount for selected discount name and percentage."}}, "required": ["DiscountName", "DiscountRate", "DiscountAmount"], "additionalProperties": false}}, "Charges": {"type": "array", "description": "Any additional charges or fees or costs applied on subtotal amount. Reply with applicable charges only.", "items": {"type": "object", "properties": {"ChargeName": {"type": "string", "description": "Name of charge for selected ChargeRate and ChargeAmount", "enum": ["Service Charge", "Handling Charge", "Packaging Charge", "Freight/Transportation Charge", "Delivery/Shipping Charge", "Insurance Fee", "Installation Charge", "Maintenance Charge", "Labor/Installation Charge", "Convenience Charge", "Digital Payment Surcharge", "Bank Charges", "GST (Goods and Services Tax)", "Cess/Surcharge", "Regulatory/Statutory Charges", "Demurrage Charges", "Courier Charges", "Other Charges"]}, "ChargeRate": {"type": "number", "description": "Charge rate percentage, if provided."}, "ChargeAmount": {"type": "number", "description": "Total charge (without minus sign) amount for selected charge name and/or charge rate."}}, "required": ["ChargeName", "ChargeRate", "ChargeAmount"], "additionalProperties": false}}, "Taxes": {"type": "object", "description": "Must include either CGST+SGST or IGST in output.", "properties": {"ApplicableGstTaxType": {"type": "string", "description": "Fetch buyer state, then fetch seller state. If these both are same then 'CGST+SGST' applies otherwise 'IGST'. Strictly follow this.", "enum": ["CGST+SGST", "IGST"]}, "ApplicableGstTaxTypeReason": {"type": "string", "description": "Reason for selecting ApplicableGstTaxType."}, "MainTaxes": {"anyOf": [{"type": "array", "description": "The CGST/SGST rate and amount if supply of goods or service is within state (i.e., intrastate transactions). If transaction is intrastate but wrongly IGST is mentioned in input then convert to CGST+SGST. Also include exempted TaxableAmount values (with TaxRate '0' and TaxAmount '0') if there is an Amount-1 and Amount-2 mismatch.", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate.", "enum": ["CGST", "SGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "CGST/SGST rate percentage.", "enum": [0.0, 0.125, 1.5, 2.5, 3.0, 6.0, 9.0, 14.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount is calculated for selected TaxName and TaxRate."}, "TaxAmount": {"type": "number", "description": "Total CGST/SGST amount for selected TaxName and TaxRate."}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}, {"type": "object", "description": "The IGST rate and amount if supply of goods or service is out-of-state (i.e., interstate transactions) or if goods or services are supplied to an SEZ unit. If transaction is interstate but wrongly CGST+SGST is mentioned in the input then convert to IGST.", "properties": {"IGST": {"type": "array", "description": "Provide only if transaction is in a different state.", "items": {"type": "object", "properties": {"TaxName": {"type": "string", "description": "Name of tax for selected TaxRate.", "enum": ["IGST", "Exempted"]}, "TaxRate": {"type": "number", "description": "IGST rate percentage.", "enum": [0.0, 0.25, 3.0, 5.0, 12.0, 18.0, 28.0]}, "TaxableAmount": {"type": "number", "description": "Amount on which TaxAmount is calculated for selected TaxName and TaxRate."}, "TaxAmount": {"type": "number", "description": "Total IGST amount for selected TaxName and TaxRate."}}, "required": ["TaxName", "TaxRate", "TaxableAmount", "TaxAmount"], "additionalProperties": false}}}, "additionalProperties": false, "required": ["IGST"]}]}, "OtherTaxes": {"type": "object", "description": "Taxes other than CGST, SGST, IGST.", "properties": {"TaxName": {"type": "string", "description": "Name of the tax."}, "TaxRate": {"type": "number", "description": "Tax rate percentage for other taxes."}, "TaxAmount": {"type": "number", "description": "Amount for other taxes."}}, "required": ["TaxName", "TaxRate", "TaxAmount"], "additionalProperties": false}}, "additionalProperties": false, "required": ["ApplicableGstTaxType", "ApplicableGstTaxTypeReason", "MainTaxes", "OtherTaxes"]}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the invoice total. If the adjustment is negative, a negative value should be provided. Note: if the amount appears with a trailing minus sign (e.g., '0.3-'), it should be interpreted as '-0.3'."}, "TotalAmount": {"type": "number", "description": "The total amount payable as per the invoice, including Discounts, Charges, Taxes, and RoundingOff adjustments."}}, "required": ["InvoiceNo", "InvoiceDate", "InvoiceTime", "SellerDetails", "BuyersDetails", "ItemDetails", "SubTotal", "Discounts", "Charges", "Taxes", "RoundingOff", "TotalAmount"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING. STRICTLY CROSS-<PERSON><PERSON><PERSON> ALL PROVIDED VALUES THAT FOLLOW THIS FORMULA: SubTotal - Discounts + Charges + Taxes (CGST/SGST/IGST/Other) + RoundingOff = TotalAmount."}}}