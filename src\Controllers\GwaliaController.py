# import sys
# sys.path.append("")
# import xml.etree.ElementTree as ET

# import os
# import json
# import asyncio

# from datetime import datetime
# from sqlalchemy import select
# import json
# import asyncio
# from fastapi import HTTPException
# from src.Models.models import DocExtractedData


# from sqlalchemy.future import select
# from config.db_config import AsyncSessionLocal
# # from src.Controllers.auth_controller import get_single_user_api_usage
# from src.Controllers.Logs_Controller import CLogController
# import os

# from openpyxl import Workbook, load_workbook
# from src.utilities.helperFunc import CEx<PERSON><PERSON><PERSON><PERSON>, DateHelper, CComparator



# class CGwalia:


#     @staticmethod
#     def MSCreatePWOInvXML(dictExtractedData, strVendorName):
        
#         pass


# class CKarnavati:
#     # TODO:  Ask mitul to update the response format for karnavati to return invoice number as string with prefix RIW/24019
#     # TODO: 
#     # TODO: 
#     # TODO:    
#     # TODO: 
    
#     _mStrCompanyName = "Gwalia Sweets Pvt Ltd - (24-25)"
#     _mStrBuyerName = "Gwalia Sweets Pvt Ltd (2021-22)"
#     _mLsBuyerAddress = [
#             "401-405 Fourth Floor",
#             "Sunrise Mall Near Mansi Circle",
#             "Vastrapur Ahmedabad",
#             "Fssi No. 10713026000232"
#         ]
#     _mStrPartyName = "Karnavati"
#     _mStrBuyerGST = "24AAACG5535F1ZY"
#     _mStrState = "Gujarat"
#     _mStrCostCenterName = "Sweet Factory"
    
#     @staticmethod
#     def MSGenerateNarration(lsLineItems):
#         strNarration = "VEGITABLES:\n"
#         try:
#             for iIndex, dictItemInfo in enumerate(lsLineItems):
#                 strItemName = str(dictItemInfo.get("Description", "")).strip()
#                 strQuantity = str(dictItemInfo.get("Qty", "")).strip()
#                 strItemRate = str(dictItemInfo.get("Rate", "")).strip()
#                 strItemAmount = str(dictItemInfo.get("Amount", "")).strip()
                
#                 strNarration += f"({iIndex+1}). {strItemName}  |  Rate: {strItemRate}  |  Quantity: {strQuantity}  |  Amount: {strItemAmount}\n"
                
#         except Exception as e:
#             print(f"Error in MSGenerateNarration: {str(e)}")
        
#         return strNarration
    
    
#     @staticmethod
#     def MSGenerateXML(dictExtractedData):
#         envelope = ET.Element("ENVELOPE")

#         # HEADER
#         header = ET.SubElement(envelope, "HEADER")
#         tally_request = ET.SubElement(header, "TALLYREQUEST")
#         tally_request.text = "Import Data"

#         # BODY
#         body = ET.SubElement(envelope, "BODY")
#         import_data = ET.SubElement(body, "IMPORTDATA")

#         # REQUESTDESC
#         request_desc = ET.SubElement(import_data, "REQUESTDESC")
#         report_name = ET.SubElement(request_desc, "REPORTNAME")
#         report_name.text = "Vouchers"

#         # STATICVARIABLES
#         static_variables = ET.SubElement(request_desc, "STATICVARIABLES")
#         sv_current_company = ET.SubElement(static_variables, "SVCURRENTCOMPANY")
#         sv_current_company.text = CKarnavati._mStrCompanyName

#         # REQUESTDATA
#         request_data = ET.SubElement(import_data, "REQUESTDATA")
        
#         # TALLYMESSAGE - VOUCHER
#         tally_message_voucher = ET.SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})
#         voucher = ET.SubElement(tally_message_voucher, "VOUCHER", {
#             "VCHTYPE": "Purchase (Credit)",
#             "ACTION": "Create",
#             "OBJVIEW": "Invoice Voucher View"
#         })

#         # BASICBUYERADDRESS
#         buyer_address = ET.SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
#         for address in CKarnavati._mLsBuyerAddress:
#             ET.SubElement(buyer_address, "BASICBUYERADDRESS").text = address
        
#         old_audit_entry_ids_list = ET.SubElement(voucher, "OLDAUDITENTRYIDS.LIST", {"TYPE":"Number"})
#         old_audit_entry_ids = ET.SubElement(old_audit_entry_ids_list, "OLDAUDITENTRYIDS")
#         old_audit_entry_ids.text = "-1"

#         # Separate line for each field instead of using a dictionary loop
#         iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate"))
#         iTallySupportedInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)
#         date = ET.SubElement(voucher, "DATE")
#         date.text = iTallySupportedInvoiceDate

#         reference_date = ET.SubElement(voucher, "REFERENCEDATE")
#         reference_date.text =  iTallySupportedInvoiceDate

#         vch_status_date = ET.SubElement(voucher, "VCHSTATUSDATE")
#         vch_status_date.text = iTallySupportedInvoiceDate

#         gst_registration_type = ET.SubElement(voucher, "GSTREGISTRATIONTYPE")
#         gst_registration_type.text = "Unregistered/Consumer"

#         state_name = ET.SubElement(voucher, "STATENAME")
#         state_name.text = CKarnavati._mStrState

#         narration = ET.SubElement(voucher, "NARRATION")
#         narration.text = CKarnavati.MSGenerateNarration(dictExtractedData["Table"])  

#         country_of_residence = ET.SubElement(voucher, "COUNTRYOFRESIDENCE")
#         country_of_residence.text = "India"

#         place_of_supply = ET.SubElement(voucher, "PLACEOFSUPPLY")
#         place_of_supply.text = CKarnavati._mStrState

#         party_name = ET.SubElement(voucher, "PARTYNAME")
#         party_name.text = CKarnavati._mStrPartyName
        
#         gst_registration = ET.SubElement(voucher, "GSTREGISTRATION", {"TAXTYPE":"GST", "TAXREGISTRATION":CKarnavati._mStrBuyerGST})
#         gst_registration.text = "Gujarat Registration"


#         cmp_gstin = ET.SubElement(voucher, "CMPGSTIN")
#         cmp_gstin.text = CKarnavati._mStrBuyerGST

#         voucher_type_name = ET.SubElement(voucher, "VOUCHERTYPENAME")
#         voucher_type_name.text = "Purchase (Credit)"

#         party_ledger_name = ET.SubElement(voucher, "PARTYLEDGERNAME")
#         party_ledger_name.text = CKarnavati._mStrPartyName

#         voucher_number = ET.SubElement(voucher, "VOUCHERNUMBER")
#         voucher_number.text = str(dictExtractedData.get("InvoiceNo"))  # TODO: Make sure to verify invoice number is in correct format, with Prefix RIW/

#         basic_buyer_name = ET.SubElement(voucher, "BASICBUYERNAME")
#         basic_buyer_name.text = CKarnavati._mStrBuyerName

#         cmp_gst_registration_type = ET.SubElement(voucher, "CMPGSTREGISTRATIONTYPE")
#         cmp_gst_registration_type.text = "Regular"

#         reference = ET.SubElement(voucher, "REFERENCE")
#         reference.text = str(dictExtractedData.get("InvoiceNo"))  # TODO: Make sure to verify invoice number is in correct format, with Prefix RIW/

#         party_mailing_name = ET.SubElement(voucher, "PARTYMAILINGNAME")
#         party_mailing_name.text = CKarnavati._mStrPartyName

#         consignee_gstin = ET.SubElement(voucher, "CONSIGNEEGSTIN")
#         consignee_gstin.text = CKarnavati._mStrBuyerGST

#         consignee_mailing_name = ET.SubElement(voucher, "CONSIGNEEMAILINGNAME")
#         consignee_mailing_name.text = CKarnavati._mStrBuyerName

#         consignee_state_name = ET.SubElement(voucher, "CONSIGNEESTATENAME")
#         consignee_state_name.text = CKarnavati._mStrState

#         cmp_gst_state = ET.SubElement(voucher, "CMPGSTSTATE")
#         cmp_gst_state.text = CKarnavati._mStrState

#         consignee_country_name = ET.SubElement(voucher, "CONSIGNEECOUNTRYNAME")
#         consignee_country_name.text = "India"

#         basic_base_party_name = ET.SubElement(voucher, "BASICBASEPARTYNAME")
#         basic_base_party_name.text = CKarnavati._mStrPartyName

#         numbering_style = ET.SubElement(voucher, "NUMBERINGSTYLE")
#         numbering_style.text = "Manual"

#         cst_form_issue_type = ET.SubElement(voucher, "CSTFORMISSUETYPE")
#         cst_form_issue_type.text = "Not Applicable"

#         cst_form_recv_type = ET.SubElement(voucher, "CSTFORMRECVTYPE")
#         cst_form_recv_type.text = "Not Applicable"

#         fbt_payment_type = ET.SubElement(voucher, "FBTPAYMENTTYPE")
#         fbt_payment_type.text = "Default"

#         persisted_view = ET.SubElement(voucher, "PERSISTEDVIEW")
#         persisted_view.text = "Invoice Voucher View"

#         vch_status_tax_adjustment = ET.SubElement(voucher, "VCHSTATUSTAXADJUSTMENT")
#         vch_status_tax_adjustment.text = "Default"

#         vch_status_voucher_type = ET.SubElement(voucher, "VCHSTATUSVOUCHERTYPE")
#         vch_status_voucher_type.text = "Purchase (Credit)"

#         vch_status_tax_unit = ET.SubElement(voucher, "VCHSTATUSTAXUNIT")
#         vch_status_tax_unit.text = "Gujarat Registration"

#         vch_gst_class = ET.SubElement(voucher, "VCHGSTCLASS")
#         vch_gst_class.text = "Not Applicable"

#         cost_centre_name = ET.SubElement(voucher, "COSTCENTRENAME")
#         cost_centre_name.text = CKarnavati._mStrCostCenterName

#         vch_entry_mode = ET.SubElement(voucher, "VCHENTRYMODE")
#         vch_entry_mode.text = "Item Invoice"

#         voucher_type_orig_name = ET.SubElement(voucher, "VOUCHERTYPEORIGNAME")
#         voucher_type_orig_name.text = "Purchase"

#         diff_actual_qty = ET.SubElement(voucher, "DIFFACTUALQTY")
#         diff_actual_qty.text = "Yes"

#         ismst_from_sync = ET.SubElement(voucher, "ISMSTFROMSYNC")
#         ismst_from_sync.text = "No"

#         is_deleted = ET.SubElement(voucher, "ISDELETED")
#         is_deleted.text = "No"

#         is_security_on_when_entered = ET.SubElement(voucher, "ISSECURITYONWHENENTERED")
#         is_security_on_when_entered.text = "No"

#         as_original = ET.SubElement(voucher, "ASORIGINAL")
#         as_original.text = "No"

#         audited = ET.SubElement(voucher, "AUDITED")
#         audited.text = "No"

#         is_common_party = ET.SubElement(voucher, "ISCOMMONPARTY")
#         is_common_party.text = "No"

#         for_job_costing = ET.SubElement(voucher, "FORJOBCOSTING")
#         for_job_costing.text = "No"

#         is_optional = ET.SubElement(voucher, "ISOPTIONAL")
#         is_optional.text = "No"

#         effective_date = ET.SubElement(voucher, "EFFECTIVEDATE")
#         effective_date.text = iTallySupportedInvoiceDate

#         use_for_excise = ET.SubElement(voucher, "USEFOREXCISE")
#         use_for_excise.text = "No"

#         is_for_job_work_in = ET.SubElement(voucher, "ISFORJOBWORKIN")
#         is_for_job_work_in.text = "No"

#         allow_consumption = ET.SubElement(voucher, "ALLOWCONSUMPTION")
#         allow_consumption.text = "No"

#         use_for_interest = ET.SubElement(voucher, "USEFORINTEREST")
#         use_for_interest.text = "No"

#         use_for_gain_loss = ET.SubElement(voucher, "USEFORGAINLOSS")
#         use_for_gain_loss.text = "No"

#         use_for_godown_transfer = ET.SubElement(voucher, "USEFORGODOWNTRANSFER")
#         use_for_godown_transfer.text = "No"

#         use_for_compound = ET.SubElement(voucher, "USEFORCOMPOUND")
#         use_for_compound.text = "No"

#         use_for_service_tax = ET.SubElement(voucher, "USEFORSERVICETAX")
#         use_for_service_tax.text = "No"

#         is_reverse_charge_applicable = ET.SubElement(voucher, "ISREVERSECHARGEAPPLICABLE")
#         is_reverse_charge_applicable.text = "No"

#         is_system = ET.SubElement(voucher, "ISSYSTEM")
#         is_system.text = "No"

#         is_invoice = ET.SubElement(voucher, "ISINVOICE")
#         is_invoice.text = "Yes"
        
#         is_fetched_only = ET.SubElement(voucher, "ISFETCHEDONLY")
#         is_fetched_only.text = "No"

#         is_gst_overridden = ET.SubElement(voucher, "ISGSTOVERRIDDEN")
#         is_gst_overridden.text = "No"

#         is_cancelled = ET.SubElement(voucher, "ISCANCELLED")
#         is_cancelled.text = "No"

#         is_on_hold = ET.SubElement(voucher, "ISONHOLD")
#         is_on_hold.text = "No"

#         is_summary = ET.SubElement(voucher, "ISSUMMARY")
#         is_summary.text = "No"

#         is_ecommerce_supply = ET.SubElement(voucher, "ISECOMMERCESUPPLY")
#         is_ecommerce_supply.text = "No"

#         is_boe_not_applicable = ET.SubElement(voucher, "ISBOENOTAPPLICABLE")
#         is_boe_not_applicable.text = "No"

#         is_gst_sec_seven_applicable = ET.SubElement(voucher, "ISGSTSECSEVENAPPLICABLE")
#         is_gst_sec_seven_applicable.text = "No"

#         ignore_einv_validation = ET.SubElement(voucher, "IGNOREEINVVALIDATION")
#         ignore_einv_validation.text = "No"

#         cmp_gst_is_other_territory_assessee = ET.SubElement(voucher, "CMPGSTISOTHTERRITORYASSESSEE")
#         cmp_gst_is_other_territory_assessee.text = "No"

#         party_gst_is_other_territory_assessee = ET.SubElement(voucher, "PARTYGSTISOTHTERRITORYASSESSEE")
#         party_gst_is_other_territory_assessee.text = "No"

#         irn_json_exported = ET.SubElement(voucher, "IRNJSONEXPORTED")
#         irn_json_exported.text = "No"

#         irn_cancelled = ET.SubElement(voucher, "IRNCANCELLED")
#         irn_cancelled.text = "No"

#         ignore_gst_conflict_in_mig = ET.SubElement(voucher, "IGNOREGSTCONFLICTINMIG")
#         ignore_gst_conflict_in_mig.text = "No"

#         is_op_bal_transaction = ET.SubElement(voucher, "ISOPBALTRANSACTION")
#         is_op_bal_transaction.text = "No"

#         ignore_gst_format_validation = ET.SubElement(voucher, "IGNOREGSTFORMATVALIDATION")
#         ignore_gst_format_validation.text = "No"

#         is_eligible_for_itc = ET.SubElement(voucher, "ISELIGIBLEFORITC")
#         is_eligible_for_itc.text = "No"

#         ignore_gst_optional_uncertain = ET.SubElement(voucher, "IGNOREGSTOPTIONALUNCERTAIN")
#         ignore_gst_optional_uncertain.text = "No"

#         update_summary_values = ET.SubElement(voucher, "UPDATESUMMARYVALUES")
#         update_summary_values.text = "No"

#         is_eway_bill_applicable = ET.SubElement(voucher, "ISEWAYBILLAPPLICABLE")
#         is_eway_bill_applicable.text = "No"

#         is_deleted_retained = ET.SubElement(voucher, "ISDELETEDRETAINED")
#         is_deleted_retained.text = "No"

#         is_null = ET.SubElement(voucher, "ISNULL")
#         is_null.text = "No"

#         is_excise_voucher = ET.SubElement(voucher, "ISEXCISEVOUCHER")
#         is_excise_voucher.text = "No"

#         excise_tax_override = ET.SubElement(voucher, "EXCISETAXOVERRIDE")
#         excise_tax_override.text = "No"

#         use_for_tax_unit_transfer = ET.SubElement(voucher, "USEFORTAXUNITTRANSFER")
#         use_for_tax_unit_transfer.text = "No"

#         is_exer1_no_po_overwrite = ET.SubElement(voucher, "ISEXER1NOPOVERWRITE")
#         is_exer1_no_po_overwrite.text = "No"

#         is_exf2_no_po_overwrite = ET.SubElement(voucher, "ISEXF2NOPOVERWRITE")
#         is_exf2_no_po_overwrite.text = "No"

#         is_exer3_no_po_overwrite = ET.SubElement(voucher, "ISEXER3NOPOVERWRITE")
#         is_exer3_no_po_overwrite.text = "No"

#         ignore_pos_validation = ET.SubElement(voucher, "IGNOREPOSVALIDATION")
#         ignore_pos_validation.text = "No"

#         # Ledger Entries List
#         dTotalAmount = CKarnavati.MSCalculateTotalAmount(dictExtractedData)
#         CKarnavati.MSCreateLedgerEntry( voucher, ledger_name="Karnavati", amount=dTotalAmount, is_party_ledger="Yes")
#         CKarnavati.MSCreateLedgerEntry( voucher, ledger_name="Purchase Vegetable GST Exempted", amount=-dTotalAmount, 
#                                         gst_taxability="Exempt", hsn_name="0713", gst_ledger_source="Purchase Vegetable GST Exempted",
#                                         is_deemed_positive="Yes", is_last_deemed_positive="Yes")

#         # Convert to string and print
#         xml_string = ET.tostring(envelope, encoding="utf-8").decode("utf-8")
        
#         with open("output.xml", "w") as f:
#             f.write(xml_string)
            
#         return xml_string


#     @staticmethod
#     def MSCalculateTotalAmount(dictExtractedData):
#         dExtractedTotalAmount = dictExtractedData.get("TotalAmount")
#         dCalculatedTotalAmount = 0
        
#         try:
#             for dicItemDetails in dictExtractedData["Table"]:
#                 dCalculatedTotalAmount += dicItemDetails.get("Amount", 0)  # For Calculating Total Amount
                
#             if not CComparator.MSAreNumbersClose(dExtractedTotalAmount, dCalculatedTotalAmount, allowed_variation=1):
#                 print(f"Extracted Total Amount '{dExtractedTotalAmount}' and Calculated Total Amount '{dCalculatedTotalAmount}' are Not Same.")
                
#         except Exception as e:
#             print("Failed to calculate total amount")
            
#         return dExtractedTotalAmount
    
#     @staticmethod
#     def MSCreateLedgerEntry(parent, ledger_name, amount, gst_class="Not Applicable", is_deemed_positive="No",
#                         is_party_ledger="No", category="Primary Cost Category", cost_centre="Sweet Factory",
#                         gst_taxability=None, hsn_name=None, gst_ledger_source=None, is_last_deemed_positive="No"):
#         """
#         Function to create a <LEDGERENTRIES.LIST> XML node dynamically.
        
#         :param parent: Parent XML node where the <LEDGERENTRIES.LIST> should be added
#         :param ledger_name: Name of the ledger
#         :param amount: Amount value for the ledger
#         :param gst_class: GST classification
#         :param is_deemed_positive: Whether the amount is deemed positive
#         :param is_party_ledger: Whether it is a party ledger
#         :param category: Cost category
#         :param cost_centre: Cost centre name
#         :param gst_taxability: GST Taxability, if applicable
#         :param hsn_name: HSN Name, if applicable
#         :param gst_ledger_source: GST Ledger Source, if applicable
#         :param is_last_deemed_positive: Last deemed positive flag
#         """
#         ledger_entry = ET.SubElement(parent, "LEDGERENTRIES.LIST")

#         old_audit_entry_ids_list = ET.SubElement(ledger_entry, "OLDAUDITENTRYIDS.LIST", {"TYPE": "Number"})
#         old_audit_entry_ids = ET.SubElement(old_audit_entry_ids_list, "OLDAUDITENTRYIDS")
#         old_audit_entry_ids.text = "-1"

#         ET.SubElement(ledger_entry, "LEDGERNAME").text = ledger_name
#         ET.SubElement(ledger_entry, "GSTCLASS").text = gst_class
#         ET.SubElement(ledger_entry, "ISDEEMEDPOSITIVE").text = is_deemed_positive
#         ET.SubElement(ledger_entry, "LEDGERFROMITEM").text = "No"
#         ET.SubElement(ledger_entry, "REMOVEZEROENTRIES").text = "No"
#         ET.SubElement(ledger_entry, "ISPARTYLEDGER").text = is_party_ledger
#         ET.SubElement(ledger_entry, "AMOUNT").text = str(amount)

#         # Category Allocations
#         category_allocations = ET.SubElement(ledger_entry, "CATEGORYALLOCATIONS.LIST")
#         ET.SubElement(category_allocations, "CATEGORY").text = category
#         ET.SubElement(category_allocations, "ISDEEMEDPOSITIVE").text = is_deemed_positive

#         cost_centre_allocations = ET.SubElement(category_allocations, "COSTCENTREALLOCATIONS.LIST")
#         ET.SubElement(cost_centre_allocations, "NAME").text = cost_centre
#         ET.SubElement(cost_centre_allocations, "AMOUNT").text = str(amount)

#         # Optional GST fields
#         if gst_taxability:
#             ET.SubElement(ledger_entry, "GSTOVRDNTAXABILITY").text = gst_taxability

#         if hsn_name:
#             ET.SubElement(ledger_entry, "GSTHSNNAME").text = hsn_name

#         if gst_ledger_source:
#             ET.SubElement(ledger_entry, "GSTLEDGERSOURCE").text = gst_ledger_source

#         ET.SubElement(ledger_entry, "ISLASTDEEMEDPOSITIVE").text = is_last_deemed_positive

#         return ledger_entry


# if __name__ == "__main__":
#     dictExtractedData = {
#                             "SellerName": "KARNAVATI AGRO PRODUCTS",
#                             "Sellerphone": "079-27505787",
#                             "SellerMobile": 9825303197,
#                             "SellerAddress": "C/o. Rajani Chatrabhuj Malpani, Ambawadi, Near Satyanarayan Society, Ramnagar, Sabarmati, Ahmedabad-5",
#                             "SellerCity": "Ahmedabad",
#                             "BuyerName": "GWALIA SWEETS PVT LTD.-NARODA",
#                             "BuyerAddress": "NARODA",
#                             "InvoiceNo": 24223,
#                             "InvoiceDate": 240125,
#                             "InvoiceTime": "12:36",
#                             "Table": [
#                                 {
#                                     "SlNo.": 1,
#                                     "Description": "BABYCORN",
#                                     "Qty.": 5.0,
#                                     "Rate": 155.0,
#                                     "Amount": 775.0
#                                 },
#                                 {
#                                     "SlNo.": 2,
#                                     "Description": "CELARY",
#                                     "Qty.": 2.0,
#                                     "Rate": 70.0,
#                                     "Amount": 140.0
#                                 },
#                                 {
#                                     "SlNo.": 3,
#                                     "Description": "BASIL",
#                                     "Qty.": 1.0,
#                                     "Rate": 120.0,
#                                     "Amount": 120.0
#                                 },
#                                 {
#                                     "SlNo.": 4,
#                                     "Description": "BROCOLI",
#                                     "Qty.": 20.0,
#                                     "Rate": 125.0,
#                                     "Amount": 2500.0
#                                 },
#                                 {
#                                     "SlNo.": 5,
#                                     "Description": "CHERRY TOMATO",
#                                     "Qty.": 1.75,
#                                     "Rate": 120.0,
#                                     "Amount": 210.0
#                                 }
#                             ],
#                             "TotalAmount": 3745.0,
#                             "TotalAmount(In Words)": "Three Thousand Seven Hundred Forty-Five Only",
#                             "Terms & conditions": "Subject to Ahmedabad Jurisdiction. Store quality check."
#                         }
#     xml_string = CKarnavati.MSGenerateXML(dictExtractedData=dictExtractedData)
    
    
    
    
    
    
    
    
    
    
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
import datetime

class CompanyInfo:
    """
    Holds details about the Tally Company configuration
    (like the Tally Company name, GST registration type, GSTIN, etc.).
    """
    def __init__(self, 
                 company_name: str,
                 gst_registration_type: str = "Regular",
                 gst_in: str = "24AAACG5535F1ZY",
                 state_name: str = "Gujarat",
                 country_name: str = "India"):
        self.company_name = company_name
        self.gst_registration_type = gst_registration_type
        self.gst_in = gst_in
        self.state_name = state_name
        self.country_name = country_name


class PartyDetails:
    """
    Holds details about the party/supplier: name, address, GSTIN, etc.
    """
    def __init__(self, 
                 party_name: str,
                 address_list: list,       # e.g. ["19, Jaypunj Complex", "Nr Master Petrol Pump, Shahpur", "Ahmedabad"]
                 gst_in: str,
                 state_name: str,
                 country_name: str,
                 pin_code: str = None):
        self.party_name = party_name
        self.address_list = address_list
        self.gst_in = gst_in
        self.state_name = state_name
        self.country_name = country_name
        self.pin_code = pin_code


class ConsigneeDetails:
    """
    Holds details about the consignee/ship-to address (which might be the company's address).
    """
    def __init__(self, 
                 address_list: list, 
                 gst_in: str, 
                 mailing_name: str,
                 state_name: str, 
                 pin_code: str = None,
                 country_name: str = "India"):
        self.address_list = address_list
        self.gst_in = gst_in
        self.mailing_name = mailing_name
        self.state_name = state_name
        self.pin_code = pin_code
        self.country_name = country_name


class LedgerEntry:
    """
    Represents a ledger entry line in the voucher.
    Example usage:
        - For the main party ledger line (Sundry Creditors line).
        - For GST lines (Input CGST, Input SGST, etc.).
        - For Round Off or any other ledger in Tally.
    """
    def __init__(   self,
                    ledger_name: str,
                    amount: float,
                    is_deemed_positive: bool,
                    cost_center: str = None,     # e.g. "Sweet Factory"
                    cost_category: str = "Primary Cost Category",
                    gst_class: str = "Not Applicable",
                    is_party_ledger: bool = False,
                    gst_overridden: bool = False):
        self.ledger_name = ledger_name
        self.amount = amount
        self.is_deemed_positive = is_deemed_positive
        self.cost_center = cost_center
        self.cost_category = cost_category
        self.gst_class = gst_class
        self.is_party_ledger = is_party_ledger
        self.gst_overridden = gst_overridden

    def to_xml(self, parent_element):
        """Construct the <LEDGERENTRIES.LIST> section."""
        ledger_entries_list = SubElement(parent_element, "LEDGERENTRIES.LIST")

        SubElement(ledger_entries_list, "LEDGERNAME").text = self.ledger_name
        SubElement(ledger_entries_list, "GSTCLASS").text = f" {self.gst_class}"
        SubElement(ledger_entries_list, "ISDEEMEDPOSITIVE").text = "Yes" if self.is_deemed_positive else "No"
        SubElement(ledger_entries_list, "LEDGERFROMITEM").text = "No"
        SubElement(ledger_entries_list, "REMOVEZEROENTRIES").text = "No"
        SubElement(ledger_entries_list, "ISPARTYLEDGER").text = "Yes" if self.is_party_ledger else "No"
        SubElement(ledger_entries_list, "GSTOVERRIDDEN").text = "Yes" if self.gst_overridden else "No"
        # If the ledger is negative, Tally typically expects a '-' sign 
        # e.g. -6955.20 (use the sign if is_deemed_positive is True).
        amount_str = f"{self.amount:.2f}"
        SubElement(ledger_entries_list, "AMOUNT").text = amount_str
        SubElement(ledger_entries_list, "VATEXPAMOUNT").text = amount_str

        # Cost center allocations
        if self.cost_center:
            category_alloc_list = SubElement(ledger_entries_list, "CATEGORYALLOCATIONS.LIST")
            SubElement(category_alloc_list, "CATEGORY").text = self.cost_category
            SubElement(category_alloc_list, "ISDEEMEDPOSITIVE").text = "Yes" if self.is_deemed_positive else "No"

            cost_center_alloc = SubElement(category_alloc_list, "COSTCENTREALLOCATIONS.LIST")
            SubElement(cost_center_alloc, "NAME").text = self.cost_center
            SubElement(cost_center_alloc, "AMOUNT").text = amount_str

        return ledger_entries_list


class TallyPurchaseVoucher:
    """
    Main class that brings together:
        - Company info
        - Party details
        - Consignee details
        - Ledger lines
        - And constructs the final XML using the Tally voucher template.
    """
    def __init__(   self,
                    company_info: CompanyInfo,
                    party_details: PartyDetails,
                    consignee_details: ConsigneeDetails,
                    voucher_number: str,
                    invoice_date: datetime.date,
                    invoice_no: str, 
                    voucher_type: str = "Purchase (Credit)",
                    narration: str = None,
                    cost_center_name: str = "Sweet Factory",
                    ledger_entries: list = None):
        self.company_info = company_info
        self.party_details = party_details
        self.consignee_details = consignee_details
        self.voucher_number = voucher_number
        self.invoice_date = invoice_date
        self.invoice_no = invoice_no
        self.voucher_type = voucher_type
        self.narration = narration
        self.cost_center_name = cost_center_name
        # List of LedgerEntry objects:
        self.ledger_entries = ledger_entries if ledger_entries else []

    def to_xml(self):
        """
        Builds the Tally voucher XML using xml.etree.ElementTree
        and returns the root XML Element.
        """
        # Envelope
        envelope = Element("ENVELOPE")

        # HEADER
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"

        # BODY
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")

        # REQUESTDESC
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"

        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name

        # REQUESTDATA
        request_data = SubElement(import_data, "REQUESTDATA")

        tally_message = SubElement(request_data, "TALLYMESSAGE")
        tally_message.set("xmlns:UDF", "TallyUDF")

        # VOUCHER
        voucher = SubElement(tally_message, "VOUCHER")
        voucher.set("VCHTYPE", self.voucher_type)
        voucher.set("ACTION", "Create")
        voucher.set("OBJVIEW", "Invoice Voucher View")

        # Party Address
        address_list_el = SubElement(voucher, "ADDRESS.LIST")
        address_list_el.set("TYPE", "String")
        for addr_line in self.party_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Consignee Address
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST")
        buyer_address_list_el.set("TYPE", "String")
        for addr_line in self.consignee_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates
        date_str = self.invoice_date.strftime("%Y%m%d")  # Tally expects YYYYMMDD
        SubElement(voucher, "DATE").text = date_str
        SubElement(voucher, "REFERENCEDATE").text = date_str
        SubElement(voucher, "VCHSTATUSDATE").text = date_str
        SubElement(voucher, "EFFECTIVEDATE").text = date_str

        # GST Info
        SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "VATDEALERTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.party_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.party_details.country_name

        # Narration
        if self.narration:
            SubElement(voucher, "NARRATION").text = self.narration
        else:
            SubElement(voucher, "NARRATION").text = ""

        # Party GSTIN
        SubElement(voucher, "PARTYGSTIN").text = self.party_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text = self.party_details.state_name

        # Party Name
        SubElement(voucher, "PARTYNAME").text = self.party_details.party_name

        # Tally Company GST config
        SubElement(voucher, "GSTREGISTRATION").text = "Gujarat Registration"
        SubElement(voucher, "GSTREGISTRATION").set("TAXTYPE", "GST")
        SubElement(voucher, "GSTREGISTRATION").set("TAXREGISTRATION", self.company_info.gst_in)
        SubElement(voucher, "CMPGSTIN").text = self.company_info.gst_in

        # Voucher type, ledger name, numbers
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.party_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "REFERENCE").text = self.invoice_no

        # Basic Buyer & Consignee Info
        SubElement(voucher, "BASICBUYERNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "PARTYMAILINGNAME").text = self.party_details.party_name
        SubElement(voucher, "PARTYPINCODE").text = self.party_details.pin_code if self.party_details.pin_code else ""
        SubElement(voucher, "CONSIGNEEGSTIN").text = self.consignee_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.consignee_details.pin_code if self.consignee_details.pin_code else ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.consignee_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.consignee_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.consignee_details.country_name

        # Basic base party
        SubElement(voucher, "BASICBASEPARTYNAME").text = self.party_details.party_name

        # Other static fields 
        SubElement(voucher, "NUMBERINGSTYLE").text = "Manual"
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = "Invoice Voucher View"
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = "Gujarat Registration"
        SubElement(voucher, "VCHGSTCLASS").text = " Not Applicable"
        SubElement(voucher, "COSTCENTRENAME").text = self.cost_center_name
        SubElement(voucher, "BUYERPINNUMBER").text = self.party_details.gst_in[:-3]  # Just an example
        SubElement(voucher, "VCHENTRYMODE").text = "Item Invoice"
        SubElement(voucher, "ISINVOICE").text = "Yes"
        SubElement(voucher, "HASDISCOUNTS").text = "No"
        SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"

        # Now we add each LedgerEntry in self.ledger_entries
        # For the main party ledger, we might also want a BillAllocation if needed:
        for idx, ledger_entry in enumerate(self.ledger_entries):
            # Convert to XML
            entry_xml = ledger_entry.to_xml(voucher)

            # Example for BillAllocation if it's the party ledger
            if ledger_entry.is_party_ledger:
                bill_alloc_list = SubElement(entry_xml, "BILLALLOCATIONS.LIST")
                SubElement(bill_alloc_list, "NAME").text = self.invoice_no
                SubElement(bill_alloc_list, "BILLTYPE").text = "New Ref"
                SubElement(bill_alloc_list, "TDSDEDUCTEEISSPECIALRATE").text = "No"
                SubElement(bill_alloc_list, "AMOUNT").text = f"{ledger_entry.amount:.2f}"

        return envelope

    def to_string(self, pretty=False):
        """
        Return the XML as a string. If pretty=True, attempts to indent the XML.
        """
        envelope = self.to_xml()
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            import xml.dom.minidom
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")

if __name__ == "__main__":
    import datetime

    # Company information (this can be the same or different based on each Tally company setup)
    company_info = CompanyInfo(
        company_name="Gwalia Sweets Pvt Ltd - (24-25)",
        gst_registration_type="Regular",
        gst_in="24AAACG5535F1ZY",
        state_name="Gujarat",
        country_name="India"
    )

    # Party details (vendor) for Vendor A
    party_details_A = PartyDetails(
        party_name="Bhavya Sales Company",
        address_list=["19, Jaypunj Complex,", "Nr Master Petrol Pump, Shahpur", "Ahmedabad"],
        gst_in="24EDQPS8677J1ZF",
        state_name="Gujarat",
        country_name="India",
        pin_code="380001"
    )

    # Consignee details (our company or any "ship to" details)
    consignee_details = ConsigneeDetails(
        address_list=["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Vastrapur Ahmedabad", "Fssi No. 10713026000232"],
        gst_in="24AAACG5535F1ZY",
        mailing_name="Gwalia Sweets Pvt Ltd (2021-22)",
        state_name="Gujarat",
        pin_code="380002",
        country_name="India"
    )

    # Ledger entries
    # 1. Party Ledger (Credit)
    ledger_party = LedgerEntry(
        ledger_name="Bhavya Sales Company",
        amount=91190.00,
        is_deemed_positive=False,  # Because this is credit (goes out from Tally's perspective)
        is_party_ledger=True
    )

    # 2. Purchase Ledger (Debit)
    ledger_purchase = LedgerEntry(
        ledger_name="Purchase Packing 18%",
        amount=-77280.00,    # Negative from Tally's perspective for a debit
        is_deemed_positive=True,
        cost_center="Sweet Factory",
        gst_class="Not Applicable"
    )

    # 3. CGST Ledger
    ledger_cgst = LedgerEntry(
        ledger_name="Input CGST 9%",
        amount=-6955.20,
        is_deemed_positive=True,
        cost_center="Sweet Factory",
        gst_class="Not Applicable"
    )

    # 4. SGST Ledger
    ledger_sgst = LedgerEntry(
        ledger_name="Input SGST 9%",
        amount=-6955.20,
        is_deemed_positive=True,
        cost_center="Sweet Factory",
        gst_class="Not Applicable"
    )

    # 5. Round Off
    ledger_round_off = LedgerEntry(
        ledger_name="Round Off",
        amount=0.40,    # Tally might treat positive as negative or vice versa, but let's keep consistent
        is_deemed_positive=True,
        cost_center="Sweet Factory",
        gst_class="Not Applicable"
    )

    # Collect all ledger entries
    ledger_entries_for_A = [
        ledger_party,
        ledger_purchase,
        ledger_cgst,
        ledger_sgst,
        ledger_round_off
    ]

    # Create a TallyPurchaseVoucher instance
    voucher_A = TallyPurchaseVoucher(
        company_info=company_info,
        party_details=party_details_A,
        consignee_details=consignee_details,
        voucher_number="005/24-25",
        invoice_date=datetime.date(2025, 3, 1),
        invoice_no="005/24-25",
        narration="PLASTIC BAG",
        ledger_entries=ledger_entries_for_A
    )

    # Generate the XML string
    xml_output_A = voucher_A.to_string(pretty=True)
    print(xml_output_A)

    # ---------------------------------------------------------
    # If you have another Vendor B, just instantiate a new
    # PartyDetails object + relevant LedgerEntry lines,
    # then create TallyPurchaseVoucher again, call .to_string(), etc.
    # ---------------------------------------------------------
