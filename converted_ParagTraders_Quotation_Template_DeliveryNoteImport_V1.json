{"tag": "ENVELOPE", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "HEADER", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "TALLYREQUEST", "attributes": {}, "text": {"TEXT": "Import Data"}, "children": []}]}, {"tag": "BODY", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "IMPORTDATA", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "REQUESTDESC", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "REPORTNAME", "attributes": {}, "text": {"TEXT": "Vouchers"}, "children": []}, {"tag": "STATICVARIABLES", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "SVCURRENTCOMPANY", "attributes": {}, "text": {"TEXT": "PARAG TRADERS (24-25)"}, "children": []}]}]}, {"tag": "REQUESTDATA", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "TALLYMESSAGE", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "VOUCHER", "attributes": {"VCHTYPE": {"TEXT": "S Delivery Note"}, "ACTION": {"TEXT": "Create"}, "OBJVIEW": {"TEXT": "Invoice Voucher View"}}, "text": {"TEXT": ""}, "children": [{"tag": "ADDRESS.LIST", "attributes": {"TYPE": {"TEXT": "String"}}, "text": {"TEXT": ""}, "children": [{"tag": "ADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}, {"tag": "ADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}, {"tag": "ADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}]}, {"tag": "BASICBUYERADDRESS.LIST", "attributes": {"TYPE": {"TEXT": "String"}}, "text": {"TEXT": ""}, "children": [{"tag": "BASICBUYERADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}, {"tag": "BASICBUYERADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}, {"tag": "BASICBUYERADDRESS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}]}, {"tag": "BASICORDERTERMS.LIST", "attributes": {"TYPE": {"TEXT": "String"}}, "text": {"TEXT": ""}, "children": [{"tag": "BASICORDERTERMS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}, {"tag": "BASICORDERTERMS", "attributes": {}, "text": {"FIELD": "<PERSON><PERSON><PERSON><PERSON><PERSON>"}, "children": []}]}, {"tag": "DATE", "attributes": {}, "text": {"FIELD": "DocumentCreatedOn"}, "children": []}, {"tag": "REFERENCEDATE", "attributes": {}, "text": {"FIELD": "DocumentCreatedOn"}, "children": []}, {"tag": "VCHSTATUSDATE", "attributes": {}, "text": {"FIELD": "DocumentCreatedOn"}, "children": []}, {"tag": "PARTYNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "STATENAME", "attributes": {}, "text": {"FIELD": "ClientLocation"}, "children": []}, {"tag": "COUNTRYOFRESIDENCE", "attributes": {}, "text": {"TEXT": "India"}, "children": []}, {"tag": "PLACEOFSUPPLY", "attributes": {}, "text": {"FIELD": "ClientLocation"}, "children": []}, {"tag": "PARTYMAILINGNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "PARTYPINCODE", "attributes": {}, "text": {"TEXT": "452001"}, "children": []}, {"tag": "CONSIGNEEMAILINGNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "CONSIGNEEPINCODE", "attributes": {}, "text": {"TEXT": "452001"}, "children": []}, {"tag": "CONSIGNEESTATENAME", "attributes": {}, "text": {"FIELD": "ClientLocation"}, "children": []}, {"tag": "CMPGSTSTATE", "attributes": {}, "text": {"FIELD": "ClientLocation"}, "children": []}, {"tag": "CONSIGNEECOUNTRYNAME", "attributes": {}, "text": {"TEXT": "India"}, "children": []}, {"tag": "BASICBASEPARTYNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "NUMBERINGSTYLE", "attributes": {}, "text": {"TEXT": "Auto Retain"}, "children": []}, {"tag": "CLASSNAME", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "VOUCHERTYPENAME", "attributes": {}, "text": {"TEXT": "S Delivery Note"}, "children": []}, {"tag": "PARTYLEDGERNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "VOUCHERNUMBER", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "BASICBUYERNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "PERSISTEDVIEW", "attributes": {}, "text": {"TEXT": "Invoice Voucher View"}, "children": []}, {"tag": "VCHSTATUSVOUCHERTYPE", "attributes": {}, "text": {"TEXT": "S Delivery Note"}, "children": []}, {"tag": "EFFECTIVEDATE", "attributes": {}, "text": {"FIELD": "DocumentCreatedOn"}, "children": []}, {"tag": "ISVATDUTYPAID", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "ISDELIVERYSAMEASCONSIGNEE", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "ISDISPATCHSAMEASCONSIGNOR", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "BASICSHIPPEDBY", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "BASICFINALDESTINATION", "attributes": {}, "text": {"TEXT": "GODOWN"}, "children": []}, {"tag": "ALLINVENTORYENTRIES.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "STOCKITEMNAME", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "GSTOVRDNISREVCHARGEAPPL", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "GSTOVRDNTAXABILITY", "attributes": {}, "text": {"TEXT": "Taxable"}, "children": []}, {"tag": "GSTSOURCETYPE", "attributes": {}, "text": {"TEXT": "Stock Item"}, "children": []}, {"tag": "GSTITEMSOURCE", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "HSNSOURCETYPE", "attributes": {}, "text": {"TEXT": "Stock Item"}, "children": []}, {"tag": "HSNITEMSOURCE", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "GSTOVRDNTYPEOFSUPPLY", "attributes": {}, "text": {"TEXT": "Goods"}, "children": []}, {"tag": "GSTRATEINFERAPPLICABILITY", "attributes": {}, "text": {"TEXT": "As per Masters/Company"}, "children": []}, {"tag": "GSTHSNNAME", "attributes": {}, "text": {"TEXT": "39229000"}, "children": []}, {"tag": "GSTHSNINFERAPPLICABILITY", "attributes": {}, "text": {"TEXT": "As per Masters/Company"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "RATE", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "ACTUALQTY", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "BILLEDQTY", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "BATCHALLOCATIONS.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "GODOWNNAME", "attributes": {}, "text": {"TEXT": "Godown"}, "children": []}, {"tag": "BATCHNAME", "attributes": {}, "text": {"TEXT": "Primary Batch"}, "children": []}, {"tag": "DESTINATIONGODOWNNAME", "attributes": {}, "text": {"TEXT": "Godown"}, "children": []}, {"tag": "INDENTNO", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ORDERNO", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "TRACKINGNUMBER", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "DYNAMICCSTISCLEARED", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "ACTUALQTY", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "BILLEDQTY", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "{TallyUDF}_UDF_687866857.LIST", "attributes": {"DESC": {"TEXT": ""}, "ISLIST": {"TEXT": "YES"}, "TYPE": {"TEXT": "Amount"}, "INDEX": {"TEXT": "1000"}}, "text": {"TEXT": ""}, "children": [{"tag": "{TallyUDF}_UDF_687866857", "attributes": {"DESC": {"TEXT": ""}}, "text": {"TEXT": ""}, "children": []}]}]}, {"tag": "ACCOUNTINGALLOCATIONS.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS.LIST", "attributes": {"TYPE": {"TEXT": "Number"}}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS", "attributes": {}, "text": {"TEXT": "-1"}, "children": []}]}, {"tag": "LEDGERNAME", "attributes": {}, "text": "NEW GST SALES LEDGER", "children": []}, {"tag": "GSTCLASS", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "LEDGERFROMITEM", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "REMOVEZEROENTRIES", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "ISPARTYLEDGER", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "GSTOVERRIDDEN", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}]}, {"tag": "RATEDETAILS.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "GSTRATEDUTYHEAD", "attributes": {}, "text": {"TEXT": "CGST"}, "children": []}, {"tag": "GSTRATEVALUATIONTYPE", "attributes": {}, "text": {"TEXT": "Based on Value"}, "children": []}, {"tag": "GSTRATE", "attributes": {}, "text": {"TEXT": "9"}, "children": []}]}, {"tag": "RATEDETAILS.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "GSTRATEDUTYHEAD", "attributes": {}, "text": {"TEXT": "SGST/UTGST"}, "children": []}, {"tag": "GSTRATEVALUATIONTYPE", "attributes": {}, "text": {"TEXT": "Based on Value"}, "children": []}, {"tag": "GSTRATE", "attributes": {}, "text": {"TEXT": "9"}, "children": []}]}, {"tag": "RATEDETAILS.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "GSTRATEDUTYHEAD", "attributes": {}, "text": {"TEXT": "IGST"}, "children": []}, {"tag": "GSTRATEVALUATIONTYPE", "attributes": {}, "text": {"TEXT": "Based on Value"}, "children": []}, {"tag": "GSTRATE", "attributes": {}, "text": {"TEXT": "18"}, "children": []}]}, {"tag": "{TallyUDF}_UDF_687866857.LIST", "attributes": {"DESC": {"TEXT": ""}, "ISLIST": {"TEXT": "YES"}, "TYPE": {"TEXT": "Amount"}, "INDEX": {"TEXT": "1000"}}, "text": {"TEXT": ""}, "children": [{"tag": "{TallyUDF}_UDF_687866857", "attributes": {"DESC": {"TEXT": ""}}, "text": {"TEXT": "480.51"}, "children": []}]}]}, {"tag": "LEDGERENTRIES.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS.LIST", "attributes": {"TYPE": {"TEXT": "Number"}}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS", "attributes": {}, "text": {"TEXT": "-1"}, "children": []}]}, {"tag": "APPROPRIATEFOR", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "LEDGERNAME", "attributes": {}, "text": {"FIELD": "ClientName"}, "children": []}, {"tag": "GSTCLASS", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "LEDGERFROMITEM", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "REMOVEZEROENTRIES", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "ISPARTYLEDGER", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "GSTOVERRIDDEN", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}]}, {"tag": "LEDGERENTRIES.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS.LIST", "attributes": {"TYPE": {"TEXT": "Number"}}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS", "attributes": {}, "text": {"TEXT": "-1"}, "children": []}]}, {"tag": "APPROPRIATEFOR", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ROUNDTYPE", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "LEDGERNAME", "attributes": {}, "text": {"TEXT": "CGST A/C"}, "children": []}, {"tag": "METHODTYPE", "attributes": {}, "text": {"TEXT": "GST"}, "children": []}, {"tag": "GSTCLASS", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "LEDGERFROMITEM", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "REMOVEZEROENTRIES", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "ISPARTYLEDGER", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "GSTOVERRIDDEN", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "VATEXPAMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}]}, {"tag": "LEDGERENTRIES.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS.LIST", "attributes": {"TYPE": {"TEXT": "Number"}}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS", "attributes": {}, "text": {"TEXT": "-1"}, "children": []}]}, {"tag": "APPROPRIATEFOR", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ROUNDTYPE", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "LEDGERNAME", "attributes": {}, "text": {"TEXT": "SGST A/C"}, "children": []}, {"tag": "METHODTYPE", "attributes": {}, "text": {"TEXT": "GST"}, "children": []}, {"tag": "GSTCLASS", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "LEDGERFROMITEM", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "REMOVEZEROENTRIES", "attributes": {}, "text": {"TEXT": "Yes"}, "children": []}, {"tag": "ISPARTYLEDGER", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "GSTOVERRIDDEN", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}, {"tag": "VATEXPAMOUNT", "attributes": {}, "text": {"TEXT": ""}, "children": []}]}, {"tag": "LEDGERENTRIES.LIST", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS.LIST", "attributes": {"TYPE": {"TEXT": "Number"}}, "text": {"TEXT": ""}, "children": [{"tag": "OLDAUDITENTRYIDS", "attributes": {}, "text": {"TEXT": "-1"}, "children": []}]}, {"tag": "ROUNDTYPE", "attributes": {}, "text": {"TEXT": "Normal Rounding"}, "children": []}, {"tag": "LEDGERNAME", "attributes": {}, "text": {"TEXT": "Rounding Off (SALES)"}, "children": []}, {"tag": "GSTCLASS", "attributes": {}, "text": {"TEXT": "Not Applicable"}, "children": []}, {"tag": "ISDEEMEDPOSITIVE", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "LEDGERFROMITEM", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "REMOVEZEROENTRIES", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "ISPARTYLEDGER", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "GSTOVERRIDDEN", "attributes": {}, "text": {"TEXT": "No"}, "children": []}, {"tag": "ROUNDLIMIT", "attributes": {}, "text": {"TEXT": "1"}, "children": []}, {"tag": "AMOUNT", "attributes": {}, "text": {"TEXT": "0.01"}, "children": []}, {"tag": "VATEXPAMOUNT", "attributes": {}, "text": {"TEXT": "0.01"}, "children": []}]}, {"tag": "{TallyUDF}_UDF_788530053.LIST", "attributes": {"DESC": {"TEXT": ""}, "ISLIST": "YES", "TYPE": {"TEXT": "String"}, "INDEX": {"TEXT": "900"}}, "text": {"TEXT": ""}, "children": [{"tag": "{TallyUDF}_UDF_788530053", "attributes": {"DESC": {"TEXT": ""}}, "text": "BANTI THAKUR", "children": []}]}, {"tag": "{TallyUDF}_UDF_788530055.LIST", "attributes": {"DESC": {"TEXT": ""}, "ISLIST": {"TEXT": "YES"}, "TYPE": {"TEXT": "String"}, "INDEX": {"TEXT": "900"}}, "text": {"TEXT": ""}, "children": [{"tag": "{TallyUDF}_UDF_788530055", "attributes": {"DESC": {"TEXT": ""}}, "text": {"TEXT": "Retail"}, "children": []}]}, {"tag": "{TallyUDF}_UDF_788530060.LIST", "attributes": {"DESC": {"TEXT": ""}, "ISLIST": {"TEXT": "YES"}, "TYPE": {"TEXT": "String"}, "INDEX": {"TEXT": "907"}}, "text": {"TEXT": ""}, "children": [{"tag": "{TallyUDF}_UDF_788530060", "attributes": {"DESC": {"TEXT": ""}}, "text": {"TEXT": "Local"}, "children": []}]}]}]}, {"tag": "TALLYMESSAGE", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "COMPANY", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "REMOTECMPINFO.LIST", "attributes": {"MERGE": {"TEXT": "Yes"}}, "text": {"TEXT": ""}, "children": [{"tag": "NAME", "attributes": {}, "text": {"TEXT": "232444dc-0a9f-41fc-9050-89f8ea29cb48"}, "children": []}, {"tag": "REMOTECMPNAME", "attributes": {}, "text": {"TEXT": "PARAG TRADERS (24-25)"}, "children": []}, {"tag": "REMOTECMPSTATE", "attributes": {}, "text": {"TEXT": "Madhya Pradesh"}, "children": []}]}]}]}, {"tag": "TALLYMESSAGE", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "COMPANY", "attributes": {}, "text": {"TEXT": ""}, "children": [{"tag": "REMOTECMPINFO.LIST", "attributes": {"MERGE": {"TEXT": "Yes"}}, "text": {"TEXT": ""}, "children": [{"tag": "NAME", "attributes": {}, "text": {"TEXT": "232444dc-0a9f-41fc-9050-89f8ea29cb48"}, "children": []}, {"tag": "REMOTECMPNAME", "attributes": {}, "text": {"TEXT": "PARAG TRADERS (24-25)"}, "children": []}, {"tag": "REMOTECMPSTATE", "attributes": {}, "text": {"TEXT": "Madhya Pradesh"}, "children": []}]}]}]}]}]}]}]}