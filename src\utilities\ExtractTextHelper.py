import sys
sys.path.append("")
from src.Controllers.Logs_Controller import CLogController
import traceback
import io
import base64
import binascii
import re
from fastapi import HTTPException
from PyPDF2 import PdfReader
from PIL import Image
import pytesseract
import fitz  # PyMuPDF
from pdf2image import convert_from_bytes
from config.constants import Constants
from pillow_heif import register_heif_opener
register_heif_opener()
from src.utilities.DocmentOCR import ProcessDocument
from src.utilities.helperFunc import GPTHelper
from google.cloud import documentai_v1 as documentai
import json
import math
from reportlab.pdfgen import canvas

import asyncio
import aioboto3
import json
import sys
from botocore.exceptions import ClientError
import csv 
import os  
import difflib

class ExtractTextFromDoc:
    """
    A class to extract text from various types of documents including PDFs, text files, and images.
    It utilizes user-specific logging for tracing and debugging.
    """
    
    def __init__(self, dictUserData: dict, dictDocumentData: dict, debug: bool = False, bPerformAWSExtraction=False):
        """
        Initializes the document extractor with user ID and debug mode.

        Args:
            dictUserData (dict): The user data.
            dictDocumentData (dict): The document data
            debug (bool): Flag to enable/disable debug mode.
        """
        self.bPerformAWSExtraction = bPerformAWSExtraction
        self.debug = debug
        self.dictUserData = dictUserData
        self.dictDocumentData = dictDocumentData
        self.user_id = dictUserData.get('uid',None)
        
        self.file_data = dictDocumentData.get("DocBinaryData")
        self.file_type = dictDocumentData.get("file_type")
            
        if self.user_id is None:
            raise HTTPException(status_code=404, detail="ERROR: User ID Not found")
    
    
    async def log(self, level: str, message: str):
        """
        Helper method to log messages with the specified severity level.

        Args:
            level (str): The severity level of the log ('Info', 'Error', 'Debug').
            message (str): The message to log.
        """
        await CLogController.MSWriteLog(self.user_id, level, message)

    @staticmethod
    async def isScannedPDFDoc(file_binary,filetype="pdf") -> bool:
        """
        Determines if the given PDF file binary is a scanned document.

        Returns:
            bool: True if the document is scanned, False otherwise.
        """
        try:
            file_binary = base64.b64decode(file_binary) if isinstance(file_binary, str) else file_binary
            # By Default it works for PDF Document only
            doc = fitz.open(stream=file_binary, filetype=filetype)
            text_content = ""
            for page in doc:
                text_content += page.get_text()
            num_pages = doc.page_count  # Directly use the page_count attribute
            doc.close()  # Close the document after you're done with it
            is_scanned = len(text_content.strip()) < 25 * num_pages
            
            return is_scanned
        except Exception as e:
            return False
    
    
    @staticmethod
    async def MSGetPDFPageCount(fileBinary):
        iPageCount = 0
        fileSize = 0
        try:
            file_binary = base64.b64decode(fileBinary) if isinstance(fileBinary, str) else fileBinary
            objDoc = fitz.open(stream=file_binary, filetype="pdf")
            iPageCount = objDoc.page_count
            fileSize = len(file_binary)  # Get the size of the document in bytes
        except Exception as e:
            pass
        
        return {"pageCount":iPageCount,"fileSize" :fileSize}
        
    async def extract_text_only(self):
        """
        Extracts text from a file binary based on the file type.

        Returns:
            tuple: A tuple containing the extracted text and the number of pages.
        """
        try:
            output = ""
            page_count = 0
            if self.file_type.upper() == 'PDF':
                # Assuming `file_data` is the base64 encoded PDF data retrieved.
                file_binary = base64.b64decode(self.file_data) if isinstance(self.file_data, str) else self.file_data
                pdf = fitz.open(stream=file_binary, filetype="pdf")
                page_count = len(pdf)
                for page in pdf:
                    output += f"Page {page.number + 1}: \n{page.get_text()}\n---------\n"
                pdf.close()
            else:
                raise ValueError('Invalid file type for extraction')
            return {"extracted_text":output,"page_count": page_count}
        except Exception as e:
            await self.log("Error", f"Error extracting text: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Error occurred while extracting text")

    async def extract_text_from_image(self, images):
        """
        Extracts text from a list of image objects.

        Args:
            images (list): List of PIL.Image objects.

        Returns:
            tuple: A tuple containing the extracted text and the number of pages processed.
        """
        try:
            extracted_text = ""
            page_count = 0

            for i, image in enumerate(images):
                try:
                    # Extract text directly from the image object
                    text = pytesseract.image_to_string(image)

                    # Append to the final extracted text
                    extracted_text += f"--- Page {i + 1} ---\n{text}\n"
                    page_count += 1
                except Exception as e:
                    # Log and handle specific image processing errors
                    await self.log("Error", f"Error processing image page {i + 1}: {traceback.format_exc()}")
                    raise e

            return {"extracted_text": extracted_text, "page_count": page_count}
        except Exception as e:
            raise e

    async def ExtractTxtWithCoordinatesForDigitalPDFOnly(self):
        """
        Extracts text from a file binary along with their coordinates within the document.

        Returns:
            tuple: A tuple containing a dictionary of extracted text with coordinates and the page count.
        """
        try:
            output_data = {}
            # Extract text from Only Digital PDF Documents
            if self.file_type.upper() != "PDF":
                raise HTTPException(status_code=415, detail="Unsupported file type. Only PDF files are supported.")

            if not self.file_data:
                raise HTTPException(status_code=400, detail="Empty file binary data provided.")
            # Assuming `file_data` is the base64 encoded PDF data retrieved.
            file_binary = self.file_data
            doc = fitz.open(stream=file_binary, filetype="pdf")
            # NOTE:- ASSUMptions Document Pages are of same Size please change this in later versions
            page = doc[0]
            rect = page.rect  # Get the rectangle of the page
            page_width = rect.width
            page_height = rect.height

            iPageCount = doc.page_count
            for page_number, page in enumerate(doc):
                page_data = []
                blocks = page.get_text("dict")["blocks"]
                sorted_blocks = sorted(blocks, key=lambda b: (b['bbox'][1], b['bbox'][0]) if b['type'] == 0 else (float('inf'), float('inf')))
                prev_bbox = None
                combined_text = ""
                for block in sorted_blocks:
                    if block['type'] == 0:
                        for line in block["lines"]:
                            for span in line["spans"]:
                                text = span["text"].strip()
                                bbox = span["bbox"]
                                if prev_bbox and abs(bbox[0] - prev_bbox[2]) < 0.1:
                                    combined_text += text
                                    prev_bbox = (prev_bbox[0], prev_bbox[1], bbox[2], bbox[3])
                                    continue
                                if combined_text:
                                    page_data.append((combined_text, int(page_number)+1, int(round(prev_bbox[0], 0)), int(round(prev_bbox[1], 0)), int(round(prev_bbox[2], 0)), int(round(prev_bbox[3], 0))))
                                    combined_text = ""
                                combined_text = text
                                prev_bbox = bbox
                if combined_text:
                    page_data.append((combined_text, int(page_number)+1, int(round(prev_bbox[0], 0)), int(round(prev_bbox[1], 0)), int(round(prev_bbox[2], 0)), int(round(prev_bbox[3], 0))))
                if page_data:
                    output_data[page_number + 1] = page_data
            return {"extracted_text":output_data,"page_count": iPageCount, "page_width": page_width, "page_height":page_height}
        except HTTPException as e:
            raise e
        except Exception as e:
            await self.log("Error", f"Error extracting text with coordinates: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Error occurred while extracting text")
            
    async def ExtractTxtWithCoordinatesForScannedDoc(self):
        """
        Extracts text with the help of Document OCR from a file binary and then it will provide coordinates within the document.

        Returns:
            tuple: A tuple containing a dictionary of extracted text with coordinates and the page count.
        """
        try:
            await self.log("Info", f"Extraction of Scanned doc Text Content with coordinates Started.")

            if not self.file_data:
                raise HTTPException(status_code=404, detail="Empty file binary data provided.")
            # Assuming `file_data` is the base64 encoded PDF data retrieved.

            MimeType = Constants.strMimeType[self.file_type.lower()]
            
            if MimeType is None and isinstance(MimeType,str):
                raise ValueError(f"DocumentAIAPI: MIME type not found from Constants File: {self.file_type}")
            
            if Constants.strProjectId is None and isinstance(Constants.strProjectId,str):
                raise ValueError(f"DocumentAIAPI: Project ID not found from Constants File: {self.file_type}")
            
            if Constants.strLocation is None and isinstance(Constants.strLocation,str):
                raise ValueError(f"DocumentAIAPI: Location type not found from Constants File: {self.file_type}")
            
            if Constants.strProcessorId is None and isinstance(Constants.strProcessorId,str):
                raise ValueError(f"DocumentAIAPI: strProcessorId not found from Constants File: {self.file_type}")
            
            if Constants.strProcessorVersion is None and isinstance(Constants.strProcessorVersion,str):
                raise ValueError(f"DocumentAIAPI: strProcessorVersion not found from Constants File: {self.file_type}")
            
            obOCROutputJsonFile = ProcessDocument(strProjectId = Constants.strProjectId, 
                                    strLocation = Constants.strLocation, 
                                    strProcessorId = Constants.strProcessorId, 
                                    strProcessorVersion = Constants.strProcessorVersion, 
                                    file_binary = self.file_data, 
                                    strMimeType = MimeType,process_options=documentai.ProcessOptions(ocr_config=documentai.OcrConfig(
                                    enable_native_pdf_parsing=False, 
                                    enable_image_quality_scores=False,
                                    enable_symbol=False,
                                    compute_style_info=False,
                                    disable_character_boxes_detection=False,
                                    advanced_ocr_options=["legacy_layout"]
                                    )))
            await self.log("Info", f"Extracted Text Content: {obOCROutputJsonFile}")

            try:
                # Attempt to parse the JSON data
                dictInputJsonData = json.loads(obOCROutputJsonFile)
            except json.JSONDecodeError as e:
                # Handle the case where JSON parsing fails
                raise ValueError(f"DocumentAIAPI: Error parsing JSON: {e}")

            # Defining the necessary variables
            intTextIndexCounter = 0
            lsTupTextRectCoordinates = []
            output_data = {}  # Ensure this is defined before the loop if not already
            iPageCount = 0

            page_width = dictInputJsonData['pages'][0]['dimension']['width']
            page_height = dictInputJsonData['pages'][0]['dimension']['height']

            # Adding a loop to iterate over all pages
            for PageNumber, page in enumerate(dictInputJsonData["pages"]):
                for block in page["lines"]:
                    
                    # Defining points of Polygon
                    dictPolyBtmLftCrnrCoor = block["layout"]["boundingPoly"]["vertices"][0]
                    dictPolyBtmRgtCrnrCoor = block["layout"]["boundingPoly"]["vertices"][1]
                    dictPolyTopRgtCrnrCoor = block["layout"]["boundingPoly"]["vertices"][2]
                    dictPolyTopLftCrnrCoor = block["layout"]["boundingPoly"]["vertices"][3]

                    # Converting them to Rectangle by averaging numbers accordingly
                    intRectX1Coor = math.floor((dictPolyBtmLftCrnrCoor["x"] + dictPolyTopLftCrnrCoor["x"]) / 2)
                    intRectX2Coor = math.ceil((dictPolyBtmRgtCrnrCoor["x"] + dictPolyTopRgtCrnrCoor["x"]) / 2)
                    intRectY1Coor = math.floor((dictPolyBtmLftCrnrCoor["y"] + dictPolyBtmRgtCrnrCoor["y"]) / 2)
                    intRectY2Coor = math.ceil((dictPolyTopRgtCrnrCoor["y"] + dictPolyTopLftCrnrCoor["y"]) / 2)

                    # Extracting End index
                    try:
                        strTextEndIndex = block["layout"]["textAnchor"]["textSegments"][0]["endIndex"]
                    except KeyError:
                        pass
                    
                    # Converting String of strTextEndIndex to integer
                    strTextEndIndex = int(strTextEndIndex)

                    # Now you can safely use Text_Counter and end_index as slice indices
                    strExtractedText = dictInputJsonData["text"][intTextIndexCounter:strTextEndIndex]
                    intTextIndexCounter = strTextEndIndex

                    # Creating Tuple for storing values
                    tupExtTextRectCoor = (strExtractedText,PageNumber + 1, intRectX1Coor, intRectY1Coor, intRectX2Coor, intRectY2Coor)
                    
                    # Appending values in Value_List
                    lsTupTextRectCoordinates.append(tupExtTextRectCoor)
                    
                # Final_Dict[(page_number)] = [(Text,Page Number, X1, Y1, X2, Y2)]
                output_data[(PageNumber) + 1] = lsTupTextRectCoordinates
                iPageCount += 1
                await self.log("Info", f"Final Extracted Text Content: {output_data}")
            return {"extracted_text":output_data,"page_count": iPageCount, "page_width": page_width, "page_height":page_height} #return dictPageNoTextRectCoord
        except HTTPException as e:
            raise e
        except Exception as e:
            await self.log("Error", f"DocumentAIAPI: Error extracting text with coordinates: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Error occurred while extracting text")
    
    @staticmethod
    async def isScannedDoc(file_data, file_type):
        """
        Determines if OCR should be applied based on the file type and content.

        Parameters:
            file_data (bytes): Doc file data
            file_type (str): The type of the file.

        Returns:
            bool: True if OCR should be applied, False otherwise.
        """
        try:
            # Lowercase the file type string for case-insensitive comparison
            file_type = file_type.lower()

            # Use regular expressions to match file types
            if re.match(r'^jpeg|png|webp|bmp|jpg$', file_type):
                return True
            else:
                # Check if the document is a scanned document based on its content
                return await ExtractTextFromDoc.isScannedPDFDoc(file_data)

        except Exception as e:
            # Handle any other exceptions that may occur
            # You can customize the error message or re-raise the exception as needed
            raise Exception("Error determining OCR requirement: " + str(e))
        
    async def MDocAITextOnly(self):
        """
        Convert polygon coordinates to rectangle coordinates for each block of text in a JSON file.

        Input:
            strInputJsonFilePath (str): The file path of the input JSON file containing polygon coordinates.

        Output:
            dict: A dictionary containing the text and converted rectangle coordinates for each block of text, organized by page number.
            e.g. "Output Format is in: { PageNo : [ (Text, X1, Y1, X2, Y2), ..... ]}"

        Purpose:
            This function is designed to process OCR (Optical Character Recognition) output stored in a JSON file. It converts polygon coordinates representing text blocks into rectangle coordinates. The resulting dictionary organizes the text and its corresponding rectangle coordinates by page number, facilitating further processing or visualization.

        """

        try:
            await self.log("Info", f"Extraction of Text Content Started")

            if not self.file_data:
                raise HTTPException(status_code=404, detail="Empty file binary data provided.")
            
            MimeType = Constants.strMimeType[self.file_type.lower()]

            if MimeType is None and isinstance(MimeType,str):
                raise ValueError(f"DocumentAIAPI: MIME type not found from Constants File: {self.file_type}")
            
            if Constants.strProjectId is None and isinstance(Constants.strProjectId,str):
                raise ValueError(f"DocumentAIAPI: Project ID not found from Constants File: {self.file_type}")
            
            if Constants.strLocation is None and isinstance(Constants.strLocation,str):
                raise ValueError(f"DocumentAIAPI: Location type not found from Constants File: {self.file_type}")
            
            if Constants.strProcessorId is None and isinstance(Constants.strProcessorId,str):
                raise ValueError(f"DocumentAIAPI: strProcessorId not found from Constants File: {self.file_type}")
            
            if Constants.strProcessorVersion is None and isinstance(Constants.strProcessorVersion,str):
                raise ValueError(f"DocumentAIAPI: strProcessorVersion not found from Constants File: {self.file_type}")
            
            obOCROutputJsonFile = ProcessDocument(strProjectId = Constants.strProjectId, 
                                    strLocation = Constants.strLocation, 
                                    strProcessorId = Constants.strProcessorId, 
                                    strProcessorVersion = Constants.strProcessorVersion, 
                                    file_binary = self.file_data, 
                                    strMimeType = MimeType,process_options=documentai.ProcessOptions(ocr_config=documentai.OcrConfig(
                                    enable_native_pdf_parsing=False, 
                                    enable_image_quality_scores=False,
                                    enable_symbol=False,
                                    compute_style_info=False,
                                    disable_character_boxes_detection=False,
                                    advanced_ocr_options=["legacy_layout"]
                                    )))
            await self.log("Info", f"Extracted Text Content: {obOCROutputJsonFile}")
            try:
                # Attempt to parse the JSON data
                dictInputJsonData = json.loads(obOCROutputJsonFile)
            except json.JSONDecodeError as e:
                # Handle the case where JSON parsing fails
                raise ValueError(f"DocumentAIAPI: Error parsing JSON: {e}")
        

            # Defining the necessary variables
            intTextIndexCounter = 0
            lsTupTextRectCoordinates = []
            strExtractedTextFinal = ""
            iPageCount = 0

            # Adding a loop to iterate over all pages
            for PageNumber, page in enumerate(dictInputJsonData["pages"]):
                # lsTupTextRectCoordinates.append(f"Page {PageNumber}:\n")
                for block in page["lines"]:
                    # Extracting End index
                    try:
                        strTextEndIndex = block["layout"]["textAnchor"]["textSegments"][0]["endIndex"]
                    except KeyError:
                        pass
                    
                    # Converting String of strTextEndIndex to integer
                    strTextEndIndex = int(strTextEndIndex)

                    # Now you can safely use Text_Counter and end_index as slice indices
                    strExtractedText = dictInputJsonData["text"][intTextIndexCounter:strTextEndIndex]
                    intTextIndexCounter = strTextEndIndex
                    
                    # Appending values in Value_List
                    lsTupTextRectCoordinates.append(strExtractedText)

                strPageContent = ""
                # iterate over every line of text
                for strLineTxt in lsTupTextRectCoordinates:
                    strPageContent+= strLineTxt.strip() + "\n"
                strExtractedTextFinal += f"Page {PageNumber+1}:\n{strPageContent}\n---------\n"

                lsTupTextRectCoordinates = []
                iPageCount += 1
                
                await self.log("Info", f"Final Extracted Text Content: {strExtractedTextFinal}")

            return {"extracted_text":strExtractedTextFinal,"page_count": iPageCount}
        except HTTPException as e:
            raise e
        except Exception as e:
            await self.log("Error", f"DocumentAIAPI: Error extracting text with coordinates: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Error occurred while extracting text")

    async def MExtractTxtFromDoc(self, isGPTEnabled: bool, bPerformAWSExtraction = False):
        """
        Main method to extract text from binary data, which can also handle OCR for scanned documents.

        Args:
            file_binary (bytes): Binary content of the file.
            isGPTEnabled (bool): Flag indicating if GPT is enabled.

        Returns:
            tuple: A tuple containing the extracted text, the number of pages, a flag indicating if OCR was used,
                and a flag indicating if paid OCR was used.
        """
        dictResult = {
            "AWSResponse": None,
            "extracted_text": None,
            "page_count": None,
            "is_scanned_document": None,
            "page_width": None,
            "page_height": None 
        }
        try:
            # Scanned Document Text Extraction (FREE!!!)
            page_width = None
            page_height = None
            is_paid_document_extraction = isGPTEnabled
            isScannedDoc = await ExtractTextFromDoc.isScannedDoc(self.file_data, self.file_type)
            bSimpolo = self.dictDocumentData.get("ModelName", "").lower() == "simpolo"
            if bPerformAWSExtraction:
                #! Call aws extraction and store the actual response from aws
                dictAWSResponse = await CAWSExtraction.MSExtractDocUsingAWS(s3BucketName=os.getenv("s3_bucket_name_for_tally_docs"), s3FileObjectName=self.dictDocumentData["DocS3ObjectKey"], bSimpolo=bSimpolo)
                
            #  Text file then use Gemini instead of GPT due to constraint of text along with Cordinates 
            elif is_paid_document_extraction and not isScannedDoc:
                # Digital PDF Document
                # self.file_data = base64.b64decode(self.file_data) if isinstance(self.file_data, str) else self.file_data
                # extraction_result = await self.ExtractTxtWithCoordinatesForDigitalPDFOnly()
                # extracted_text, page_count, page_width, page_height = extraction_result["extracted_text"], extraction_result["page_count"], extraction_result["page_width"], extraction_result["page_height"]
                # Scanned Documents Extraction (PAID!!!)
                self.file_data = base64.b64decode(self.file_data.encode('utf-8'))
                # Assuming `file_data` is the base64 encoded PDF data retrieved.
                extraction_result = await self.ExtractTxtWithCoordinatesForScannedDoc()
                extracted_text, page_count, page_width, page_height = extraction_result["extracted_text"], extraction_result["page_count"], extraction_result["page_width"], extraction_result["page_height"]
            elif isScannedDoc and is_paid_document_extraction:
                # Scanned Documents Extraction (PAID!!!)
                self.file_data = base64.b64decode(self.file_data.encode('utf-8'))
                # Assuming `file_data` is the base64 encoded PDF data retrieved.
                extraction_result = await self.ExtractTxtWithCoordinatesForScannedDoc()
                extracted_text, page_count, page_width, page_height = extraction_result["extracted_text"], extraction_result["page_count"], extraction_result["page_width"], extraction_result["page_height"]

            elif isScannedDoc and not is_paid_document_extraction:
                # Scanned Document Text Extraction (PAID!!!) Document AI API

                extraction_result = await self.MDocAITextOnly()

                extracted_text, page_count = extraction_result["extracted_text"], extraction_result["page_count"]
            else:
                # Not scanned Document & not a Paid USER
                extraction_result = await self.extract_text_only()
                extracted_text, page_count = extraction_result["extracted_text"], extraction_result["page_count"]

            if bPerformAWSExtraction:
                dictResult.update(
                                    {
                                        "AWSResponse": dictAWSResponse["AWSExtractedObject"],
                                        "extracted_text": dictAWSResponse["AWSExtractedData"],
                                    }
                                )
            else:
                dictResult.update({
                    "extracted_text": extracted_text,
                    "page_count": page_count,
                    "is_scanned_document": isScannedDoc,
                    "page_width": page_width,
                    "page_height": page_height
                })
                
            return dictResult
        
        except Exception as e:
            await self.log("Error", f"Failed processing binary data: {traceback.format_exc()}")
            raise HTTPException(status_code=500, detail="Error occurred while processing file")


class CAWSExtraction:
    
    @staticmethod
    async def MSExtractDocUsingAWS(s3BucketName, s3FileObjectName, bSimpolo=False):
        result = {
            "AWSExtractedObject": None,
            "AWSExtractedData":""
        }
        # Initialize aioboto3 Session
        session = aioboto3.Session()

        async with session.client('textract') as textract_client:
            # Step 1: Start Document Analysis with Textract
            print("Starting document analysis with Textract...")
            job_id = await CAWSExtraction.start_document_analysis(textract_client, s3BucketName, s3FileObjectName)

            # Step 2: Get Document Analysis Results
            print("Polling for document analysis results...")
            blocks = await CAWSExtraction.get_document_analysis(textract_client, job_id)
            
            result["AWSExtractedObject"] = blocks
            
            # Step 3: Extract Tables
            print("Extracting tables from results...")
            tables = CAWSExtraction.extract_tables_from_blocks(blocks)
            print(f"Found {len(tables)} table(s).")

            if bSimpolo:
                tables = CAWSExtraction.merge_tables(tables)
                tables = CAWSExtraction.filter_discount_column(tables)
                
            # Step 4: Extract Text Without Tables
            print("Extracting text without tables from results...")
            text_content = CAWSExtraction.get_text_without_tables(blocks)

            # Step 5: Combine Text and Tables into a Single Structured String
            print("Combining extracted text and tables into structured data...")

            # Construct combined_str similar to the combined text file
            combined_str = "Text\n"
            combined_str += json.dumps(text_content, ensure_ascii=False, indent=4)
            combined_str += "\n\n"

            for idx, table in enumerate(tables, start=1):
                combined_str += f"Table-{idx}\n"
                # Convert table to CSV string in-memory
                output = io.StringIO()
                writer = csv.writer(output)
                writer.writerows(table)
                table_csv_str = output.getvalue()
                combined_str += table_csv_str
                combined_str += "\n\n"

            result["AWSExtractedData"] = combined_str
            
            return result
        
    @staticmethod
    async def start_document_analysis(textract_client, bucket_name, object_name, job_tag="job", notification_channel=None):
        """
        Starts asynchronous document analysis with Textract.

        :param textract_client: aioboto3 Textract client.
        :param bucket_name: S3 bucket name where the document is stored.
        :param object_name: S3 object name of the document.
        :param job_tag: Identifier for the job.
        :param notification_channel: (Optional) SNS topic ARN for notifications.
        :return: Job ID
        """
        try:
            params = {
                'DocumentLocation': {
                    'S3Object': {
                        'Bucket': bucket_name,
                        'Name': object_name
                    }
                },
                'FeatureTypes': ['TABLES'],
                'JobTag': job_tag
            }
            if notification_channel:
                params['NotificationChannel'] = notification_channel

            response = await textract_client.start_document_analysis(**params)
            job_id = response['JobId']
            print(f"Started document analysis job with Job ID: {job_id}")
            return job_id
        except ClientError as e:
            print(f"Error starting document analysis: {e}")
            sys.exit(1)

    @staticmethod
    async def get_document_analysis(textract_client, job_id):
        """
        Retrieves the results of an asynchronous document analysis job.

        :param textract_client: aioboto3 Textract client.
        :param job_id: ID of the Textract job.
        :return: List of blocks from Textract response.
        """
        try:
            while True:
                response = await textract_client.get_document_analysis(JobId=job_id)
                status = response['JobStatus']
                print(f"Job status: {status}")

                if status in ['SUCCEEDED', 'FAILED']:
                    if status == 'SUCCEEDED':
                        print("Document analysis succeeded.")
                        blocks = response.get('Blocks', [])
                        # Handle pagination if NextToken is present
                        next_token = response.get('NextToken', None)
                        while next_token:
                            print("Retrieving next page of results...")
                            response = await textract_client.get_document_analysis(JobId=job_id, NextToken=next_token)
                            blocks.extend(response.get('Blocks', []))
                            next_token = response.get('NextToken', None)
                        return blocks
                    else:
                        print("Document analysis failed.")
                        return ValueError("Document analysis failed.")
                await asyncio.sleep(5)  # Wait before polling again
        except ClientError as e:
            print(f"Error getting document analysis: {e}")
            return ValueError("Document analysis failed.")



    @staticmethod
    def extract_tables_from_blocks(blocks):
        """
        Extracts tables from Textract response blocks.

        :param blocks: List of blocks from Textract response.
        :return: List of tables, each table is a list of rows, each row is a list of cell texts.
        """
        tables = []
        table_blocks = [block for block in blocks if block['BlockType'] == 'TABLE']

        for table in table_blocks:
            cell_blocks = []

            # Extract CELL blocks related to the current table
            relationships = table.get('Relationships', [])
            for rel in relationships:
                if rel['Type'] == 'CHILD':
                    for child_id in rel['Ids']:
                        cell = next((b for b in blocks if b['Id'] == child_id and b['BlockType'] == 'CELL'), None)
                        if cell:
                            cell_blocks.append(cell)

            if not cell_blocks:
                continue

            # Sort cells by row and column
            sorted_cells = sorted(cell_blocks, key=lambda x: (x.get('RowIndex', 0), x.get('ColumnIndex', 0)))

            # Determine the number of rows and columns
            max_row = max(cell.get('RowIndex', 0) for cell in sorted_cells)
            max_col = max(cell.get('ColumnIndex', 0) for cell in sorted_cells)

            # Initialize table structure
            table_data = [["" for _ in range(max_col)] for _ in range(max_row)]

            for cell in sorted_cells:
                row = cell.get('RowIndex', 0) - 1  # Zero-based index
                col = cell.get('ColumnIndex', 0) - 1  # Zero-based index
                text = CAWSExtraction.get_text_for_block(blocks, cell)
                if 0 <= row < max_row and 0 <= col < max_col:
                    table_data[row][col] = text
                else:
                    print(f"Cell position out of bounds. Row: {row+1}, Column: {col+1}")

            tables.append(table_data)

        return tables
    
    @staticmethod
    def get_text_for_block(blocks, cell_block):
        """
        Retrieves the text associated with a cell block.

        :param blocks: List of all blocks.
        :param cell_block: The cell block for which text is to be retrieved.
        :return: Concatenated text within the cell.
        """
        text = []
        relationships = cell_block.get('Relationships', [])
        for rel in relationships:
            if rel['Type'] == 'CHILD':
                for child_id in rel['Ids']:
                    child = next((b for b in blocks if b['Id'] == child_id), None)
                    if child:
                        if child['BlockType'] == 'WORD':
                            text.append(child.get('Text', ''))
                        elif child['BlockType'] == 'SELECTION_ELEMENT' and child.get('SelectionStatus') == 'SELECTED':
                            text.append("X")
        return ' '.join(text)

    @staticmethod
    def get_text_without_tables(blocks):
        """
        Extracts text from Textract response blocks, excluding tables.

        :param blocks: List of blocks from Textract response.
        :return: Dictionary containing extracted text information.
        """
        # Create a mapping from block Id to block for quick lookup
        id_to_block = {block['Id']: block for block in blocks}

        # Collect all WORD Ids that are part of cells
        word_ids_in_cells = set()
        for block in blocks:
            if block['BlockType'] == 'CELL':
                if 'Relationships' in block:
                    for relationship in block['Relationships']:
                        if relationship['Type'] == 'CHILD':
                            word_ids_in_cells.update(relationship['Ids'])

        # Map LINE block Ids to their respective page numbers
        line_id_to_page = {}
        page_number = 1  # Assuming page numbers start from 1
        for block in blocks:
            if block['BlockType'] == 'PAGE':
                page_id = block['Id']
                if 'Relationships' in block:
                    for relationship in block['Relationships']:
                        if relationship['Type'] == 'CHILD':
                            for child_id in relationship['Ids']:
                                child_block = id_to_block.get(child_id)
                                if child_block and child_block['BlockType'] == 'LINE':
                                    line_id_to_page[child_id] = page_number
                page_number += 1  # Increment page number for the next PAGE block

        # Collect LINE blocks that are not part of any table (CELL)
        non_table_lines = []
        for block in blocks:
            if block['BlockType'] == 'LINE':
                is_in_table = False
                if 'Relationships' in block:
                    for relationship in block['Relationships']:
                        if relationship['Type'] == 'CHILD':
                            # Check if any of the LINE's child WORD Ids are in word_ids_in_cells
                            if any(child_id in word_ids_in_cells for child_id in relationship['Ids']):
                                is_in_table = True
                                break
                if not is_in_table:
                    non_table_lines.append(block)

        # Prepare the output in the desired format
        output = {}
        for line in non_table_lines:
            text = line['Text']
            bbox = line['Geometry']['BoundingBox']
            x1 = round(bbox['Left'], 5)
            y1 = round(bbox['Top'], 5)
            x2 = round(x1 + bbox['Width'], 5)
            y2 = round(y1 + bbox['Height'], 5)
            line_id = line['Id']
            page_num = line_id_to_page.get(line_id, 1)  # Default to page 1 if not found

            if page_num not in output:
                output[page_num] = []
            output[page_num].append([
                text,
                x1,
                y1,
                x2,
                y2
            ])

        return output

    @staticmethod
    def headers_match(table_header, reference_headers):
        # Check if each column in the table header has a close match in the reference headers
        normalized_table_header = []
        for col in table_header:
            closest_match = difflib.get_close_matches(col, reference_headers, n=1, cutoff=0.8)
            if closest_match:
                normalized_table_header.append(closest_match[0])
            else:
                return False  # If any column doesn't have a close match, return False
        return normalized_table_header == reference_headers  # Check if matched headers align in order

    @staticmethod
    def merge_tables(tables):
        # Predefined headers for reference
        reference_headers = ['Sr No', 'Description Trade Mark', 'HSN Code | Grade', 'Batch / Shade', 'Qty(Box)', 'Qty(Sq Mt)',
                            'Rate Box', 'Basic Rate / Sq Ft', 'Trade Disc. Unit', 'Proj/Spcl Disc.', 'Scheme Disc.',
                            'Progressive Disc.', 'Loyaly Disc.', 'Gr.Boost. Disc.', 'Cash Disc.', 'Net Rate / Sqft', 'Amount']
        
        # Dictionary to group tables by normalized headers
        grouped_tables = {}

        # Group tables by headers
        for table in tables:
            if len(table) == 0:
                continue  # Skip empty tables
            if CAWSExtraction.headers_match(table[0], reference_headers):
                header_key = tuple(reference_headers)  # Use normalized headers as the key
            else:
                header_key = tuple(table[0])  # Use the raw header if it doesn't match

            if header_key not in grouped_tables:
                grouped_tables[header_key] = []
            grouped_tables[header_key].append(table)
        
        # Initialize to keep the updated tables list
        updated_tables = []

        try:
            # Process each group of tables
            for header_key, table_group in grouped_tables.items():
                if header_key == tuple(reference_headers):  # Process only tables with matching headers
                    merged_table = [list(header_key)]  # Start with the normalized header
                    for table in table_group:
                        for row in table[1:]:  # Skip the header row
                            if len(merged_table) > 1 and (
                                (row[1] and not any(row[3:])) or (row[2] and not any(row[3:]))
                            ):
                                last_row = merged_table[-1]
                                for col_index in range(len(row)):
                                    if row[col_index]:  # If there is data in the current column
                                        last_row[col_index] += f" {row[col_index]}"
                            else:
                                merged_table.append(row)
                    updated_tables.append(merged_table)
                else:
                    # If headers don't match reference headers, add tables unmodified
                    updated_tables.extend(table_group)
        except Exception as e:
            print(f"An error occurred: {e}")
            return tables  # Return the original tables unmodified if any error occurs

        return updated_tables

    

    @staticmethod
    def remove_percent_symbol(input_string):
        # Raise ValueError if more than one comma is found
        if input_string.count(',') > 1:
            raise ValueError(f"Invalid input format: more than one comma found in '{input_string}'")
        # Replace commas with periods and ensure only one decimal point
        cleaned_input = re.sub(r',', '.', input_string.strip())
        
        # Use regex to match the floating-point number before any whitespace or percent symbol
        match = re.match(r'^\d+(\.\d+)?', cleaned_input)
        
        # Return only the matched number or raise a ValueError if no valid number found
        if match:
            return f"{match.group(0)}%"
        else:
            print(f"WARN - Invalid input format, could not extract a valid floating-point number - {input_string}")
            return input_string

    @staticmethod
    def filter_discount_column(tables):
        """
        Filters discount columns in the tables by terminating values at space and taking the decimal number before the percent symbol.
        
        :param tables: List of tables to process
        :param discount_columns: List of discount column headers to filter
        :param headers: List of headers to identify column indices
        :return: Updated list of tables
        """
        discount_columns = ['Proj/Spcl Disc.', 'Scheme Disc.', 'Progressive Disc.', 'Loyaly Disc.', 'Gr.Boost. Disc.', 'Cash Disc.']
        # Find and print indexes of discount columns in input_columns
        headers = ['Sr No', 'Description Trade Mark', 'HSN Code Grade', 'Batch Shade', 'Qty(Box)', 'Qty(Sq Mt)', 'Rate Box', 'Basic Rate / Sq Ft', 'Trade Disc. Unit', 'Proj/Spcl Disc.', 'Scheme Disc.', 'Progressive Disc.', 'Loyaly Disc.', 'Gr.Boost. Disc.', 'Cash Disc.', 'Net Rate / Sqft', 'Amount']
        # Find indexes of discount columns
        discount_indexes = [headers.index(col) for col in discount_columns if col in headers]
        
        for table in tables:
            for row in table:
                for idx in discount_indexes:
                    if idx < len(row) and row[idx] not in headers and row[idx] !="":
                        row[idx] = CAWSExtraction.remove_percent_symbol(row[idx])

        return tables


if __name__ == "__main__":
    lsTables = [[['Sr No', 'Description Trade Mark', 'HSN Code I Grade', 'Batch Shade', 'Qty(Box)', 'Qty(Sq Mt)', 'Rate / Box', 'Basic Rate / Sq Ft', 'Trade Disc. Unit', 'Proj Spcl Disc.', 'Scheme Disc.', 'Progressive Disc.', 'Loyaly Disc.', 'Gr.Boost. Disc.', 'Cash Disc.', 'Net Rate / Sqft', 'Amount'], ['1', 'SCS CALACATTA SUN 2P PG1200X1800X9 FORZA', '6907.21.00 PREMIUM', '15C4PRES01', '15', '64.800', '3,487.54', '75.00', '', '', '5.0%*', '5.0%', '2.0%', '', '4.0%', '63.68', '44,417.53'], ['2', 'SCS ARAVALI ONYX 2P PG1200X1800X9 FORZA', '6907.21.00 PREMIUM', '13H4PRES01', '10', '43.200', '3,487.54', '75.00', '', '', '5.0%*', '5.0%', '2.0%', '', '4.0%', '63.68', '29,611.71'], ['3', 'PG PROZZO 1197 3P PG600X1200X8.5 PROZZO', '6907.21.00 PREMIUM', '07J4PRES01', '14', '30.240', '697.51', '30.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '26.81', '8,727.65'], ['4', 'GV SOLID WHITE GV600X600X8.5', '6907.21.00 PREMIUM', '04I4PRES01', '16', '23.040', '728.51', '47.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '42.01', '10,417.79'], ['5', 'GV SOLID BLACK GV600X600X8.5', '6907.21.00 PREMIUM', '03I4PRES01', '16', '23.040', '728.51', '47.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '42.01', '10,417.79'], ['6', 'FB SOLID COTTA GV600X600X8.5', '6907.21.00 PREMIUM', '07I4PRES01', '17', '24.480', '666.51', '43.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '38.43', '10,126.87'], ['7', 'FB FLAKY WHITE 3PC GV60X120X8.5', '6907.21.00 PREMIUM', '07H4PRES01', '1', '2.160', '1,255.51', '54.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '48.26', '1,122.12'], ['8', 'FB FLAKY WHITE 3PC PG60X120X8.5', '6907.21.00 PREMIUM', '07K4PRES02', '4', '8.640', '1,302.01', '56.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '50.05', '4,654.76'], ['9', 'TD DURO DOVE 3PC GV60X120X8.5', '6907.21.00 PREMIUM', '13J4PRES01', '7', '15.120', '720.76', '31.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '27.71', '4,509.29'], ['10', 'TD DURO KOTA 3PC GV60X120X8.5', '6907.21.00 PREMIUM', '14J4PRES01', '3', '6.480', '720.76', '31.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '27.71', '1,932.57'], ['11', 'TD VELVET SLATE 3PC GV60X120X8.5', '6907.21.00', '23J4PRES01', '5', '10.800', '720.76', '31.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '27.71', '3,220.92']], [['Name & Address of Receiver', 'Details of Consignee'], ['Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE - 452001 MADHYA PRADESH State Code : 23', 'Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE - 452001 MADHYA PRADESH State Code: 23']], [['Sr No', 'Description Trade Mark', 'HSN Code | Grade', 'Batch / Shade', 'Qty(Box)', 'Qty(Sq Mt)', 'Rate/Box', 'Basic Rate / Sq Ft', 'Trade Disc. Unit', 'Proj Spcl Disc.', 'Scheme Disc.', 'Progressive Disc.', 'Loyaly Disc.', 'Gr.Boost. Disc.', 'Cash Disc.', 'Net Rate / Sqft', 'Amount'], ['', '', 'PREMIUM', '', '', '', '', '', '', '', '', '', '', '', '', '', ''], ['12', 'TD VELVET DOVE 3PC GV60X120X8.5', '6907.21.00 PREMIUM', '2314PRES01', '11', '23.760', '720.76', '31.00', '', '', '', '5.0%', '2.0%', '', '4.0%', '27.71', '7,086.03'], ['', '', '', 'Total', '119', '275.76', '', '', '', '', '', '', '', '', '', '', '136,245.03']], [['Sub Total', '136,245.03'], ['IGST @ 18.00%', '24,524.12'], ['Rounding Off', '0.15'], ['', ''], ['Grand Total', '160,769.00']], [['Outstanding in your account inclusive of the bill is (Subject to realization of payment)', '160,769.00'], ['Overdue on Assigned Credit days as per company policy', ''], ['Sales Target For Growth Booster Scheme', '*********.38']], [['Current Year Business with Simpolo', '********.75'], ['(Excluding Insu., Freight, GST and this Invoice Value)', ''], ['Pending Target For Growth Booster Scheme', '********.63']], [['Name & Address of Receiver', 'Details of Consignee'], ['Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE - 452001 MADHYA PRADESH State Code : 23', 'Vhd Distributors Llp 201,Princess Empire, 12,Race Course Road, INDORE - 452001 MADHYA PRADESH State Code: 23']]]
    finalTables = CAWSExtraction.merge_tables(lsTables)
    print(finalTables)