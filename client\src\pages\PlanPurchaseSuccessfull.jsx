import { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';
import logo from '../assets/logo.svg'; // Ensure the logo is correctly located
import { AiOutlineCheckCircle } from 'react-icons/ai'; // Import check icon from react-icons

export default function PlanPurchaseSuccessful() {
    const navigate = useNavigate();
    const location = useLocation();
    const [message, setMessage] = useState("Plan Purchase Successful!");
    const [description, setDescription] = useState("Thank you for your purchase! Enjoy your premium features with AccuVelocity.");

    useEffect(() => {
        const params = new URLSearchParams(location.search);
        const topUp = params.get('top_up');
        const subscription = params.get('subscription');

        if (topUp) {
            setMessage("Top-Up Plan Purchase Successful!");
            setDescription("Thank you for topping up! Enjoy your premium features with AccuVelocity.");
        } else if (subscription) {
            setMessage("Subscription Purchase Successful!");
            setDescription("Thank you for your purchase! Enjoy your premium features with AccuVelocity.");
        }

        // Redirect to /Profile after 10 seconds
        const timer = setTimeout(() => {
            navigate('/Profile', { replace: true });
        }, 10000);

        // Cleanup timer on component unmount
        return () => clearTimeout(timer);

    }, [location.search, navigate]);

    const handleStart = () => {
        navigate('/Profile'); // Change to where you want to redirect after start
    };

    return (
        <div className="min-h-screen flex flex-col items-center justify-center bg-[#ffff]">
            <div className="flex justify-center">
                <img src={logo} alt="AccuVelocity" className="h-12 w-auto mb-6" />
            </div>
            <div className="text-center">
                <div className="flex justify-center mb-6 my-10">
                    <AiOutlineCheckCircle size={100} color="#28a745" />
                </div>
                <h3 className="text-xl font-semibold mb-2 mt-12">{message}</h3>
                <p className="text-gray-400 mb-8">{description}</p>
                <button 
                    onClick={handleStart} 
                    className="py-2 px-8 mt-10 border border-transparent rounded-md shadow-sm text-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                >
                    Go to Dashboard
                </button>
            </div>
        </div>
    );
}
