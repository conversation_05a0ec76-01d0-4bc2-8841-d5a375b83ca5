<ENVELOPE>
    <HEADER>
        <TALLYREQUEST>Import Data</TALLYREQUEST>
    </HEADER>
    <BODY>
        <IMPORTDATA>
            <REQUESTDESC>
                <REPORTNAME>Vouchers</REPORTNAME>
                <STATICVARIABLES>
                    <SVCURRENTCOMPANY>PARAG TRADERS (24-25)</SVCURRENTCOMPANY>                    <!--NAME OF company PARAG Static in our case-->
                </STATICVARIABLES>
            </REQUESTDESC>
            <REQUESTDATA>
                <TALLYMESSAGE xmlns:UDF="TallyUDF">
                    <VOUCHER VCHTYPE="S Delivery Note" ACTION="Create" OBJVIEW="Invoice Voucher View">
                        <ADDRESS.LIST TYPE="String">                            <!--Address of Party-->
                            <ADDRESS>ADD:- 22 SHRI NAGAR KANKAD</ADDRESS>
                            <ADDRESS>OPP KIRTI PALACE</ADDRESS>
                            <ADDRESS>MOB:- 9826083786</ADDRESS>
                        </ADDRESS.LIST>
                        <BASICBUYERADDRESS.LIST TYPE="String">                            <!--Address of Party-->
                            <BASICBUYERADDRESS>ADD:- 22 SHRI NAGAR KANKAD</BASICBUYERADDRESS>
                            <BASICBUYERADDRESS>OPP KIRTI PALACE</BASICBUYERADDRESS>
                            <BASICBUYERADDRESS>MOB:- 9826083786</BASICBUYERADDRESS>
                        </BASICBUYERADDRESS.LIST>
                        <BASICORDERTERMS.LIST TYPE="String">                            <!--Basic order details-->
                            <BASICORDERTERMS>DEL:- 280 ANOOP NAGR</BASICORDERTERMS>
                            <BASICORDERTERMS>MOB:-9826083786</BASICORDERTERMS>
                        </BASICORDERTERMS.LIST>
                        <DATE>20250103</DATE>
                        <REFERENCEDATE>20250103</REFERENCEDATE>                        <!--RefDate-->
                        <VCHSTATUSDATE>20250103</VCHSTATUSDATE>                        <!--VoucherDate-->
                        <PARTYNAME>AMJAD KHAN</PARTYNAME>                        <!--PartyDetailsPart-->
                        <STATENAME>Madhya Pradesh</STATENAME>
                        <COUNTRYOFRESIDENCE>India</COUNTRYOFRESIDENCE>                        <!--PartyDetailsPart-->
                        <PLACEOFSUPPLY>Madhya Pradesh</PLACEOFSUPPLY>                        <!--PartyDetailsPart-->
                        <PARTYMAILINGNAME>AMJAD KHAN</PARTYMAILINGNAME>                        <!--PartyDetailsPart-->
                        <PARTYPINCODE>452001</PARTYPINCODE>                        <!--PartyDetailsPart-->
                        <CONSIGNEEMAILINGNAME>AMJAD KHAN</CONSIGNEEMAILINGNAME>                        <!--ConsignDetailsPart-->
                        <CONSIGNEEPINCODE>452001</CONSIGNEEPINCODE>                        <!--ConsignDetailsPart-->
                        <CONSIGNEESTATENAME>Madhya Pradesh</CONSIGNEESTATENAME>                        <!--ConsignDetailsPart-->
                        <CMPGSTSTATE>Madhya Pradesh</CMPGSTSTATE>                        <!--ConsignDetailsPart-->
                        <CONSIGNEECOUNTRYNAME>India</CONSIGNEECOUNTRYNAME>                        <!--ConsignDetailsPart-->
                        <BASICBASEPARTYNAME>AMJAD KHAN</BASICBASEPARTYNAME>                        <!--PartyName-->
                        <NUMBERINGSTYLE>Auto Retain</NUMBERINGSTYLE>                        <!--NumberingConfigStatic-->
                        <CLASSNAME>GST LOCAL SALES</CLASSNAME>                        <!--VoucherClassDependsOnBuyerState-->
                        <VOUCHERTYPENAME>S Delivery Note</VOUCHERTYPENAME>                        <!--NAME OF Voucher Static-->
                        <PARTYLEDGERNAME>AMJAD KHAN</PARTYLEDGERNAME>                        <!--PartyDetailsPart-->
                        <VOUCHERNUMBER>S/32137</VOUCHERNUMBER>                        <!--Voucher No.NEED TO CHECK-->
                        <BASICBUYERNAME>AMJAD KHAN</BASICBUYERNAME>                        <!--PartyDetailsPart-->
                        <PERSISTEDVIEW>Invoice Voucher View</PERSISTEDVIEW>                        <!--View Of Voucher Static-->
                        <VCHSTATUSVOUCHERTYPE>S Delivery Note</VCHSTATUSVOUCHERTYPE>                        <!--NAME OF Voucher Static-->
                        <EFFECTIVEDATE>20250103</EFFECTIVEDATE>                        <!--EFfectiveDate-->
                        <ISVATDUTYPAID>Yes</ISVATDUTYPAID>                        <!--NEED TO CHECK-->
                        <ISDELIVERYSAMEASCONSIGNEE>Yes</ISDELIVERYSAMEASCONSIGNEE>                        <!--NEED TO CHECK-->
                        <ISDISPATCHSAMEASCONSIGNOR>Yes</ISDISPATCHSAMEASCONSIGNOR>                        <!--NEED TO CHECK-->
                        <BASICSHIPPEDBY>626 ACHAL 02-12-2024</BASICSHIPPEDBY>                        <!--DispatchDetails-->
                        <BASICFINALDESTINATION>GODOWN</BASICFINALDESTINATION>                        <!--DispatchDetails-->
                        <ALLINVENTORYENTRIES.LIST>
                            <STOCKITEMNAME>K-12925IN-CP HEALTH FAUCET W/WHITE SDSPRAY, M HOSE</STOCKITEMNAME>                            <!--NameOf Item-->
                            <GSTOVRDNISREVCHARGEAPPL> Not Applicable</GSTOVRDNISREVCHARGEAPPL>                            <!--NEED TO CHECK-->
                            <GSTOVRDNTAXABILITY>Taxable</GSTOVRDNTAXABILITY>                            <!--NEED TO CHECK-->
                            <GSTSOURCETYPE>Stock Item</GSTSOURCETYPE>                            <!--StocK Item Static-->
                            <GSTITEMSOURCE>K-12925IN-CP HEALTH FAUCET W/WHITE SDSPRAY, M HOSE</GSTITEMSOURCE>                            <!--NameOf Item-->
                            <HSNSOURCETYPE>Stock Item</HSNSOURCETYPE>                            <!--StocK Item Static-->
                            <HSNITEMSOURCE>K-12925IN-CP HEALTH FAUCET W/WHITE SDSPRAY, M HOSE</HSNITEMSOURCE>                            <!--NameOf Item-->
                            <GSTOVRDNTYPEOFSUPPLY>Goods</GSTOVRDNTYPEOFSUPPLY>                            <!--TypeOfSupply static-->
                            <GSTRATEINFERAPPLICABILITY>As per Masters/Company</GSTRATEINFERAPPLICABILITY>                            <!--SelectFromDropDown can be static-->
                            <GSTHSNNAME>39229000</GSTHSNNAME>                            <!--HSNNumber depend on StockItem-->
                            <GSTHSNINFERAPPLICABILITY>As per Masters/Company</GSTHSNINFERAPPLICABILITY>                            <!--SelectFromDropDown can be static-->
                            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>                            <!--NEED TO CHECK-->
                            <RATE>1050.00/Nos</RATE>                            <!--Total Amt per Unit including TAX-->
                            <AMOUNT>2669.49</AMOUNT>                            <!--Total Amt of item without tax Unit*Rate-->
                            <ACTUALQTY> 3 Nos</ACTUALQTY>                            <!--Total Quntity-->
                            <BILLEDQTY> 3 Nos</BILLEDQTY>                            <!--Total Quntity-->
                            <BATCHALLOCATIONS.LIST>
                                <GODOWNNAME>Godown</GODOWNNAME>                                <!--Name of godown-->
                                <BATCHNAME>Primary Batch</BATCHNAME>                                <!--Name of Batch NEED TO CHECK-->
                                <DESTINATIONGODOWNNAME>Godown</DESTINATIONGODOWNNAME>                                <!--Name of godown-->
                                <INDENTNO> Not Applicable</INDENTNO>                                <!--NEED TO CHECK-->
                                <ORDERNO> Not Applicable</ORDERNO>                                <!--NEED TO CHECK-->
                                <TRACKINGNUMBER>S/3207</TRACKINGNUMBER>                                <!--SalesOrderNumber-->
                                <DYNAMICCSTISCLEARED>No</DYNAMICCSTISCLEARED>                                <!--NEED TO CHECK-->
                                <AMOUNT>2669.49</AMOUNT>                                <!--Total Amt of item without tax Unit*Rate-->
                                <ACTUALQTY> 3 Nos</ACTUALQTY>                                <!--Total Quntity-->
                                <BILLEDQTY> 3 Nos</BILLEDQTY>                                <!--Total Quntity-->
                                <UDF:_UDF_687866857.LIST DESC="" ISLIST="YES" TYPE="Amount" INDEX="1000">                                    <!--GST VALUES-->
                                    <UDF:_UDF_687866857 DESC="">480.51</UDF:_UDF_687866857>                                    <!--GST VALUES-->
                                </UDF:_UDF_687866857.LIST>                                <!--GST VALUES-->
                            </BATCHALLOCATIONS.LIST>                            <!--NEED TO CHECK-->
                            <ACCOUNTINGALLOCATIONS.LIST>                                <!--NEED TO CHECK-->
                                <OLDAUDITENTRYIDS.LIST TYPE="Number">                                    <!--NEED TO CHECK-->
                                    <OLDAUDITENTRYIDS>-1</OLDAUDITENTRYIDS>                                    <!--NEED TO CHECK-->
                                </OLDAUDITENTRYIDS.LIST>                                <!--NEED TO CHECK-->
                                <LEDGERNAME>NEW GST SALES LEDGER</LEDGERNAME>                                <!--NameOfSalesGL-->
                                <GSTCLASS> Not Applicable</GSTCLASS>                                <!--NEED TO CHECK-->
                                <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>                                <!--Mostly No for Credit NEED TO CHECK-->
                                <LEDGERFROMITEM>No</LEDGERFROMITEM>                                <!--NEED TO CHECK-->
                                <REMOVEZEROENTRIES>No</REMOVEZEROENTRIES>                                <!--NEED TO CHECK-->
                                <ISPARTYLEDGER>No</ISPARTYLEDGER>                                <!--WillBeNo-->
                                <GSTOVERRIDDEN>No</GSTOVERRIDDEN>                                <!--NEED TO CHECK-->
                                <AMOUNT>2669.49</AMOUNT>                                <!--TOTAL AMT-->
                            </ACCOUNTINGALLOCATIONS.LIST>                            <!--NEED TO CHECK-->
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>CGST</GSTRATEDUTYHEAD>                                <!--Name of Tax CGST/SGST/IGST/Cess-->
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>                                <!-- Mostly Static NEED TO CHECK-->
                                <GSTRATE> 9</GSTRATE>                                <!-- TaxRatOfItem-->
                            </RATEDETAILS.LIST>
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>SGST/UTGST</GSTRATEDUTYHEAD>                                <!--Name of Tax CGST/SGST/IGST/Cess-->
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>                                <!-- Mostly Static NEED TO CHECK-->
                                <GSTRATE> 9</GSTRATE>                                <!-- TaxRatOfItem-->
                            </RATEDETAILS.LIST>
                            <RATEDETAILS.LIST>
                                <GSTRATEDUTYHEAD>IGST</GSTRATEDUTYHEAD>                                <!--Name of Tax CGST/SGST/IGST/Cess-->
                                <GSTRATEVALUATIONTYPE>Based on Value</GSTRATEVALUATIONTYPE>                                <!-- Mostly Static NEED TO CHECK-->
                                <GSTRATE> 18</GSTRATE>                                <!-- TaxRatOfItem-->
                            </RATEDETAILS.LIST>
                            <UDF:_UDF_687866857.LIST DESC="" ISLIST="YES" TYPE="Amount" INDEX="1000">                                <!--GST VALUES-->
                                <UDF:_UDF_687866857 DESC="">480.51</UDF:_UDF_687866857>                                <!--GST VALUES-->
                            </UDF:_UDF_687866857.LIST>                            <!--GST VALUES-->
                        </ALLINVENTORYENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <OLDAUDITENTRYIDS.LIST TYPE="Number">                                <!--NEED TO CHECK-->
                                <OLDAUDITENTRYIDS>-1</OLDAUDITENTRYIDS>                                <!--NEED TO CHECK-->
                            </OLDAUDITENTRYIDS.LIST>                            <!--NEED TO CHECK-->
                            <APPROPRIATEFOR> Not Applicable</APPROPRIATEFOR>                            <!--NEED TO CHECK-->
                            <LEDGERNAME>AMJAD KHAN</LEDGERNAME>                            <!--PartyName-->
                            <GSTCLASS> Not Applicable</GSTCLASS>                            <!--NEED TO CHECK-->
                            <ISDEEMEDPOSITIVE>Yes</ISDEEMEDPOSITIVE>                            <!--Mostly yes for Debit NEED TO CHECK-->
                            <LEDGERFROMITEM>No</LEDGERFROMITEM>                            <!--NEED TO CHECK-->
                            <REMOVEZEROENTRIES>No</REMOVEZEROENTRIES>                            <!--NEED TO CHECK-->
                            <ISPARTYLEDGER>Yes</ISPARTYLEDGER>                            <!--WillBeYes-->
                            <GSTOVERRIDDEN>No</GSTOVERRIDDEN>                            <!--NEED TO CHECK-->
                            <AMOUNT>-3150.00</AMOUNT>                            <!--TotalAmt-->
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <OLDAUDITENTRYIDS.LIST TYPE="Number">                                <!--NEED TO CHECK-->
                                <OLDAUDITENTRYIDS>-1</OLDAUDITENTRYIDS>                                <!--NEED TO CHECK-->
                            </OLDAUDITENTRYIDS.LIST>                            <!--NEED TO CHECK-->
                            <APPROPRIATEFOR> Not Applicable</APPROPRIATEFOR>                            <!--NEED TO CHECK-->
                            <ROUNDTYPE> Not Applicable</ROUNDTYPE>                            <!--Type of Round off will be Not Applicable in other Gls case and Normal Up or Down in case of Roundup GL-->
                            <LEDGERNAME>CGST A/C</LEDGERNAME>                            <!--NAME OF GSTAccount-->
                            <METHODTYPE>GST</METHODTYPE>                            <!--Will be GST STATIC-->
                            <GSTCLASS> Not Applicable</GSTCLASS>                            <!--NEED TO CHECK-->
                            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>                            <!--Mostly No for Credit NEED TO CHECK-->
                            <LEDGERFROMITEM>No</LEDGERFROMITEM>                            <!--NEED TO CHECK-->
                            <REMOVEZEROENTRIES>Yes</REMOVEZEROENTRIES>                            <!--May be Remove GL if amt is Zero-->
                            <ISPARTYLEDGER>No</ISPARTYLEDGER>                            <!--WillBeNo-->
                            <GSTOVERRIDDEN>No</GSTOVERRIDDEN>                            <!--NEED TO CHECK-->
                            <AMOUNT>240.25</AMOUNT>                            <!--TotalGST Amt-->
                            <VATEXPAMOUNT>240.25</VATEXPAMOUNT>                            <!--TotalGST Amt-->
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>                            <!--NEED TO CHECK-->
                            <OLDAUDITENTRYIDS.LIST TYPE="Number">                                <!--NEED TO CHECK-->
                                <OLDAUDITENTRYIDS>-1</OLDAUDITENTRYIDS>                                <!--NEED TO CHECK-->
                            </OLDAUDITENTRYIDS.LIST>                            <!--NEED TO CHECK-->
                            <APPROPRIATEFOR> Not Applicable</APPROPRIATEFOR>                            <!--NEED TO CHECK-->
                            <ROUNDTYPE> Not Applicable</ROUNDTYPE>                            <!--Type of Round off will be Not Applicable in other Gls case and Normal Up or Down in case of Roundup GL-->
                            <LEDGERNAME>SGST A/C</LEDGERNAME>                            <!--NAME OF GSTAccount-->
                            <METHODTYPE>GST</METHODTYPE>                            <!--Will be GST STATIC-->
                            <GSTCLASS> Not Applicable</GSTCLASS>                            <!--NEED TO CHECK-->
                            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>                            <!--Mostly No for Credit NEED TO CHECK-->
                            <LEDGERFROMITEM>No</LEDGERFROMITEM>                            <!--NEED TO CHECK-->
                            <REMOVEZEROENTRIES>Yes</REMOVEZEROENTRIES>                            <!--May be Remove GL if amt is Zero-->
                            <ISPARTYLEDGER>No</ISPARTYLEDGER>                            <!--WillBeNo-->
                            <GSTOVERRIDDEN>No</GSTOVERRIDDEN>                            <!--NEED TO CHECK-->
                            <AMOUNT>240.25</AMOUNT>                            <!--TotalGST Amt-->
                            <VATEXPAMOUNT>240.25</VATEXPAMOUNT>                            <!--TotalGST Amt-->
                        </LEDGERENTRIES.LIST>
                        <LEDGERENTRIES.LIST>
                            <OLDAUDITENTRYIDS.LIST TYPE="Number">
                                <OLDAUDITENTRYIDS>-1</OLDAUDITENTRYIDS>
                            </OLDAUDITENTRYIDS.LIST>
                            <ROUNDTYPE>Normal Rounding</ROUNDTYPE>                            <!--Type of Round off will be Not Applicable in other Gls case and Normal Up or Down in case of Roundup GL-->
                            <LEDGERNAME>Rounding Off (SALES)</LEDGERNAME>                            <!--Name of RoundOfGL-->
                            <GSTCLASS> Not Applicable</GSTCLASS>                            <!--NEED TO CHECK-->
                            <ISDEEMEDPOSITIVE>No</ISDEEMEDPOSITIVE>                            <!--Mostly No for Credit NEED TO CHECK-->
                            <LEDGERFROMITEM>No</LEDGERFROMITEM>                            <!--NEED TO CHECK-->
                            <REMOVEZEROENTRIES>No</REMOVEZEROENTRIES>                            <!--May be Remove GL if amt is Zero-->
                            <ISPARTYLEDGER>No</ISPARTYLEDGER>                            <!--WillBeNo-->
                            <GSTOVERRIDDEN>No</GSTOVERRIDDEN>                            <!--NEED TO CHECK-->
                            <ROUNDLIMIT> 1</ROUNDLIMIT>                            <!--TotalLimitForRoundOff-->
                            <AMOUNT>0.01</AMOUNT>                            <!--totalRoundOFfAmt-->
                            <VATEXPAMOUNT>0.01</VATEXPAMOUNT>                            <!--totalRoundOFfAmt-->
                        </LEDGERENTRIES.LIST>                        <!--NEED TO CHECK-->
                        <UDF:_UDF_788530053.LIST DESC="" ISLIST="YES" TYPE="String" INDEX="900">                            <!--CustomTDL field for SalepersonName-->
                            <UDF:_UDF_788530053 DESC="">BANTI THAKUR</UDF:_UDF_788530053>                            <!--CustomTDL field for SalepersonName-->
                        </UDF:_UDF_788530053.LIST>
                        <UDF:_UDF_788530055.LIST DESC="" ISLIST="YES" TYPE="String" INDEX="902">                            <!--CustomTDL field for Segment-->
                            <UDF:_UDF_788530055 DESC="">Retail</UDF:_UDF_788530055>                            <!--CustomTDL field for Segment-->
                        </UDF:_UDF_788530055.LIST>
                        <UDF:_UDF_788530060.LIST DESC="" ISLIST="YES" TYPE="String" INDEX="907">                            <!--CustomTDL field for Area-->
                            <UDF:_UDF_788530060 DESC="">Local</UDF:_UDF_788530060>                            <!--CustomTDL field for Area-->
                        </UDF:_UDF_788530060.LIST>
                    </VOUCHER>
                </TALLYMESSAGE>
                <TALLYMESSAGE xmlns:UDF="TallyUDF">
                    <COMPANY>
                        <REMOTECMPINFO.LIST MERGE="Yes">
                            <NAME>232444dc-0a9f-41fc-9050-89f8ea29cb48</NAME>                            <!--Remote PARAGsCompny-->
                            <REMOTECMPNAME>PARAG TRADERS (24-25)</REMOTECMPNAME>                            <!--NAME OF PARAGsCompny-->
                            <REMOTECMPSTATE>Madhya Pradesh</REMOTECMPSTATE>                            <!--StateOFParagscompny-->
                        </REMOTECMPINFO.LIST>
                    </COMPANY>
                </TALLYMESSAGE>
                <TALLYMESSAGE xmlns:UDF="TallyUDF">
                    <COMPANY>
                        <REMOTECMPINFO.LIST MERGE="Yes">
                            <NAME>232444dc-0a9f-41fc-9050-89f8ea29cb48</NAME>                            <!--Remote PARAGsCompny-->
                            <REMOTECMPNAME>PARAG TRADERS (24-25)</REMOTECMPNAME>                            <!--NAME OF PARAGsCompny-->
                            <REMOTECMPSTATE>Madhya Pradesh</REMOTECMPSTATE>                            <!--StateOFParagscompny-->
                        </REMOTECMPINFO.LIST>
                    </COMPANY>
                </TALLYMESSAGE>
            </REQUESTDATA>
        </IMPORTDATA>
    </BODY>
</ENVELOPE>
