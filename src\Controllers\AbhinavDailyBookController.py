from config.db_config import AsyncSessionLocal
from src.Models.models import AbhinavDayBook
import base64
import asyncio
from src.Controllers.Logs_Controller import CLogController
from sqlalchemy.exc import SQLAlchemyError
import traceback
from datetime import datetime
from sqlalchemy import select

class CAbhinavDailyBookController:
    """
    Purpose:
        This class handles database operations for the `AbhinavDailyDate` table.
        It provides functionality to create and update records in the `AbhinavDailyDate` table
    """
    @staticmethod
    async def MSInsertDayBookRecord(kwargs: dict, userId: int):
        """
        Adds or updates an AbhinavDayBook record in the database.

        Args:
            dictData (dict): Dictionary containing column-value pairs (at least current_day_data).
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing APIStatusCode, detail, and the ID of the created/updated record.

        Raises:
            Exception: If a database error or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Extract current_day_data from dictData
                new_current_day_data = kwargs.get('current_day_data')
                if new_current_day_data is None:
                    raise ValueError("current_day_data is required in dictData")
                new_current_day_data = base64.b64decode(new_current_day_data)
                # Extract other fields from kwargs (use defaults if not provided)
                new_user_id = kwargs.get('user_id', userId)  # Default to userId from argument
                new_bDevMode = kwargs.get('bDevMode', None)  # Nullable
                new_previous_day_data = kwargs.get('previous_day_data', None)


                # Fetch the most recent record
                query = select(AbhinavDayBook).where(
                (AbhinavDayBook.user_id == new_user_id) &
                (AbhinavDayBook.bDevMode == new_bDevMode)
                )
                
                result = await db.execute(query)
                existing_record = result.scalars().first()

                if existing_record:
                    # Update existing record
                    existing_record.previous_day_data = existing_record.current_day_data
                    existing_record.current_day_data = new_current_day_data
                    existing_record.updated_at = datetime.now()
                    await db.commit()
                    await db.refresh(existing_record)

                    await CLogController.MSWriteLog(userId, "Info", f"AbhinavDayBook record with ID {existing_record.id} updated successfully.")
                    return {
                        "APIStatusCode": 200,
                        "detail": "Record updated successfully",
                        "id": existing_record.id
                    }
                else:
                    # Insert new record with previous_day_data as None
                    new_record = AbhinavDayBook(
                        user_id=new_user_id,
                        bDevMode=new_bDevMode,
                        previous_day_data=None,
                        current_day_data=new_current_day_data
                    )
                    db.add(new_record)
                    await db.commit()
                    await db.refresh(new_record)

                    await CLogController.MSWriteLog(userId, "Info", f"AbhinavDayBook record with ID {new_record.id} created successfully.")
                    return {
                        "APIStatusCode": 200,
                        "detail": "Record created successfully",
                        "id": new_record.id
                    }

            except SQLAlchemyError as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Failed to save/update AbhinavDayBook record to DB")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")
    
    @staticmethod
    async def MSRetrieveDayBookRecord(client_user_id: int, bDevMode: bool | None, userId: int) -> dict:
        """
        Retrieves an AbhinavDayBook record from the database based on user_id and bDevMode.

        Args:
            user_id (int): The ID of the user to filter records.
            bDevMode (bool | None): The development mode flag to filter records (can be None).
            userId (int): The ID of the user performing the operation for logging purposes.

        Returns:
            dict: A dictionary containing APIStatusCode, detail, and the record data (or None if not found).

        Raises:
            Exception: If a database error or unhandled exception occurs.
        """
        async with AsyncSessionLocal() as db:
            try:
                # Build query to fetch record by user_id and bDevMode
                query = select(AbhinavDayBook).where(
                    (AbhinavDayBook.user_id == client_user_id) &
                    (AbhinavDayBook.bDevMode == bDevMode)
                )
                
                result = await db.execute(query)
                record = result.scalars().first()

                if record:
                    # Convert record to dictionary, handling base64 encoding if needed
                    record_data = {
                        "id": record.id,
                        "user_id": record.user_id,
                        "bDevMode": record.bDevMode,
                        "current_day_data": base64.b64encode(record.current_day_data).decode('utf-8') if record.current_day_data else None,
                        "previous_day_data": base64.b64encode(record.previous_day_data).decode('utf-8') if record.previous_day_data else None
                    }
                    await CLogController.MSWriteLog(userId, "Info", f"AbhinavDayBook record with ID {record.id} retrieved successfully.")
                    return record_data
                else:
                    await CLogController.MSWriteLog(userId, "Info", f"No AbhinavDayBook record found for user_id {client_user_id} and bDevMode {bDevMode}.")
                    return None

            except SQLAlchemyError as e:
                await CLogController.MSWriteLog(userId, "Error", f"Failed to retrieve AbhinavDayBook record from DB")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Database error occurred.")
            except Exception as e:
                await CLogController.MSWriteLog(userId, "Error", f"Unhandled exception: {str(e)}")
                await CLogController.MSWriteLog(userId, "Debug", f"Traceback: {traceback.format_exc()}")
                raise Exception("Unhandled error occurred.")