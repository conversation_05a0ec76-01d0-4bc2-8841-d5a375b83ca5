{"plans": {"monthly": [{"Starter": {"ProductID": "prod_QWnxFG4C89Dzc1", "PriceID": "price_1PfkLdD9i0KVpTcad0ne7AV0", "description": "$25.99 / month", "currency": "usd", "amount": 25.99, "pageCount": 75}}, {"Professional": {"ProductID": "prod_QWnzow5dZX39pm", "PriceID": "price_1PfkNnD9i0KVpTcay3uFTXow", "description": "$259.99 / month", "currency": "usd", "amount": 259.99, "pageCount": 1000}}], "yearly": [{"Starter": {"ProductID": "prod_QWnxFG4C89Dzc1", "PriceID": "price_1PfkLdD9i0KVpTcaA0Kqx19m", "description": "$239.88 / year", "currency": "usd", "amount": 239.88, "pageCount": 900}}, {"Professional": {"ProductID": "prod_QWnzow5dZX39pm", "PriceID": "price_1PfkNnD9i0KVpTcabfUFR96q", "description": "$2,399.88 / year", "currency": "usd", "amount": 2399.88, "pageCount": 12000}}]}, "top_up": {"monthly": [{"Starter": {"ProductID": "prod_QWnxFG4C89Dzc1", "PriceID": "price_1PgoPtD9i0KVpTcafEqozDBB", "description": "Price per page for the starter monthly plan", "currency": "usd", "amount": 0.35, "pageCount": 1}}, {"Professional": {"ProductID": "prod_QWnzow5dZX39pm", "PriceID": "price_1Pgp52D9i0KVpTcaGZaN4yim", "description": "Price per page for the Professional monthly plan", "currency": "usd", "amount": 0.26, "pageCount": 1}}], "yearly": [{"Starter": {"ProductID": "prod_QWnxFG4C89Dzc1", "PriceID": "price_1PgoRXD9i0KVpTcaxASOmh8S", "description": "Price per page for the starter yearly plan", "currency": "usd", "amount": 0.27, "pageCount": 1}}, {"Professional": {"ProductID": "prod_QWnzow5dZX39pm", "PriceID": "price_1Pgp5gD9i0KVpTca8BqKFDNX", "description": "Price per page for the Professional yearly plan", "currency": "usd", "amount": 0.2, "pageCount": 1}}]}, "AccountID": "acct_1PUPPJD9i0KVpTca", "STRIPE_CLI": {"code": "extol-mercy-cute-loving", "comment": "> Done! The Stripe CLI is configured for AccuVelocity with account id \nPlease note: this key will expire after 90 days, at which point you'll need to re-authenticate."}}