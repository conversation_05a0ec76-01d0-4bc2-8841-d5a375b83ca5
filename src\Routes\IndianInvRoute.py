import sys

sys.path.append(".")
import socket
import json
import os
import random
import asyncio
import pickle
import string
import base64
import zlib
from fastapi import HTTPException
from src.Controllers.Logs_Controller import CLogController
from typing import Dict
from dotenv import load_dotenv
# import py7zr
import mimetypes
import traceback
from datetime import datetime
from src.middleware.checkAuth import user_required
from fastapi import APIRouter, Depends, UploadFile, File, Query, HTTPException, Form, Body
from src.utilities.helperFunc import CJSONFileReader, Hashing
from typing import List
# Declaring router
IndianInvoiceRoute = APIRouter(tags=['IndianInvoiceProcessDocAPI'],  prefix="/IndianInvTally")
from src.Routes.IndianInTallyController import CIndianInvTallyController
import csv
from src.Controllers.ParagTradersControllers import CHansgrohe_XML, CNexion_XML, CSimpolo_XML, C<PERSON><PERSON>ler_XML, CToto_XML, CGeberit_XML, CAquant_XML, CQuotation
from  src.Controllers.ICDController import CICDController
from  src.Controllers.GwaliaController_XML import CGwalia
from  src.Controllers.PremTextilesController import CPremTextiles
from src.Controllers.TallyIndianInvoiceController import CTallyIndianInvoiceController
from src.Controllers.BankStatementsController import  CProcessBankStatement
from src.Controllers.AbhinavDailyBookController import CAbhinavDailyBookController

from config.db_config import AsyncSessionLocal
from src.Controllers.Tally_Controller import CTallyController
from src.utilities.TallyEmailUtilsV3_Production import EmailAttachmentProcessor
import TallyEmailSender as TallyReportSender
from src.Schemas.Tally_Schemas import  ClientDNReqObj
from src.Controllers.AbhinavInfrabuildController import CAbhinavInfrabuild,CAbhinavInfrabuildGRN, CAbhinavInfrabuildMachineryGRN
from src.Controllers.MultipleVendorRecordHelper import CMultipleVendorRecordHelper
import os
from typing import Optional
from datetime import date, datetime
from fastapi.responses import StreamingResponse
from src.utilities.helperFunc import CJSONFileReader
from src.utilities.AntivirusHelper import  VirusDetected
from src.utilities.helperFunc import Hashing
import traceback
from src.Schemas.Tally_Schemas import VoucherType
from src.Controllers.AVRequestDetailController import CAVRequestDetail
mimetypes.add_type("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet", ".xlsx")
mimetypes.add_type("application/vnd.ms-excel", ".xls")
mimetypes.add_type("application/pdf", ".pdf")
mimetypes.add_type("application/xml", ".xml")

# Specify the path to the .env file
dotenv_path = os.path.join('.env')

# Load the .env file
load_dotenv(dotenv_path)

# Read the value of EXTERNAL_API_SERVER
EXTERNAL_API_SERVER = os.getenv('EXTERNAL_API_SERVER')

from pydantic import BaseModel
from typing import Any

DEVICE_NAME = socket.gethostname()  # Fetch the device name
IP_ADDRESS = socket.gethostbyname(DEVICE_NAME)  # Get the IP address of the device


@IndianInvoiceRoute.post("/process_doc")
async def ProcessFiles( iUserid:int = Depends(user_required),
                        documents: List[UploadFile] = File(...), checksums:str = Form(...), 
    strVoucherType: VoucherType = Query(..., description="Type of the voucher: PV_WITH_INVENTORY or DELIVERY_NOTE"),
                        bTestMode:bool = Query(False, description="Development or Production Server to Use"),
                        strSerializeUserConfig: str = Query(..., description="Version information as a JSON string"), # Accept serialized version info from query parameter
                        lsClientDocMetaData:List = Query(..., description="List fo dictonary that contains filename,type and locatin"),
                        bScanForVirus:bool = Query(True, description="Weather to scan files for viruses and malware, Default to True"), 
                        bIsMultivendorDoc : bool=   Query(False, description="Weather File Content multiple vendor split Algorithm to execute or not , Default to False"),  
                        strSystemName: str = Query("-", description="The name of the client's system (computer) where the application is running."),
                        bEmail=Query(False, description="Enable when process document using Email service")
                    ):
    """
    Input:

        1) iUserid: int
            The user ID of the client making the request. This is automatically retrieved from authentication middleware.

        2) documents: List[UploadFile]
            A list of files uploaded by the client for processing.

        3) checksums: str
            A comma-separated string of checksums corresponding to the uploaded files. Used to verify the integrity of the files.

        4) strVoucherType: VoucherType
            The type of voucher to process. Must be either `PV_WITH_INVENTORY` or `DELIVERY_NOTE`.

        5) bTestMode: bool
            A flag to indicate whether to use the development server (True) or production server (False). Defaults to False.

        6) strSerializeUserConfig: str
            A JSON string containing version information or configuration settings for processing.

        7) lsClientDocMetaData: List
            A list of dictionaries, each containing metadata for a document, including 'filename', 'type', and 'location'.

        8) bScanForVirus: bool
            A flag to indicate whether to scan the uploaded files for viruses and malware. Defaults to True.

        9) bIsMultivendorDoc: bool
            A flag to indicate whether the file contains data for multiple vendors and the split algorithm should be executed. Defaults to False.

        10) strSystemName: str
            The name of the client's system (computer) where the application is running. Defaults to "-".

    Output:

        dict: A dictionary containing the status of the operation, processing time, and a list of successfully processed files.

    Purpose:

        To handle file uploads from clients, validate their integrity using checksums, optionally scan for viruses, and process the files based on the specified voucher type and configuration. The function supports processing files in different environments (test or production) and can handle files with multiple vendors if specified.

    """
    strClientREQID = None
    CReqServerReceivedAt = None
    try:
        CIndianInvTallyController.reset()
        # bIsMultivendorDoc = False
        # if bTestMode:
        bScanForVirus = False # TODO: Take this argument from the license file
        # Config
        dictUsersConfig = CJSONFileReader.read_json_file(CIndianInvTallyController._mStrTDLUserConfigPath)

        # Validate user ID and get data directory
        if str(iUserid) not in dictUsersConfig:
            raise HTTPException(status_code=404, detail="User ID not found in configuration.")

        client_config = dictUsersConfig[str(iUserid)]
        
        CReqServerReceivedAt = datetime.now()
        # Correct datetime formatting
        strCurrentTime = datetime.now().strftime("%H%M%S")

        # Format customer name (remove spaces, convert to uppercase)
        formatted_customer_name = client_config["customerName"].replace(" ", "").upper() if not bTestMode else "AVDEVELOPER" 

        dictExeUserDetails = json.loads(strSerializeUserConfig)  # Deserialize the JSON string to a dictionary
        dictExeUserDetails["CustomerName"] = client_config["customerName"].upper() if not bTestMode else "AVDEVELOPER"
        dictClientUUIDDetails = {}
    
        
        # Construct request ID
        if dictExeUserDetails.get("strClientREQID",None) is None:
            strRandomAlpaNumeric = ''.join(random.choices(string.ascii_uppercase + string.digits, k=6))
            strClientREQID = f"REQ_CS{formatted_customer_name}_TS{strCurrentTime}_UID{strRandomAlpaNumeric}"
        else:
            strClientREQID = dictExeUserDetails.get("strClientREQID")
        
        # Log the version information
        await CLogController.MSWriteLog(iUserid, "INFO", f"Version Information: {dictExeUserDetails}")
        
        await CLogController.MSWriteLog(iUserid, "INFO", f"lsClientDocMetaData Information: {lsClientDocMetaData}") # NOTE: use this when devanshi tdl for attachment given, client location path   print(lsClientDocMetaData) ["{'filename': '782.pdf', 'Type': '.pdf', 'location': 'C:\\\\Users\\\\<USER>\\\\TallyPrime\\\\AccuVelocity\\\\Requests\\\\20250322_124903\\\\782.pdf'}"]
        lsClientDocMetaData=lsClientDocMetaData

        strUserName = dictClientUUIDDetails.get("TallyUserName", "Developer") 
        dictLsClientDocMetaData  = {}
        try:
            if lsClientDocMetaData is not None and lsClientDocMetaData:
                StUploadTimePeriod = f"Upload on {datetime.now().strftime('%d-%b-%y at %H:%M')}"

                LinkDocName=strVoucherType.value
                for dictClientDocMetaData in lsClientDocMetaData:
                    file_info = dictClientDocMetaData
                    file_info = file_info.replace("'", '"')
                    file_info = json.loads(file_info)
                    location = file_info['location']
                    LinkOffered = os.path.dirname(location)
                    strFileName = file_info['filename']
                    TDL = file_info['location']
                    Type=file_info["Type"]
                    strDocCheckSum = file_info.get("checksum","")

                    lsUdfData = None
                    if iUserid == 2 and not bTestMode:
                        lsUdfData=[
                            {
                                "DocType": LinkDocName, # Voucher Type = PV_WITH_INVENTORY, PV_WITHOUT_INVENTORY
                                "DocPath": TDL,
                                "TDL": TDL,
                                "LinkOfferedAgg": "AccuVelocity",
                                "LinkDocName": LinkDocName,
                                "stDocNameLineno": "1.",
                                "StUploadTimePeriod":StUploadTimePeriod,
                                "stUserName": strUserName,
                                "LinkOffered": LinkOffered,
                                "stFullpath": strFileName
                                
                            },
                        ]
                    else:
                        # Else Other Clients Our Own attachment TDL Tags
                        lsUdfData = [
                            {
                                        "LinkOfferedAgg": "AccuVelocity",
                                        "LinkDocName": LinkDocName,
                                        "stDocNameLineno": "1.",
                                        "StUploadTimePeriod":StUploadTimePeriod,
                                        "stUserName": strUserName,
                                        "LinkOffered": LinkOffered,
                                        "stFullpath": strFileName,
                                        "TDL": TDL
                                    },
                        ]
                    if bEmail:
                        pass # TODO: Attachment Provide when It process Document using Email Service
                    else:
                        if strDocCheckSum !="": # Use CheckSum Base Attachment
                            dictLsClientDocMetaData[strDocCheckSum] = lsUdfData
                        else:
                            dictClientDocMetaData[strFileName] = lsUdfData # Discard this filename base Map attachment
        except Exception as e:
            pass   # silent error
        
        await Hashing.VerifyChecksum(checksums=checksums, documents=documents)

        # To do: Clean This code
        if iUserid == 10: # Vedansh Customer: Multi Thread Solution Super Blazing Fast Speed
            objUserRequest = CIndianInvTallyController(strClientREQID = strClientREQID,
                    iUserID=iUserid, 
                    documents=documents, 
                    strVoucherType=strVoucherType,
                    bTestMode=bTestMode,
                    dictLsClientDocMetaData=dictLsClientDocMetaData,
                    dictExeUserDetails=dictExeUserDetails,
                    bScanForVirus=bScanForVirus,
                    bIsMultivendorDoc=bIsMultivendorDoc,
                    strSystemName=strSystemName,
                    strClientTallyUserName = strUserName,
                    CReqServerReceivedAt = CReqServerReceivedAt)
            strZipResFile = await objUserRequest.MProcessIndianInvDocV2()
        elif strVoucherType == VoucherType.RECEIPT_NOTE and ((iUserid==11) or (iUserid==4)) :  
        # if strVoucherType == VoucherType.RECEIPT_NOTE and iUserid==11 :
            # Process GRN
            strZipResFile = await CIndianInvTallyController.MSProcessGRN(
                strClientREQID = strClientREQID,
                dictExeUserDetails=dictExeUserDetails,
                iUserId=iUserid, 
                documents=documents, 
                bTestMode=bTestMode,
                strVoucherType=strVoucherType,
                bScanForVirus=bScanForVirus,
                strSystemName=strSystemName                      
            )
        
        elif strVoucherType == VoucherType.JOURNAL_VOUCHER and ((iUserid==11) ):
            strZipResFile = await CIndianInvTallyController.MSProcessAbhinavImprestJournal(
                strClientREQID = strClientREQID,
                dictExeUserDetails=dictExeUserDetails,
                iUserId=iUserid, 
                documents=documents, 
                bTestMode=bTestMode,
                strVoucherType=strVoucherType,
                bScanForVirus=bScanForVirus,
                strSystemName=strSystemName                 
            )
            
        elif strVoucherType in [ VoucherType.PV_WITH_INVENTORY, VoucherType.PV_WITHOUT_INVENTORY, VoucherType.JOURNAL_VOUCHER , VoucherType.RECEIPT_NOTE]:
            # Process Purchase Invoice and General Voucher
            strZipResFile = await CIndianInvTallyController.MSProcessIndianInvDoc(
                    strClientREQID = strClientREQID,
                    iUserId=iUserid, 
                    documents=documents, 
                    strVoucherType=strVoucherType,
                    bTestMode=bTestMode,
                    dictLsClientDocMetaData=dictLsClientDocMetaData,
                    dictExeUserDetails=dictExeUserDetails,
                    bScanForVirus=bScanForVirus,
                    bIsMultivendorDoc=bIsMultivendorDoc,
                    strSystemName=strSystemName,
                    strClientTallyUserName = strUserName,
                    CReqServerReceivedAt = CReqServerReceivedAt
                )
            
        elif strVoucherType == VoucherType.DELIVERY_NOTE:
            # Process Quotation Documents 
            strZipResFile = await CIndianInvTallyController.MSProcessIndianDeliveryNoteDoc(
                    strClientREQID = strClientREQID,
                    iUserId=iUserid, 
                    documents=documents, 
                    bTestMode=bTestMode,
                    bScanForVirus=bScanForVirus,
                    checksum = checksums,
                    dictExeUserDetails=dictExeUserDetails,
                    strVoucherType=strVoucherType
                )
        elif strVoucherType == VoucherType.BANK_STATEMENT:
            # Process Quotation Documents
            strZipResFile = await CIndianInvTallyController.MSProcessBankStatement(
                    strClientREQID = strClientREQID,
                    iUserId=iUserid, 
                    documents=documents, 
                    strVoucherType=strVoucherType,
                    bScanForVirus=bScanForVirus,
                    strSystemName=strSystemName,
                    bTestMode=bTestMode,
                    dictExeUserDetails=dictExeUserDetails
                )
        
        elif strVoucherType == VoucherType.PURCHASE_ORDER:
            strZipResFile = await CIndianInvTallyController.MSProcessPurchaseOrder(
                    strClientREQID = strClientREQID,
                    dictExeUserDetails=dictExeUserDetails,
                    iUserId=iUserid, 
                    documents=documents, 
                    dictLsClientDocMetaData=dictLsClientDocMetaData,
                    bTestMode=bTestMode,
                    strVoucherType=strVoucherType,
                    bScanForVirus=bScanForVirus,
                    strSystemName=strSystemName
                )
            
        else:
            raise HTTPException(status_code=500, detail=f"Invalid Document Type: {strVoucherType}.")
           
        # Generate checksum for the zip file
        zip_checksum = Hashing.calculate_checksum_from_file(strZipResFile)
        
        # Send the binary zip file with checksum in headers
        file_name = os.path.basename(strZipResFile)
        headers = {
            "Content-Disposition": f"attachment; filename={file_name}",
            "X-Zip-Checksum": zip_checksum
        }
        # AVRecordDetail Table Columns Update
        await CAVRequestDetail.MSOnReqCompleted(iUserId=iUserid, strClientREQID=strClientREQID, CReqServerReceivedAt=CReqServerReceivedAt, dictExeUserDetails=dictExeUserDetails)

        return StreamingResponse(open(strZipResFile, "rb"), media_type="application/zip", headers=headers)

    except VirusDetected as vd:
        # AVRecordDetail Table Columns Update
        await CAVRequestDetail.MSOnReqCompleted(iUserId=iUserid, strClientREQID=strClientREQID, CReqServerReceivedAt=CReqServerReceivedAt, dictExeUserDetails=dictExeUserDetails)
        raise HTTPException(status_code=500, detail=str(vd))

    except Exception as e:
        # AVRecordDetail Table Columns Update
        await CAVRequestDetail.MSOnReqCompleted(iUserId=iUserid, strClientREQID=strClientREQID, CReqServerReceivedAt=CReqServerReceivedAt, dictExeUserDetails=dictExeUserDetails)
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")


@IndianInvoiceRoute.post("/client_import_tally_response")
async def ProcessXMLResponseFiles(iUserid:int = Depends(user_required),
                       documents: List[UploadFile] = File(...), checksums:str = Form(...), strClientDetail: str = Form(default="{}")):
    """
        Input:

            1) iUserid: int
            The user ID of the client making the request. This is automatically retrieved from authentication middleware.

            2) documents: List[UploadFile]
            A list of files uploaded by the client for processing. Each file should be a valid XML.

            3) checksums: str
            A comma-separated string of checksums corresponding to the uploaded files. Used to verify the integrity of the files.

        Output:

            dict: A dictionary containing the status of the operation, received time, and a list of successfully processed files.

        Purpose:

            To handle the `/client_import_tally_response` endpoint by:
            - Validating the integrity of uploaded files using their checksums.
            - Processing the files via `MSXMLImportedResponse` to store them and generate responses.
            - Returning a success or failure response to the client.
    """
    try:
        dictClientDetail = json.loads(strClientDetail)
        await CLogController.MSWriteLog(iUserid, "Info",f"dictClientDetail - {dictClientDetail}")  
        await Hashing.VerifyChecksum(checksums=checksums, documents=documents)
        dictAVProcessResponse = None
        # Process documents
        dictAVProcessResponse = await CIndianInvTallyController.MSXMLImportedResponse(
            iUserId=iUserid, 
            documents=documents,
            dictClientDetail = dictClientDetail
        )
        return dictAVProcessResponse
    except Exception as e:
        await CLogController.MSWriteLog(iUserid, "ERROR",f"Error - {str(traceback.format_exc())}")
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")


@IndianInvoiceRoute.post("/client_request_delivered_at")
async def SetClientRequestDeliveredAt(iUserid:int = Depends(user_required),
                       strClientDetail: str = Query(default="{}")):
    """
        Input:

            1) iUserid: int
            The user ID of the client making the request. This is automatically retrieved from authentication middleware.

            2) documents: List[UploadFile]
            A list of files uploaded by the client for processing. Each file should be a valid XML.

            3) checksums: str
            A comma-separated string of checksums corresponding to the uploaded files. Used to verify the integrity of the files.

        Output:

            dict: A dictionary containing the status of the operation, received time, and a list of successfully processed files.

        Purpose:

            To handle the `/client_import_tally_response` endpoint by:
            - Validating the integrity of uploaded files using their checksums.
            - Processing the files via `MSXMLImportedResponse` to store them and generate responses.
            - Returning a success or failure response to the client.
    """
    try:
        dictClientDetail = json.loads(strClientDetail)
        await CLogController.MSWriteLog(iUserid, "Info",f"dictClientDetail - {dictClientDetail}")  
        # Delivered At 
        await CAVRequestDetail.MSUpdateTimeFieldsForRequestID(iUserId=iUserid, strClientREQID = dictClientDetail.get("strClientREQID"),CReqDeliveredTimeAt=dictClientDetail.get("CReqDeliveredTimeAt"))
    except Exception as e:
        await CLogController.MSWriteLog(iUserid, "ERROR",f"Error - {str(traceback.format_exc())}")
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")

@IndianInvoiceRoute.post("/day_book_update")
async def UpdateDataDailyBook(iUserid:int = Depends(user_required),
                       data:Dict[str, Any] = Body(...)):
    """
        Input:

            1) iUserid: int
            The user ID of the client making the request. This is automatically retrieved from authentication middleware.

            2) documents: List[UploadFile]
            A list of files uploaded by the client for processing. Each file should be a valid XML.

            3) checksums: str
            A comma-separated string of checksums corresponding to the uploaded files. Used to verify the integrity of the files.

        Output:

            dict: A dictionary containing the status of the operation, received time, and a list of successfully processed files.

        Purpose:

            To handle the `/client_import_tally_response` endpoint by:
            - Validating the integrity of uploaded files using their checksums.
            - Processing the files via `MSXMLImportedResponse` to store them and generate responses.
            - Returning a success or failure response to the client.
    """
    try:
        file_content = data['binary_file_content']
        iUserid = data['iUserId']
        bDevMode = data['bDevMode']
        kwargs = {
            "current_day_data" : file_content,
            "user_id":iUserid,
            "bDevMode":bDevMode
        }
       
        await CAbhinavDailyBookController.MSInsertDayBookRecord(userId=iUserid,kwargs=kwargs)
    except Exception as e:
        await CLogController.MSWriteLog(iUserid, "ERROR",f"Error - {str(traceback.format_exc())}")
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later.")

@IndianInvoiceRoute.post("/get_day_book")
async def GetDataDailyBook(iUserid:int,
                       data:Dict[str, Any] = Body(...)):
    """
        Input:

            1) iUserid: int
            The user ID of the client making the request. This is automatically retrieved from authentication middleware.

            2) documents: List[UploadFile]
            A list of files uploaded by the client for processing. Each file should be a valid XML.

            3) checksums: str
            A comma-separated string of checksums corresponding to the uploaded files. Used to verify the integrity of the files.

        Output:

            dict: A dictionary containing the status of the operation, received time, and a list of successfully processed files.

        Purpose:

            To handle the `/client_import_tally_response` endpoint by:
            - Validating the integrity of uploaded files using their checksums.
            - Processing the files via `MSXMLImportedResponse` to store them and generate responses.
            - Returning a success or failure response to the client.
    """
    try:
       client_user_id = data["client_user_id"]
       bGetPrev = data.get('bGetPrev',False)
       bDevMode = data.get('bDevMode',False)
       result = await CAbhinavDailyBookController.MSRetrieveDayBookRecord(client_user_id = client_user_id, bDevMode = bDevMode, userId = iUserid)
       if result is  None:
            return {"current_day_data": None, "previous_day_data":None}
       compressed_data = base64.b64decode(result['current_day_data'])
       decompressed_data = zlib.decompress(compressed_data)
       current_data_str = decompressed_data.decode('utf-8')
       if bGetPrev:
            compressed_data = base64.b64decode(result['previous_day_data'])
            decompressed_data = zlib.decompress(compressed_data)
            prev_data_str = decompressed_data.decode('utf-8')
            return {"current_day_data": current_data_str, "previous_day_data":prev_data_str}
       return {"current_day_data": current_data_str, "previous_day_data":None}
    except Exception as e:
        await CLogController.MSWriteLog(iUserid, "ERROR",f"Error - {str(traceback.format_exc())}")
        if hasattr(e, 'status_code') and hasattr(e, 'detail'):
            raise HTTPException(status_code=e.status_code, detail=e.detail)
        else:
            raise HTTPException(status_code=500, detail="We are unable to process your request at this time. Please try again later." + str(e))

async def main():
    temp = await GetDataDailyBook(iUserid=11, data = {"client_user_id" : 11})
      

@IndianInvoiceRoute.get("/avrequests")
async def get_av_requests(
    user_id: int = Depends(user_required),
    start_date: Optional[date] = Query(None),
    end_date: Optional[date] = Query(None),
    strVoucherType: Optional[str] = Query(None),
    tallySuccessFullyPunchIn: Optional[bool] = Query(None),
    AVTallySuccessStatus: Optional[str] = Query(None),
    strSystemUserName: Optional[str] = Query(None),
):
    return await CAVRequestDetail.MSGetAVRequestByUserID(
        user_id=user_id,
        start_date=start_date,
        end_date=end_date,
        strVoucherType=strVoucherType,
        tallySuccessFullyPunchIn=tallySuccessFullyPunchIn,
        AVTallySuccessStatus=AVTallySuccessStatus,
        strSystemUserName=strSystemUserName,
    )

if "__main__" == __name__:
    # asyncio.run(main())
    asyncio.run(main())
