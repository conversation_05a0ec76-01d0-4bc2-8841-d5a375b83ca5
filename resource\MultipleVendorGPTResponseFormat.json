{"ICD": {"type": "json_schema", "json_schema": {"name": "ICD", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["Pegasus", "hind terminals", "<PERSON><PERSON> ha<PERSON>ra", "msc agency", "kotak global logistics pvt ltd", "goodrich maritime private limited", "anl singapore pte. ltd", "anl singapore pte. ltd. c/o ccai", "united liner shipping services llp", "container corporation of india ltd.", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}, "Gwalia": {"type": "json_schema", "json_schema": {"name": "Gwalia", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["sygnia brandworks llp", "sheeba dairy", "bhavya sales company", "agarwal suppliers", "shri ram enterprises", "the ahmedabad coop dept stories ltd", "shree foods", "r k trading company", "r k plast (india)", "uma converter ltd", "radhe agency", "amar traders", "<PERSON><PERSON><PERSON> international", "gas guys", "zeel pest solution llp", "shri a<PERSON>hant sales agency", "diamond private security investigation services", "shree dutt krupa lamination", "nakoda trading", "shivambica sales corporation", "sachi products", "govind tours and travels", "i i traders", "sovereign sales", "dharm sales company", "palladium", "satyam steel house", "savnath enterprise llp", "unicorn enterprise", "gurukrupa traders", "grains and more", "shivay enterprise", "yash tradelink", "karnavati", "regenta m foods", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}, "Parag Traders": {"type": "json_schema", "json_schema": {"name": "ParagTraders", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["<PERSON><PERSON><PERSON>", "Nexion", "Hansgrohe", "<PERSON><PERSON>", "Toto", "<PERSON><PERSON><PERSON><PERSON>", "PowerGrace", "Aquant", "Icon", "rollin logistics", "kiron electricals", "airtel", "bharat sanchar nigam limited", "mangal hardware and paints", "gulab hardware stores", "mohit enterprises", "sainath engineering services", "cd multi media gallery", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}, "AVDEVELOPER": {"type": "json_schema", "json_schema": {"name": "AVDEVELOPER", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["<PERSON><PERSON><PERSON>", "Icon", "Aquant", "PowerGrace", "<PERSON><PERSON><PERSON><PERSON>", "Toto", "<PERSON><PERSON>", "Hansgrohe", "Nexion", "Pegasus", "satyam traders", "ketan engineering works", "sygnia brandworks llp", "sheeba dairy", "bhavya sales company", "hind terminals", "<PERSON><PERSON> ha<PERSON>ra", "somani brothers", "the ahmedabad coop dept stories ltd", "shri ram enterprises", "agarwal suppliers", "shree foods", "msc agency", "kotak global logistics pvt ltd", "goodrich maritime private limited", "anl singapore pte. ltd", "anl singapore pte. ltd. c/o ccai", "united liner shipping services llp", "container corporation of india ltd.", "the vedansh international school", "r k trading company", "r k plast (india)", "uma converter ltd", "radhe agency", "amar traders", "<PERSON><PERSON><PERSON> international", "gas guys", "zeel pest solution llp", "shri a<PERSON>hant sales agency", "diamond private security investigation services", "shree dutt krupa lamination", "nakoda trading", "shivambica sales corporation", "sachi products", "govind tours and travels", "i i traders", "sovereign sales", "dharm sales company", "south asia fm ltd", "guru kripa petroleum", "trophy wala", "bhagwati trading co", "a.a.k<PERSON> & sons", "narayan marketing", "v.s.agencies", "ashkelon enterprises", "r k enterprises", "sanghi brothers", "<PERSON><PERSON><PERSON> kirana stores", "ronak rudra security & management services", "<PERSON><PERSON><PERSON><PERSON> bahi<PERSON>ya", "inifinty cars pvt ltd", "mendwell agencies", "airtel", "bsnl", "kiron electricals", "rollin logistics", "bharat sanchar nigam limited", "yash tradelink", "shivay enterprise", "grains and more", "gurukrupa traders", "unicorn enterprise", "savnath enterprise llp", "palladium", "satyam steel house", "karnavati", "regenta m foods", "mangal hardware and paints", "gulab hardware stores", "mohit enterprises", "sainath engineering services", "cd multi media gallery", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}, "Vedansh School": {"type": "json_schema", "json_schema": {"name": "VedanshSchool", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["south asia fm ltd", "guru kripa petroleum", "trophy wala", "r k enterprises", "sanghi brothers", "<PERSON><PERSON><PERSON> kirana stores", "ronak rudra security & management services", "<PERSON><PERSON><PERSON><PERSON> bahi<PERSON>ya", "inifinty cars pvt ltd", "mendwell agencies", "the vedansh international school", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}, "Abhinav InfraBuild": {"type": "json_schema", "json_schema": {"name": "AbhinavInfraBuild", "strict": true, "schema": {"type": "object", "properties": {"VendorNames": {"type": "array", "description": "An array that holds the vendor name or seller name extracted from each invoice page or tax journal voucher page", "items": {"type": "object", "properties": {"PageNumber": {"type": "integer", "description": "The page number where the vendor name or seller was identified."}, "VendorName": {"type": "string", "description": "The vendor name or seller name extracted from the page.", "enum": ["bhagwati trading co", "a.a.k<PERSON> & sons", "narayan marketing", "v.s.agencies", "ashkelon enterprises", "Unknown"]}, "DocumentType": {"type": "string", "description": "The Type of Document, Identify from content", "enum": ["Tax Invoice", "Tax Journal Voucher"]}, "InvoiceOrDocumentNumber": {"type": "integer", "description": "Unique identification number of Invoice or Document"}}, "required": ["PageNumber", "VendorName", "DocumentType", "InvoiceOrDocumentNumber"], "additionalProperties": false}}}, "required": ["VendorNames"], "additionalProperties": false, "description": "DO NOT ASSUME ANYTHING."}}}}