# tally_templates.py

import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
from typing import List, Optional, Dict, Any
from pydantic import BaseModel, Field
import traceback
from src.Schemas.Tally_XML_Schema import BatchAllocationSchema, AccountingAllocationSchema, RateDetailSchema

# ============================================================================
# Pydantic Schemas
# ============================================================================

class CompanyInfoSchema(BaseModel):
    company_name: str = Field(..., description="Name of the Tally Company")
    gst_registration_type: str = Field("Regular", description="GST Registration Type")
    gst_in: Optional[str] = Field(None, description="GSTIN of the company")
    state_name: Optional[str] = Field(None, description="State Name of the company")
    country_name: str = Field("India", description="Country Name of the company")


class InventoryEntrySchema(BaseModel):
    stockitemname: str
    gst_ovrd_ineligible_itc: Optional[str] = "Not Applicable"
    gst_ovrd_is_revcharge_appl: Optional[str] = "Not Applicable"
    gst_ovrd_taxability: Optional[str] = "Taxable"
    gstsourcetype: Optional[str] = "Ledger"
    gstledgersource: Optional[str] = None  # e.g. "GST INTERSTATE PURCHASE (18%)"
    hsnsourcetype: Optional[str] = "Stock Item"
    hsnitemsource: Optional[str] = None  # typically same as stockitemname
    gst_ovrd_stored_nature: Optional[str] = "Interstate Purchase - Taxable" # None Default prefarrable
    gst_ovrd_type_of_supply: Optional[str] = "Goods"
    gstrate_infer_applicability: Optional[str] = "As per Masters/Company"
    gst_hsnname: Optional[str]=None  # e.g. "********"
    gst_hsn_infer_applicability: Optional[str] = "As per Masters/Company"
    is_deemed_positive: bool = True
    rate: str  # e.g. "4278.05/Box"
    discount: Optional[float]=0.0  # e.g. 20.0
    amount: float
    actual_qty: str  # e.g. "214 Box"
    billed_qty: str  # e.g. "214 Box"
    description:Optional[str] = ""
    batch_allocations: List[BatchAllocationSchema] = Field(default_factory=list)
    accounting_allocations: List[AccountingAllocationSchema] = Field(default_factory=list)
    rate_details: List[RateDetailSchema] = Field(default_factory=list)


class LedgerEntrySchema(BaseModel):
    ledger_name: str = Field(..., description="Name of the ledger")
    amount: float = Field(..., description="Amount for the ledger entry")
    is_deemed_positive: bool = Field(..., description="Whether the entry is deemed positive")
    is_party_ledger: bool = Field(False, description="Is this a party ledger?")
    gst_taxability: Optional[str] = Field(None, description="GST override taxability (e.g. 'Taxable')")
    gst_type_of_supply: Optional[str] = Field(None, description="GST type of supply (e.g. 'Services')")
    cost_center_category: Optional[str] = Field(
        None, description="Cost center category (e.g. '2024-2025')"
    )
    cost_center_allocations: Optional[List[Dict[str, Any]]] = Field(
        None,
        description="List of cost center allocations, each as a dict with keys 'name' and 'amount'",
    )
    bill_allocation: Optional[Dict[str, Any]] = Field(
        None,
        description="Bill allocation dictionary with keys 'name', 'billtype', and 'amount'",
    )
    inventory_allocation: Optional[InventoryEntrySchema] = None
    batch_allocation: Optional[BatchAllocationSchema] = None
    str_narration : Optional[str] = None

    


class TallyJournalVoucherInputSchema(BaseModel):
    company_info: CompanyInfoSchema
    voucher_date: str = Field(..., description="Voucher date in format YYYYMMDD")
    narration: str = Field(..., description="Narration for the voucher")
    reference: str = Field(..., description="Reference for the voucher")
    voucher_number: str = Field(..., description="Voucher number")
    voucher_type: Optional[str] = Field("AV Tax Journal", description="Voucher type")
    numbering_style: Optional[str] = Field("Automatic (Manual Override)", description="Numbering style")
    effective_date: Optional[str] = Field(None, description="Effective date for the voucher (defaults to voucher_date)")
    ledger_entries: List[LedgerEntrySchema] = Field(..., description="List of ledger entries for the voucher")
    udf_data: Optional[List[Dict[str, Any]]] = None  # Dynamic UDF values
    str_costcentre :Optional[str] = None
    reference_date : Optional[str] = Field(None, description="Reference date for the voucher (defaults to voucher_date)") 

# ============================================================================
# Base Template Class
# ============================================================================

class CTallyTemplate:
    """
    Base class for generating a Tally XML envelope.
    Contains methods to build the common HEADER and BODY.
    """

    def __init__(self, company_name: str):
        self.company_name = company_name
        self.envelope: Optional[Element] = None
        self.body: Optional[Element] = None
        self.tally_message: Optional[Element] = None

    def build_envelope(self) -> Element:
        self.envelope = Element("ENVELOPE")
        self._build_header(self.envelope)
        self._build_body(self.envelope)
        return self.envelope

    def _build_header(self, parent: Element) -> None:
        header = SubElement(parent, "HEADER")
        SubElement(header, "VERSION").text = "5"
        SubElement(header, "TALLYREQUEST").text = "Import"
        SubElement(header, "TYPE").text = "Data"
        SubElement(header, "ID").text = "Vouchers"

    def _build_body(self, parent: Element) -> None:
        self.body = SubElement(parent, "BODY")
        desc = SubElement(self.body, "DESC")
        static_vars = SubElement(desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_name

        data = SubElement(self.body, "DATA")
        self.tally_message = SubElement(data, "TALLYMESSAGE",{"xmlns:UDF": "TallyUDF"})

    def to_string(self, pretty: bool = False) -> str:
        if self.envelope is None:
            self.build_envelope()
        raw_str = tostring(self.envelope, encoding="utf-8").decode("utf-8")
        if not pretty:
            return raw_str
        else:
            import xml.dom.minidom
            dom = xml.dom.minidom.parseString(raw_str)
            return dom.toprettyxml(indent="    ")



# ============================================================================
# Journal Voucher Template Class
# ============================================================================

class CTallyJournalVoucherTemplate(CTallyTemplate):
    """
    Template for a Journal Voucher XML.
    Uses a Pydantic schema (TallyJournalVoucherInputSchema) to receive input.
    """

    def __init__(self, input_data: TallyJournalVoucherInputSchema):
        super().__init__(company_name=input_data.company_info.company_name)
        self.voucher_date = input_data.voucher_date
        self.narration = input_data.narration
        self.reference = input_data.reference
        self.voucher_number = input_data.voucher_number
        self.voucher_type = input_data.voucher_type
        self.numbering_style = input_data.numbering_style
        self.effective_date = input_data.effective_date or self.voucher_date
        self.ledger_entries = [entry.dict() for entry in input_data.ledger_entries]
        self.udf_data = input_data.udf_data
        self.str_costcentre = input_data.str_costcentre
        self.reference_date = input_data.reference_date

    def build_voucher(self) -> Element:
        """
        Build the <VOUCHER> element and append it to the tally message.
        """

        voucher = SubElement(self.tally_message, "VOUCHER")
        SubElement(voucher, "DATE").text = self.voucher_date
        SubElement(voucher, "NARRATION").text = self.narration
        SubElement(voucher, "REFERENCE").text = self.reference
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "NUMBERINGSTYLE").text = self.numbering_style
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "PERSISTEDVIEW").text = "Accounting Voucher View"
        SubElement(voucher, "VCHENTRYMODE").text = "As Voucher"
        SubElement(voucher, "VOUCHERTYPEORIGNAME").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "EFFECTIVEDATE").text = self.effective_date
        if self.str_costcentre:
            SubElement(voucher, "COSTCENTRENAME").text =  self.str_costcentre
        if self.reference_date:
            SubElement(voucher, "REFERENCEDATE").text =  self.reference_date
        # NOTE: Whenever Cost Center is provided, add it to the voucher and set ISCOSTCENTRE to Yes
        if self.str_costcentre:
            SubElement(voucher, "COSTCENTRENAME").text =  self.str_costcentre
            SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        # Add ledger entries
        for ledger in self.ledger_entries:
            self._add_ledger_entry(voucher, ledger)

        # Add UDF entries if provided
        if self.udf_data is not None:
            self.add_udf_entries(voucher)

        return voucher


  

    def _add_ledger_entry(self, voucher: Element, ledger: Dict[str, Any]) -> None:
        """
        Appends a <LEDGERENTRIES.LIST> node with all provided details.
        """
        ledger_el = SubElement(voucher, "LEDGERENTRIES.LIST")
        
        SubElement(ledger_el, "LEDGERNAME").text = ledger.get("ledger_name", "")
        if ledger.get("gst_taxability"):
            SubElement(ledger_el, "GSTOVRDNTAXABILITY").text = ledger["gst_taxability"]
        if ledger.get("gst_type_of_supply"):
            SubElement(ledger_el, "GSTOVRDNTYPEOFSUPPLY").text = ledger["gst_type_of_supply"]
        SubElement(ledger_el, "ISDEEMEDPOSITIVE").text = "Yes" if ledger.get("is_deemed_positive", True) else "No"
        SubElement(ledger_el, "ISPARTYLEDGER").text = "Yes" if ledger.get("is_party_ledger", False) else "No"
        amount_str = str(ledger.get("amount", "0"))
        SubElement(ledger_el, "AMOUNT").text = amount_str
        SubElement(ledger_el, "VATEXPAMOUNT").text = amount_str
        if ledger.get("str_narration"):
            SubElement(ledger_el, "NARRATION").text = ledger.get("str_narration")
        if ledger.get("inventory_allocation"):
            dictInventory = ledger.get("inventory_allocation")
            inventory_tag = SubElement(ledger_el, "INVENTORYALLOCATIONS.LIST")
            SubElement(inventory_tag, "STOCKITEMNAME").text = dictInventory.get("stockitemname", "")
            SubElement(inventory_tag, "RATE").text = str(dictInventory.get("rate", ""))
            SubElement(inventory_tag, "AMOUNT").text = str(dictInventory.get("amount", ""))
            SubElement(inventory_tag, "ACTUALQTY").text = dictInventory.get("actual_qty", "")
            SubElement(inventory_tag, "BILLEDQTY").text = dictInventory.get("billed_qty", "")
            
            if ledger.get("cost_center_allocations") and ledger.get("cost_center_category"):
                cat_alloc = SubElement(inventory_tag, "CATEGORYALLOCATIONS.LIST")
                SubElement(cat_alloc, "CATEGORY").text = ledger.get("cost_center_category", "")
                SubElement(cat_alloc, "ISDEEMEDPOSITIVE").text = "Yes" if ledger.get("is_deemed_positive", True) else "No"
                for alloc in ledger["cost_center_allocations"]:
                    cc_alloc = SubElement(cat_alloc, "COSTCENTREALLOCATIONS.LIST")
                    SubElement(cc_alloc, "NAME").text = alloc.get("name", "")
                    SubElement(cc_alloc, "AMOUNT").text = str(alloc.get("amount", 0))
            
            if ledger.get("batch_allocation"):
                dictBatch = ledger.get("batch_allocation")
                batch_alloc = SubElement(inventory_tag, "BATCHALLOCATIONS.LIST")
                SubElement(batch_alloc, "GODOWNNAME").text = dictBatch.get("godownname", "")
                SubElement(batch_alloc, "BATCHNAME").text = dictBatch.get("batchname", "")
                SubElement(batch_alloc, "TRACKINGNUMBER").text = dictBatch.get("trackingnumber", "")
                SubElement(batch_alloc, "AMOUNT").text = str(dictBatch.get("amount", ""))
                SubElement(batch_alloc, "ACTUALQTY").text = dictBatch.get("actual_qty", "")
                SubElement(batch_alloc, "BILLEDQTY").text = dictBatch.get("billed_qty", "")
                
            
        else:
            # Add cost center allocations if provided
            if ledger.get("cost_center_allocations") and ledger.get("cost_center_category"):
                cat_alloc = SubElement(ledger_el, "CATEGORYALLOCATIONS.LIST")
                SubElement(cat_alloc, "CATEGORY").text = ledger.get("cost_center_category", "")
                SubElement(cat_alloc, "ISDEEMEDPOSITIVE").text = "Yes" if ledger.get("is_deemed_positive", True) else "No"
                for alloc in ledger["cost_center_allocations"]:
                    cc_alloc = SubElement(cat_alloc, "COSTCENTREALLOCATIONS.LIST")
                    SubElement(cc_alloc, "NAME").text = alloc.get("name", "")
                    SubElement(cc_alloc, "AMOUNT").text = str(alloc.get("amount", 0))
            # Add bill allocation if provided
            if ledger.get("bill_allocation"):
                bill_alloc = SubElement(ledger_el, "BILLALLOCATIONS.LIST")
                SubElement(bill_alloc, "NAME").text = ledger["bill_allocation"].get("name", "")
                SubElement(bill_alloc, "BILLTYPE").text = ledger["bill_allocation"].get("billtype", "")
                SubElement(bill_alloc, "AMOUNT").text = str(ledger["bill_allocation"].get("amount", 0))

    def add_udf_entries(self, voucher):
        # Initialize the base index
        base_index = 1010  # Starting index for UDF fields

        # Loop through each dictionary in udf_data
        for idx, data in enumerate(self.udf_data):
            base_index = 1010  # Starting index for UDF fields
            # Create UDF:LINKOFFEREDAGG.LIST element
            link_offered_agg = SubElement(voucher, "UDF:LINKOFFEREDAGG.LIST", {
                "DESC": "`LinkOfferedAgg`",
                "INDEX": str(base_index)  # Incrementing index per item
            })

            # Add LinkOffered field
            link_offered = data.get("LinkOffered")
            if link_offered:
                link_offered_element = SubElement(link_offered_agg, "UDF:LINKOFFERED.LIST", {
                    "DESC": "`Link Offered`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 1)
                })
                SubElement(link_offered_element, "UDF:LINKOFFERED", {"DESC": "`Link Offered`"}).text = link_offered

            # Add LinkDocName field
            link_doc_name = data.get("LinkDocName")
            if link_doc_name:
                link_doc_name_element = SubElement(link_offered_agg, "UDF:LINKDOCNAME.LIST", {
                    "DESC": "`LinkDocName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 2)
                })
                SubElement(link_doc_name_element, "UDF:LINKDOCNAME", {"DESC": "`LinkDocName`"}).text = link_doc_name

            # Add stDocNameLineno field
            st_doc_name_lineno = data.get("stDocNameLineno")
            if st_doc_name_lineno:
                st_doc_name_lineno_element = SubElement(link_offered_agg, "UDF:STDOCNAMELINENO.LIST", {
                    "DESC": "`stDocNameLineno`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 3)
                })
                SubElement(st_doc_name_lineno_element, "UDF:STDOCNAMELINENO", {"DESC": "`stDocNameLineno`"}).text = st_doc_name_lineno

            # Add StUploadTimePeriod field
            st_upload_time_period = data.get("StUploadTimePeriod")
            if st_upload_time_period:
                st_upload_time_period_element = SubElement(link_offered_agg, "UDF:STUPLOADTIMEPERIOD.LIST", {
                    "DESC": "`StUploadTimePeriod`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 6)
                })
                SubElement(st_upload_time_period_element, "UDF:STUPLOADTIMEPERIOD", {"DESC": "`StUploadTimePeriod`"}).text = st_upload_time_period

            # Add stUserName field
            st_user_name = data.get("stUserName")
            if st_user_name:
                st_user_name_element = SubElement(link_offered_agg, "UDF:STUSERNAME.LIST", {
                    "DESC": "`stUserName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 7)
                })
                SubElement(st_user_name_element, "UDF:STUSERNAME", {"DESC": "`stUserName`"}).text = st_user_name

            # Add stFullpath field
            st_fullpath = data.get("stFullpath")
            if st_fullpath:
                st_fullpath_element = SubElement(link_offered_agg, "UDF:STFULLPATH.LIST", {
                    "DESC": "`stFullpath`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 8)
                })
                SubElement(st_fullpath_element, "UDF:STFULLPATH", {"DESC": "`stFullpath`"}).text = st_fullpath

            # Add TDL field
            tdl = data.get("TDL")
            if tdl:
                tdl_element = SubElement(link_offered_agg, "UDF:TDL.LIST", {
                    "DESC": "`TDL`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 9)
                })
                SubElement(tdl_element, "UDF:TDL", {"DESC": "`TDL`"}).text = tdl

    def to_xml(self, pretty: bool = True) -> str:
        """
        Build the complete XML and return as a string.
        """
        try:
            self.build_envelope()
            self.build_voucher()

            return self.to_string(pretty=pretty)
        except Exception as e:
            print("Error occur in creating xml : ", traceback.format_exc())
# ============================================================================
# Example Usage
# ============================================================================

if __name__ == "__main__":
    # Example for Journal Voucher Template

    # Create input using Pydantic schemas
    journal_input = TallyJournalVoucherInputSchema(
        company_info=CompanyInfoSchema(
            company_name="FAIRDEAL INTERNATIONAL",
            gst_in="24AAACG5535F1ZY",
            state_name="Gujarat"
        ),
        voucher_date="20250131",
        narration=( "BEING INVOICE NO. 2024-25/DD/1030 DATE 14.10.2024 RCVD AGST JOB NO. "
                    "E-03807, E-03808 A/C VE COMMERCIAL VEHICLES LTD AGST CONT. NO. "
                    "TCLU1730911/40 ( TDS DED. ON RS. 8,330.00@2%= 167 ) PICD"),
        reference="2024-25/DD/1030 dt. 14-Oct-24",
        voucher_number="AV/Purc/31012025-152932",
        ledger_entries=[
            LedgerEntrySchema(
                ledger_name="ICD Expense",
                amount=-8330.0,
                is_deemed_positive=True,
                is_party_ledger=False,
                gst_taxability="Taxable",
                gst_type_of_supply="Services",
                cost_center_category="2024-2025",
                cost_center_allocations=[
                    {"name": "E-03807", "amount": -4165.0},
                    {"name": "E-03808", "amount": -4165.0},
                ]
            ),
            LedgerEntrySchema(
                ledger_name="ITC CGST @ 6%",
                amount=-499.8,
                is_deemed_positive=True,
                is_party_ledger=False
            ),
            LedgerEntrySchema(
                ledger_name="ITC SGST @ 6%",
                amount=-499.8,
                is_deemed_positive=True,
                is_party_ledger=False
            ),
            LedgerEntrySchema(
                ledger_name="ROUND OFF",
                amount=-0.4,
                is_deemed_positive=True,
                is_party_ledger=False
            ),
            LedgerEntrySchema(
                ledger_name="PEGASUS INLAND CONTAINER DEPOT PVT. LTD.",
                amount=9163.0,
                is_deemed_positive=False,
                is_party_ledger=True,
                bill_allocation={
                    "name": "2024-25/DD/1030/E-03807 TO E-03808",
                    "billtype": "New Ref",
                    "amount": 9163.0
                }
            ),
            LedgerEntrySchema(
                ledger_name="TDS ON CONTRACT PAYABLE (FOR COMPANY)",
                amount=167,
                is_deemed_positive=False,
                is_party_ledger=True
            ),
        ]
    )

    journal_template = CTallyJournalVoucherTemplate(journal_input)
    xml_output_journal = journal_template.to_xml(pretty=True)
    
    with open("journal_voucher.xml", "w", encoding="utf-8") as file:
        file.write(xml_output_journal)
    
    # print("----- Journal Voucher XML -----")
    # print(xml_output_journal)
