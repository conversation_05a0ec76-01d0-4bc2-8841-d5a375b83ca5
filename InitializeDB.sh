#!/bin/bash
# Debugging: Print the current directory
echo "Current directory: $(pwd)"

# Activate the virtual environment
echo "Activating virtual environment..."
source ./5_Env/bin/activate

# Debugging: Check the Python executable in the virtual environment
echo "Python executable:"
PYTHON_EXECUTABLE=./5_Env/bin/python
echo $PYTHON_EXECUTABLE
$PYTHON_EXECUTABLE --version

# Run the database initialization script
echo "Running database initialization script..."
$PYTHON_EXECUTABLE ./src/utilities/startupUtils.py

echo "Database initialization completed."
