# Logs
logs
*.log
BankStatementSrc\Logs
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
BankStatementSrc\.gitignore
envProdBankStatPred
node_modules
Backup
dist
dist-ssr
*.local

# Editor directories and files
.vscode/*
!.vscode/extensions.json
.idea
.DS_Store
*.suo
*.ntvs*
*.njsproj
*.sln
*.sw?

#Serverside
Server/5_Env
/6_GitIgnoregit add
*.pyc
5_Env/

5_Env/
client/node_modules/
config/__pycache__/
logs/
src/Controllers/__pycache__/
src/Models/__pycache__/
src/Routes/__pycache__/
src/Schemas/__pycache__/
src/__pycache__/
src/middleware/__pycache__/
src/utilities/__pycache__/
validation_errors.log
GitIgnore
GitIgnore_1
alembic/
alembic/env.py
resource\GoogleDocOCRServiceConfig.json
*.env
client\.env
logs.txt
tempCodeRunnerFile.py

# Tally Related Files
TallyClients
5_Env_old
Data/tally_request_object/
envProdBankStatPred
BankStatementSrc
BankStatementSrcOld
BankStatementSrc/*.xlsx
BankStatementSrcOld/*.xlsx
TestXml/XmlFiles/Invoiceno_359_xmlfile.xml
TestXml/apiResponse/359_gptResponse.json
Data/test_gwalia/*
StockItemMatchingWithAgent/*

# XML Automation
XMLAutomation_generalSchema/*
PWIMatchingOpenAI/*
PurchaseWithInventoryXMLAutomationGeneralSchema/*
PWIMatchingOpenAI/__pycache__/*
prodBankStatementPrediction/*

Reference
Logs
VEDANSHSCHOOL