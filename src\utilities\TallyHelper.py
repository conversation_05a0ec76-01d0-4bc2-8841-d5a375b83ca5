import hashlib
import sys

from src.Controllers.CustomLogger import CLogger
sys.path.append("")

import os
import json
from src.Controllers.ParagTradersControllers import CHansgrohe_XML, CNexion_XML, CSimpolo_XML, CKohler_XML, CToto_XML, CGeberit_XML, CAquant_XML, CQuotation, CIcon_XML
import asyncio

from datetime import datetime
from sqlalchemy import select
import json
import asyncio
from fastapi import HTTPException
from src.Models.models import DocExtractedData


from sqlalchemy.future import select
from config.db_config import AsyncSessionLocal
# from src.Controllers.auth_controller import get_single_user_api_usage
from src.Controllers.Logs_Controller import CLogController
import os
from openpyxl import load_workbook
from openpyxl import Workbook
from openpyxl import Workbook, load_workbook
from src.utilities.helperFunc import CExcelHelper

class CTallyHelper:

    @staticmethod
    async def create_pricelist_report(iUserId, dictExtractedData, strVendorName, strReportFilePath):
        lsRows = []
        if strVendorName.lower() == "hansgrohe":
            lsRows = await CHansgrohe_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)

        elif strVendorName.lower() == "geberit":
            lsRows = await CGeberit_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "nexion":
            lsRows = await CNexion_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        
        elif strVendorName.lower() == "simpolo":
            lsRows = await CSimpolo_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData, strversion="v3")
            
        elif strVendorName.lower() == "toto":
            lsRows = await CToto_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
            
        elif strVendorName.lower() == "kohler":
            lsRows = await CKohler_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)
        elif strVendorName.lower() == "aquant":
            lsRows = await CAquant_XML.MSGetPriceListInfo(iUserId=iUserId, dictExtractedData=dictExtractedData)    
        else:
            # raise ValueError("No Pricelist found for the given vendor.")
            print(f"No Pricelist found for the given vendor. - {strVendorName}")
        
        # Call Function to write the csv report 
        if lsRows:
            await CTallyHelper.update_pricing_list_report(lsReportRows=lsRows, report_pricing_file=strReportFilePath)

    @staticmethod
    async def update_pricing_list_report(lsReportRows, report_pricing_file):
        """
        Update the pricing list report.
        Enhances formatting for better readability.
        """
        try:
            print(f"Saving Pricing List Report at location: {report_pricing_file}")

            # Define field names
            TilesFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property',
                'Qty(Box)', 'Rate / Box', 'Basic Rate / Sq Ft', 'Discount (%)',
                'Item Amount', 'PriceList Rate / Box', 'Ex-Fac./Sft.', 'Pcs/Box',
                'Sft./Box', 'PriceList Rate / Box Matched', 'PriceList Basic Rate / Sq Ft Matched',
                'Accuvelocity Comments'
            ]
            SanitaryFieldNames = [
                'VendorName', 'Price List Effective Start Date', 'InvoiceNumber', 'InvoiceDate',
                'Unique Item Key', 'Item Name', 'Matched PricelistItem Property', 'Item Rate',
                'Qty', 'Discount (%)', 'Item Amount', 'PriceList Amount',
                'PriceList Rate Matched', 'Accuvelocity Comments'
            ]

            # Load or create workbook
            try:
                wb = load_workbook(report_pricing_file)
                ws_tiles = wb["Tiles"]
                ws_sanitary = wb["Sanitary"]
            except FileNotFoundError:
                wb = Workbook()
                ws_tiles = wb.active
                ws_tiles.title = "Tiles"
                ws_sanitary = wb.create_sheet(title="Sanitary")

                # Write headers
                ws_tiles.append(TilesFieldNames)
                ws_sanitary.append(SanitaryFieldNames)

            # Process entries and update rows
            def find_and_update_row(sheet, criteria, entry, field_names):
                headers = list(sheet.iter_rows(min_row=1, max_row=1, values_only=True))[0]
                for row_index, row in enumerate(sheet.iter_rows(min_row=2, values_only=True), start=2):
                    if all(row[headers.index(key)] == value for key, value in criteria.items() if key in headers):
                        for col_index, field in enumerate(field_names, start=1):
                            sheet.cell(row=row_index, column=col_index, value=entry.get(field, sheet.cell(row=row_index, column=col_index).value))
                        return True
                return False

            # Vendor categories
            sanitary_vendors = {"Hansgrohe", "Geberit", "Toto", "Kohler", "Aquant"}
            tiles_vendors = {"Ispira", "Nexion", "Simpolo"}

            for report_row in lsReportRows:
                entry = {
                    'VendorName': report_row.strVendorName,
                    'Price List Effective Start Date': report_row.strPriceListEffectiveStartDate,
                    'InvoiceNumber': report_row.strInvoiceNo,
                    'InvoiceDate': report_row.strInvoiceDate,
                    'Unique Item Key': report_row.strItemId,
                    'Item Name': report_row.strItemName,
                    'Matched PricelistItem Property': report_row.strMatchedItemProperty,
                    'Qty(Box)': report_row.strItemQty,
                    'Qty': report_row.strItemQty,
                    'Item Rate': report_row.strItemRate,
                    'Rate / Box': report_row.strItemRatePerBox,
                    'Basic Rate / Sq Ft': report_row.strItemRatePerSqFt,
                    'Discount (%)': report_row.strDiscount,
                    'Item Amount': report_row.strItemAmount,
                    'PriceList Rate / Box': report_row.strPriceListAmount,
                    'PriceList Amount': report_row.strPriceListAmount,
                    'PriceList Rate Matched': report_row.strIsSanitaryItemRateMatch,
                    'Pcs/Box': report_row.strPCSPerBox,
                    'Sft./Box': report_row.strPriceListRatePerSqFt,
                    'Ex-Fac./Sft.': report_row.strExFacAmount,
                    'PriceList Rate / Box Matched': report_row.strIsPriceListRatePerBoxMatch,
                    'PriceList Basic Rate / Sq Ft Matched': report_row.strIsPriceListRatePerSqFtMatch,
                    'Accuvelocity Comments': report_row.strAccuvelocityComments
                }

                if report_row.strVendorName in sanitary_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Item Rate': entry['Item Rate'],
                        'Item Name':  entry['Item Name'],
                    }
                    sheet = ws_sanitary
                    field_names = SanitaryFieldNames
                elif report_row.strVendorName in tiles_vendors:
                    criteria = {
                        'VendorName': entry['VendorName'],
                        'InvoiceNumber': entry['InvoiceNumber'],
                        'Unique Item Key': entry['Unique Item Key'],
                        'Rate / Box': entry['Rate / Box'],
                        'Item Name':  entry['Item Name']
                    }
                    sheet = ws_tiles
                    field_names = TilesFieldNames
                else:
                    continue

                if not find_and_update_row(sheet, criteria, entry, field_names):
                    sheet.append([entry.get(field, "") for field in field_names])

            # Apply formatting
            ws_tiles= CExcelHelper.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_tiles,  lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})
            ws_sanitary = CExcelHelper.MSFormatExcel(bIsSheetObject=True, oExcelSheet=ws_sanitary, lsWordWrapColumns=['Item Name',"Matched PricelistItem Property", "Accuvelocity Comments"],  column_width_mapping={
        "Matched PricelistItem Property": 45, 'Item Name' : 45, 'Accuvelocity Comments': 70})

            # Save workbook
            wb.save(report_pricing_file)
            print(f"Pricing list report successfully updated at location: {report_pricing_file}")

        except Exception as e:
            import traceback
            traceback.print_exc()
            print("Error:", e)

    @staticmethod
    async def create_pricing_list_report(iUserID, json_file_path, strVendorName, strReportDirPath):
        
        try:
            # Read the JSON file
            with open(json_file_path, 'r', encoding='utf-8') as file:
                json_data = json.load(file)

            # Extract the 'choices[0]["content"]' field
            extracted_data = json_data["choices"][0]["message"]["content"]
            
            # Perform json dump (simulate processing, if needed)
            dict_extracted_data = json.loads(extracted_data)

            # Get the current timestamp in your desired format
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

            # Generate the new file name with timestamp
            report_file_name = f"{timestamp}_pricelist_report.xlsx"
            report_file_path = os.path.join(strReportDirPath, report_file_name) #os.path.join(strReportDirPath, report_file_name)

            # Call the method to create a price list report
            await CTallyHelper.create_pricelist_report(
                iUserID,
                dict_extracted_data,
                strVendorName,
                report_file_path
            )

        except Exception as e:
            print(f"Error processing file '{report_file_name}': {e}")


    @staticmethod
    async def create_pricing_list_report_for_all(iUserID, strJsonDirPath, strVendorName, strReportDirPath):
        # Ensure the directory path exists
        if not os.path.exists(strJsonDirPath):
            print(f"Error: Directory '{strJsonDirPath}' does not exist.")
            return
        
        # Ensure the report directory exists or create it
        if not os.path.exists(strReportDirPath):
            os.makedirs(strReportDirPath)

        # Collect all JSON files in the directory
        json_files = [f for f in os.listdir(strJsonDirPath) if f.endswith('.json')]

        # If no JSON files are found, exit
        if not json_files:
            print(f"No JSON files found in directory: {strJsonDirPath}")
            return

        
        for json_file in json_files:
            json_file_path = os.path.join(strJsonDirPath, json_file)
            try:
                # Read the JSON file
                with open(json_file_path, 'r', encoding='utf-8') as file:
                    json_data = json.load(file)

                # Extract the 'choices[0]["content"]' field
                extracted_data = json_data["choices"][0]["message"]["content"]
                
                # Ensure extracted_data is valid
                if not extracted_data:
                    print(f"Warning: No 'content' found in 'choices[0]' of file: {json_file}")
                    continue

                # Perform json dump (simulate processing, if needed)
                dict_extracted_data = json.loads(extracted_data)

                # Create the report file path for the current JSON file
                # Get the current timestamp in your desired format
                timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")

                # Generate the new file name with timestamp
                report_file_name = f"{os.path.splitext(json_file)[0]}_{timestamp}_pricelist_report.xlsx"

                report_file_path = os.path.join(strReportDirPath, report_file_name)

                # Call the method to create a price list report
                await CTallyHelper.create_pricelist_report(
                    iUserID,
                    dict_extracted_data,
                    strVendorName,
                    report_file_path
                )

            except Exception as e:
                print(f"Error processing file '{json_file}': {e}")
    
    @staticmethod
    async def MSGetResponsesForDocumentIds(iUserID, listDicDocData):
        """
        Purpose : Retrieve the 'Response' column data for a list of document IDs from the database and return it
                in the provided input format with an additional 'Response' key.

        Inputs  :   (1) iUserID        : The ID of the user making the request.
                    (2) listDicDocData : List of dictionaries containing 'DocID' and 'ModelName'.

        Outputs : A list of dictionaries containing 'DocID', 'ModelName', and the retrieved 'Response' data.

        Example : 
            input_data = [
                {"DocID": 2412, "ModelName": "Hansgrohe"},
                {"DocID": 2414, "ModelName": "Hansgrohe"}
            ]
            responses = await CDocumentData.MSGetResponsesForDocumentIds(iUserID=123, listDicDocData=input_data)
            print(responses)
        """
        try:
            if not listDicDocData:
                raise ValueError("The input list is empty.")

            # Extract DocIDs with better handling for key case mismatch
            listDocIds = [item.get('DocId') or item.get('DocID') for item in listDicDocData]

            async with AsyncSessionLocal() as db:
                query = select(DocExtractedData.DocId, DocExtractedData.DocVerifiedData)

                if len(listDocIds) == 1:
                    query = query.filter(DocExtractedData.DocId == listDocIds[0])  # Single ID case
                else:
                    query = query.filter(DocExtractedData.DocId.in_(listDocIds))  # Multiple ID case

                result = await db.execute(query)

                response_data = {
                    row.DocId: row.DocVerifiedData for row in result.mappings().all()
                }

            # Add 'Response' data to original input format
            for item in listDicDocData:
                item['Response'] = response_data.get(item.get('DocId') or item.get('DocID'), None)

            # Logging
            await CLogController.MSWriteLog(
                iUserID, "Info",
                f"Successfully retrieved 'Response' data for document IDs: {listDocIds}"
            )

            return listDicDocData

        except Exception as e:
            print(f"Error fetching document responses: {e}")
            raise HTTPException(
                status_code=500,
                detail="An unexpected error occurred while fetching document data."
            )

    
    @staticmethod
    async def MSCreatePriceListReportFromDocIds(iUserID, json_data, strReportDirPath):
        """
        Creates a price list report using JSON data and a model name.

        Args:
            iUserID (int): The user ID initiating the request.
            json_data (dict): The input JSON data containing document IDs.
            model_name (str): The vendor/model name to process the data.
            strReportDirPath (str): Directory path to save the generated reports.

        Returns:
            None
        """
        try:
        
            if not json_data:
                raise ValueError("No document provided in the input JSON data.")

            # Fetch responses for the provided document IDs
            responses = await CTallyHelper.MSGetResponsesForDocumentIds(iUserID=iUserID, listDicDocData=json_data)

            if not responses:
                raise ValueError("No responses found for the provided document IDs.")

            # Ensure the report directory exists or create it
            if not os.path.exists(strReportDirPath):
                os.makedirs(strReportDirPath)

            # Iterate through the responses and generate price list reports
            for response_json in responses:
                
                # Generate the report file path
                report_file_name = f"{response_json['Name']}_doc_{response_json['DocId']}_pricelist_report.xlsx"
                report_file_path = os.path.join(strReportDirPath, report_file_name)

                # Call the method to create a price list report
                await CTallyHelper.create_pricelist_report(
                    iUserID,
                    response_json["Response"],
                    response_json["Name"],
                    report_file_path
                )

            print(f"Price list reports generated successfully for model '{response_json['Name']}' and document.")

        except Exception as e:
            print(f"Error creating price list report: {e}")

class UnsupportedFileFormatError(Exception):
    """Raised when a file's content type is not supported."""
    def __init__(self, file_path, content_type):
        self.message = f"Unsupported file format: [{file_path}] with content type '{content_type}'"
        super().__init__(self.message)
class CDocument:
    
    def __init__(self, file_path):
        self.file_path = file_path
        self.filename = os.path.basename(file_path)
        CLogger.MCWriteLog("info", f"Initializing document for file: {self.filename}")

        self.content_type = self.get_content_type()
        CLogger.MCWriteLog("info", f"Detected content type: {self.content_type}")

        # Validate content type
        if self.content_type == "application/octet-stream":
            CLogger.MCWriteLog("error", f"Unsupported file format for {self.file_path}")
            raise UnsupportedFileFormatError(self.file_path, self.content_type)
        
        self._data = self.read_file()
        self.checksum = self.calculate_checksum()
        CLogger.MCWriteLog("info", f"Document initialized successfully: {self.filename}")


    def read_file(self):
        """Reads the file data into memory."""
        try:
            with open(self.file_path, 'rb') as f:
                data = f.read()
                CLogger.MCWriteLog("info", f"File read successfully: {self.filename}, Size: {len(data)} bytes")
                return data
        except Exception as e:
            raise IOError(f"Failed to read file {self.file_path}: {e}")

    def calculate_checksum(self):
        """Calculates the file's checksum using MD5."""
        try:
            checksum = hashlib.md5(self._data).hexdigest()
            CLogger.MCWriteLog("info", f"Checksum calculated for {self.filename}: {checksum}")
            return checksum
        except Exception as e:
            raise ValueError(f"Checksum calculation failed: {e}")

    def calculate_checksum_from_file(file_path: str) -> str:
        """
        Calculates the MD5 checksum of a file using its path.
        
        Args:
            file_path (str): Path to the file
        
        Returns:
            str: The calculated MD5 checksum
        """
        try:
            hash_md5 = hashlib.md5()
            with open(file_path, "rb") as f:
                for chunk in iter(lambda: f.read(4096), b""):
                    hash_md5.update(chunk)
            checksum = hash_md5.hexdigest()
            CLogger.MCWriteLog("info", f"Checksum calculated for {file_path}: {checksum}")
            return checksum
        except Exception as e:
            raise ValueError(f"Checksum calculation failed for {file_path}: {e}")

    @property
    def size(self):
        """Returns the file size in bytes."""
        file_size = len(self._data)
        CLogger.MCWriteLog("info", f"File size for {self.filename}: {file_size} bytes")
        return file_size


    def get_content_type(self):
        """Determines the content type based on file extension."""
        extension = self.get_extension()
        mime_types = {
            ".pdf": "application/pdf",
            ".jpg": "image/jpeg",
            ".png": "image/png",
            ".xls": "application/vnd.ms-excel",
            ".xlsx": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet",
            ".zip": "application/zip",
            ".rar": "application/vnd.rar",
            ".7z": "application/x-7z-compressed",
            ".xml": "application/xml",  # Added MIME type for XML
            ".csv": "text/csv"          # Added MIME type for CSV
        }
        return mime_types.get(extension, "application/octet-stream")

    def get_extension(self):
        """Extracts the file extension in lowercase."""
        extension = os.path.splitext(self.filename)[1].lower()
        CLogger.MCWriteLog("info", f"File extension for [{self.filename}] is {extension}")
        return extension

    @staticmethod
    def MSCalculateChecksum(file_path):
        """
        Calculate the SHA256 checksum of a file.
        """
        with open(file_path, "rb") as f:
            file_content = f.read()
            return hashlib.md5(file_content).hexdigest()
        return False
  

if __name__ == "__main__":
    # Run the asyncio loop for processing files
    # iUserID = 4
    # strVendorName = "Aquant"
    # strReportDirPath = r"GitIgnore\PriceLIst"j
    # strJsonDirPath = r"GitIgnore\PriceLIst\AquantAPIRes.json"
    # # asyncio.run(CTallyHelper.create_pricing_list_report_for_all(iUserID=iUserID, strJsonDirPath=strJsonDirPath, strVendorName=strVendorName, strReportDirPath=strReportDirPath))
    # asyncio.run(CTallyHelper.create_pricing_list_report(iUserID, strJsonDirPath, strVendorName, strReportDirPath))
    # iUserID = 4
    # strVendorName = "aquant"
    # strReportDirPath = r"H:\DEVELOPER_PUBLIC\Developers\Dhruvin\IndianInvoiceProcessing\data\apiResponses\15_AQUANT\PriceListReport"
    # strJsonDirPath = r"H:\DEVELOPER_PUBLIC\Developers\Dhruvin\IndianInvoiceProcessing\data\apiResponses\15_AQUANT"
    # asyncio.run(CTallyHelper.create_pricing_list_report_for_all(iUserID=iUserID, strJsonDirPath=strJsonDirPath, strVendorName=strVendorName, strReportDirPath=strReportDirPath))

    # json_file_path = r"H:\DEVELOPER_PUBLIC\Developers\Mohit\indianInvoicePosting\data\apiResponses\1_simpolo\Run5-AllDigitalFiles-part1\2411008342_gptResponse.json"
    # asyncio.run(CTallyHelper.create_pricing_list_report(iUserID=iUserID, json_file_path=json_file_path, strVendorName=strVendorName, strReportDirPath=strReportDirPath))

    lsDocInfo = []
    # with open(r"H:\DEVELOPER_PUBLIC\Customers\AccuVelocity\TillNowProcessedFrom2025-01-01.json") as f:
    #     lsDocInfo = json.load(f)
   
    # strVendorName = "Simpolo"
    # strReportFilePath = "SimpoloPricelist.xlsx"
    # asyncio.run(CTallyHelper.create_pricelist_report(4, dictExtractedData, strVendorName, strReportFilePath))
    
    # lsDocInfo = [{
	# 	"DocId" : 3023,
	# 	"Name" : "Kohler"
    # }
    # ]
    with open(r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\api_responses.json") as f:
        lsDocInfo = json.load(f)
    asyncio.run(CTallyHelper.MSCreatePriceListReportFromDocIds(iUserID=2, json_data=lsDocInfo, strReportDirPath=r"C:\Users\<USER>\Sanket Gohil\Customers\Real\Accuvelocity\AccuVelocity\GitIgnore\ParagTraders Price List\April Pricelist"))