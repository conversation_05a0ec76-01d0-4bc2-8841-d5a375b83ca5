import re
import asyncio
from sqlalchemy.ext.asyncio import create_async_engine, AsyncSession
from sqlalchemy.sql import text
import os
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv(dotenv_path=r".env")

# Database Configuration
DB_CONFIG = {
    "driver": os.getenv("BS_DB_DRIVER"),
    "username": os.getenv("BS_DB_USERNAME"),
    "password": os.getenv("BS_DB_PASSWORD"),
    "host": os.getenv("BS_DB_HOST"),
    "port": int(os.getenv("BS_DB_PORT")),
    # "port": 3078,
    
    "database": os.getenv("BS_DB_NAME")
}

# Create Async Database Engine
DATABASE_URL = f"{DB_CONFIG['driver']}://{DB_CONFIG['username']}:{DB_CONFIG['password']}@{DB_CONFIG['host']}:{DB_CONFIG['port']}/{DB_CONFIG['database']}"
engine = create_async_engine(DATABASE_URL, echo=False)

async def MGetUserReport(intUserID):
    """
    Fetches the total transactions and total time saved for a given user ID.
    
    Args:
        intUserID (int): The user ID to filter the report data.
    
    Returns:
        dict: A dictionary containing intTillNowTotalTxns as an integer and 
                intTillNowTotalTimeSaved as "HH Hours : MM Minutes : SS Seconds".
    """
    async with AsyncSession(engine) as session:
        try:
            # Query to get sum of intTotalTxns
            query_total_txns = text("""
                SELECT SUM(intPredictedTxns) AS totalTxns
                FROM report
                WHERE intUserID = :userID
            """)
            result = await session.execute(query_total_txns, {"userID": intUserID})
            total_txns = result.scalar() or 0  # Default to 0 if NULL
            intTillNowTotalTxns = int(total_txns)  # Ensure integer output

            # Query to get all strTimeSaved values
            query_time_saved = text("""
                SELECT strTimeSaved
                FROM report
                WHERE intUserID = :userID
            """)
            result = await session.execute(query_time_saved, {"userID": intUserID})
            time_saved_list = [row[0] for row in result.fetchall()]

            # Convert time saved format "35 Minutes :45 Seconds" into total seconds
            def convert_time_to_seconds(time_str):
                match = re.search(r"(\d+)\s*Minutes.*?(\d+)\s*Seconds", time_str)
                if match:
                    minutes, seconds = map(int, match.groups())
                    return (minutes * 60) + seconds
                return 0  # Return 0 if format is incorrect

            total_seconds = sum(convert_time_to_seconds(ts) for ts in time_saved_list)

            # Convert total seconds into "HH Hours : MM Minutes : SS Seconds"
            total_hours, remaining_seconds = divmod(total_seconds, 3600)  # Get hours
            total_minutes, remaining_seconds = divmod(remaining_seconds, 60)  # Get minutes

            # Format time as "HH Hours : MM Minutes : SS Seconds"
            intTillNowTotalTimeSaved = f"{total_hours:02d} Hours : {total_minutes:02d} Minutes : {remaining_seconds:02d} Seconds"

            return {
                "intTillNowTotalTxns": intTillNowTotalTxns,  # Ensured as integer
                "intTillNowTotalTimeSaved": intTillNowTotalTimeSaved
            }

        except Exception as e:
            print(f"Error: {e}")
            return None

# Example Usage
if __name__ == "__main__":
    user_id = 7  # Replace with an actual user ID
    result = asyncio.run(MGetUserReport(user_id))
    print(result)
