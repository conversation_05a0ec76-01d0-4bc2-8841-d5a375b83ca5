{"type": "json_schema", "json_schema": {"name": "Tax_Invoice", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "SellerAddress": {"type": "string", "description": "Address of the seller issuing the invoice"}, "SellerCity": {"type": "string", "description": "City of the seller issuing the invoice"}, "SellerPincode": {"type": "number", "description": "Pincode of the seller issuing the invoice"}, "SellerFSSAINO": {"type": "string", "description": "FSSAI License Number of the seller issuing the invoice"}, "SellerUDYAMO": {"type": "string", "description": "Seller UDYAMO value in string.For example it start with this format GJ-01-0006446"}, "SellerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the seller issuing the invoice"}, "SellerStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the seller issuing the invoice"}, "SellerStateCode": {"type": "number", "description": "State code of the seller issuing the invoice"}, "SellerEmail": {"type": "string", "description": "State code of the seller issuing the invoice"}, "ConsigneeName(Ship to)": {"type": "string", "description": "Name of the Consignee"}, "ConsigneeAddress": {"type": "string", "description": "Address of the Consignee"}, "ConsigneeGSTIN/UIN": {"type": "string", "description": "GSTIN number of the Consignee"}, "ConsigneeStateName": {"type": "string", "description": "State Name of the Consignee", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "ConsigneeStateCode": {"type": "number", "description": "State code of the Consignee"}, "ConsigneeContactPerson": {"type": "string", "description": "Name of the contact person"}, "BuyerName(Bill To)": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerCity": {"type": "string", "description": "City of the buyer"}, "BuyerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the buyer"}, "BuyerStateName": {"type": "string", "description": "State Name of the buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "BuyerStateCode": {"type": "number", "description": "State code of the buyer"}, "BuyerContactPerson": {"type": "string", "description": "Name of the contact person"}, "InvoiceNo": {"type": "integer", "description": "Unique invoice number"}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "DeliveryNote": {"type": "string", "description": "Note about the delivery if it does't contains ant things tha return empty string"}, "Mode/TermsOfPayment(In Days)": {"type": "integer", "description": "Terms of payment in days, e.g., 150 days"}, "ReferenceNo & Date": {"type": "string", "description": "Reference number and date of delivery"}, "OtherReference": {"type": "string", "description": "Other reference about delivery"}, "BuyerOrderNO": {"type": "string", "description": "Buyer order number"}, "DispatchDocNO": {"type": "string", "description": "Dispatch document number"}, "DeliveryNoteDate": {"type": "integer", "description": "Date of the delivery note in ddmmyy format"}, "DispatchedThrough": {"type": "string", "description": "Name of the carrier or service used for dispatch if not cinsisit any value return empty string"}, "Destination": {"type": "string", "description": "Destination address"}, "BillOfLanding/LR-RR No": {"type": "string", "description": "Bill of lading number"}, "MotorVehicalNO No": {"type": "string", "description": "Motor vehicle number (e.g., GJ 01 LT 4388)"}, "Tearms Of Delivery": {"type": "string", "description": "Tearms Of Delivery "}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Unique serial number fro each rows"}, "DescriptionOfGoods": {"type": "string", "description": "Detailed description of the item or goods"}, "HSN/SAC": {"type": "string", "description": "HSN or SAC code for the item"}, "Quantity": {"type": "number", "description": "Quantity of the item"}, "Rate": {"type": "number", "description": "Rate per unit of the item"}, "Per": {"type": "string", "description": "Unit of measure (e.g., Box)"}, "Amount": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "DescriptionOfGoods", "HSN/SAC", "Quantity", "Rate", "Per", "Amount"], "additionalProperties": false, "description": "Table Conatins all property's like SlNo,DescriptionOfGoods,HSN/SAC,Quantity,Rate,Per,Amount each field contanis values"}}, "OutputCGSTAmount": {"type": "number", "description": "The total amount of CGST calculated based on the CGST rate(s)"}, "OutputSGSTAmount": {"type": "number", "description": "The total amount of SGST calculated based on the SGST rate(s)"}, "RoundingOFF A/c": {"type": "number", "description": "Rounding off for the invoice"}, "TotalInvoiceQuantity": {"type": "number", "description": "Total quantity for the invoice"}, "TotalChargeable": {"type": "number", "description": "Total chargeable amount for the invoice"}, "TotalChargeable(In Words)": {"type": "string", "description": "Total chargeable value in words"}, "TotalTaxableValue": {"type": "number", "description": "Total taxable value for the invoice"}, "TotalCGST": {"type": "number", "description": "Total CGST applied to the invoice"}, "TotalSGST/UTGST": {"type": "number", "description": "Total SGST or UTGST applied to the invoice"}, "TotalTaxAmount": {"type": "number", "description": "Total tax amount after applying all taxes"}, "TotalTaxAmount(INWORD)": {"type": "string", "description": "Total tax amount in words"}, "Table1": {"type": "array", "items": {"type": "object", "properties": {"TaxableValue": {"type": "number", "description": "Taxable value Consider  from this table"}, "CGSTRate": {"type": "number", "description": " CGSTRate  consider from this table's total cgst"}, "CGSTAmount": {"type": "number", "description": " CGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTRate": {"type": "number", "description": " SGST/UTGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTAmount": {"type": "number", "description": " SGS/UTGSTAmount  consider from this table's total cgst"}, "TotalTaxAmount": {"type": "number", "description": "Final     for the item after discounts and taxes"}}, "required": ["TaxableValue", "CGSTRate", "CGSTAmount", "SGST/UTGSTRate", "SGST/UTGSTAmount", "TotalTaxAmount"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice. The required fields must be provided, and no additional fields are allowed."}}, "SellerPanNO": {"type": "string", "description": "<PERSON><PERSON>'s PAN card number"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCity", "SellerPincode", "SellerFSSAINO", "SellerUDYAMO", "SellerGSTIN/UIN", "SellerStateName", "SellerStateCode", "Seller<PERSON>mail", "Consign<PERSON><PERSON><PERSON>(Ship to)", "Consign<PERSON><PERSON><PERSON><PERSON>", "ConsigneeGSTIN/UIN", "ConsigneeStateName", "ConsigneeStateCode", "Consign<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "BuyerName(Bill To)", "BuyerAddress", "BuyerCity", "BuyerGSTIN/UIN", "BuyerStateName", "BuyerStateCode", "BuyerContactPerson", "InvoiceNo", "InvoiceDate", "DeliveryNote", "Mode/TermsOfPayment(In Days)", "ReferenceNo & Date", "OtherReference", "BuyerOrderNO", "DispatchDocNO", "DeliveryNoteDate", "DispatchedThrough", "Destination", "BillOfLanding/LR-RR No", "MotorVehicalNO No", "Tearms Of Delivery", "OutputCGSTAmount", "OutputSGSTAmount", "RoundingOFF A/c", "Table", "TotalInvoiceQuantity", "TotalChargeable", "TotalChargeable(In Words)", "TotalTaxableValue", "TotalCGST", "TotalSGST/UTGST", "TotalTaxAmount", "TotalTaxAmount(INWORD)", "Table1", "SellerPanNO"], "additionalProperties": false}}}