@echo off

echo Clean up before creating virtual environment...

rd /s /q 5_Env 2>nul

echo successfully clean up...

echo Creating virtual environment...

py -m venv 5_Env

echo Virtual environment created successfully.

echo Activating environment...

cd 5_Env\Scripts

call activate.bat

echo Environment activated successfully.

cd ..
cd ..

echo change to parent-directory...

pip install -r "resource\requirements.txt"

echo Project-required backend modules installed successfully.



pause

exit /b