{"type": "json_schema", "json_schema": {"name": "tax_invoice_details", "strict": true, "schema": {"type": "object", "properties": {"InvoiceBrand": {"type": "string", "description": "Extract the brand name from the 'Description of Goods' column. Look for the text immediately preceding or succeeding the keyword 'Brand' (e.g., 'ISPIRA Brand'). Ignore the word 'Brand' and retain only the brand name (e.g., 'ISPIRA'). If no brand is specified, return an empty string."}, "SellerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the seller."}, "SellerPAN": {"type": "string", "description": "Permanent Account Number (PAN) of the seller for taxation purposes."}, "InvoiceNo": {"type": "integer", "description": "The unique invoice number assigned by the seller without year information."}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyyyy format) on which the invoice was issued."}, "TotalAmountRs": {"type": "number", "description": "Total value of the goods in Indian Rupees."}, "P&fAndOtherChargesRate": {"type": "number", "description": "Percentage of packing and forwarding charges, if any."}, "P&fAndOtherChargesAmount": {"type": "number", "description": "Amount of packing and forwarding charges, if any."}, "AdvancePaymentDiscountRate": {"type": "number", "description": "Percentage discount applied due to advance payment, if any"}, "AdvancePaymentDiscountAmount": {"type": "number", "description": "Discount amount applied due to advance payment, if any"}, "SpecialProductDiscountRate": {"type": "number", "description": "Percentage of special discount, if any"}, "SpecialProductDiscountAmount": {"type": "number", "description": "Amount of special discount, if any"}, "DisplayDiscountRate": {"type": "number", "description": "Percentage of display discount, if any"}, "DisplayDiscountAmount": {"type": "number", "description": "Amount of display discount, if any"}, "Insurance&OthersRate": {"type": "number", "description": "Percentage Charges rate related to insurance and other associated costs."}, "Insurance&OthersAmount": {"type": "number", "description": "Charges amount related to insurance and other associated costs."}, "SubTotal": {"type": "number", "description": "Subtotal before taxes and other charges."}, "CGST": {"type": "number", "description": "Central Goods and Services Tax (CGST) amount."}, "SGST": {"type": "number", "description": "State Goods and Services Tax (SGST) amount."}, "IGSTRate": {"type": "number", "description": "Integrated Goods and Services Tax (IGST) percentage for interstate supplies."}, "IGSTAmount": {"type": "number", "description": "Integrated Goods and Services Tax (IGST) amount for interstate supplies."}, "GrandTotal": {"type": "number", "description": "Final grand total amount of the invoice after all taxes and charges."}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "Serial number of the item"}, "DescriptionOfGoods": {"type": "string", "description": "Description of the goods"}, "ItemType": {"type": "string", "description": "Extracted item type from the first bracket (if available) in the Description of Goods. Represents values like FULL LAPPATO, NATURALE, POLISHED, SOFT MATT, etc. If not available, the value will be an empty string."}, "ItemPcs": {"type": "string", "description": "Extracted number of pieces from the second bracket in the Description of Goods (e.g., '2 Pcs') if available. the value will be 2,  If not available, the value will be N/A"}, "Batch": {"type": "integer", "description": "Batch number"}, "ActualSize(mm)": {"type": "string", "description": "Actual size of the goods"}, "Grade": {"type": "string", "description": "Grade of the goods"}, "QTYBox": {"type": "integer", "description": "Number of boxes"}, "HSNCode": {"type": "integer", "description": "Code of the goods"}, "MRP": {"type": "number", "description": "Maximum Retail Price per unit"}, "Rate/BoxRs": {"type": "number", "description": "The price per box in Indian Rupees (Rs) as mentioned in the invoice. Typically located in the pricing table under a column labeled as 'Rate/Box', 'Price/Box', or similar. Ensure to extract only numerical values, excluding any symbols or text like 'Rs' or '/Box'."}, "Rate/SqftRs": {"type": "number", "description": "The price per square foot in Indian Rupees (Rs), as mentioned in the invoice. Generally found in the pricing section under a column labeled 'Rate/Sqft', 'Price/Sqft', or similar. Extract only the numerical value, excluding any additional text or symbols such as 'Rs' or '/Sqft'."}, "Dis%": {"type": "number", "description": "Discount percentage applied on the goods or services."}, "AmountRs": {"type": "number", "description": "Total amount for the item"}}, "required": ["SrNo", "DescriptionOfGoods", "ItemType", "ItemPcs", "<PERSON><PERSON>", "ActualSize(mm)", "Grade", "QTYBox", "HSNCode", "MRP", "Rate/BoxRs", "Rate/SqftRs", "Dis%", "AmountRs"], "additionalProperties": false}, "description": "Details of all the items in the invoice"}}, "required": ["InvoiceBrand", "SellerGSTIN", "SellerPAN", "InvoiceNo", "InvoiceDate", "TotalAmountRs", "P&fAndOtherChargesRate", "P&fAndOtherChargesAmount", "AdvancePaymentDiscountRate", "AdvancePaymentDiscountAmount", "SpecialProductDiscountRate", "SpecialProductDiscountAmount", "DisplayDiscountRate", "DisplayDiscountAmount", "Insurance&OthersRate", "Insurance&OthersAmount", "SubTotal", "CGST", "SGST", "IGSTRate", "IGSTAmount", "GrandTotal", "Table"], "additionalProperties": false}}}