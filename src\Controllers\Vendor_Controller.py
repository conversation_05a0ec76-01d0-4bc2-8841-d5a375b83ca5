
import traceback
import json
from fastapi import HTTPException, Query
from sqlalchemy.future import select
from sqlalchemy.exc import SQLAlchemyError
from sqlalchemy import func, desc, asc, and_, select, func, delete, or_
from config.db_config import AsyncSessionLocal
from src.Models.models import ModelFields, ModelTable, UploadedDoc, Prompt
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.user_Logs_Controller import <PERSON><PERSON><PERSON><PERSON>og<PERSON>ontroller
from src.Schemas.Vendor_Schema import ModelFieldItem, ModelTable  as ModelTableFields, GetAllModelFilterQuery
from typing import List, Dict, Union
from src.utilities.PromptBuilder import MakeModel<PERSON>TPrompt 
from src.utilities.helperFunc import CModelsHelper
from config.constants import Constants

class CVendorController:

    @staticmethod
    async def MSGetModelByID(iUserID: int, modelId: int):
        """
        Retrieves a ModelTable entry based on its ID.

        Parameters:
            iUserID (int): The ID of the user requesting the model.
            modelId (int): The ID of the model to retrieve.
            db_session_factory (object): The database session factory to manage the connection.
            
        Returns:
            dict: A dictionary containing the model's details if found.
            
        Raises:
            HTTPException: If no model is found or an error occurs.
        """
        try:
            async with AsyncSessionLocal() as db:
                from src.Models.models import ModelTable

                # Execute the query to find the ModelTable with the given ID
                query_result = await db.execute(
                    select(ModelTable).where(ModelTable.Id == modelId)
                )
                model_instance = query_result.scalar()  # Fetch the first result as a scalar

                if model_instance is None:
                    await CLogController.MSWriteLog(iUserID, "Error", f"No ModelTable found with ID {modelId}.")
                    raise HTTPException(
                        status_code=404,
                        detail=f"There was an error while processing your request."
                    )

                # Return model details if found
                return {
                    "ModelId": model_instance.Id,
                    "UserID": model_instance.UserID,
                    "preDefinedModelDict": json.loads(model_instance.preDefinedModelDict) if isinstance(model_instance.preDefinedModelDict, str) else model_instance.preDefinedModelDict,
                    "ModelName": model_instance.Name,
                    "FamilyName": model_instance.FamilyName,
                    "Description": model_instance.Description,
                    "CreatedDateTime": str(model_instance.CreatedDateTime)
                }

        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except SQLAlchemyError as e:
            # Log the SQLAlchemy error and raise an HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve ModelTable with ID {modelId}. SQL Error: {str(e)}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500,
                detail="There was an error while processing your request."
            )
        
        except Exception as e:
            # Log the Exception error and raise an HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve ModelTable with ID {modelId}. ERROR {str(e)}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500,
                detail="There was an error while processing your request."
            )
    
    @staticmethod
    async def MSGetModelByName(iUserID: int, modelName: str, familyName: str):
        """
        Retrieves a ModelTable entry based on the model name and family name.

        Parameters:
            iUserID (int): The ID of the user requesting the model.
            modelName (str): The name of the model to retrieve.
            familyName (str): The family name of the model to retrieve.

        Returns:
            dict: A dictionary containing the model's details if found.

        Raises:
            HTTPException: If no model is found or an error occurs.
        """
        try:
            async with AsyncSessionLocal() as db:
                from src.Models.models import ModelTable

                # Execute the query to find the ModelTable with the given name and family name
                query_result = await db.execute(
                    select(ModelTable).where(
                        ModelTable.Name == modelName,
                        ModelTable.FamilyName == familyName
                    )
                )
                model_instance = query_result.scalar()  # Fetch the first result as a scalar

                if model_instance is None:
                    await CLogController.MSWriteLog(iUserID, "Error", f"No ModelTable found with Name '{modelName}' and Family Name '{familyName}'.")
                    raise HTTPException(
                        status_code=404,
                        detail="No model found with the specified name and family name."
                    )

                # Return model details if found
                return {
                    "ModelId": model_instance.Id,
                    "UserID": model_instance.UserID,
                    "preDefinedModelDict": json.loads(model_instance.preDefinedModelDict) if isinstance(model_instance.preDefinedModelDict, str) else model_instance.preDefinedModelDict,
                    "ModelName": model_instance.Name,
                    "FamilyName": model_instance.FamilyName,
                    "Description": model_instance.Description,
                    "CreatedDateTime": str(model_instance.CreatedDateTime)
                }

        except HTTPException as e:
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise e
        
        except SQLAlchemyError as e:
            # Log the SQLAlchemy error and raise an HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve ModelTable with Name '{modelName}' and Family Name '{familyName}'. SQL Error: {str(e)}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500,
                detail="There was an error while processing your request."
            )
        
        except Exception as e:
            # Log the Exception error and raise an HTTPException
            await CLogController.MSWriteLog(iUserID, "Error", f"Failed to retrieve ModelTable with Name '{modelName}' and Family Name '{familyName}'. ERROR: {str(e)}")
            await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(
                status_code=500,
                detail="There was an error while processing your request."
            )


    @staticmethod
    async def MSAddInvoiceFields(iUserID: int, strModelName, dictFields: Dict[str, Union[List[ModelFieldItem], List[ModelTableFields]]], strFamilyName=Constants.DefaultFamilyName, strModelDesc:str=""):
        """
        Inputs  :   (1) iUserID   :   Id of Current User

                    (2) strModelName : Name of ModelTable
                    
                    (3) dictFields : List of Invoice Fields to be added

        Output  :   dict: Containing Message and list of Invoice Fields data.

        Purpose :   To Add Invoice Fields to fetch

        Example :   await MSAddInvoiceFields(   1, 
                                                "ModelTable 1", 
                                                
                                                {
                                                    "Fields":  [
                                                                    {
                                                                    "FieldName": "string",
                                                                    "FieldType": "string",
                                                                    "FieldDescription": "string"
                                                                    }
                                                                ],
                                                    "Tables":[  
                                                                {
                                                                    "TableName": "Table1",
                                                                    "Fields":[
                                                                                {
                                                                                "FieldName": "string",
                                                                                "FieldType": "string",
                                                                                "FieldDescription": "string"
                                                                                }
                                                                            ]
                                                                },
                                                                {
                                                                    "TableName": "Table2",
                                                                    "Fields:[
                                                                                {
                                                                                "FieldName": "string",
                                                                                "FieldType": "string",
                                                                                "FieldDescription": "string"
                                                                                }
                                                                            ]
                                                                }
                                                            ]
                                                }
                                            )        --> Returns list of Invoice Fields added.
        """
        async with AsyncSessionLocal() as db:
            try:
                from src.utilities.PromptBuilder import MakeModelGPTPrompt
                await CLogController.MSWriteLog(iUserID, "Info", f"Adding of Invoice Fields for ModelTable {strModelName} Started.")

                # Creating ModelTable if not exists else just fetching ModelTable details
                dictResult = await CVendorController.MSCreateVendor(iUserID= iUserID, strModelName= strModelName, strFamilyName=strFamilyName, strModelDesc=strModelDesc )
                dictVendorDetails = dictResult["ModelTable"]
                
                dictResult = {
                }
                
                for key, val in dictFields.items():
                    
                    if key == "Tables":
                        lstTables = [
                                        {
                                            "TableName": table.TableName,
                                            "Fields": [
                                                {
                                                    "FieldName": field.FieldName,
                                                    "FieldCategory": field.FieldCategory,
                                                    "FieldFormat": field.FieldFormat,
                                                    "FieldDescription": field.FieldDescription,
                                                    "FieldNotes": field.FieldNotes
                                                }
                                                for field in table.Fields
                                            ]
                                        }
                                        for table in val
                                    ]
                        dictResult["Tables"] = lstTables # Adding List of tables to Result
                    else:
                        # Converting fields to a list of dictionaries
                        lstFields = [
                            {
                                "FieldName": field.FieldName,
                                "FieldCategory": field.FieldCategory,
                                "FieldFormat": field.FieldFormat,
                                "FieldDescription": field.FieldDescription,
                                "FieldNotes": field.FieldNotes
                            }
                            for field in val
                        ]
                        
                        dictResult[key] = lstFields     # Adding List of tables to Result


                # Check for existing entries
                existing_invoice_fields = await db.scalar(
                    select(ModelFields).where(ModelFields.ModelId == dictVendorDetails["Id"])
                )
                
                if existing_invoice_fields:
                    # Update existing entry
                    existing_invoice_fields.FieldData = dictResult

                    action_message = "Updated existing"
                else:
                    # Create a new entry
                    new_invoice_field = ModelFields(
                        ModelId=dictVendorDetails["Id"],
                        FieldData=dictResult
                    )
                    db.add(new_invoice_field)
                    action_message = "Added new"
                
                await db.commit()

                # Logging and response preparation
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully {action_message} invoice fields and tables for Vendor {strModelName}.")
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully {action_message} invoice fields and tables for Vendor {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully {action_message} Model {strModelName}.", "MyModels")
                # create unique promot base on Model Family and Model Name
                await MakeModelGPTPrompt.MakePromptDict(UserID=iUserID, iModelId=dictVendorDetails["Id"], strModelName= strModelName,strFamilyName=strFamilyName)
                
                return {
                    "Message": f"Successfully {action_message} invoice fields and tables for Vendor {strModelName}.",
                    "Model":dictVendorDetails,
                    "Fields": lstFields,
                    "Tables": lstTables,
                }

            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to add invoice fields and tables for ModelTable {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to Update Model {strModelName}.", "MyModels")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(
                    status_code=500, detail=f"Error occurred while adding invoice fields and tables for ModelTable {strModelName}"
                )

    @staticmethod
    async def MSDuplicateModel(iUserID: int, iModelId: int, strNewModelName: str, strFamilyName: str, strModelDesc:str=""):
        """
        Inputs  :   (1) iUserID       :   Id of Current User
                    (2) iModelId      :   Id of Model to be duplicated
                    (3) strNewModelName:  Name of the new Model
                    (4) strFamilyName :   Name of the Family (Default is 'Default')
        
        Output  :   dict: Containing Message and the details of the duplicated model
        
        Purpose :   To duplicate a model with the same fields and tables
        
        Example :   await MSDuplicateModel(1, 100, "New Model Name")
                    --> Returns details of the duplicated model.
        """
        async with AsyncSessionLocal() as db:
            try:
                from src.utilities.PromptBuilder import MakeModelGPTPrompt
                await CLogController.MSWriteLog(iUserID, "Info", f"Duplicating Model ID {iModelId} to create new model '{strNewModelName}' started.")

                
                # Fetching the original model details
                original_model = await db.scalar(
                    select(ModelFields).where(ModelFields.ModelId == iModelId)
                )
                
                if not original_model:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Selected model don't have any fields, Please try adding some fields first.")
                    raise HTTPException(
                        status_code=204, detail=f"Selected model don't have any fields, Please try adding some fields first."
                    )

                lsObjFoundModels = await db.execute(select(ModelTable).filter(ModelTable.Name == strNewModelName , ModelTable.UserID == iUserID, ModelTable.FamilyName == strFamilyName))
                objExistingModel = lsObjFoundModels.scalars().first()
                
                if objExistingModel:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Model named {strNewModelName} with family {strFamilyName} already exists.")
                    raise HTTPException(
                        status_code=409, detail=f"Model named {strNewModelName} with family {strFamilyName} already exists."
                    )
                    
                dictFields = original_model.FieldData
                
                dictFields = CModelsHelper.MSConvertDictToModels(dictFields)
                
                # Use the existing method to add invoice fields for the new model
                dictResult = await CVendorController.MSAddInvoiceFields(
                    iUserID=iUserID,
                    strModelName=strNewModelName,
                    dictFields=dictFields,
                    strFamilyName=strFamilyName,
                    strModelDesc = strModelDesc
                )
                
                # Logging and response preparation
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully duplicated Model ID {iModelId} to new model '{strNewModelName}'.")
                dictModelData = await CVendorController.MSGetModelByID(iUserID=iUserID, modelId=iModelId)
                strOldModelName = dictModelData["ModelName"]
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully duplicated Model {strOldModelName} to new model {strNewModelName}", "MyModels")

                return {
                    "Message": f"Successfully duplicated Model ID {iModelId} to new model '{strNewModelName}'.",
                    "ModelDetails": dictResult
                }

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
        
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to duplicate Model ID {iModelId} to create new model '{strNewModelName}'.")
                # await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to duplicate Model {strOldModelName} to create new model {strNewModelName}", "MyModels")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(
                        status_code=500, 
                        detail=f"Error occurred while duplicating Model to create new model '{strNewModelName}'."
                    )


    @staticmethod
    async def GetAllReqInvoiceFields(iUserID:int, iModelId:int):
        async with AsyncSessionLocal() as db:
            try:
                from src.Models.models import ModelTable
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching Invoice Fields for Model With id {iModelId} Started.")

                dictModelData = await CVendorController.MSGetModelByID(iUserID=iUserID, modelId=iModelId);
                # Fetch Invoice Fields for the found vendor
                invoice_fields_record = await db.scalar(
                    select(ModelFields).filter(ModelFields.ModelId == iModelId)
                )
                
                dictResult= {
                    "Summary":{},
                    "Model": dictModelData,
                    "ModelData":{}
                }
                
                
                if invoice_fields_record:
                    
                    for key, val in invoice_fields_record.FieldData.items():
                        dictResult["ModelData"][key] = val
                    
                    await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched All Invoice Fields for Model With id {iModelId}.")

                    
                else:
                    await CLogController.MSWriteLog(iUserID, "Info", f"No Invoice Fields Found for Model With id {iModelId}.")

                dictInvoiceSummary = await CVendorController.GetInvoicesSummary(iUserID = iUserID, iModelId=iModelId)
                dictInvoiceSummary["UpdatedDateTime"] = invoice_fields_record.UpdatedDateTime if invoice_fields_record else None
                dictResult["Summary"] =  dictInvoiceSummary
                
                return dictResult
        
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Get Invoice Fields For Model With id {iModelId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise HTTPException(status_code=500, detail=f"An error occurred while Processing.")

    @staticmethod
    async def GetInvoicesSummary(iUserID:int, iModelId: int):
        """
            Input   :   1) iUserID : Userid of the user
            
                        2) strModelName : Name of the ModelTable
            
            Output: {
                        "Total Invoices": 2,
                        "Processed_Invoices": 1,
                        "Not_Processed_Invoices": 1
                    }
                    
            Purpose: To get the total processed and unprocessed invoices count
        """
        
        async with AsyncSessionLocal() as db:
            try:
                from src.Models.models import ModelTable
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching Invoices Summary for Model With id {iModelId} Started.")
                
                # Query for total processed invoices
                query_processed = select(UploadedDoc).filter(
                                                                UploadedDoc.ModelId == iModelId,
                                                                UploadedDoc.Status == "ToBeApproved",
                                                                or_(UploadedDoc.isDeleted == 0, UploadedDoc.isDeleted.is_(None))
                                                            )
                
                processed_count = await db.execute(query_processed)
                processed_count = len(processed_count.scalars().all())

                # Query for total not processed invoices
                query_not_processed = select(UploadedDoc).filter(
                                                                    UploadedDoc.ModelId == iModelId,
                                                                    UploadedDoc.Status.in_(["Queue", "Error","NotProcess","Processing"]),
                                                                    or_(UploadedDoc.isDeleted == 0, UploadedDoc.isDeleted.is_(None))
                                                                )
                not_processed_count = await db.execute(query_not_processed)
                not_processed_count = len(not_processed_count.scalars().all())

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched Invoices Summary for Model With id {iModelId}.")

                return {
                    "Total Invoices": processed_count + not_processed_count,
                    "Processed_Invoices": processed_count,
                    "Not_Processed_Invoices": not_processed_count
                }

            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to Get Invoice Summary For Model With id {iModelId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                
                raise HTTPException(status_code=500, detail=f"An error occurred while Processing.")



    @staticmethod
    async def MSCreateVendor(iUserID:int, strModelName: str, strFamilyName=Constants.DefaultFamilyName ,preDefinedModelDict:dict={}, strModelDesc:str=""):
        from src.Models.models import ModelTable
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Creating ModelTable {strModelName} Started.")

                # Validate the ModelTable name is not empty
                if not strModelName.strip():
                    # If the ModelTable name is empty or only contains whitespace, log and return an error message.
                    await CLogController.MSWriteLog(iUserID, "Error", f"Attempt to create a ModelTable with an empty name.")
                    raise HTTPException(status_code=400, detail="ModelTable name cannot be empty.")



                # Check if the ModelTable already exists
                lsObjFoundModels = await db.execute(select(ModelTable).filter(ModelTable.Name == strModelName , ModelTable.UserID == iUserID, ModelTable.FamilyName == strFamilyName))
                objVendor = lsObjFoundModels.scalars().first()
                
                if objVendor:
                    # If the ModelTable exists, log and return a message.
                    await CLogController.MSWriteLog(iUserID, "Info", f"ModelTable {strModelName} already exists.")
                    
                    return {    "message": "ModelTable already exists", 
                                "ModelTable": {
                                            "Id": objVendor.Id,
                                            "UserID": objVendor.UserID,
                                            "preDefinedModelDict":json.dumps(objVendor.preDefinedModelDict, ensure_ascii=False) if isinstance(objVendor.preDefinedModelDict, dict) else objVendor.preDefinedModelDict,
                                            "Name": objVendor.Name,
                                            "FamilyName": objVendor.FamilyName,
                                            "Description": objVendor.Description,
                                            "CreatedDateTime": objVendor.CreatedDateTime
                                        }
                            }
                
                # Create a new ModelTable instance
                new_vendor = ModelTable(
                                        UserID= iUserID,
                                        Name=strModelName,
                                        FamilyName=strFamilyName,
                                        Description= strModelDesc,
                                        preDefinedModelDict = json.dumps(preDefinedModelDict, ensure_ascii=False) if isinstance(preDefinedModelDict, dict) else preDefinedModelDict 
                                    )

                # Add the new ModelTable to the session
                db.add(new_vendor)
                
                # Commit the transaction to save the new ModelTable to the database
                await db.commit()
                await db.refresh(new_vendor)
                
                dictVendorInfo = {  
                                    "Id": new_vendor.Id,
                                    "UserID": new_vendor.UserID,
                                    "preDefinedModelDict":json.loads(new_vendor.preDefinedModelDict) if isinstance(new_vendor.preDefinedModelDict, str) else new_vendor.preDefinedModelDict,
                                    "Name": new_vendor.Name,
                                    "FamilyName":new_vendor.FamilyName,
                                    "Description": new_vendor.Description,
                                    "CreatedDateTime": new_vendor.CreatedDateTime
                                }
                
                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Created ModelTable {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully Created Model {strModelName}", "MyModels")

                return {"message": "ModelTable created successfully", "ModelTable": dictVendorInfo}

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to create ModelTable named {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to create Model {strModelName}", "MyModels")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await db.rollback()
                
                raise HTTPException(status_code=500, detail=f"An error occurred while creating Model.")


    @staticmethod
    async def MSUpdateVendor(iUserID:int, iModelId:int, strNewModelName: str = "", strNewModelFamily: str = "",strNewModelDesc:str = "", preDefinedModelDict=None):
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating ModelTable with id {iModelId} Started.")
                # Fetch the ModelTable record by ID
                result = await db.execute(select(ModelTable).filter(ModelTable.Id == iModelId))
                objModelTable = result.scalars().first()

                if not objModelTable:
                    await CLogController.MSWriteLog(iUserID, "Error", f"ModelTable with id {iModelId} not found")
                    raise HTTPException(status_code=404, detail=f"Failed to process your request, as Requested Model not found")

                if strNewModelName:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Updating Model Name with ModelId {iModelId} From '{objModelTable.Name}' to '{strNewModelName}' .")
                    if objModelTable.Name != strNewModelName: 
                        await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully Updated model From {objModelTable.Name} to '{strNewModelName}.", "MyModels")
                    objModelTable.Name = strNewModelName
                
                if strNewModelFamily:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Updating Model Family Name with ModelId {iModelId} From '{objModelTable.FamilyName}' to '{strNewModelFamily}' .")
                    if objModelTable.FamilyName != strNewModelFamily:
                        await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully Updated Family Name From {objModelTable.FamilyName} to {strNewModelFamily}.", "MyModels")
                    objModelTable.FamilyName = strNewModelFamily
                    
                if strNewModelDesc:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Updating Model Description with ModelId {iModelId} From {objModelTable.Description} to {strNewModelDesc} .")
                    if objModelTable.Description != strNewModelDesc:
                        await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully Updated Description From {objModelTable.Description} to {strNewModelDesc}.", "MyModels")
                    objModelTable.Description = strNewModelDesc

                # Commit the changes to the database
                await db.commit()

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Updated Model with Id {iModelId}.")

                # If Model Name is updated then we also need to update model series
                if strNewModelName:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Model Series with Model id {iModelId} Started.")

                    modelPromptResult = await db.execute(select(Prompt).filter(Prompt.ModelId == iModelId))
                    objPromptData = modelPromptResult.scalars().all()
                    
                    for objPrompt in objPromptData:
                        # Split the string into parts
                        lsModelSeriesParts = objPrompt.ModelSeries.split('_')

                        # Replace the first part with the new value
                        lsModelSeriesParts[0] = strNewModelName

                        # Join the parts back together with underscores
                        updatedModelSeries = '_'.join(lsModelSeriesParts)
                        objPrompt.ModelSeries = updatedModelSeries
                    
                    await db.commit()
                    await CLogController.MSWriteLog(iUserID, "Info", f"Process of Updating Model Series with Model id {iModelId} Completed Successfully.")

                dictVendorInfo = {  
                                    "Id": objModelTable.Id,
                                    "UserID": objModelTable.UserID,
                                    "preDefinedModelDict":json.loads(objModelTable.preDefinedModelDict) if isinstance(objModelTable.preDefinedModelDict, str) else objModelTable.preDefinedModelDict,
                                    "Name": objModelTable.Name,
                                    "FamilyName": objModelTable.FamilyName,
                                    "Description": objModelTable.Description,
                                    "CreatedDateTime": objModelTable.CreatedDateTime
                                }
                return {"message": "ModelTable updated successfully", "modelData": dictVendorInfo}

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except Exception as e:
                await db.rollback()
                
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to update ModelTable with id {iModelId}.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                
                raise HTTPException(status_code=500, detail=f"An error occurred while updating Model.")


    @staticmethod
    async def MSDeleteVendor(iUserID:int, strModelName: str,iModelId :int):
        from src.Models.models import ModelTable
        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Deleting ModelTable {strModelName} Started.")
                # Fetch the ModelTable record by ID
                
                result = await db.execute(select(ModelTable).filter(ModelTable.Id == iModelId))
                ModelTable = result.scalars().first()

                if not ModelTable:
                    await CLogController.MSWriteLog(iUserID, "Error", f"ModelTable Named {strModelName} not found")

                    raise HTTPException(status_code=404, detail=f"ModelTable Named {strModelName} not found")

                # Delete the ModelTable record
                await db.delete(ModelTable)

                # Commit the transaction to delete the ModelTable from the database
                await db.commit()

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Deleted ModelTable {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully Deleted Model {strModelName}", "MyModels")

                return {"message": "ModelTable deleted successfully"}

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                raise e
            
            except Exception as e:
                await db.rollback()
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to delete ModelTable named {strModelName}.")
                await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to delete Model {strModelName}", "MyModels")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                
                raise HTTPException(status_code=500, detail=f"An error occurred while processing your request, Please try again later.")


    @staticmethod
    async def MSGetAllVendors(iUserID: int, filterQuery:GetAllModelFilterQuery, hasAdminRights: bool ,iPage: int=Query(1, gt=0), iLimit: int= Query(10, gt=0)):

        async with AsyncSessionLocal() as db:
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Fetching all Models Started.")
                
                # First, count the total number of Models for the user

                total_count = await db.scalar(select(func.count()).filter(ModelTable.UserID == iUserID))
                total_pages = (total_count + iLimit - 1) // iLimit  # Calculate total pages needed
                
                # Calculate offset based on the current page
                iOffset = (iPage - 1) * iLimit

                # Fetch the list of Models associated with the user ID
                
                selectQuery = select(ModelTable).filter(ModelTable.UserID == iUserID)

                if filterQuery:
                    selectQuery = CVendorController.apply_ordering(selectQuery, filterQuery, user_id= iUserID)
                
                selectQuery = selectQuery.offset(iOffset).limit(iLimit)
                
                result = await db.execute(selectQuery)
                Models = result.scalars().all()

                # Construct a dictionary containing all ModelTable information as a list of dictionaries
                ModelsData = []
                
                for objModelTable in Models:

                    # Skip the vendor named "General"
                    if objModelTable.Name == "General":
                        continue
                    
                    # For each ModelTable, fetch the latest UpdatedDateTime of ModelFields
                    lsObjLatestInvoiceField = await db.execute(
                                                            select(ModelFields.UpdatedDateTime)
                                                                .filter(ModelFields.ModelId == objModelTable.Id)
                                                                .order_by(ModelFields.UpdatedDateTime.desc())
                                                                .limit(1)
                                                        )
                    objUpdatedDateTime = lsObjLatestInvoiceField.scalars().first()
                    
                    
                    dictSummary =  await CVendorController.GetInvoicesSummary(iUserID = iUserID, iModelId = objModelTable.Id)
                    ModelsData.append(
                                        {
                                            "Id": objModelTable.Id, 
                                            "UserID":objModelTable.UserID,
                                            "Name": objModelTable.Name,
                                            "FamilyName": objModelTable.FamilyName,
                                            "Description": objModelTable.Description,
                                            "Summary": dictSummary,
                                            "UpdatedDateTime": objUpdatedDateTime if objUpdatedDateTime else "No updates"  
                                        }
                                        )

                # Fetch unique model families for the user
                uniqueFamiliesResult = await db.execute(
                    select(ModelTable.FamilyName).distinct().filter(ModelTable.UserID == iUserID)
                )
                lsModelFamilies = [row[0] for row in uniqueFamiliesResult.all()]

                await CLogController.MSWriteLog(iUserID, "Info", f"Successfully Fetched all Models.")

                # Include pagination information in the response
                response = {
                    "pagination": {
                        "total_pages": total_pages,
                        "current_page": iPage,
                        "per_page": iLimit
                    },
                    "Models": ModelsData,
                    "Model_Families": lsModelFamilies
                }
            
            
                return response

            except Exception as e:
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to get all Models.")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                
                raise HTTPException(status_code=500, detail=f"An error occurred while fetching Models.")



    @staticmethod
    async def MSDeleteInvoiceFields(iUserID:int, iModelId:int, strModelName: int, lsInvoiceFieldIds: List[int]):
        async with AsyncSessionLocal() as db:  # Replace AsyncSessionLocal with your actual session maker
            try:
                await CLogController.MSWriteLog(iUserID, "Info", f"Process of Deleting invoice fields for Model Named {strModelName} has started")

                # Fetch the vendor by name
                lsObjModels = await db.execute(select(ModelTable).filter(ModelTable.Id == iModelId))
                objVendor = lsObjModels.scalars().first()

                if not objVendor:
                    await CLogController.MSWriteLog(iUserID, "Error", f"Model Named {strModelName} not found")
                    raise HTTPException(status_code=404, detail=f"Vendor '{strModelName}' not found.")

                # Fetch and delete invoice fields by vendor ID and field names
                iTotalDeletedRows = 0
                
                delete_result = await db.execute(
                                                    delete(ModelFields)
                                                    .where(
                                                        ModelFields.ModelId == objVendor.Id,
                                                        ModelFields.Id.in_(lsInvoiceFieldIds)
                                                    )
                )
                await db.commit()
                
                iTotalDeletedRows += delete_result.rowcount

                if iTotalDeletedRows == 0:
                    await CLogController.MSWriteLog(iUserID, "Info", f"No invoice fields were deleted as Provided Fields Not Found.")

                    return {"message": "No invoice fields were deleted as Provided Fields Not Found."}
                else:
                    await CLogController.MSWriteLog(iUserID, "Info", f"Successfully deleted {iTotalDeletedRows} invoice fields for Model '{strModelName}'.")
                    await CUserLogController.MSWriteLog(iUserID, "Info", f"Successfully deleted Model {iTotalDeletedRows} fields for Model {strModelName}.", "MyModels")

                    return {"message": f"Successfully deleted {iTotalDeletedRows} invoice fields for vendor '{strModelName}'."}

            except HTTPException as e:
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to delete fields for Model {strModelName}.", "MyModels")
                raise e
            
            except Exception as e:
                await db.rollback()
                
                await CLogController.MSWriteLog(iUserID, "Error", f"Failed to delete invoice fields for Model Named '{strModelName}'.")
                await CUserLogController.MSWriteLog(iUserID, "Error", f"Failed to delete fields for Model {strModelName}.", "MyModels")
                await CLogController.MSWriteLog(iUserID, "Debug", f"Traceback: {str(traceback.format_exc())}")

                raise HTTPException(status_code=500, detail=f"An error occurred while processing your request, Please try again later.")
            
    
    @staticmethod
    def apply_ordering(query, filterQuery, user_id):
        if hasattr(filterQuery, 'bModelNameAsc') and filterQuery.bModelNameAsc is not None:
            if filterQuery.bModelNameAsc:
                query = query.order_by(asc(ModelTable.Name))
            else:
                query = query.order_by(desc(ModelTable.Name))
            return query 

        if hasattr(filterQuery, 'bModelFamilyNameAsc') and filterQuery.bModelFamilyNameAsc is not None:
            if filterQuery.bModelFamilyNameAsc:
                query = query.order_by(asc(ModelTable.FamilyName))
            else:
                query = query.order_by(desc(ModelTable.FamilyName))
            return query 
        
        if hasattr(filterQuery, 'bTotalDocsAsc') and filterQuery.bTotalDocsAsc is not None:
            # Join ModelTable with UploadedDoc to count total documents per model
            subquery = (
                select(UploadedDoc.ModelId, func.count(UploadedDoc.DocId).label("total_docs"))
                .where(UploadedDoc.UserId == user_id)
                .group_by(UploadedDoc.ModelId)
                .subquery()
            )
            
            query = query.outerjoin(subquery, ModelTable.Id == subquery.c.ModelId)
            if filterQuery.bTotalDocsAsc:
                query = query.order_by(asc(subquery.c.total_docs))
            else:
                query = query.order_by(desc(subquery.c.total_docs))
            return query

        return query  # Return the query as is if no conditions are met