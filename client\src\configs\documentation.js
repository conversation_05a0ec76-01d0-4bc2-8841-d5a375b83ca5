export const DOCUMENTATION_INDEX = [
  {
    id: "overview",
    title: "1. Overview",
  },
  {
    id: "accuVelocityPageDetails:",
    title: "2. AccuVelocity Page Details:",
  },
  {
    id: "supportedDocumentFormats",
    title: "3. Supported Document Formats",
  },
  {
    id: "accuVelocityAccessibility",
    title: "4. AccuVelocity Accessibility (log in and sign up):",
  },
  {
    id: "accuVelocityUpdatePasswordPage",
    title: "5. AccuVelocity Update Password Page:",
  },
  {
    id: "certifications",
    title: "6. Certifications",
  },
  {
    id: "frequentlyAskedQuestions",
    title: "7. Frequently Asked Questions (FAQ)",
  },
  {
    id: "conclusion",
    title: "8. Conclusion",
  },
];
export const DOCUMENTATION = {
  Overview: {
    title: "1. Overview",
    description:
      "AccuVelocity is an advanced technology service platform designed to automate data extraction and processing from various document types. Utilizing cutting-edge generative AI and machine learning technologies. AccuVelocity converts unstructured data into actionable insights, breaking down data barriers and ensuring seamless integration with legacy systems. Known for its innovative solutions, AccuVelocity helps businesses optimize their data management processes effectively.",
  },
  AccuVelocityPageDetails: {
    title: "2. AccuVelocity Page Details:",
    bulletPoints: [
      {
        id: 1,
        point: "My Documents:",
        text: "This page allows users to view, manage, and access all their Uploaded documents. Users can also download, delete, re-Extract or review specific documents directly from this page.",
      },
      {
        id: 2,
        point: "Upload Document",
        text: "On this page, users can upload new documents to their account. The page provides a simple interface for file and Model selection and supports various document formats.",
      },
      {
        id: 3,
        point: "Profile:",
        text: "The Profile page displays the user's personal information and settings. Users can view and edit their details such as name, email, and contact information.",
      },
      {
        id: 4,
        point: "My Models:",
        text: "This section is dedicated to managing models. Users can create, edit, or delete models used for extraction.",
      },
      {
        id: 5,
        point: "Update Password:",
        text: "Users can securely change their password on this page. It includes fields for entering the current password and setting a new one, along with confirmation for the new password.",
      },
      {
        id: 6,
        point: "Log Out:",
        text: "This option allows users to securely log out of the application. It ensures that all user sessions are closed to maintain privacy and security.",
      },
      {
        id: 7,
        point: "Log In:",
        text: "The Log In page allows users to access their accounts securely. This page is designed to ensure a smooth and secure authentication process for all users.",
      },
      {
        id: 8,
        point: "Register or Sign Up / Account Creation:",
        text: "This Page allows users to create their Account in AccuVelocity.",
      },
    ],
  },
  SupportedDocumentFormats: {
    title: "3. Supported Document Formats",
    description:
      "AccuVelocity supports a range of document formats, ensuring flexibility and convenience for users. The supported file formats include TXT, PDF, JPG, JPEG, PNG, WEBP, BMP, CSV as well as Excel and Word documents. If user want to upload other than these files, then contact us at",
  },
  ForLogIn: {
    title: "For Log in:",
    bulletPoints: [
      {
        id: 1,
        point: "Select Log in Tab:",
        text: "User are required to select Log in tab.",
      },
      {
        id: 2,
        point: "Work Email:",
        text: "Users are required to enter their registered email address in this field. This acts as the unique identifier for accessing their account.",
      },
      {
        id: 3,
        point: "Password:",
        text: "Users must enter their account password in this field. The password is masked for security, and users can toggle visibility using the eye icon to ensure they have entered it correctly.",
      },
      {
        id: 4,
        point: "Security CAPTCHA:",
        text: "A CAPTCHA box is included to verify that the user is not a robot. This adds an extra layer of security to prevent automated login attempts.",
      },
      {
        id: 5,
        point: "Login Button:",
        text: 'After entering their credentials and completing the CAPTCHA, users click the "Login" button to access their account. This button becomes active only when all required fields are filled correctly.',
      },
    ],
  },
  AccuVelocityUpdatePasswordPage: {
    title: "5. AccuVelocity Update Password Page:",
    description: "To Update Password of AccuVelocity user account:",
    bulletPoints: [
      {
        id: 1,
        point: "Navigate Update Password Page:",
        text: "Access the 'Update Password Page' from the sidebar/Navigation menu.",
      },
      {
        id: 2,
        point: "Current Password:",
        text: "Users must enter their current password in this field to verify their identity and authorize the password change.",
      },
      {
        id: 3,
        point: "New Password:",
        text: "Users enter their desired new password in this field. The page provides guidelines for creating a strong password.",
      },
      {
        id: 4,
        point: "Confirm New Password:",
        text: "Users must re-enter their new password in this field to confirm that it was typed correctly.",
      },
      {
        id: 5,
        point: "Update Password Button:",
        text: 'Once all fields are correctly filled, users can click the "Update Password" button to complete the process. This button is disabled until all requirements are met.',
      },
    ],
  },
  Certifications: {
    title: "Certifications",
    bulletPoints: [
      {
        id: 1,
        point: "ISO Certificate:",
        text: "AccuVelocity is proud to be an ISO 9001:2015 certified company, reflecting our commitment to quality management and excellence. This certification ensures that we meet international standards for customer satisfaction, continual improvement, and risk management. Your data security is our top priority, safeguarded through advanced encryption, strict access controls, regular audits, and comprehensive employee training.",
      },
      {
        id: 2,
        point: "HIPAA Compliant:",
        text: "We are currently in the process of becoming HIPAA compliant, demonstrating our commitment to the highest standards of data security and privacy. This certification will ensure that we adhere to the stringent requirements for protecting sensitive health information. Once the process is complete, we will update our status to reflect our compliance.",
      },
      {
        id: 3,
        point: "GDPR Compliance:",
        text: "We are actively working towards achieving GDPR compliance to ensure we meet the strict data protection and privacy regulations set by the European Union. This certification will guarantee that we handle personal data with the utmost care and transparency. Once the compliance process is completed, we will update our status accordingly.",
      },
      {
        id: 4,
        point: "SOC 2 Certification:",
        text: "We are in the process of obtaining SOC 2 certification, which will affirm our commitment to the highest standards of security, availability, processing integrity, confidentiality, and privacy. This rigorous certification process ensures that we have the necessary controls and practices in place to protect your data. Once we achieve SOC 2 compliance, we will update our status to reflect this significant milestone.",
      },
    ],
  },
  GeneralInformation: {
    title: "General Information",
    bulletPoints: [
      {
        id: 1,
        point: "Q. What is AccuVelocity?",
        text: "AccuVelocity is a technology service platform dedicated to automating data extraction and processing from various document types, utilizing advanced AI and machine learning to convert unstructured data into actionable insights.",
      },
      {
        id: 2,
        point: "Q. How can I get chatbot support?",
        text: "You can open chatbot support by clicking the 'Support' button in the navigation pane.",
      },
      {
        id: 3,
        point: "Q. What is support on the navigation pane?",
        text: "By clicking the 'Support' button in the navigation pane, you can open chatbot support for AccuVelocity.",
      },
      {
        id: 4,
        point: "Q. How can I see a demo?",
        text: "To book a live demo by our AI Expert, click on 'Book a Demo' on the homepage (www.accuvelocity.com).",
      },
      {
        id: 5,
        point: "Q. What types of documents can I upload?",
        text: "You can upload documents in TXT, PDF, JPG, JPEG, PNG, WEBP, BMP, CSV file formats, as well as Excel and Word documents.",
      },
      {
        id: 6,
        point: "Q. How can I upload a document?",
        text: "You can upload a document by dragging and dropping it or by clicking on the upload button on the homepage.",
      },
    ],
  },
  Conclusion: {
    title: "Conclusion",
    description:
      "AccuVelocity provides a comprehensive solution for automating data extraction and processing. With a user-friendly interface, robust support, and a wide range of features, AccuVelocity is designed to help businesses streamline their data management processes and gain valuable insights from their documents. For any further assistance or inquiries, please contact our support <NAME_EMAIL>",
  },
};
