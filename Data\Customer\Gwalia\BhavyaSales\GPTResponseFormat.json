{"type": "json_schema", "json_schema": {"name": "TaxiNVOICE", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller issuing the invoice"}, "SellerAddress": {"type": "string", "description": "Address of the seller issuing the invoice"}, "SellerMobileNo": {"type": "array", "description": "Phone numbers of the seller issuing the invoice", "items": {"type": "string"}}, "SellerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the seller issuing the invoice"}, "SellerStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the seller issuing the invoice"}, "SellerStateCode": {"type": "number", "description": "State code of the seller issuing the invoice"}, "SellerContact": {"type": "string", "description": "Contact number of the seller"}, "SellerEmail": {"type": "string", "description": "Email address of the <NAME_EMAIL>"}, "ConsigneeName(Ship to)": {"type": "string", "description": "Name of the Consignee"}, "ConsigneeAddress": {"type": "string", "description": "Address of the Consignee"}, "ConsigneeGSTIN/UIN": {"type": "string", "description": "GSTIN number of the Consignee"}, "ConsigneeStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the Consignee"}, "ConsigneeStateCode": {"type": "number", "description": "State code of the Consignee"}, "BuyerName": {"type": "string", "description": "Name of the Buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the Buyer"}, "BuyerGSTIN/UIN": {"type": "string", "description": "GSTIN number of the Buyer"}, "BuyerStateName": {"type": "string", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"], "description": "State name of the Buyer"}, "BuyerStateCode": {"type": "number", "description": "State code of the Buyer"}, "BuyerContactPerson": {"type": "string", "description": "Name of the contact person for the Buyer"}, "InvoiceNo": {"type": "string", "description": "Unique invoice number with a fiscal year "}, "InvoiceDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "DeliveryNote": {"type": "string", "description": "Note about the delivery if it doesn't contain any items return empty string  "}, "Mode/TermsOfPayment(In Days)": {"type": "integer", "description": "Terms of payment in days, e.g., 1 days return only 1"}, "ReferenceNo & Date": {"type": "string", "description": "Reference number and date of delivery"}, "OtherReference": {"type": "string", "description": "Other reference about delivery"}, "BuyerOrderNO": {"type": "string", "description": "Buyer order number"}, "DispatchDocNO": {"type": "string", "description": "Dispatch document number"}, "Dated": {"type": "integer", "description": "Date  in ddmmyy format don't include value of DeliveryNoteDate if not contains value return 0 "}, "DeliveryNoteDate": {"type": "integer", "description": "Date of the delivery note in ddmmyy format likes 22-jan-25 convert it into 220125"}, "DispatchedThrough": {"type": "string", "description": "Name of the carrier or service used for dispatch only cosider values ifDispatchedThrough contains else return empty string"}, "Destination": {"type": "string", "description": "Destination address ilke SBR etc "}, "Tearms Of Delivery": {"type": "string", "description": "Terms of Delivery"}, "CGSTAmount": {"type": "number", "description": "The total amount of CGST calculated based on the CGST rate(s) here MATCH NAME WITH OutputCGSTAmount@2.5 VALUES IF availabe then return only"}, "SGSTAmount": {"type": "number", "description": "The total amount of SGST calculated based on the SGST rate(s). here MATCH NAME WITH OutputSGSTAmount@2.5 VALUES IF availabe then return only"}, "Round Off": {"type": "number", "description": "Round off  for the invoice"}, "FREIGHT ON SALES": {"type": "number", "description": "FREIGHT SALES  for the invoice"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"SlNo.": {"type": "integer", "description": "Unique serial number for each item"}, "No&KindofPages": {"type": "integer", "description": "No and kind of pages"}, "DescriptionOfGoods": {"type": "string", "description": "Detailed description of the item or goods"}, "HSN/SAC": {"type": "integer", "description": "HSN or SAC code for the item"}, "GSTRate(In %)": {"type": "number", "description": "GSTRate per unit of the item EX 12 % only include 12"}, "Quantity": {"type": "number", "description": "Quantity of the item ONLY CONTAINS VALUES DO'T CONSIDER Example 1320 ltrs only consider 1320 the wrods like ltrs,kg.,BOX etc remove it "}, "Rate": {"type": "number", "description": "Rate per unit"}, "Per": {"type": "string", "description": "Unit of measure (e.g., Box ,ltrs,kg. etc)"}, "Amount": {"type": "number", "description": "Final amount for the item after discounts and taxes"}}, "required": ["SlNo.", "No&KindofPages", "DescriptionOfGoods", "HSN/SAC", "Quantity", "GSTRate(In %)", "Rate", "Per", "Amount"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice."}}, "TotalInvoiceQuantity": {"type": "number", "description": "Total quantity for the invoice"}, "TotalChargeable": {"type": "number", "description": "Total chargeable amount for the invoice including CGST,SGST,FREIGHT ON SALES ,Round off"}, "TotalChargeable(In Words)": {"type": "string", "description": "Total chargeable value in words"}, "TotalTaxableValue": {"type": "number", "description": "Total taxable value for the invoice"}, "TotalCGST": {"type": "number", "description": "Total CGST(Central Goods and Services Tax) applied to the invoice"}, "TotalSGST/UTGST": {"type": "number", "description": "Total SGST(State Goods and Services Tax) or UTGST applied to the invoice"}, "TotalTaxAmount": {"type": "number", "description": "Total tax amount after applying all taxes"}, "TotalTaxAmount(INWORD)": {"type": "string", "description": "Total tax amount in words"}, "Table1": {"type": "array", "items": {"type": "object", "properties": {"HSN/SAC": {"type": "string", "description": "The HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code) code for the item, which is used for tax purposes."}, "TaxableValue": {"type": "number", "description": "Taxable value Consider  from this table"}, "CGSTRate": {"type": "number", "description": " CGSTRate  consider from this table's total cgst"}, "CGSTAmount": {"type": "number", "description": " CGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTRate": {"type": "number", "description": " SGST/UTGSTAmount  consider from this table's total cgst"}, "SGST/UTGSTAmount": {"type": "number", "description": " SGS/UTGSTAmount  consider from this table's total cgst"}, "TotalTaxAmount": {"type": "number", "description": "Final     for the item after discounts and taxes"}}, "required": ["HSN/SAC", "TaxableValue", "CGSTRate", "CGSTAmount", "SGST/UTGSTRate", "SGST/UTGSTAmount", "TotalTaxAmount"], "additionalProperties": false, "description": "Each object in this array represents an item in the invoice. The required fields must be provided, and no additional fields are allowed."}}, "SellerPanNO": {"type": "string", "description": "<PERSON><PERSON>'s PAN card number"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerMobileNo", "SellerGSTIN/UIN", "SellerStateName", "SellerStateCode", "SellerContact", "Seller<PERSON>mail", "Consign<PERSON><PERSON><PERSON>(Ship to)", "Consign<PERSON><PERSON><PERSON><PERSON>", "ConsigneeGSTIN/UIN", "ConsigneeStateName", "ConsigneeStateCode", "BuyerName", "BuyerAddress", "BuyerGSTIN/UIN", "BuyerStateName", "BuyerStateCode", "BuyerContactPerson", "InvoiceNo", "InvoiceDate", "DeliveryNote", "Mode/TermsOfPayment(In Days)", "ReferenceNo & Date", "OtherReference", "BuyerOrderNO", "DispatchDocNO", "Dated", "DeliveryNoteDate", "DispatchedThrough", "Destination", "Tearms Of Delivery", "CGSTAmount", "SGSTAmount", "Round Off", "FREIGHT ON SALES", "Table", "TotalInvoiceQuantity", "TotalChargeable", "TotalChargeable(In Words)", "TotalTaxableValue", "TotalCGST", "TotalSGST/UTGST", "TotalTaxAmount", "TotalTaxAmount(INWORD)", "Table1", "SellerPanNO"], "additionalProperties": false}}}