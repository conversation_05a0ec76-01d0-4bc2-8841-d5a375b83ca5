from pydantic import BaseModel
from typing import Optional
from typing import List

class ModelFieldItem(BaseModel):
    FieldName: str
    FieldCategory: str
    FieldFormat: Optional[str] = None 
    FieldDescription: Optional[str] = None 
    FieldNotes: Optional[str] = None


class ModelTable(BaseModel):
    TableName: Optional[str] = None 
    Fields: Optional[List[ModelFieldItem]]


class InvoiceFieldItemUpdate(BaseModel):
    FieldName: Optional[str] = None 
    FieldType: Optional[str] = None 
    FieldDescription: Optional[str] = None 
    isLineItemTableField: Optional[bool] = None 


class UpdateVendor(BaseModel):
    Name: Optional[str] = None 
    FamilyName: Optional[str] = None 
    
class GetAllModelFilterQuery(BaseModel):
    bModelNameAsc : Optional[bool] = None
    bModelFamilyNameAsc : Optional[bool] = None
    bTotalDocsAsc : Optional[bool] = None
    