"""
Test Module: test_IndianInvRouteParagAquant.py

Purpose:
    This module tests the Indian Invoice Processing API for Parag Aquant documents.
    It processes PDF files containing invoice data and validates the API response against expected outputs.

Test Flow:
    1. Upload PDF file to the API endpoint
    2. Receive processed ZIP file containing XML and CSV outputs
    3. Extract and validate the outputs against reference files (when BRELEARN=False)
    4. Save outputs for future reference (when BRELEARN=True)

File Types Supported:
    - Input: PDF files (Parag Aquant invoice format)
    - Output: ZIP files containing XML and CSV data

Date: 2025
"""

import unittest
import os
import asyncio
import socket
import json
import httpx
from Test.CustomHelper import TestLogger, CLicenseHelper, calculate_checksum, compare_xml, compare_csv, extract_zip

# Learning Mode Configuration
# BRELEARN = True:  Save API outputs as reference files for future testing
# BRELEARN = False: Compare API outputs against existing reference files
BRELEARN = True

# Directory Structure Configuration
TEST_DIR = "Test"  # Root directory for all test-related files

# Dynamic folder structure based on current file and BRELEARN flag
CURRENT_FILE_NAME = os.path.splitext(os.path.basename(__file__))[0]  # Gets filename without .py extension
INPUT_DIR = os.path.join(TEST_DIR, "Input", CURRENT_FILE_NAME)
REFERENCE_DIR = os.path.join(TEST_DIR, "Reference", CURRENT_FILE_NAME)
LOG_DIR = os.path.join(TEST_DIR, "Logs")
API_URL = "http://**************:8034/IndianInvTally/process_doc"
LICENSE_FILE = os.path.join(TEST_DIR, "license.lic")
LOG_FILE = f"{CURRENT_FILE_NAME}.log"

# Global input file paths
INPUT_PDF_FILENAME = "AQUANT.pdf"

# Function to get dynamic paths based on BRELEARN flag and test method
def get_dynamic_paths(test_method_name):
    """
    Generate dynamic file paths based on BRELEARN flag and test method name.

    This function implements the dynamic folder structure that organizes test data
    based on the current test file and method being executed.

    Directory Structure Created:
        BRELEARN = True:  Test/Input/{current_file_name}/{test_method_name}/
        BRELEARN = False: Test/Reference/{current_file_name}/{test_method_name}/

    Args:
        test_method_name (str): Name of the test method being executed
                               (e.g., "test_ParagAquant")

    Returns:
        tuple: A tuple containing:
            - data_dir (str): Full path to the test data directory
            - input_file_path (str): Full path to the input PDF file

    Example:
        For test method "test_ParagAquant" with BRELEARN=True:
        Returns: ("Test/Input/test_IndianInvRouteParagAquant/test_ParagAquant/",
                 "Test/Input/test_IndianInvRouteParagAquant/test_ParagAquant/AQUANT.pdf")
    """
    # Determine target directory based on BRELEARN flag
    if BRELEARN:
        # Learning mode: Save new outputs to Input directory
        data_dir = os.path.join(INPUT_DIR, test_method_name)
    else:
        # Testing mode: Use Reference directory for comparison
        data_dir = os.path.join(REFERENCE_DIR, test_method_name)

    # Create directory if it doesn't exist (ensures clean test environment)
    os.makedirs(data_dir, exist_ok=True)

    # Construct full path to input file
    input_file_path = os.path.join(data_dir, INPUT_PDF_FILENAME)

    return data_dir, input_file_path

# Initialize logger
os.makedirs(LOG_DIR, exist_ok=True)
test_logger = TestLogger(LOG_DIR, LOG_FILE)
test_logger.MSWriteLog("info", "Test logger initialized")

class TestIndianInvRouteParagAquant(unittest.IsolatedAsyncioTestCase):
    """
    Test class for Parag Aquant document processing.

    This class contains test methods that validate the Indian Invoice Processing API
    for Parag Aquant PDF documents. It inherits from IsolatedAsyncioTestCase to support
    asynchronous test execution.

    Test Methods:
        - test_ParagAquant: Main test method for Parag Aquant document processing

    Setup/Teardown:
        - asyncSetUp: Initializes test environment and creates necessary directories
        - asyncTearDown: Cleans up after test execution

    Utility Methods:
        - is_server_listening: Checks if the API server is available
    """
    async def asyncSetUp(self):
        """
        Asynchronous setup method executed before each test method.

        This method prepares the test environment by:
        1. Creating dynamic directory structure based on BRELEARN flag
        2. Setting up logging for the current test method
        3. Ensuring all necessary directories exist

        The dynamic path structure ensures that each test method has its own
        isolated directory for input files and outputs.

        Raises:
            Exception: If directory creation or logger setup fails
        """
        try:
            # Get dynamic paths based on BRELEARN flag and current test method
            # This creates either Input or Reference directory structure
            self.strCurTestCaseDirPath, self.input_file_path = get_dynamic_paths(self._testMethodName)

            # Initialize logger for this test instance
            self.logger = test_logger
            test_logger.MSWriteLog("info", f"Setup completed for: {self._testMethodName}")
            test_logger.MSWriteLog("info", f"Using directory: {self.strCurTestCaseDirPath}")
            test_logger.MSWriteLog("info", f"Input file path: {self.input_file_path}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Setup failed: {str(e)}")
            raise

    async def asyncTearDown(self):
        """
        Clean up after the test case.
        """
        try:
            test_logger.MSWriteLog("info", f"Teardown completed for: {self.strCurTestCaseDirPath}")
        except Exception as e:
            test_logger.MSWriteLog("error", f"Teardown failed: {str(e)}")
            raise

    def is_server_listening(self, host="**************", port=8034, timeout=2):
        """
        Check if the server is listening on the specified host and port.
        Args:
            host (str): Server host.
            port (int): Server port.
            timeout (int): Timeout for connection attempt.
        Returns:
            bool: True if server is listening, False otherwise.
        """
        try:
            with socket.create_connection((host, port), timeout=timeout):
                test_logger.MSWriteLog("info", f"Server at {host}:{port} is listening")
                return True
        except (socket.timeout, ConnectionRefusedError) as e:
            test_logger.MSWriteLog("error", f"Server at {host}:{port} is not listening: {str(e)}")
            return False

    async def test_ParagAquant(self):
        """
        Main test method for Parag Aquant document processing.

        This test validates the complete workflow of processing Parag Aquant documents:

        Test Flow:
        1. License Verification: Validates API license and extracts user token
        2. File Preparation: Sets up input PDF file and output paths
        3. Server Connectivity: Checks if API server is available
        4. Document Upload: Sends PDF file to API endpoint with metadata
        5. Response Processing: Receives and validates ZIP response
        6. Content Extraction: Extracts XML and CSV from ZIP file
        7. File Validation: Compares outputs with reference files (BRELEARN=False)
                           or saves as new reference files (BRELEARN=True)

        API Endpoint: POST /IndianInvTally/process_doc
        User Type: Premium user (iUserid=4)
        Input Format: PDF file containing invoice data
        Output Format: ZIP file containing XML and CSV files

        Expected Outputs:
        - ParagAQUANT.xml: Structured XML representation of invoice data
        - Report_ParagAQUANT.csv: Tabular CSV report of processed data

        Raises:
            AssertionError: If API response is invalid or file comparison fails
            Exception: If license verification, file operations, or API calls fail
        """
        test_logger.MSWriteLog("info", f"Executing test: {self._testMethodName}")
        try:

            test_logger.MSWriteLog("info", "Step 1: Verifying license file")

            # Check if license file exists
            if not os.path.exists(LICENSE_FILE):
                test_logger.MSWriteLog("error", f"License file not found: {LICENSE_FILE}")
                self.fail(f"License file not found: {LICENSE_FILE}")

            # Extract license data and user token
            license_data = CLicenseHelper.MSVerifyLicense(LICENSE_FILE)
            strUserToken = license_data["Token"]
            user_id = license_data.get("uid", "unknown")
            test_logger.MSWriteLog("info", f"License verified, token: {strUserToken[:10]}..., uid: {user_id}")


            test_logger.MSWriteLog("info", "Step 2: Setting up file paths")

            # Define document name and file paths
            doc_name = "ParagAQUANT"  # Standardized document name derived from vendor
            strInputPdfPath = self.input_file_path  # Dynamic input file path
            strInputXmlPath = os.path.join(self.strCurTestCaseDirPath, f"{doc_name}.xml")
            strInputCsvPath = os.path.join(self.strCurTestCaseDirPath, f"Report_{doc_name}.csv")
            output_dir = self.strCurTestCaseDirPath  # Dynamic output directory
            strZipPath = os.path.join(output_dir, "Content_zipFile_Aquant.zip")

            test_logger.MSWriteLog("info", f"Input PDF: {strInputPdfPath}")
            test_logger.MSWriteLog("info", f"Output directory: {output_dir}")
            strOutputXmlPath = os.path.join(output_dir, f"{doc_name}.xml")
            strOutputCsvPath = os.path.join(output_dir, f"Report_{doc_name}.csv")

            # Verify input PDF file
            if not os.path.exists(strInputPdfPath):
                test_logger.MSWriteLog("error", f"Input PDF file not found: {strInputPdfPath}")
                self.fail(f"Input PDF file not found: {strInputPdfPath}")

            # Verify input XML and CSV files only if BRELEARN=False
            if not BRELEARN:
                for file_path in [strInputXmlPath, strInputCsvPath]:
                    if not os.path.exists(file_path):
                        test_logger.MSWriteLog("error", f"Input file not found: {file_path}")
                        self.fail(f"Input file not found: {file_path}")

            # Ensure output directory exists
            os.makedirs(output_dir, exist_ok=True)

            # Check server availability
            if not self.is_server_listening():
                test_logger.MSWriteLog("error", f"Server at {API_URL} is not available")
                self.fail(f"Server at {API_URL} is not available")

            # Prepare API request (matching the provided script)
            headers = {"Authorization": f"Bearer {strUserToken}", "iUserid": str(user_id)}
            checksum = calculate_checksum(strInputPdfPath, algorithm="md5")
            lsClientDocMetaData = [{
                "filename": INPUT_PDF_FILENAME,
                "Type": ".pdf",
                "location": strInputPdfPath,
                "checksum": checksum
            }]
            params = {
                "bTestMode": True,
                "strVoucherType": "PV_WITH_INVENTORY",
                "lsClientDocMetaData": json.dumps(lsClientDocMetaData),
                "bIsMultivendorDoc": False,
                "strSystemName": os.getlogin(),
                "strSerializeUserConfig": json.dumps({
                    "Exe_version": "2.4",
                    "Exe_ReleaseDate": "2025-06-28",
                    "Tdl_version": "8.0",
                    "Tdl_ReleaseDate": "2025-03-29",
                    "worker": 2,
                    "apiEndpoints": [
                        "http://122.170.3.105:8024/",
                        "http://**************:8034/",
                        "http://122.170.3.105:8034/",
                        "http://**************:8024/"
                    ]
                })
            }
            data = {"checksums": checksum}

            # Send POST request
            test_logger.MSWriteLog("info", f"Sending POST request to {API_URL}")
            try:
                with httpx.Client(timeout=1200) as client:
                    response = client.post(
                        API_URL,
                        headers=headers,
                        params=params,
                        files=[("documents", (INPUT_PDF_FILENAME, open(strInputPdfPath, "rb"), "application/pdf"))],
                        data=data
                    )
            except Exception as e:
                test_logger.MSWriteLog("error", f"API request failed: {str(e)}")
                self.fail(f"API request failed: {str(e)}")

            # Validate response
            if response.status_code != 200:
                test_logger.MSWriteLog("error", f"Non-200 response: {response.status_code}, content: {response.text}")
                self.fail(f"Expected status code 200, got {response.status_code}")
            self.assertEqual(response.headers["Content-Type"], "application/zip", f"Expected Content-Type application/zip, got {response.headers['Content-Type']}")

            # Verify and save ZIP file
            response_checksum = response.headers.get("X-Zip-Checksum")
            if not response_checksum:
                test_logger.MSWriteLog("error", "Checksum not received in response headers")
                self.fail("Checksum not received in response headers")
            with open(strZipPath, "wb") as f:
                for chunk in response.iter_bytes():
                    f.write(chunk)
            saved_checksum = calculate_checksum(strZipPath, algorithm="md5")
            self.assertEqual(saved_checksum, response_checksum, "Checksum mismatch! The downloaded file is corrupted")
            test_logger.MSWriteLog("info", f"ZIP file saved to: {strZipPath}")

            # Extract specific XML and CSV from ZIP
            xml_path, csv_path = extract_zip(strZipPath, output_dir, "DName2512002397.xml", "Report")
            self.assertTrue(os.path.exists(xml_path), f"XML file not found at: {xml_path}")
            test_logger.MSWriteLog("info", f"XML extracted to: {xml_path}")
            self.assertTrue(os.path.exists(csv_path), f"CSV file not found at: {csv_path}")
            test_logger.MSWriteLog("info", f"CSV extracted to: {csv_path}")

            # Rename extracted XML and CSV to standard names
            if os.path.basename(xml_path) != f"{doc_name}.xml":
                os.rename(xml_path, strOutputXmlPath)
                xml_path = strOutputXmlPath
                test_logger.MSWriteLog("info", f"Renamed XML to: {xml_path}")
            if os.path.basename(csv_path) != f"Report_{doc_name}.csv":
                os.rename(csv_path, strOutputCsvPath)
                csv_path = strOutputCsvPath
                test_logger.MSWriteLog("info", f"Renamed CSV to: {csv_path}")

            # Compare files if not in relearn mode
            if not BRELEARN:
                test_logger.MSWriteLog("info", f"Comparing XML: {strInputXmlPath} vs {xml_path}")
                bAreSameXml, strLogMessageXml = compare_xml(self.logger, strInputXmlPath, xml_path, ignore_tags=["VOUCHERNUMBER"])
                if not bAreSameXml:
                    test_logger.MSWriteLog("error", f"XML content mismatch: {strLogMessageXml}")
                    self.fail(f"XML content mismatch: {strLogMessageXml}")
                test_logger.MSWriteLog("info", "XML content matched")

                test_logger.MSWriteLog("info", f"Comparing CSV: {strInputCsvPath} vs {csv_path}")
                bAreSameCsv, strLogMessageCsv = compare_csv(self.logger, strInputCsvPath, csv_path, ignore_columns=["Received Date"])
                if not bAreSameCsv:
                    test_logger.MSWriteLog("error", f"CSV content mismatch: {strLogMessageCsv}")
                    self.fail(f"CSV content mismatch: {strLogMessageCsv}")
                test_logger.MSWriteLog("info", "CSV content matched")

        except Exception as e:
            test_logger.MSWriteLog("error", f"Test failed: {str(e)}")
            raise

if __name__ == "__main__":
    try:
        test_logger.MSWriteLog("info", "Starting unittest execution")
        asyncio.run(unittest.main(verbosity=2))
        test_logger.MSWriteLog("info", "Unittest execution completed")
    except Exception as e:
        test_logger.MSWriteLog("error", f"Top-level error: {str(e)}")
        raise