import imaplib
import email
import os
import time
import json
from dotenv import load_dotenv
import magic  # Import python-magic
import asyncio
import csv
from datetime import datetime
import logging

import sys
sys.path.append(".")

from src.Controllers.GPTResponse_controller import DocumentUploader, CGPTResponseData
from src.Controllers.ParagTradersControllers import CParagTraders

class AsyncDocument:
    def __init__(self, filename, content_type, data):
        self.filename = filename
        self.content_type = content_type
        self._data = data

    async def read(self):
        return self._data

    @property
    def size(self):
        return len(self._data)
    
    def get_extension(self):
        return os.path.splitext(self.filename)[1].lower()

class EmailAttachmentDownloader:
    def __init__(
        self, 
        user_id, 
        model_name, 
        model_family_name, 
        email_account, 
        provider, 
        password, 
        user_directory,
        logger
    ):
        # Load environment variables
        load_dotenv()

        self.logger = logger
        self.logger.info(f"Initializing EmailAttachmentDownloader for {email_account}")

        # Set user-specific credentials and download directory
        self.user_id = user_id
        self.model_name = model_name
        self.model_family_name = model_family_name
        self.email_account = email_account
        self.email_password = password
        self.email_provider = provider.lower()
        self.user_dir = user_directory
        
        # Get today's date in the desired format
        today = datetime.now().strftime("%Y_%m_%d")

        # Create the download directory with today's date
        self.download_dir = os.path.join(user_directory, today)

        self.report_dir = os.path.join(user_directory, "Daily_Automation_Reports")

        # Make all required directories
        os.makedirs(self.download_dir, exist_ok=True)
        self.logger.debug(f"Download directory set to {self.download_dir}")
        os.makedirs(self.report_dir, exist_ok=True)
        self.logger.debug(f"Report directory set to {self.report_dir}")
        
        # Set IMAP server based on provider
        if self.email_provider == 'zoho':
            self.imap_server = os.getenv('ZOHO_IMAP_SERVER')
        elif self.email_provider == 'gmail':
            self.imap_server = os.getenv('GMAIL_IMAP_SERVER')
        else:
            self.logger.error("Unsupported email provider. Choose 'zoho' or 'gmail'.")
            raise ValueError("Unsupported email provider. Choose 'zoho' or 'gmail'.")

        self.imap_port = int(os.getenv('IMAP_PORT'))
        self.mail = None

        # Initialize report file path
        today_date_str = datetime.now().strftime('%Y-%m-%d')
        self.report_file = os.path.join(self.report_dir, f"Report_{today_date_str}.csv")
        self.initialize_report()

    def initialize_report(self):
        """Initialize the report CSV file with headers if it doesn't exist."""
        if not os.path.isfile(self.report_file):
            self.logger.info(f"Creating new report file: {self.report_file}")
            with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
                writer = csv.DictWriter(csvfile, fieldnames=[
                    'Received Date',
                    'Vendor Name',
                    'Invoice No',
                    'Invoice Date',
                    'Total Amount',
                    'Tally Punch-in Status',
                    'Accuvelocity Comments'
                ])
                writer.writeheader()
        else:
            self.logger.debug(f"Report file already exists: {self.report_file}")

    async def mark_email_as_read(self, email_id, retries=3, delay=5):
        """
        Mark the email as read. Retries the operation if it fails.
        :param email_id: The ID of the email to be marked as read.
        :param retries: Number of retries to perform in case of failure.
        :param delay: Delay in seconds between retries.
        :return: True if successful, False otherwise.
        """
        for attempt in range(1, retries + 1):
            try:
                self.mail.store(email_id, '+FLAGS', '\\Seen')
                self.logger.info(f"Email ID {email_id.decode()} marked as read on attempt {attempt}.")
                return True
            except Exception as e:
                self.logger.error(f"Attempt {attempt} failed to mark email ID {email_id.decode()} as read: {e}")
                if attempt < retries:
                    self.logger.info(f"Retrying in {delay} seconds...")
                    time.sleep(delay)
        self.logger.error(f"Failed to mark email ID {email_id.decode()} as read after {retries} attempts.")
        return False

    def update_report(self, entry):
        """
        Update the report CSV with a new entry.
        If an entry for the same filename exists, update only the provided fields
        while keeping the existing data for other fields intact.
        """
        self.logger.debug(f"Updating report for Invoice No: {entry.get('Invoice No')}")
        
        # Read existing entries
        entries = []
        file_updated = False
        if os.path.isfile(self.report_file):
            with open(self.report_file, mode='r', newline='', encoding='utf-8') as csvfile:
                reader = csv.DictReader(csvfile)
                for row in reader:
                    # Check if the current row matches the entry to be updated
                    if row['Invoice No'] == entry.get('Invoice No'):
                        # Update only provided fields and retain others
                        for key, value in entry.items():
                            if value is not None:
                                row[key] = value
                        file_updated = True
                    entries.append(row)
        
        # If the file was not found in the report, append the new entry
        if not file_updated:
            entries.append(entry)
        
        # Write back all entries
        with open(self.report_file, mode='w', newline='', encoding='utf-8') as csvfile:
            fieldnames = [
                'Received Date',
                'Vendor Name',
                'Invoice No',
                'Invoice Date',
                'Total Amount',
                'Tally Punch-in Status',
                'Accuvelocity Comments'
            ]
            writer = csv.DictWriter(csvfile, fieldnames=fieldnames)
            writer.writeheader()
            writer.writerows(entries)
        
        self.logger.info(f"Report updated for file: {entry.get('Invoice No')}")


    def connect_to_mailbox(self):
        try:
            self.logger.info(f"Connecting to IMAP server {self.imap_server}:{self.imap_port} for {self.email_account}")
            # Connect to the email server
            self.mail = imaplib.IMAP4_SSL(self.imap_server, self.imap_port)
            self.mail.login(self.email_account, self.email_password)
            self.mail.select('inbox')
            # self.logger.info(f"Successfully connected to mailbox for {self.email_account}")
        except Exception as e:
            self.logger.error(f"Error connecting to mailbox for {self.email_account}: {e}")
            self.mail = None

    async def download_attachments(self):
        if not self.mail:
            self.logger.warning(f"Not connected to the mailbox for {self.email_account}.")
            return

        try:
            # Search for all unseen (new) emails
            self.logger.debug("Searching for unseen emails.")
            status, messages = self.mail.search(None, '(UNSEEN)')
            if status != 'OK':
                self.logger.error(f"Failed to search emails for {self.email_account}: {status}")
                return

            email_ids = messages[0].split()
            self.logger.info(f"Found {len(email_ids)} new emails for {self.email_account}")

            if not email_ids:
                self.logger.info(f"No new emails to process for {self.email_account}")
                return

            # Initialize magic for MIME type detection
            mime = magic.Magic(mime=True)

            # Loop through each new email
            for email_id in email_ids:
                try:
                    self.logger.debug(f"Processing email ID: {email_id.decode()}")
                    # Fetch the email message by ID
                    status, data = self.mail.fetch(email_id, '(RFC822)')

                    if status != 'OK':
                        self.logger.error(f"Failed to fetch email ID {email_id.decode()} for {self.email_account}")
                        continue

                    # Parse the email content
                    raw_email = data[0][1]
                    msg = email.message_from_bytes(raw_email)

                    # Extract sender and received date
                    sender = msg.get('From', 'Unknown Sender')
                    received_on = msg.get('Date', 'Unknown Date')

                    self.logger.debug(f"Email from: {sender}, received on: {received_on}")

                    # Convert received_on to a standard format if possible
                    try:
                        received_on_parsed = datetime.strptime(received_on, '%a, %d %b %Y %H:%M:%S %z')
                        received_on_str = received_on_parsed.strftime('%m.%d.%Y')
                    except Exception:
                        received_on_str = received_on  # Keep original if parsing fails
                        self.logger.warning(f"Failed to parse received date: {received_on}")

                    # Loop through the email's parts to find attachments
                    for part in msg.walk():
                        if part.get_content_disposition() == 'attachment':
                            
                            filename = part.get_filename()

                            # Proceed only if filename exists
                            if filename:
                                self.logger.debug(f"Found attachment: {filename}")

                                # Define allowed file extensions
                                allowed_extensions = [
                                    '.pdf', '.docx', '.xlsx', '.csv', '.txt', 
                                    '.jpeg', '.jpg', '.png', '.bmp', '.webp'
                                ]

                                if any(filename.lower().endswith(ext) for ext in allowed_extensions):
                                    self.logger.info(f"Processing attachment: {filename}")

                                    # Initialize report entry
                                    report_entry = {
                                        'Received Date': received_on_str,
                                        'Vendor Name': self.model_name,
                                        'Invoice No':'',
                                        'Invoice Date':'',
                                        'Total Amount':'',
                                        'Tally Punch-in Status':'',
                                        'Accuvelocity Comments':''
                                    }

                                    try:
                                        # Download the attachment data
                                        file_data = part.get_payload(decode=True)
                                        if file_data:
                                            strFilePath = os.path.join(self.download_dir, filename)
                                            
                                            try:
                                                with open(strFilePath, "wb") as attachmentFile:
                                                    attachmentFile.write(file_data)
                                                self.logger.info(f"Attachment saved: {strFilePath}")
                                            except Exception as e:
                                                self.logger.error(f"Failed to save the attachment {filename} for {self.email_account}: {e}")

                                            # Use python-magic to detect the actual MIME type
                                            actual_mime_type = mime.from_buffer(file_data)
                                            if actual_mime_type:
                                                content_type = actual_mime_type
                                                self.logger.debug(f"Detected MIME type: {content_type} for file: {filename}")
                                            else:
                                                content_type = part.get_content_type()
                                                self.logger.warning(f"Using default MIME type: {content_type} for file: {filename}")

                                            # Create an AsyncDocument instance with accurate MIME type
                                            document = AsyncDocument(
                                                filename=filename,
                                                content_type=content_type,
                                                data=file_data
                                            )

                                            # Instantiate DocumentUploader
                                            objDocUploader = DocumentUploader()

                                            # 1 Upload the document
                                            dictUploadedDocData = await objDocUploader.upload_document(
                                                user_id=self.user_id, 
                                                document=document,  # Pass the AsyncDocument instance
                                                strFamilyName=self.model_family_name, 
                                                model_name=self.model_name, 
                                                bUsePaidModel=True  # Adjust this as needed
                                            )
                                            
                                            self.logger.info(f"Downloaded and uploaded: {filename} for {self.email_account}")
                                            iDocID = dictUploadedDocData.get("DocId", None)
                                            # 2 Perform Extraction
                                            await CGPTResponseData.performDocumentExtractionFromDocumentID( userid=self.user_id, 
                                                                                                            document_id=iDocID, 
                                                                                                            isTrialPaidDocExtraction=True)

                                            # Get extracted Data for adding in report
                                            dictDocData = await CGPTResponseData.MSGetDocumentByIdForAWSExtraction(iUserID=self.user_id, iDocId=iDocID)
                                            strInvoiceNo = dictDocData["DocExtractedData"]["InvoiceNo"]
                                            strInvoceDate = dictDocData["DocExtractedData"]["InvoiceDate"]
                                            strInvoceDate = await CParagTraders.MSConvertIntToDate(strInvoceDate)
                                            strTotalAmount = dictDocData["DocExtractedData"]["GrandTotal"]

                                            report_entry['Invoice No'] = strInvoiceNo
                                            report_entry['Invoice Date'] = strInvoceDate
                                            report_entry['Total Amount'] = strTotalAmount

                                            # 3 Add to Tally
                                            objParagTraders = CParagTraders(user_id=self.user_id, doc_id = iDocID)
                                            result = await objParagTraders.MSCallTallyAPI()
                                            strTallyMessage = result[0].get("Result", "")

                                            if "Processed Successfully" in  strTallyMessage:                                       
                                                report_entry['Tally Punch-in Status'] = "Success"
                                            else:
                                                report_entry['Tally Punch-in Status'] = "Failure"

                                        else:
                                            self.logger.warning(f"No data found in attachment {filename} for {self.email_account}.")

                                    except Exception as e:
                                        self.logger.error(f"Failed to download or upload {filename} for {self.email_account}: {e}")
                                        report_entry['Tally Punch-in Status'] = "Failure"

                                    finally:
                                        # Update the report
                                        self.update_report(report_entry)
                                else:
                                    self.logger.debug(f"Skipping unsupported file type: {filename}")
                            else:
                                self.logger.warning("Found attachment without a filename.")
                except Exception as e:
                    self.logger.error(f"Failed to process email for {self.email_account}: {e}")

                # After all attachments were processed successfully, mark the email as read
                await self.mark_email_as_read(email_id)
                try:
                    self.mail.store(email_id, '+FLAGS', '\\Seen')
                    self.logger.debug(f"Email ID {email_id.decode()} marked as read.")
                except Exception as e:
                    self.logger.error(f"Failed to mark email ID {email_id.decode()} as read: {e}")

        except Exception as e:
            self.logger.error(f"Failed to process email for {self.email_account}: {e}")
            
    def close_connection(self):
        if self.mail:
            try:
                self.mail.close()
                self.mail.logout()
                self.logger.info(f"Closed connection to mailbox for {self.email_account}")
            except Exception as e:
                self.logger.error(f"Error closing mailbox for {self.email_account}: {e}")

    def start_polling(self, interval_minutes):
        while True:
            self.logger.info(f"Checking for new emails for {self.email_account}...")
            self.connect_to_mailbox()
            asyncio.run(self.download_attachments())  # Ensure this is run in an async context
            self.close_connection()
            self.logger.info(f"Waiting for {interval_minutes} minutes before next check for {self.email_account}...")
            time.sleep(interval_minutes * 60)

    @staticmethod
    async def poll_emails_for_user(user_info, interval_minutes, logger):
        downloader = EmailAttachmentDownloader(
            user_id=user_info['userId'],
            model_name=user_info['modelName'],
            model_family_name=user_info['modelFamilyName'],
            email_account=user_info['email'],
            provider=user_info['provider'],
            password=user_info['password'],
            user_directory=user_info['userDirectory'],
            logger=logger
        )
        while True:
            logger.info(f"Checking for new emails for {user_info['email']}...")
            downloader.connect_to_mailbox()
            await downloader.download_attachments()
            downloader.close_connection()
            logger.info(f"Waiting for {interval_minutes} minutes before next check for {user_info['email']}...")
            await asyncio.sleep(interval_minutes * 60)

def setup_logging():
    log_dir = r'Logs//Tally_logs'
    os.makedirs(log_dir, exist_ok=True)
    today_date_str = datetime.now().strftime('%Y-%m-%d')
    log_file = os.path.join(log_dir, f"Log_{today_date_str}.log")

    logger = logging.getLogger('EmailAttachmentDownloaderLogger')
    logger.setLevel(logging.DEBUG)  # Set to DEBUG to capture all levels of log messages

    # Create file handler which logs even debug messages
    fh = logging.FileHandler(log_file, encoding='utf-8')
    fh.setLevel(logging.DEBUG)

    # Create console handler with a higher log level
    ch = logging.StreamHandler()
    ch.setLevel(logging.INFO)

    # Create formatter and add it to the handlers
    formatter = logging.Formatter('%(asctime)s - %(name)s - %(levelname)s - %(message)s')
    fh.setFormatter(formatter)
    ch.setFormatter(formatter)

    # Add the handlers to the logger
    if not logger.handlers:
        logger.addHandler(fh)
        logger.addHandler(ch)

    return logger

if __name__ == '__main__':

    # objParagTraders = CSimpoloModel(user_id=2, doc_id = 208)
    # asyncio.run(objParagTraders.MSCallTallyAPI())

    # result = await objParagTraders.MSCallTallyAPI()
    
    # Setup logging
    logger = setup_logging()
    logger.info("Starting Email Attachment Downloader Program.")

    # Load user details from JSON file
    try:
        with open(r'resource\TallyUserConfig.json', 'r') as file:
            user_data = json.load(file)
        logger.info("Loaded user configuration successfully.")
    except Exception as e:
        logger.error(f"Failed to load user configuration: {e}")
        sys.exit(1)

    interval_minutes = 1  # Set the polling interval in minutes

    # Create a list of user information dictionaries
    user_info_list = [
        {
            'email': email,
            'provider': details['provider'],
            'password': details['password'],
            'userDirectory': details['userDirectory'],
            'userId': details['userId'],
            'modelName': details['modelName'],  # Add modelName
            'modelFamilyName': details['modelFamilyName']  # Add modelFamilyName
        }
        for email, details in user_data.items()
    ]

    # Run the pollers asynchronously
    async def main():
        tasks = []
        for user_info in user_info_list:
            tasks.append(EmailAttachmentDownloader.poll_emails_for_user(user_info, interval_minutes, logger))
        await asyncio.gather(*tasks)

    try:
        asyncio.run(main())
    except KeyboardInterrupt:
        logger.info("Program terminated by user.")
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
