import { GoDotFill } from "react-icons/go";
import { LiaCommentAlt } from "react-icons/lia";
import { pdfjs } from 'react-pdf';
import { RiPauseMiniLine } from "react-icons/ri";
import { RiPlayMiniFill } from "react-icons/ri";
import { MdOutlineModeComment } from "react-icons/md";
import '@react-pdf-viewer/default-layout/lib/styles/index.css';
import 'react-pdf/dist/esm/Page/AnnotationLayer.css';
import '@react-pdf-viewer/core/lib/styles/index.css';
import '@react-pdf-viewer/search/lib/styles/index.css';
import { useEffect, useMemo, useRef, useState } from "react";
import { useLocation } from "react-router-dom";
import { Formik, Form } from 'formik';
import axios from "axios";
import loading from '/animations/loadingTheme.svg';
import { jsPDF } from 'jspdf';
import mammoth from 'mammoth';
import html2canvas from 'html2canvas';
import { MdOutlineDoneAll } from "react-icons/md";
import toast, { Toaster } from 'react-hot-toast';
import { Tooltip } from "@material-tailwind/react";
import { isValid, format } from 'date-fns';
import { IoIosFastforward } from "react-icons/io";
import PropTypes from 'prop-types';
import { useNavigate } from 'react-router-dom';
import DocumentComponent from '../components/Preview/DocumentComment';
import DocViewer from '../components/Preview/DocViewer';
import dayjs from 'dayjs';
import CustomDatePicker from "../components/Preview/CustomDatePicker";
import customParseFormat from 'dayjs/plugin/customParseFormat';
import { setCookie, getCookie } from '../utils/cookieUtils';
import PaidSvg from "../assets/SVGs/Paid.svg"
import FreeSvg from "../assets/SVGs/Free.svg"
import TobeApproved from "../assets/SVGs/Status/TobeApproved.svg"
import ProcessingSvg from "../assets/SVGs/Status/Processing.svg"
import NotProcessSvg from "../assets/SVGs/Status/NotProcess.svg"
import ApprovedSvg from "../assets/SVGs/Status/Approved.svg"
import ErrorSvg from "../assets/SVGs/Status/error.svg"
import OnHoldSvg from "../assets/SVGs/Status/OnHold.svg"
import DocPendingForProcess from "../assets/SVGs/PendingForProcess.svg"

const navigateWithUpdatedState = (navigate, currentState, updatedDocId) => {
    navigate(location.pathname, {
        state: {
            ...currentState,
            docId: updatedDocId,
        },
    });
};

dayjs.extend(customParseFormat);
pdfjs.GlobalWorkerOptions.workerSrc = `//cdnjs.cloudflare.com/ajax/libs/pdf.js/${pdfjs.version}/pdf.worker.min.js`;

export default function Preview({ showAlert, isOpen }) {

    const location = useLocation();
    const { state } = location;

    const callerRoute = state && state.route
    const [docId, setDocId] = useState(state && state.docId)
    const docIds = state?.docIds || [];

    const navigate = useNavigate();

    // State to track the active field index
    const [isLoading, setIsLoading] = useState(true)
    const [changedFields, setChangedFields] = useState(new Set());
    const [currentDocIndex, setCurrentDocIndex] = useState(docIds.indexOf(docId));
    const [HighlightCoords, setHighlightCoords] = useState({});
    const [highlightCoordinates, setHighlightCoordinates] = useState([]); // values we get from api
    const [isDataFetched, setIsDataFetched] = useState(false);
    const [activeFieldIndex, setActiveFieldIndex] = useState(0);
    const [formData, setFormData] = useState(null);
    const [TableData, setTableData] = useState({});
    const [docComment, setDocComment] = useState("");
    const [DataKey, setDataKey] = useState('')
    const [documentData, setDocumentData] = useState(null);
    const [notProcessed, setNotProcessed] = useState(false);
    const [processingStatus, setProcessingStatus] = useState(false);
    const [activeTable, setActiveTable] = useState('');
    const [isTableActive, setIsTableActive] = useState(false);
    const [iteratingFormData, setIteratingFormData] = useState(true);
    const [fileUrl, setFileUrl] = useState('');
    const [approvedFields, setApprovedFields] = useState(new Set()); // To store approval status of each field
    const [approvedTableFields, setApprovedTableFields] = useState(new Set());
    const [initialApprovedFields, setInitialApprovedFields] = useState(new Set());
    const [showDropdown, setShowDropdown] = useState(false);
    const [openModal, setOpenModal] = useState(false);
    const [documentStatus, setDocumentStatus] = useState('');
    const [originalValues, setOriginalValues] = useState({});
    const [pageHeight, setPageHeight] = useState(null);
    const [pageWidth, setPageWidth] = useState(null);
    const [isError, setIsError] = useState(false);
    const [dataFormats, setDataFormats] = useState([]);

    useEffect(() => {
        if (docId && docId !== state.docId) {
            navigateWithUpdatedState(navigate, state, docId);
        }
    }, [docId]);

    // Function to toggle the modal visibility
    const fetchComment = async () => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/?DocId=${docId}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                }
            });

            const fetchedComment = response.data.UploadedDoc.Comment;
            setDocComment(fetchedComment);
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error("Oops! We couldn't load the comment right now. Please try again later.");
        }
    };

    const toggleModal = () => {
        setOpenModal(!openModal);
        fetchComment(); // Fetch comment when opening the modal
    };

    // Function to toggle the dropdown menu
    const toggleDropdown = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setShowDropdown(!showDropdown);
    };

    const resetState = () => {
        setTableData({});
        setApprovedFields(new Set());
        setApprovedTableFields(new Set());
        setInitialApprovedFields(new Set());
        setProcessingStatus(false)
        setNotProcessed(false);
        setIsDataFetched(false);
        setActiveTable(``);
        setDocumentStatus(``);
    };

    const fetchDocumentData = async (id) => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/?DocId=${id}`,
                {
                    headers: {
                        "Authorization": `Bearer ${localStorage.getItem('token')}`
                    },
                });

            const docComment = response.data.UploadedDoc.Comment;
            const status = response.data.UploadedDoc.Status;

            if (status === "NotProcess") {
                setNotProcessed(true);
            } else if (status === "Processing") {
                setProcessingStatus(true);
            }

            setDocComment(docComment)
            setDocumentData(response.data);

            // Extract page height and page width from the response
            const pageHeight = response.data.page_height;
            const pageWidth = response.data.page_width;

            // Set the state variables
            setPageHeight(pageHeight);
            setPageWidth(pageWidth);

            let coordinates = []
            // Extract coordinates for fields
            if (Object.keys(response.data.DocCordinateData).length > 0) {
                coordinates = response.data.DocCordinateData.Fields.map(field => {
                    const fieldName = Object.keys(field)[0];
                    const value = field[fieldName];

                    // Check if value is an array with coordinates, otherwise set all to 0
                    if (Array.isArray(value) && value.length === 6) {
                        const [_, page, x1, y1, x2, y2] = value;
                        return { page, x1, y1, x2, y2 };
                    } else {
                        return { page: 0, x1: 0, y1: 0, x2: 0, y2: 0 };
                    }
                });
            }


            setHighlightCoordinates(coordinates);

            // New logic to handle the dynamic key for approved fields
            const approvedFieldsSet = new Set();
            const initialApprovedFieldsSet = new Set();

            Object.keys(response.data.ApprovedFields).forEach(key => {
                if (key !== 'Tables') {
                    response.data.ApprovedFields[key].forEach(field => {
                        const fieldName = Object.keys(field)[0];
                        if (field[fieldName] === 1) {
                            approvedFieldsSet.add(fieldName);
                            initialApprovedFieldsSet.add(fieldName);
                        }
                    });
                }
            });

            setIsError(Object.keys(response.data.ApprovedFields).length === 0)

            if (response.data.ApprovedFields.Tables) {
                response.data.ApprovedFields.Tables.forEach(tableObject => {
                    Object.entries(tableObject).forEach(([tableName, rows]) => {
                        rows.forEach((row, rowIndex) => {
                            Object.entries(row).forEach(([field, approvalStatus]) => {
                                if (approvalStatus === 1) {
                                    // Add cell-level approval
                                    approvedTableFields.add(`${tableName}[${rowIndex}][${field}]`);
                                    initialApprovedFieldsSet.add(`${tableName}[${rowIndex}][${field}]`);
                                }
                            });
                        });
                    });
                });
            }

            setApprovedFields(approvedFieldsSet);
            setApprovedTableFields(approvedTableFields);
            setInitialApprovedFields(initialApprovedFieldsSet);

            // Logic to handle the dynamic key for fields
            const formData = {};
            const originalValues = {};
            Object.keys(response.data.DocExtractedData).forEach(key => {
                if (key !== 'Tables') {
                    setDataKey(key)
                    response.data.DocExtractedData[key].forEach(field => {
                        const fieldName = Object.keys(field)[0];
                        formData[fieldName] = field[fieldName];
                        originalValues[fieldName] = field[fieldName];
                    });
                }
            });

            setFormData(formData);
            setIsDataFetched(true);

            // Separate logic to handle the 'Tables' key and store it using setTableData
            if (response.data.DocExtractedData.Tables) {
                // Iterate over the array of tables
                response.data.DocExtractedData.Tables.forEach(tableObject => {

                    // Each tableObject is an object where key is the table name and value is an array of rows
                    Object.keys(tableObject).forEach(tableName => {
                        const rows = tableObject[tableName];
                        // Check if rows is an array before processing
                        if (Array.isArray(rows)) {

                            // Create an array of item data for the table
                            const tableItems = rows.map(row => {
                                const rowData = {};
                                Object.keys(row).forEach(fieldName => {
                                    rowData[fieldName] = row[fieldName];
                                });
                                return rowData;
                            });

                            // Store the table data with its name
                            setTableData(prevTableData => ({
                                ...prevTableData,
                                [tableName]: tableItems,
                            }));

                            // Store the original table data
                            setOriginalValues(prevValues => ({
                                ...prevValues,
                                [tableName]: tableItems,
                            }));
                        }
                    });
                });

                const activeTableCookie = getCookie(`activeTable${id}`);

                if (activeTable.length == 0 && response.data.DocExtractedData.Tables.length > 0) {
                    if (activeTableCookie) {
                        // If the cookie exists, set the active table to its value
                        const activeTableFromCookie = activeTableCookie;
                        setActiveTable(activeTableFromCookie);
                    } else {
                        setActiveTable(Object.keys(response.data.DocExtractedData.Tables[0])[0]);
                    }
                }

                setOriginalValues(prevValues => ({
                    ...prevValues,
                    ...originalValues,
                }));
            }

            // Extracting all fields including from Tables
            const allFieldsList = [];
            // Extract from Fields
            if (response.data?.ModelFields?.Fields) {
                Object.values(response.data.ModelFields.Fields).forEach(field => {
                    allFieldsList.push({
                        FieldName: field.FieldName || "N/A",
                        FieldCategory: field.FieldCategory || "N/A",
                        FieldFormat: field.FieldFormat || "N/A"
                    });
                });
            }

            setDataFormats(allFieldsList)

        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Failed to fetch document data', error);
        }
    }

    useEffect(() => {
        if (docId) {
            setIsLoading(true);
            resetState();
            fetchDocumentData(docId);
            setIsLoading(false);
        }
    }, [docId]);

    useEffect(() => {
        const isPaidModel = documentData?.UploadedDoc?.IsPaidModel;
        if (isPaidModel === false) {
            toast.error(
                <div>
                    Lite mode used: Extraction will be <span style={{ fontWeight: 'bold' }}>Moderately Accurate</span> and <span style={{ fontWeight: 'bold' }}>No Highlight</span>
                </div>,
                {
                    type: 'error',
                    icon: '⚠️',
                    duration: 6000,
                }
            );
        }
    }, [documentData?.UploadedDoc?.IsPaidModel]);

    useEffect(() => {
        if (documentData && documentData.UploadedDoc) {
            setDocumentStatus(documentData.UploadedDoc.Status);
        }
    }, [documentData]);

    useEffect(() => {
        const handleBeforeUnload = (e) => {
            if (changedFields.size > 0) {
                const message = 'You have unsaved changes. Are you sure you want to leave?';
                e.returnValue = message;
                return message;
            }
        };

        window.addEventListener('beforeunload', handleBeforeUnload);

        return () => {
            window.removeEventListener('beforeunload', handleBeforeUnload);
        };
    }, [changedFields]);

    // const initialValues = formData || {};
    // This variable can be used to prevent rapid multiple submissions
    const isSubmitting = useRef(false);

    useEffect(() => {
        if (documentStatus === "NotProcess") setNotProcessed(true)
        else if (documentStatus === "Processing") setProcessingStatus(true);
        if (documentData?.UploadedDoc?.DocBinaryData) {
            if (documentData?.UploadedDoc?.file_type === 'PDF') {
                const blob = base64ToBlob(documentData.UploadedDoc.DocBinaryData, 'application/pdf');
                const url = URL.createObjectURL(blob);
                setFileUrl(url);
            } else if (documentData?.UploadedDoc?.file_type === 'JPEG' || documentData?.UploadedDoc?.file_type === 'PNG' || documentData?.UploadedDoc?.file_type === 'WEBP' || documentData?.UploadedDoc?.file_type === 'HEIF' || documentData?.UploadedDoc?.file_type === 'HEIC' || documentData?.UploadedDoc?.file_type === 'BMP') {
                const blob = base64ToBlob(documentData.UploadedDoc.DocBinaryData, 'image/png'); // or 'image/png'
                const url = URL.createObjectURL(blob);
                setFileUrl(url);
            }
            else if (documentData?.UploadedDoc?.file_type === 'TXT') {
                // Decode base64 to text for non-PDF files
                const text = atob(documentData.UploadedDoc.DocBinaryData);
                convertTextToPDF(text);
            } else if (documentData?.UploadedDoc?.file_type === 'docx' || documentData?.UploadedDoc?.file_type === 'doc') {
                convertWordToPDF(documentData.UploadedDoc.DocBinaryData);
            }

            return () => {
                // Cleanup function to revoke the object URL
                fileUrl && URL.revokeObjectURL(fileUrl);
            };
        }
    }, [documentData]);

    useEffect(() => {
        // Wait for a short time before highlighting to ensure the PDF is ready
        highlightFieldAtIndex(activeFieldIndex, formData, true);

    }, [formData]);


    const handleNextDoc = () => {
        resetState(); // Ensure state is reset before moving to the next document
        setCurrentDocIndex(prevIndex => {
            const nextIndex = (prevIndex + 1) % docIds.length;
            const newDocId = docIds[nextIndex];
            setDocId(newDocId);
            return nextIndex;
        });
    };

    const base64ToBlob = (base64, mimeType) => {
        const binaryString = window.atob(base64);
        const binaryLen = binaryString.length;
        const bytes = new Uint8Array(binaryLen);
        for (let i = 0; i < binaryLen; i++) {
            bytes[i] = binaryString.charCodeAt(i);
        }
        return new Blob([bytes], { type: mimeType });
    };

    const setDocOnHold = async (docId) => {

        let newStatus;
        if (documentStatus === "OnHold") {
            if (allFieldsApproved) {
                newStatus = "Approved";
            } else {
                if (isError) {
                    newStatus = "Error";
                } else {
                    newStatus = "ToBeApproved";
                }
            }
        } else {
            newStatus = "OnHold";
        }

        try {
            const response = await axios.put(`${import.meta.env.VITE_SERVER}/doc/?DocId=${docId}&eNewStatus=${newStatus}`, {}, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                setDocumentStatus(newStatus)

            } else {
                toast.error("Server Error, Please try again later.")
            }


        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Failed to put document on hold', error);
        }
    };

    function isValidDateString(dateString) {
        const regex = /^[A-Za-z]{3}\s[A-Za-z]{3}\s\d{2}\s\d{4}\s\d{2}:\d{2}:\d{2}\sGMT[+-]\d{4}\s\(.+\)$/;
        return regex.test(dateString);
    }

    const convertTextToPDF = (text) => {
        const pdf = new jsPDF();
        pdf.text(text, 10, 10);
        const pdfBlob = pdf.output('blob');
        const pdfUrl = URL.createObjectURL(pdfBlob);
        setFileUrl(pdfUrl);
    };

    const convertWordToPDF = async (base64Word) => {
        try {
            // Convert base64 string to a Blob
            const wordBlob = base64ToBlob(base64Word, 'application/vnd.openxmlformats-officedocument.wordprocessingml.document');

            // Use mammoth.js to convert Blob to HTML
            const buffer = await wordBlob.arrayBuffer();
            const result = await mammoth.convertToHtml({ arrayBuffer: buffer });
            const html = result.value; // The generated HTML

            // Create a temporary element to hold the HTML
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = html;
            document.body.appendChild(tempDiv);

            // Use html2canvas to capture the rendered HTML
            html2canvas(tempDiv, { useCORS: true }).then(canvas => {
                const imgData = canvas.toDataURL('image/png');

                // Create a new jsPDF instance and add the image
                const pdf = new jsPDF();
                pdf.addImage(imgData, 'PNG', 10, 10);
                const pdfBlob = pdf.output('blob');
                const pdfUrl = URL.createObjectURL(pdfBlob);
                setFileUrl(pdfUrl);

                // Clean up the temporary element
                document.body.removeChild(tempDiv);
            });
        } catch (error) {
            console.error('Error converting Word to PDF:', error);
        }
    };

    const coordsOfForm = (index) => {
        if (index < highlightCoordinates.length) {
            return highlightCoordinates[index];
        }
        return { page: 0, x1: 0, y1: 0, x2: 0, y2: 0 };
    };

    // Function to check if all coordinates are 0
    const areAllCoordsZero = (coords) => {
        return coords.x1 === 0 && coords.y1 === 0 && coords.x2 === 0 && coords.y2 === 0;
    };

    // Function to highlight the PDF field at a given index
    const highlightFieldAtIndex = (index, data, isForm) => {
        // Determine which data set to use
        if (data) {
            const dataKeys = Object.keys(data);
            // console.log(dataKeys)
            if (index < dataKeys.length) {
                const keyword = data[dataKeys[index]];
                let value;
                if (Array.isArray(keyword)) {
                    value = keyword[0]?.toString(); // Get the first element of the array as a string
                } else {
                    value = keyword?.toString(); // Convert the value to a string directly
                }
                if (value !== null && value !== undefined) {
                    // Determine highlight coordinates based on the index
                    let highlightCoords;
                    if (isForm) {
                        highlightCoords = coordsOfForm(index);
                    }
                    // Pass the highlight coordinates to the DocViewer component if they are not all 0
                    if (!areAllCoordsZero(highlightCoords)) {
                        setHighlightCoords(highlightCoords);
                    } else {
                        setHighlightCoords({});
                    }
                }
            }
        }
    };

    // Function to scroll to active form field
    const scrollToActiveField = () => {
        const activeFormField = document.getElementById('active');
        const formFieldsContainer = document.getElementById('formFields');
        if (activeFormField) {
            const offsetTop = activeFormField.offsetTop;
            if (formFieldsContainer) {
                formFieldsContainer.scrollTo({
                    top: offsetTop - formFieldsContainer.offsetTop - 20,
                    behavior: 'instant'
                });
            }
        }
    };

    // Function to approve a field(form and table)
    const approveField = async (fieldKey) => {
        if (isSubmitting.current) return;
        isSubmitting.current = true;

        const formKeys = Object.keys(formData);
        const tableKeys = Object.keys(TableData);

        let requestBody;

        // Check if fieldKey is a table name
        const isTableField = tableKeys.includes(fieldKey);

        if (!isTableField) {
            // Handle only formData
            let updatedValue = formData[fieldKey];

            // Check if the fieldKey is a date field and format the date
            if (isDateField(fieldKey) && updatedValue && (isValidDate(updatedValue) || isFullDateString(updatedValue))) {
                updatedValue = dayjs(updatedValue).format('MM-DD-YYYY');
            }

            // If there are no tables, handle only formData
            if (tableKeys.length === 0) {
                requestBody = {
                    bApprove: true,
                    UpdatedVal: {
                        [DataKey]: [{ [fieldKey]: updatedValue }]
                    }
                };
            } else {
                // Existing logic for when tables are present
                if (iteratingFormData) {
                    requestBody = {
                        bApprove: true,
                        UpdatedVal: {
                            [DataKey]: [{ [fieldKey]: updatedValue }]
                        }
                    };
                } else {
                    const tableArray = transformTableDataToObjectArray(TableData);
                    const tableName = Object.keys(tableArray[activeFieldIndex])[0];
                    const tableBody = tableArray[activeFieldIndex][tableName].map((item, index) => ({
                        rowIndex: index,
                        values: item
                    }));

                    requestBody = {
                        bApprove: true,
                        UpdatedVal: {
                            Tables: [
                                {
                                    [tableName]: tableBody
                                }
                            ]
                        }
                    };
                }
            }

            // Update originalValues for formData
            setOriginalValues((prev) => ({
                ...prev,
                [fieldKey]: updatedValue
            }));

        } else {
            // Handle the case where the entire table is approved
            const tableArray = transformTableDataToObjectArray(TableData);
            const tableName = Object.keys(tableArray[activeFieldIndex])[0];
            const tableBody = tableArray[activeFieldIndex][tableName].map((item, index) => ({
                rowIndex: index,
                values: item
            }));

            requestBody = {
                bApprove: true,
                UpdatedVal: {
                    Tables: [
                        {
                            [tableName]: tableBody
                        }
                    ]
                }
            };

            // Update originalValues for table data
            setOriginalValues((prev) => ({
                ...prev,
                [tableName]: TableData[tableName]
            }));
        }

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
            setDocumentStatus(response.data.Status);

            // Update Coordinates Data
            let coordinates = []
            // Extract coordinates for fields
            if (Object.keys(response?.data?.DocVerifiedData).length > 0) {
                coordinates = response?.data?.DocVerifiedData?.Fields?.map(field => {
                    const fieldName = Object.keys(field)[0];
                    const value = field[fieldName];

                    // Check if value is an array with coordinates, otherwise set all to 0
                    if (Array.isArray(value) && value.length === 6) {
                        const [_, page, x1, y1, x2, y2] = value;
                        return { page, x1, y1, x2, y2 };
                    } else {
                        return { page: 0, x1: 0, y1: 0, x2: 0, y2: 0 };
                    }
                });
            }
            setHighlightCoordinates(coordinates);

            if (!isTableField) {
                setApprovedFields((prev) => {
                    const newApproved = new Set(prev);
                    newApproved.add(fieldKey);
                    return newApproved;
                });
                setInitialApprovedFields((prev) => {
                    const newApproved = new Set(prev);
                    newApproved.add(fieldKey);
                    return newApproved;
                });
            }

            // Move to the next field or table
            if (tableKeys.length === 0) {
                let nextIndex = activeFieldIndex + 1;
                while (nextIndex !== activeFieldIndex) {
                    const nextFieldKey = formKeys[nextIndex % formKeys.length];
                    if (!approvedFields.has(nextFieldKey)) {
                        setActiveFieldIndex(nextIndex % formKeys.length);
                        highlightFieldAtIndex(nextIndex % formKeys.length, formData, true);
                        break;
                    }
                    nextIndex++;
                }

                if (nextIndex === activeFieldIndex) {
                    setIteratingFormData(true);
                    setActiveFieldIndex(-1);
                    // Reset scroll position using scrollTo({ top: 0 })
                    document.getElementById('formFields').scrollTo({
                        top: 0,
                        behavior: 'instant' // Optional for immediate scrolling
                    });
                } else {
                    // Auto-scroll to the active form field within the formFields div
                    if (activeFieldIndex > 4) {
                        scrollToActiveField();
                    }
                }

            } else {
                if (iteratingFormData) {
                    let nextIndex = activeFieldIndex + 1;
                    let foundNextField = false;
                    let iterations = 0;

                    while (nextIndex !== activeFieldIndex && iterations < formKeys.length - 1) {
                        const nextFieldKey = formKeys[nextIndex % formKeys.length];
                        if (!approvedFields.has(nextFieldKey)) {
                            setActiveFieldIndex(nextIndex % formKeys.length);
                            highlightFieldAtIndex(nextIndex % formKeys.length, formData, true);
                            foundNextField = true;
                            break;
                        }
                        nextIndex++;
                        iterations++;
                    }
                    if (!foundNextField) {
                        setIteratingFormData(false);
                        setIsTableActive(true);
                        setActiveTable(tableKeys[0]);
                        setCookie(`activeTable${docId}`, tableKeys[0], 1);
                        setActiveFieldIndex(0);
                        console.log("is it last field")
                    } else {
                        if (activeFieldIndex > 4) {
                            scrollToActiveField();
                        }
                    }
                } else {
                    const tableArray = transformTableDataToObjectArray(TableData);
                    const tableName = Object.keys(tableArray[activeFieldIndex])[0];
                    const currentTableIndex = tableKeys.indexOf(activeTable);

                    if (currentTableIndex + 1 < tableKeys.length) {
                        // Update the approved fields set for all cells in the table
                        TableData[tableName].forEach((row, rowIndex) => {
                            Object.keys(row).forEach(key => {
                                approvedTableFields.add(`${tableName}[${rowIndex}][${key}]`);
                                initialApprovedFields.add(`${tableName}[${rowIndex}][${key}]`);
                            });
                        });
                        setApprovedTableFields(new Set(approvedTableFields)); // Update state to trigger re-render
                        setInitialApprovedFields(new Set(initialApprovedFields)); // Update state to trigger re-render
                        setActiveTable(tableKeys[currentTableIndex + 1]);
                        setCookie(`activeTable${docId}`, tableKeys[currentTableIndex + 1], 1);
                        setActiveFieldIndex(currentTableIndex + 1);
                        // highlightFieldAtIndex(currentTableIndex + 1,TableData,false);
                    } else {
                        // Check if all previous tables are approved
                        let allPreviousTablesApproved = true;
                        for (let i = 0; i < currentTableIndex; i++) {
                            const previousTableName = tableKeys[i];
                            TableData[previousTableName].forEach((row, rowIndex) => {
                                Object.keys(row).forEach(key => {
                                    if (!approvedTableFields.has(`${previousTableName}[${rowIndex}][${key}]`)) {
                                        allPreviousTablesApproved = false;
                                    }
                                });
                            });
                        }

                        // Approve the last table regardless of previous tables' approval status
                        TableData[tableName].forEach((row, rowIndex) => {
                            Object.keys(row).forEach(key => {
                                approvedTableFields.add(`${tableName}[${rowIndex}][${key}]`);
                                initialApprovedFields.add(`${tableName}[${rowIndex}][${key}]`);
                            });
                        });
                        setApprovedTableFields(new Set(approvedTableFields)); // Update state to trigger re-render
                        setInitialApprovedFields(new Set(initialApprovedFields));
                        setIsTableActive(false);
                        setActiveFieldIndex(0);
                        setIteratingFormData(true);
                        highlightFieldAtIndex(0, formData, true);

                        if (allPreviousTablesApproved) {
                            // Approve all fields in all tables only when moving past the last table
                            tableArray.forEach(table => {
                                const tableName = Object.keys(table)[0];
                                TableData[tableName].forEach((row, rowIndex) => {
                                    Object.keys(row).forEach(key => {
                                        approvedTableFields.add(`${tableName}[${rowIndex}][${key}]`);
                                        initialApprovedFields.add(`${tableName}[${rowIndex}][${key}]`);
                                    });
                                });
                            });
                            setApprovedTableFields(new Set(approvedTableFields));
                            setInitialApprovedFields(new Set(initialApprovedFields));
                        }
                        // Reset scroll position using scrollTo({ top: 0 })
                        document.getElementById('formFields').scrollTo({
                            top: 0,
                            behavior: 'instant' // Optional for immediate scrolling
                        });
                    }
                }
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving field:', error);
        } finally {
            isSubmitting.current = false;
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                newChanged.delete(fieldKey);
                return newChanged;
            });
        }
    };

    //function to approve form Field with check icon
    const approveFormField = async (fieldKey) => {

        if (isSubmitting.current) return;
        isSubmitting.current = true;

        // Determine the value of bApprove
        const bApprove = !approvedFields.has(fieldKey);

        let requestBody;

        // Handle only formData
        let updatedValue = formData[fieldKey];

        // Check if the fieldKey is a date field and format the date
        if (isDateField(fieldKey) && updatedValue && (isValidDate(updatedValue) || isFullDateString(updatedValue))) {
            updatedValue = dayjs(updatedValue).format('MM-DD-YYYY');
        }

        // Handle only formData
        requestBody = {
            bApprove: bApprove,
            docID: docId,
            UpdatedVal: {
                [DataKey]: [{ [fieldKey]: updatedValue }]
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            // Update Coordinates Data
            let coordinates = []
            // Extract coordinates for fields
            if (Object.keys(response.data.DocVerifiedData).length > 0) {
                coordinates = response.data.DocVerifiedData.Fields.map(field => {
                    const fieldName = Object.keys(field)[0];
                    const value = field[fieldName];

                    // Check if value is an array with coordinates, otherwise set all to 0
                    if (Array.isArray(value) && value.length === 6) {
                        const [_, page, x1, y1, x2, y2] = value;
                        return { page, x1, y1, x2, y2 };
                    } else {
                        return { page: 0, x1: 0, y1: 0, x2: 0, y2: 0 };
                    }
                });
            }
            setHighlightCoordinates(coordinates);
            setHighlightCoords(coordinates[activeFieldIndex]);

            setDocumentStatus(response.data.Status);

            const approvedFieldsSet = new Set(approvedFields);
            const approvedFieldsInitSet = new Set(initialApprovedFields);

            // Update the approved fields set based on the response data
            if (response.data.DocApprovedData && response.status === 200) {
                if (bApprove) {
                    approvedFieldsSet.add(fieldKey);
                    approvedFieldsInitSet.add(fieldKey);
                } else {
                    approvedFieldsSet.delete(fieldKey);
                    approvedFieldsInitSet.delete(fieldKey);
                }
            }

            // Update the state to trigger re-render
            setApprovedFields(new Set(approvedFieldsSet));
            setInitialApprovedFields(new Set(approvedFieldsInitSet));
            // Update originalValues for formData
            setOriginalValues((prev) => ({
                ...prev,
                [fieldKey]: updatedValue
            }));

        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving field:', error);
        } finally {
            isSubmitting.current = false;
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                newChanged.delete(fieldKey);
                return newChanged;
            });
        }
    };

    const moveNext = () => {
        if (isSubmitting.current) return;

        const formKeys = Object.keys(formData);
        const tableKeys = Object.keys(TableData);

        // Function to scroll to active form field
        const scrollToActiveField = () => {
            const activeFormField = document.getElementById('active');
            const formFieldsContainer = document.getElementById('formFields');
            if (activeFormField) {
                const offsetTop = activeFormField.offsetTop;
                if (formFieldsContainer) {
                    formFieldsContainer.scrollTo({
                        top: offsetTop - formFieldsContainer.offsetTop - 20,
                        behavior: 'instant'
                    });
                }
            }
        };

        // If there are no tables, iterate only formData
        if (tableKeys.length === 0) {
            let nextIndex = activeFieldIndex + 1;
            if (nextIndex >= formKeys.length) {
                setActiveFieldIndex(0); // Optionally, handle the end of formData iteration
                highlightFieldAtIndex(0, formData, true);
                // Reset scroll position using scrollTo({ top: 0 })
                document.getElementById('formFields').scrollTo({
                    top: 0,
                    behavior: 'instant' // Optional for immediate scrolling
                });
            } else {
                setActiveFieldIndex(nextIndex);
                highlightFieldAtIndex(nextIndex, formData, true);
                // Auto-scroll to the active form field within the formFields div
                if (activeFieldIndex > 4) {
                    scrollToActiveField();
                }
            }
        } else {
            // Existing logic for when tables are present
            if (iteratingFormData) {
                let nextIndex = activeFieldIndex + 1;
                if (nextIndex >= formKeys.length) {
                    setIteratingFormData(false);
                    setActiveFieldIndex(0);
                    // highlightFieldAtIndex(0,TableData,false);
                    setIsTableActive(true);
                    setActiveTable(tableKeys[0]);
                    setHighlightCoords({});
                    setCookie(`activeTable${docId}`, tableKeys[0], 1);
                } else {
                    setActiveFieldIndex(nextIndex);
                    highlightFieldAtIndex(nextIndex, formData, true);
                    // Auto-scroll to the active form field within the formFields div
                    if (activeFieldIndex > 4) {
                        scrollToActiveField();
                    }
                }
            } else {
                const currentTableIndex = tableKeys.indexOf(activeTable);
                if (currentTableIndex + 1 < tableKeys.length) {
                    setActiveTable(tableKeys[currentTableIndex + 1]);
                    setCookie(`activeTable${docId}`, tableKeys[currentTableIndex + 1], 1);
                    setActiveFieldIndex(prev => prev + 1);
                    // highlightFieldAtIndex(currentTableIndex + 1,TableData,false);
                } else {
                    setIsTableActive(false);
                    setActiveFieldIndex(0);
                    setIteratingFormData(true);
                    highlightFieldAtIndex(0, formData, true);
                    // Reset scroll position using scrollTo({ top: 0 })
                    document.getElementById('formFields').scrollTo({
                        top: 0,
                        behavior: 'instant' // Optional for immediate scrolling
                    });
                }
            }
        }
    };


    const transformTableDataToObjectArray = (tableData) => {
        // Create an array to hold each table object
        let tablesArray = [];

        // Iterate over each property in the tableData object
        for (const tableName in tableData) {
            if (tableData.hasOwnProperty(tableName)) {
                // Push an object with the table name as key and the table data as value
                tablesArray.push({ [tableName]: tableData[tableName] });
            }
        }

        return tablesArray;
    };

    // Function to approve all fields(Table and Form)
    const approveAllFields = async () => {
        if (isSubmitting.current) return;
        isSubmitting.current = true;

        // Convert formData to the required array of objects format and format date fields
        const fieldsArray = Object.entries(formData).map(([key, value]) => {
            const formattedValue = (isDateField(key) && (isValidDate(value) || isFullDateString(value))) ? dayjs(value).format('MM-DD-YYYY') : value;
            // const formattedValue = value;
            return { [key]: formattedValue };
        });

        const tableArray = transformTableDataToObjectArray(TableData)
        const tableBody = tableArray.map(table => {
            const tableName = Object.keys(table)[0]; // Get the name of the table (assuming each object has only one key)
            return {
                [tableName]: table[tableName].map((item, index) => ({
                    rowIndex: index,
                    values: item
                }))
            };
        });

        const requestBody = {
            bApprove: true,
            UpdatedVal: {
                [DataKey]: fieldsArray,
                Tables: tableBody
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });
            setDocumentStatus(response.data.Status)
            const newApprovedSet = new Set([
                ...Object.keys(formData),
                ...Object.keys(TableData)
            ]);
            setApprovedFields(newApprovedSet);
            setInitialApprovedFields(newApprovedSet);
            setOriginalValues((prev) => ({
                ...prev,
                ...formData,
                ...TableData
            }));

            // Update the approved fields set for all cells in the table
            tableArray.map(table => {
                const tableName = Object.keys(table)[0]; // Get the name of the table (assuming each object has only one key)
                TableData[tableName].forEach((row, rowIndex) => {
                    Object.keys(row).forEach(key => {
                        approvedTableFields.add(`${tableName}[${rowIndex}][${key}]`);
                        initialApprovedFields.add(`${tableName}[${rowIndex}][${key}]`);
                    });
                });
            });

            setApprovedTableFields(new Set(approvedTableFields));
            setInitialApprovedFields(new Set(initialApprovedFields));
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving all fields:', error);
        } finally {
            isSubmitting.current = false;
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                newChanged.clear();
                return newChanged;
            });
        }
    };

    //function to approve table Fields(single cell of table)
    const approveTableField = async (tableName, rowIndex, fieldKey, value) => {
        // Determine the value of bApprove
        const bApprove = !approvedTableFields.has(`${tableName}[${rowIndex}][${fieldKey}]`);
        const requestBody = {
            bApprove: bApprove,
            UpdatedVal: {
                Tables: [
                    {
                        [tableName]: [
                            {
                                rowIndex: rowIndex,
                                values: {
                                    [fieldKey]: value
                                }
                            }
                        ]
                    }
                ]
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                const approvedFieldsSet = new Set(approvedTableFields);
                const approvedFieldsInitSet = new Set(initialApprovedFields);

                // Update the approved fields set based on the bApprove value
                if (bApprove) {
                    approvedFieldsSet.add(`${tableName}[${rowIndex}][${fieldKey}]`);
                    approvedFieldsInitSet.add(`${tableName}[${rowIndex}][${fieldKey}]`);
                } else {
                    approvedFieldsSet.delete(`${tableName}[${rowIndex}][${fieldKey}]`);
                    approvedFieldsInitSet.delete(`${tableName}[${rowIndex}][${fieldKey}]`);
                }

                setDocumentStatus(response.data.Status);

                // Update the state to trigger re-render
                setApprovedTableFields(new Set(approvedFieldsSet));
                setInitialApprovedFields(new Set(approvedFieldsInitSet));
                setOriginalValues(prev => ({
                    ...prev,
                    [tableName]: prev[tableName].map((row, index) =>
                        index === rowIndex ? { ...row, [fieldKey]: value } : row
                    )
                }));
            } else {
                console.error('Failed to approve field:', response.data);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving field:', error);
        } finally {
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                newChanged.delete(`${tableName}[${rowIndex}][${fieldKey}]`);
                return newChanged;
            });
        }
    };

    //function to approve table Row at single time
    const approveRowFields = async (tableName, rowIndex, row) => {
        const rowValues = Object.entries(row).reduce((acc, [key, value]) => {
            acc[key] = value;
            return acc;
        }, {});

        // Determine the value of bApprove for the entire row
        const rowFieldKeys = Object.keys(row).map(key => `${tableName}[${rowIndex}][${key}]`);
        const bApprove = !rowFieldKeys.every(fieldKey => approvedTableFields.has(fieldKey));

        const requestBody = {
            bApprove: bApprove,
            UpdatedVal: {
                Tables: [
                    {
                        [tableName]: [
                            {
                                rowIndex: rowIndex,
                                values: rowValues
                            }
                        ]
                    }
                ]
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                const approvedFieldsSet = new Set(approvedTableFields);
                const approvedFieldsInitSet = new Set(initialApprovedFields);

                // Update the approved fields set based on the bApprove value
                rowFieldKeys.forEach(fieldKey => {
                    if (bApprove) {
                        approvedFieldsSet.add(fieldKey);
                        approvedFieldsInitSet.add(fieldKey);
                    } else {
                        approvedFieldsSet.delete(fieldKey);
                        approvedFieldsInitSet.delete(fieldKey);
                    }
                });

                setDocumentStatus(response.data.Status);

                // Update the state to trigger re-render
                setApprovedTableFields(new Set(approvedFieldsSet));
                setInitialApprovedFields(new Set(approvedFieldsInitSet));
                // Update the originalValues state with the approved row data
                setOriginalValues(prev => ({
                    ...prev,
                    [tableName]: prev[tableName].map((existingRow, index) =>
                        index === rowIndex ? { ...existingRow, ...rowValues } : existingRow
                    )
                }));
            } else {
                console.error('Failed to approve row:', response.data);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving row:', error);
        } finally {
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                rowFieldKeys.forEach(key => newChanged.delete(key));
                return newChanged;
            });
        }
    };

    //function to approve table column at single time
    const approveColumnFields = async (tableName, columnKey) => {
        const columnValues = TableData[tableName].map((row, rowIndex) => ({
            rowIndex: rowIndex,
            values: {
                [columnKey]: row[columnKey]
            }
        }));

        // Determine the value of bApprove for the entire column
        const columnFieldKeys = TableData[tableName].map((row, rowIndex) => `${tableName}[${rowIndex}][${columnKey}]`);
        const bApprove = !columnFieldKeys.every(fieldKey => approvedTableFields.has(fieldKey));

        const requestBody = {
            bApprove: bApprove,
            UpdatedVal: {
                Tables: [
                    {
                        [tableName]: columnValues
                    }
                ]
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                const approvedFieldsSet = new Set(approvedTableFields);
                const approvedFieldsInitSet = new Set(initialApprovedFields);

                // Update the approved fields set based on the bApprove value
                columnFieldKeys.forEach(fieldKey => {
                    if (bApprove) {
                        approvedFieldsSet.add(fieldKey);
                        approvedFieldsInitSet.add(fieldKey);
                    } else {
                        approvedFieldsSet.delete(fieldKey);
                        approvedFieldsInitSet.delete(fieldKey);
                    }
                });

                setDocumentStatus(response.data.Status);

                // Update the state to trigger re-render
                setApprovedTableFields(new Set(approvedFieldsSet));
                setInitialApprovedFields(new Set(approvedFieldsInitSet));
                // Update the originalValues state with the approved column data
                setOriginalValues(prev => ({
                    ...prev,
                    [tableName]: prev[tableName].map(row => ({
                        ...row,
                        [columnKey]: row[columnKey]
                    }))
                }));
            } else {
                console.error('Failed to approve column:', response.data);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving column:', error);
        } finally {
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                columnFieldKeys.forEach(key => newChanged.delete(key));
                return newChanged;
            });
        }
    };

    //function to approve whole table single time
    const approveWholeTable = async (tableName) => {
        const tableValues = TableData[tableName].map((row, rowIndex) => ({
            rowIndex: rowIndex,
            values: row
        }));

        // Determine the value of bApprove for the entire table
        const tableFieldKeys = TableData[tableName].flatMap((row, rowIndex) =>
            Object.keys(row).map(key => `${tableName}[${rowIndex}][${key}]`)
        );
        const bApprove = !tableFieldKeys.every(fieldKey => approvedTableFields.has(fieldKey));

        const requestBody = {
            bApprove: bApprove,
            UpdatedVal: {
                Tables: [
                    {
                        [tableName]: tableValues
                    }
                ]
            }
        };

        try {
            const response = await axios.post(`${import.meta.env.VITE_SERVER}/upload-updated-gpt-json/?DocId=${docId}`, requestBody, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            if (response.status === 200) {
                const approvedFieldsSet = new Set(approvedTableFields);
                const approvedFieldsInitSet = new Set(initialApprovedFields);

                // Update the approved fields set based on the response data
                if (response.data.DocApprovedData && response.data.DocApprovedData.Tables) {
                    response.data.DocApprovedData.Tables.forEach(tableObject => {
                        Object.entries(tableObject).forEach(([tableName, rows]) => {
                            rows.forEach((row, rowIndex) => {
                                Object.entries(row).forEach(([field, approvalStatus]) => {
                                    const fieldKey = `${tableName}[${rowIndex}][${field}]`;
                                    if (approvalStatus === 1) {
                                        approvedFieldsSet.add(fieldKey);
                                        approvedFieldsInitSet.add(fieldKey);
                                    } else {
                                        approvedFieldsSet.delete(fieldKey);
                                        approvedFieldsInitSet.delete(fieldKey);
                                    }
                                });
                            });
                        });
                    });
                }

                setDocumentStatus(response.data.Status);

                // Update the state to trigger re-render
                setApprovedTableFields(new Set(approvedFieldsSet));
                setInitialApprovedFields(new Set(approvedFieldsInitSet));
                // Update the originalValues state with the approved table data
                setOriginalValues(prev => ({
                    ...prev,
                    [tableName]: TableData[tableName]
                }));
            } else {
                console.error('Failed to approve table:', response.data);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error approving table:', error);
        } finally {
            setChangedFields(prev => {
                const newChanged = new Set(prev);
                tableFieldKeys.forEach(key => newChanged.delete(key));
                return newChanged;
            });
        }

    };

    // Utility function to normalize date formats for comparison
    const normalizeDate = (date) => {
        const commonFormats = ['MM/DD/YYYY', 'DD/MM/YYYY', 'YYYY-MM-DD', 'DD-MM-YYYY', 'MM-DD-YYYY', 'YYYY/MM/DD'];

        if (typeof date === 'string') {
            // Try parsing the string date with various common formats
            for (let formatStr of commonFormats) {
                const parsedDate = dayjs(date, formatStr, true);
                if (parsedDate.isValid()) {
                    return parsedDate.format('YYYY-MM-DD');
                }
            }
            // If all else fails, try ISO parsing
            const isoParsedDate = dayjs(date);
            if (isoParsedDate.isValid()) {
                return isoParsedDate.format('YYYY-MM-DD');
            }
        } else if (date instanceof Date && isValid(date)) {
            // If already a Date object, format it directly
            return format(date, 'yyyy-MM-dd');
        }
        return format(new Date(), 'yyyy-MM-dd'); // Fallback to current date
    };

    const customHandleChange = (e, handleChange) => {
        e.preventDefault();
        const { name, value } = e.target;
        // Check if the field is a date field
        const isDate = isDateField(name);

        // Pattern to extract table name, index, and key
        const match = name.match(/([^[]+)\[(\d+)\]\.(.+)/);
        if (match) {
            const tableName = match[1];
            const index = parseInt(match[2], 10);
            const key = match[3];
            let newValue = value;

            // Handle date field formatting
            if (isDate && isValidDateString(newValue)) {
                newValue = dayjs(newValue).format('MM-DD-YYYY');
            }

            if (TableData[tableName]) {
                // Use functional update form to ensure we're working with the most recent state
                setTableData(prevData => {
                    // Clone the existing table data
                    const updatedTable = [...prevData[tableName]];
                    // Update the changed field
                    updatedTable[index] = {
                        ...updatedTable[index],
                        [key]: newValue,
                    };

                    setChangedFields(prev => {
                        const newChanged = new Set(prev);
                        newChanged.add(`${tableName}[${index}][${key}]`);
                        return newChanged;
                    });

                    // Remove only the specific field from approvedTableFields if the value changes from the original value
                    if (newValue !== originalValues[tableName][index][key]) {
                        setApprovedTableFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.delete(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });
                        setChangedFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.add(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });

                    } else if (initialApprovedFields.has(`${tableName}[${index}][${key}]`)) {
                        // Add the specific field to approvedTableFields if the value matches the original value
                        setApprovedTableFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.add(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });
                        setChangedFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.delete(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });
                    }

                    if (newValue !== originalValues[tableName][index][key]) {
                        setChangedFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.add(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });

                    } else if (changedFields.has(`${tableName}[${index}][${key}]`)) {
                        setChangedFields(prev => {
                            const newApproved = new Set(prev);
                            newApproved.delete(`${tableName}[${index}][${key}]`);
                            return newApproved;
                        });
                    }

                    // Return the new state with only the modified table updated
                    return {
                        ...prevData,
                        [tableName]: updatedTable,
                    };
                });
            }
        } else {
            let newValue = value;
            // Handle date field formatting
            if (isDate && isValidDateString(newValue)) {
                newValue = dayjs(newValue).format('MM-DD-YYYY');
            }
            setFormData(prevData => ({
                ...prevData,
                [name]: newValue,
            }));

            const originalValue = originalValues[name];
            const newValueNormalized = isDate && isValidDateString(newValue) ? normalizeDate(newValue) : newValue.trim();
            const originalValueNormalized = isDate && isValidDateString(newValue) ? normalizeDate(originalValue) : originalValue.trim();


            if (newValueNormalized !== originalValueNormalized) {
                setApprovedFields(prev => {
                    const newApproved = new Set(prev);
                    newApproved.delete(name);
                    return newApproved;
                });
                setChangedFields(prev => {
                    const newChanged = new Set(prev);
                    newChanged.add(name);
                    return newChanged;
                });
            } else if (initialApprovedFields.has(name)) {
                setApprovedFields(prev => {
                    const newApproved = new Set(prev);
                    newApproved.add(name);
                    return newApproved;
                });
                setChangedFields(prev => {
                    const newChanged = new Set(prev);
                    newChanged.delete(name);
                    return newChanged;
                });
            }

            if (newValueNormalized !== originalValueNormalized) {
                setChangedFields(prev => {
                    const newChanged = new Set(prev);
                    newChanged.add(name);
                    return newChanged;
                });
            } else if (changedFields.has(name)) {
                setChangedFields(prev => {
                    const newChanged = new Set(prev);
                    newChanged.delete(name);
                    return newChanged;
                });
            }

            handleChange(e);
        }
    };

    const allTablesApproved = useMemo(() => {
        if (!TableData || !approvedTableFields) return false;

        return Object.entries(TableData).every(([tableName, rows]) =>
            rows.every((row, rowIndex) =>
                Object.keys(row).every((columnName) =>
                    approvedTableFields.has(`${tableName}[${rowIndex}][${columnName}]`)
                )
            )
        );
    }, [TableData, approvedTableFields]);

    // Check if all Data fields are approved
    const allFieldsApproved = useMemo(() => {
        const isFormDataNotEmpty = formData && Object.keys(formData).length > 0;
        const isTableDataNotEmpty = TableData && Object.keys(TableData).length > 0;

        const allFormFieldsApproved = isFormDataNotEmpty && Object.keys(formData).every((key) => approvedFields.has(key));
        const allTablesApproved = isTableDataNotEmpty && Object.entries(TableData).every(([tableName, rows]) =>
            rows.every((row, rowIndex) =>
                Object.keys(row).every((columnName) =>
                    approvedTableFields.has(`${tableName}[${rowIndex}][${columnName}]`)
                )
            )
        );
        
        return allFormFieldsApproved && allTablesApproved;
    }, [formData, TableData, approvedFields, approvedTableFields]);

    // Function to download the CSV file
    const downloadCsv = async (event) => {
        event.preventDefault(); // Prevents the default button click action
        event.stopPropagation(); // Stops the click from bubbling up to the form

        if (!formData || !TableData) return; // Check if formData and TableData are present

        let actualFileName = formData.InvoiceNumber;
        if (!actualFileName) {
            actualFileName = documentData?.UploadedDoc?.DocName;
            if (!actualFileName) actualFileName = "defaultFilename";
        }

        let DocId = parseInt(documentData?.UploadedDoc?.DocId, 10);

        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/download/?lsDocIds=${DocId}&strFileType=csv`, {
                headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` },
            });

            if (response.status === 200 && response?.data[0]?.ExtractedData) {
                const fileData = atob(response?.data[0].ExtractedData);
                const url = window.URL.createObjectURL(new Blob([fileData]));
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `${actualFileName}-data.csv`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
            else {
                toast.error('Failed to Download the data, Please try again later');
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error downloading the CSV file', error);
            toast.error('Failed to Download the data, Please try again later');

        }
        setShowDropdown(false);
    };

    const downloadExcel = async (event) => {
        event.preventDefault(); // Prevents the default button click action
        event.stopPropagation(); // Stops the click from bubbling up to the form

        if (!formData || !TableData) return; // Check if formData and TableData are present

        let actualFileName = formData.InvoiceNumber;
        if (!actualFileName) {
            actualFileName = documentData?.UploadedDoc?.DocName;
            if (!actualFileName) actualFileName = "defaultFilename";
        }

        let DocId = parseInt(documentData?.UploadedDoc?.DocId, 10);

        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/download/?lsDocIds=${DocId}&strFileType=excel`, {
                headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` }
            });

            if (response.status === 200 && response?.data[0]?.ExtractedData) {
                const fileData = atob(response.data[0].ExtractedData);
                const byteNumbers = new Array(fileData.length);
                for (let i = 0; i < fileData.length; i++) {
                    byteNumbers[i] = fileData.charCodeAt(i);
                }
                const byteArray = new Uint8Array(byteNumbers);
                const url = window.URL.createObjectURL(new Blob([byteArray], { type: 'application/vnd.openxmlformats-officedocument.spreadsheetml.sheet' }));
                const a = document.createElement('a');
                a.style.display = 'none';
                a.href = url;
                a.download = `${actualFileName}-data.xlsx`;
                document.body.appendChild(a);
                a.click();
                window.URL.revokeObjectURL(url);
                document.body.removeChild(a);
            }
            else {
                toast.error('Failed to Download the data, Please try again later');
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Error downloading the Excel file', error);
        }
        setShowDropdown(false);
    };

    const downloadText = (event) => {
        event.preventDefault(); // Prevents the default button click action
        event.stopPropagation(); // Stops the click from bubbling up to the form
        if (!formData) return;

        let actualFileName = formData.InvoiceNumber;

        if (actualFileName === undefined || actualFileName === "null" || actualFileName === "None" || actualFileName === null || !actualFileName) {
            actualFileName = documentData?.UploadedDoc?.DocName.replace(/\.[^/.]+$/, "");
        }
        const textFieldData = JsonToTextFile(formData);     // Hypothetical conversion function

        let fullData = textFieldData;

        // Append table data if available.
        if (TableData && Object.keys(TableData).length > 0) {
            let count = 0;
            Object.keys(TableData).forEach((key) => {
                if ((TableData[key][0])) {
                    count += 1;
                    fullData += `\n\n----------------------Table ${count}: ${key}----------------------\n`;

                    Object.keys(TableData[key]).forEach((rowKey, index) => {
                        const textTableData = JsonToTextFile(TableData[key][index]);
                        fullData += `\nRow ${index + 1}\n${textTableData}`;
                    });
                }

            });
        }

        const blob = new Blob([fullData], { type: 'text/plain' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${actualFileName}-data.txt`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        setShowDropdown(false)
    };


    const downloadJSON = (event) => {
        event.preventDefault(); // Prevents the default button click action
        event.stopPropagation(); // Stops the click from bubbling up to the form
        if (!formData) return;

        let actualFileName = formData.InvoiceNumber;

        if (actualFileName === undefined || actualFileName === "null" || actualFileName === "None" || actualFileName === null || !actualFileName) {
            actualFileName = documentData?.UploadedDoc?.DocName.replace(/\.[^/.]+$/, "");
        }

        const jsonData = {
            Fields: formData,
            Tables: TableData
        };

        const jsonString = JSON.stringify(jsonData, null, 2); // Pretty print JSON with 2 space indent
        const blob = new Blob([jsonString], { type: 'application/json' });
        const url = window.URL.createObjectURL(blob);
        const a = document.createElement('a');
        a.style.display = 'none';
        a.href = url;
        a.download = `${actualFileName}-data.json`;
        document.body.appendChild(a);
        a.click();
        window.URL.revokeObjectURL(url);
        document.body.removeChild(a);

        setShowDropdown(false);
    };


    const JsonToTextFile = (invoiceDetails, lineItemsKey = 'LineItemTable') => {
        let content = '';

        // Writing invoice details
        for (const [key, value] of Object.entries(invoiceDetails)) {
            // If the current key is the one for line items, skip it
            if (key === lineItemsKey) {
                continue;
            }
            content += `${key}: ${value}\n`;
        }

        // Check if "LineItemTable: " is a key and has content
        if (invoiceDetails.hasOwnProperty(lineItemsKey) && Array.isArray(invoiceDetails[lineItemsKey])) {
            content += "\n\nLineItemTable\n";
            // Writing line item details
            invoiceDetails[lineItemsKey].forEach((item, index) => {
                content += `\nRow${index + 1}\n`;
                for (const [key, value] of Object.entries(item)) {
                    content += `${key}:${value}\n`;
                }
            });
        }

        return content

    };

    const parseDate = (value, format, fallbackFormats) => {
        let date = dayjs(value, format, true);
        if (!date.isValid()) {
            date = dayjs(value, fallbackFormats, true);
        }
        return date;
    };

    const dateFormats = [
        'MMMM D, YYYY', 'MMMM D, YY', 'MMMM D,YYYY', 'MMMM D,YY',
        'DD MMMM YYYY', 'DD MMMM YY', 'DD MMM YYYY', 'DD MMM YY',
        'DD.MM.YYYY', 'DD.MM.YY', 'MM.DD.YYYY', 'MM.DD.YY',
        'YYYY.MM.DD', 'YY.MM.DD', 'YY-MM-DD', 'YYYY-MM-DD',
        'MM-DD-YYYY', 'MM-DD-YY', 'DD-MM-YY', 'DD-MM-YYYY',
        'MM/DD/YY', 'MM/DD/YYYY', 'DD/MM/YYYY', 'DD/MM/YY', 'YY/MM/DD',
        'YYYY/MM/DD'
    ];

    // Function to check if a date string is valid
    const isValidDate = (date) => {
        const valid = dayjs(date, dateFormats, true).isValid();

        return valid;
    };

    // Function to check if a field is a date field based on dataFormats
    const isDateField = (key) => {
        const fieldInfo = dataFormats.find(field => field.FieldName === key);
        return fieldInfo && fieldInfo.FieldCategory === 'Date';
    };

    // Function to get date format for a given field key
    const getDateFormat = (key) => {
        const fieldInfo = dataFormats.find(field => field.FieldName === key);
        return fieldInfo ? fieldInfo.FieldFormat : null;
    };

    const truncateDocComment = (text, wordLimit) => {
        const words = text.split(' ');
        if (words.length > wordLimit) {
            return words.slice(0, wordLimit).join(' ') + '...';
        }
        return text;
    };
    // Function to check if the date string is in the full date format
    const isFullDateString = (value) => {
        return /\w{3} \w{3} \d{2} \d{4} \d{2}:\d{2}:\d{2} GMT[+-]\d{4} \(\w+\s\w+\s\w+\)/.test(value);
    };

    const calculateCols = (tableName) => {
        const headers = Object.keys(TableData[tableName][0]);
        const maxCols = {};

        headers.forEach((header) => {
            const maxContentLength = Math.max(...TableData[tableName].map(row => (row[header]?.toString().length || 0)));
            maxCols[header] = Math.max(10, Math.ceil(maxContentLength / 2)); // Adjust the divisor for optimal textarea width
        });

        return maxCols;
    };

    const formatDate = (dateObj) => {
        // Extract components from the date object
        const { month, day, year, hours, minutes, am_pm } = dateObj;

        // Format the hours and minutes to ensure two digits
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        // Construct the formatted date string
        return `${month} ${day}, ${year} at ${formattedHours}:${formattedMinutes} ${am_pm} EDT`;
    };


    const greenTick = (
        <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="xl:w-[17px]">
            <g filter="url(#filter0_d_344_8400)">
                <circle cx="9.5" cy="8.5" r="8.5" fill="#11AF22" />
            </g>
            <path d="M6.8252 8.78322L8.60891 10.5664L12.1748 7" stroke="white" strokeWidth="0.713287" strokeLinecap="round" strokeLinejoin="round" />
            <defs>
                <filter id="filter0_d_344_8400" x="0.524476" y="0" width="17.951" height="17.951" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feGaussianBlur stdDeviation="0.237762" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_344_8400" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_344_8400" result="shape" />
                </filter>
            </defs>
        </svg>
    )

    const greyTick = (
        <svg width="19" height="18" viewBox="0 0 19 18" fill="none" xmlns="http://www.w3.org/2000/svg" className="xl:w-[17px]">
            <g filter="url(#filter0_d_344_8400)">
                <circle cx="9.5" cy="8.5" r="8.5" fill="#A5A5A5" />
            </g>
            <path d="M6.8252 8.78322L8.60891 10.5664L12.1748 7" stroke="white" strokeWidth="0.713287" strokeLinecap="round" strokeLinejoin="round" />
            <defs>
                <filter id="filter0_d_344_8400" x="0.524476" y="0" width="17.951" height="17.951" filterUnits="userSpaceOnUse" colorInterpolationFilters="sRGB">
                    <feFlood floodOpacity="0" result="BackgroundImageFix" />
                    <feColorMatrix in="SourceAlpha" type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 127 0" result="hardAlpha" />
                    <feGaussianBlur stdDeviation="0.237762" />
                    <feComposite in2="hardAlpha" operator="out" />
                    <feColorMatrix type="matrix" values="0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0 0.25 0" />
                    <feBlend mode="normal" in2="BackgroundImageFix" result="effect1_dropShadow_344_8400" />
                    <feBlend mode="normal" in="SourceGraphic" in2="effect1_dropShadow_344_8400" result="shape" />
                </filter>
            </defs>
        </svg>
    )

    const truncateText = (text, maxLength) => {
        if (text.length > maxLength) {
            return text.substring(0, maxLength) + '...';
        }
        return text;
    };

    const truncatedComment = docComment ? truncateDocComment(docComment, 30) : "";

    // Function to calculate the number of lines
    const calculateRows = (text) => {
        const charsPerLine = 40; // Adjust this number based on your average character per line
        return Math.min(Math.max(Math.ceil(text.length / charsPerLine), 1), 2); // Minimum 1 row, maximum 3 rows
    };

    return (
        <>
            <Toaster position="top-center"></Toaster>
            {/* Conditionally render the DocumentComponent and pass the onClose function */}
            {openModal && (
                <DocumentComponent
                    docId={docId}
                    docComment={docComment}
                    onClose={toggleModal}
                />
            )}
            {isLoading ? (
                <div className="min-h-screen flex justify-center items-center">
                    <img src={loading} className='h-[50vh]' alt="No data available" />
                </div>
            ) : notProcessed || processingStatus ? (
                <div className={`flex flex-grow flex-col ${showAlert ? 'max-h-[95vh]' : 'max-h-screen'} `} style={{ height: '100vh', overflow: 'hidden' }}>

                    {/* Header Section */}
                    <div className="px-6 py-2">
                        <h1 className="text-mb font-semibold text-[#707EAE]">
                            <div className="grid grid-cols-3 gap-3 overflow-none">
                                <div
                                    onClick={() => navigate(callerRoute === 'Home' ? '/' : callerRoute === 'My Documents' ? '/history' : `/${callerRoute}`)}
                                    style={{ display: 'flex', alignItems: 'left', cursor: 'pointer' }}
                                >
                                    <span style={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">{callerRoute}</span>
                                    {
                                        documentData?.UploadedDoc?.DocName.length > 60 ? (
                                            <Tooltip content={documentData?.UploadedDoc?.DocName} placement="bottom">
                                                <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">&nbsp;/ {truncateText(documentData?.UploadedDoc?.DocName, 40)}
                                                </div>
                                            </Tooltip>
                                        ) : (
                                            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">
                                                &nbsp;/ {documentData?.UploadedDoc?.DocName}
                                            </div>
                                        )
                                    }
                                </div>
                                <div className="col-span-1 ml-3 cursor-pointer truncate">
                                    {
                                        documentData?.UploadedDoc?.ModelName && documentData?.UploadedDoc?.ModelName.split('').length > 30 ? (
                                            <div className="flex items-center justify-end text-[0.9rem]"
                                                onClick={() => navigate('/AddModelPage', {
                                                    state: {
                                                        vName: documentData?.UploadedDoc?.ModelName,
                                                        vFamilyName: documentData?.UploadedDoc?.ModelFamilyName,
                                                        iModelId: documentData?.UploadedDoc?.ModelId
                                                    }
                                                })}
                                                style={{ textAlign: 'right', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                            >
                                                <span className="mr-1 ">Model Used:</span>
                                                <Tooltip content={documentData?.UploadedDoc?.IsPaidModel ? "Extracted Using Pro Mode" : "Extracted Using Lite Mode"} direction="down">
                                                    <div style={{ display: 'inline-block' }}>
                                                        {documentData?.UploadedDoc?.IsPaidModel ? (
                                                            <img src={PaidSvg} />
                                                        ) : (
                                                            <img src={FreeSvg} />
                                                        )}
                                                    </div>
                                                </Tooltip>

                                                {/* Tooltip for the model name */}
                                                <Tooltip content={documentData?.UploadedDoc?.ModelDescription} >
                                                    <span className="ml-1 mr-2">{truncateText(documentData?.UploadedDoc?.ModelName, 20)}</span>
                                                </Tooltip>
                                            </div>
                                        ) : (
                                            <div
                                                className="flex items-center justify-end text-[0.9rem]"
                                                onClick={() => navigate('/AddModelPage', {
                                                    state: {
                                                        vName: documentData?.UploadedDoc?.ModelName,
                                                        vFamilyName: documentData?.UploadedDoc?.ModelFamilyName,
                                                        iModelId: documentData?.UploadedDoc?.ModelId
                                                    }
                                                })}
                                                style={{ textAlign: 'right', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                            >
                                                <span className="mr-1">Model Used:</span>
                                                <Tooltip content={documentData?.UploadedDoc?.IsPaidModel ? "Extracted Using Pro Mode" : "Extracted Using Lite Mode"} direction="down">
                                                    <div style={{ display: 'inline-block' }}>
                                                        {documentData?.UploadedDoc?.IsPaidModel ? (
                                                            <img src={PaidSvg} />
                                                        ) : (
                                                            <img src={FreeSvg} />
                                                        )}
                                                    </div>
                                                </Tooltip>

                                                <Tooltip content={documentData?.UploadedDoc?.ModelDescription} >
                                                    <span className="ml-1 mr-2">{documentData?.UploadedDoc?.ModelName}</span>
                                                </Tooltip>
                                            </div>
                                        )
                                    }
                                </div>
                            </div>
                        </h1>
                    </div>
                    <>
                        <div
                            className="grid grid-cols-3 gap-3 overflow-none"
                            style={{ overflow: 'hidden' }}
                        >
                            {/* Left Side: PDF Viewer and Metadata */}
                            <div className={`col-span-2 ml-3`}>
                                {/* Metadata Section */}
                                <div
                                    className="p-4 bg-[#ffff] shadow-md"
                                    style={{ borderTopLeftRadius: '20px', borderTopRightRadius: '20px' }}
                                >
                                    <div className="flex justify-between items-center mb-2">
                                        <div style={{ maxWidth: '25%', overflow: 'hidden', textWrap: 'pretty' }}>
                                            <p className="2xl:text-sm xl:text-xs text-[#696F79] truncate">
                                                {documentData?.UploadedDoc?.DocName.length > 60 ? (
                                                    <>
                                                        <strong>File Name:</strong>
                                                        <Tooltip content={documentData?.UploadedDoc?.DocName} placement="bottom" style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                            {documentData?.UploadedDoc?.DocName}
                                                        </Tooltip>

                                                    </>
                                                ) : (
                                                    <>
                                                        <strong>File Name:</strong> {documentData?.UploadedDoc?.DocName}
                                                    </>
                                                )}
                                            </p>
                                        </div>
                                        <div className="flex items-center">
                                            <strong className="2xl:text-sm xl:text-xs text-[#696F79] mr-1">File Status:</strong>
                                            {documentStatus === 'OnHold' ? (
                                                <img src={OnHoldSvg} className="xl:w-[140px]" />
                                            ) : documentStatus === 'Error' ? (
                                                <Tooltip content={documentData?.UploadedDoc?.DocErrorMsg}>
                                                    <img src={ErrorSvg} className="xl:w-[140px]" />
                                                </Tooltip>
                                            ) : documentStatus === 'NotProcess' ? (
                                                <img src={NotProcessSvg} className="xl:w-[140px]" />
                                            ) : documentStatus === 'Processing' ? (
                                                <Tooltip content="Refresh to view latest status or it will be automatically updated once extraction is completed for all documents.">
                                                    <img src={ProcessingSvg} className="xl:w-[140px]" />
                                                </Tooltip>
                                            ) : null}
                                        </div>
                                        <p className="xl:text-xs 2xl:text-sm text-[#696F79]">
                                            <strong>Date:</strong> {formatDate(documentData?.UploadedDoc?.ModifiedDateTime)}
                                        </p>
                                    </div>
                                </div>

                                {/* PDF Viewer Section */}
                                <div className={`flex-grow overflow-y-auto ${showAlert
                                    ? (Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0)
                                        ? 'h-[55vh]'
                                        : 'h-[82vh]')
                                    : (Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0)
                                        ? 'h-[65vh]'
                                        : 'h-[86vh]')
                                    }`}
                                >
                                    {documentData?.UploadedDoc?.file_type === 'PNG' || documentData?.UploadedDoc?.file_type === 'JPG' || documentData?.UploadedDoc?.file_type === 'JPEG' || documentData?.UploadedDoc?.file_type === 'WEBP' || documentData?.UploadedDoc?.file_type === 'BMP' ? (
                                        <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="image" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                            pageWidth={pageWidth} />
                                    ) : documentData?.UploadedDoc?.file_type === 'TXT' ? (
                                        <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="text" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                            pageWidth={pageWidth} />
                                    ) : fileUrl ? (
                                        <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="pdf" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                            pageWidth={pageWidth} />
                                    ) : (
                                        <div>Loading PDF...</div>
                                    )}
                                </div>
                            </div>
                            <div className="col-span-1 ml-3">
                                <div className="flex justify-center items-center flex-col min-h-screen h-full ">
                                    <img src={DocPendingForProcess} />
                                    <label className="text-md m-3 text-[#003654]">This Document is pending for Processing</label>
                                </div>
                            </div >
                        </div >
                    </>
                </div >
            ) : (
                <div className={`flex flex-grow flex-col ${showAlert ? 'max-h-[95vh]' : 'max-h-screen'} `} style={{ height: '100vh', overflow: 'hidden' }}>
                    {/* Header Section */}
                    <div className="px-6 py-2">
                        <h1 className={`text-mb font-semibold text-[#707EAE]`}>
                            <div className="grid grid-cols-3 gap-3 overflow-none">
                                <div
                                    onClick={() => navigate(callerRoute === 'Home' ? '/' : callerRoute === 'My Documents' ? '/history' : `/${callerRoute}`)}
                                    style={{ display: 'flex', alignItems: 'left', cursor: 'pointer' }}
                                >
                                    <span style={{ whiteSpace: 'nowrap', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">{callerRoute}</span>
                                    {
                                        documentData?.UploadedDoc?.DocName.length > 60 ? (
                                            <Tooltip content={documentData?.UploadedDoc?.DocName} placement="bottom">
                                                <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">
                                                    &nbsp;/ {truncateText(documentData?.UploadedDoc?.DocName, 40)}
                                                </div>
                                            </Tooltip>
                                        ) : (
                                            <div style={{ whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }} className="xl:text-[0.9rem]">
                                                &nbsp;/ {documentData?.UploadedDoc?.DocName}
                                            </div>
                                        )
                                    }
                                </div>
                                <div className={`col-span-1 ml-3 cursor-pointer truncate  ${isOpen ? '' : '2xl:w-[34vw] xl:w-[75.5rem]'}`}>
                                    {
                                        documentData?.UploadedDoc?.ModelName && documentData?.UploadedDoc?.ModelName.split('').length > 50 ? (
                                            <div className="flex items-center justify-end text-[0.9rem]"
                                                onClick={() => navigate('/AddModelPage', {
                                                    state: {
                                                        vName: documentData?.UploadedDoc?.ModelName,
                                                        vFamilyName: documentData?.UploadedDoc?.ModelFamilyName,
                                                        iModelId: documentData?.UploadedDoc?.ModelId
                                                    }
                                                })}
                                                style={{ textAlign: 'right', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                            >
                                                <span className="mr-1">Model Used:</span>
                                                <Tooltip content={documentData?.UploadedDoc?.IsPaidModel ? "Extracted Using Pro Mode" : "Extracted Using Lite Mode"} direction="up">
                                                    <div style={{ display: 'inline-block' }}>
                                                        {documentData?.UploadedDoc?.IsPaidModel ? (
                                                            <img src={PaidSvg} />
                                                        ) : (
                                                            <img src={FreeSvg} />
                                                        )}
                                                    </div>
                                                </Tooltip>

                                                {/* Tooltip for the model name */}
                                                <Tooltip content={documentData?.UploadedDoc?.ModelName} direction="up">
                                                    <span className="ml-1 mr-2">{truncateText(documentData?.UploadedDoc?.ModelName, 50)}</span>
                                                </Tooltip>
                                            </div>
                                        ) : (
                                            <div
                                                className="flex items-center justify-end text-[0.9rem]"
                                                onClick={() => navigate('/AddModelPage', {
                                                    state: {
                                                        vName: documentData?.UploadedDoc?.ModelName,
                                                        vFamilyName: documentData?.UploadedDoc?.ModelFamilyName,
                                                        iModelId: documentData?.UploadedDoc?.ModelId
                                                    }
                                                })}
                                                style={{ textAlign: 'right', whiteSpace: 'nowrap', overflow: 'hidden', textOverflow: 'ellipsis' }}
                                            >
                                                <span className="mr-1 ">Model Used:</span>
                                                <Tooltip content={documentData?.UploadedDoc?.IsPaidModel ? "Extracted Using Pro Mode" : "Extracted Using Lite Mode"} direction="down">
                                                    <div style={{ display: 'inline-block' }}>
                                                        {documentData?.UploadedDoc?.IsPaidModel ? (
                                                            <img src={PaidSvg} />
                                                        ) : (
                                                            <img src={FreeSvg} />
                                                        )}
                                                    </div>
                                                </Tooltip>
                                                <span className="ml-1 mr-2">{documentData?.UploadedDoc?.ModelName}</span>
                                            </div>
                                        )
                                    }
                                </div>
                            </div>
                        </h1>
                    </div>

                    {formData && isDataFetched && (
                        <>
                            <Formik
                                initialValues={formData}
                                enableReinitialize={true}
                                onSubmit={async ({ setSubmitting }) => {
                                    setSubmitting(false);
                                    const token = localStorage.getItem('token');
                                    try {
                                        const response = await axios.post(`${import.meta.env.VITE_SERVER}/tally/upload?iDocID=${docId}`, null, {
                                            headers: { "Authorization": `Bearer ${token}` },
                                        });
                                        toast.success(response.data[0].Result);
                                    } catch (error) {
                                        if (error.response && error.response.data && error.response.data.detail) {
                                            toast.error(error.response.data.detail);
                                        } else {
                                            toast.error("An unexpected error occurred"); // Fallback message
                                        }
                                        console.error('Failed to Upload to tally', error);
                                        toast.error('Failed to Upload to tally');
                                    }
                                }}
                            >
                                {({ values, handleChange, handleBlur, handleSubmit, isSubmitting }) => (
                                    <Form
                                        onSubmit={handleSubmit}
                                        className="grid grid-cols-3 gap-3 overflow-none"
                                        style={{ overflow: 'hidden' }}
                                    >
                                        {/* Left Side: PDF Viewer and Metadata */}
                                        <div className={`col-span-2 ${isOpen ? 'xl:w-[54vw]' : '2xl:w-[67vw] xl:w-[64vw]'} ml-3`}>
                                            {/* Metadata Section */}
                                            <div
                                                className="p-4 bg-[#ffff] shadow-md"
                                                style={{ borderTopLeftRadius: '20px', borderTopRightRadius: '20px' }}
                                            >
                                                <div className="flex justify-between items-center mb-2">
                                                    <div style={{ maxWidth: '40%', overflow: 'hidden', textWrap: 'pretty' }}>
                                                        <p className="xl:text-xs 2xl:text-sm text-[#696F79] truncate">
                                                            {documentData?.UploadedDoc?.DocName.length > 60 ? (
                                                                <>
                                                                    <strong>File Name:</strong>
                                                                    <Tooltip content={documentData?.UploadedDoc?.DocName} placement="bottom" style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                                        {documentData?.UploadedDoc?.DocName}
                                                                    </Tooltip>
                                                                </>
                                                            ) : (
                                                                <>
                                                                    <strong>File Name:</strong> {documentData?.UploadedDoc?.DocName}
                                                                </>
                                                            )}
                                                        </p>
                                                    </div>
                                                    <div className="flex items-center">
                                                        <strong className="xl:text-xs 2xl:text-sm text-[#696F79] mr-1">File Status:</strong>
                                                        {documentStatus === 'OnHold' ? (
                                                            <img src={OnHoldSvg} className="xl:w-[140px]" />
                                                        ) : documentStatus === 'Processing' ? (
                                                            <Tooltip content="Refresh to view latest status or it will be automatically updated once extraction is completed for all documents.">
                                                                <img src={ProcessingSvg} className="xl:w-[140px]" />
                                                            </Tooltip>
                                                        ) : documentStatus === 'Error' ? (
                                                            <Tooltip content={documentData?.UploadedDoc?.DocErrorMsg}>
                                                                <img src={ErrorSvg} className="xl:w-[140px]" />
                                                            </Tooltip>
                                                        ) : documentStatus === 'NotProcess' ? (
                                                            <img src={NotProcessSvg} className="xl:w-[140px]" />
                                                        ) : allFieldsApproved || documentStatus === 'Approved' ? (
                                                            <img src={ApprovedSvg} className="xl:w-[140px]" />
                                                        ) : !allFieldsApproved || documentStatus === 'ToBeApproved' ? (
                                                            <img src={TobeApproved} className="xl:w-[140px]" />
                                                        ) : null}
                                                    </div>
                                                    <p className="xl:text-xs 2xl:text-sm text-[#696F79]">
                                                        <strong>Date:</strong> {formatDate(documentData?.UploadedDoc?.ModifiedDateTime)}
                                                    </p>
                                                </div>
                                            </div>

                                            {/* PDF Viewer Section */}
                                            <div className={`flex-grow overflow-y-auto ${showAlert
                                                ? (Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0)
                                                    ? 'h-[55vh]'
                                                    : 'h-[82vh]')
                                                : (Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0)
                                                    ? 'h-[58vh]'
                                                    : 'h-[86vh]')
                                                }`}>
                                                {documentData?.UploadedDoc?.file_type === 'PNG' || documentData?.UploadedDoc?.file_type === 'JPG' || documentData?.UploadedDoc?.file_type === 'JPEG' || documentData?.UploadedDoc?.file_type === 'WEBP' || documentData?.UploadedDoc?.file_type === 'BMP' ? (
                                                    <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="image" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                                        pageWidth={pageWidth} />
                                                ) : documentData?.UploadedDoc?.file_type === 'TXT' ? (
                                                    <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="text" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                                        pageWidth={pageWidth} />
                                                ) : fileUrl ? (
                                                    <DocViewer highlightCoords={HighlightCoords} contentUrl={fileUrl} contentType="pdf" isTable={Object.keys(TableData).length > 0} docData={documentData?.UploadedDoc?.DocBinaryData} fileName={documentData?.UploadedDoc?.DocName} isOpen={isOpen} pageHeight={pageHeight}
                                                        pageWidth={pageWidth} />
                                                ) : (
                                                    <div>Loading PDF...</div>
                                                )}
                                            </div>
                                        </div>

                                        {/* Right Side: Form Fields and Controls */}
                                        <div className={`absolute 2xl:w-[28vw] xl:w-[27vw] xl:text-xs 2xl:text-sm ${showAlert ? 'top-[4.3rem]' : 'top-[2rem]'} right-[0.5rem] 2xl:min-w-[10vw] xl:min-w-[7vw] mt-2 ml-3`}>
                                            <div className="flex bg-[#ffff] w-full" style={{ borderTopRightRadius: '20px', borderTopLeftRadius: '20px' }}>
                                                <svg className='mt-4 ml-5' width="22" height="21" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                    <path d="M4.05381 16.0787C3.77356 16.3379 3.77356 16.7581 4.05381 17.0173C4.33406 17.2764 4.78843 17.2765 5.06868 17.0173L4.05381 16.0787ZM16.9423 5.09804L17.6599 5.09804C17.6599 4.92203 17.5843 4.75323 17.4497 4.62877C17.3152 4.50431 17.1326 4.43439 16.9423 4.43439L16.9423 5.09804ZM16.2247 13.5349C16.2247 13.9014 16.546 14.1985 16.9423 14.1985C17.3386 14.1985 17.6599 13.9014 17.6599 13.5349L16.2247 13.5349ZM7.81942 4.43439C7.42309 4.43439 7.1018 4.73152 7.1018 5.09805C7.1018 5.46457 7.42309 5.7617 7.81942 5.7617L7.81942 4.43439ZM5.06868 17.0173L17.4497 5.56732L16.4349 4.62877L4.05381 16.0787L5.06868 17.0173ZM16.2247 5.09804L16.2247 13.5349L17.6599 13.5349L17.6599 5.09804L16.2247 5.09804ZM16.9423 4.43439L7.81942 4.43439L7.81942 5.7617L16.9423 5.7617L16.9423 4.43439Z" fill="#003654" />
                                                </svg>
                                                <label className="text-md mt-4 ml-1 font-bold text-[#003654]">Extraction {DataKey}:</label>
                                            </div>
                                            <div id='formFields' className={`bg-[#ffff]  overflow-y-auto ${Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0) && showAlert ? 'max-h-[59vh]' :
                                                Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0) && !showAlert ? 'max-h-[61.5vh]' :
                                                    !(Object.keys(TableData).length > 0 && Object.values(TableData).some(table => table.length > 0)) && showAlert ? 'max-h-[85vh]' :
                                                        'max-h-[91vh]'
                                                }
                                                `} style={{ borderBottomRightRadius: '20px', borderBottomLeftRadius: '20px' }}>
                                                <div className="flex flex-col m-3 gap-1 mb-4">
                                                    {Object.entries(values).map(([key, value], index) => {
                                                        let dateValue;
                                                        const isApproved = approvedFields.has(key);
                                                        const isActiveField = index === activeFieldIndex;
                                                        const fieldFormat = getDateFormat(key);
                                                        const isDate = isDateField(key);
                                                        if (isDate && fieldFormat !== "As per your Document") {
                                                            if (isValidDate(value)) {
                                                                dateValue = parseDate(value, fieldFormat, dateFormats).format(fieldFormat);
                                                                {/* dateValue = dayjs(value, [
                                                                    'MMMM D, YYYY', 'MMMM D, YY', 'MMMM D,YYYY', 'MMMM D,YY', 'DD MMMM YYYY', 'DD MMMM YY', 'DD MMM YYYY', 'DD MMM YY', 'MM.DD.YYYY', 'DD.MM.YYYY', 'YYYY.MM.DD', 'YY-MM-DD', 'YYYY-MM-DD', 'MM-DD-YYYY', 'MM-DD-YY', 'DD-MM-YY', 'DD-MM-YYYY', 'DD/MM/YYYY', 'DD/MM/YY', 'MM/DD/YY', 'YY/MM/DD', 'MM/DD/YYYY', 'YYYY/MM/DD'
                                                                    ], true).format(fieldFormat); */}
                                                            } else {
                                                                dateValue = value;
                                                            }
                                                        }
                                                        return (
                                                            <div key={key}
                                                                id={isActiveField ? 'active' : undefined}
                                                                className={`relative p-2 rounded-lg flex items-center ${isTableActive ? 'border-none' : isActiveField ? 'border-dashed border-2 border-[#003654]' : 'border-[#003654]'}`}
                                                            >
                                                                <label htmlFor={key} className="block 2xl:text-sm xl:text-xs font-medium tracking-tight text-gray-700 w-1/3">
                                                                    {key.replace(/([a-z])([A-Z])/g, '$1 $2').trim()}
                                                                </label>

                                                                <div className="flex items-center w-3/4">
                                                                    {isDate && fieldFormat !== "As per your Document" ? (
                                                                        isValidDate(value) ? (
                                                                            <div className={`flex flex-1 sm:text-sm p-0 focus:outline-none focus:ring-0 rounded-md resize-none bg-gray-200 ${isApproved ? 'bg-checked' : ''}`} onClick={() => {
                                                                                setActiveFieldIndex(index);
                                                                                setIsTableActive(false);
                                                                                setIteratingFormData(true);
                                                                                highlightFieldAtIndex(index, formData, true);
                                                                            }}>
                                                                                <CustomDatePicker
                                                                                    k={key}
                                                                                    dateValue={dateValue}
                                                                                    customHandleChange={customHandleChange}
                                                                                    handleChange={handleChange}
                                                                                    formateOfDate={fieldFormat}
                                                                                />
                                                                            </div>
                                                                        ) : (
                                                                            <textarea
                                                                                id={key}
                                                                                name={key}
                                                                                rows={calculateRows(value)}
                                                                                className={`flex-1 smo:text-sm p-1 focus:outline-none 2xl:text-md xl:text-xs rounded-md resize-none bg-gray-200 ${isApproved ? 'bg-checked' : ''}`}
                                                                                defaultValue={value}
                                                                                onClick={() => {
                                                                                    setActiveFieldIndex(index);
                                                                                    setIsTableActive(false);
                                                                                    setIteratingFormData(true);
                                                                                    highlightFieldAtIndex(index, formData, true);
                                                                                }}
                                                                                onChange={(e) => {
                                                                                    customHandleChange(e, handleChange);
                                                                                    const charLength = e.target.value.length;
                                                                                    const height = charLength < 40 ? 2 : Math.ceil((charLength / 40) + 2);
                                                                                    e.target.style.height = `${height}rem`;
                                                                                }}
                                                                                // onBlur={handleBlur}
                                                                                style={{
                                                                                    overflowX: "auto", height: `${value.length < 40 ? 2 : Math.min(Math.ceil((value.length / 40) + 2), 4)}rem`,
                                                                                    WebkitFontSmoothing: 'antialiased',
                                                                                    MozOsxFontSmoothing: 'grayscale',
                                                                                }}
                                                                            />
                                                                        )
                                                                    ) : (
                                                                        <textarea
                                                                            id={key}
                                                                            name={key}
                                                                            rows={calculateRows(value)}
                                                                            className={`flex-1 smo:text-sm p-1 focus:outline-none 2xl:text-md xl:text-xs rounded-md resize-none bg-gray-200 ${isApproved ? 'bg-checked' : ''}`}
                                                                            defaultValue={value}
                                                                            onClick={() => {
                                                                                setActiveFieldIndex(index);
                                                                                setIsTableActive(false);
                                                                                setIteratingFormData(true);
                                                                                highlightFieldAtIndex(index, formData, true);
                                                                            }}
                                                                            onChange={(e) => {
                                                                                customHandleChange(e, handleChange);
                                                                                const charLength = e.target.value.length;
                                                                                const height = charLength < 40 ? 2 : Math.ceil((charLength / 40) + 2);
                                                                                e.target.style.height = `${height}rem`;
                                                                            }}
                                                                            // onBlur={handleBlur}
                                                                            style={{
                                                                                overflowX: "auto", height: `${value.length < 40 ? 2 : Math.min(Math.ceil((value.length / 40) + 2), 4)}rem`,
                                                                                WebkitFontSmoothing: 'antialiased',
                                                                                MozOsxFontSmoothing: 'grayscale',
                                                                            }}
                                                                        />
                                                                    )}
                                                                </div>

                                                                <Tooltip
                                                                    content={isApproved ? `Unapprove This Field` : `Approve This Field`}
                                                                    placement="left"
                                                                >
                                                                    <span
                                                                        className={`absolute right-2 cursor-pointer mt-1 mr-1`}
                                                                        onClick={() => {
                                                                            approveFormField(Object.keys(formData)[index]);
                                                                            setActiveFieldIndex(index);
                                                                        }}
                                                                    >
                                                                        {isApproved ? greenTick : greyTick}
                                                                    </span>
                                                                </Tooltip>
                                                            </div>
                                                        );

                                                    })}
                                                </div>

                                                {/* Submission controls */}
                                                <div className="bg-[#003654] rounded-b-lg space-x-2 mt-4 2xl:p-3 xl:p-2 sticky bottom-0" style={{ borderBottomRightRadius: '20px', borderBottomLeftRadius: '20px' }}>
                                                    <div className="flex justify-between">
                                                        <svg width="139" height="29" viewBox="0 0 139 29" fill="none" xmlns="http://www.w3.org/2000/svg" className="xl:w-[120px]">
                                                            <path d="M9.08374 4.41546C9.18377 4.41546 9.26562 4.33362 9.2929 4.23359C9.47477 3.21509 9.4293 3.1969 10.5478 2.97866C10.657 2.96047 10.7297 2.87862 10.7297 2.7695C10.7297 2.66947 10.657 2.57853 10.5478 2.56034C9.42021 2.35119 9.46568 2.31481 9.2929 1.3145C9.26562 1.20538 9.19287 1.12354 9.08374 1.12354C8.96552 1.12354 8.90187 1.20538 8.87459 1.3145C8.66543 2.31481 8.72909 2.35119 7.61056 2.56034C7.50143 2.57853 7.43778 2.66947 7.43778 2.7695C7.43778 2.87862 7.50143 2.96047 7.62875 2.97866C8.72909 3.16962 8.66543 3.21509 8.87459 4.2154C8.90187 4.33362 8.96552 4.41546 9.08374 4.41546ZM6.13737 8.63495C6.28287 8.63495 6.41019 8.51674 6.43747 8.37124C6.72847 6.81621 6.62844 6.77074 8.31078 6.50702C8.46537 6.47974 8.57449 6.35243 8.57449 6.19783C8.57449 6.04324 8.46537 5.91593 8.31078 5.88865C6.62844 5.61584 6.71937 5.56127 6.43747 4.04262C6.41019 3.87893 6.29197 3.76072 6.13737 3.76072C5.97369 3.76072 5.86456 3.86984 5.82819 4.04262C5.53719 5.55218 5.63722 5.60674 3.95488 5.88865C3.80029 5.91593 3.69116 6.04324 3.69116 6.19783C3.69116 6.37062 3.80029 6.47974 3.98216 6.50702C5.62813 6.76165 5.53719 6.81621 5.82819 8.34395C5.86456 8.51674 5.96459 8.63495 6.13737 8.63495ZM10.3296 15.3279C10.5387 15.3279 10.7024 15.1824 10.7388 14.9642C11.3026 11.7177 11.6118 11.2903 14.8219 10.8629C15.0492 10.8356 15.2038 10.6629 15.2038 10.4446C15.2038 10.2354 15.0492 10.0718 14.8219 10.0354C11.63 9.61708 11.2208 9.20786 10.7388 5.94321C10.7024 5.72496 10.5387 5.57037 10.3296 5.57037C10.1204 5.57037 9.95674 5.72496 9.91127 5.94321C9.35655 9.18967 9.03827 9.60798 5.82819 10.0354C5.60084 10.0718 5.44625 10.2354 5.44625 10.4446C5.44625 10.6629 5.60084 10.8356 5.82819 10.8629C9.02009 11.2358 9.4384 11.6904 9.91127 14.9551C9.94765 15.1824 10.1022 15.3279 10.3296 15.3279ZM12.121 6.58887C12.2756 7.48914 12.3757 7.86199 12.8122 8.11661C14.0034 7.66192 15.3039 7.41639 16.5952 7.41639C20.4509 7.41639 23.6337 9.75348 23.6337 13.3091C23.6337 14.9278 22.7607 16.4556 21.4603 17.565C20.6601 16.5465 19.396 15.9281 17.9138 15.9281C15.6767 15.9281 14.0216 17.1012 14.0216 18.8199C14.0216 18.9472 14.0307 19.0655 14.0489 19.1837C10.5115 19.0564 7.75606 17.3286 7.75606 14.1094C7.75606 13.6547 7.8379 13.2364 7.97431 12.8453C7.71968 12.4361 7.20134 12.327 6.41928 12.1906C6.21922 12.7908 6.11009 13.4273 6.11009 14.1003C6.11009 18.338 9.73849 20.8205 14.3763 20.8205C14.7037 20.8205 15.0401 20.8024 15.3766 20.7842C15.7676 20.9479 16.2405 21.0388 16.7679 21.0388C18.1229 21.0388 19.487 20.675 20.7146 20.0476C20.7146 20.1203 20.7146 20.1931 20.7146 20.2658C20.7146 21.3025 20.1144 22.3301 19.1141 22.9849C18.8049 23.2031 18.5867 23.4395 18.5867 23.8033C18.5867 24.2216 18.9232 24.5672 19.4324 24.5672C19.6507 24.5672 19.8052 24.5035 20.0053 24.3762C21.4603 23.4214 22.3606 21.8481 22.3606 20.2476C22.3606 19.8384 22.3151 19.4474 22.2151 19.0836C24.0338 17.6468 25.2797 15.5735 25.2797 13.3182C25.2797 8.84411 21.4057 5.76134 16.5861 5.76134C15.0492 5.76134 13.5215 6.06143 12.121 6.58887ZM15.6767 18.7926C15.6767 18.0742 16.7134 17.5832 18.0229 17.5832C18.8868 17.5832 19.6234 17.9287 20.1144 18.5016C19.0777 19.0746 17.9138 19.4019 16.7679 19.4019C16.1405 19.4019 15.6767 19.1564 15.6767 18.7926Z" fill="white" />
                                                            <path d="M32.544 18L37.1858 5.40593H39.2728L43.8786 18H41.8636L40.7841 14.9234H35.6385L34.559 18H32.544ZM36.1782 13.4122H40.2443L38.2113 7.67286L36.1782 13.4122ZM48.6621 18.2159C47.5826 18.2159 46.731 17.8801 46.1073 17.2084C45.4956 16.5367 45.1897 15.5352 45.1897 14.2038V9.0762H47.0968V14.0059C47.0968 15.7331 47.8045 16.5967 49.2198 16.5967C49.9275 16.5967 50.5092 16.3448 50.965 15.841C51.4208 15.3373 51.6487 14.6176 51.6487 13.682V9.0762H53.5558V18H51.8646L51.7207 16.4347C51.4448 16.9865 51.037 17.4243 50.4972 17.7481C49.9695 18.06 49.3578 18.2159 48.6621 18.2159ZM60.0479 18.2159C59.1843 18.2159 58.4227 18.012 57.763 17.6042C57.1033 17.1964 56.5876 16.6386 56.2157 15.931C55.8439 15.2233 55.658 14.4197 55.658 13.5201C55.658 12.6205 55.8439 11.8229 56.2157 11.1272C56.5876 10.4196 57.1033 9.86783 57.763 9.47202C58.4347 9.06421 59.2023 8.8603 60.0659 8.8603C60.7736 8.8603 61.3913 8.99824 61.9191 9.27411C62.4588 9.54998 62.8786 9.9398 63.1785 10.4436V5.0461H65.0856V18H63.3764L63.1785 16.6147C62.8906 17.0345 62.4948 17.4063 61.991 17.7301C61.4873 18.054 60.8396 18.2159 60.0479 18.2159ZM60.3898 16.5607C61.2054 16.5607 61.8711 16.2788 62.3868 15.7151C62.9146 15.1513 63.1785 14.4257 63.1785 13.5381C63.1785 12.6385 62.9146 11.9129 62.3868 11.3611C61.8711 10.7974 61.2054 10.5155 60.3898 10.5155C59.5742 10.5155 58.9025 10.7974 58.3747 11.3611C57.847 11.9129 57.5831 12.6385 57.5831 13.5381C57.5831 14.1258 57.703 14.6476 57.9429 15.1034C58.1828 15.5591 58.5127 15.919 58.9325 16.1829C59.3643 16.4347 59.85 16.5607 60.3898 16.5607ZM68.5696 7.385C68.2098 7.385 67.9099 7.27705 67.67 7.06115C67.4421 6.83326 67.3282 6.55139 67.3282 6.21555C67.3282 5.87971 67.4421 5.60384 67.67 5.38794C67.9099 5.16005 68.2098 5.0461 68.5696 5.0461C68.9294 5.0461 69.2233 5.16005 69.4512 5.38794C69.6911 5.60384 69.811 5.87971 69.811 6.21555C69.811 6.55139 69.6911 6.83326 69.4512 7.06115C69.2233 7.27705 68.9294 7.385 68.5696 7.385ZM67.616 18V9.0762H69.5231V18H67.616ZM75.7829 18C74.9073 18 74.2117 17.7901 73.6959 17.3703C73.1802 16.9385 72.9223 16.1769 72.9223 15.0854V10.6774H71.393V9.0762H72.9223L73.1562 6.80927H74.8294V9.0762H77.3482V10.6774H74.8294V15.0854C74.8294 15.5771 74.9313 15.919 75.1352 16.1109C75.3511 16.2908 75.717 16.3808 76.2327 16.3808H77.2582V18H75.7829ZM88.0756 18.2159C87.1521 18.2159 86.3425 18.054 85.6468 17.7301C84.9511 17.4063 84.4054 16.9445 84.0096 16.3448C83.6257 15.7451 83.4278 15.0314 83.4158 14.2038H85.4309C85.4429 14.8755 85.6768 15.4452 86.1326 15.913C86.5883 16.3808 87.23 16.6147 88.0576 16.6147C88.7893 16.6147 89.359 16.4407 89.7668 16.0929C90.1866 15.7331 90.3965 15.2773 90.3965 14.7255C90.3965 14.2818 90.2946 13.9219 90.0907 13.6461C89.8988 13.3702 89.6289 13.1423 89.2811 12.9624C88.9452 12.7825 88.5554 12.6205 88.1116 12.4766C87.6678 12.3327 87.2001 12.1767 86.7083 12.0088C85.7367 11.685 85.0051 11.2652 84.5133 10.7494C84.0335 10.2337 83.7937 9.55598 83.7937 8.71637C83.7817 8.00871 83.9436 7.391 84.2794 6.86325C84.6273 6.33549 85.107 5.92769 85.7187 5.63982C86.3425 5.33996 87.0681 5.19003 87.8957 5.19003C88.7113 5.19003 89.425 5.33996 90.0367 5.63982C90.6604 5.93968 91.1462 6.35948 91.494 6.89923C91.8419 7.42698 92.0218 8.04469 92.0338 8.75236H90.0187C90.0187 8.42851 89.9348 8.12265 89.7668 7.83479C89.5989 7.53493 89.353 7.28904 89.0292 7.09714C88.7053 6.90523 88.3095 6.80927 87.8417 6.80927C87.242 6.79728 86.7443 6.94721 86.3485 7.25906C85.9646 7.57091 85.7727 8.00271 85.7727 8.55445C85.7727 9.04622 85.9167 9.42404 86.2045 9.68791C86.4924 9.95179 86.8882 10.1737 87.392 10.3536C87.8957 10.5215 88.4715 10.7194 89.1191 10.9473C89.7429 11.1512 90.3006 11.3971 90.7924 11.685C91.2841 11.9728 91.6739 12.3507 91.9618 12.8184C92.2617 13.2862 92.4116 13.8799 92.4116 14.5996C92.4116 15.2353 92.2497 15.829 91.9258 16.3808C91.602 16.9205 91.1162 17.3643 90.4685 17.7121C89.8208 18.048 89.0232 18.2159 88.0756 18.2159ZM98.1669 18C97.2913 18 96.5957 17.7901 96.0799 17.3703C95.5641 16.9385 95.3063 16.1769 95.3063 15.0854V10.6774H93.777V9.0762H95.3063L95.5402 6.80927H97.2134V9.0762H99.7322V10.6774H97.2134V15.0854C97.2134 15.5771 97.3153 15.919 97.5192 16.1109C97.7351 16.2908 98.1009 16.3808 98.6167 16.3808H99.6422V18H98.1669ZM104.721 18.2159C103.966 18.2159 103.342 18.09 102.85 17.8381C102.358 17.5862 101.992 17.2564 101.753 16.8485C101.513 16.4287 101.393 15.973 101.393 15.4812C101.393 14.6176 101.729 13.9339 102.4 13.4302C103.072 12.9264 104.031 12.6745 105.279 12.6745H107.618V12.5126C107.618 11.8169 107.426 11.2952 107.042 10.9473C106.67 10.5995 106.184 10.4256 105.585 10.4256C105.057 10.4256 104.595 10.5575 104.199 10.8214C103.816 11.0733 103.582 11.4511 103.498 11.9548H101.591C101.651 11.3072 101.867 10.7554 102.238 10.2996C102.622 9.83185 103.102 9.47801 103.678 9.23813C104.265 8.98625 104.907 8.8603 105.603 8.8603C106.85 8.8603 107.816 9.19015 108.499 9.84984C109.183 10.4975 109.525 11.3851 109.525 12.5126V18H107.87L107.708 16.4707C107.456 16.9625 107.09 17.3763 106.61 17.7121C106.131 18.048 105.501 18.2159 104.721 18.2159ZM105.099 16.6686C105.615 16.6686 106.047 16.5487 106.394 16.3088C106.754 16.0569 107.03 15.7271 107.222 15.3193C107.426 14.9115 107.552 14.4617 107.6 13.9699H105.477C104.721 13.9699 104.181 14.1018 103.858 14.3657C103.546 14.6296 103.39 14.9594 103.39 15.3552C103.39 15.7631 103.54 16.0869 103.84 16.3268C104.151 16.5547 104.571 16.6686 105.099 16.6686ZM115.315 18C114.44 18 113.744 17.7901 113.228 17.3703C112.712 16.9385 112.454 16.1769 112.454 15.0854V10.6774H110.925V9.0762H112.454L112.688 6.80927H114.362V9.0762H116.88V10.6774H114.362V15.0854C114.362 15.5771 114.463 15.919 114.667 16.1109C114.883 16.2908 115.249 16.3808 115.765 16.3808H116.79V18H115.315ZM122.139 18.2159C121.06 18.2159 120.208 17.8801 119.584 17.2084C118.973 16.5367 118.667 15.5352 118.667 14.2038V9.0762H120.574V14.0059C120.574 15.7331 121.282 16.5967 122.697 16.5967C123.405 16.5967 123.986 16.3448 124.442 15.841C124.898 15.3373 125.126 14.6176 125.126 13.682V9.0762H127.033V18H125.342L125.198 16.4347C124.922 16.9865 124.514 17.4243 123.974 17.7481C123.447 18.06 122.835 18.2159 122.139 18.2159ZM133.021 18.2159C131.894 18.2159 130.964 17.94 130.233 17.3883C129.501 16.8365 129.081 16.1049 128.973 15.1933H130.898C130.994 15.6011 131.222 15.955 131.582 16.2548C131.942 16.5427 132.416 16.6866 133.003 16.6866C133.579 16.6866 133.999 16.5667 134.263 16.3268C134.527 16.0869 134.659 15.811 134.659 15.4992C134.659 15.0434 134.473 14.7375 134.101 14.5816C133.741 14.4137 133.237 14.2638 132.589 14.1318C132.086 14.0239 131.582 13.8799 131.078 13.7C130.586 13.5201 130.173 13.2682 129.837 12.9444C129.513 12.6085 129.351 12.1588 129.351 11.595C129.351 10.8154 129.651 10.1677 130.251 9.65193C130.85 9.12418 131.69 8.8603 132.769 8.8603C133.765 8.8603 134.569 9.10019 135.18 9.57997C135.804 10.0597 136.17 10.7374 136.278 11.613H134.443C134.383 11.2292 134.203 10.9293 133.903 10.7134C133.615 10.4975 133.225 10.3896 132.733 10.3896C132.254 10.3896 131.882 10.4915 131.618 10.6954C131.354 10.8873 131.222 11.1392 131.222 11.4511C131.222 11.7629 131.402 12.0088 131.762 12.1887C132.134 12.3687 132.619 12.5306 133.219 12.6745C133.819 12.8064 134.371 12.9624 134.874 13.1423C135.39 13.3102 135.804 13.5621 136.116 13.8979C136.428 14.2338 136.584 14.7255 136.584 15.3732C136.596 16.1889 136.278 16.8665 135.63 17.4063C134.994 17.946 134.125 18.2159 133.021 18.2159Z" fill="white" />
                                                        </svg>
                                                        <div>
                                                            <div className='flex 2xl:space-x-10 xl:space-x-5 mr-2'>
                                                                <Tooltip content="Move Next Without Approve">
                                                                    <button type='button'>
                                                                        <RiPlayMiniFill className="w-5 h-5 xl:w-4 xl:h-4 mr-1 text-[#fff] cursor-pointer" onClick={moveNext} />
                                                                    </button>
                                                                </Tooltip>
                                                                <Tooltip content={documentStatus === "OnHold" ? "Remove On Hold Status" : "Put Doc Status On Hold"}>
                                                                    <div>
                                                                        <RiPauseMiniLine onClick={() => setDocOnHold(docId)} className="w-5 h-5 xl:w-4 xl:h-4 mr-1 text-[#fff] cursor-pointer" />
                                                                    </div>
                                                                </Tooltip>

                                                                {docComment ?
                                                                    <Tooltip content={truncatedComment} style={{
                                                                        width: '300px',
                                                                        marginRight: '10%'
                                                                    }}>
                                                                        <div style={{ position: 'relative', display: 'inline-block' }}>
                                                                            <LiaCommentAlt className="w-5 h-5 xl:w-4 xl:h-4 mr-1 text-[#fff] cursor-pointer"
                                                                                onClick={toggleModal} />
                                                                            <GoDotFill
                                                                                className="text-[red] cursor-pointer"
                                                                                onClick={toggleModal}
                                                                                style={{
                                                                                    position: 'absolute',
                                                                                    bottom: '0',
                                                                                    right: '0',
                                                                                    transform: 'translate(21%, 9%)',
                                                                                    width: '25px',  // 20px * 1.2 = 24px
                                                                                }}
                                                                            />
                                                                        </div>
                                                                    </Tooltip> :
                                                                    <Tooltip content="Add Comment">
                                                                        <div>
                                                                            <MdOutlineModeComment className="w-5 h-5 xl:w-4 xl:h-4 mr-1 text-[#fff] cursor-pointer" onClick={toggleModal} />
                                                                        </div>
                                                                    </Tooltip>}
                                                                {docIds && docIds.length >= 2 && (
                                                                    <Tooltip content="Next document">
                                                                        <button type='button' onClick={handleNextDoc}>
                                                                            <IoIosFastforward className="w-5 h-5 xl:w-3.5 xl:h-3.5 mr-1 text-[#fff] cursor-pointer" />
                                                                        </button>
                                                                    </Tooltip>
                                                                )}
                                                            </div>
                                                        </div>
                                                    </div>
                                                    <div className="flex justify-between space-x-6 mt-0.5 xl:mr-3 xl:space-x-5 2xl:h-[4rem] xl:ml-[0rem] 2xl:ml-[0.6rem]">
                                                        <Tooltip content="Click this button to download data">
                                                            <button
                                                                onClick={toggleDropdown}
                                                                type='button'
                                                                className="flex items-center justify-center xl:p-2  mt-4 mb-1 px-3 py-2 bg-[#ffff] text-[#003654]  rounded-xl cursor-pointer xl:text-base 2xl:text-sm"
                                                            >
                                                                <svg
                                                                    className="2xl:w-6 2xl:h-[1.5rem]"
                                                                    viewBox="0 0 28 28"
                                                                    xmlns="http://www.w3.org/2000/svg"

                                                                >
                                                                    <path d="M15.7037 14.1123C15.9403 14.1123 16.1572 14.042 16.384 13.8398L19.588 11.0889C19.7458 10.9482 19.8345 10.79 19.8345 10.5791C19.8345 10.1836 19.4796 9.89355 19.0458 9.89355C18.819 9.89355 18.6022 9.97266 18.4444 10.1309L17.1234 11.3789L16.5318 12.0117L16.6304 10.6934V4.10156C16.6304 3.6709 16.2164 3.29297 15.7037 3.29297C15.2009 3.29297 14.7869 3.6709 14.7869 4.10156V10.6934L14.8855 12.0205L14.2841 11.3789L12.9729 10.1309C12.8151 9.97266 12.5884 9.89355 12.3616 9.89355C11.9279 9.89355 11.573 10.1836 11.573 10.5791C11.573 10.79 11.6617 10.9482 11.8293 11.0889L15.0333 13.8398C15.2601 14.042 15.477 14.1123 15.7037 14.1123ZM7.26473 22.9893H24.1328C26.2722 22.9893 27.3862 22.0049 27.3862 20.124V14.9648C27.3862 14.0332 27.258 13.5322 26.7848 12.9873L23.5315 9.26074C22.3484 7.89844 21.5992 7.2041 19.795 7.2041H18.4543V8.64551H19.8838C20.5739 8.64551 21.2541 9.15527 21.7076 9.67383L25.0793 13.6289C25.4145 14.0244 25.2863 14.1914 24.7835 14.1914H18.6022C18.0895 14.1914 17.8529 14.5166 17.8529 14.8594V14.8945C17.8529 15.8438 17.0248 16.8545 15.7037 16.8545C14.3728 16.8545 13.5447 15.8438 13.5447 14.8945V14.8594C13.5447 14.5166 13.3179 14.1914 12.7954 14.1914H6.62392C6.11127 14.1914 6.00283 13.998 6.32816 13.6289L9.69981 9.67383C10.1533 9.15527 10.8336 8.64551 11.5237 8.64551H12.9532V7.2041H11.6124C9.80826 7.2041 9.059 7.88965 7.87596 9.26074L4.61276 12.9873C4.1494 13.541 4.02124 14.0332 4.02124 14.9648V20.124C4.02124 22.0049 5.13527 22.9893 7.26473 22.9893ZM7.36332 21.2314C6.45632 21.2314 5.96339 20.8096 5.96339 19.9658V15.7383H11.8687C12.1842 17.3027 13.7123 18.4629 15.7037 18.4629C17.6952 18.4629 19.2232 17.3027 19.5387 15.7383H25.444V19.9658C25.444 20.8096 24.9413 21.2314 24.0343 21.2314H7.36332Z" fill="#003654" />
                                                                </svg>
                                                                <div className="ml-1 mt-0.1 xl:text-xs font-bold 2xl:text-[0.85rem]">

                                                                    Download Data
                                                                </div>
                                                            </button>
                                                        </Tooltip>

                                                        {/* Dropdown menu for download options */}
                                                        {showDropdown && (
                                                            <div className="absolute bottom-full mb-2 rounded-md shadow-lg bg-[#003654] ring-1  font-bold ring-black ring-opacity-5">
                                                                <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                                                    <button onClick={downloadCsv} className="block px-4 py-2 xl:text-xs 2xl:text-sm text-[#fff] hover:bg-[#001e2e] w-full text-left">
                                                                        Download CSV
                                                                    </button>
                                                                    <button onClick={downloadExcel} className="block px-4 py-2 xl:text-xs 2xl:text-sm text-[#fff] hover:bg-[#001e2e] w-full text-left">
                                                                        Download Excel
                                                                    </button>
                                                                    <button onClick={downloadText} className="block px-4 py-2 xl:text-xs 2xl:text-sm text-[#fff] hover:bg-[#001e2e] w-full text-left">
                                                                        Download Text
                                                                    </button>
                                                                    <button onClick={downloadJSON} className="block px-4 py-2 xl:text-xs 2xl:text-sm text-[#fff] hover:bg-[#001e2e] w-full text-left">
                                                                        Download JSON
                                                                    </button>
                                                                </div>
                                                            </div>
                                                        )}

                                                        {allFieldsApproved || documentStatus === 'Approved' ? (
                                                            <p></p>
                                                        ) : (
                                                            <>
                                                                <Tooltip
                                                                    content="You can Approve and go to the next field">
                                                                    <button
                                                                        onClick={() => {
                                                                            if (iteratingFormData) {
                                                                                approveField(Object.keys(formData)[activeFieldIndex]);
                                                                            } else {
                                                                                approveField(Object.keys(TableData)[activeFieldIndex]);
                                                                            }
                                                                        }}
                                                                        type="button"
                                                                        className={`flex mt-4 xl:p-1 px-3 py-2 mb-1 bg-[#ffff] text-[#003654] rounded-xl cursor-pointer `}
                                                                        disabled={isSubmitting}
                                                                    >
                                                                        <svg className="w-6 h-6 md:w-4 mt-1 md:h-4 lg:w-6 lg:h-6 xl:mt-2"
                                                                            viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                                            <path d="M15.6068 22.8432V21.1908L16.9955 21.9907C18.349 22.7729 19.3773 22.2455 19.3773 20.6371V19.2485L20.5638 19.934C21.9174 20.7162 22.9545 20.1889 22.9545 18.5805V10.7055C22.9545 9.67717 22.6469 9.14104 21.7592 8.63127L14.8158 4.61467C13.8226 4.04338 12.5746 4.42131 12.5746 5.84514V7.44475L11.2474 6.67131C10.2543 6.10002 9.00623 6.48674 9.00623 7.91057V9.76506L7.47693 8.88616C6.48376 8.31487 5.23572 8.6928 5.23572 10.1166V18.2114C5.23572 19.2221 5.4906 19.7319 6.41345 20.268L13.225 24.2055C14.5785 24.9789 15.6068 24.4516 15.6068 22.8432ZM21.1176 18.2729L19.3773 17.2621V12.7621C19.3773 11.7338 19.0697 11.2065 18.182 10.6879L14.2709 8.42913V6.46038C14.2709 6.38127 14.3412 6.33733 14.4203 6.38127L20.8978 10.1254C21.1615 10.2836 21.2582 10.3979 21.2582 10.7406V18.2026C21.2582 18.2817 21.1967 18.3168 21.1176 18.2729ZM15.6068 14.9682C15.6068 13.9487 15.308 13.4125 14.4203 12.894L10.7025 10.7494V8.51702C10.7025 8.43791 10.764 8.39397 10.8431 8.43791L17.3207 12.1821C17.5844 12.3403 17.681 12.4545 17.681 12.7973V20.2592C17.681 20.3471 17.6283 20.3735 17.5492 20.3295L15.6068 19.2133V14.9682ZM13.5502 14.3881C13.8226 14.5463 13.9105 14.6606 13.9105 15.0121V22.4653C13.9105 22.5532 13.8578 22.5883 13.7787 22.5444L7.27478 18.7739C7.00232 18.6069 6.93201 18.4662 6.93201 18.1498V10.7231C6.93201 10.644 6.99353 10.6 7.08142 10.6528L13.5502 14.3881Z" fill="#003654" />
                                                                        </svg>
                                                                        <div className="ml-1 mt-0.5 xl:mt-[6%] text-base font-bold xl:text-xs 2xl:text-[0.85rem]">
                                                                            Approve & Next
                                                                        </div>
                                                                    </button>
                                                                </Tooltip>

                                                                <Tooltip
                                                                    content="Click this button to Approve everything directly from here">
                                                                    <button
                                                                        onClick={approveAllFields}
                                                                        type="button"
                                                                        disabled={isSubmitting}
                                                                        // disabled
                                                                        className="flex mt-4 xl:p-[0.1rem] 2xl:px-3 2xl:py-2 xl:px-1 xl:py-0.5 mb-1 bg-[#ffff] text-[#003654] rounded-xl cursor-pointer text-base md:text-sm lg:text-base"
                                                                    >
                                                                        <MdOutlineDoneAll className="mt-1 xl:mt-3 mr-1 2xl:w-5 2xl:h-6 md:w-4 md:h-4 lg:w-5 lg:h-6 xl:w-4 xl:h-5 " />
                                                                        <div>
                                                                            <div className="ml-1 mt-0.5 text-base font-bold  xl:text-sm xl:mt-1.5 2xl:text-[0.85rem]">
                                                                                Approve All

                                                                            </div>
                                                                        </div>
                                                                    </button>
                                                                </Tooltip>
                                                            </>
                                                        )}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>

                                        {/* Independent Table Field */}
                                        <div className={`${Object.values(TableData).some(table => table.length > 0) ? '' : 'hidden'} mx-2 col-span-3 max-h-full flex flex-col mb-4 rounded-lg`}>
                                            <div
                                                className={`w-full flex flex-col mb-4 ${showAlert ? 'max-h-[27vh] xl:max-h-[22vh]' : 'max-h-[28vh] xl:max-h-[24vh]'}  ${isTableActive ? 'border-dashed border-2 border-[#003654]' : ''} bg-[#ffff]  rounded-lg`}
                                            >
                                                <div className="flex justify-between p-3 xl:p-1">
                                                    <div className="flex">
                                                        <svg width="22" height="22" viewBox="0 0 22 22" fill="none" xmlns="http://www.w3.org/2000/svg">
                                                            <path d="M4.05381 16.0787C3.77356 16.3379 3.77356 16.7581 4.05381 17.0173C4.33406 17.2764 4.78843 17.2765 5.06868 17.0173L4.05381 16.0787ZM16.9423 5.09804L17.6599 5.09804C17.6599 4.92203 17.5843 4.75323 17.4497 4.62877C17.3152 4.50431 17.1326 4.43439 16.9423 4.43439L16.9423 5.09804ZM16.2247 13.5349C16.2247 13.9014 16.546 14.1985 16.9423 14.1985C17.3386 14.1985 17.6599 13.9014 17.6599 13.5349L16.2247 13.5349ZM7.81942 4.43439C7.42309 4.43439 7.1018 4.73152 7.1018 5.09805C7.1018 5.46457 7.42309 5.7617 7.81942 5.7617L7.81942 4.43439ZM5.06868 17.0173L17.4497 5.56732L16.4349 4.62877L4.05381 16.0787L5.06868 17.0173ZM16.2247 5.09804L16.2247 13.5349L17.6599 13.5349L17.6599 5.09804L16.2247 5.09804ZM16.9423 4.43439L7.81942 4.43439L7.81942 5.7617L16.9423 5.7617L16.9423 4.43439Z" fill="#003654" />
                                                        </svg>
                                                        <label className="text-md ml-1 font-bold text-[#003654] 2xl:text-md xl:text-sm xl:mt-0.5">Extraction Table:</label>
                                                        {(allFieldsApproved || allTablesApproved) ? <span className="ml-1 mt-1 cursor-not-allowed">{greenTick}</span> : <span className="ml-1 mt-1 cursor-not-allowed">{greyTick}</span>}
                                                    </div>
                                                    <div className="flex">
                                                        {Object.keys(TableData).filter(tableName => TableData[tableName].length > 0).map((tableName, index) => (
                                                            <button
                                                                key={tableName}
                                                                type='button'
                                                                onClick={() => {
                                                                    setIteratingFormData(false);
                                                                    setActiveFieldIndex(index);
                                                                    setIsTableActive(true);
                                                                    setActiveTable(tableName);
                                                                    setHighlightCoords({})
                                                                    // highlightFieldAtIndex(index,TableData,false);
                                                                    setCookie(`activeTable${docId}`, tableName, 1);
                                                                }}
                                                                className={`px-4 py-1 text-sm xl:text-xs text-[#fff] ${activeTable === tableName ? 'bg-[#003654]' : 'bg-[#A9A9A9] border'} rounded-lg mr-2`}
                                                                style={{ borderRadius: "13.56px" }}
                                                            >
                                                                <span className="flex items-center">
                                                                    {tableName}
                                                                    {TableData[tableName].every((row, rowIndex) =>
                                                                        Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                                                    ) ? <span className="ml-1">{greenTick}</span> : <span className="ml-1">{greyTick}</span>}
                                                                </span>
                                                            </button>
                                                        ))}
                                                    </div>
                                                </div>
                                                {Object.keys(TableData).map((tableName, index) => (
                                                    <div
                                                        key={index}
                                                        className={`w-full flex flex-col mb-1 overflow-auto ${activeTable === tableName ? '' : 'hidden'} bg-[#ffff] rounded-lg`}
                                                    >
                                                        <div className={`overflow-auto m-3 xl:m-2  rounded-md ${TableData[tableName].length > 3 ? 'max-h-[55vh]' : 'h-auto'}`} style={{ boxShadow: '0px 2.1px 8.4px 0px #0000001F' }}>
                                                            <table className="min-w-full divide-y divide-gray-200 table-auto ">
                                                                <thead className="bg-[#F1F3F9] sticky top-0 xl:p-1">
                                                                    <tr>
                                                                        <th className="px-2 py-3 text-left text-xs xl:text-[0.5rem]  font-semibold text-gray-600 uppercase">
                                                                            <Tooltip
                                                                                content={TableData[tableName].every((row, rowIndex) =>
                                                                                    Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                                                                ) ? ` Click on the table row to Unapprove all items in the table ${tableName}` : `Click on the table row to approve all items in the table  
                                                                                ${tableName}`}
                                                                            >
                                                                                <span
                                                                                    className="flex justify-center mr-3 cursor-pointer"
                                                                                    onClick={() => {
                                                                                        approveWholeTable(tableName);
                                                                                        setIteratingFormData(false);
                                                                                        setActiveFieldIndex(index);
                                                                                        setIsTableActive(true);
                                                                                        setActiveTable(tableName);
                                                                                        setHighlightCoords({})
                                                                                    }}
                                                                                >
                                                                                    {TableData[tableName].every((row, rowIndex) =>
                                                                                        Object.keys(row).every(key => approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`))
                                                                                    ) ? greenTick : greyTick}
                                                                                </span>
                                                                            </Tooltip>
                                                                        </th>
                                                                        {TableData[tableName].length > 0 && Object.keys(TableData[tableName][0]).map(header => (
                                                                            <th key={header} className="px-3 py-3 text-left text-xs xl:text-[0.7rem] font-semibold text-gray-600 border-r-4 uppercase tracking-wider whitespace-nowrap">
                                                                                <div className="flex justify-between items-center">
                                                                                    {header.replace(/([A-Z])/g, ' $1').trim()}
                                                                                    <Tooltip
                                                                                        content={TableData[tableName].every((row, rowIndex) =>
                                                                                            approvedTableFields.has(`${tableName}[${rowIndex}][${header}]`)
                                                                                        ) ? `Unapprove Column` : `Approve Column`}
                                                                                    >
                                                                                        <span
                                                                                            className="cursor-pointer"
                                                                                            onClick={() => {
                                                                                                approveColumnFields(tableName, header);
                                                                                                setIteratingFormData(false);
                                                                                                setActiveFieldIndex(index);
                                                                                                setIsTableActive(true);
                                                                                                setActiveTable(tableName);
                                                                                                setHighlightCoords({})
                                                                                            }}
                                                                                        >
                                                                                            {TableData[tableName].every((row, rowIndex) =>
                                                                                                approvedTableFields.has(`${tableName}[${rowIndex}][${header}]`)
                                                                                            ) ? greenTick : greyTick}
                                                                                        </span>
                                                                                    </Tooltip>
                                                                                </div>
                                                                            </th>
                                                                        ))}
                                                                    </tr>
                                                                </thead>
                                                                <tbody className="divide-y divide-gray-200  " style={{ maxHeight: "30vh", overflow: 'auto' }}>
                                                                    {TableData[tableName].map((row, rowIndex) => (
                                                                        <tr key={rowIndex}
                                                                            onClick={() => {
                                                                                setIteratingFormData(false);
                                                                                setActiveFieldIndex(index);
                                                                                setIsTableActive(true);
                                                                                setActiveTable(tableName);
                                                                                setHighlightCoords({})
                                                                                // highlightFieldAtIndex(index,TableData,false);
                                                                            }}
                                                                            className={`${rowIndex % 2 === 0 ? 'bg-white' : 'bg-[#F8F9FC]'}`}
                                                                        >
                                                                            <td className="flex justify-center items-center  ">
                                                                                <Tooltip
                                                                                    content={Object.keys(row).every(key =>
                                                                                        approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`)
                                                                                    ) ? `Unapprove Row` : `Approve Row`}
                                                                                >
                                                                                    <span
                                                                                        className="mr-3 mt-4 xl:mt-2 xl:mr-2 cursor-pointer"
                                                                                        onClick={() => {
                                                                                            approveRowFields(tableName, rowIndex, row)
                                                                                            setIteratingFormData(false);
                                                                                            setActiveFieldIndex(index);
                                                                                            setIsTableActive(true);
                                                                                            setActiveTable(tableName);
                                                                                            setHighlightCoords({})
                                                                                        }}
                                                                                    >
                                                                                        {Object.keys(row).every(key =>
                                                                                            approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`)
                                                                                        ) ? greenTick : greyTick}
                                                                                    </span>
                                                                                </Tooltip>
                                                                            </td>
                                                                            {Object.entries(row).map(([key, value]) => (
                                                                                <td key={`${rowIndex}-${key}`} className="text-gray-700 2xl:text-md xl:text-xs  whitespace-nowrap border border-gray-300">
                                                                                    {/* Replace this with your actual input field */}
                                                                                    <div className="flex justify-between items-center">
                                                                                        <textarea
                                                                                            name={`${tableName}[${rowIndex}].${key}`}
                                                                                            className={`text-justify w-full  min-h-[2em] max-h-[3em] min-w-[16em] ${rowIndex % 2 === 0 ? 'bg-white' : 'bg-[#F8F9FC]'} border-0 px-3 resize-none overflow-auto flex items-center`}
                                                                                            onChange={(e) => customHandleChange(e, handleChange)}
                                                                                            onBlur={handleBlur}
                                                                                            value={value}
                                                                                            cols={calculateCols(tableName)[key]}
                                                                                            rows={calculateRows(value)}
                                                                                            style={{
                                                                                                display: 'flex',
                                                                                                alignItems: 'center',
                                                                                                justifyContent: 'center',
                                                                                                textAlign: 'left',
                                                                                                padding: '0.5em 1em', // Add padding here (adjust as needed)
                                                                                            }}
                                                                                        />

                                                                                        <Tooltip
                                                                                            content={approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`)
                                                                                                ? `Unapprove Cell`
                                                                                                : `Approve Cell`}
                                                                                        >
                                                                                            <span
                                                                                                className="mr-4 cursor-pointer"
                                                                                                onClick={() => {
                                                                                                    approveTableField(tableName, rowIndex, key, value);
                                                                                                    setIteratingFormData(false);
                                                                                                    setActiveFieldIndex(index);
                                                                                                    setIsTableActive(true);
                                                                                                    setActiveTable(tableName);
                                                                                                    setHighlightCoords({})
                                                                                                }}
                                                                                                style={{
                                                                                                    display: 'flex',
                                                                                                    alignItems: 'center',
                                                                                                    justifyContent: 'center',
                                                                                                    textAlign: 'left',
                                                                                                    padding: '0.5em 0', // Add padding here (adjust as needed)
                                                                                                }}
                                                                                            >
                                                                                                {approvedTableFields.has(`${tableName}[${rowIndex}][${key}]`) ? greenTick : greyTick}
                                                                                            </span>
                                                                                        </Tooltip>
                                                                                    </div>
                                                                                </td>
                                                                            ))}
                                                                        </tr>
                                                                    ))}
                                                                </tbody>
                                                            </table>
                                                        </div>
                                                    </div>
                                                ))}
                                            </div>
                                        </div>
                                    </Form >
                                )
                                }
                            </Formik >
                        </>
                    )}
                </div >
            )
            }
        </>
    )
}

Preview.propTypes = {
    showAlert: PropTypes.bool.isRequired,
    isOpen: PropTypes.bool.isRequired
};