@echo off
REM Debugging: Print the current directory
echo Current directory: %cd%

REM Activate the virtual environment
echo Activating virtual environment...
call .\5_Env\Scripts\activate.bat

REM Debugging: Check the Python executable in the virtual environment
echo Python executable:
set PYTHON_EXECUTABLE=.\5_Env\Scripts\python.exe
echo %PYTHON_EXECUTABLE%
%PYTHON_EXECUTABLE% --version

REM Run the app initialization script and log errors
echo Running app initialization script...
%PYTHON_EXECUTABLE% ./src/utilities/initializeAppPlans.py
set ERRORLEVEL=%ERRORLEVEL%

pause
