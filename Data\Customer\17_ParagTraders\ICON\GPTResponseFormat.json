{"type": "json_schema", "json_schema": {"name": "invoice_schema", "strict": true, "schema": {"type": "object", "properties": {"InvoiceType": {"type": "string", "description": "Type of invoice being generated. For example 'Domestic'."}, "InvoiceNo": {"type": "string", "description": "The unique identifier assigned to the invoice by the seller or service provider."}, "InvoiceDate": {"type": "string", "description": "The date when the invoice was generated and issued by the seller, in the format 'YYYYMMDD'"}, "InvoiceTime": {"type": "string", "description": "The exact time when the invoice was generated, in the format 'HHMMSS'"}, "SellerDetails": {"type": "object", "description": "The Details of Seller / Vendor / Service provider / Ship To", "properties": {"SellerName": {"type": "string", "description": "The full name of the seller or service provider"}, "SellerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the seller or service provider."}, "SellerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the seller, if mentioned"}, "SellerContactNumber": {"type": "string", "description": "The contact phone number of the seller, if available."}, "SellerEmail": {"type": "string", "description": "The email address of the seller, if provided."}, "SellerAddress": {"type": "string", "description": "The full physical address of the seller, if mentioned."}, "SellerState": {"type": "string", "description": "State of seller", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "SellerGST", "SellerPAN", "SellerContactNumber", "Seller<PERSON>mail", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerState"], "additionalProperties": false}, "BuyersDetails": {"type": "object", "description": "The Details of Buyer / Purchaser / Service receiver / Bill to", "properties": {"BuyerName": {"type": "string", "description": "The full legal name of the buyer or service recipient as stated on the invoice."}, "BuyerGST": {"type": "string", "description": "The Goods and Services Tax Identification Number (GSTIN/GST) of the buyer, if mentioned."}, "BuyerPAN": {"type": "string", "description": "The Permanent Account Number (PAN) of the buyer, if mentioned"}, "BuyerContactNumber": {"type": "string", "description": "The contact phone number of the buyer, if mentioned."}, "BuyerEmail": {"type": "string", "description": "The email address of the buyer listed on the invoice, if mentioned"}, "BuyerAddress": {"type": "string", "description": "The full physical address of the buyer, if mentioned"}, "BuyerState": {"type": "string", "description": "State of buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "Andaman and Nicobar Islands", "Chandigarh", "Dadra and Nagar Haveli and Daman and Diu", "Lakshadweep", "Delhi", "Puducherry", "Ladakh", "Jammu and Kashmir", "null"]}}, "required": ["BuyerName", "BuyerGST", "BuyerPAN", "BuyerContactNumber", "BuyerEmail", "BuyerAddress", "BuyerState"], "additionalProperties": false}, "ItemTable": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "The serial number of the item in the invoice."}, "Size": {"type": "string", "description": "Dimensions of the Product."}, "PkgQty": {"type": "integer", "description": "The packed quantity of the product."}, "Category": {"type": "string", "description": "Category of the product"}, "DesignName": {"type": "string", "description": "Name of the Design."}, "Quality": {"type": "string", "description": "Quality of the Product"}, "HSNCode": {"type": "string", "description": "The HSN code or grade of the product or service"}, "Qty": {"type": "integer", "description": "The quantity of the product measured in boxes"}, "Rate/Box": {"type": "number", "description": "The rate per box for the product"}, "Amount": {"type": "number", "description": "The total amount for the item, after applying discounts and taxes"}}, "required": ["SrNo", "Size", "PkgQty", "Category", "DesignName", "Quality", "HSNCode", "Qty", "Rate/Box", "Amount"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced including duplicate rows"}, "SubTotal": {"type": "number", "description": "The subtotal of the invoice before any taxes, discounts, or additional charges. If not mentioned, this should be set to '0'"}, "InsuranceOnSale": {"type": "number", "description": "Insurance on sale, if provided"}, "CashDiscountRate": {"type": "number", "description": "Cash discount rate percentage, if provided"}, "CashDiscountAmount": {"type": "number", "description": "Total cash discount amount (without minus sign) for selected discount name and percentage, if provided"}, "IGSTRate": {"type": "number", "description": "IGST Rate mentioned in the invoice"}, "IGSTAmount": {"type": "number", "description": "IGST Amount mentioned in the invoice"}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the invoice total. Negative if applicable."}, "TotalAmount": {"type": "number", "description": "The total amount payable as per the invoice, including Discounts, Charges, Taxes, and RoundingOff adjustments"}}, "required": ["InvoiceType", "InvoiceNo", "InvoiceDate", "InvoiceTime", "SellerDetails", "BuyersDetails", "ItemTable", "SubTotal", "InsuranceOnSale", "CashDiscountRate", "CashDiscountAmount", "IGSTRate", "IGSTAmount", "RoundingOff", "TotalAmount"], "additionalProperties": false}}}