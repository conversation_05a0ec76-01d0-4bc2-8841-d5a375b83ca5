import pyclamd
import time
import datetime
import os
import logging
import shutil


# Custom Exceptions -----------
class AntivirusError(Exception):
    """Base class for antivirus-related errors."""
    pass

class AntivirusScanFailed(AntivirusError):
    """Exception raised when an antivirus scan fails."""
    def __init__(self, message="Antivirus scan failed due to an unexpected error"):
        self.message = message
        super().__init__(self.message)

class VirusDetected(AntivirusError):
    """Exception raised when a virus is detected during scanning."""
    def __init__(self, file_path, message="A virus was detected in the file being processed"):
        self.file_path = file_path
        self.message = f"{message}: {file_path}"
        super().__init__(self.message)

# -----------------------------

class ClamAVScanner:
    """Handles virus scanning using ClamAV via clamd with logging and quarantine support."""

    def __init__(self, log_dir="Log_Antivirus_Scan", quarantine_dir="quarantine"):
        """Initialize connection to ClamAV daemon and set up logging."""
        self.clamd_client = pyclamd.ClamdNetworkSocket()  # Use ClamdUnixSocket if needed
        if not self.clamd_client.ping():
            raise ConnectionError("Clamd service is not running")

        # Setup directories
        self.log_dir = os.path.abspath(log_dir)
        self.quarantine_dir = os.path.abspath(quarantine_dir)

        os.makedirs(self.log_dir, exist_ok=True)  # Ensure log directory exists
        os.makedirs(self.quarantine_dir, exist_ok=True)  # Ensure quarantine directory exists

        self.log_file = self.get_log_filename()
        self.setup_logger()

    def get_log_filename(self):
        """Generate a dynamic log filename based on the current timestamp."""
        timestamp = datetime.datetime.now().strftime("%Y_%m_%d_%H_%M_%S")
        return os.path.join(self.log_dir, f"Log_Antivirus_Scan_{timestamp}.log")

    def setup_logger(self):
        """Set up the logger to log scan results."""
        logging.basicConfig(
            filename=self.log_file,
            filemode="a",
            format="%(asctime)s - %(levelname)s - %(message)s",
            datefmt="%Y-%m-%d %H:%M:%S",
            level=logging.INFO
        )
        logging.info("========== ClamAV Scanner Initialized ==========")

    def is_file_accessible(self, file_path):
        """Check if a file is accessible for reading."""
        try:
            with open(file_path, "rb"):
                return True
        except IOError:
            return False

    def move_to_quarantine(self, file_path):
        """Move infected file to quarantine directory instead of deleting it."""
        try:
            file_name = os.path.basename(file_path)
            quarantine_path = os.path.join(self.quarantine_dir, file_name)
            shutil.move(file_path, quarantine_path)
            logging.warning(f"File quarantined: {quarantine_path}")
            return quarantine_path
        except Exception as e:
            logging.error(f"Failed to quarantine file {file_path}: {e}")
            return None
        
    def scan(self, input_data, is_binary=False, delete_infected=True):
        """
        Scans a file path or binary data using ClamAV daemon.

        :param input_data: File path (str) or binary data (bytes).
        :param is_binary: Set to True if scanning binary data.
        :param delete_infected: Set to True to delete infected files, False to quarantine them.
        :return: Dictionary containing scan report.
        """
        try:
            scan_start = time.time()
            file_name = "Binary Data Stream" if is_binary else input_data

            # If scanning a file path, ensure it exists
            if not is_binary:
                input_data = os.path.abspath(input_data)

                if not os.path.isfile(input_data):
                    # ✅ File missing = Likely quarantined by Windows Defender
                    logging.warning(f"File not found: {input_data}.")
                    return {
                        "File Name": input_data,
                        "Scanned": False,  # ✅ Treat as scanned
                        "Infected": False,  # ✅ Assume infected
                        "Virus Name": None,
                        "Error": "File not found."
                    }

                # ✅ Check if the file is locked by Windows Defender
                if not self.is_file_accessible(input_data):
                    logging.warning(f"File {input_data} is locked. Windows Defender may have detected a virus.")
                    return {
                        "File Name": input_data,
                        "Scanned": True,  # ✅ Treat as scanned
                        "Infected": True,  # ✅ Assume infected
                        "Virus Name": "Detected by Windows Defender",
                        "Error": "File locked. Windows Defender may have flagged it."
                    }

                scan_result = self.clamd_client.scan_file(input_data)
            else:
                scan_result = self.clamd_client.scan_stream(input_data)

            scan_end = time.time()

            # Format the results
            scan_details = {
                "File Name": file_name,
                "Absolute Path": input_data if not is_binary else None,
                "Scan Start Time": datetime.datetime.fromtimestamp(scan_start).strftime("%Y-%m-%d %H:%M:%S"),
                "Scan End Time": datetime.datetime.fromtimestamp(scan_end).strftime("%Y-%m-%d %H:%M:%S"),
                "Scan Duration (Seconds)": round(scan_end - scan_start, 4),
                "Infected": False,
                "Virus Name": None,
                "Scanned": True
            }

            # If scan_result is None, it means no virus was found
            if scan_result:
                for key, value in scan_result.items():
                    if value[0] == "FOUND":
                        scan_details["Infected"] = True
                        scan_details["Virus Name"] = value[1]

                        # Handle infected file
                        if not is_binary:
                            if delete_infected:
                                os.remove(input_data)
                                logging.warning(f"Infected file deleted: {input_data}")
                                scan_details["File Action"] = "Deleted"
                            else:
                                quarantined_path = self.move_to_quarantine(input_data)
                                if quarantined_path:
                                    scan_details["File Action"] = f"Quarantined to {quarantined_path}"
                                else:
                                    scan_details["File Action"] = "Failed to quarantine"

                    elif value[0] == "ERROR":
                        scan_details["Scanned"] = False
                        scan_details["Error"] = value[1]
                        logging.error(f"Scanning error for {file_name}: {value[1]}")

            # Log the scan results
            logging.info(f"Scan Completed: {scan_details}")

            return scan_details
        except Exception as e:
            error_msg = f"Error scanning data: {e}"
            logging.error(error_msg)
            return {
                "File Name": file_name,
                "Scanned": False,
                "Error": error_msg
            }

# Example usage
if __name__ == "__main__":
    scanner = ClamAVScanner(log_dir="C:/ClamAV_Logs", quarantine_dir="C:/ClamAV_Quarantine")  # Set preferred directories

    # Scan a file path (relative or absolute)
    file_path = r"D:\AV\eicar copy.zip"
    file_scan_report = scanner.scan(file_path, delete_infected=False)  # Set to True to delete infected files

    print("\nFile Scan Report:")
    for key, value in file_scan_report.items():
        print(f"{key}: {value}")
