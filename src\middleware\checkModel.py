from fastapi import Request, HTTPException, status
from typing import <PERSON><PERSON>, Dict
import os
from jose import JW<PERSON>rror, jwt
from src.utilities.DBHelper import CModelTable

# Your secret key and algorithm might differ
SECRET_KEY = os.getenv('JWT_SECRET')
ALGORITHM = "HS256"

async def ModelIdRequired(request: Request):
    try:
        # Extract and validate the authorization header
        authorization: str = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header is missing",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token_prefix, token = authorization.split()
        if token_prefix.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract role and docId from request
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        role = payload.get("role")
        user_id = payload.get("id")

        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role ID is required"
            )
            
        # Prepare role data
        role_data = {}
        role_data["role_type"] = str(role)
        # Allow to read any document if role is not general
        role_data["hasAdminRights"] = True if role.lower() in ["admin","super admin","verifier"] else False
        
        dictModelData = {}
        dictModelData["ModelName"] = str(request.query_params.get("strModelName"))
        dictModelData["ModelId"] = int(request.query_params.get("iModelId"))
        
        # Check if the docId exists in the database if role is general
        if role_data["hasAdminRights"]:
            if not await CModelTable.MSIsModelExistsBaseOnModelId(modelId=dictModelData["ModelId"]):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Model not found"
                )
        # User is not have admin rights i.e. general user rights
        elif not role_data["hasAdminRights"]:
            if not await CModelTable.MSIsModelExistsBaseOnUserId(userId=user_id,modelId= dictModelData["ModelId"]):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Model not found"
                )

        return {"UserId":user_id, "RoleData":role_data,"ModelData":dictModelData}
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token data"
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal Server Error")


async def ModelIdAndDocIdRequired(request: Request):
    try:
        # Extract and validate the authorization header
        authorization: str = request.headers.get("Authorization")
        if not authorization:
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Authorization header is missing",
                headers={"WWW-Authenticate": "Bearer"},
            )

        token_prefix, token = authorization.split()
        if token_prefix.lower() != "bearer":
            raise HTTPException(
                status_code=status.HTTP_401_UNAUTHORIZED,
                detail="Invalid authentication credentials",
                headers={"WWW-Authenticate": "Bearer"},
            )

        # Extract role and docId from request
        payload = jwt.decode(token, SECRET_KEY, algorithms=[ALGORITHM])
        role = payload.get("role")
        user_id = payload.get("id")
        docId = int(request.query_params.get("DocId"))
        if not docId:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Document ID is required"
            )

        if not role:
            raise HTTPException(
                status_code=status.HTTP_400_BAD_REQUEST,
                detail="Role ID is required"
            )
            
        # Prepare role data
        role_data = {}
        role_data["role_type"] = str(role)
        # Allow to read any document if role is not general
        role_data["hasAdminRights"] = True if role.lower() in ["admin","super admin","verifier"] else False
        
        dictModelData = {}
        dictModelData["ModelName"] = str(request.query_params.get("strModelName"))
        dictModelData["ModelId"] = int(request.query_params.get("iModelId"))
        
        # Check if the docId exists in the database if role is general
        if role_data["hasAdminRights"]:
            if not await CModelTable.MSIsModelExistsBaseOnModelId(modelId=dictModelData["ModelId"]):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Model not found"
                )
            # Check if the docId exists in the database if role is general
            if not await CDocumentTable.MSIsDocExistsBaseOnDocId(docId=docId):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )
        # User is not have admin rights i.e. general user rights
        elif not role_data["hasAdminRights"]:
            if not await CModelTable.MSIsModelExistsBaseOnUserId(userId=user_id,modelId= dictModelData["ModelId"]):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Model not found"
                )
            if not await CDocumentTable.MSIsDocExistsBaseOnUserId(userId=user_id,docId= docId):
                raise HTTPException(
                    status_code=status.HTTP_404_NOT_FOUND,
                    detail="Document not found"
                )

        return {"UserId":user_id, "DocId":docId,"RoleData":role_data,"ModelData":dictModelData}
    except JWTError as e:
        raise HTTPException(
            status_code=status.HTTP_403_FORBIDDEN,
            detail=str(e),
            headers={"WWW-Authenticate": "Bearer"}
        )
    except ValueError:
        raise HTTPException(
            status_code=status.HTTP_400_BAD_REQUEST,
            detail="Invalid token data"
        )
    except Exception as e:
        raise HTTPException(status_code=status.HTTP_500_INTERNAL_SERVER_ERROR,
            detail="Internal Server Error")