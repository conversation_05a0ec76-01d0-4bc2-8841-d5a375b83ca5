import { useState, useEffect, useCallback, useRef } from 'react';
import { jwtDecode } from "jwt-decode";
import axios from 'axios';
import { MdRemoveRedEye } from "react-icons/md";
import { IoIosArrowBack, IoIosArrowForward } from "react-icons/io";
import { useLocation, useNavigate } from 'react-router-dom';
import { RiDownloadLine, RiPauseMiniFill } from "react-icons/ri";
import { LiaCommentSolid } from "react-icons/lia";
import { TbRefreshAlert } from "react-icons/tb";
import { IoTimeOutline } from "react-icons/io5";
import toast, { Toaster } from "react-hot-toast";
import { FiSearch } from 'react-icons/fi';
import _ from 'lodash'
import { MdOutlineClear } from "react-icons/md";
import loading from '/animations/loading.svg';
import { MdErrorOutline } from "react-icons/md";
import { Tooltip } from "@material-tailwind/react";
import ModelComponent from '../components/TrialUserBox'; // Import the ModelComponent
import { ButtonGroup, Button } from "@material-tailwind/react";
import { MdDelete } from "react-icons/md";
import PropTypes from 'prop-types';
import { usePageLimitLeft, useTotalPageLimitLeft, useFreePageLimitUsage, useTotalFreePageLimit, isTrialPaidDocExtractionAtom } from '../context/TrailUsageUserData';
import { setCookie, getCookie, setCookieExpireInMin } from '../utils/cookieUtils';
import { useRecoilValue } from 'recoil';
import { TbFileUpload } from "react-icons/tb";
import JSZip from 'jszip';
import { saveAs } from 'file-saver'; // To save the generated ZIP file
import { useUserNameSetter } from "../context/userData";
import WatchVideo from "../components/ProductWalkThrough";
import filterSVG from "../assets/SVGs/filterSvg.svg"
import TobeApproved from "../assets/SVGs/Status/TobeApproved.svg"
import ProcessingSvg from "../assets/SVGs/Status/Processing.svg"
import NotProcessSvg from "../assets/SVGs/Status/NotProcess.svg"
import ApprovedSvg from "../assets/SVGs/Status/Approved.svg"
import ErrorSvg from "../assets/SVGs/Status/error.svg"
import OnHoldSvg from "../assets/SVGs/Status/OnHold.svg"
import PaidSvg from "../assets/SVGs/Paid.svg"
import FreeSvg from "../assets/SVGs/Free.svg"
import allFiles from '../assets/Strings/allFiles';
import eye from '../assets/Strings/eye';
import processing1 from '../assets/Strings/processing1.js';
import processing2 from '../assets/Strings/processing2.js';
import fetchData from '../utils/fetchData.js';

const HistoryPage = ({ showAlert, isOpen }) => {

    const location = useLocation();
    const [documents, setDocuments] = useState([]);
    const [docIDs, setDocIDs] = useState([]);
    const [processingStatus, setProcessingStatus] = useState({});
    //per page data
    const [totalData, setTotalData] = useState(10);
    //current active page
    const [activePage, setActivePage] = useState(1);
    // total number of pages
    const [totalPages, setTotalPages] = useState(0)
    const [selectedDocs, setSelectedDocs] = useState({});
    const [selectAll, setSelectAll] = useState(false); // New state to track the header checkbox
    const token = localStorage.getItem('token'); // Assuming the token is stored in localStorage
    const decodedToken = jwtDecode(token);
    const userId = decodedToken.id;
    const [activeTab, setActiveTab] = useState('today');
    const [activeFilter, setActiveFilter] = useState('allFiles');
    const navigate = useNavigate();
    const [isLoading, setIsLoading] = useState(true);
    const [searchTerm, setSearchTerm] = useState('');
    const [UserRegistered, setUserRegistered] = useState('');
    const [itemsPerPage, setItemsPerPage] = useState(10);
    const [isSearching, setIsSearching] = useState(false);
    const [isInitialFetchPageCount, setIsInitialFetchPageCount] = useState(true);
    const [hasAdminRights, setAdminRights] = useState(false);
    const [pageLimitLeft] = useState(() => {
        const cookieValue = getCookie("pageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalPageLimitLeft] = useState(() => {
        const cookieValue = getCookie("totalPageLimitLeft");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [freePageLimitUsage] = useState(() => {
        const cookieValue = getCookie("freePageLimitUsage");
        return cookieValue !== undefined ? cookieValue : undefined;
    });

    const [totalFreePageLimit] = useState(() => {
        const cookieValue = getCookie("totalFreePageLimit");
        return cookieValue !== undefined ? cookieValue : undefined;
    });


    const setUserName = useUserNameSetter()
    const isDone = useRef(true);
    const isProcessingComplete = useRef(false);
    const [startDate, setStartDate] = useState('');
    const [endDate, setEndDate] = useState('');
    const [UserIdFilter, setUserIdFilter] = useState(() => {
        const savedUserIdFilter = getCookie('UserIdFilter');
        return savedUserIdFilter || '';
    });
    const [userNameFilter, setUserNameFilter] = useState(() => {
        const savedUserNameFilter = getCookie('UserNameFilter');
        return savedUserNameFilter || '';
    });
    const [userNameOptions, setUserNameOptions] = useState([]);
    const [inputValue, setInputValue] = useState(() => {
        const savedUserSelectedInput = getCookie('UserSelectedInput');
        return savedUserSelectedInput || '';
    });
    const [showOptions, setShowOptions] = useState(false);
    const [sorting, setSorting] = useState({
        dateModifiedSortAsc: 0,
        dateAddedSortAsc: null,
        fileNameSortAsc: null,
        bStatusAsc: null,
        bUserIdAsc: null,
        bDocTypeAsc: null,
    });

    const [filters, setFilters] = useState({
        strSearchInTable: '',
        strStartDate: '',
        strEndDate: '',
    });
    const setPageLimitLeft = usePageLimitLeft();
    const setTotalPageLimitLeft = useTotalPageLimitLeft();
    const setFreePageLimitUsage = useFreePageLimitUsage();
    const setTotalFreePageLimit = useTotalFreePageLimit();

    const isTrialPaidDocExtraction = useRecoilValue(isTrialPaidDocExtractionAtom);
    const [isExtracting, setIsExtracting] = useState(false);
    const [showDropdown, setShowDropdown] = useState(false);
    const dropdownRef = useRef(null);

    const toggleDropdown = (event) => {
        event.preventDefault();
        event.stopPropagation();
        setShowDropdown(!showDropdown);
    };

    const handleClickOutside = (event) => {
        if (dropdownRef.current && !dropdownRef.current.contains(event.target)) {
            setShowDropdown(false);
        }
    };
    const [IsActionBtnAllow, setIsActionBtnAllow] = useState(false);

    useEffect(() => {
        setIsActionBtnAllow(Object.keys(selectedDocs).length > 0);
        console.log("selectedDocs", selectedDocs, IsActionBtnAllow)
    }, [selectedDocs])

    useEffect(() => {
        if (showDropdown) {
            document.addEventListener('mousedown', handleClickOutside);
        } else {
            document.removeEventListener('mousedown', handleClickOutside);
        }

        return () => {
            document.removeEventListener('mousedown', handleClickOutside);
        };
    }, [showDropdown]);

    let docIds = location.state?.docIds || [];

    useEffect(() => {
        if (docIds.length === 0) return;

        // Start document processing
        setIsLoading(true);
        handleExtractDocumentsOfUpload(docIds);
    }, [docIds]);

    const handleExtractDocumentsOfUpload = async (docIds) => {
        if (docIds.length > 0) {
            // Set all selected documents to 'processing' before starting
            setProcessingStatus(prevState => {
                return docIds.reduce((acc, docId) => {
                    acc[docId] = 'Processing';
                    return acc;
                }, { ...prevState });
            });

            await processDocumentsOfUpload();

            // After processing, update the selectedDocs to unselect only those documents
            setSelectedDocs(prevSelectedDocs => {
                const updatedSelectedDocs = { ...prevSelectedDocs };
                for (const docId of docIds) {
                    delete updatedSelectedDocs[docId]; // Remove the processed documents from selection
                }
                return updatedSelectedDocs;
            });
            setSelectAll(false); // Unset the select all when fetching new page data

            if (location.state && location.state.docIds) {
                // Create a new state object without docIds
                const newState = { ...location.state };
                delete newState.docIds;
                // Replace the state in history
                window.history.replaceState({ ...location, state: newState }, '');
            }
        } else {
            // Handle case where no data could be fetched
            toast.error('No documents selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
        isDone.current = true;
    };

    // Function to process multiple documents for extraction
    const processDocumentsOfUpload = async () => {
        try {
            setIsExtracting(true);
            const docIdsString = docIds.join('&doc_ids=');
            const response = await axios.post(`${import.meta.env.VITE_EXTERNAL_API_SERVER}/ProcessDocuments?isTrialPaidDocExtraction=${isTrialPaidDocExtraction}&doc_ids=${docIdsString}`, null, {
                headers: { Authorization: `Bearer ${localStorage.getItem('token')}` },
            });

            if (response.status === 200) {
                response.data.forEach((data) => {
                    updateExtractionModelType(data.document_id, data.IsPaidModel);

                    // Handle different API status codes
                    if (data.APIStatusCode === 429 && data.detail) {
                        setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                        updateDocumentStatus(data.document_id, 'Error', data.detail);
                        toast.error(data.detail);
                    } else if (data.APIStatusCode === 400 && data.detail) {
                        updateDocumentStatus(data.document_id, 'Error', data.detail);
                        setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                        toast.error(data.detail);
                    } else if (data.APIStatusCode === 500 && data.DocExtractionStatus === 'Error') {
                        setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                        updateDocumentStatus(data.document_id, 'Error', data.DocErrorMsg);
                    } else {
                        setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: data.DocExtractionStatus }));
                        updateDocumentStatus(data.document_id, data.DocExtractionStatus, null);
                    }
                    if (data.total_allowed_page_limit !== null && data.total_allowed_page_limit !== undefined) {
                        const totalPageLimitLeft = data.total_allowed_page_limit ?? 12;
                        setTotalPageLimitLeft(totalPageLimitLeft);
                        setCookieExpireInMin("totalPageLimitLeft", totalPageLimitLeft, 3);
                    }

                    if (data.page_limit_left !== null && data.page_limit_left !== undefined) {
                        const pageLimitLeft = data.page_limit_left ?? 0;
                        setPageLimitLeft(pageLimitLeft);
                        setCookieExpireInMin("pageLimitLeft", pageLimitLeft, 3);
                    }

                    if (data.free_page_limit_usage !== null && data.free_page_limit_usage !== undefined) {
                        const freePageLimitUsage = data.free_page_limit_usage ?? 0;
                        setFreePageLimitUsage(freePageLimitUsage);
                        setCookieExpireInMin("freePageLimitUsage", freePageLimitUsage, 3);
                    }

                    if (data.total_allowed_free_page_limit !== null && data.total_allowed_free_page_limit !== undefined) {
                        const totalFreePageLimit = data.total_allowed_free_page_limit ?? 50;
                        setTotalFreePageLimit(totalFreePageLimit);
                        setCookieExpireInMin("totalFreePageLimit", totalFreePageLimit, 3);
                    }
                });
                isProcessingComplete.current = true;
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            isProcessingComplete.current = true;
            console.error('Error processing documents:', error);
            docIds.forEach(docId => {
                setProcessingStatus(prevState => ({ ...prevState, [docId]: 'Error' }));
                updateDocumentStatus(docId, 'Error', "SomethingWent Wrong try again later");
            });
        }
        setIsExtracting(false);
    };

    const fetchUsersData = async (userFilter) => {
        try {
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/u_metadata`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`,
                },
                params: {
                    userFilter: userFilter || '',
                },
            });

            try {
                if (response.data.users) {
                    const UserNameOptions2 = Object.values(response.data.users).map(user => {
                        const userId2 = user.id;
                        return `${userId2}-${user.name}-${user.email}`;
                    });
                    setUserNameOptions(UserNameOptions2);
                    setCookie('UserNameOptions', UserNameOptions2, 1);
                }
            } catch (error) {
                console.error("Error fetching users data:", error);
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error("Error fetching users data:", error);
        }
    };

    // Initialization using useEffect
    useEffect(() => {
        const savedPage = getCookie('activePage') || 1;
        setActivePage(savedPage);

        const savedTotalPage = getCookie('totalPages') || 0;
        setTotalPages(savedTotalPage);

        const savedItemsPerPage = getCookie('itemsPerPage') || 10;
        setItemsPerPage(savedItemsPerPage);

        const savedSearchTerm = getCookie('searchTerm');
        if (savedSearchTerm  && (docIds?.length === 0)) {
            setSearchTerm(savedSearchTerm);
            setIsSearching(true);
        }
        const hasAdminRights = getCookie('hasAdminRights');
        setAdminRights(hasAdminRights);

        const UserIdFilter = getCookie('UserIdFilter');
        if (UserIdFilter && userNameFilter && userNameFilter !== '' && hasAdminRights) {
            setUserIdFilter(UserIdFilter);
        }

        if (docIds.length > 0) {
            // Reset activeTab and activeFilter
            setActiveTab('today');
            setActiveFilter('allFiles');
        } else {
            const savedTab = getCookie('activeTab') || 'today';
            setActiveTab(savedTab);
            const savedFilter = getCookie('activeFilter') || 'allFiles';
            setActiveFilter(savedFilter);
        }
        const UserSelectedInput = getCookie('UserSelectedInput');
        setAdminRights(UserSelectedInput);

    }, []);

    useEffect(() => {

        if (isInitialFetchPageCount) {
            const fetchUserData = async () => {
                try {
                    const userResponse = await fetchData(userId);
                    const pageLimitLeft = userResponse.page_limit_left;
                    const totalPageLimitLeft = userResponse.total_allowed_page_limit;
                    const freePageLimitUsage = userResponse.free_page_limit_usage;
                    const totalFreePageLimit = userResponse.total_allowed_free_page_limit;

                    setCookieExpireInMin("pageLimitLeft", pageLimitLeft, 3);
                    setCookieExpireInMin("totalPageLimitLeft", totalPageLimitLeft, 3);
                    setCookieExpireInMin("freePageLimitUsage", freePageLimitUsage, 3);
                    setCookieExpireInMin("totalFreePageLimit", totalFreePageLimit, 3);
                    const userName = userResponse.name;
                    setUserName(userName);
                    setPageLimitLeft(pageLimitLeft);
                    setTotalPageLimitLeft(totalPageLimitLeft);
                    setFreePageLimitUsage(freePageLimitUsage);
                    setTotalFreePageLimit(totalFreePageLimit);
                    setUserRegistered(userResponse.created_at);

                    if (hasAdminRights && userResponse.Users) {
                        const UserNameOptions = [...new Set(userResponse.Users.map(document => `${document.UserId}-${document.name}-${document.email}`))];
                        setUserNameOptions(UserNameOptions);
                    }
                } catch (error) {
                    if (error.response && error.response.data && error.response.data.detail) {
                        toast.error(error.response.data.detail);
                    } else {
                        toast.error("An unexpected error occurred"); // Fallback message
                    }
                    console.error('Error fetching user data:', error);
                }
            };

            fetchUserData();
            setIsInitialFetchPageCount(false);
        }
    }, [isInitialFetchPageCount]);

    const timeZone = 'America/New_York'; // Specify your timezone

    const isXLScreen = () => {
        return window.innerWidth >= 1280 && window.innerWidth < 1600;
    };

    const formatDateForActiveTab = (dateString) => {
        const date = new Date(dateString);
        if (isNaN(date)) return 'Invalid Date';

        const options = isXLScreen()
            ? { year: 'numeric', month: 'short', day: 'numeric', timeZone }
            : { year: 'numeric', month: 'long', day: 'numeric', timeZone };

        const dateStringFormatted = new Intl.DateTimeFormat('en-US', options).format(date);

        const dayOptions = isXLScreen()
            ? { weekday: 'short', timeZone }
            : { weekday: 'long', timeZone };

        const dayString = new Intl.DateTimeFormat('en-US', dayOptions).format(date);

        return `${dateStringFormatted} (${dayString})`;
    };

    const formatYmdInTimeZone = (dateString) => {
        const date = new Date(dateString);
        if (isNaN(date)) return 'Invalid Date';

        const options = { year: 'numeric', month: '2-digit', day: '2-digit', timeZone };
        const formattedDate = new Intl.DateTimeFormat('en-US', options).format(date);
        const [month, day, year] = formattedDate.split('/');
        return `${year}-${month}-${day}`;
    };

    // Fetch all documents once
    const fetchDocs = useCallback(async () => {
        let startDate;
        let endDate;
        let displayStartDate;
        let iUserId = null;
        let queryString = `?page=${activePage}&user_id=${userId}`;
        if (UserIdFilter && userNameFilter && userNameFilter !== '' && hasAdminRights) {
            iUserId = UserIdFilter; // Convert UserIdFilter to an integer
            queryString += `&iUserId=${iUserId}`;
        }

        const today = new Date();

        switch (activeTab) {
            case 'today': {
                startDate = endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'yesterday': {
                const yesterdayDate = new Date(today);
                const yesterday = new Date(today);
                displayStartDate = yesterday.setDate(yesterday.getDate() - 1);
                yesterdayDate.setUTCDate(yesterdayDate.getUTCDate() - 1);
                startDate = endDate = formatYmdInTimeZone(yesterdayDate);
                break;
            }
            case 'last7days': {
                const last7Days = new Date(today);
                const last7 = new Date(today);
                displayStartDate = last7.setDate(last7.getDate() - 6);
                last7Days.setUTCDate(last7Days.getUTCDate() - 6);
                startDate = formatYmdInTimeZone(last7Days);
                endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'last30days': {
                const last30Days = new Date(today);
                const last30 = new Date(today);
                displayStartDate = last30.setDate(last30.getDate() - 29);
                last30Days.setUTCDate(last30Days.getUTCDate() - 29);
                startDate = formatYmdInTimeZone(last30Days);
                endDate = formatYmdInTimeZone(today);
                break;
            }
            case 'All': {
                const last5Years = new Date(today);
                const last5 = new Date(today);
                displayStartDate = last5.setDate(last5.getUTCFullYear() - 5);
                last5Years.setUTCFullYear(last5Years.getUTCFullYear() - 5);
                startDate = formatYmdInTimeZone(last5Years);
                endDate = formatYmdInTimeZone(today);
                break;
            }
        }

        setStartDate(activeTab === 'today' ? formatDateForActiveTab(today) : activeTab === 'All' ? formatDateForActiveTab(UserRegistered) : formatDateForActiveTab(displayStartDate));
        if (!endDate) {
            setEndDate((activeTab === 'yesterday' ? '' : formatDateForActiveTab(today)));
        } else {
            setEndDate((activeTab === 'yesterday' ? '' : formatDateForActiveTab(today)));
        }
        // }

        try {
            if (startDate) {
                queryString += `&strStartdate=${startDate}`;
            }
            if (endDate) {
                queryString += `&strEnddate=${endDate}`;
            }
            if (itemsPerPage !== 'All') {
                queryString += `&per_page=${itemsPerPage}`;
            }
            if (searchTerm && searchTerm.trim()) {
                queryString += `&strSearchInTable=${encodeURIComponent(searchTerm.trim())}`;
            }

            // status filter based on activeFilter
            switch (activeFilter) {
                case 'Error':
                    queryString += '&strDocStatus=Error';
                    break;
                case 'onHold':
                    queryString += '&strDocStatus=OnHold';
                    break;
                case 'notProcessed':
                    queryString += '&strDocStatus=NotProcess';
                    break;
                case 'approved':
                    queryString += '&strDocStatus=Approved';
                    break;
                case 'toBeApproved':
                    queryString += '&&strDocStatus=ToBeApproved';
                    break;
                case 'Processing':
                    queryString += '&&strDocStatus=Processing';
                    break;
            }

            if (sorting.dateModifiedSortAsc !== null) {
                queryString += `&bDateAsc=${sorting.dateModifiedSortAsc ? '1' : '0'}`;
            }
            if (sorting.dateAddedSortAsc !== null) {
                queryString += `&bUploadDateAsc=${sorting.dateAddedSortAsc ? '1' : '0'}`;
            }
            if (sorting.fileNameSortAsc !== null) {
                queryString += `&bFileNameAsc=${sorting.fileNameSortAsc ? '1' : '0'}`;
            }

            if (sorting.bDocTypeAsc !== null) {
                queryString += `&bDocTypeAsc=${sorting.bDocTypeAsc ? '1' : '0'}`;
            }

            if (sorting.bStatusAsc !== null) {
                queryString += `&bStatusAsc=${sorting.bStatusAsc ? '1' : '0'}`;
            }
            if (sorting.bUserIdAsc !== null && hasAdminRights) {
                queryString += `&bUserIdAsc=${sorting.bUserIdAsc ? '1' : '0'}`;
            }
            const response = await axios.get(`${import.meta.env.VITE_SERVER}/docs/all${queryString}`, {
                headers: {
                    "Authorization": `Bearer ${localStorage.getItem('token')}`
                },
            });

            setTotalData(response.data.pagination.per_page)
            const updatedDocuments = response.data.documents.map(doc => {
                let tooltipContent = null;
                if (doc.Status === 'Error' && doc.DocErrorMsg?.trim() !== "") {
                    tooltipContent = doc.DocErrorMsg;
                } else if (doc.Status === 'NotProcess' && (!doc.DocErrorMsg || doc.DocErrorMsg?.trim() === "")) {
                    tooltipContent = "This Document is pending for processing";
                }
                else if (doc.Status === 'Processing' && (!doc.DocErrorMsg || doc.DocErrorMsg?.trim() === "")) {
                    tooltipContent = "Refresh to view latest status or it will be automatically updated once extraction is completed for all documents.";
                }

                return { ...doc, tooltipContent };
            });

            setDocuments(updatedDocuments);
            setDocIDs(response.data.documents.map(doc => doc.DocId))
            setIsLoading(false);
            setSelectedDocs({});
            setSelectAll(false);
            setTotalPages(response.data.pagination.total_pages);

            // Save filters to cookies
            setCookie('activeTab', activeTab, 1);
            setCookie('activeFilter', activeFilter, 1);
            setCookie('itemsPerPage', itemsPerPage, 1);
            setCookie('searchTerm', searchTerm, 1);
            setCookie('activePage', activePage, 1);
            setCookie('totalPages', totalPages, 1);
            setCookie('UserIdFilter', iUserId, 1);

        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            console.error('Failed to fetch documents', error);
            setIsLoading(false);
            setSelectedDocs({});
            setSelectAll(false);
        }
    }, [activePage, itemsPerPage, filters.strSearchInTable, userId, activeTab, sorting, activeFilter, totalPages, UserIdFilter]);

    const toggleSort = (field) => {
        // Set the current field to the toggled value and others to null
        setSorting({
            dateModifiedSortAsc: field === 'dateModified' ? !sorting.dateModifiedSortAsc : null,
            dateAddedSortAsc: field === 'dateAdded' ? !sorting.dateAddedSortAsc : null,
            fileNameSortAsc: field === 'fileName' ? !sorting.fileNameSortAsc : null,
            bStatusAsc: field === 'status' ? !sorting.bStatusAsc : null,
            bUserIdAsc: field === 'UserId' ? !sorting.bUserIdAsc : null,
            bDocTypeAsc: field === 'DocType' ? !sorting.bDocTypeAsc : null,
        });
    };

    const handleInputChange = (e) => {
        // Input change handler that sets the searchTerm and triggers the debounced search
        const value = e.target.value;
        setSearchTerm(value);
        setIsSearching(true);
        debouncedSearch(value);
    };

    // Debounced search handler
    const debouncedSearch = useCallback(
        _.debounce((searchValue) => {
            setFilters((prevFilters) => {
                const newFilters = { ...prevFilters };
                if (searchValue?.trim() === '') {
                    delete newFilters.strSearchInTable; // Remove the search filter
                    setIsSearching(false)
                } else {
                    newFilters.strSearchInTable = searchValue; // Set the search filter
                }
                return newFilters;
            });

            setActivePage(1); // Reset to page 1 when performing a search
        }, 300), // 300 ms debounce delay
        [] // This tells React the debounce function doesn't depend on any props or state
    );

    // Add the following useEffect to listen to changes in searchTerm
    useEffect(() => {
        if (searchTerm?.trim() === '') {
            setFilters((prevFilters) => {
                const newFilters = { ...prevFilters };
                delete newFilters.strSearchInTable; // Remove the search filter
                return newFilters;
            });
        } else {
            setFilters((prevFilters) => {
                const newFilters = { ...prevFilters };
                newFilters.strSearchInTable = searchTerm; // Set the search filter
                return newFilters;
            });
        }
    }, [searchTerm]);

    // Effect that triggers a fetch when either activePage or filters change
    useEffect(() => {
        // Only call fetchData if isDone is true
        setIsLoading(true)
        fetchDocs();
        setTimeout(() => {
            setIsLoading(false)
        }, 1000)
        // }
    }, [fetchDocs]);

    useEffect(() => {
        // This will run when the component unmounts
        return () => {
            debouncedSearch.cancel();
        };
    }, []);

    useEffect(() => {
        // Retrieve role from localStorage
        const role = localStorage.getItem('Role');

        if (role && ["admin", "super admin", "verifier"].includes(role.toLowerCase())) {
            setAdminRights(true);
        } else {
            setAdminRights(false);
        }
        setCookie("hasAdminRights", hasAdminRights)
    }, []); // Empty dependency array ensures the effect runs only once after initial render


    const formatDateEDT = (dateObj) => {
        // Extract components from the date object
        const { month, day, year, hours, minutes, am_pm } = dateObj;

        // Format the hours and minutes to ensure two digits
        const formattedHours = hours.toString().padStart(2, '0');
        const formattedMinutes = minutes.toString().padStart(2, '0');

        // Construct the formatted date string
        return (
            <span className="flex xl:text-xs 2xl:text-[1rem] flex-col ml-2">
                <span>
                    {`${formattedHours}:${formattedMinutes} ${am_pm} EDT`}
                </span>
                <span className="xl:text-xs 2xl:text-md 2xl:mr-[0.120rem]" style={{ color: '#9C9AA5' }}>
                    {`${month} ${day}, ${year}`}
                </span>
            </span>
        );
    };

    const prevPage = () => {
        setActivePage(activePage - 1);
    };

    const nextPage = () => {
        setActivePage(activePage + 1);
    };

    // Called when an individual checkbox is toggled
    const toggleCheckbox = (docId) => {
        setSelectedDocs((prevSelectedDocs) => {
            const newSelectedDocs = { ...prevSelectedDocs };

            if (newSelectedDocs[docId]) {
                delete newSelectedDocs[docId]; // Remove if it's already selected
            } else {
                newSelectedDocs[docId] = true; // Add to selected if it's not
            }

            // Check if all documents are selected, if so, set the selectAll state to true
            setSelectAll(Object.keys(newSelectedDocs).length === documents.length &&
                Object.values(newSelectedDocs).every(value => value));

            return newSelectedDocs;
        });
    };

    // Called when the header checkbox is toggled
    const toggleAllCheckboxes = (isChecked) => {
        const newSelectedDocs = {};

        if (isChecked) {
            // Add all documents to the selected list if checked
            documents.forEach((doc) => {
                newSelectedDocs[doc.DocId] = true;
            });
        }

        setSelectAll(isChecked);
        setSelectedDocs(newSelectedDocs);

    };

    const handleDeleteDocuments = async () => {

        // Capture the IDs of documents that were selected at the time the function was called
        const initialSelectedDocIds = Object.entries(selectedDocs)
            .filter(([_, isSelected]) => isSelected)
            .map(([docId, _]) => parseInt(docId, 10));

        if (initialSelectedDocIds.length > 0) {
            if (window.confirm("Are you sure you want to delete all selected documents?")) {

                try {
                    const response = await axios.post(`${import.meta.env.VITE_SERVER}/delete-documents`,
                        initialSelectedDocIds,
                        { headers: { Authorization: `Bearer ${localStorage.getItem('token')}` } }
                    );

                    if (response.status === 200) {
                        toast.success('Document Deleted Successfully');
                        setTimeout(() => {
                            window.location.reload();
                        }, 1000);  // 1000 milliseconds = 1 seconds
                    }
                    else {
                        toast.error('Failed to deleted Document!');
                    }
                } catch (error) {
                    if (error.response && error.response.data && error.response.data.detail) {
                        toast.error(error.response.data.detail);
                    } else {
                        toast.error("An unexpected error occurred"); // Fallback message
                    }
                    console.error('Error processing files:', error);
                    toast.error('Failed to deleted Document!');
                }
            }
        }
        else {
            // Handle case where no data could be fetched
            toast.error('No documents Selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
    };

    const downloadData = async (strDownloadType) => {
        try {


            const initialSelectedDocIds = Object.entries(selectedDocs)
                .filter(([_, isSelected]) => isSelected)
                .map(([docId, _]) => parseInt(docId, 10));

            if (initialSelectedDocIds.length === 0) {
                // Handle case where no data could be fetched
                toast.error('No documents Selected. Please check your selection or try again later.', {
                    position: 'top-center',
                    duration: 4000,
                });
                return;
            }

            const zip = new JSZip();

            const fetchAndAddTextOrJsonToZip = async (docID) => {
                try {
                    const response = await axios.get(`${import.meta.env.VITE_SERVER}/documents/?DocId=${docID}`,
                        {
                            headers: {
                                "Authorization": `Bearer ${localStorage.getItem('token')}`
                            },
                        });


                    if (response.status === 200) {
                        if (response?.data?.DocExtractedData) {
                            const docObj = documents.find(doc => doc.DocId === docID);
                            const formData = response.data.DocExtractedData;

                            if (strDownloadType === 'TEXT') {
                                const textData = JsonToTextFile(formData);
                                zip.file(`${docObj.DocName}-data.txt`, textData);
                            } else if (strDownloadType === 'JSON') {
                                const jsonData = JSON.stringify({
                                    formData
                                }, null, 2);
                                zip.file(`${docObj.DocName}-data.json`, jsonData);
                            }
                        } else {
                            toast.error('Download failed. No extracted data found. Please extract the data first, then try again.');
                        }
                    } else {
                        toast.error('Failed to Download the data, Please try again later');
                    }
                } catch (error) {
                    if (error.response && error.response.data && error.response.data.detail) {
                        toast.error(error.response.data.detail);
                    } else {
                        toast.error("An unexpected error occurred");
                    }
                    console.error('Error downloading the file', error);
                }
            };

            const fetchAndAddToZip = async (docID) => {
                try {
                    const docObj = documents.find(doc => doc.DocId === docID);
                    const DocName = docObj.DocName;

                    const response = await axios.get(`${import.meta.env.VITE_SERVER}/download/?lsDocIds=${docID}&strFileType=${strDownloadType}`, {
                        headers: { "Authorization": `Bearer ${localStorage.getItem('token')}` }
                    });

                    if (response.status === 200) {
                        if (response?.data[0]?.ExtractedData) {
                            const fileData = atob(response.data[0].ExtractedData);
                            const byteNumbers = new Array(fileData.length);
                            for (let i = 0; i < fileData.length; i++) {
                                byteNumbers[i] = fileData.charCodeAt(i);
                            }

                            const byteArray = new Uint8Array(byteNumbers);

                            zip.file(`${DocName}-data.${strDownloadType === "CSV" ? "csv" : "xlsx"}`, byteArray);
                        } else {
                            toast.error(`Download failed for '${DocName}'. No extracted data found. Please extract the data first, then try again.`);
                        }
                    } else {
                        toast.error('Failed to Download the data, Please try again later');
                    }
                } catch (error) {
                    if (error.response && error.response.data && error.response.data.detail) {
                        toast.error(error.response.data.detail);
                    } else {
                        toast.error("An unexpected error occurred"); // Fallback message
                    }
                    console.error('Error downloading the Excel file', error);
                }
            };

            if (strDownloadType === 'EXCEL' || strDownloadType === 'CSV') {
                await Promise.all(initialSelectedDocIds.map(docID => fetchAndAddToZip(docID)));
            } else if (strDownloadType === 'TEXT' || strDownloadType === 'JSON') {
                await Promise.all(initialSelectedDocIds.map(docID => fetchAndAddTextOrJsonToZip(docID)));
            }

            // Get all files in the ZIP
            const files = Object.keys(zip.files);

            if (files.length === 0) {
                toast.error('No files available for download');
                return;
            } else if (files.length === 1) {
                // Download the single file directly
                const fileName = files[0];
                zip.file(fileName).async('blob').then((content) => {
                    saveAs(content, fileName);
                }).catch((err) => {
                    toast.error("An error occurred while downloading the file");
                    console.error('Error downloading the file', err);
                });
            } else {
                // Generate and download the ZIP file
                zip.generateAsync({ type: 'blob' }).then((content) => {
                    saveAs(content, 'documents.zip');
                }).catch((err) => {
                    toast.error("An error occurred while creating the ZIP file, Please try again later.");
                    console.error('Error creating the ZIP file', err);
                });
            }
        }
        catch (error) {
            toast.error("Sorry, we couldn't download the data at this moment. Please try again later.");
        }
    }

    const JsonToTextFile = (data) => {
        let content = '';

        // Convert Fields to text
        if (data.Fields && Array.isArray(data.Fields)) {
            data.Fields.forEach(field => {
                const key = Object.keys(field)[0];
                const value = field[key];
                content += `${key}: ${value}\n`;
            });
        }

        // Convert Tables to text
        if (data.Tables && Array.isArray(data.Tables)) {
            data.Tables.forEach((table, tableIndex) => {
                const tableName = Object.keys(table)[0];
                const tableRows = table[tableName];
                content += `\n\n----------------------Table ${tableIndex + 1}: ${tableName}----------------------\n`;

                tableRows.forEach((row, rowIndex) => {
                    content += `\nRow ${rowIndex + 1}\n`;
                    for (const [key, value] of Object.entries(row)) {
                        content += `${key}: ${value}\n`;
                    }
                });
            });
        }

        return content;
    };

    // Used to update weather free/paid model used for extraction
    const updateExtractionModelType = (docId, bIsPaidModelUsedForExtraction) => {
        setDocuments(prevDocuments => prevDocuments.map(doc => {
            if (doc.DocId === docId) {
                return { ...doc, IsPaidModel: bIsPaidModelUsedForExtraction };
            }
            return doc;
        }));
    };

    const updateDocumentStatus = (docId, newStatus, newErrorMsg) => {
        setDocuments(prevDocuments => prevDocuments.map(doc => {
            if (doc.DocId === docId) {
                let tooltipContent = null;

                if (newStatus === 'Error' && newErrorMsg?.trim() !== "") {
                    tooltipContent = newErrorMsg;
                } else if (newStatus === 'NotProcess' && (!newErrorMsg || newErrorMsg?.trim() === "")) {
                    tooltipContent = "This Document is pending for processing";
                } else if (doc.Status === 'Processing' && (!newErrorMsg || newErrorMsg?.trim() === "")) {
                    tooltipContent = "Refresh to view latest status or it will be automatically updated once extraction is completed for all documents.";
                }

                return { ...doc, Status: newStatus, DocErrorMsg: newErrorMsg, tooltipContent };
            }
            return doc;
        }));
    };

    const handleExtractDocuments = async () => {

        setActiveFilter('allFiles');
        const initialSelectedDocIds = Object.entries(selectedDocs)
            .filter(([_, isSelected]) => isSelected)
            .map(([docId, _]) => parseInt(docId, 10));

        if (initialSelectedDocIds.length > 5) {
            toast.error("You can only extract up to 5 files at a time.");
            return;
        }

        initialSelectedDocIds.forEach((iDocId) => {
            updateExtractionModelType(iDocId, isTrialPaidDocExtraction);
        });
        

        if (initialSelectedDocIds.length > 0) {
            setProcessingStatus(prevState => {
                return initialSelectedDocIds.reduce((acc, docId) => {
                    acc[docId] = 'Processing';
                    return acc;
                }, { ...prevState });
            });

            setIsExtracting(true);
            try {
                const docIdsString = initialSelectedDocIds.join('&doc_ids=');
                // const isTrialPaidDocExtraction = useIsTrialPaidDocExtractionValue()
                const response = await axios.post(
                    `${import.meta.env.VITE_EXTERNAL_API_SERVER}/ProcessDocuments?isTrialPaidDocExtraction=${isTrialPaidDocExtraction}&doc_ids=${docIdsString}`,
                    null,
                    {
                        headers: {
                            Authorization: `Bearer ${localStorage.getItem('token')}`,
                        },
                    }
                );

                if (response.status === 200) {

                    response.data.forEach((data) => {
                        updateExtractionModelType(data.document_id, data.IsPaidModel);

                        // Handle different API status codes
                        if (data.APIStatusCode === 429 && data.detail) {
                            setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                            updateDocumentStatus(data.document_id, 'Error', data.detail);
                            toast.error(data.detail);
                        } else if (data.APIStatusCode === 400 && data.detail) {
                            updateDocumentStatus(data.document_id, 'Error', data.detail);
                            setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                            toast.error(data.detail);
                        } else if (data.APIStatusCode === 500 && data.DocExtractionStatus === 'Error') {
                            setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: 'Error' }));
                            updateDocumentStatus(data.document_id, 'Error', data.DocErrorMsg);
                        } else {
                            setProcessingStatus(prevState => ({ ...prevState, [data.document_id]: data.DocExtractionStatus }));
                            updateDocumentStatus(data.document_id, data.DocExtractionStatus, null);
                        }
                        if (data.total_allowed_page_limit !== null && data.total_allowed_page_limit !== undefined) {
                            const totalPageLimitLeft = data.total_allowed_page_limit ?? 12;
                            setTotalPageLimitLeft(totalPageLimitLeft);
                            setCookieExpireInMin("totalPageLimitLeft", totalPageLimitLeft, 3);
                        }

                        if (data.page_limit_left !== null && data.page_limit_left !== undefined) {
                            const pageLimitLeft = data.page_limit_left ?? 0;
                            setPageLimitLeft(pageLimitLeft);
                            setCookieExpireInMin("pageLimitLeft", pageLimitLeft, 3);
                        }

                        if (data.free_page_limit_usage !== null && data.free_page_limit_usage !== undefined) {
                            const freePageLimitUsage = data.free_page_limit_usage ?? 0;
                            setFreePageLimitUsage(freePageLimitUsage);
                            setCookieExpireInMin("freePageLimitUsage", freePageLimitUsage, 3);
                        }

                        if (data.total_allowed_free_page_limit !== null && data.total_allowed_free_page_limit !== undefined) {
                            const totalFreePageLimit = data.total_allowed_free_page_limit ?? 50;
                            setTotalFreePageLimit(totalFreePageLimit);
                            setCookieExpireInMin("totalFreePageLimit", totalFreePageLimit, 3);
                        }
                    });
                }
            } catch (error) {
                if (error.response && error.response.data && error.response.data.detail) {
                    toast.error(error.response.data.detail);
                } else {
                    toast.error("An unexpected error occurred"); // Fallback message
                }
                console.error('Error processing documents:', error);
                initialSelectedDocIds.forEach(docId => {
                    setProcessingStatus(prevState => ({ ...prevState, [docId]: 'Error' }));
                    updateDocumentStatus(docId, 'Error', "SomethingWent Wrong try again later");
                });
            } finally {
                setSelectAll(false);
                setSelectedDocs(prevSelectedDocs => {
                    const updatedSelectedDocs = { ...prevSelectedDocs };
                    for (const docId of docIds) {
                        delete updatedSelectedDocs[docId]; // Remove the processed documents from selection
                    }
                    return updatedSelectedDocs;
                });
            }
        } else {
            toast.error('No documents selected. Please check your selection or try again later.', {
                position: 'top-center',
                duration: 4000,
            });
        }
        setIsExtracting(false);

    };

    // Calculate the correct index based on current page
    const calculateIndex = (index) => {
        return (activePage - 1) * totalData + index + 1;
    };

    useEffect(() => {

        if (hasAdminRights) {
            if (userNameFilter) {
                fetchUsersData(userNameFilter);

            } else {
                setUserNameOptions([]);
                setCookie('UserNameOptions', [], 1);
            }
        }
    }, [userNameFilter, hasAdminRights]);

    const handleUserNameFilter = (e) => {
        let input = e.target.value.replace(/[^a-zA-Z0-9\s]/g, '');
        let truncatedInput = input.slice(0, 50);
        setUserNameFilter(truncatedInput);
        setInputValue(truncatedInput);
        // Reset to Empty String
        setUserIdFilter("");
        setCookie('UserIdFilter', "", 1);
        setCookie('UserSelectedInput', "", 1);
        setCookie('UserNameFilter', "", 1);
        setShowOptions(true);
    };

    const handleOptionClick = (option) => {
        const [userId, userName, _] = option.split('-'); // Split on the hyphen delimiter
        setUserIdFilter(userId);
        setUserNameFilter(userName); // Set username based on first part
        setInputValue(option); // Set input value to the entire option string
        setShowOptions(false); // Hide options dropdown
        setCookie('UserSelectedInput', option, 1);
        setCookie('UserNameFilter', userName, 1);
    };


    return (
        <div style={{ overflow: 'hidden' }} className={`${showAlert ? 'max-h-[95vh]' : 'max-h-[98vh]'}`}>
            <Toaster position="top-center"></Toaster>
            <div className="flex pl-[1.5rem] py-2 pt-4">
                <div className="flex-grow">
                    <h1 className="text-3xl font-semibold xl:text-2xl text-[#3F3F3F]">My Documents</h1>
                </div>
                <WatchVideo />
                {/* Place the ModelComponent to the right */}
                <ModelComponent
                    initialPageLimitLeft={pageLimitLeft}
                    initialTotalPageLimitLeft={totalPageLimitLeft}
                    initialFreePageLimitUsage={freePageLimitUsage}
                    initialTotalFreePageLimit={totalFreePageLimit}
                />
            </div>
            <div className={`pb-6 bg-[#ffff] mt-3 mx-6 ${showAlert ? 'max-h-[83vh]' : 'max-h-[92vh]'}  rounded-xl`}>
                <div className="flex lg:flex-row items-center bg-[#f4f7fe] justify-between mb-6 space-y-3 lg:space-y-0 lg:space-x-3">

                    {/* Filter Section */}
                    <div className="flex flex-wrap space-x-3 xl:mt-1">
                        <button
                            onClick={() => { setActiveTab('today'); setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'today' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'today' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                        >
                            Today
                        </button>
                        <button
                            onClick={() => { setActiveTab('yesterday'); setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'yesterday' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'yesterday' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                        >
                            Yesterday
                        </button>
                        <button
                            onClick={() => { setActiveTab('last7days'); setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'last7days' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'last7days' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                        >
                            Last 7 Days
                        </button>
                        <button
                            onClick={() => { setActiveTab('last30days'); setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'last30days' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'last30days' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                        >
                            Last 30 Days
                        </button>
                        <button
                            onClick={() => { setActiveTab('All'); setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 ${activeTab === 'All' ? 'bg-[#fff] text-[#373739] rounded-t-xl' : 'bg-[#f4f7fe] text-[#A6A6A6]'} ${activeTab === 'All' && isSearching ? 'opacity-70' : 'opacity-100'}`}
                        >
                            ALL
                        </button>
                        <button
                            onClick={() => { setActivePage(1) }}
                            className={`px-2 py-2 mt-8 text-md sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem] xl:px-2 xl:py-2.5 2xl:px-3 2xl:py-3 bg-[#fff] text-[#373739] rounded-t-xl ${isSearching ? '' : 'hidden'}`}
                        >
                            Results
                        </button>
                    </div>

                    {/* Date Range Display */}
                    <div style={{
                        marginTop: '1.99rem',
                    }} className="text-[#373739] p-3 xl:mt-7 bg-[#fff] rounded-t-xl sm:text-sm md:text-sm lg:text-md xl:text-sm 2xl:text-[0.99rem]">
                        {startDate === endDate || !endDate ? startDate : `${startDate} - ${endDate}`}
                    </div>

                    {/* Search bar container */}
                    <div className="flex flex-grow 2xl:max-w-[20rem] xl:px-3 xl:max-w-[160px] bg-[#fff] text-[#373739] items-center justify-center rounded-t-xl  lg:mt-7 xl:p-1.5 2xl:p-0.5"> {/* Adjust the max-width as needed */}
                        <div className="flex items-center justify-center py-1 mt-4">
                            <input
                                type="text"
                                className="2xl:p-2 xl:p-1 w-full bg-[#F5F5F5] xl:text-sm 2xl:text-md rounded-l-lg focus:outline-none focus:ring-0 hover:border-gray-300"
                                placeholder="Search..."
                                value={searchTerm}
                                onChange={handleInputChange}
                            />
                            {searchTerm ? (
                                <Tooltip content="Clear Results">
                                    <button
                                        className="2xl:p-[11px] xl:p-[5px] border border-gray-300 rounded-r-lg focus:outline-none hover:border-gray-300"
                                        onClick={() => {
                                            setSearchTerm('');
                                            setIsSearching(false);
                                        }}
                                    >
                                        <MdOutlineClear />
                                    </button>
                                </Tooltip>
                            ) : (
                                <Tooltip content="Search Results">
                                    <button className="2xl:p-[11px] xl:p-[5px] border border-gray-300 rounded-r-lg focus:outline-none hover:border-gray-300">
                                        <FiSearch />
                                    </button>
                                </Tooltip>
                            )}
                        </div>
                    </div>
                </div>

                <div className='ml-4 flex justify-between'>
                    <div style={{ display: 'flex', fontSize: '0.950rem' }} className='space-x-1 text-[#A6A6A6] xl:text-sm 2xl:text-[1rem]'>
                        {hasAdminRights ? (
                            <div className={`flex cursor-pointer items-center text-black z-50`}>
                                <div className="mr-1 text-sm px-2" style={{ fontSize: '0.950rem' }}>UserName</div>
                                <div style={{ position: 'relative', width: '200px' }}>
                                    <input
                                        type="text"
                                        className="w-full rounded-md border border-gray-300 px-1 py-1 text-gray-700 focus:border-blue-500 focus:outline-none text-sm"
                                        placeholder="User Name"
                                        value={inputValue}
                                        style={{ fontSize: '0.950rem' }}
                                        onChange={handleUserNameFilter}
                                        onFocus={() => setShowOptions(true)}
                                        onBlur={() => setTimeout(() => setShowOptions(false), 200)}
                                    />
                                    {showOptions && (
                                        <div className="absolute z-10 w-full bg-white border border-gray-300 mt-1 rounded-md shadow-lg max-h-40 overflow-y-auto">
                                            {userNameOptions.map(option => (
                                                <div
                                                    key={option}
                                                    className="px-3 py-2 cursor-pointer hover:bg-gray-200"
                                                    style={{ fontSize: '0.950rem' }}
                                                    onClick={() => handleOptionClick(option)}
                                                >
                                                    {option}
                                                </div>
                                            ))}
                                        </div>
                                    )}
                                </div>
                            </div>
                        ) : null}
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'allFiles' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => setActiveFilter('allFiles')} style={{ fontSize: '0.950rem' }}>
                            <svg width="23" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d={allFiles} fill={activeFilter === 'allFiles' ? '#1C1C1E' : '#A6A6A6'} />
                            </svg>
                            All Files
                        </div>
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'toBeApproved' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('toBeApproved'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                            <svg width="28" height="28" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg" className='mt-2'>
                                <path d={eye} fill={activeFilter === 'toBeApproved' ? '#1C1C1E' : '#A6A6A6'} />
                            </svg>
                            To be Approved
                        </div>
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'onHold' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('onHold'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                            <RiPauseMiniFill className='mr-1' /> On Hold
                        </div>
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'notProcessed' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('notProcessed'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                            <TbRefreshAlert className='mr-1' />Not Processed
                        </div>
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'Processing' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('Processing'); setActivePage(1); }} style={{ fontSize: '0.950rem' }}>
                            <svg width="14" height="15" viewBox="0 0 14 15" fill="none" xmlns="http://www.w3.org/2000/svg" style={{ marginRight: '5px' }}>
                                <path d={processing1} fill="white" stroke={activeFilter === 'Processing' ? '#1C1C1E' : '#A6A6A6'} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
                                <path d={processing2} fill="white" stroke={activeFilter === 'Processing' ? '#1C1C1E' : '#A6A6A6'} strokeWidth="1" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            Processing
                        </div>

                        <div className={`flex cursor-pointer items-center text-sm px-2  ${activeFilter === 'Error' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('Error'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                            <MdErrorOutline className='mr-1' /> Error
                        </div>
                        <div className={`flex cursor-pointer items-center text-sm px-2 ${activeFilter === 'approved' ? 'text-black' : 'text-[#A6A6A6]'}`} onClick={() => { setActiveFilter('approved'); setActivePage(1) }} style={{ fontSize: '0.950rem' }}>
                            <svg className='mt-3' width="27" height="25" viewBox="0 0 28 28" fill="none" xmlns="http://www.w3.org/2000/svg">
                                <path d="M14.3017 4.29053L6.43577 12.1564L2.86035 8.58103" stroke={activeFilter === 'approved' ? '#1C1C1E' : '#A6A6A6'} strokeWidth="1.60894" strokeLinecap="round" strokeLinejoin="round" />
                                <path d="M19.3017 4.29053L11.4358 12.1564L7.86035 8.58103" stroke={activeFilter === 'approved' ? '#1C1C1E' : '#A6A6A6'} strokeWidth="1.60894" strokeLinecap="round" strokeLinejoin="round" />
                            </svg>
                            Approved
                        </div>
                    </div>

                    {/* Buttons container, not growing and aligning right */}
                    <div className=" flex-shrink-0 flex items-center space-x-4 mr-4">
                        <Tooltip content={isExtracting ? "Please wait until extraction ends" : IsActionBtnAllow ? "Click to extract the document." : "Select a document to enable extraction."} placement="bottom">
                            <div className="relative">
                                <button
                                    data-tip={isExtracting ? "Please wait until extraction ends" : ""}
                                    disabled={isExtracting || !IsActionBtnAllow}
                                    className={`${isExtracting || !IsActionBtnAllow ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'} bg-[#003654] hover:bg-[#002744] font-bold text-white py-2 px-4 rounded-2xl flex items-center  text-xs lg:text-sm xl:text-2xs 2xl:text-sm`}
                                    onClick={handleExtractDocuments}
                                    style={{ fontSize: '0.950rem' }}
                                >
                                    <TbFileUpload className="h-5 w-4 mr-1 " />
                                    Extract
                                </button>
                            </div>
                        </Tooltip>

                        <div className="relative" ref={dropdownRef}>
                            <Tooltip content={IsActionBtnAllow ? "Click to download the selected document." : "Please select a document to enable the download button."} placement="bottom">
                                <button
                                    className={`font-bold bg-[#003654] hover:bg-[#002744] text-white py-2 px-3 rounded-2xl flex items-center text-xs lg:text-sm xl:text-2xs 2xl:text-sm ${IsActionBtnAllow ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
                                    onClick={toggleDropdown}
                                    disabled={!IsActionBtnAllow}
                                    style={{ fontSize: '0.950rem' }}
                                >
                                    <RiDownloadLine className="h-5 w-4 mr-1" />
                                    Download
                                </button>
                            </Tooltip>
                            {/* Dropdown menu for download options */}
                            {showDropdown && (
                                <div className="font-bold w-60 absolute top-full mt-2 right-3 rounded-2xl shadow-lg bg-[#003654] ring-1 ring-black ring-opacity-5 z-50">
                                    <div className="py-1" role="menu" aria-orientation="vertical" aria-labelledby="options-menu">
                                        <button onClick={() => downloadData("CSV")} className="block px-4 py-2 text-2xs 2xl:text-xs text-[#fff] hover:bg-[#001e2e] w-full text-left" style={{ fontSize: '0.950rem' }}>
                                            Download CSV
                                        </button>
                                        <button onClick={() => downloadData("EXCEL")} className="block px-4 py-2 text-2xs 2xl:text-xs text-[#fff] hover:bg-[#001e2e] w-full text-left" style={{ fontSize: '0.950rem' }}>
                                            Download Excel
                                        </button>
                                        <button onClick={() => downloadData("TEXT")} className="block px-4 py-2 text-2xs 2xl:text-xs text-[#fff] hover:bg-[#001e2e] w-full text-left" style={{ fontSize: '0.950rem' }}>
                                            Download Text
                                        </button>
                                        <button onClick={() => downloadData("JSON")} className="block px-4 py-2 text-2xs 2xl:text-xs text-[#fff] hover:bg-[#001e2e] w-full text-left" style={{ fontSize: '0.950rem' }}>
                                            Download JSON
                                        </button>
                                    </div>
                                </div>
                            )}
                        </div>
                        <Tooltip content={IsActionBtnAllow ? "Click to delete the selected documents." : "Please select a document to enable the delete action."} placement="bottom">
                            <button
                                className={`font-bold bg-red-600 hover:bg-red-700 text-white py-2 px-4 rounded-2xl flex items-center text-xs lg:text-sm xl:text-2xs 2xl:text-sm ${IsActionBtnAllow ? 'cursor-pointer' : 'opacity-50 cursor-not-allowed'}`}
                                onClick={handleDeleteDocuments}
                                disabled={!IsActionBtnAllow}
                                style={{ fontSize: '0.950rem' }}
                            >
                                <MdDelete className="h-5 w-4 mr-1" />
                                Delete
                            </button>
                        </Tooltip>
                    </div>
                </div>

                {isLoading ? (
                    <div className="flex justify-center">
                        <img src={loading} className='h-[20vh]' alt="No data available" />
                    </div>
                ) : (
                    documents.length === 0 ? (
                        <div className="flex justify-center">
                            <img src={`animations/noData.png`} className='h-[50vh]' alt="No data available" />
                        </div>
                    )
                        : (
                            <>
                                <div className={`mt-4 relative mx-4 overflow-y-auto ${showAlert ? '2xl:max-h-[55vh] xl:max-h-[43vh]' : '2xl:max-h-[60vh]'}`} >
                                    <table className="min-w-full leading-normal table-auto">
                                        <thead className="sticky top-0 z-10 bg-white">
                                            <tr className="text-left text-gray-700 text-xs sm:text-sm md:text-sm lg:text-md xl:text-md 2xl:text-[1rem]">
                                                <th className="px-1 w-[73px] py-3 pl-4 border-b-2 border-gray-200 font-medium">
                                                    <input
                                                        type="checkbox"
                                                        checked={selectAll}
                                                        onChange={(e) => toggleAllCheckboxes(e.target.checked)}
                                                    />
                                                </th>
                                                <th className="w-16 py-3 border-b-2 border-gray-200 font-medium">Sr No</th>
                                                {/* Conditionally render the table column based on adminRights */}
                                                {hasAdminRights ? (
                                                    <th className="pl-2 pr-1 py-3 border-b-2 border-gray-200 font-medium cursor-pointer"
                                                        onClick={() => toggleSort('UserId')}>
                                                        <div className='flex'>
                                                            UserId  <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                        </div>
                                                    </th>
                                                ) : null}
                                                <th className="-pl-[0.5rem] pr-24 xl:pr-[0rem] py-3 border-b-2 border-gray-200 font-medium cursor-pointer"
                                                    onClick={() => toggleSort('fileName')}>
                                                    <div className='flex text-start'>
                                                        File Name  <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    </div>
                                                </th>
                                                <th className="xl:pl-[3.24rem]  w-48 py-3 border-b-2 border-gray-200 font-medium cursor-pointer" onClick={() => toggleSort('status')}>
                                                    <div className='flex ml-10'>
                                                        Status <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    </div>
                                                </th>
                                                <th className="py-3 w-52 max-w-33 border-b-2 border-gray-200 font-medium cursor-pointer" onClick={() => toggleSort('DocType')}>
                                                    <div className='flex justify-center'>
                                                        Doc Type / Model <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    </div>
                                                </th>
                                                <th className="pl-0 w-[8vw] border-b-2 border-gray-200 font-medium cursor-pointer"
                                                    onClick={() => toggleSort('dateModified')}>
                                                    <div className='flex justify-center'>
                                                        Date Modified  <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    </div>
                                                </th>
                                                <th className="px-0 w-[8vw] border-b-2 border-gray-200 font-medium cursor-pointer"
                                                    onClick={() => toggleSort('dateAdded')}>
                                                    <div className='flex ml-[0.20rem] justify-center'>
                                                        Date Added <span className='-mt-1'><img className='mt-1.5 ml-1' src={filterSVG} /></span>
                                                    </div>
                                                </th>
                                                <th className="px-5 py-3 w-[2vw] border-b-2 border-gray-200 font-medium">Review</th>
                                            </tr>
                                        </thead>
                                        <tbody className="divide-y overflow-y-auto">
                                            {documents?.map((doc, index) => {
                                                return (
                                                    <tr key={doc.DocId} className={`odd:bg-gray-50 even:bg-white hover:bg-gray-200 xl:text-xs 2xl:text-sm`}>
                                                        <td className="px-1 py-1 pl-4 border-b-2 border-gray-200">
                                                            <input
                                                                type="checkbox"
                                                                checked={selectedDocs[doc.DocId] || false}
                                                                onChange={() => toggleCheckbox(doc.DocId)}
                                                            />
                                                        </td>
                                                        <td className="w-16 px-4 border-b border-gray-200">{calculateIndex(index)}</td>
                                                        {hasAdminRights ? <td className="w-8 px-4 border-b border-gray-200">{doc.UserId}</td> : null}
                                                        {/* Column 2 DocName */}
                                                        {doc.DocName?.length > 20 ? (
                                                            <td
                                                                className={`py-4 border-b border-gray-300 sm:px-6 sm:py-4 cursor-pointer truncate ${isOpen ? 'xl:max-w-[2px] 2xl:max-w-[100px]' : 'xl:max-w-[40px] 2xl:max-w-[100px]'} `}
                                                                style={{
                                                                    whiteSpace: 'nowrap',
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis'
                                                                }}
                                                            >
                                                                <span
                                                                    className="flex justify-between"
                                                                    onClick={() => navigate('/preview', { state: { docId: doc.DocId, route: 'My Documents', docIds: docIDs } })}
                                                                >
                                                                    <div className={`truncate ${isOpen && doc.Comment ? 'xl:max-w-[100px] 2xl:max-w-[31vw]' :
                                                                        !isOpen && doc.Comment ? 'xl:max-w-[230px] 2xl:max-w-[40vw]' :
                                                                            !isOpen && !doc.Comment ? 'xl:max-w-[250px] 2xl:max-w-[40vw]' :
                                                                                isOpen && !doc.Comment ? 'xl:max-w-[160px] 2xl:max-w-[40vw]' :
                                                                                    ''
                                                                        }`}>
                                                                        <Tooltip content={doc?.DocName ? doc?.DocName : ""}>
                                                                            {doc.DocName}
                                                                        </Tooltip>
                                                                    </div>
                                                                    {doc.Comment && (
                                                                        <div className='2xl:mr-10'>
                                                                            <Tooltip
                                                                                content={
                                                                                    <span
                                                                                        style={{
                                                                                            display: 'inline-block',
                                                                                            maxWidth: '300px',
                                                                                            whiteSpace: 'normal',
                                                                                            wordWrap: 'break-word'
                                                                                        }}
                                                                                    >
                                                                                        {doc.Comment}
                                                                                    </span>
                                                                                }
                                                                            >
                                                                                <span>
                                                                                    <LiaCommentSolid className="h-5 w-5 ml-2" />
                                                                                </span>
                                                                            </Tooltip>
                                                                        </div>
                                                                    )}
                                                                </span>
                                                            </td>
                                                        ) : (
                                                            <td
                                                                className={`py-4 border-b border-gray-300 sm:px-6 sm:py-4 cursor-pointer truncate ${isOpen ? 'xl:max-w-[2px] 2xl:max-w-[100px]' : 'xl:max-w-[40px] 2xl:max-w-[100px]'} `}
                                                                style={{
                                                                    whiteSpace: 'nowrap',
                                                                    overflow: 'hidden',
                                                                    textOverflow: 'ellipsis'
                                                                }}
                                                            >
                                                                <span
                                                                    className="flex justify-between"
                                                                    onClick={() => navigate('/preview', { state: { docId: doc.DocId, route: 'My Documents', docIds: docIDs } })}
                                                                >
                                                                    <div className={`truncate ${isOpen && doc.Comment ? 'xl:max-w-[100px] 2xl:max-w-[31vw]' :
                                                                        !isOpen && doc.Comment ? 'xl:max-w-[230px] 2xl:max-w-[40vw]' :
                                                                            !isOpen && !doc.Comment ? 'xl:max-w-[250px] 2xl:max-w-[40vw]' :
                                                                                isOpen && !doc.Comment ? 'xl:max-w-[160px] 2xl:max-w-[40vw]' :
                                                                                    ''
                                                                        }`}>
                                                                        {doc.DocName}
                                                                    </div>
                                                                    {doc.Comment && (
                                                                        <div className='2xl:mr-10'>
                                                                            <Tooltip
                                                                                content={
                                                                                    <span className='w-80'
                                                                                        style={{
                                                                                            display: 'inline-block',
                                                                                            maxWidth: '300px',
                                                                                            whiteSpace: 'normal',
                                                                                            wordWrap: 'break-word',
                                                                                        }}
                                                                                    >
                                                                                        {doc.Comment}
                                                                                    </span>
                                                                                }
                                                                            >
                                                                                <span>
                                                                                    <LiaCommentSolid className="h-5 w-5 ml-2" />
                                                                                </span>
                                                                            </Tooltip>
                                                                        </div>
                                                                    )}
                                                                </span>
                                                            </td>
                                                        )}


                                                        {doc?.tooltipContent ? (
                                                            <Tooltip content={<div style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                                {doc?.tooltipContent ? doc?.tooltipContent : ""}
                                                            </div>} >
                                                                <td className="xl:pl-[3.25rem] py-1 border-b border-gray-200">
                                                                    <span className={`flex items-center ${doc.Status === 'Success' ? 'text-[#4AA785]' : 'text-[#CD0000]'}`}>
                                                                        {processingStatus[doc.DocId] === 'Processing' ? (
                                                                            <IoTimeOutline className="animate-spin mx-16 h-5 w-5 text-gray-500" />
                                                                        ) : processingStatus[doc.DocId] === 'ToBeApproved' ? (
                                                                            <img src={TobeApproved} className="xl:w-[140px]" />
                                                                        ) : processingStatus[doc.DocId] === 'Error' ? (
                                                                            <img src={ErrorSvg} className="xl:w-[140px]" />
                                                                        ) :
                                                                            (
                                                                                <>
                                                                                    {doc.Status === 'Approved' ? (
                                                                                        <img src={ApprovedSvg} className="xl:w-[140px]" />
                                                                                    ) : doc.Status === 'ToBeApproved' ? (
                                                                                        <img src={TobeApproved} className="xl:w-[140px]" />
                                                                                    ) : doc.Status === 'NotProcess' ? (
                                                                                        <img src={NotProcessSvg} className="xl:w-[140px]" />
                                                                                    ) : doc.Status === 'Processing' ? (
                                                                                        <img src={ProcessingSvg} className="xl:w-[140px]" />
                                                                                    ) : doc.Status === 'Error' ? (
                                                                                        <img src={ErrorSvg} className="xl:w-[140px]" />
                                                                                    ) : doc.Status === 'OnHold' ? (
                                                                                        <img src={OnHoldSvg} className="xl:w-[140px]" />
                                                                                    ) : null}
                                                                                </>
                                                                            )
                                                                        }
                                                                    </span>
                                                                </td>
                                                            </Tooltip>
                                                        ) : (
                                                            <td className="xl:pl-[3.25rem] py-1 border-b border-gray-200">
                                                                <span className={`flex items-center ${doc.Status === 'Success' ? 'text-[#4AA785]' : 'text-[#CD0000]'}`}>
                                                                    {processingStatus[doc.DocId] === 'Processing' ? (
                                                                        <IoTimeOutline className="animate-spin mx-16 h-5 w-5 text-gray-500" />
                                                                    ) : processingStatus[doc.DocId] === 'ToBeApproved' ? (
                                                                        <img src={TobeApproved} className="xl:w-[140px]" />
                                                                    ) : processingStatus[doc.DocId] === 'Error' ? (
                                                                        <img src={ErrorSvg} className="xl:w-[140px]" />
                                                                    ) :
                                                                        (
                                                                            <>
                                                                                {doc.Status === 'Approved' ? (
                                                                                    <img src={ApprovedSvg} className="xl:w-[140px]" />
                                                                                ) : doc.Status === 'ToBeApproved' ? (
                                                                                    <img src={TobeApproved} className="xl:w-[140px]" />
                                                                                ) : doc.Status === 'NotProcess' ? (
                                                                                    <img src={NotProcessSvg} className="xl:w-[140px]" />
                                                                                ) : doc.Status === 'Processing' ? (
                                                                                    <img src={ProcessingSvg} className="xl:w-[140px]" />
                                                                                ) : doc.Status === 'Error' ? (
                                                                                    <img src={ErrorSvg} className="xl:w-[140px]" />
                                                                                ) : doc.Status === 'OnHold' ? (
                                                                                    <img src={OnHoldSvg} className="xl:w-[140px]" />
                                                                                ) : null}
                                                                            </>
                                                                        )
                                                                    }
                                                                </span>
                                                            </td>
                                                        )}

                                                        {doc?.ModelName.length > 19 ? (

                                                            <td className="px-6 text-center py-1 border-b border-gray-200 max-w-[10rem] truncate">
                                                                <div className='flex items-center'>
                                                                    <div>
                                                                        {doc.IsPaidModel ? (
                                                                            <Tooltip content="Extracted Using Pro Mode">
                                                                                <div>
                                                                                    <img src={PaidSvg} />
                                                                                </div>
                                                                            </Tooltip>
                                                                        ) : (
                                                                            <Tooltip content="Extracted Using Lite Mode">
                                                                                <div>
                                                                                    <img src={FreeSvg} />
                                                                                </div>
                                                                            </Tooltip>

                                                                        )}
                                                                    </div>
                                                                    <Tooltip content=
                                                                        {doc?.ModelName ? doc?.ModelName : ""}>
                                                                        <span className='truncate' >
                                                                            {doc.ModelName}
                                                                        </span>
                                                                    </Tooltip>
                                                                </div>
                                                            </td>
                                                        ) : (
                                                            <td className="px-6 py-1 text-center border-b border-gray-200 max-w-[10rem] truncate">
                                                                <div className='flex items-center justify-center'>
                                                                    <div>
                                                                        {doc.IsPaidModel ? (
                                                                            <Tooltip content="Extracted Using Pro Mode">
                                                                                <div>
                                                                                    <img src={PaidSvg} />
                                                                                </div>
                                                                            </Tooltip>
                                                                        ) : (
                                                                            <Tooltip content="Extracted Using Lite Mode">
                                                                                <div>
                                                                                    <img src={FreeSvg} />
                                                                                </div>
                                                                            </Tooltip>
                                                                        )}
                                                                    </div>

                                                                    {doc.ModelDescription ? (
                                                                        <Tooltip content={
                                                                            <div style={{ maxWidth: '700px', whiteSpace: 'pre-wrap' }}>
                                                                                {doc.ModelDescription}
                                                                            </div>
                                                                        }>
                                                                            <span className='truncate'>{doc.ModelName}</span>
                                                                        </Tooltip>
                                                                    ) : (
                                                                        <span className='truncate'>{doc.ModelName}</span>
                                                                    )}

                                                                </div>
                                                            </td>
                                                        )
                                                        }
                                                        <td className="justify-center pl-0 py-1 border-b border-gray-200 "><div className='text-center'>{formatDateEDT(doc.ModifiedDateTime)}</div></td>
                                                        <td className="px-0 py-1 border-b border-gray-200 "> <div className='text-center'>{formatDateEDT(doc.UploadedDateTime)}</div></td>

                                                        <td className="px-5 w-[5vw] py-1 border-b border-gray-200">
                                                            <MdRemoveRedEye
                                                                className={`mx-4 h-6 w-6 text-primary ${!isDone.current && docIds.includes(doc.DocId) ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
                                                                onClick={() => {
                                                                    if (isDone.current || !docIds.includes(doc.DocId)) {
                                                                        navigate('/preview', { state: { docId: doc.DocId, route: 'My Documents', docIds: docIDs } });
                                                                    }
                                                                }}
                                                            />
                                                        </td>
                                                    </tr>
                                                );
                                            })}
                                        </tbody>
                                    </table>
                                </div>
                                {/* </div> */}

                                <div className="flex justify-between items-center pt-4 sticky bottom-5 bg-white z-10 mt-5">
                                    {/* Rows per Page Selector */}
                                    <div className='mx-4'>
                                        <ButtonGroup variant='outlined' size="sm">
                                            <Button onClick={() => { setItemsPerPage(10); setActivePage(1) }} className={`${itemsPerPage == 10 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                                10
                                            </Button>
                                            <Button onClick={() => { setItemsPerPage(20); setActivePage(1) }} className={`${itemsPerPage == 20 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                                20
                                            </Button>
                                            <Button onClick={() => { setItemsPerPage(50); setActivePage(1) }} className={`${itemsPerPage == 50 ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                                50
                                            </Button>
                                            <Button onClick={() => { setItemsPerPage('All'); setActivePage(1) }} className={`${itemsPerPage === 'All' ? 'bg-[#003654] text-white' : 'bg-[#fff] text-black'}`}>
                                                All
                                            </Button>
                                        </ButtonGroup>
                                    </div>
                                    {/* Pagination Controls */}
                                    <div className="flex items-center">
                                        <Button
                                            variant="text"
                                            onClick={prevPage}
                                            disabled={activePage == 1}
                                            className="text-xs xl:text-xs"
                                        >
                                            <IoIosArrowBack strokeWidth={2} className="h-4 w-4" />
                                        </Button>

                                        {totalPages <= 3 ? (
                                            Array.from({ length: totalPages }, (_, index) => (
                                                <button
                                                    key={index}
                                                    className={`px-3 py-1 ${activePage == index + 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-xs`}
                                                    onClick={() => setActivePage(index + 1)}
                                                >
                                                    {index + 1}
                                                </button>
                                            ))
                                        ) : (
                                            <>
                                                <button
                                                    className={`px-3 py-1 ${activePage == 1 ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-xs`}
                                                    onClick={() => setActivePage(1)}
                                                >
                                                    1
                                                </button>
                                                {activePage > 3 && <span className="px-2 text-xs xl:text-sm">...</span>}
                                                {Array.from({ length: 3 }, (_, idx) => activePage - 1 + idx)
                                                    .filter(page => page > 1 && page < totalPages)
                                                    .map(page => (
                                                        <button
                                                            key={page}
                                                            className={`px-3 py-1 ${activePage == page ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-sm`}
                                                            onClick={() => setActivePage(page)}
                                                        >
                                                            {page}
                                                        </button>
                                                    ))
                                                }
                                                {activePage < totalPages - 2 && <span className="px-2 text-xs xl:text-sm">...</span>}
                                                <button
                                                    className={`px-3 py-1 ${activePage == totalPages ? 'bg-[#BCBCBC] text-white' : 'bg-[#ffff]'} rounded-md text-xs xl:text-sm`}
                                                    onClick={() => setActivePage(totalPages)}
                                                >
                                                    {totalPages}
                                                </button>
                                            </>
                                        )}

                                        <Button
                                            variant="text"
                                            onClick={nextPage}
                                            disabled={activePage == totalPages || totalPages == 0}
                                        >
                                            <IoIosArrowForward strokeWidth={2} className="h-4 w-4" />
                                        </Button>
                                    </div>
                                </div>
                            </>
                        )
                )
                }
            </div>
        </div >
    );
};

export default HistoryPage;

HistoryPage.propTypes = {
    showAlert: PropTypes.bool.isRequired,
    isOpen: PropTypes.bool.isRequired,
};