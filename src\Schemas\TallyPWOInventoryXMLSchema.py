# ============================================================================
# Purchase Voucher without Inventory Template Class
# ============================================================================
# TODO: Need to Test Voucher Purchase without inventory


# class InventoryEntrySchema(BaseModel):
#     stock_item_name: str = Field(..., description="Name of the stock item")
#     rate: float = Field(..., description="Rate of the stock item")
#     amount: float = Field(..., description="Amount for the stock item")
#     quantity: Optional[float] = Field(None, description="Quantity of the stock item")


# class TallyPWOInventoryInputSchema(BaseModel):
#     company_info: CompanyInfoSchema
#     voucher_date: str = Field(..., description="Voucher date in format YYYYMMDD")
#     narration: str = Field(..., description="Narration for the voucher")
#     voucher_number: str = Field(..., description="Voucher number")
#     ledger_entries: List[LedgerEntrySchema] = Field(..., description="List of ledger entries for the voucher")
#     inventory_entries: List[InventoryEntrySchema] = Field(..., description="List of inventory entries for the voucher")


# class CTallyPWOInventoryTemplate(CTallyTemplate):
#     """
#     Template for a Purchase Voucher with Inventory XML.
#     Uses a Pydantic schema (TallyPWOInventoryInputSchema) for inputs.
#     """

#     def __init__(self, input_data: TallyPWOInventoryInputSchema):
#         super().__init__(company_name=input_data.company_info.company_name)
#         self.voucher_date = input_data.voucher_date
#         self.narration = input_data.narration
#         self.voucher_number = input_data.voucher_number
#         self.ledger_entries = [entry.dict() for entry in input_data.ledger_entries]
#         self.inventory_entries = [entry.dict() for entry in input_data.inventory_entries]

#     def build_voucher(self) -> Element:
#         voucher = SubElement(self.tally_message, "VOUCHER")
#         SubElement(voucher, "DATE").text = self.voucher_date
#         SubElement(voucher, "NARRATION").text = self.narration
#         SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number

#         # Add ledger entries
#         for ledger in self.ledger_entries:
#             self._add_ledger_entry(voucher, ledger)

#         # Add inventory entries
#         for inv in self.inventory_entries:
#             self._add_inventory_entry(voucher, inv)

#         return voucher

#     def _add_ledger_entry(self, voucher: Element, ledger: Dict[str, Any]) -> None:
#         ledger_el = SubElement(voucher, "LEDGERENTRIES.LIST")
#         SubElement(ledger_el, "LEDGERNAME").text = ledger.get("ledger_name", "")
#         SubElement(ledger_el, "AMOUNT").text = str(ledger.get("amount", 0))
#         # Extend further as needed (GST, cost center, etc.)

#     def _add_inventory_entry(self, voucher: Element, inv: Dict[str, Any]) -> None:
#         inv_el = SubElement(voucher, "ALLINVENTORYENTRIES.LIST")
#         SubElement(inv_el, "STOCKITEMNAME").text = inv.get("stock_item_name", "")
#         SubElement(inv_el, "RATE").text = str(inv.get("rate", ""))
#         SubElement(inv_el, "AMOUNT").text = str(inv.get("amount", ""))
#         if inv.get("quantity") is not None:
#             SubElement(inv_el, "ACTUALQTY").text = str(inv.get("quantity"))

#     def to_xml(self, pretty: bool = True) -> str:
#         self.build_envelope()
#         self.build_voucher()
#         return self.to_string(pretty=pretty)


# if __name__ == "__main__":
    
    # Example for Purchase Voucher with Inventory Template

    # pwoinv_input = TallyPWOInventoryInputSchema(
    #     company_info=CompanyInfoSchema(
    #         company_name="FAIRDEAL INTERNATIONAL - 2024-25",
    #         gst_in="24AAACG5535F1ZY",
    #         state_name="Gujarat"
    #     ),
    #     voucher_date="20250131",
    #     narration="Purchase voucher with inventory details",
    #     voucher_number="PWOINV/001",
    #     ledger_entries=[
    #         LedgerEntrySchema(
    #             ledger_name="Purchase Ledger",
    #             amount=-5000.0,
    #             is_deemed_positive=True
    #         )
    #     ],
    #     inventory_entries=[
    #         InventoryEntrySchema(
    #             stock_item_name="Item A",
    #             rate=100.0,
    #             amount=1000.0,
    #             quantity=10
    #         ),
    #         InventoryEntrySchema(
    #             stock_item_name="Item B",
    #             rate=200.0,
    #             amount=4000.0,
    #             quantity=20
    #         )
    #     ]
    # )

    # pwoinv_template = CTallyPWOInventoryTemplate(pwoinv_input)
    # xml_output_pwoinv = pwoinv_template.to_xml(pretty=True)
    # print("----- Purchase Voucher (with Inventory) XML -----")
    # print(xml_output_pwoinv)
