import sys
sys.path.append('.')
from dotenv import load_dotenv
import subprocess
from sqlalchemy.future import select
from src.utilities.S3BucketHelper import CAWSS3Storage
from config.db_config import AsyncSessionLocal
import os
from datetime import date
import asyncio
import traceback
from pathlib import Path
load_dotenv()


async def create_sql_file(lsTable: list, strFilePath: str):
    async with AsyncSessionLocal() as db:
        async with db.begin():
            with open(strFilePath, 'w', encoding='utf-8') as f:
                for objTable in lsTable:
                    # Introspect the table to get column names dynamically
                    table = objTable.__table__
                    columns = [column.name for column in table.columns]
                    column_names = ', '.join(columns)

                    result = await db.execute(select(objTable))
                    TableData = result.scalars().all()

                    for row in TableData:
                        # Dynamically construct column values string
                        values = ', '.join([
                            f"'{getattr(row, col)}'" if isinstance(getattr(row, col), str) else
                            f"'{getattr(row, col)}'" if getattr(row, col) is None else
                            str(getattr(row, col))
                            for col in columns
                        ])
                        # Handling special characters and SQL injection risks requires more sophisticated handling

                        insert_stmt = f"INSERT INTO {table.name} ({column_names}) VALUES ({values});\n"
                        f.write(insert_stmt)

# To call the coroutine, use the following:


async def main():
    db_user = os.getenv('DATABASE_USER')
    db_pass = os.getenv('DATABASE_PASSWORD_DEV')
    db_name = os.getenv('DATABASE_NAME')
    host = os.getenv('DATABASE_URL_DEV')
    port = os.getenv('DATABASE_PORT')
    
    today = date.today()
    formatted_date = today.strftime("%d-%m-%Y")
    strDBBackUpDir = Path(r"Data/DBBackUp")
    strFileName = f"{db_name}-{formatted_date}-db-backup.sql"
    strFilePath = strDBBackUpDir + os.sep + strFileName
    strBucketName = "accuvelocity-db-backup"
    # Check if the directory exists, if not, create it
    if not os.path.exists(strDBBackUpDir):
        os.makedirs(strDBBackUpDir)
        
    # Construct the mysqldump command
    command = [
        'mysqldump',
        '-h', host,
        '-P', str(port),
        '-u', db_user,
        f'--password={db_pass}',
        db_name
    ]
    
    # Running the backup command
    with open(strFilePath, 'w') as outfile:
        subprocess.run(command, stdout=outfile, shell=False, check=True)
    
    if os.path.exists(strFilePath):
        CAWSS3Storage.MSSetS3Object(
            object_name=strFileName, file_data=strFilePath, fileType="filePath", bucket_name=strBucketName)
    else:
        error_message = f"File path {strFilePath} does not exist."
        raise ValueError(error_message)

if __name__ == "__main__":
    # set cron job in ubuntu to schedule backup at every day at 3 AM -> 0 3 * * * /home/<USER>/Desktop/AccuVelocity/5_Env/bin/python /home/<USER>/Desktop/AccuVelocity/src/utilities/UploadDBToBucket.py

    try:
        asyncio.run(main())
    except Exception as e:
        print(f"An error occurred: {e}")
        traceback.print_exc()
