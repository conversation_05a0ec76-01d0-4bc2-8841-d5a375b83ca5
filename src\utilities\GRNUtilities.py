from fastapi import HTTPException


class CGRNUtilities:
    @staticmethod
    def MSGetCostCenter(dictItemDetails):
        strGRNNo = str(dictItemDetails.get("GRNNO", "")).strip()
        
        if strGRNNo.lower().replace(" ", "").startswith("sf-"):
            return "CFPI"
        
        lsGRNNo = strGRNNo.split("-")
        for part in lsGRNNo:
            if len(part) >= 4:
                return part

        raise HTTPException(status_code=404, detail=f"Unable to find the cost center for GRN Number {strGRNNo}")