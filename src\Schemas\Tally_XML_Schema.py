from pydantic import BaseModel, Field, conlist
from typing import List, Optional, Any, Dict, Union
from xml.etree.ElementTree import Element, SubElement, tostring
import xml.dom.minidom

import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
from datetime import datetime

# -----------------------------------------------------
# 1. Define your Pydantic Models
# -----------------------------------------------------

class CompanyInfoSchema(BaseModel):
    """
    Holds details about the Tally Company configuration
    (like the Tally Company name, GST registration type, GSTIN, etc.).
    """
    company_name: str = Field(..., description="Name of the Tally Company")
    gst_registration_type: str = Field("Regular", description="GST Registration Type, e.g. 'Regular'")
    cmp_gst_registration_type: Optional[str] = None
    gst_in: str = Field("24AAACG5535F1ZY", description="GSTIN of the company")
    state_name: Optional[str] = Field("Gujarat", description="State Name of the company")
    country_name: Optional[str] = Field("India", description="Country Name of the company")

class AdditionalLedgerInfo(BaseModel):
    """
    Holds details about tally voucher configuration, like diff actual quantity,
    """
    bDiffActualQTY: bool = Field(True, description="Weather to view difference between Quantity.")

    
class PartyDetailsSchema(BaseModel):
    """
    Holds details about the party/supplier: name, address, GSTIN, etc.
    """
    party_name: str
    address_list: Optional[conlist(str, max_length=4)] = []
    gst_in: Optional[str] = ""
    state_name: Optional[str] = ""
    country_name: Optional[str] = ""
    pin_code: Optional[str] = None
    gst_registration_type: Optional[str] = ""


class ConsigneeDetailsSchema(BaseModel):
    """
    Holds details about the consignee/ship-to address (which might be the company's address).
    """
    address_list: Optional[conlist(str, max_length=6)] = []
    # address_list: conlist(str, max_length=4)
    gst_in: str
    mailing_name: str
    state_name: str
    pin_code: Optional[str] = None
    country_name: str = "India"
    cmp_registration: Optional[str] = None


class BillAllocationSchema(BaseModel):
    """
    Represents the bill allocation details for a ledger entry.
    Expected keys:
      - name: Reference number (e.g. Supplier Invoice No)
      - billtype: Type of bill allocation (e.g. "New Ref" or "Agnt Ref")
      - amount: The total amount for the bill allocation
    """
    name: str
    billtype: str
    amount: float
    billcreditperiod: Optional[str] = None

class InvoiceDelNotesSchema(BaseModel):
    """
        Schema for capturing delivery note details from invoice data.

        Attributes:
            basic_shipping_date (str): The date the shipment was dispatched.
            basic_ship_delivery_note (str): The delivery note number associated with the shipment.

    """
    basic_shipping_date: str
    basic_ship_delivery_note: str

class InvoiceOrderListSchema(BaseModel):
    """
        Schema for capturing Purchase order details from invoice data.

        Attributes:
            basic_order_date (str): The date the shipment was dispatched.
            basic_purchase_order_no (str): The delivery note number associated with the shipment.
    """
    basic_order_date: str
    basic_purchase_order_no: str

class CostCenterAllocationSchema(BaseModel):
    name: str
    amount: float



class CategoryAllocationSchema(BaseModel):
    category: str
    is_deemed_positive: bool = True
    cost_center_allocations: List[CostCenterAllocationSchema] = Field(default_factory=list)


class LedgerEntrySchemaForJournalVoucher(BaseModel):
    ledger_name: str
    amount: float
    is_deemed_positive: bool
    is_party_ledger: bool = False
    # Optional GST override tags (e.g. for Expense Ledgers)
    gst_taxability: Optional[str] = None           # e.g. "Taxable"
    gst_type_of_supply: Optional[str] = None         # e.g. "Services"
    # Optional Cost Center Allocation details:
    # Provide a category (e.g. "2024-2025") and a list of allocations.
    cost_center_category: Optional[str] = None
    cost_center_allocations: Optional[List[Dict[str, Any]]] = None
    # Optional Bill Allocation details:
    # Provide a dict with keys: name, billtype, amount.
    bill_allocation: Optional[Dict[str, Any]] = None


# -----------------------------------------------------
# Models for Inventory Entry Details
# -----------------------------------------------------
class BatchAllocationSchema(BaseModel):
    godownname: str
    batchname: str
    tracking_no: Optional[str] = None
    destinationgodownname: Optional[str] = None
    trackingnumber: Optional[str] = None
    dynamiccsticleared: Optional[str] = "No"
    amount: float
    actual_qty: str  # e.g. "214 Box"
    billed_qty: str  # e.g. "214 Box"
    order_no:Optional[str] = None
    orderduedate: Optional[str] = None

class AccountingAllocationSchema(BaseModel):
    ledgername: str
    gstclass: str = "Not Applicable"
    is_deemed_positive: bool = True
    amount: float
    category_allocations: Optional[List[CategoryAllocationSchema]] = None

class RateDetailSchema(BaseModel):
    gstrate_duty_head: str  # e.g. "CGST", "SGST/UTGST", "IGST"
    gstrate_valuation_type: str = "Based on Value"
    gstrate: float  # e.g. 9.0 or 18.0

class InventoryEntrySchema(BaseModel):
    stockitemname: str
    gst_ovrd_ineligible_itc: Optional[str] = "Not Applicable"
    gst_ovrd_is_revcharge_appl: Optional[str] = "Not Applicable"
    gst_ovrd_taxability: Optional[str] = "Taxable"
    gstsourcetype: Optional[str] = "Ledger"
    gstledgersource: Optional[str] = None  # e.g. "GST INTERSTATE PURCHASE (18%)"
    hsnsourcetype: Optional[str] = "Stock Item"
    hsnitemsource: Optional[str] = None  # typically same as stockitemname
    gst_ovrd_stored_nature: Optional[str] = "Interstate Purchase - Taxable" # None Default prefarrable
    gst_ovrd_type_of_supply: Optional[str] = "Goods"
    gstrate_infer_applicability: Optional[str] = "As per Masters/Company"
    gst_hsnname: Optional[str]=None  # e.g. "********"
    gst_hsn_infer_applicability: Optional[str] = "As per Masters/Company"
    is_deemed_positive: bool = True
    rate: str  # e.g. "4278.05/Box"
    discount: Optional[float]=0.0  # e.g. 20.0
    amount: float
    actual_qty: str  # e.g. "214 Box"
    billed_qty: str  # e.g. "214 Box"
    description:Optional[str] = ""
    batch_allocations: List[BatchAllocationSchema] = Field(default_factory=list)
    accounting_allocations: List[AccountingAllocationSchema] = Field(default_factory=list)
    rate_details: List[RateDetailSchema] = Field(default_factory=list)

class LedgerEntrySchema(BaseModel):
    ledger_name: str
    amount: float
    is_deemed_positive: bool
    cost_center: Optional[str] = None       # e.g. "Sweet Factory"
    cost_category: Optional[str] = "Primary Cost Category"
    gst_class: str = "Not Applicable"
    is_party_ledger: Optional[bool] = False
    gst_overridden: Optional[bool] = False
    remove_zero_entries: Optional[bool] = False  # If True, outputs "Yes", else "No"
    method_type: Optional[str] = None            # e.g. "GST" or "As Total Amount Rounding"
    round_type: Optional[str] = None             # e.g. "Normal Rounding"
    round_limit: Optional[int] = None            # e.g. 1
    vat_exp_amount: Optional[float] = None       # VAT expense amount if different from amount
    bill_allocation: Optional[BillAllocationSchema] = None
    category_allocations: Optional[List[CategoryAllocationSchema]] = []
    ledger_rate: Optional[List[float]] = None     # List of ledger rates (e.g., [2.5, 8, 12])
    str_narration: Optional[str] = None
    inventory_allocation: Optional[InventoryEntrySchema] = None

    

# -------------------------------------------------------

# -----------------------------------------------------
# Voucher class for Purchase with Inventory XML
# -----------------------------------------------------
class TallyPurchaseInventoryVoucherSchema(BaseModel):
    company_info: CompanyInfoSchema
    party_details: PartyDetailsSchema
    consignee_details: ConsigneeDetailsSchema
    voucher_number: str
    invoice_date: str  # Expected in a format like YYYYMMDD (e.g. "20250102")
    effective_date: Optional[str] = None   # Expected in a format like YYYYMMDD (e.g. "20250102")
    invoice_no: Optional[str] = None  # Optional invoice number as in dhanuka we are not using it
    voucher_type: str = "PURCHASE A/C"  # For purchase with inventory
    voucher_class: Optional[str] = None
    narration: Optional[str] = None
    ledger_entries: List[LedgerEntrySchema] = Field(default_factory=list)
    inventory_entries: List[InventoryEntrySchema] = Field(default_factory=list)
    udf_data: Optional[List[Dict[str, Any]]] = None  # Optional UDF details
    voucher_entry_mode: str = "Item Invoice"
    strPlaceOfSupply : str = "Madhya Pradesh"
    strTaxUnitState : str = "Madhya Pradesh"
    bISParag: Optional[bool] = False
    strCompGSTIN: Optional[str] = None
    basicduedateofpymt: Optional[str] = None   # e.g. "45 Days"
    bIsVoucherDateSameAsInvoiceDate: Optional[bool] = True
    invoice_del_notes_schema: Optional[List[InvoiceDelNotesSchema]] = Field(default_factory=list) 
    invoice_order_list_schema: Optional[List[InvoiceOrderListSchema]] = Field(default_factory=list) 
    cost_center_name: Optional[str] = None 
    vat_dealer_type: Optional[str] = None  # e.g. "Regular"

    def to_xml(self) -> Element:
        envelope = Element("ENVELOPE")

        # HEADER
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"

        # BODY / IMPORTDATA
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"
        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name

        # REQUESTDATA with TALLYMESSAGE
        request_data = SubElement(import_data, "REQUESTDATA")
        tally_message = SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})


        # VOUCHER element
        voucher = SubElement(tally_message, "VOUCHER", {
            "VCHTYPE": self.voucher_type,
            "ACTION": "Create",
            "OBJVIEW": "Invoice Voucher View"
        })

        # Party Address (from PartyDetails)
        address_list_el = SubElement(voucher, "ADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.party_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Buyer/Consignee Address
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.consignee_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates (assumed to be in proper format already)
        SubElement(voucher, "DATE").text = self.invoice_date if self.bIsVoucherDateSameAsInvoiceDate else self.effective_date
        SubElement(voucher, "REFERENCEDATE").text = self.invoice_date
        SubElement(voucher, "VCHSTATUSDATE").text = self.invoice_date  
        SubElement(voucher, "EFFECTIVEDATE").text = self.invoice_date  
    
        # GST and Address Details CMPGSTREGISTRATIONTYPE
        SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        if self.vat_dealer_type:
            SubElement(voucher, "VATDEALERTYPE").text = self.vat_dealer_type
        else:
            SubElement(voucher, "VATDEALERTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.party_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.party_details.country_name

        SubElement(voucher, "PARTYGSTIN").text = self.party_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text =  self.strPlaceOfSupply
        SubElement(voucher, "CLASSNAME").text = self.voucher_class
        SubElement(voucher, "PARTYNAME").text = self.party_details.party_name

        # GST Registration for Supplier
        gstreg = SubElement(voucher, "GSTREGISTRATION", {
            "TAXTYPE": "GST",
            "TAXREGISTRATION": self.company_info.gst_in
        })
        gstreg.text = f"{self.strPlaceOfSupply} Registration"
        SubElement(voucher, "CMPGSTIN").text = self.strCompGSTIN if self.strCompGSTIN else self.party_details.gst_in
        
        if self.basicduedateofpymt:
            SubElement(voucher, "BASICDUEDATEOFPYMT").text = self.basicduedateofpymt
        
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.party_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "REFERENCE").text = self.invoice_no

        SubElement(voucher, "BASICBUYERNAME").text = self.company_info.company_name
        if self.company_info.cmp_gst_registration_type:
            SubElement(voucher, "CMPGSTREGISTRATIONTYPE").text = self.company_info.cmp_gst_registration_type
        else:
            SubElement(voucher, "CMPGSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "PARTYMAILINGNAME").text = self.party_details.party_name

        SubElement(voucher, "CONSIGNEEGSTIN").text = self.consignee_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.consignee_details.pin_code or ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.consignee_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.consignee_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.consignee_details.country_name

        SubElement(voucher, "BASICBASEPARTYNAME").text = self.party_details.party_name
        SubElement(voucher, "NUMBERINGSTYLE").text = "Auto Retain"
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = "Invoice Voucher View"
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = f"{self.strTaxUnitState} Registration"
        SubElement(voucher, "VCHGSTCLASS").text = "Not Applicable"
        if self.cost_center_name is not None:
            SubElement(voucher, "COSTCENTRENAME").text = self.cost_center_name
            SubElement(voucher, "ISCOSTCENTRE").text = "Yes"


        SubElement(voucher, "VCHENTRYMODE").text = "Item Invoice"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "ISINVOICE").text = "Yes"
        SubElement(voucher, "HASDISCOUNTS").text = "Yes"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"

        # Narration
        SubElement(voucher, "NARRATION").text = self.narration if self.narration else ""

        # Currently Only For Abhinav Infra
        if self.invoice_del_notes_schema:
            for del_note in self.invoice_del_notes_schema:
                invoice_delivery_note_detail = SubElement(voucher, "INVOICEDELNOTES.LIST")
                SubElement(invoice_delivery_note_detail, "BASICSHIPPINGDATE").text = del_note.basic_shipping_date
                SubElement(invoice_delivery_note_detail, "BASICSHIPDELIVERYNOTE").text = del_note.basic_ship_delivery_note
        
        # Currently Only For Abhinav Infra
        if self.invoice_order_list_schema:
            for po_note in self.invoice_order_list_schema:
                invoice_delivery_note_detail = SubElement(voucher, "INVOICEORDERLIST.LIST")
                SubElement(invoice_delivery_note_detail, "BASICORDERDATE").text = po_note.basic_order_date
                SubElement(invoice_delivery_note_detail, "BASICPURCHASEORDERNO").text = po_note.basic_purchase_order_no
                
        # -----------------------------------------------------------------
        # Add Inventory Entries (ALLINVENTORYENTRIES.LIST sections)
        # -----------------------------------------------------------------
        for inv_entry in self.inventory_entries:
            self._add_inventory_entry(voucher, inv_entry)

        # -----------------------------------------------------------------
        # Add Ledger Entries (LEDGERENTRIES.LIST sections)
        # -----------------------------------------------------------------
        for entry in self.ledger_entries:
            self._add_ledger_entry(voucher, entry)

        # -----------------------------------------------------------------
        # Optionally add UDF entries if provided
        # -----------------------------------------------------------------
        if self.udf_data is not None:
            if self.bISParag:
                self.add_udf_entries_For_Parag(voucher=voucher)
            else:
                self.add_udf_entries(voucher)

        return envelope

    def _add_inventory_entry(self, parent_element: Element, inv_entry: InventoryEntrySchema) -> None:

        inv_el = SubElement(parent_element, "ALLINVENTORYENTRIES.LIST")
        # To add description
        if inv_entry.description:
            basic_user_desc_list = SubElement(inv_el, "BASICUSERDESCRIPTION.LIST", TYPE="String")
            basic_user_desc = SubElement(basic_user_desc_list, "BASICUSERDESCRIPTION")
            basic_user_desc.text = inv_entry.description
            
        SubElement(inv_el, "STOCKITEMNAME").text = inv_entry.stockitemname
        SubElement(inv_el, "GSTOVRDNINELIGIBLEITC").text = inv_entry.gst_ovrd_ineligible_itc
        SubElement(inv_el, "GSTOVRDNISREVCHARGEAPPL").text = inv_entry.gst_ovrd_is_revcharge_appl
        SubElement(inv_el, "GSTOVRDNTAXABILITY").text = inv_entry.gst_ovrd_taxability
        SubElement(inv_el, "GSTSOURCETYPE").text = inv_entry.gstsourcetype
        if not self.bISParag:
            SubElement(inv_el, "GSTITEMSOURCE").text = inv_entry.stockitemname
        SubElement(inv_el, "GSTLEDGERSOURCE").text = inv_entry.gstledgersource
        SubElement(inv_el, "HSNSOURCETYPE").text = inv_entry.hsnsourcetype
        SubElement(inv_el, "HSNITEMSOURCE").text = inv_entry.hsnitemsource
        SubElement(inv_el, "GSTOVRDNSTOREDNATURE").text = inv_entry.gst_ovrd_stored_nature
        SubElement(inv_el, "GSTOVRDNTYPEOFSUPPLY").text = inv_entry.gst_ovrd_type_of_supply
        SubElement(inv_el, "GSTRATEINFERAPPLICABILITY").text = inv_entry.gstrate_infer_applicability
        SubElement(inv_el, "GSTHSNNAME").text = inv_entry.gst_hsnname
        SubElement(inv_el, "GSTHSNINFERAPPLICABILITY").text = inv_entry.gst_hsn_infer_applicability
        SubElement(inv_el, "ISDEEMEDPOSITIVE").text = "Yes" if inv_entry.is_deemed_positive else "No"
        SubElement(inv_el, "RATE").text = inv_entry.rate
        SubElement(inv_el, "DISCOUNT").text = str(inv_entry.discount)
        SubElement(inv_el, "AMOUNT").text = f"{inv_entry.amount:.2f}"
        SubElement(inv_el, "ACTUALQTY").text = inv_entry.actual_qty
        SubElement(inv_el, "BILLEDQTY").text = inv_entry.billed_qty

        # Batch Allocations
        for batch in inv_entry.batch_allocations:
            batch_el = SubElement(inv_el, "BATCHALLOCATIONS.LIST")
            SubElement(batch_el, "GODOWNNAME").text = batch.godownname
            SubElement(batch_el, "BATCHNAME").text = batch.batchname
            SubElement(batch_el, "DESTINATIONGODOWNNAME").text = batch.destinationgodownname
            SubElement(batch_el, "DYNAMICCSTISCLEARED").text = batch.dynamiccsticleared
            SubElement(batch_el, "AMOUNT").text = f"{batch.amount:.2f}"
            SubElement(batch_el, "ACTUALQTY").text = batch.actual_qty
            SubElement(batch_el, "BILLEDQTY").text = batch.billed_qty
            if batch.trackingnumber is not None:
                SubElement(batch_el, "TRACKINGNUMBER").text = batch.trackingnumber
            elif batch.tracking_no is not None:
                SubElement(batch_el, "TRACKINGNUMBER").text = batch.tracking_no
            
            if batch.order_no is not None and batch.order_no:
                SubElement(batch_el, "ORDERNO").text = batch.order_no
            if batch.orderduedate is not None and batch.orderduedate:
                try:
                    # Assume self.po_date is in format "1/4/2025" (i.e., dd/mm/yyyy or d/m/yyyy)
                    # Parse self.po_date into a datetime object
                    po_date_obj = datetime.strptime(batch.orderduedate, "%Y%m%d")

                    # Format the due date string (e.g., "1-Apr-25")
                    order_due_date = po_date_obj.strftime("%#d-%b-%y")  # Use %-d for Linux/macOS

                    # Calculate Julian Date (days since 01-Jan-1900)
                    base_date = datetime(1900, 1, 1)
                    julian_date = (po_date_obj - base_date).days + 1

                    # Create the ORDERDUEDATE XML element with P and JD attributes
                    SubElement(batch_el, "ORDERDUEDATE", {"JD": str(julian_date), "P": order_due_date}).text = order_due_date
                except Exception as e:
                    print(f"Error parsing order due date: {e}")
        # Accounting Allocations
        for alloc in inv_entry.accounting_allocations:
            alloc_el = SubElement(inv_el, "ACCOUNTINGALLOCATIONS.LIST")
            SubElement(alloc_el, "LEDGERNAME").text = alloc.ledgername
            SubElement(alloc_el, "GSTCLASS").text = alloc.gstclass
            SubElement(alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if alloc.is_deemed_positive else "No"
            SubElement(alloc_el, "AMOUNT").text = f"{alloc.amount:.2f}"
            if not self.bISParag and alloc.category_allocations:
                for category in alloc.category_allocations:
                    category_el = SubElement(alloc_el, "CATEGORYALLOCATIONS.LIST")
                    SubElement(category_el, "CATEGORY").text = category.category
                    SubElement(category_el, "ISDEEMEDPOSITIVE").text = "Yes" if category.is_deemed_positive else "No"
                    for cost_center in category.cost_center_allocations:
                        cost_center_el = SubElement(category_el, "COSTCENTERS.LIST")
                        SubElement(cost_center_el, "NAME").text = cost_center.name
                        SubElement(cost_center_el, "AMOUNT").text = f"{cost_center.amount:.2f}"

        # Rate Details
        for rate_detail in inv_entry.rate_details:
            rate_el = SubElement(inv_el, "RATEDETAILS.LIST")
            SubElement(rate_el, "GSTRATEDUTYHEAD").text = rate_detail.gstrate_duty_head
            SubElement(rate_el, "GSTRATEVALUATIONTYPE").text = rate_detail.gstrate_valuation_type
            SubElement(rate_el, "GSTRATE").text = str(rate_detail.gstrate)
        

    def _add_ledger_entry(self, parent_element: Element, entry: LedgerEntrySchema) -> None:
        ledger_list_el = SubElement(parent_element, "LEDGERENTRIES.LIST")
        
        # If round_type is provided, add ROUNDTYPE element at the top
        if entry.round_type:
            SubElement(ledger_list_el, "ROUNDTYPE").text = entry.round_type

        SubElement(ledger_list_el, "LEDGERNAME").text = entry.ledger_name

        # If method_type is provided, add METHODTYPE element
        if entry.method_type:
            SubElement(ledger_list_el, "METHODTYPE").text = entry.method_type

        SubElement(ledger_list_el, "GSTCLASS").text = entry.gst_class
        SubElement(ledger_list_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_list_el, "LEDGERFROMITEM").text = "No"
        
        # Use provided remove_zero_entries if specified (Yes/No)
        SubElement(ledger_list_el, "REMOVEZEROENTRIES").text = "Yes" if entry.remove_zero_entries else "No"
        
        SubElement(ledger_list_el, "ISPARTYLEDGER").text = "Yes" if entry.is_party_ledger else "No"
        SubElement(ledger_list_el, "GSTOVERRIDDEN").text = "Yes" if entry.gst_overridden else "No"
        
        amount_str = f"{entry.amount:.2f}"
        SubElement(ledger_list_el, "AMOUNT").text = amount_str

        # Use provided vat_exp_amount if available, otherwise use amount_str
        vat_amount = f"{entry.vat_exp_amount:.2f}" if entry.vat_exp_amount is not None else amount_str
        SubElement(ledger_list_el, "VATEXPAMOUNT").text = vat_amount

        # If round_limit is provided, add ROUNDLIMIT element
        if entry.round_limit is not None:
            SubElement(ledger_list_el, "ROUNDLIMIT").text = str(entry.round_limit)

        # If bill_allocation is provided, add BILLALLOCATIONS.LIST element using the new model
        if entry.bill_allocation:
            bill_alloc_el = SubElement(ledger_list_el, "BILLALLOCATIONS.LIST")
            SubElement(bill_alloc_el, "NAME").text = entry.bill_allocation.name
            SubElement(bill_alloc_el, "BILLTYPE").text = entry.bill_allocation.billtype
            SubElement(bill_alloc_el, "AMOUNT").text = f"{entry.bill_allocation.amount:.2f}"
            # Currently being Used By Abhinav
            if entry.bill_allocation.billcreditperiod:
                SubElement(bill_alloc_el, "BILLCREDITPERIOD").text = entry.bill_allocation.billcreditperiod

        # Add Ledger Rate if provided
        if entry.ledger_rate:
            rate_value = ", ".join(map(str, entry.ledger_rate))  # Convert list of numbers to a comma-separated string
            rateOfInvoiceTax = SubElement(ledger_list_el, "RATEOFINVOICETAX.LIST")
            SubElement(rateOfInvoiceTax, "RATEOFINVOICETAX").text = rate_value


    def add_udf_entries_For_Parag(self, voucher: Element) -> None:
        # Loop through each entry in udf_data (if needed, else you can just pick the first one)
        for data in self.udf_data:
            pdf_path = data.get("TDL", "")  # Make sure your input uses key 'pdf_path'

            udf_main = SubElement(voucher, "UDF:_UDF_805347380.LIST", {
                "DESC": "",
                "INDEX": "41011"
            })

            # Static: Document type PUR
            doc_type = SubElement(udf_main, "UDF:_UDF_788570165.LIST", {
                "DESC": "",
                "ISLIST": "YES",
                "TYPE": "String",
                "INDEX": "41012"
            })
            SubElement(doc_type, "UDF:_UDF_788570165", {"DESC": ""}).text = "PUR"

            # Dynamic: PDF File Path
            file_path = SubElement(udf_main, "UDF:_UDF_788570166.LIST", {
                "DESC": "",
                "ISLIST": "YES",
                "TYPE": "String",
                "INDEX": "41013"
            })
            SubElement(file_path, "UDF:_UDF_788570166", {"DESC": ""}).text = pdf_path
            
    def add_udf_entries(self, voucher: Element) -> None:
        # Example implementation for adding UDF entries.
        # --------------------- START PARAG TDL  ------------------------- 
        # base_index = 1010
        # for data in self.udf_data:
        #     udf_agg = SubElement(voucher, "UDF:LINKOFFEREDAGG.LIST", {
        #         "DESC": "`LinkOfferedAgg`",
        #         "INDEX": str(base_index)
        #     })
        #     link_offered = data.get("LinkOffered")
        #     if link_offered:
        #         udf_link = SubElement(udf_agg, "UDF:LINKOFFERED.LIST", {
        #             "DESC": "`Link Offered`",
        #             "ISLIST": "YES",
        #             "TYPE": "String",
        #             "INDEX": str(base_index + 1)
        #         })
        #         SubElement(udf_link, "UDF:LINKOFFERED", {"DESC": "`Link Offered`"}).text = link_offered
            # Continue with additional UDF fields as needed.
        
        # --------------------- End PARAG TDL  ------------------------- 

        # --------------------- START AV TDL  ------------------------- 
        # Initialize the base index
        base_index = 1010  # Starting index for UDF fields

        # Loop through each dictionary in udf_data
        for idx, data in enumerate(self.udf_data):
            base_index = 1010  # Starting index for UDF fields
            # Create UDF:LINKOFFEREDAGG.LIST element
            link_offered_agg = SubElement(voucher, "UDF:LINKOFFEREDAGG.LIST", {
                "DESC": "`LinkOfferedAgg`",
                "INDEX": str(base_index)  # Incrementing index per item
            })

            # Add LinkOffered field
            link_offered = data.get("LinkOffered")
            if link_offered:
                link_offered_element = SubElement(link_offered_agg, "UDF:LINKOFFERED.LIST", {
                    "DESC": "`Link Offered`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 1)
                })
                SubElement(link_offered_element, "UDF:LINKOFFERED", {"DESC": "`Link Offered`"}).text = link_offered

            # Add LinkDocName field
            link_doc_name = data.get("LinkDocName")
            if link_doc_name:
                link_doc_name_element = SubElement(link_offered_agg, "UDF:LINKDOCNAME.LIST", {
                    "DESC": "`LinkDocName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 2)
                })
                SubElement(link_doc_name_element, "UDF:LINKDOCNAME", {"DESC": "`LinkDocName`"}).text = link_doc_name

            # Add stDocNameLineno field
            st_doc_name_lineno = data.get("stDocNameLineno")
            if st_doc_name_lineno:
                st_doc_name_lineno_element = SubElement(link_offered_agg, "UDF:STDOCNAMELINENO.LIST", {
                    "DESC": "`stDocNameLineno`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 3)
                })
                SubElement(st_doc_name_lineno_element, "UDF:STDOCNAMELINENO", {"DESC": "`stDocNameLineno`"}).text = st_doc_name_lineno

            # Add StUploadTimePeriod field
            st_upload_time_period = data.get("StUploadTimePeriod")
            if st_upload_time_period:
                st_upload_time_period_element = SubElement(link_offered_agg, "UDF:STUPLOADTIMEPERIOD.LIST", {
                    "DESC": "`StUploadTimePeriod`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 6)
                })
                SubElement(st_upload_time_period_element, "UDF:STUPLOADTIMEPERIOD", {"DESC": "`StUploadTimePeriod`"}).text = st_upload_time_period

            # Add stUserName field
            st_user_name = data.get("stUserName")
            if st_user_name:
                st_user_name_element = SubElement(link_offered_agg, "UDF:STUSERNAME.LIST", {
                    "DESC": "`stUserName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 7)
                })
                SubElement(st_user_name_element, "UDF:STUSERNAME", {"DESC": "`stUserName`"}).text = st_user_name

            # Add stFullpath field
            st_fullpath = data.get("stFullpath")
            if st_fullpath:
                st_fullpath_element = SubElement(link_offered_agg, "UDF:STFULLPATH.LIST", {
                    "DESC": "`stFullpath`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 8)
                })
                SubElement(st_fullpath_element, "UDF:STFULLPATH", {"DESC": "`stFullpath`"}).text = st_fullpath

            # Add TDL field
            tdl = data.get("TDL")
            if tdl:
                tdl_element = SubElement(link_offered_agg, "UDF:TDL.LIST", {
                    "DESC": "`TDL`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 9)
                })
                SubElement(tdl_element, "UDF:TDL", {"DESC": "`TDL`"}).text = tdl
        
        # --------------------- End AV TDL  ------------------------- 
    
    def to_string(self, pretty: bool = False) -> str:
        envelope = self.to_xml()
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            import xml.dom.minidom
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")


class CParagTallyPurchaseVoucherSchema(BaseModel):
    """
    Main model that combines:
        - Company info
        - Party details
        - Consignee details
        - Ledger lines
        - And constructs the final XML using the Tally voucher template.
    """
    company_info: CompanyInfoSchema
    party_details: PartyDetailsSchema
    consignee_details: ConsigneeDetailsSchema
    voucher_number: str
    invoice_date: str
    invoice_no: str
    voucher_type: str = "AV EXPENSES A/C"
    narration: Optional[str] = None
    cost_center_name: str = ""
    ledger_entries: List[LedgerEntrySchema] = Field(default_factory=list)
    additionalInfo: Optional[AdditionalLedgerInfo] = Field(default_factory=AdditionalLedgerInfo)
    udf_data: Optional[List[Dict[str, Any]]] = None  # Dynamic UDF values

    
    
    
    def to_xml(self) -> Element:
        """
        Builds the Tally voucher XML using xml.etree.ElementTree
        and returns the root XML Element.
        """
        # Envelope
        envelope = Element("ENVELOPE")

        # HEADER
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"

        # BODY
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")

        # REQUESTDESC
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"

        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name

        # REQUESTDATA
        request_data = SubElement(import_data, "REQUESTDATA")

        tally_message = SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})

        # VOUCHER
        voucher = SubElement(tally_message, "VOUCHER", {
            "VCHTYPE": self.voucher_type,
            "ACTION": "Create",
            "OBJVIEW": "Invoice Voucher View"
        })

        # Party Address
        address_list_el = SubElement(voucher, "ADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.party_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Consignee Address
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.consignee_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates
        date_str = self.invoice_date
        SubElement(voucher, "DATE").text = date_str
        SubElement(voucher, "REFERENCEDATE").text = date_str
        SubElement(voucher, "VCHSTATUSDATE").text = date_str
        SubElement(voucher, "EFFECTIVEDATE").text = date_str

        # GST Info
        SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "VATDEALERTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.party_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.party_details.country_name

        # Narration
        SubElement(voucher, "NARRATION").text = self.narration if self.narration else ""

        # Party GSTIN
        SubElement(voucher, "PARTYGSTIN").text = self.party_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text = self.party_details.state_name

        # Party Name
        SubElement(voucher, "PARTYNAME").text = self.party_details.party_name

        # Tally Company GST config
        gstreg = SubElement(voucher, "GSTREGISTRATION", {
            "TAXTYPE": "GST",
            "TAXREGISTRATION": self.company_info.gst_in
        })
        gstreg.text = "Gujarat Registration"
        SubElement(voucher, "CMPGSTIN").text = self.company_info.gst_in

        # Voucher type, ledger name, numbers
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.party_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "REFERENCE").text = self.invoice_no

        # Basic Buyer & Consignee Info
        SubElement(voucher, "BASICBUYERNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "PARTYMAILINGNAME").text = self.party_details.party_name
        SubElement(voucher, "PARTYPINCODE").text = self.party_details.pin_code or ""
        SubElement(voucher, "CONSIGNEEGSTIN").text = self.consignee_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.consignee_details.pin_code or ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.consignee_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.consignee_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.consignee_details.country_name

        # Basic base party
        SubElement(voucher, "BASICBASEPARTYNAME").text = self.party_details.party_name

        # Other static fields 
        SubElement(voucher, "NUMBERINGSTYLE").text = "Manual"
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = "Invoice Voucher View"
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = "Gujarat Registration"
        SubElement(voucher, "VCHGSTCLASS").text = " Not Applicable"
        SubElement(voucher, "COSTCENTRENAME").text = self.cost_center_name
        # example: slicing GSTIN to get  BuyerPinNumber
        SubElement(voucher, "BUYERPINNUMBER").text = self.party_details.gst_in[:-3]  
        SubElement(voucher, "VCHENTRYMODE").text = "Accounting Invoice"
        SubElement(voucher, "ISINVOICE").text = "Yes"
        SubElement(voucher, "DIFFACTUALQTY").text = "Yes" if self.additionalInfo.bDiffActualQTY else "No"
        SubElement(voucher, "HASDISCOUNTS").text = "No"
        SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"
        
        if self.udf_data is not None:
            self.add_udf_entries(voucher)
        
        

        
        for entry in self.ledger_entries:
                self._add_ledger_entry(voucher, entry)


        return envelope
    
    
    
          
    def add_udf_entries(self, voucher):
            # Initialize the base index
        base_index = 41011  # Matching your XML index pattern

        # Loop through each dictionary in udf_data
        for idx, data in enumerate(self.udf_data):
            # Create main UDF container
            udf_container = SubElement(voucher, "UDF:_UDF_805347380.LIST", {
                "DESC": "",
                "INDEX": str(base_index)
            })

            # Add DocType field
            doc_type = data.get("DocType")
            if doc_type:
                doc_type_element = SubElement(udf_container, "UDF:_UDF_788570165.LIST", {
                    "DESC": "",
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 1)
                })
                SubElement(doc_type_element, "UDF:_UDF_788570165", {"DESC": ""}).text = doc_type

            # Add DocPath field
            doc_path = data.get("DocPath")
            if doc_path:
                doc_path_element = SubElement(udf_container, "UDF:_UDF_788570166.LIST", {
                    "DESC": "",
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 2)
                })
                SubElement(doc_path_element, "UDF:_UDF_788570166", {"DESC": ""}).text = doc_path
            
           
            
            
    def _add_ledger_entry(self, parent_element: Element, entry: LedgerEntrySchema) -> None:
        """
        Construct the <LEDGERENTRIES.LIST> section
        and BillAllocations if it is a party ledger.
        """
        ledger_list_el = SubElement(parent_element, "LEDGERENTRIES.LIST")

        SubElement(ledger_list_el, "LEDGERNAME").text = entry.ledger_name
        SubElement(ledger_list_el, "GSTCLASS").text = f" {entry.gst_class}"
        SubElement(ledger_list_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_list_el, "LEDGERFROMITEM").text = "No"
        SubElement(ledger_list_el, "REMOVEZEROENTRIES").text = "No"
        SubElement(ledger_list_el, "ISPARTYLEDGER").text = "Yes" if entry.is_party_ledger else "No"
        SubElement(ledger_list_el, "GSTOVERRIDDEN").text = "Yes" if entry.gst_overridden else "No"

        # Format the amount (include negative sign if needed).
        amount_str = f"{entry.amount:.2f}"
        SubElement(ledger_list_el, "AMOUNT").text = amount_str
        SubElement(ledger_list_el, "VATEXPAMOUNT").text = amount_str

        # Cost center allocations
        if entry.cost_center:
            cat_alloc_el = SubElement(ledger_list_el, "CATEGORYALLOCATIONS.LIST")
            SubElement(cat_alloc_el, "CATEGORY").text = entry.cost_category
            SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"

            cost_center_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
            SubElement(cost_center_alloc_el, "NAME").text = entry.cost_center
            SubElement(cost_center_alloc_el, "AMOUNT").text = amount_str

        # If it's the Party Ledger, add Bill Allocations
        if entry.is_party_ledger:
            bill_alloc_el = SubElement(ledger_list_el, "BILLALLOCATIONS.LIST")
            SubElement(bill_alloc_el, "NAME").text = self.invoice_no
            SubElement(bill_alloc_el, "BILLTYPE").text = "New Ref"
            SubElement(bill_alloc_el, "TDSDEDUCTEEISSPECIALRATE").text = "No"
            SubElement(bill_alloc_el, "AMOUNT").text = amount_str

    def to_string(self, pretty: bool = False) -> str:
        """
        Return the XML as a string. If pretty=True, attempts to indent the XML.
        """
        envelope = self.to_xml()
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            import xml.dom.minidom
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")

class TallyPurchaseVoucherSchema(BaseModel):
    """
    Main model that combines:
        - Company info
        - Party details
        - Consignee details
        - Ledger lines
        - And constructs the final XML using the Tally voucher template.
    """
    company_info: CompanyInfoSchema
    party_details: PartyDetailsSchema
    consignee_details: ConsigneeDetailsSchema
    voucher_number: str
    invoice_date: str
    invoice_no: str
    voucher_type: str = "Purchase (Credit)"
    narration: Optional[str] = None
    cost_center_name: str = "Sweet Factory"
    ledger_entries: List[LedgerEntrySchema] = Field(default_factory=list)
    additionalInfo: Optional[AdditionalLedgerInfo] = Field(default_factory=AdditionalLedgerInfo)
    udf_data: Optional[List[Dict[str, Any]]] = None  # Dynamic UDF values
    voucher_entry_mode: str = "Item Invoice"
    strPlaceOfSupply : Optional[str] = ""
    strTaxUnitState : Optional[str]  = ""
    bISParag: Optional[bool] = False
    
    
    def to_xml(self, strVoucherViewMode = "Invoice Voucher View") -> Element:
        """
        Builds the Tally voucher XML using xml.etree.ElementTree
        and returns the root XML Element.
        """
        # Envelope
        envelope = Element("ENVELOPE")

        # HEADER
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"

        # BODY
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")

        # REQUESTDESC
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"

        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name

        # REQUESTDATA
        request_data = SubElement(import_data, "REQUESTDATA")

        tally_message = SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})

        # VOUCHER
        voucher = SubElement(tally_message, "VOUCHER", {
            "VCHTYPE": self.voucher_type,
            "ACTION": "Create",
            "OBJVIEW": strVoucherViewMode
        })

        # Party Address
        address_list_el = SubElement(voucher, "ADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.party_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Consignee Address
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.consignee_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates
        date_str = self.invoice_date
        SubElement(voucher, "DATE").text = date_str
        SubElement(voucher, "REFERENCEDATE").text = date_str
        SubElement(voucher, "VCHSTATUSDATE").text = date_str
        SubElement(voucher, "EFFECTIVEDATE").text = date_str

        # GST Info
        SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "VATDEALERTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.party_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.party_details.country_name

        # Narration
        SubElement(voucher, "NARRATION").text = self.narration if self.narration else ""

        # Party GSTIN
        SubElement(voucher, "PARTYGSTIN").text = self.party_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text = self.strPlaceOfSupply if self.strPlaceOfSupply != "" else self.party_details.state_name

        # Party Name
        SubElement(voucher, "PARTYNAME").text = self.party_details.party_name

        # Tally Company GST config
        gstreg = SubElement(voucher, "GSTREGISTRATION", {
            "TAXTYPE": "GST",
            "TAXREGISTRATION": self.company_info.gst_in
        })
        gstreg.text = f"{self.strPlaceOfSupply} Registration" if self.strPlaceOfSupply != "" else "Gujarat Registration"
        SubElement(voucher, "CMPGSTIN").text = self.company_info.gst_in

        # Voucher type, ledger name, numbers
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.party_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "REFERENCE").text = self.invoice_no

        # Basic Buyer & Consignee Info
        SubElement(voucher, "BASICBUYERNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "PARTYMAILINGNAME").text = self.party_details.party_name
        SubElement(voucher, "PARTYPINCODE").text = self.party_details.pin_code or ""
        SubElement(voucher, "CONSIGNEEGSTIN").text = self.consignee_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.consignee_details.mailing_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.consignee_details.pin_code or ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.consignee_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.consignee_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.consignee_details.country_name

        # Basic base party
        SubElement(voucher, "BASICBASEPARTYNAME").text = self.party_details.party_name

        # Other static fields 
        SubElement(voucher, "NUMBERINGSTYLE").text = "Manual"
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = strVoucherViewMode
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = f"{self.strTaxUnitState} Registration" if self.strTaxUnitState != "" else "Gujarat Registration"
        SubElement(voucher, "VCHGSTCLASS").text = " Not Applicable"
        SubElement(voucher, "COSTCENTRENAME").text = self.cost_center_name
        # example: slicing GSTIN to get  BuyerPinNumber
        SubElement(voucher, "BUYERPINNUMBER").text = self.party_details.gst_in[:-3]  
        SubElement(voucher, "VCHENTRYMODE").text = self.voucher_entry_mode
        SubElement(voucher, "ISINVOICE").text = "Yes"
        SubElement(voucher, "DIFFACTUALQTY").text = "Yes" if self.additionalInfo.bDiffActualQTY else "No"
        SubElement(voucher, "HASDISCOUNTS").text = "No"
        SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"
        
        if self.udf_data is not None:
            if self.bISParag:
                self.add_udf_entries_For_Parag(voucher=voucher)
            else:
                self.add_udf_entries(voucher)
        
        

        
        for entry in self.ledger_entries:
                self._add_ledger_entry(voucher, entry)


        return envelope
    
    def add_udf_entries_For_Parag(self, voucher: Element) -> None:
        # Loop through each entry in udf_data (if needed, else you can just pick the first one)
        for data in self.udf_data:
            pdf_path = data.get("TDL", "")  # Make sure your input uses key 'pdf_path'

            udf_main = SubElement(voucher, "UDF:_UDF_805347380.LIST", {
                "DESC": "",
                "INDEX": "41011"
            })

            # Static: Document type PUR
            doc_type = SubElement(udf_main, "UDF:_UDF_788570165.LIST", {
                "DESC": "",
                "ISLIST": "YES",
                "TYPE": "String",
                "INDEX": "41012"
            })
            SubElement(doc_type, "UDF:_UDF_788570165", {"DESC": ""}).text = "PUR"

            # Dynamic: PDF File Path
            file_path = SubElement(udf_main, "UDF:_UDF_788570166.LIST", {
                "DESC": "",
                "ISLIST": "YES",
                "TYPE": "String",
                "INDEX": "41013"
            })
            SubElement(file_path, "UDF:_UDF_788570166", {"DESC": ""}).text = pdf_path
    
    
    def add_udf_entries(self, voucher):
        # Initialize the base index
        base_index = 1010  # Starting index for UDF fields

        # Loop through each dictionary in udf_data
        for idx, data in enumerate(self.udf_data):
            base_index = 1010  # Starting index for UDF fields
            # Create UDF:LINKOFFEREDAGG.LIST element
            link_offered_agg = SubElement(voucher, "UDF:LINKOFFEREDAGG.LIST", {
                "DESC": "`LinkOfferedAgg`",
                "INDEX": str(base_index)  # Incrementing index per item
            })

            # Add LinkOffered field
            link_offered = data.get("LinkOffered")
            if link_offered:
                link_offered_element = SubElement(link_offered_agg, "UDF:LINKOFFERED.LIST", {
                    "DESC": "`Link Offered`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 1)
                })
                SubElement(link_offered_element, "UDF:LINKOFFERED", {"DESC": "`Link Offered`"}).text = link_offered

            # Add LinkDocName field
            link_doc_name = data.get("LinkDocName")
            if link_doc_name:
                link_doc_name_element = SubElement(link_offered_agg, "UDF:LINKDOCNAME.LIST", {
                    "DESC": "`LinkDocName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 2)
                })
                SubElement(link_doc_name_element, "UDF:LINKDOCNAME", {"DESC": "`LinkDocName`"}).text = link_doc_name

            # Add stDocNameLineno field
            st_doc_name_lineno = data.get("stDocNameLineno")
            if st_doc_name_lineno:
                st_doc_name_lineno_element = SubElement(link_offered_agg, "UDF:STDOCNAMELINENO.LIST", {
                    "DESC": "`stDocNameLineno`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 3)
                })
                SubElement(st_doc_name_lineno_element, "UDF:STDOCNAMELINENO", {"DESC": "`stDocNameLineno`"}).text = st_doc_name_lineno

            # Add StUploadTimePeriod field
            st_upload_time_period = data.get("StUploadTimePeriod")
            if st_upload_time_period:
                st_upload_time_period_element = SubElement(link_offered_agg, "UDF:STUPLOADTIMEPERIOD.LIST", {
                    "DESC": "`StUploadTimePeriod`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 6)
                })
                SubElement(st_upload_time_period_element, "UDF:STUPLOADTIMEPERIOD", {"DESC": "`StUploadTimePeriod`"}).text = st_upload_time_period

            # Add stUserName field
            st_user_name = data.get("stUserName")
            if st_user_name:
                st_user_name_element = SubElement(link_offered_agg, "UDF:STUSERNAME.LIST", {
                    "DESC": "`stUserName`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 7)
                })
                SubElement(st_user_name_element, "UDF:STUSERNAME", {"DESC": "`stUserName`"}).text = st_user_name

            # Add stFullpath field
            st_fullpath = data.get("stFullpath")
            if st_fullpath:
                st_fullpath_element = SubElement(link_offered_agg, "UDF:STFULLPATH.LIST", {
                    "DESC": "`stFullpath`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 8)
                })
                SubElement(st_fullpath_element, "UDF:STFULLPATH", {"DESC": "`stFullpath`"}).text = st_fullpath

            # Add TDL field
            tdl = data.get("TDL")
            if tdl:
                tdl_element = SubElement(link_offered_agg, "UDF:TDL.LIST", {
                    "DESC": "`TDL`",  # Use backticks around the description
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index  + 9)
                })
                SubElement(tdl_element, "UDF:TDL", {"DESC": "`TDL`"}).text = tdl
           
            
    def _add_ledger_entry(self, parent_element: Element, entry: LedgerEntrySchema) -> None:
        """
        Construct the <LEDGERENTRIES.LIST> section
        and BillAllocations if it is a party ledger.
        """
        ledger_list_el = SubElement(parent_element, "LEDGERENTRIES.LIST")

        SubElement(ledger_list_el, "LEDGERNAME").text = entry.ledger_name
        SubElement(ledger_list_el, "GSTCLASS").text = f" {entry.gst_class}"
        SubElement(ledger_list_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_list_el, "LEDGERFROMITEM").text = "No"
        SubElement(ledger_list_el, "REMOVEZEROENTRIES").text = "No"
        SubElement(ledger_list_el, "ISPARTYLEDGER").text = "Yes" if entry.is_party_ledger else "No"
        SubElement(ledger_list_el, "GSTOVERRIDDEN").text = "Yes" if entry.gst_overridden else "No"

        # Format the amount (include negative sign if needed).
        amount_str = f"{entry.amount:.2f}"
        SubElement(ledger_list_el, "AMOUNT").text = amount_str
        SubElement(ledger_list_el, "VATEXPAMOUNT").text = amount_str

        # Cost center allocations
        if entry.cost_center:
            cat_alloc_el = SubElement(ledger_list_el, "CATEGORYALLOCATIONS.LIST")
            SubElement(cat_alloc_el, "CATEGORY").text = entry.cost_category
            SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"

            cost_center_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
            SubElement(cost_center_alloc_el, "NAME").text = entry.cost_center
            SubElement(cost_center_alloc_el, "AMOUNT").text = amount_str

        # If it's the Party Ledger, add Bill Allocations
        if entry.is_party_ledger:
            bill_alloc_el = SubElement(ledger_list_el, "BILLALLOCATIONS.LIST")
            SubElement(bill_alloc_el, "NAME").text = self.invoice_no
            SubElement(bill_alloc_el, "BILLTYPE").text = "New Ref"
            SubElement(bill_alloc_el, "TDSDEDUCTEEISSPECIALRATE").text = "No"
            SubElement(bill_alloc_el, "AMOUNT").text = amount_str
        
        if getattr(entry, "str_narration") and entry.str_narration:
            SubElement(ledger_list_el, "NARRATION").text = entry.str_narration


    def to_string(self, pretty: bool = False,strVoucherViewMode="Invoice Voucher View") -> str:
        """
        Return the XML as a string. If pretty=True, attempts to indent the XML.
        """
        envelope = self.to_xml(strVoucherViewMode=strVoucherViewMode)
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            import xml.dom.minidom
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")



# --------------------------------------------------------------------
# Tally Journal Voucher Schema
# --------------------------------------------------------------------
class TallyJournalVoucherSchema(BaseModel):
    company_info: CompanyInfoSchema
    voucher_number: str
    voucher_date: str  = Field(..., description="Voucher date (e.g. '********')")
    narration: str
    reference: str
    # Voucher header fixed values (can be overridden if needed)
    voucher_type: str = "AV Tax Journal"
    numbering_style: str = "Automatic (Manual Override)"
    persisted_view: str = "Accounting Voucher View"
    vch_entry_mode: str = "As Voucher"
    voucher_type_origin_name: str = "AV Tax Journal"
    vch_status_voucher_type: str = "AV Tax Journal"
    effective_date: Optional[str] = None
    ledger_entries: List[LedgerEntrySchemaForJournalVoucher] = Field(default_factory=list)

    def to_xml(self) -> Element:
        """
        Build the Tally Journal Voucher XML with the required tags.
        """
        # Use voucher_date as effective_date if not provided
        effective_date = self.effective_date if self.effective_date else self.voucher_date

        # ------------------ ENVELOPE ------------------
        envelope = Element("ENVELOPE")

        # HEADER
        header = SubElement(envelope, "HEADER")
        SubElement(header, "VERSION").text = "5"
        SubElement(header, "TALLYREQUEST").text = "Import"
        SubElement(header, "TYPE").text = "Data"
        SubElement(header, "ID").text = "Vouchers"

        # BODY with DESC (STATICVARIABLES) and DATA
        body = SubElement(envelope, "BODY")
        desc = SubElement(body, "DESC")
        static_vars = SubElement(desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name

        data = SubElement(body, "DATA")
        tally_message = SubElement(data, "TALLYMESSAGE")

        # ------------------ VOUCHER ------------------
        voucher = SubElement(tally_message, "VOUCHER")
        # Header Fields
        SubElement(voucher, "DATE").text = self.voucher_date
        SubElement(voucher, "NARRATION").text = self.narration
        SubElement(voucher, "REFERENCE").text = self.reference
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "NUMBERINGSTYLE").text = self.numbering_style
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "PERSISTEDVIEW").text = self.persisted_view
        SubElement(voucher, "VCHENTRYMODE").text = self.vch_entry_mode
        SubElement(voucher, "VOUCHERTYPEORIGNAME").text = self.voucher_type_origin_name
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.vch_status_voucher_type
        SubElement(voucher, "EFFECTIVEDATE").text = effective_date

        # ------------------ Ledger Entries ------------------
        for entry in self.ledger_entries:
            self._add_ledger_entry(voucher, entry)

        return envelope

    def _add_ledger_entry(self, parent_element: Element, entry: LedgerEntrySchema) -> None:
        """
        Append a single <LEDGERENTRIES.LIST> node for a ledger entry,
        including optional GST, cost center, and bill allocation tags.
        """
        ledger_el = SubElement(parent_element, "LEDGERENTRIES.LIST")

        # Basic Ledger Info
        SubElement(ledger_el, "LEDGERNAME").text = entry.ledger_name

        # For expense ledgers, add GST override tags if provided
        if entry.gst_taxability:
            SubElement(ledger_el, "GSTOVRDNTAXABILITY").text = entry.gst_taxability
        if entry.gst_type_of_supply:
            SubElement(ledger_el, "GSTOVRDNTYPEOFSUPPLY").text = entry.gst_type_of_supply

        SubElement(ledger_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_el, "ISPARTYLEDGER").text = "Yes" if entry.is_party_ledger else "No"

        amount_str = f"{entry.amount}"
        SubElement(ledger_el, "AMOUNT").text = amount_str
        SubElement(ledger_el, "VATEXPAMOUNT").text = amount_str

        # Add Cost Center Allocations if provided
        if entry.cost_center_allocations and entry.cost_center_category:
            cat_alloc = SubElement(ledger_el, "CATEGORYALLOCATIONS.LIST")
            SubElement(cat_alloc, "CATEGORY").text = entry.cost_center_category
            SubElement(cat_alloc, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
            # For each cost center allocation, add a COSTCENTREALLOCATIONS.LIST node
            for allocation in entry.cost_center_allocations:
                cost_center_alloc = SubElement(cat_alloc, "COSTCENTREALLOCATIONS.LIST")
                SubElement(cost_center_alloc, "NAME").text = allocation.get("name", "")
                SubElement(cost_center_alloc, "AMOUNT").text = f"{allocation.get('amount', 0)}"

        # Add Bill Allocation if provided
        if entry.bill_allocation:
            bill_alloc = SubElement(ledger_el, "BILLALLOCATIONS.LIST")
            SubElement(bill_alloc, "NAME").text = entry.bill_allocation.get("name", "")
            SubElement(bill_alloc, "BILLTYPE").text = entry.bill_allocation.get("billtype", "")
            SubElement(bill_alloc, "AMOUNT").text = f"{entry.bill_allocation.get('amount', 0)}"
        
        
        
    def to_string(self, pretty: bool = False) -> str:
        """
        Convert the XML to a string.
        """
        envelope = self.to_xml()
        raw_str = tostring(envelope, encoding="utf-8").decode("utf-8")
        if not pretty:
            return raw_str
        else:
            import xml.dom.minidom
            dom = xml.dom.minidom.parseString(raw_str)
            return dom.toprettyxml(indent="    ")


# -----------------------------------------------------
# New Model for Purchase Order Voucher (PO)
# -----------------------------------------------------

class TallyPOVoucherSchema(BaseModel):
    company_info: CompanyInfoSchema
    seller_details: PartyDetailsSchema  # Seller information (appears in ADDRESS.LIST, PARTYNAME etc.)
    buyer_details: PartyDetailsSchema   # Buyer information (used in BASICBUYERADDRESS.LIST, BASICBUYERNAME etc.)
    voucher_number: str                 # e.g. "PO-ZFLB-CIVIL\\24-25\\0002"
    cost_center: str
    po_date: str                        # Date in YYYYMMDD format (e.g. "20250402")
    narration: Optional[str] = ""
    voucher_type: str = "PO-ZFLB-CIVIL ::"  # PO voucher type as per your sample
    consigneecstnumber: Optional[str] = None  # Extra PO tag
    basicduedateofpymt: Optional[str] = None   # e.g. "45 Days"
    buyerpinnumber: Optional[str] = None       # e.g. "**********"
    consigneepinnumber: Optional[str] = None   # e.g. "**********"
    inventory_entries: List[InventoryEntrySchema] = Field(default_factory=list)  # Should be list of InventoryEntrySchema
    ledger_entries: List[LedgerEntrySchema] = Field(default_factory=list)   # Should be list of LedgerEntrySchema
    udf_data: Optional[List[Dict[str, Any]]] = None  # Optional UDF details

    def to_xml(self) -> Element:
        envelope = Element("ENVELOPE")
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"
        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name
        request_data = SubElement(import_data, "REQUESTDATA")
        tally_message = SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})
        voucher = SubElement(tally_message, "VOUCHER", {
            "VCHTYPE": self.voucher_type,
            "ACTION": "Create",
            "OBJVIEW": "Invoice Voucher View"
        })

        # Seller Address (ADDRESS.LIST)
        address_list_el = SubElement(voucher, "ADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.seller_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Buyer Address (BASICBUYERADDRESS.LIST)
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.buyer_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates
        SubElement(voucher, "DATE").text = self.po_date
        SubElement(voucher, "VCHSTATUSDATE").text = self.po_date
        SubElement(voucher, "EFFECTIVEDATE").text = self.po_date

        # GST and Address Details from Seller
        SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "VATDEALERTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.seller_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.seller_details.country_name

        SubElement(voucher, "PARTYGSTIN").text = self.seller_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text = self.seller_details.state_name

        SubElement(voucher, "NARRATION").text = self.narration

        # Party (Seller) Name and GST Registration info
        SubElement(voucher, "PARTYNAME").text = self.seller_details.party_name
        gstreg = SubElement(voucher, "GSTREGISTRATION", {
            "TAXTYPE": "GST",
            "TAXREGISTRATION": self.company_info.gst_in
        })
        gstreg.text = f"{self.seller_details.state_name} Registration"
        SubElement(voucher, "CMPGSTIN").text = self.company_info.gst_in

        # Voucher Type, Ledger and Voucher Number
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.seller_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number

        # Buyer and Reference Details
        SubElement(voucher, "BASICBUYERNAME").text = self.buyer_details.party_name
        SubElement(voucher, "CMPGSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "REFERENCE").text = self.voucher_number
        SubElement(voucher, "PARTYMAILINGNAME").text = self.seller_details.party_name

        # Consignee (Buyer) Details
        SubElement(voucher, "CONSIGNEEGSTIN").text = self.buyer_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.buyer_details.party_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.buyer_details.pin_code or ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.buyer_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.buyer_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.buyer_details.country_name

        # Basic Base Party
        SubElement(voucher, "BASICBASEPARTYNAME").text = self.seller_details.party_name

        # NUMBERINGSTYLE and extra PO tags
        SubElement(voucher, "NUMBERINGSTYLE").text = "Auto Retain"
        if self.consigneecstnumber:
            SubElement(voucher, "CONSIGNEECSTNUMBER").text = self.consigneecstnumber
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = "Invoice Voucher View"
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = f"{self.seller_details.state_name} Registration"
        if self.basicduedateofpymt:
            SubElement(voucher, "BASICDUEDATEOFPYMT").text = self.basicduedateofpymt
        # For this example, we set COSTCENTRENAME as a fixed value ("ZFLB")
        SubElement(voucher, "COSTCENTRENAME").text = self.cost_center
        if self.buyerpinnumber:
            SubElement(voucher, "BUYERPINNUMBER").text = self.buyerpinnumber
        if self.consigneepinnumber:
            SubElement(voucher, "CONSIGNEEPINNUMBER").text = self.consigneepinnumber

        SubElement(voucher, "DIFFACTUALQTY").text = "No"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "ISINVOICE").text = "No"
        SubElement(voucher, "HASDISCOUNTS").text = "No"
        SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"

        # -----------------------------------------------------------------
        # Add Inventory Entries (ALLINVENTORYENTRIES.LIST)
        # -----------------------------------------------------------------
        for inv_entry in self.inventory_entries:
            self._add_inventory_entry(voucher, inv_entry)

        # -----------------------------------------------------------------
        # Add Ledger Entries (LEDGERENTRIES.LIST)
        # -----------------------------------------------------------------
        for entry in self.ledger_entries:
            self._add_ledger_entry(voucher, entry)

        # -----------------------------------------------------------------
        # Optionally add UDF entries if provided
        # -----------------------------------------------------------------
        if self.udf_data is not None:
            self.add_udf_entries(voucher)

        return envelope

    def _add_inventory_entry(self, parent_element: Element, inv_entry: InventoryEntrySchema) -> None:
        inv_el = SubElement(parent_element, "ALLINVENTORYENTRIES.LIST")

        # To add description
        if inv_entry.description:
            basic_user_desc_list = SubElement(inv_el, "BASICUSERDESCRIPTION.LIST", TYPE="String")
            basic_user_desc = SubElement(basic_user_desc_list, "BASICUSERDESCRIPTION")
            basic_user_desc.text = inv_entry.description

        SubElement(inv_el, "STOCKITEMNAME").text = inv_entry.stockitemname
        SubElement(inv_el, "GSTOVRDNTAXABILITY").text = getattr(inv_entry, "gst_ovrd_taxability", "Taxable")
        SubElement(inv_el, "GSTSOURCETYPE").text = getattr(inv_entry, "gstsourcetype", "Stock Item")
        SubElement(inv_el, "GSTITEMSOURCE").text = getattr(inv_entry, "hsnitemsource", inv_entry.stockitemname)
        SubElement(inv_el, "HSNSOURCETYPE").text = getattr(inv_entry, "hsnsourcetype", "Stock Item")
        SubElement(inv_el, "HSNITEMSOURCE").text = getattr(inv_entry, "hsnitemsource", inv_entry.stockitemname)
        SubElement(inv_el, "GSTOVRDNTYPEOFSUPPLY").text = getattr(inv_entry, "gst_ovrd_type_of_supply", "Goods")
        SubElement(inv_el, "GSTRATEINFERAPPLICABILITY").text = getattr(inv_entry, "gstrate_infer_applicability", "As per Masters/Company")
        SubElement(inv_el, "GSTHSNNAME").text = getattr(inv_entry, "gst_hsnname", "")
        SubElement(inv_el, "GSTHSNINFERAPPLICABILITY").text = getattr(inv_entry, "gst_hsn_infer_applicability", "As per Masters/Company")
        SubElement(inv_el, "ISDEEMEDPOSITIVE").text = "Yes" if inv_entry.is_deemed_positive else "No"
        SubElement(inv_el, "RATE").text = inv_entry.rate
        SubElement(inv_el, "AMOUNT").text = f"{inv_entry.amount:.2f}"
        SubElement(inv_el, "ACTUALQTY").text = inv_entry.actual_qty
        SubElement(inv_el, "BILLEDQTY").text = inv_entry.billed_qty

        # Batch Allocations (unchanged)
        for batch in inv_entry.batch_allocations:
            batch_el = SubElement(inv_el, "BATCHALLOCATIONS.LIST")
            SubElement(batch_el, "GODOWNNAME").text = batch.godownname
            SubElement(batch_el, "BATCHNAME").text = batch.batchname
            SubElement(batch_el, "ORDERNO").text = self.voucher_number
            SubElement(batch_el, "AMOUNT").text = f"{batch.amount:.2f}"
            SubElement(batch_el, "ACTUALQTY").text = batch.actual_qty
            SubElement(batch_el, "BILLEDQTY").text = batch.billed_qty
            
            # Assume self.po_date is in format "1/4/2025" (i.e., dd/mm/yyyy or d/m/yyyy)
            # Parse self.po_date into a datetime object
            po_date_obj = datetime.strptime(self.po_date, "%Y%m%d")

            # Format the due date string (e.g., "1-Apr-25")
            order_due_date = po_date_obj.strftime("%#d-%b-%y")  # Use %-d for Linux/macOS

            # Calculate Julian Date (days since 01-Jan-1900)
            base_date = datetime(1900, 1, 1)
            julian_date = (po_date_obj - base_date).days + 1

            # Create the ORDERDUEDATE XML element with P and JD attributes
            SubElement(batch_el, "ORDERDUEDATE", {"JD": str(julian_date), "P": order_due_date}).text = order_due_date



        # Updated Accounting Allocations with nested Category Allocations
        for alloc in inv_entry.accounting_allocations:
            alloc_el = SubElement(inv_el, "ACCOUNTINGALLOCATIONS.LIST")
            SubElement(alloc_el, "LEDGERNAME").text = alloc.ledgername
            SubElement(alloc_el, "GSTCLASS").text = alloc.gstclass
            SubElement(alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if alloc.is_deemed_positive else "No"
            SubElement(alloc_el, "AMOUNT").text = f"{alloc.amount:.2f}"
            # If there are nested category allocations, add them here
            if alloc.category_allocations:
                for cat_alloc in alloc.category_allocations:
                    cat_alloc_el = SubElement(alloc_el, "CATEGORYALLOCATIONS.LIST")
                    SubElement(cat_alloc_el, "CATEGORY").text = cat_alloc.category
                    SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if cat_alloc.is_deemed_positive else "No"
                    for cc_alloc in cat_alloc.cost_center_allocations:
                        cc_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
                        SubElement(cc_alloc_el, "NAME").text = cc_alloc.name
                        SubElement(cc_alloc_el, "AMOUNT").text = f"{cc_alloc.amount:.2f}"

        # Rate Details (unchanged)
        for rate_detail in inv_entry.rate_details:
            rate_el = SubElement(inv_el, "RATEDETAILS.LIST")
            SubElement(rate_el, "GSTRATEDUTYHEAD").text = rate_detail.gstrate_duty_head
            SubElement(rate_el, "GSTRATEVALUATIONTYPE").text = rate_detail.gstrate_valuation_type
            SubElement(rate_el, "GSTRATE").text = str(rate_detail.gstrate)


    def _add_ledger_entry(self, parent_element: Element, entry: Any) -> None:
        ledger_list_el = SubElement(parent_element, "LEDGERENTRIES.LIST")
        SubElement(ledger_list_el, "LEDGERNAME").text = entry.ledger_name
        SubElement(ledger_list_el, "GSTCLASS").text = entry.gst_class
        SubElement(ledger_list_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_list_el, "LEDGERFROMITEM").text = "No"
        SubElement(ledger_list_el, "REMOVEZEROENTRIES").text = "No"
        SubElement(ledger_list_el, "ISPARTYLEDGER").text = "Yes" if entry.is_party_ledger else "No"
        SubElement(ledger_list_el, "GSTOVERRIDDEN").text = "Yes" if entry.gst_overridden else "No"
        amount_str = f"{entry.amount:.2f}"
        SubElement(ledger_list_el, "AMOUNT").text = amount_str
        SubElement(ledger_list_el, "VATEXPAMOUNT").text = amount_str

        # If there are nested category allocations, add them here
        if hasattr(ledger_list_el, "category_allocations"):
            for cat_alloc in ledger_list_el.category_allocations:
                cat_alloc_el = SubElement(ledger_list_el, "CATEGORYALLOCATIONS.LIST")
                SubElement(cat_alloc_el, "CATEGORY").text = cat_alloc.category
                SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if cat_alloc.is_deemed_positive else "No"
                for cc_alloc in cat_alloc.cost_center_allocations:
                    cc_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
                    SubElement(cc_alloc_el, "NAME").text = cc_alloc.name
                    SubElement(cc_alloc_el, "AMOUNT").text = f"{cc_alloc.amount:.2f}"

    def add_udf_entries(self, voucher: Element) -> None:
        # Example implementation for adding UDF entries.
        base_index = 1010
        for data in self.udf_data:
            udf_agg = SubElement(voucher, "UDF:LINKOFFEREDAGG.LIST", {
                "DESC": "`LinkOfferedAgg`",
                "INDEX": str(base_index)
            })
            link_offered = data.get("LinkOffered")
            if link_offered:
                udf_link = SubElement(udf_agg, "UDF:LINKOFFERED.LIST", {
                    "DESC": "`Link Offered`",
                    "ISLIST": "YES",
                    "TYPE": "String",
                    "INDEX": str(base_index + 1)
                })
                SubElement(udf_link, "UDF:LINKOFFERED", {"DESC": "`Link Offered`"}).text = link_offered
            # Continue with additional UDF fields as needed.


    def to_string(self, pretty: bool = False) -> str:
        envelope = self.to_xml()
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            import xml.dom.minidom
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")
   
   
class TallyGRNVoucherSchema(BaseModel):
    company_info: CompanyInfoSchema
    supplier_details: PartyDetailsSchema  # Supplier information
    buyer_details: PartyDetailsSchema     # Buyer (consignee) information
    voucher_number: str                   # GRN number, e.g., "ZFLB-CIVIL\\24-25\\0001"
    reference_number: str
    cost_center: str                      # e.g., "ZFLB"h
    grn_date: str                         # Date in YYYYMMDD format, e.g., "20250403"
    voucher_type: str                     # e.g., "GRN-ZFLB-CIVIL ::"
    narration:str
    po_number: str                        # Related PO number, e.g., "PO-ZFLB-CIVIL\\24-25\\0002"
    po_date: str                          # PO date, e.g., "20250401"
    inventory_entries: List[InventoryEntrySchema] = Field(default_factory=list)
    ledger_entries: List[LedgerEntrySchema] = Field(default_factory=list)
    basic_duedate_of_pymt: Optional[str] = None

    def to_xml(self) -> Element:
        envelope = Element("ENVELOPE")
        header = SubElement(envelope, "HEADER")
        SubElement(header, "TALLYREQUEST").text = "Import Data"
        body = SubElement(envelope, "BODY")
        import_data = SubElement(body, "IMPORTDATA")
        request_desc = SubElement(import_data, "REQUESTDESC")
        SubElement(request_desc, "REPORTNAME").text = "Vouchers"
        static_vars = SubElement(request_desc, "STATICVARIABLES")
        SubElement(static_vars, "SVCURRENTCOMPANY").text = self.company_info.company_name
        request_data = SubElement(import_data, "REQUESTDATA")
        tally_message = SubElement(request_data, "TALLYMESSAGE", {"xmlns:UDF": "TallyUDF"})
        voucher = SubElement(tally_message, "VOUCHER", {
            "VCHTYPE": self.voucher_type,
            "ACTION": "Create",
            "OBJVIEW": "Invoice Voucher View"
        })

        # Supplier Address
        address_list_el = SubElement(voucher, "ADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.supplier_details.address_list:
            SubElement(address_list_el, "ADDRESS").text = addr_line

        # Buyer Address
        buyer_address_list_el = SubElement(voucher, "BASICBUYERADDRESS.LIST", {"TYPE": "String"})
        for addr_line in self.buyer_details.address_list:
            SubElement(buyer_address_list_el, "BASICBUYERADDRESS").text = addr_line

        # Dates
        SubElement(voucher, "DATE").text = self.grn_date
        SubElement(voucher, "REFERENCEDATE").text = self.grn_date
        SubElement(voucher, "VCHSTATUSDATE").text = self.grn_date
        SubElement(voucher, "EFFECTIVEDATE").text = self.grn_date

        # GST and Party Details
        # SubElement(voucher, "GSTREGISTRATIONTYPE").text = self.supplier_details.gst_registration_type
        # SubElement(voucher, "VATDEALERTYPE").text = self.supplier_details.gst_registration_type
        SubElement(voucher, "STATENAME").text = self.supplier_details.state_name
        SubElement(voucher, "COUNTRYOFRESIDENCE").text = self.supplier_details.country_name
        SubElement(voucher, "PARTYGSTIN").text = self.supplier_details.gst_in
        SubElement(voucher, "PLACEOFSUPPLY").text = self.supplier_details.state_name
        SubElement(voucher, "PARTYNAME").text = self.supplier_details.party_name
        gstreg = SubElement(voucher, "GSTREGISTRATION", {
            "TAXTYPE": "GST",
            "TAXREGISTRATION": self.company_info.gst_in
        })
        gstreg.text = f"{self.buyer_details.state_name} Registration"
        SubElement(voucher, "CMPGSTIN").text = self.company_info.gst_in
        # Narration
        SubElement(voucher, "NARRATION").text = self.narration if self.narration else ""

        # Voucher Details
        SubElement(voucher, "VOUCHERTYPENAME").text = self.voucher_type
        SubElement(voucher, "PARTYLEDGERNAME").text = self.supplier_details.party_name
        SubElement(voucher, "VOUCHERNUMBER").text = self.voucher_number
        SubElement(voucher, "REFERENCE").text = self.reference_number


        # Buyer Details
        SubElement(voucher, "BASICBUYERNAME").text = self.buyer_details.party_name
        SubElement(voucher, "CMPGSTREGISTRATIONTYPE").text = self.company_info.gst_registration_type
        SubElement(voucher, "PARTYMAILINGNAME").text = self.supplier_details.party_name
        SubElement(voucher, "CONSIGNEEGSTIN").text = self.buyer_details.gst_in
        SubElement(voucher, "CONSIGNEEMAILINGNAME").text = self.buyer_details.party_name
        SubElement(voucher, "CONSIGNEEPINCODE").text = self.buyer_details.pin_code or ""
        SubElement(voucher, "CONSIGNEESTATENAME").text = self.buyer_details.state_name
        SubElement(voucher, "CMPGSTSTATE").text = self.buyer_details.state_name
        SubElement(voucher, "CONSIGNEECOUNTRYNAME").text = self.buyer_details.country_name
        SubElement(voucher, "BASICBASEPARTYNAME").text = self.supplier_details.party_name

        # Additional Fields
        SubElement(voucher, "NUMBERINGSTYLE").text = "Auto Retain"
        SubElement(voucher, "FBTPAYMENTTYPE").text = "Default"
        SubElement(voucher, "PERSISTEDVIEW").text = "Invoice Voucher View"
        SubElement(voucher, "VCHSTATUSTAXADJUSTMENT").text = "Default"
        SubElement(voucher, "VCHSTATUSVOUCHERTYPE").text = self.voucher_type
        SubElement(voucher, "VCHSTATUSTAXUNIT").text = f"{self.buyer_details.state_name} Registration"
        SubElement(voucher, "COSTCENTRENAME").text = self.cost_center
        SubElement(voucher, "DIFFACTUALQTY").text = "No"
        SubElement(voucher, "ISOPTIONAL").text = "No"
        SubElement(voucher, "ISINVOICE").text = "No"
        SubElement(voucher, "HASDISCOUNTS").text = "No"
        SubElement(voucher, "ISCOSTCENTRE").text = "Yes"
        SubElement(voucher, "VOUCHERNUMBERSERIES").text = "Default"
        if self.basic_duedate_of_pymt:
            SubElement(voucher, "BASICDUEDATEOFPYMT").text = self.basic_duedate_of_pymt

        # Invoice Order List (PO Reference)
        invoice_order_list = SubElement(voucher, "INVOICEORDERLIST.LIST")
        SubElement(invoice_order_list, "BASICORDERDATE").text = self.po_date
        SubElement(invoice_order_list, "ORDERTYPE").text = "Purchase Order"
        SubElement(invoice_order_list, "BASICPURCHASEORDERNO").text = self.po_number

        # Inventory Entries
        for inv_entry in self.inventory_entries:
            self._add_inventory_entry(voucher, inv_entry, self.voucher_number, self.po_number)

        # Ledger Entries
        for ledger_entry in self.ledger_entries:
            self._add_ledger_entry(voucher, ledger_entry)

        return envelope

    def _add_inventory_entry(self, parent_element: Element, inv_entry: InventoryEntrySchema, grn_number: str, po_number: str) -> None:
        inv_el = SubElement(parent_element, "ALLINVENTORYENTRIES.LIST")

        # To add description
        if inv_entry.description:
            basic_user_desc_list = SubElement(inv_el, "BASICUSERDESCRIPTION.LIST", TYPE="String")
            basic_user_desc = SubElement(basic_user_desc_list, "BASICUSERDESCRIPTION")
            basic_user_desc.text = inv_entry.description
            
        SubElement(inv_el, "STOCKITEMNAME").text = inv_entry.stockitemname
        SubElement(inv_el, "GSTOVRDNTAXABILITY").text = "Taxable"
        SubElement(inv_el, "GSTSOURCETYPE").text = "Stock Item"
        SubElement(inv_el, "GSTITEMSOURCE").text = inv_entry.stockitemname
        SubElement(inv_el, "HSNSOURCETYPE").text = "Stock Item"
        SubElement(inv_el, "HSNITEMSOURCE").text = inv_entry.stockitemname
        SubElement(inv_el, "GSTOVRDNTYPEOFSUPPLY").text = "Goods"
        SubElement(inv_el, "GSTRATEINFERAPPLICABILITY").text = "As per Masters/Company"
        SubElement(inv_el, "GSTHSNNAME").text = inv_entry.gst_hsnname
        SubElement(inv_el, "GSTHSNINFERAPPLICABILITY").text = "As per Masters/Company"
        SubElement(inv_el, "ISDEEMEDPOSITIVE").text = "Yes" if inv_entry.is_deemed_positive else "No"
        SubElement(inv_el, "RATE").text = inv_entry.rate
        SubElement(inv_el, "AMOUNT").text = f"{inv_entry.amount:.2f}"
        SubElement(inv_el, "ACTUALQTY").text = inv_entry.actual_qty
        SubElement(inv_el, "BILLEDQTY").text = inv_entry.billed_qty

        # Batch Allocations
        for batch in inv_entry.batch_allocations:
            batch_el = SubElement(inv_el, "BATCHALLOCATIONS.LIST")
            SubElement(batch_el, "GODOWNNAME").text = batch.godownname
            SubElement(batch_el, "BATCHNAME").text = batch.batchname
            SubElement(batch_el, "ORDERNO").text = po_number
            SubElement(batch_el, "TRACKINGNUMBER").text = batch.tracking_no if batch.tracking_no is not None  else grn_number
            SubElement(batch_el, "AMOUNT").text = f"{batch.amount:.2f}"
            SubElement(batch_el, "ACTUALQTY").text = batch.actual_qty
            SubElement(batch_el, "BILLEDQTY").text = batch.billed_qty
            SubElement(batch_el, "ORDERDUEDATE").text = batch.orderduedate

        # Accounting Allocations
        for alloc in inv_entry.accounting_allocations:
            alloc_el = SubElement(inv_el, "ACCOUNTINGALLOCATIONS.LIST")
            SubElement(alloc_el, "LEDGERNAME").text = alloc.ledgername
            SubElement(alloc_el, "GSTCLASS").text = alloc.gstclass
            SubElement(alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if alloc.is_deemed_positive else "No"
            SubElement(alloc_el, "AMOUNT").text = f"{alloc.amount:.2f}"
            for cat_alloc in alloc.category_allocations:
                cat_alloc_el = SubElement(alloc_el, "CATEGORYALLOCATIONS.LIST")
                SubElement(cat_alloc_el, "CATEGORY").text = cat_alloc.category
                SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if cat_alloc.is_deemed_positive else "No"
                for cc_alloc in cat_alloc.cost_center_allocations:
                    cc_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
                    SubElement(cc_alloc_el, "NAME").text = cc_alloc.name
                    SubElement(cc_alloc_el, "AMOUNT").text = f"{cc_alloc.amount:.2f}"

        # Rate Details
        for rate_detail in inv_entry.rate_details:
            rate_el = SubElement(inv_el, "RATEDETAILS.LIST")
            SubElement(rate_el, "GSTRATEDUTYHEAD").text = rate_detail.gstrate_duty_head
            SubElement(rate_el, "GSTRATEVALUATIONTYPE").text = rate_detail.gstrate_valuation_type
            SubElement(rate_el, "GSTRATE").text = f"{rate_detail.gstrate}"

    def _add_ledger_entry(self, parent_element: Element, entry: LedgerEntrySchema) -> None:
        ledger_list_el = SubElement(parent_element, "LEDGERENTRIES.LIST")
        SubElement(ledger_list_el, "LEDGERNAME").text = entry.ledger_name
        SubElement(ledger_list_el, "GSTCLASS").text = entry.gst_class
        SubElement(ledger_list_el, "ISDEEMEDPOSITIVE").text = "Yes" if entry.is_deemed_positive else "No"
        SubElement(ledger_list_el, "AMOUNT").text = f"{entry.amount:.2f}"
        SubElement(ledger_list_el, "VATEXPAMOUNT").text = f"{entry.amount:.2f}"
        if entry.round_type:
            SubElement(ledger_list_el, "ROUNDTYPE").text = entry.round_type
        if entry.round_limit:
            SubElement(ledger_list_el, "ROUNDLIMIT").text = f"{entry.round_limit}"
        for cat_alloc in entry.category_allocations:
            cat_alloc_el = SubElement(ledger_list_el, "CATEGORYALLOCATIONS.LIST")
            SubElement(cat_alloc_el, "CATEGORY").text = cat_alloc.category
            SubElement(cat_alloc_el, "ISDEEMEDPOSITIVE").text = "Yes" if cat_alloc.is_deemed_positive else "No"
            for cc_alloc in cat_alloc.cost_center_allocations:
                cc_alloc_el = SubElement(cat_alloc_el, "COSTCENTREALLOCATIONS.LIST")
                SubElement(cc_alloc_el, "NAME").text = cc_alloc.name
                SubElement(cc_alloc_el, "AMOUNT").text = f"{cc_alloc.amount:.2f}"

    def to_string(self, pretty: bool = False) -> str:
        envelope = self.to_xml()
        if not pretty:
            return tostring(envelope, encoding="utf-8").decode("utf-8")
        else:
            rough_string = tostring(envelope, encoding="utf-8")
            reparsed = xml.dom.minidom.parseString(rough_string)
            return reparsed.toprettyxml(indent="    ")


   


if __name__ == "__main__":
    # Example usage:
    # company_data = {
    #     "company_name": "Gwalia Sweets Pvt Ltd - (24-25)",
    #     "gst_registration_type": "Regular",
    #     "gst_in": "24AAACG5535F1ZY",
    #     "state_name": "Gujarat",
    #     "country_name": "India"
    # }
    # party_data = {
    #     "party_name": "Bhavya Sales Company",
    #     "address_list": ["19, Jaypunj Complex,", "Nr Master Petrol Pump, Shahpur", "Ahmedabad"],
    #     "gst_in": "24EDQPS8677J1ZF",
    #     "state_name": "Gujarat",
    #     "country_name": "India",
    #     "pin_code": "380001"
    # }
    # consignee_data = {
    #     "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Vastrapur Ahmedabad", "Fssi No. 10713026000232"],
    #     "gst_in": "24AAACG5535F1ZY",
    #     "mailing_name": "Gwalia Sweets Pvt Ltd (2021-22)",
    #     "state_name": "Gujarat",
    #     "pin_code": "380002",
    #     "country_name": "India"
    # }

    # # Sample ledger entries
    # ledger_entries_data = [
    #     {
    #         "ledger_name": "Bhavya Sales Company",
    #         "amount": 91190.00,
    #         "is_deemed_positive": False,  # credit
    #         "is_party_ledger": True
    #     },
    #     {
    #         "ledger_name": "Purchase Packing 18%",
    #         "amount": -77280.00,  # debit
    #         "is_deemed_positive": True,
    #         "cost_center": "Sweet Factory",
    #         "gst_class": "Not Applicable"
    #     },
    #     {
    #         "ledger_name": "Input CGST 9%",
    #         "amount": -6955.20,  # debit
    #         "is_deemed_positive": True,
    #         "cost_center": "Sweet Factory"
    #     },
    #     {
    #         "ledger_name": "Input SGST 9%",
    #         "amount": -6955.20,  # debit
    #         "is_deemed_positive": True,
    #         "cost_center": "Sweet Factory"
    #     },
    #     {
    #         "ledger_name": "Round Off",
    #         "amount": 0.40,  # example round-off
    #         "is_deemed_positive": True,
    #         "cost_center": "Sweet Factory"
    #     }
    # ]

    # # Create instances using Pydantic
    # company_info = CompanyInfoSchema(**company_data)
    # party_details = PartyDetailsSchema(**party_data)
    # consignee_details = ConsigneeDetailsSchema(**consignee_data)
    # ledger_entries = [LedgerEntrySchema(**ld) for ld in ledger_entries_data]

    # # Create the TallyPurchaseVoucher Pydantic object
    # voucher_model = TallyPurchaseVoucherSchema(
    #     company_info=company_info,
    #     party_details=party_details,
    #     consignee_details=consignee_details,
    #     voucher_number="005/24-25",
    #     invoice_date=datetime.date(2025, 3, 1),
    #     invoice_no="005/24-25",
    #     narration="PLASTIC BAG",
    #     ledger_entries=ledger_entries
    # )

    # # Generate the XML string
    # xml_str = voucher_model.to_string(pretty=True)
    # print(xml_str)



# ---------------------------
# Example usage Journal Voucher:

    # Example company info (ensure it matches the desired SVCURRENTCOMPANY)
    # company_info = CompanyInfoSchema(
    #     company_name="FAIRDEAL INTERNATIONAL - 2024-25",
    #     gst_registration_type="Regular",
    #     gst_in="24AAACG5535F1ZY",
    #     state_name="Gujarat",
    #     country_name="India"
    # )

    # # Example ledger entries
    # ledger_entries = [
    #     # Expense Ledger with GST and cost center allocations
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="ICD Expense",
    #         amount=-8330.0,
    #         is_deemed_positive=True,
    #         is_party_ledger=False,
    #         gst_taxability="Taxable",
    #         gst_type_of_supply="Services",
    #         cost_center_category="2024-2025",
    #         cost_center_allocations=[
    #             {"name": "E-03807", "amount": -4165.0},
    #             {"name": "E-03808", "amount": -4165.0},
    #         ]
    #     ),
    #     # GST Ledger Entry: ITC CGST
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="ITC CGST @ 6%",
    #         amount=-499.8,
    #         is_deemed_positive=True,
    #         is_party_ledger=False
    #     ),
    #     # GST Ledger Entry: ITC SGST
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="ITC SGST @ 6%",
    #         amount=-499.8,
    #         is_deemed_positive=True,
    #         is_party_ledger=False
    #     ),
    #     # Round Off Ledger Entry
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="ROUND OFF",
    #         amount=-0.4,
    #         is_deemed_positive=True,
    #         is_party_ledger=False
    #     ),
    #     # Credit Ledger Entry with Bill Allocation
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="PEGASUS INLAND CONTAINER DEPOT PVT. LTD.",
    #         amount=9163.0,
    #         is_deemed_positive=False,
    #         is_party_ledger=True,
    #         bill_allocation={
    #             "name": "2024-25/DD/1030/E-03807 TO E-03808",
    #             "billtype": "New Ref",
    #             "amount": 9163.0
    #         }
    #     ),
    #     # TDS Ledger Entry
    #     LedgerEntrySchemaForJournalVoucher(
    #         ledger_name="TDS ON CONTRACT PAYABLE (FOR COMPANY)",
    #         amount=167,
    #         is_deemed_positive=False,
    #         is_party_ledger=True
    #     )
    # ]

    # # Create a Journal Voucher instance
    # journal_voucher = TallyJournalVoucherSchema(
    #     company_info=company_info,
    #     voucher_number="AV/Purc/31012025-152932",
    #     voucher_date="********",
    #     narration=( "BEING INVOICE NO. 2024-25/DD/1030 DATE 14.10.2024 RCVD AGST JOB NO. "
    #                 "E-03807, E-03808 A/C VE COMMERCIAL VEHICLES LTD AGST CONT. NO. "
    #                 "TCLU1730911/40 ( TDS DED. ON RS. 8,330.00@2%= 167 ) PICD"),
    #     reference="2024-25/DD/1030 dt. 14-Oct-24",
    #     ledger_entries=ledger_entries
    # )

    # # Generate and print the XML (pretty printed)
    # xml_output = journal_voucher.to_string(pretty=True)    
    # with open("journal_voucher.xml", "w", encoding="utf-8") as file:
    #     file.write(xml_output)
    # # print(xml_output)




# -----------------------------------------------------
# Example Usage:
# -----------------------------------------------------

    # Create sample company, seller, and buyer details.
    company = CompanyInfoSchema(company_name="Abhinav Infrabuild Pvt.Ltd.(24-26)")
    seller = PartyDetailsSchema(
        party_name="Shree Steel Trading Corporation",
        address_list=["417, D.M.21/1, Race Course Road , Indore"],
        gst_in="23**********1ZV",
        state_name="Madhya Pradesh",
        country_name="India"
    )
    buyer = PartyDetailsSchema(
        party_name="Abhinav Infrabuild Pvt.Ltd.(24-26)",
        address_list=[
            "207-208 Industry House,A.B.Road Indore(M.P.)",
            "Regd.Office 3,Sanghi Colony,A.B.Road Indore-(M.P.)",
            "E-Mail : <EMAIL>"
        ],
        gst_in="23**********1ZY",
        state_name="Madhya Pradesh",
        country_name="India",
        pin_code="452001"
    )
    
    # (Assume you have populated inventory_entries and ledger_entries as per your models.)
    # For demonstration, we use empty lists.
    po_voucher = TallyPOVoucherSchema(
        company_info=company,
        seller_details=seller,
        buyer_details=buyer,
        voucher_number="PO-ZFLB-CIVIL\\24-25\\0002",
        po_date="20250402",
        voucher_type="AV-Purchase Order",
        consigneecstnumber="23211204978",
        basicduedateofpymt="45 Days",
        buyerpinnumber="**********",
        consigneepinnumber="**********",
        inventory_entries=[

        ],  # Populate with InventoryEntrySchema instances as needed
        ledger_entries=[

        ]      # Populate with LedgerEntrySchema instances as needed
    )
    
    xml_output = po_voucher.to_string(pretty=True)

    strFileName = "PO.xml"
    with open(strFileName, "w") as file:
        file.write(xml_output)

    print(f"File Written Successfully {strFileName}")