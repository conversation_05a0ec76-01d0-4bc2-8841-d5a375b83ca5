from config.db_config import AsyncSessionLocal
from sqlalchemy.future import select
from src.Models.models import Logs  # Make sure to import your UserAPIUsage model
from sqlalchemy.exc import SQLAlchemyError
from fastapi import HTTPException, Query
import inspect

class CLogController:
    # CUserAPIUsageData:
    @staticmethod
    async def MSWriteLog(iUserID, strLogType, strLogMessage):
        """
            Input
                strLogType : Type of Log, "Information" OR "Debug" OR "Warning" OR "Error"
                strLogMessage: Log Message
            Output
                None

            Purpose
                To write logs into database

            Example
                MSWriteLog(objLog)  --> Writes Logs in database
        """
        try:
            objCallingFrame = inspect.currentframe().f_back
            strCallerFunctionName = inspect.getframeinfo(objCallingFrame).function
            iLineNumber = inspect.getframeinfo(objCallingFrame).lineno
            strFileName = objCallingFrame.f_code.co_filename
            
            strFormattedFunctionName = f"{strFileName}   -->   {strCallerFunctionName}   -->   {iLineNumber}"
            async with AsyncSessionLocal() as db:  # Make sure AsyncSessionLocal is correctly imported
                # Convert Pydantic schema to SQLAlchemy model
                objLog = Logs(
                    UserID=iUserID,
                    LogType=strLogType,
                    CallerFunction = strFormattedFunctionName,
                    LogMessage=strLogMessage
                )

                db.add(objLog)
                await db.commit()
                await db.refresh(objLog)

                # Return the inserted data
                return objLog
        except SQLAlchemyError as e:
            await db.rollback()
            raise HTTPException(
                status_code=500, detail="Something Went Wrong , Please try again later!")

        except Exception as e:
            raise HTTPException(
                status_code=e.status_code, detail=e.detail)

    @staticmethod
    async def MSGetAllLogs(iUserID, iLimit: int = Query(10, gt=0), iOffset: int = Query(0, ge=0)) -> list:
        '''
        Purpose : This method is used to retrieve all the logs.

        Inputs  : 
                    1) iLimit: The number of logs to retrieve
                    2) iOffset: The offset from where to start the query

        Output  : list of Logs

        Example : await CUserAPIUsageData.MSGetAllUserApiUsage()
        '''
        async with AsyncSessionLocal() as db:  # Replace with your actual session maker
            try:
                result = await db.execute(select(Logs).offset(iOffset).limit(iLimit))
                lsObjLogs = result.scalars().all()

                if not lsObjLogs:

                    raise HTTPException(
                        status_code=404, detail="No logs found.")

                # Construct the response for each user API usage
                mResponseData = [
                    {
                        "Id": objLog.Id,
                        "LogType": objLog.LogType,
                        "LogMessage": objLog.LogMessage,
                        "CreatedDateTime": objLog.CreatedDateTime.isoformat(),
                        # Add any additional fields as needed
                    }
                    for objLog in lsObjLogs
                ]

                # Convert each dictionary to your schema if needed, or return the list directly
                return mResponseData
            except SQLAlchemyError as e:
                raise HTTPException(
                    status_code=500, detail="A database error prevented retrieving all logs data.")
            except Exception as e:
                raise HTTPException(
                    status_code=500, detail="An unexpected error occurred while processing the request.")
