<!DOCTYPE html>
<html>
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>No Emails Received - Notification</title>
    <style>
        body {{
            font-family: Arial, Helvetica, sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #f9f9f9;
            color: #333333;
        }}

        .container {{
            max-width: 800px;
            margin: 0 auto;
            background-color: #ffffff;
            padding: 30px;
            box-shadow: 0 4px 10px rgba(0,0,0,0.1);
            border-radius: 8px;
        }}

        .header {{
            text-align: center;
            border-bottom: 2px solid #009879;
            padding-bottom: 20px;
            margin-bottom: 20px;
        }}

        .header h2 {{
            color: #009879;
            margin: 10px 0;
            font-size: 24px;
        }}

        .intro {{
            font-size: 16px;
            line-height: 1.6;
            margin-bottom: 20px;
        }}

        .footer {{
            text-align: center;
            font-size: 14px;
            color: #777777;
            border-top: 1px solid #dddddd;
            padding-top: 20px;
        }}

        @media only screen and (max-width: 600px) {{
            .container {{
                padding: 20px;
            }}

            .header h2 {{
                font-size: 20px;
            }}

            .intro, .footer {{
                font-size: 14px;
            }}
        }}
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h2>No Emails Received - Notification</h2>
            <p>Date: {date}</p>
        </div>

        <div class="intro">
            <p>Dear {receiverName},</p>
            <p>We noticed that no emails were received on {prevDate}. Please ensure the Tally posting process was completed successfully, or check if there were any issues that might have caused this situation.</p>
            <p>If this continues, kindly reach out to our support team for further assistance.</p>
        </div>

        <div class="footer">
            <p>Regards,<br>Accuvelocity Team</p>
        </div>
    </div>
</body>
</html>
