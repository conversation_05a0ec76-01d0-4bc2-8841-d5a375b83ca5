# contactus_controller.py
import os
from sqlalchemy.ext.asyncio import AsyncSession
from sqlalchemy.exc import Integrity<PERSON>rror
from datetime import datetime
import pytz
from fastapi import HTTPException
from sqlalchemy.future import select
import traceback
from config.constants import Constants
from src.utilities.S3BucketHelper import CAWSS3Storage

from typing import List, Optional
from fastapi import HTTPException, File,UploadFile

from src.Models.models import ContactUs, QueryCategory
from config.db_config import AsyncSessionLocal
from src.Controllers.Logs_Controller import CLogController
from src.utilities.email_utils import CEmailer
from config.constants import Constants
from dotenv import load_dotenv
import json
load_dotenv()

class ContactUsController:

    @staticmethod
    async def add_contact_us(query_category,
            customer_name,
            email,
            phone_number,
            country,
            designation,
            company_name,
            message,
            status,
            files=Optional[List[UploadFile]]):
        try:
            BugS3ObjectKeysJson = None
            if files:
                if len(files) > 5:
                    raise HTTPException(status_code=400, detail="Upload limit reached. You can upload up to 5 files.")

                file_types = [Constants.allowed_content_types_for_bug.get(file.content_type) for file in files]

                if not all(file_types):
                    raise HTTPException(status_code=400, detail="Invalid file type : Please upload a valid file type")
                
                total_size = sum(file.size for file in files)
                if total_size > Constants.MAXContactUSFileSize:
                    raise HTTPException(status_code=400, detail="Total file size limit exceeded. Please upload files smaller than 50MB in total.")

                # Example to check file type and process each file
                file_keys = []
                for file, file_type in zip(files, file_types):
                    # You can implement file storage logic here
                    file_data = await file.read()
                    file_name = file.filename
                    # For example, saving file to S3
                    S3ObjName = CAWSS3Storage.MSGenerateUniqueObjecID()
                    objCAWSS3Storage = CAWSS3Storage(S3ObjName, bucket_name=os.getenv('s3_bucket_contactus_name'))

                    await objCAWSS3Storage.MSetS3Object(file_data, metadata={"x-amz-meta-filename": str(file_name), "x-amz-meta-file_type":str(file_type)})

                    file_keys.append(S3ObjName)
                
                BugS3ObjectKeysJson = json.dumps(file_keys)

            async with AsyncSessionLocal() as session:
                new_contact_us = ContactUs(
                    query_category=query_category,
                    customer_name=customer_name,
                    email=email,
                    phone_number=phone_number,
                    country=country,
                    designation=designation,
                    company_name=company_name,
                    message=message,
                    s3ObjectKey=BugS3ObjectKeysJson,
                    status=status
                )
                session.add(new_contact_us)
                await session.commit()
                emailer = CEmailer()
                await emailer.send_email(
                    to=Constants.lsContectUsList,
                    template_name='NewContactUsNotification',
                    template_data={
                        'customer_name': new_contact_us.customer_name,
                        'query_category': new_contact_us.query_category,
                        'email': new_contact_us.email,
                        'phone_number': new_contact_us.phone_number,
                        'country': new_contact_us.country,
                        'designation': new_contact_us.designation,
                        'company_name': new_contact_us.company_name,
                        'message': new_contact_us.message,
                        'status': new_contact_us.status
                    }
                )
                return {"message": "Contact Us entry added successfully"}

        except IntegrityError:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=400, detail="An Error Occured while Processing your Request !!!")
        except Exception as e:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail=f"An Error Occured while Processing your Request !!!")

    @staticmethod
    async def get_AllContactUs():
        try:
            async with AsyncSessionLocal() as session:
                result = await session.execute(select(ContactUs))
                contact_us_list = result.scalars().all()
                return contact_us_list

        except Exception as e:
            await CLogController.MSWriteLog(None, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail=f"An Error Occured while getting your Request !!!")
