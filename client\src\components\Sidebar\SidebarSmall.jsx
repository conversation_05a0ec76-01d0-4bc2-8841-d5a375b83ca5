import { Drawer, List, ListItem, ListItemPrefix, IconButton, } from "@material-tailwind/react";
import { Link } from "react-router-dom";
import { useRecoilValue } from "recoil";
import userDataState from "../../context/userData";
import toast from 'react-hot-toast';
import PropTypes from 'prop-types';
import { useLocation } from 'react-router-dom';
import logo from '../../assets/logo.svg';
import { MdHistory, MdLockOpen } from "react-icons/md"; // Assuming you have these icons
import { FaUser } from "react-icons/fa6";
import { IoIosPower } from "react-icons/io";
import { AiOutlineCloudUpload } from "react-icons/ai";
import MyModelImage from '../../assets/IconMyModels.png';

const MiniSidebar = ({ isOpen, toggleSidebar })=> {
    const userData = useRecoilValue(userDataState);
    const isLoggedIn = userData.userData !== null;
    const Version = '0.2.20240419'

    const handleLogout = () => {
        localStorage.removeItem('token');
        toast.success("Logged Out!");
        setTimeout(() => {
            window.location.href = '/';
        }, 500);
    };

    const location = useLocation();

    const isActive = (path) => {
        if (path === '/') {
            return location.pathname === '/';
        }
        return location.pathname.includes(path);
    }

    return (
        <Drawer open={isOpen} position="left" size="min-h-screen">
            <div className="mb-6 flex items-center justify-between relative">
                <Link to="/" className="cursor-pointer font-medium bg-transparent" onClick={toggleSidebar}>
                    <div className="flex items-center justify-center p-2">
                        <img src={logo} alt="Full Logo" className="w-40 mdo:w-48 lgo:w-56 xlo:w-64 h-auto transition-opacity duration-300 opacity-100" />
                    </div>
                </Link>
                <IconButton variant="text" color="blue-gray" onClick={toggleSidebar} aria-label="Close Sidebar">
                    <svg
                        xmlns="http://www.w3.org/2000/svg"
                        fill="none"
                        viewBox="0 0 24 24"
                        strokeWidth={2}
                        stroke="currentColor"
                        className="h-5 w-5"
                    >
                        <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            d="M6 18L18 6M6 6l12 12"
                        />
                    </svg>
                </IconButton>
            </div>
            <hr></hr>
            <List>
                <Link to="/" onClick={toggleSidebar}>
                    <ListItem className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-200'}`}>
                        <ListItemPrefix>
                            <AiOutlineCloudUpload className="h-5 w-5" />
                        </ListItemPrefix>
                        <span>Upload Document</span>
                    </ListItem>
                </Link>
                <Link to="/history" onClick={toggleSidebar}>
                    <ListItem className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/history') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-200'}`}>
                        <ListItemPrefix>
                            <MdHistory className="h-5 w-5" />
                        </ListItemPrefix>
                        <span>My Documents</span>
                    </ListItem>
                </Link>
                <Link to="/profile" onClick={toggleSidebar}>
                    <ListItem className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/profile') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-200'}`}>
                        <ListItemPrefix>
                            <FaUser className="h-5 w-5" />
                        </ListItemPrefix>
                        <span>Profile</span>
                    </ListItem>
                </Link>

                {/* My Models */}
                <Link to="/ModelListPage">
                    <ListItem className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/ModelListPage') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-300'}`}>
                        <ListItemPrefix>
                            <img src={MyModelImage} alt="Modify" className="h-5 w-5" />
                            {/* <VscAdd className="h-4 w-5" /> */}
                        </ListItemPrefix>
                        {isOpen && <span>My Models</span>}
                    </ListItem>
                </Link>

                <Link to="/UpdateUserPassword" onClick={toggleSidebar}>
                    <ListItem className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/UpdateUserPassword') ? 'bg-[#f4f7fe]' : 'hover:bg-gray-200'}`}>
                        <ListItemPrefix>
                            <MdLockOpen className="h-5 w-5" />
                        </ListItemPrefix>
                        <span>Update Password</span>
                    </ListItem>
                </Link>
                {isLoggedIn && (
                    <ListItem onClick={(e) => {
                        e.preventDefault();
                        handleLogout();
                    }} className={`flex items-center transition-all duration-300 text-[#003654] ${isActive('/logout') ? 'bg-red-500' : 'hover:bg-red-500 hover:text-white'}`}>
                        <ListItemPrefix>
                            <IoIosPower className="h-5 w-5" />
                        </ListItemPrefix>
                        <span>Log Out</span>
                    </ListItem>
                )}
            </List>
            <div className="absolute bottom-0 w-full flex justify-center items-center px-4 py-6 shadow-md">
                {isOpen ? (<div className={`flex items-center justify-center text-[#3F3F3F] bg-[#ffff] p-3 rounded-3xl drop-shadow-md ${isOpen ? 'text-sm h-10' : 'text-xs h-6 w-16 ml-2'}`}>
                    <span>Version {Version}</span>
                </div>
                ) : (
                    <span></span>
                )}
            </div>
        </Drawer>
    );
}

MiniSidebar.propTypes = {
    isOpen: PropTypes.bool.isRequired,
    toggleSidebar: PropTypes.func.isRequired
};
export default MiniSidebar;