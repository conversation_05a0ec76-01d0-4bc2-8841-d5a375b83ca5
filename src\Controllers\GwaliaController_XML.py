import json
import sys
sys.path.append("")
import traceback
import re
import os
import pytz
from fastapi import HTTPException
import asyncio
import math
from pydantic import BaseModel, Field, conlist
from typing import List, Optional
import xml.etree.ElementTree as ET
from xml.etree.ElementTree import Element, SubElement, tostring
from datetime import datetime
from src.Schemas.Tally_XML_Schema import CompanyInfoSchema, PartyDetailsSchema, ConsigneeDetailsSchema, LedgerEntrySchema, TallyPurchaseVoucherSchema,CParagTallyPurchaseVoucherSchema, TallyPurchaseInventoryVoucherSchema,AdditionalLedgerInfo
from src.Schemas.Tally_Schemas import ENetworkLocationUpdateMode
from src.utilities.helperFunc import DateHelper, CFileHandler
from src.Controllers.Logs_Controller import CLogController
from src.Controllers.Tally_Controller import CTallyController
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.ParagTradersControllers import BillAllocationSchema, BatchAllocationSchema, RateDetailSchema, AccountingAllocationSchema, InventoryEntrySchema
from decimal import Decimal, ROUND_HALF_UP
from src.Controllers.AVRequestDetailController import CAVRequestDetail
from src.utilities.BussinessIntelligenceHelper import CBusinessIntelligence
from src.utilities.PathHandler import dictProjectPaths
from pathlib import Path

class CGwalia:
    _mStrStoragePath = dictProjectPaths.get("strGwalia_StoragePath", Path(r"H:/AI Data/DailyData/Gwalia"))

    _mDictCompanyData = {
                            "company_name": "Gwalia Sweets Pvt Ltd - (24-25)",
                            "gst_registration_type": "Regular",
                            "gst_in": "24AAACG5535F1ZY",
                            "state_name": "Gujarat",
                            "country_name": "India"
                    }

    _mDictCosigneeData = {
                        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Vastrapur Ahmedabad", "Fssi No. **************"],
                        "gst_in": "24AAACG5535F1ZY",
                        "mailing_name": "Gwalia Sweets Pvt Ltd (2021-22)",
                        "state_name": "Gujarat",
                        "pin_code": "380002",
                        "country_name": "India"
                    }



    @staticmethod
    def MSFormatTaxRate(rate):
        rate = float(rate)
        return int(rate) if rate.is_integer() else rate

    @staticmethod
    async def MGenerateTallyXML(iUserId, iDocId, dictExtractedData, strVendorName, bRaiseError=False,lsUdfData=[{}], strClientREQID = None):
        CGwalia._mIUserId = None
        dictResponse = {
            "AVComments":"",
            "TallyStatus":"",
            "XMLFilePath":"",
            "iSupplierInvoiceNumber":None,
            "TallyAPIResp": None,
            "DocErrorMsg": None,
            "strTracebackLogs": ""
        }
        iSupplierInvoiceNumber = ""
        CGwalia._mIUserId = iUserId
        try:
            await CLogController.MSWriteLog(iUserId, "Info", "Starting XML Generation for ICD.")

            iSupplierInvoiceNumber = dictExtractedData.get("InvoiceNo")
            dictResponse["iSupplierInvoiceNumber"] = iSupplierInvoiceNumber
            # iSupplierInvoiceNumber = CICDController.MSExtractInvoiceNumber(iSupplierInvoiceNumber)
            # TODO: iuserid
            if iSupplierInvoiceNumber is None or not iSupplierInvoiceNumber:
                # We Go Base on Doc ID if Invoice Number is Empty or None
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocID=iDocId)
            else:
                bIsDuplicate = await CTallyController.MSBIsDuplicateXML(iUserId, iDocumentNumber=iSupplierInvoiceNumber)

            if bIsDuplicate:
                dictResponse["TallyStatus"] = "Duplicate"
                dictResponse["AVComments"] = "Tally XML: Duplicate Entry Found in AccuVelocity."
                dictResponse["TallyAPIResp"] = {"status": f"Failed to Create the Tally xml : Tally XML: Duplicate Entry Found in AccuVelocity."},
                dictResponse["DocErrorMsg"] = "AccuVelocity Duplicate Validation Entry Found."
                await CLogController.MSWriteLog(iUserId, "Info", f"Duplicate entry found for invoice numbered '{iSupplierInvoiceNumber}'.")
                # AVRecordDetail -- AVXMLGeneratedStatus, TracebackLogs, strAccuVelocityComments Update
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, ClientImportedXMLStatusCode= 999, ClientImportedXMLResponse="",CImportedXML_Type='NOT_APPLICABLE', AVXMLGeneratedStatus="Duplicate",TracebackLogs= "WARNING - Duplicate Entry Detected in our AccuVelocity Software", strServerEstProcessingTime="NOT_APPLICABLE", EstAccountantTimeSaved="NOT_APPLICABLE", strAccuVelocityComments="Tally XML: Duplicate Entry Found in AccuVelocity.")
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"]
                )
                raise ValueError("Tally XML: Duplicate Entry Found in AccuVelocity.")

            # Get Upload Document Attributes
            objUploadedDocs = await CDocumentData.MSGetDocById(
                                                                user_id=iUserId,
                                                                docId=iDocId,
                                                                isBinaryDataRequired=False
                                                            )
            await CLogController.MSWriteLog(iUserId, "Info", f"Fetched document details for DocId: {iDocId}.")

            try:
                #TODO: please add new vendor
                strXmlData = ""
                if strVendorName.lower() == "sygnia brandworks llp":
                    dictVendorResponse = await CSygnia.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)

                elif strVendorName.lower() == "sheeba dairy":
                    dictVendorResponse = await CSheebaDairy.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "bhavya sales company":
                    dictVendorResponse = await CBhavyaSales.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shri ram enterprises":
                    dictVendorResponse = CShriRamEnterprise.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "the ahmedabad coop dept stories ltd":
                    dictVendorResponse = CAhemdabadCooDept.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "agarwal suppliers":
                    dictVendorResponse = CAgarwalSuppliers.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shree foods":
                    dictVendorResponse = CShreeFoods.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "r k trading company":
                    dictVendorResponse = CRKTradingCompany.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "r k plast (india)":
                    dictVendorResponse = CRKPlastIndia.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "uma converter ltd":
                    dictVendorResponse = CUmaConverter.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "radhe agency":
                    dictVendorResponse = CRadheAgency.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "amar traders":
                    dictVendorResponse = CAmarTraders.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "mahavir international":
                    dictVendorResponse = CMahavirInternational.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "gas guys":
                    dictVendorResponse = CGasGuys.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "zeel pest solution llp":
                    dictVendorResponse = CZeelPestSolutionLlp.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shri arihant sales agency":
                    dictVendorResponse = await CShriArihantSalesAgency.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "diamond private security investigation services":
                    dictVendorResponse = await CDiamondPrivateSecurityInvestigationServices.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "nakoda trading":
                    dictVendorResponse = await CNakodaTrading.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shree dutt krupa lamination":
                    dictVendorResponse = await CShreeDuttkrupaLamination.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shivambica sales corporation":
                    dictVendorResponse = await CShivambicaSalesCorporation.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "sachi products":
                    dictVendorResponse = await CSachiProducts.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "govind tours and travels":
                    dictVendorResponse = await CGovindToursAndTravels.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "i i traders" or strVendorName.lower() == "11 traders":
                    dictVendorResponse = await CIITraders.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "sovereign sales":
                    dictVendorResponse = await CSovereignSales.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "dharm sales company":
                    dictVendorResponse = await CDharmSalesCompany.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "savnath enterprise llp":
                    dictVendorResponse = await CSavnathEnterpriseLLP.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "unicorn enterprise":
                    dictVendorResponse = await CUnicornEnterprise.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "gurukrupa traders":
                    dictVendorResponse = await CGurukrupaTraders.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "grains and more":
                    dictVendorResponse = await CGrainsMore.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "shivay enterprise":
                    dictVendorResponse = await CShivayEnterprise.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "yash tradelink":
                    dictVendorResponse = await CYashTradelink.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "palladium":
                    dictVendorResponse = await CSwiggyDineoutPalladium.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "satyam steel house":
                    dictVendorResponse = await CSatyamSteelHouse.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "karnavati":
                    dictVendorResponse = await CKarnavati.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)


                elif strVendorName.lower() == "regenta m foods":
                    dictVendorResponse = await CRegentaMFoods.MSCreateXML(dictExtractedData=dictExtractedData,lsUdfData=lsUdfData)
                else:
                    await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus="Skipped",TracebackLogs=f"Tally XML Error: Vendor '{strVendorName}' is not supported by AccuVelocity GwaliaController_XML.py File.", strAccuVelocityComments=f"Tally XML Error: Vendor '{strVendorName}' is not supported by AccuVelocity.")
                    raise ValueError(f"Tally XML Error: Vendor '{strVendorName}' is not supported by AccuVelocity.")

                if isinstance(dictVendorResponse, dict):
                    strXmlData = dictVendorResponse.get("XMLData")
                    if dictVendorResponse.get("ErrorMsg"):
                        dictResponse["strTracebackLogs"] = dictVendorResponse.get("ErrorMsg")
                else:
                    strXmlData = dictVendorResponse

                if strXmlData is not None:
                    # AVRequestDetail  EstAccountantTimeSaved, strAccuVelocityComments,AVXMLGeneratedStatus  Update
                    strTimeSaved = CBusinessIntelligence.MSCalcEstimatedTime(vendor_name=strVendorName, no_of_stock_items=0)
                    await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus="Success",TracebackLogs=f"Info - TALLY XML: XMLCodeResponse: {dictVendorResponse} , TallYXMLCreation: {dictResponse}", strAccuVelocityComments="-", EstAccountantTimeSaved = strTimeSaved)
                else:
                    await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus="Skipped",TracebackLogs=f"Info - TALLY XML: XMLCodeResponse: {dictVendorResponse} , TallYXMLCreation: {dictResponse}", strAccuVelocityComments= "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally.")                    
            except ValueError as ve:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document could not be processed automatically due to an unknown issue. Please record it manually in Tally."
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                dictResponse["DocErrorMsg"] = str(ve)
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(ve)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                dictResponse["strTracebackLogs"] = traceback.format_exc()
                await CAVRequestDetail.MSUpdateRecord(iUserId=iUserId, strClientREQID = strClientREQID, DocID=iDocId, AVXMLGeneratedStatus="Skipped",TracebackLogs=dictResponse["strTracebackLogs"], strAccuVelocityComments=dictResponse["AVComments"])
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"]
                )
                raise ve
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}', Error:{str(e)}.")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=dictResponse["iSupplierInvoiceNumber"],
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    resp_date_time=datetime.now(),
                    DocErrorMsg=dictResponse["DocErrorMsg"],
                    strAVComments=dictResponse["AVComments"]
                )
                raise e

            try:
                # Base directory path
                strDownloadDirPath = CGwalia._mStrStoragePath
                today_date = datetime.today().strftime('%Y_%m_%d')

                # Create the full directory path with the date-wise folder
                strDownloadDirPath = os.path.join(strDownloadDirPath, today_date)

                # Ensure the directory exists
                os.makedirs(strDownloadDirPath, exist_ok=True)

                strUploadedDocumentName = os.path.splitext(objUploadedDocs.get('DocName', ''))[0]
                strXMLFileName = f"{strClientREQID}_DID{iDocId}_DName{strUploadedDocumentName}.xml"
                strTodaysXmlFilePath = os.path.join(strDownloadDirPath, strXMLFileName)
                bIsFileWritten = CFileHandler.MSWriteFile(  strFilePath=strTodaysXmlFilePath,
                                                            fileContent=strXmlData,
                                                            strWriteMode="w",
                                                            strEncoding=None)
                if bIsFileWritten:
                    dictResponse["XMLFilePath"] = strTodaysXmlFilePath
                    dictResponse["TallyStatus"] = "Success"
                    dictResponse["AVComments"] = "-"
                    dictResponse["TallyAPIResp"] = {"status": f"Successfully created the tally xml at location: {strTodaysXmlFilePath}."}
                    # AVRecordDetail Update
                    await CAVRequestDetail.MSUpdateNetworkLocation(                          # noqa: N802
                        iUserId=iUserId,
                        dictNewData = {"XMLFilePath": [strTodaysXmlFilePath]},
                        eMode = ENetworkLocationUpdateMode.APPEND,
                        strClientREQID=strClientREQID,
                        docId=iDocId)
                    await CLogController.MSWriteLog(iUserId, "Info", f"Successfully stored xml file at location '{strTodaysXmlFilePath}'.")
                # Update the status of document processing
            except Exception as e:
                dictResponse["TallyStatus"] = "Skipped"
                dictResponse["AVComments"] = "The document couldn't be processed; please enter it in Tally manually."
                dictResponse["DocErrorMsg"] = str(e)
                dictResponse["TallyAPIResp"] = {"status": f"Failed to create xml for invoice numbered '{iSupplierInvoiceNumber}'. "},
                # Update the status of document processing
                await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
                    iDocID = iDocId,
                    iUserID= iUserId,
                    strREQID = strClientREQID,
                    invoice_no=iSupplierInvoiceNumber,
                    av_tally_xml_status=dictResponse["TallyStatus"],
                    tally_api_resp=dictResponse["TallyAPIResp"],
                    DocErrorMsg=dictResponse["DocErrorMsg"] ,
                    resp_date_time=datetime.now(),
                    strAVComments=dictResponse["AVComments"]
                )

                await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document for doc, Error:{str(e)}")
                await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

        except HTTPException as he:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{he}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise he

        except ValueError as ve:
            raise ve

        except Exception as e:
            await CLogController.MSWriteLog(iUserId, "Error", f"Failed to create the xml document, Error:{e}")
            await CLogController.MSWriteLog(iUserId, "Debug", f"Traceback: {str(traceback.format_exc())}")

            raise

            if bRaiseError:
                raise e

        await CTallyController.MSUpdateTallyDocRecordBaseOnREQID(
            iDocID = iDocId,
            iUserID= iUserId,
            strREQID = strClientREQID,
            invoice_no=dictResponse["iSupplierInvoiceNumber"],
            av_tally_xml_status=dictResponse["TallyStatus"],
            tally_api_resp=dictResponse["TallyAPIResp"],
            resp_date_time=datetime.now(),
            DocErrorMsg=dictResponse["DocErrorMsg"],
            strAVComments=dictResponse["AVComments"]
        )
        return dictResponse




class CKarnavati:

    _mStrLedgerName = "Karnavati"
    _mDictPartyData = {
                        "party_name": _mStrLedgerName,
                        "gst_registration_type": "Unregistered/Consumer",
                        "gst_in": "",
                        "state_name": "Gujarat",
                        "country_name": "India"
                    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Karnavati.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # Create Ledger Details
            iTotalAmount = dictExtractedData.get("TotalAmount")
            iVoucherNumber = dictExtractedData.get("InvoiceNo")
            strNarration = CKarnavati.MSGenerateNarration(dictExtractedData["Table"])
            iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate"))
            iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            lsLedgerEntries = [
                                    {
                                        "ledger_name": CKarnavati._mStrLedgerName,
                                        "amount": iTotalAmount,
                                        "is_deemed_positive": False,
                                        "is_party_ledger": True
                                    },
                                    {
                                        "ledger_name": "Purchase Vegetable GST  Exempted",
                                        "amount": -iTotalAmount,
                                        "is_deemed_positive": True,
                                        "is_party_ledger": False
                                    }
                                ]

            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CKarnavati._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # Create the TallyPurchaseVoucher Pydantic object
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=iVoucherNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=iVoucherNumber,
                narration=strNarration,
                voucher_type="AV-Purchase (Credit)",
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData
            )

            # Generate the XML string
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CKarnavati._mStrTracebackLogs = CKarnavati._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CKarnavati._mStrTracebackLogs

        return dictResponse

    @staticmethod
    def MSGenerateNarration(lsLineItems):
        strNarration = "VEGITABLES:\n"
        try:
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = str(dictItemInfo.get("Description", "")).strip()
                strQuantity = str(dictItemInfo.get("Qty", "")).strip()
                strItemRate = str(dictItemInfo.get("Rate", "")).strip()
                strItemAmount = str(dictItemInfo.get("Amount", "")).strip()

                strNarration += f"({iIndex+1}). {strItemName} \n" # |  Rate: {strItemRate}  |  Quantity: {strQuantity}  |  Amount: {strItemAmount}

        except Exception as e:
            print("Error Occurred in Narration :" ,e)

        return strNarration


class CSheebaDairy:

    _mStrLedgerName = "Sheeba Dairy -Tapovan"
    _mDictPartyData = {
                        "party_name": _mStrLedgerName,
                        "address_list": ["Ramayani Kunj,", "B/h.Silver  Clound Hotel,", "Opp.Gandhi Ashram,Ahmedabad"],
                        "gst_registration_type": "Regular",
                        "gst_in": "24AELFS4527F1ZZ",
                        "state_name": "Gujarat",
                        "country_name": "India"
                    }

    _mDictCosigneeData = {
                        "address_list": ["Near Swaminarayan Temple", "Vastrapur Ahmedabad -380015", "Fssi No. **************"],
                        "gst_in": "24AAACG5535F1ZY",
                        "mailing_name": "Gwalia Sweets Pvt Ltd",
                        "state_name": "Gujarat",
                        "pin_code": "380015",
                        "country_name": "India"
                    }
    _mStrTracebackLogs = ""
    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
                                "ledger_name": CSheebaDairy._mStrLedgerName,
                                "amount": 0,
                                "is_deemed_positive": False,
                                "is_party_ledger": True
                                }
        try:
            dictCreditLedgerInfo["amount"] = dictExtractedData.get("TotalInvoiceAmount")
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        lsItemTableDetails = dictExtractedData.get("Table", [])
        lsItemGSTTableDetails = dictExtractedData.get("Table1", [])

        # Create a mapping for quick lookup of CGSTRate based on HSN/SAC
        dictHSNTaxMapping = {entry["HSN/SAC"]: entry["CGSTRate"] * 2 for entry in lsItemGSTTableDetails}

        ledger_details = {}

        for dictItemDetails in lsItemTableDetails:
            strHSNCode = dictItemDetails.get("HSN/SAC")
            iTaxRate = dictHSNTaxMapping.get(strHSNCode)

            # GST Item
            if iTaxRate:
                strLedgerName = f"Purchase Dairy {int(iTaxRate)}%"
            else:
                # GST Exempted Items
                strLedgerName = "Purchase Dairy GST Exempted"

            if strLedgerName in ledger_details:
                ledger_details[strLedgerName]["amount"] += -dictItemDetails.get("Amount")
            else:
                ledger_details[strLedgerName] = {
                    "ledger_name": strLedgerName,
                    "amount": -dictItemDetails.get("Amount"),
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        for key, value in dictExtractedData.items():
            if value and any(tax in key for tax in ("OutputCGSTAmount", "OutputSGSTAmount")):
                gst_type = "CGST" if "CGST" in key else "SGST"
                # try:
                rate = key.split("@")[1]
                rate = rate.rstrip(".0")  # Remove `.0`, but keep decimal values
                # except IndexError:
                #     continue  # Skip if rate extraction fails

                gst_ledger_details.append({
                    "ledger_name": f"Input {gst_type} {rate}%",
                    "amount": -value,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                })

        return gst_ledger_details

    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData["RoundingOFF"]

            if dRoundOff < 0:
                # Negative round-off => Tally interprets it as a "credit"
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)   # e.g. 0.02
                # dictRoundoffLedgerInfo["is_deemed_positive"] = True  # means Tally sees -0.02
            else:
                # Positive round-off => Tally interprets it as a "debit"
                dictRoundoffLedgerInfo["amount"] = -dRoundOff         # e.g. 0.02
            dictRoundoffLedgerInfo["is_deemed_positive"] = True # means Tally sees +0.02

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers



    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        lsLedgersInfo = []


        lsCreditLedgers = CSheebaDairy.MSGetCreditLedgerInfo(dictExtractedData=dictExtractedData)
        lsDebitLedgers = CSheebaDairy.MSGetDebitLedgerInfo(dictExtractedData=dictExtractedData)
        lsGSTLedgerDetails = CSheebaDairy.MSGetGSTLedgerInfo(dictExtractedData=dictExtractedData)
        lsRoundoffLedgers = CSheebaDairy.MSGetRoundOffLedgerInfo(dictExtractedData=dictExtractedData)

        lsLedgersInfo.extend([*lsCreditLedgers, *lsDebitLedgers, *lsGSTLedgerDetails, *lsRoundoffLedgers])

        return lsLedgersInfo


    @staticmethod
    async def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Sheeba Dairy.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # Create Ledger Details
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo"))
            strNarration = "dairy items"     # CSheebaDairy.MSGenerateNarration(dictExtractedData["Table"])
            iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate"))
            iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            lsLedgerEntries = CSheebaDairy.MSGetLedgerInformation(dictExtractedData)

            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSheebaDairy._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSheebaDairy._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # Create the TallyPurchaseVoucher Pydantic object
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                voucher_type="AV-Purchase (Credit)",
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData
            )

            # Generate the XML string
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CSheebaDairy._mStrTracebackLogs = CSheebaDairy._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSheebaDairy._mStrTracebackLogs

        return dictResponse

    # @staticmethod
    # def MSGenerateNarration(lsLineItems):
    #     strNarration = "VEGITABLES:\n"
    #     try:
    #         for iIndex, dictItemInfo in enumerate(lsLineItems):
    #             strItemName = str(dictItemInfo.get("Description", "")).strip()
    #             strQuantity = str(dictItemInfo.get("Qty", "")).strip()
    #             strItemRate = str(dictItemInfo.get("Rate", "")).strip()
    #             strItemAmount = str(dictItemInfo.get("Amount", "")).strip()

    #             strNarration += f"({iIndex+1}). {strItemName}  |  Rate: {strItemRate}  |  Quantity: {strQuantity}  |  Amount: {strItemAmount}\n"

    #     except Exception as e:
    #         print(f"Error in MSGenerateNarration: {str(e)}")

    #     return strNarration


class CBhavyaSales:

    # 1) Basic Party Info for Bhavya Sales
    _mStrLedgerName = "Bhavya Sales Company"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "19, Jaypunj Complex,",
            "Nr Master Petrol Pump, Shahpur",
            "Ahmedabad"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24EDQPS8677J1ZF",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor (Bhavya Sales).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CBhavyaSales._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalChargeable", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """
        lsItemTableDetails = dictExtractedData.get("Table", [])
        freight_charges = dictExtractedData.get("FREIGHT ON SALES", 0)

        ledger_details = {}

        for item in lsItemTableDetails:
            iTaxRate = item.get("GSTRate(In %)", 0)

            if iTaxRate == 0:
                ledger_name = "Purchase GST Exempted"
            else:
                ledger_name = f"Purchase Packing {iTaxRate}%"

            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

            line_amount = item.get("Amount", 0)
            ledger_details[ledger_name]["amount"] += -line_amount

        # Distribute Freight Charges equally if it's not zero
        if freight_charges != 0 and ledger_details:
            num_ledgers = len(ledger_details)
            equal_freight = freight_charges / num_ledgers

            for ledger in ledger_details.values():
                ledger["amount"] += -equal_freight  # Keeping it negative as per existing logic

        return list(ledger_details.values())


    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []
        table_gst = dictExtractedData.get("Table1", [])

        for row in table_gst:
            try:
                cgst_rate = CGwalia.MSFormatTaxRate(row.get("CGSTRate", 0))
                cgst_amount = row.get("CGSTAmount", 0)
                sgst_rate = CGwalia.MSFormatTaxRate(row.get("SGST/UTGSTRate", 0))
                sgst_amount = row.get("SGST/UTGSTAmount", 0)

                if cgst_amount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {cgst_rate}%",
                        "amount": -cgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })
                if sgst_amount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details

    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("Round Off", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CBhavyaSales.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CBhavyaSales.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CBhavyaSales.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CBhavyaSales.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    async def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Bhavya Sales.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CBhavyaSales.MSGenerateNarration(dictExtractedData["Table"])

            # 3) Gather ledger entries
            lsLedgerEntries = CBhavyaSales.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            #    You mentioned you have similar schemas for CompanyInfoSchema, PartyDetailsSchema, etc.
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CBhavyaSales._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                voucher_type="AV-Purchase (Credit)",
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CBhavyaSales._mStrTracebackLogs = CBhavyaSales._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CBhavyaSales._mStrTracebackLogs

        return dictResponse

    @staticmethod
    def MSGenerateNarration(lsLineItems):
        strNarration = ""
        try:
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = str(dictItemInfo.get("DescriptionOfGoods", "")).strip()
                strQuantity = str(dictItemInfo.get("Quantity", "")).strip()
                strItemRate = str(dictItemInfo.get("Rate", "")).strip()
                strItemAmount = str(dictItemInfo.get("Amount", "")).strip()

                strNarration += f"({iIndex+1}). {strItemName} \n" # |  Rate: {strItemRate}  |  Quantity: {strQuantity}  |  Amount: {strItemAmount}

        except Exception as e:
            pass

        return strNarration


class CSygnia:
    """
    A vendor-specific class for SYGNIA BRANDWORKS LLP.
    It follows a similar structure to CBhavyaSales or CSheebaDairy
    but adapts the logic to match Sygnia's data format.
    """

    # Basic Party Info
    _mStrLedgerName = "Sygnia Brandworks Llp"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        # You can add more lines if needed for the address_list
        "address_list": [
            "A BLOCK,A-306, MONDEAL HELGHTS,",
            "S G HIGHWAY,ISCON  CROSS ROAD,",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AEEFS1648L1ZT",
        "state_name": "Gujarat",
        "country_name": "India",
        # pin_code or other fields if your schema allows
        "pin_code": "380015"
    }
    _mDictCosigneeData = {
                        "address_list": ["401-405 Sunrise Mall Mansi Circle", "Near Swaminarayan Temple", "Vastrapur Ahmedabad 380015", "Fssi No. **************"],
                        "gst_in": "24AAACG5535F1ZY",
                        "mailing_name": "Gwalia Sweets Pvt Ltd",
                        "state_name": "Gujarat",
                        "pin_code": "380015",
                        "country_name": "India"
                    }
    _mStrTracebackLogs = ""

    # -----------------------------------------------------------------------
    # 1) CREDIT Ledger (Party Ledger) - Sygnia
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Typically, in a Purchase voucher, the vendor (Sygnia) is credited.
        If your code convention is reversed, keep it consistent with your existing logic
        (e.g. 'is_deemed_positive=False' if that is how your system is set up).
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CSygnia._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # If you follow the same logic as Sheeba/Bhavya
            "is_party_ledger": True
        }
        try:
            # "TotalChargeable" = 16426 in your sample data
            total_invoice_amount = dictExtractedData.get("TotalChargeable", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    # -----------------------------------------------------------------------
    # 2) DEBIT Ledgers (Purchase Lines)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        For Sygnia, we have a single major 'purchase' item with an Amount=13920
        that matches the Table1 TaxableValue=13920.
        We derive total GST rate by CGSTRate + SGST/UTGSTRate.

        Steps:
            1) Read 'Table1' to find CGSTRate, SGSTRate,
                compute totalRate = CGSTRate + SGSTRate.
            2) From 'Table', pick the main line item(s) with an actual quantity/amount
                (e.g. "200ML SYGNIA GALLONS...").
            3) Combine them under a ledger called "Purchase18%"
                (or "Purchase {totalRate}%" dynamically).
        """
        table_items = dictExtractedData.get("Table", [])
        table_tax = dictExtractedData.get("Table1", [])

        # 2A) Figure out the total GST rate from Table1 (CGSTRate + SGST/UTGSTRate).
        total_gst_rate = 0
        if table_tax:
            # Typically, there's just 1 row in your sample
            row = table_tax[0]
            cgst_rate = row.get("CGSTRate", 0)
            sgst_rate = row.get("SGST/UTGSTRate", 0)
            total_gst_rate = cgst_rate + sgst_rate  # e.g. 9 + 9 = 18

        # We'll store everything in a single "Purchase {total_gst_rate}%" ledger
        ledger_name = f"Purchase {int(total_gst_rate)}%"
        ledger_details = {
            ledger_name: {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,  # Keeping with your negative-amount approach
                "is_party_ledger": False
            }
        }

        # 2B) Sum up the real purchase item(s) from Table
        # Usually "200ML SYGNIA GALLONS..." is the real line.
        # We'll skip lines that are obviously CGST OUTPUT, SGST OUTPUT, etc.
        for line in table_items:
            desc = (line.get("DescriptionOfGoods") or "").upper().strip()
            amt = line.get("Amount", 0)

            # Filter out lines that are for CGST OUTPUT, SGST OUTPUT, ROUND OFF, or "Total"
            # We'll rely on your to not treat them as purchases.
            if not desc or "CGST" in desc or "SGST" in desc or "ROUND OFF" in desc or "TOTAL" in desc:
                continue

            # Add negative of the line amt to the ledger
            # (because your code uses is_deemed_positive=True => negative sign).
            ledger_details[ledger_name]["amount"] += -amt

        return list(ledger_details.values())

    # -----------------------------------------------------------------------
    # 3) GST Ledgers (Input CGST/SGST)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        """
        For 'Sygnia', you'll see top-level "OutputCGSTAmount" / "OutputSGSTAmount"
        or details in Table1 => we can break them out by the rates in Table1 as well.

        We'll do the standard approach:
            - Loop over Table1
            - For each row, create an 'Input CGST X%' line, an 'Input SGST X%' line
        """
        gst_ledger_details = []
        table_gst = dictExtractedData.get("Table1", [])

        for row in table_gst:
            try:
                cgst_rate = CGwalia.MSFormatTaxRate(row.get("CGSTRate", 0))
                cgst_amount = row.get("CGSTAmount", 0)
                sgst_rate = CGwalia.MSFormatTaxRate(row.get("SGST/UTGSTRate", 0))
                sgst_amount = row.get("SGST/UTGSTAmount", 0)

                # Ensure rates are either int or float, then format properly
                if isinstance(cgst_rate, float):
                    cgst_rate = int(cgst_rate)
                if isinstance(sgst_rate, float):
                    sgst_rate = int(sgst_rate)

                # If your code uses negative amounts with is_deemed_positive=True
                # to represent a debit, replicate that here:
                if cgst_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {cgst_rate}%",
                        "amount": -cgst_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })
                if sgst_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })
            except Exception as e:
                CSygnia._mStrTracebackLogs  = CSygnia._mStrTracebackLogs + f"Error processing GST ledger : {traceback.format_exc()}"
                raise

        return gst_ledger_details

    # -----------------------------------------------------------------------
    # 4) Round Off Ledger
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        In your sample, there's "RoundingOFF A/c"=0.4 at the top-level,
        or a line in 'Table' describing 'ROUND OFF A/C' with Amount=0.4.

        If you want the top-level key "RoundingOFF A/c", we can fetch it similarly
        to how you did with Sheeba. Or you can parse from Table's
        "ROUND OFF A/C" line.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            # According to your sample, "RoundingOFF A/c": 0.4
            round_off_amount = dictExtractedData.get("RoundingOFF A/c", 0)

            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # We'll replicate your prior approach:
            if round_off_amount < 0:
                dictRoundoffLedgerInfo["amount"] = abs(round_off_amount)
                # keep is_deemed_positive=True => Tally sees negative
            else:
                dictRoundoffLedgerInfo["amount"] = -round_off_amount
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            CSygnia._mStrTracebackLogs  = CSygnia._mStrTracebackLogs + f"Error processing Roundoff ledger : {traceback.format_exc()}"
            raise e

        return lsRoundoffLedgers

    # -----------------------------------------------------------------------
    # 5) Consolidated Ledger Info
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to combine:
            - Party Ledger (credit)
            - Purchase Ledger(s)
            - GST Ledger(s)
            - Round Off
        """
        lsLedgersInfo = []

        lsCreditLedgers   = CSygnia.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers    = CSygnia.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetail = CSygnia.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CSygnia.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetail)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    # -----------------------------------------------------------------------
    # 6) XML Creation
    # -----------------------------------------------------------------------
    @staticmethod
    async def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Sygnia.
        Adjust or rename fields as needed to match your existing pydantic schemas.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Invoice Number & Date
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iExtractedInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Narration
            strNarration = CSygnia.MSGenerateNarration(dictExtractedData["Table"])

            # 3) Gather ledger entries
            lsLedgerEntries = CSygnia.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSygnia._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSygnia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Build Purchase Voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData,
                voucher_type="AV-Purchase (Credit)",
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CSygnia._mStrTracebackLogs  = CSygnia._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSygnia._mStrTracebackLogs

        return dictResponse

    # -----------------------------------------------------------------------
    # 7) Optional Narration
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGenerateNarration(lsLineItems):
        """
        Example of building a textual description from the 'Table' lines,
        ignoring CGST/SGST OUTPUT or Round Off lines if you want.
        """
        strNarration = "SYGNIA Purchase:\n"
        try:
            for iIndex, dictItem in enumerate(lsLineItems):
                desc = str(dictItem.get("DescriptionOfGoods", "")).strip()
                qty = str(dictItem.get("Quantity", "0")).strip()
                rate = str(dictItem.get("Rate", "0")).strip()
                amt = str(dictItem.get("Amount", "0")).strip()

                # Filter out lines that might be filler
                if desc and desc.upper() not in ["CGST OUTPUT", "SGST OUTPUT",  "Less ROUND OFF A/C", "ROUND OFF A/C", "0", "TOTAL"]:
                    strNarration += f"({iIndex+1}). {desc} \n" # | Qty: {qty} | Rate: {rate} | Amount: {amt}
        except Exception as e:
            CSygnia._mStrTracebackLogs  = CSygnia._mStrTracebackLogs + f"Error in MSGenerateNarration: {traceback.format_exc()}"

        return strNarration


class CShriRamEnterprise:
    """
    A vendor-specific class for Shri Ram Enterprise BRANDWORKS LLP.
    It follows a similar structure to CBhavyaSales or CSheebaDairy
    but adapts the logic to match Shri Ram Enterprise's data format.
    """

    # Basic Party Info
    _mStrLedgerName = "Shri Ram Enterprise"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        # You can add more lines if needed for the address_list
        "address_list": [
            "D-304,PUSHKAR HILL - 2",
            "NR.PUSHKAR HILL,NR.GURUDWARA",
            "OPP.ODHAV LAKE,ODHAV",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24BTQPP4511D1ZS",
        "state_name": "Gujarat",
        "country_name": "India",
        # pin_code or other fields if your schema allows
        "pin_code": ""
    }
    _mDictCosigneeData = {
                        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle","Vastrapur Ahmedabad"],
                        "gst_in": "24AAACG5535F1ZY",
                        "mailing_name": "Gwalia Sweets Pvt Ltd (2021-22)",
                        "state_name": "Gujarat",
                        "pin_code": "",
                        "country_name": "India"
                    }
    _mStrTracebackLogs = ""

    # -----------------------------------------------------------------------
    # 1) CREDIT Ledger (Party Ledger) - Shri Ram Enterprise
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Typically, in a Purchase voucher, the vendor (Shri Ram Enterprise) is credited.
        If your code convention is reversed, keep it consistent with your existing logic
        (e.g. 'is_deemed_positive=False' if that is how your system is set up).
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CShriRamEnterprise._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # If you follow the same logic as Sheeba/Bhavya
            "is_party_ledger": True
        }
        try:
            # "TotalAmount" = 1709 in your sample data
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    # -----------------------------------------------------------------------
    # 2) DEBIT Ledgers (Purchase Lines)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        For Shri Ram Enterprise, we have a single major 'purchase' item with an Amount=13920
        that matches the Table1 TaxableValue=13920.
        We derive total GST rate by CGSTRate + SGST/UTGSTRate.

        Steps:
            1) Read 'Table1' to find CGSTRate, SGSTRate,
                compute totalRate = CGSTRate + SGSTRate.
            2) From 'Table', pick the main line item(s) with an actual quantity/amount
                (e.g. "200ML Shri Ram Enterprise GALLONS...").
            3) Combine them under a ledger called "Purchase18%"
                (or "Purchase {totalRate}%" dynamically).
        """

        table_tax = dictExtractedData.get("Taxes", {})


        # Check if MainTaxes is available and contains valid data
        MainTaxes = table_tax.get("MainTaxes", [])
        # Initialize ledgers dictionary
        ledger_details = {}

        # Iterate over MainTaxes to create ledgers for CGST and SGST
        for tax in MainTaxes:
            tax_name = tax["TaxName"]
            tax_rate = tax["TaxRate"]
            taxable_amount = tax["TaxableAmount"]

            # Set ledger name based on the tax type and rate
            if tax_name == "CGST":
                ledger_name = f"Purchase {int(tax_rate * 2)}%"  # For CGST, multiply rate by 2
            elif tax_name == "SGST":
                ledger_name = f"Purchase {int(tax_rate * 2)}%"  # For SGST, multiply rate by 2

            # Initialize ledger entry if not already present
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,  # Keeping with your negative-amount approach
                    "is_party_ledger": False
                }

            # Add taxable amount to the appropriate ledger (exclude same tax type) and divide by 2
            # if tax_name == "CGST":
            #     # For CGST ledger, sum only the CGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)
            # elif tax_name == "SGST":
            #     # For SGST ledger, sum only the SGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)

        # Iterate through the ledger details
        for key, ledger in ledger_details.items():
            # Extract the GST rate from the key
            if 'Purchase' in key:
                gst_rate = int(key.split(' ')[1][:-1])  # Get the rate 12 from 'Purchase 12%'

                # Check if the mainTaxes contain the GST rate * 2
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Update the amount with TaxableAmount
                        break  # Stop once the matching rate is found
        return list(ledger_details.values())

    # -----------------------------------------------------------------------
    # 3) GST Ledgers (Input CGST/SGST)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Here we process the MainTaxes to generate Input CGST and Input SGST ledgers.
        """
        gst_ledger_details = []

        # Extract Taxes details from the response
        taxes = dictExtractedData.get("Taxes", {})

        # Check for MainTaxes and process them
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                # Extract the tax information
                tax_name = tax.get("TaxName", "")
                tax_rate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))

                tax_amount = tax.get("TaxAmount", 0)

                # Ensure tax_rate is properly handled (Convert to integer if it is a whole number)
                if isinstance(tax_rate, float) :
                    tax_rate = int(tax_rate)

                # Create ledger entry for Input CGST and SGST
                if tax_name == "CGST" and tax_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {tax_rate}%",
                        "amount": -tax_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })
                elif tax_name == "SGST" and tax_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {tax_rate}%",
                        "amount": -tax_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details

    # -----------------------------------------------------------------------
    # 4) Round Off Ledger
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        In your sample, there's "RoundingOFF A/c"=0.4 at the top-level,
        or a line in 'Table' describing 'ROUND OFF A/C' with Amount=0.4.

        If you want the top-level key "RoundingOFF A/c", we can fetch it similarly
        to how you did with Sheeba. Or you can parse from Table's
        "ROUND OFF A/C" line.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            # According to your sample, "RoundingOFF A/c": 0.4
            round_off_amount = dictExtractedData.get("RoundingOff", 0)

            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # We'll replicate your prior approach:
            if round_off_amount < 0:
                dictRoundoffLedgerInfo["amount"] = abs(round_off_amount)
                # keep is_deemed_positive=True => Tally sees negative
            else:
                dictRoundoffLedgerInfo["amount"] = -round_off_amount
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    # -----------------------------------------------------------------------
    # 5) Consolidated Ledger Info
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to combine:
            - Party Ledger (credit)
            - Purchase Ledger(s)
            - GST Ledger(s)
            - Round Off
        """
        lsLedgersInfo = []

        lsCreditLedgers   = CShriRamEnterprise.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers    = CShriRamEnterprise.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetail = CShriRamEnterprise.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CShriRamEnterprise.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetail)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    # -----------------------------------------------------------------------
    # 6) XML Creation
    # -----------------------------------------------------------------------
    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shri Ram Enterprise.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Invoice Number & Date
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = dictExtractedData.get("InvoiceDate", "NA")
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Narration
            strNarration = CShriRamEnterprise.MSGenerateNarration(dictExtractedData["ItemTable"])

            # 3) Gather ledger entries
            lsLedgerEntries = CShriRamEnterprise.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShriRamEnterprise._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CShriRamEnterprise._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Build Purchase Voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                udf_data=lsUdfData,
                voucher_type="AV-Purchase (Credit)",
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CShriRamEnterprise._mStrTracebackLogs = CShriRamEnterprise._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShriRamEnterprise._mStrTracebackLogs

        return dictResponse

    # -----------------------------------------------------------------------
    # 7) Optional Narration
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGenerateNarration(lsLineItems):
        """
        Example of building a textual description from the 'Table' lines,
        ignoring CGST/SGST OUTPUT or Round Off lines if you want.
        """
        strNarration = "Shri Ram Enterpise Purchase:\n"
        try:
            for iIndex, dictItem in enumerate(lsLineItems):
                desc = str(dictItem.get("DescriptionOfGoods", "")).strip()
                hsn=str(dictItem.get("HSN/SAC","")).strip()
                gst=str(dictItem.get("GSTRate","0")).strip()
                qty = str(dictItem.get("Quantity", "0")).strip()
                rate = str(dictItem.get("Rate", "0")).strip()
                amt = str(dictItem.get("Amount", "0")).strip()

                # Filter out lines that might be filler
                if desc and desc.upper() not in ["CGST OUTPUT", "SGST OUTPUT",  "Less ROUND OFF A/C", "ROUND OFF A/C", "0", "TOTAL"]:
                    strNarration += f"({iIndex+1}). {desc} \n" # | Qty: {qty} | Rate: {rate} | Amount: {amt}
        except Exception as e:
            print("Error Occur in Narration : ", str(traceback.format_exc()))

        return strNarration


class CAgarwalSuppliers:

    # 1) Basic Party Info for Bhavya Sales
    _mStrLedgerName = "Agarwal Suppliers-Sc"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "",
            "",
            ""
        ],
        "gst_registration_type": "",
        "gst_in": "",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":""
    }
    _mStrTracebackLogs = ""


    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor (Bhavya Sales).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CAgarwalSuppliers._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}

        ledger_name = "Purchase Vegetable GST  Exempted"

        if ledger_name not in ledger_details:
            ledger_details[ledger_name] = {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }

        ledger_details[ledger_name]["amount"] += -dictExtractedData.get("TotalAmount",0)

        return list(ledger_details.values())

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CAgarwalSuppliers.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CAgarwalSuppliers.MSGetDebitLedgerInfo(dictExtractedData)
        # lsGSTLedgerDetails = CBhavyaSales.MSGetGSTLedgerInfo(dictExtractedData)
        # lsRoundoffLedgers = CBhavyaSales.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        # lsLedgersInfo.extend(lsGSTLedgerDetails)
        # lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Agarwal Suppliers.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = str(dictExtractedData.get("InvoiceDate", "NA"))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromYYYYMMDD(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            # strNarration = CBhavyaSales.MSGenerateNarration(dictExtractedData["Table"])

            # 3) Gather ledger entries
            lsLedgerEntries = CAgarwalSuppliers.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            #    You mentioned you have similar schemas for CompanyInfoSchema, PartyDetailsSchema, etc.
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CAgarwalSuppliers._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                # narration=strNarration,
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData,
                voucher_type="AV-Purchase (Credit)",
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CAgarwalSuppliers._mStrTracebackLogs = CAgarwalSuppliers._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CAgarwalSuppliers._mStrTracebackLogs

        return dictResponse


class CAhemdabadCooDept:

    # 1) Basic Party Info for Bhavya Sales
    _mStrLedgerName = "The Ahmedabad Co-Op.Department Stores Ltd."  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "GOVERMENT BULDING",
            "SARADR PARK , LAL DARWAJA",
            "Ahmedabad"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AABAT0757D1ZU",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor (Bhavya Sales).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CAhemdabadCooDept._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}

        ledger_name = "Purchase Gas Gst 18%"

        if ledger_name not in ledger_details:
            ledger_details[ledger_name] = {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }

        ledger_details[ledger_name]["amount"] += -dictExtractedData.get("SubTotal",0)

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CAhemdabadCooDept.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CAhemdabadCooDept.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CAhemdabadCooDept.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CAhemdabadCooDept.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Ahmedabad Co-Op Dept.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            # strNarration = CAhemdabadCooDept.MSGenerateNarration(dictExtractedData["Table"])

            # 3) Gather ledger entries
            lsLedgerEntries = CAhemdabadCooDept.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            #    You mentioned you have similar schemas for CompanyInfoSchema, PartyDetailsSchema, etc.
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CAhemdabadCooDept._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                # narration=strNarration,
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData,
                voucher_type="AV-Purchase (Credit)",
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CAhemdabadCooDept._mStrTracebackLogs = CAhemdabadCooDept._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CAhemdabadCooDept._mStrTracebackLogs

        return dictResponse

    @staticmethod
    def MSGenerateNarration(lsLineItems):
        strNarration = ""
        try:
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = str(dictItemInfo.get("DescriptionOfGoods", "")).strip()
                strQuantity = str(dictItemInfo.get("Quantity", "")).strip()
                strItemRate = str(dictItemInfo.get("Rate", "")).strip()
                strItemAmount = str(dictItemInfo.get("Amount", "")).strip()

                strNarration += f"({iIndex+1}). {strItemName}  \n" # |  Rate: {strItemRate}  |  Quantity: {strQuantity}  |  Amount: {strItemAmount}

        except Exception as e:
            print("Error Occur in Narration : ", str(traceback.format_exc()))

        return strNarration


class CShreeFoods:
    """
    A vendor-specific class for Shri Ram Enterprise BRANDWORKS LLP.
    It follows a similar structure to CBhavyaSales or CSheebaDairy
    but adapts the logic to match Shri Ram Enterprise's data format.
    """

    # Basic Party Info
    _mStrLedgerName = "Shree Foods"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        # You can add more lines if needed for the address_list
        "address_list": [
            "302 KASHI VISHWANATH  FLAT",
            "KANJI DIWAN'S POLE",
            "KALUPUR",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AKMPP3015C1ZK",
        "state_name": "Gujarat",
        "country_name": "India",
        # pin_code or other fields if your schema allows
        "pin_code": ""
    }
    _mDictCosigneeData = {
                        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle","Vastrapur Ahmedabad"],
                        "gst_in": "24AAACG5535F1ZY",
                        "mailing_name": "Gwalia Sweets Pvt Ltd (2021-22)",
                        "state_name": "Gujarat",
                        "pin_code": "",
                        "country_name": "India"
                    }
    _mStrTracebackLogs = ""

    # -----------------------------------------------------------------------
    # 1) CREDIT Ledger (Party Ledger) - Shri Ram Enterprise
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Typically, in a Purchase voucher, the vendor (Shri Ram Enterprise) is credited.
        If your code convention is reversed, keep it consistent with your existing logic
        (e.g. 'is_deemed_positive=False' if that is how your system is set up).
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CShreeFoods._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # If you follow the same logic as Sheeba/Bhavya
            "is_party_ledger": True
        }
        try:
            # "TotalAmount" = 1709 in your sample data
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    # -----------------------------------------------------------------------
    # 2) DEBIT Ledgers (Purchase Lines)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        For Shri Ram Enterprise, we have a single major 'purchase' item with an Amount=13920
        that matches the Table1 TaxableValue=13920.
        We derive total GST rate by CGSTRate + SGST/UTGSTRate.

        Steps:
            1) Read 'Table1' to find CGSTRate, SGSTRate,
                compute totalRate = CGSTRate + SGSTRate.
            2) From 'Table', pick the main line item(s) with an actual quantity/amount
                (e.g. "200ML Shri Ram Enterprise GALLONS...").
            3) Combine them under a ledger called "Purchase18%"
                (or "Purchase {totalRate}%" dynamically).
        """

        table_tax = dictExtractedData.get("Taxes", {})


        # Check if MainTaxes is available and contains valid data
        MainTaxes = table_tax.get("MainTaxes", [])
        # Initialize ledgers dictionary
        ledger_details = {}

        # Iterate over MainTaxes to create ledgers for CGST and SGST
        for tax in MainTaxes:
            tax_name = tax["TaxName"]
            tax_rate = tax["TaxRate"]
            taxable_amount = tax["TaxableAmount"]

            # Set ledger name based on the tax type and rate
            if tax_name == "CGST":
                ledger_name = f"Purchase {int(tax_rate * 2)}%"  # For CGST, multiply rate by 2
            elif tax_name == "SGST":
                ledger_name = f"Purchase {int(tax_rate * 2)}%"  # For SGST, multiply rate by 2

            # Initialize ledger entry if not already present
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,  # Keeping with your negative-amount approach
                    "is_party_ledger": False
                }

            # Add taxable amount to the appropriate ledger (exclude same tax type) and divide by 2
            # if tax_name == "CGST":
            #     # For CGST ledger, sum only the CGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)
            # elif tax_name == "SGST":
            #     # For SGST ledger, sum only the SGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)

        # Iterate through the ledger details
        for key, ledger in ledger_details.items():
            # Extract the GST rate from the key
            if 'Purchase' in key:
                gst_rate = int(key.split(' ')[1][:-1])  # Get the rate 12 from 'Purchase 12%'

                # Check if the mainTaxes contain the GST rate * 2
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Update the amount with TaxableAmount
                        break  # Stop once the matching rate is found
        return list(ledger_details.values())

    # -----------------------------------------------------------------------
    # 3) GST Ledgers (Input CGST/SGST)
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Here we process the MainTaxes to generate Input CGST and Input SGST ledgers.
        """
        gst_ledger_details = []

        # Extract Taxes details from the response
        taxes = dictExtractedData.get("Taxes", {})

        # Check for MainTaxes and process them
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                # Extract the tax information
                tax_name = tax.get("TaxName", "")
                tax_rate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                tax_amount = tax.get("TaxAmount", 0)

                # Ensure tax_rate is properly handled (Convert to integer if it is a whole number)
                if isinstance(tax_rate, float) :
                    tax_rate = int(tax_rate)

                # Create ledger entry for Input CGST and SGST
                if tax_name == "CGST" and tax_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {tax_rate}%",
                        "amount": -tax_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })
                elif tax_name == "SGST" and tax_amount:
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {tax_rate}%",
                        "amount": -tax_amount,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details

    # -----------------------------------------------------------------------
    # 4) Round Off Ledger
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        In your sample, there's "RoundingOFF A/c"=0.4 at the top-level,
        or a line in 'Table' describing 'ROUND OFF A/C' with Amount=0.4.

        If you want the top-level key "RoundingOFF A/c", we can fetch it similarly
        to how you did with Sheeba. Or you can parse from Table's
        "ROUND OFF A/C" line.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            # According to your sample, "RoundingOFF A/c": 0.4
            round_off_amount = dictExtractedData.get("RoundingOff", 0)

            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # We'll replicate your prior approach:
            if round_off_amount < 0:
                dictRoundoffLedgerInfo["amount"] = abs(round_off_amount)
                # keep is_deemed_positive=True => Tally sees negative
            else:
                dictRoundoffLedgerInfo["amount"] = -round_off_amount
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    # -----------------------------------------------------------------------
    # 5) Consolidated Ledger Info
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to combine:
            - Party Ledger (credit)
            - Purchase Ledger(s)
            - GST Ledger(s)
            - Round Off
        """
        lsLedgersInfo = []

        lsCreditLedgers   = CShreeFoods.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers    = CShreeFoods.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetail = CShreeFoods.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CShreeFoods.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetail)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    # -----------------------------------------------------------------------
    # 6) XML Creation
    # -----------------------------------------------------------------------
    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shree Foods.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Invoice Number & Date
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = dictExtractedData.get("InvoiceDate", "NA")
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Narration
            strNarration = CShreeFoods.MSGenerateNarration(dictExtractedData["ItemTable"])

            # 3) Gather ledger entries
            lsLedgerEntries = CShreeFoods.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShreeFoods._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Build Purchase Voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                udf_data=lsUdfData,
                voucher_type="AV-Purchase (Credit)",
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CShreeFoods._mStrTracebackLogs = CShreeFoods._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShreeFoods._mStrTracebackLogs

        return dictResponse

    # -----------------------------------------------------------------------
    # 7) Optional Narration
    # -----------------------------------------------------------------------
    @staticmethod
    def MSGenerateNarration(lsLineItems):
        """
        Example of building a textual description from the 'Table' lines,
        ignoring CGST/SGST OUTPUT or Round Off lines if you want.
        """
        strNarration = "Shree Foods:\n"
        try:
            for iIndex, dictItem in enumerate(lsLineItems):
                desc = str(dictItem.get("DescriptionOfGoods", "")).strip()
                hsn=str(dictItem.get("HSN/SAC","")).strip()
                gst=str(dictItem.get("GSTRate","0")).strip()
                qty = str(dictItem.get("Quantity", "0")).strip()
                rate = str(dictItem.get("Rate", "0")).strip()
                amt = str(dictItem.get("Amount", "0")).strip()

                # Filter out lines that might be filler
                if desc and desc.upper() not in ["CGST OUTPUT", "SGST OUTPUT",  "Less ROUND OFF A/C", "ROUND OFF A/C", "0", "TOTAL"]:
                    strNarration += f"({iIndex+1}). {desc}\n" # | Qty: {qty} | Rate: {rate} | Amount: {amt} --- Discard This Details as Scanned PDF Skewed PDF Received Which can affect accuracy
        except Exception as e:
            print("Error Occur in Narration : ", str(traceback.format_exc()))

        return strNarration


class CRKTradingCompany:

    _mStrLedgerName = "R.K.Trading Company"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "8,1ST FLOOR, ANJNA CHAMBERS,",
            "B/H. ENGLISH CINEMA, I/S. PANCHKUVA GATE",
            "AHMEDABAD-1"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ABTPK9765G1Z0",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor R K Trading Company.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CRKTradingCompany._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        CGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST'), 0)
        SGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST'), 0)
        if CGSTRate and SGSTRate:
            ledger_name = f"Purchase Packing {int(CGSTRate+SGSTRate)}%"
        else:
            ledger_name = f"Purchase Exempted"
        if ledger_name not in ledger_details:
            ledger_details[ledger_name] = {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }

        iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
        ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CRKTradingCompany.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CRKTradingCompany.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CRKTradingCompany.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CRKTradingCompany.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for R.K. Trading Company.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = "PAPER LIFAFA"

            # 3) Gather ledger entries
            lsLedgerEntries = CRKTradingCompany.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            #    You mentioned you have similar schemas for CompanyInfoSchema, PartyDetailsSchema, etc.
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CRKTradingCompany._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]
            lsUdfData=lsUdfData

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Prahlad Nagar",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Accounting Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CRKTradingCompany._mStrTracebackLogs = CRKTradingCompany._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CRKTradingCompany._mStrTracebackLogs

        return dictResponse

class CUmaConverter:

    _mStrLedgerName = "UMA CONVERTER LTD"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "BLOCK NO. 868,",
            "VILLAGE-SANTEJ, NR. CNG PETROL PUMP",
            "SANTEJ ROAD",
            "KALOL"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AAACU4076B1ZQ",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSCleanProductName(strProductName):
            """
            Clean and extract the main product name from a complex input string.

            Args:
                strProductName (str): The raw input string containing product information

            Returns:
                str: The cleaned product name
            """
                # Remove anything after "UCL" including "UCL" itself and subsequent codes/numbers
            strCleanName = re.split(r'UCL', strProductName, 1)[0].strip()

            # Remove any text within parentheses containing numbers
            strCleanName = re.sub(r'\(\s*\d+\s*\)', '', strCleanName).strip()

            # Remove trailing numbers with slashes or dashes
            strCleanName = re.sub(r'\s*[-/]\s*\d+(?:[-/]\d+)*\s*$', '', strCleanName).strip()

            # Remove any remaining codes (sequences of numbers with dashes)
            strCleanName = re.sub(r'\s*/\s*\d+(?:-\d+)+', '', strCleanName).strip()

            # Remove trailing "RS.10/-" or similar price indicators
            strCleanName = re.sub(r'\s*RS\.\d+/?-\s*$', '', strCleanName).strip()

            # Remove "GWALIA SWEETSM (NAMKEEN DIVISOI)" and anything after it
            strCleanName = re.split(r'GWALIA SWEETSM\s*\(NAMKEEN DIVISOI\)', strCleanName)[0].strip()

            # Remove trailing hyphen and any whitespace before it
            strCleanName = re.sub(r'\s*-\s*$', '', strCleanName).strip()
            return strCleanName

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Uma Converters
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CUmaConverter._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        CGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST'), 0)
        SGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST'), 0)
        if CGSTRate and SGSTRate:
            ledger_name = f"Purchase Packing {int(CGSTRate+SGSTRate)}%"
        else:
            ledger_name = f"Purchase Exempted"
        if ledger_name not in ledger_details:
            ledger_details[ledger_name] = {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }

        iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
        ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CUmaConverter.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CUmaConverter.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CUmaConverter.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CUmaConverter.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSGenerateNarration(lsGoodsList):
        strNarration = ""
        try:
            for dictGoodName in lsGoodsList:
                strNarration += CUmaConverter.MSCleanProductName(dictGoodName.get("Product/GoodsName")) + ", "
        except Exception as objException:
            print("Error Occur in Narration : ", str(traceback.format_exc()))


        return strNarration

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Uma Converter.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CUmaConverter.MSGenerateNarration(dictExtractedData.get("Products/GoodsNameList"))

            # 3) Gather ledger entries
            lsLedgerEntries = CUmaConverter.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CUmaConverter._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Namkeen Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CUmaConverter._mStrTracebackLogs = CUmaConverter._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CUmaConverter._mStrTracebackLogs

        return dictResponse

class CRadheAgency:

    _mStrLedgerName = "Radhe Agency"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "AMBAWADI NR. SATYANARAYAN SOCIETY,",
            "SABARMATI,",
            "AHMEDABAD.",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AQVPK2322C1Z4",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Radhe Agency.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CRadheAgency._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        # Extract CGST and SGST rates and taxable amounts
        CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
        SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

        # If no rates, set exempted ledger
        if not CGST_data and not SGST_data:
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
            iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount
        else:
            # Group by tax rates and sum taxable amounts
            tax_groups = {}
            for cgst, sgst in zip(CGST_data, SGST_data):
                if cgst['rate'] == sgst['rate']:  # Ensure CGST and SGST rates match
                    total_rate = cgst['rate'] + sgst['rate']
                    rate_key = int(total_rate) if total_rate else total_rate
                    if rate_key not in tax_groups:
                        tax_groups[rate_key] = 0
                    tax_groups[rate_key] += cgst['taxable']  # Assuming CGST and SGST taxable amounts are same

            # Create ledgers for each tax rate group
            for total_rate, taxable_amount in tax_groups.items():
                ledger_name = f"Purchase {total_rate}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -taxable_amount  # Store taxable amount
                    }
                else:
                    ledger_details[ledger_name]["amount"] = -taxable_amount

        if len(dictExtractedData.get("Charges")) == 1:
            ledger_name = "Transportation Exp."
            if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -dictExtractedData.get("Charges")[0].get("ChargeAmount")  # Store taxable amount
                    }
            else:
                ledger_details[ledger_name]["amount"] = -dictExtractedData.get("Charges")[0].get("ChargeAmount")

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CRadheAgency.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CRadheAgency.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CRadheAgency.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CRadheAgency.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSGenerateNarration(lsGoodsList):
        strNarration = ""
        try:
            for dictGoodName in lsGoodsList:
                strNarration += dictGoodName.get("Product/GoodsName") + ", "
        except Exception as objException:
            print("Error Occur in Narration : ", str(traceback.format_exc()))


        return strNarration

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Radhe Agency.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CRadheAgency.MSGenerateNarration(dictExtractedData.get("Products/GoodsNameList"))

            # 3) Gather ledger entries
            lsLedgerEntries = CRadheAgency.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CRadheAgency._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Mann Cafe",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CRadheAgency._mStrTracebackLogs = CRadheAgency._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CRadheAgency._mStrTracebackLogs

        return dictResponse



class CRKPlastIndia:

    _mStrLedgerName = "R K Plast (India)"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "8,1ST FLOOR, ANJNA CHAMBERS, B/H. ENGLISH CINEMA,",
            "I/S. PANCHKUVA GATE",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ACFPK3074P1ZB",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor R K Plast (India).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CRKPlastIndia._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        CGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST'), 0)
        SGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST'), 0)
        if CGSTRate and SGSTRate:
            ledger_name = f"Purchase Packing {int(CGSTRate+SGSTRate)}%"
        else:
            ledger_name = f"Purchase Exempted"

        # if len(dictExtractedData.get("Charges")):
        #     lsCharges = dictExtractedData.get("Charges")
        #     fCharges = 0
        #     for i in len(lsCharges):
        #         fCharges += dictExtractedData.get("Charges")[i]["ChargeAmount"]

        if ledger_name not in ledger_details:
            ledger_details[ledger_name] = {
                "ledger_name": ledger_name,
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }

        iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
        ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CRKPlastIndia.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CRKPlastIndia.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CRKPlastIndia.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CRKPlastIndia.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for R K Plast (India).
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = "10 LTR SQUARE IML CONTAINER"

            # 3) Gather ledger entries
            lsLedgerEntries = CRKPlastIndia.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CRKPlastIndia._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                voucher_type="AV-Purchase (Credit)",
                cost_center_name="Prahlad Nagar",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                udf_data=lsUdfData,
                voucher_entry_mode = "Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CRKPlastIndia._mStrTracebackLogs = CRKPlastIndia._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CRKPlastIndia._mStrTracebackLogs

        return dictResponse

class CAmarTraders:

    _mStrLedgerName = "Amar Traders"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "J/33 , Sumel Business Park 6,",
            "Opp. Hanumanpura Bus Stand,",
            "Dudheshwar ,",
            "AHMEDABAD - 380014.",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ABOPM5329F1ZL",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Amar Traders.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CAmarTraders._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        # Extract CGST and SGST rates and taxable amounts
        CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
        SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

        # If no rates, set exempted ledger
        if not CGST_data and not SGST_data:
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount":0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
            iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount
        else:
            # Group by tax rates and sum taxable amounts
            tax_groups = {}
            for cgst, sgst in zip(CGST_data, SGST_data):
                if cgst['rate'] == sgst['rate']:  # Ensure CGST and SGST rates match
                    total_rate = cgst['rate'] + sgst['rate']
                    rate_key = int(total_rate) if total_rate else total_rate
                    if rate_key not in tax_groups:
                        tax_groups[rate_key] = 0
                    tax_groups[rate_key] += cgst['taxable']  # Assuming CGST and SGST taxable amounts are same

            # Create ledgers for each tax rate group
            for total_rate, taxable_amount in tax_groups.items():
                ledger_name = f"Purchase {total_rate}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -taxable_amount  # Store taxable amount
                    }
                else:
                    ledger_details[ledger_name]["amount"] = -taxable_amount

            for dictTax in dictExtractedData.get("Taxes").get("MainTaxes"):
                if dictTax.get("TaxName") == "Exempted" and  dictTax.get("TaxableAmount") != 0:
                    taxable_amount =dictTax.get("TaxableAmount")
                    ledger_name = "Purchase Exempted"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                            "amount": -taxable_amount  # Store taxable amount
                        }

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CAmarTraders.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CAmarTraders.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CAmarTraders.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CAmarTraders.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSGenerateNarration(lsGoodsList):
        strNarration = ""
        try:
            for dictGoodName in lsGoodsList:
                strNarration += dictGoodName.get("Product/GoodsName") + ", "
        except Exception as objException:
            print("Error Occur in Narration : ", str(traceback.format_exc()))


        return strNarration

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Amar Traders.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CAmarTraders.MSGenerateNarration(dictExtractedData.get("Products/GoodsNameList"))

            # 3) Gather ledger entries
            lsLedgerEntries = CAmarTraders.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CAmarTraders._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CAmarTraders._mStrTracebackLogs = CAmarTraders._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CAmarTraders._mStrTracebackLogs

        return dictResponse


class CMahavirInternational:

    _mStrLedgerName = "Mahavir International"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "19, Basement, Viral Flat,",
            "Near Vastrapur Civik Center",
            "Vastrapur Ahmedabad",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AAWFM8273B1ZZ",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Mahavir International.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CMahavirInternational._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        # Extract CGST and SGST rates and taxable amounts
        CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
        SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

        # If no rates, set exempted ledger
        # TODO Need to verify the ledger name for 'Exempted' To make sure the name of the ledger is Purchase Exempted as No required data found to verify the Assumption.
        # Assumption name of the ledger is 'Purchase Exempted'
        if not CGST_data and not SGST_data:
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount":0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
            iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount
        else:
            # Group by tax rates and sum taxable amounts
            tax_groups = {}
            for cgst, sgst in zip(CGST_data, SGST_data):
                if cgst['rate'] == sgst['rate']:  # Ensure CGST and SGST rates match
                    total_rate = cgst['rate'] + sgst['rate']
                    rate_key = int(total_rate) if total_rate else total_rate
                    if rate_key not in tax_groups:
                        tax_groups[rate_key] = 0
                    tax_groups[rate_key] += cgst['taxable']  # Assuming CGST and SGST taxable amounts are same

            # Create ledgers for each tax rate group
            for total_rate, taxable_amount in tax_groups.items():
                ledger_name = f"Purchase Packing {total_rate}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -taxable_amount  # Store taxable amount
                    }
                else:
                    ledger_details[ledger_name]["amount"] = -taxable_amount

            for dictTax in dictExtractedData.get("Taxes").get("MainTaxes"):
                if dictTax.get("TaxName") == "Exempted" and  dictTax.get("TaxableAmount") != 0:
                    taxable_amount =dictTax.get("TaxableAmount")
                    ledger_name = "Purchase Exempted"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                            "amount": -taxable_amount  # Store taxable amount
                        }

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CMahavirInternational.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CMahavirInternational.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CMahavirInternational.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CMahavirInternational.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo


    @staticmethod
    def MSCleanProductName(strProductName):
        """
        Clean and extract the main product name from a complex input string.

        Args:
            strProductName (str): The raw input string containing product information

        Returns:
            str: The cleaned product name
        """
            # Remove anything after "UCL" including "UCL" itself and subsequent codes/numbers
        strCleanName = re.split(r'UCL', strProductName, 1)[0].strip()

        # Remove any text within parentheses containing numbers
        strCleanName = re.sub(r'\(\s*\d+\s*\)', '', strCleanName).strip()

        # Remove trailing numbers with slashes or dashes
        strCleanName = re.sub(r'\s*[-/]\s*\d+(?:[-/]\d+)*\s*$', '', strCleanName).strip()

        # Remove any remaining codes (sequences of numbers with dashes)
        strCleanName = re.sub(r'\s*/\s*\d+(?:-\d+)+', '', strCleanName).strip()

        # Remove trailing "RS.10/-" or similar price indicators
        strCleanName = re.sub(r'\s*RS\.\d+/?-\s*$', '', strCleanName).strip()

        # Remove trailing hyphen and any whitespace before it
        strCleanName = re.sub(r'\s*-\s*$', '', strCleanName).strip()
        return strCleanName

    @staticmethod
    def MSGenerateNarration(lsGoodsList):
        strNarration = ""
        try:
            for dictGoodName in lsGoodsList:
                strNarration += CMahavirInternational.MSCleanProductName(dictGoodName.get("Product/GoodsName")) + ", "
        except Exception as objException:
            print("Error Occur in Narration : ", str(traceback.format_exc()))


        return strNarration.upper()

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Mahavir International.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CMahavirInternational.MSGenerateNarration(dictExtractedData.get("Products/GoodsNameList"))

            # 3) Gather ledger entries
            lsLedgerEntries = CMahavirInternational.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CMahavirInternational._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="ODC",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type = "AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Accounting Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CMahavirInternational._mStrTracebackLogs = CMahavirInternational._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CMahavirInternational._mStrTracebackLogs

        return dictResponse


class CGasGuys:
    _mTotal = 0
    _mStrLedgerName = "GAS GUYS"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "SN-415P,BEHIND MAHINDRA CUSTOMER CARE,",
            "CANAL ROAD,",
            "SANATHAL CHANGODAR ROAD,",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ADWPA4587B1ZJ",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor GAS GUYS.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CGasGuys._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        # Extract CGST and SGST rates and taxable amounts
        CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
        SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

        # If no rates, set exempted ledger
        # TODO Need to verify the ledger name for 'Exempted' To make sure the name of the ledger is Purchase Exempted as No required data found to verify the Assumption.
        # Assumption name of the ledger is 'Purchase Exempted'
        if not CGST_data and not SGST_data:
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount":0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
            iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount
        else:
            # Group by tax rates and sum taxable amounts
            tax_groups = {}
            for cgst, sgst in zip(CGST_data, SGST_data):
                if cgst['rate'] == sgst['rate']:  # Ensure CGST and SGST rates match
                    total_rate = cgst['rate'] + sgst['rate']
                    rate_key = int(total_rate) if total_rate else total_rate
                    if rate_key not in tax_groups:
                        tax_groups[rate_key] = 0
                    tax_groups[rate_key] += cgst['taxable']  # Assuming CGST and SGST taxable amounts are same

            # Create ledgers for each tax rate group
            for total_rate, taxable_amount in tax_groups.items():
                ledger_name = f"Purchase Gas Gst {total_rate}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -taxable_amount  # Store taxable amount
                    }
                else:
                    ledger_details[ledger_name]["amount"] = -taxable_amount

            for dictTax in dictExtractedData.get("Taxes").get("MainTaxes"):
                if dictTax.get("TaxName") == "Exempted" and  dictTax.get("TaxableAmount") != 0:
                    taxable_amount =dictTax.get("TaxableAmount")
                    ledger_name = "Purchase Exempted"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                            "amount": -taxable_amount  # Store taxable amount
                        }

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])



        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                # iTaxAmount = tax.get("TaxAmount", 0)

                # Assumption Made that SGST and CGST rate always be 9
                iTaxAmount = round(dictExtractedData.get("Taxes").get("MainTaxes")[0].get("TaxableAmount")*.09,2)


                # try:
                #     # TODO Currently converts .N1 to .N0 (N can be any integer) as Vendors does It is not working on rounding of number i.e Verified by checking other invoices. Change when Solution Found
                #     iTaxAmount = float(f"{int(iTaxAmount*10)/10:.1f}" if str(iTaxAmount).split('.')[1][1] == '1' else str(iTaxAmount))
                #     iTaxAmount = float(f"{int(iTaxAmount*10)/10:.1f}" if str(iTaxAmount).split('.')[1][1] == '1' else str(iTaxAmount))
                # except Exception as objExecption:
                #     iTaxAmount = tax.get("TaxAmount", 0)


                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })
                CGasGuys._mTotal = round(float(dictExtractedData.get("Taxes").get("MainTaxes")[0].get("TaxableAmount") + (iTaxAmount*2)),2)
            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("TotalAmount") - CGasGuys._mTotal
            # dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CGasGuys.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CGasGuys.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CGasGuys.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CGasGuys.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo

    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Gas Guys.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = "LPG CYLINDER ITEM,"

            # 3) Gather ledger entries
            lsLedgerEntries = CGasGuys.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CGasGuys._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type = "AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode = "Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CGasGuys._mStrTracebackLogs = CGasGuys._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CGasGuys._mStrTracebackLogs

        return dictResponse


class CZeelPestSolutionLlp:

    _mStrLedgerName = "Zeel Pest Solution Llp"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "B-132, POPULAR CENTER,, SOMESHWARA COMPLEX,",
            "SHYMAL CROSS ROAD, Ahmedabad",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AACFZ6793K1ZI",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code":"380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Zeel Pest Solution Llp.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        lsCreditLedgers = []
        dictCreditLedgerInfo = {
            "ledger_name": CZeelPestSolutionLlp._mStrLedgerName,
            "amount": 0,
            "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
            "is_party_ledger": True
        }
        try:

            total_invoice_amount = dictExtractedData.get("TotalAmount",0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
        except Exception as e:
            raise

        return lsCreditLedgers

    @staticmethod
    def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """

        ledger_details = {}
        # Extract CGST and SGST rates and taxable amounts
        CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
        SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                    for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

        # If no rates, set exempted ledger
        # TODO Need to verify the ledger name for 'Exempted' To make sure the name of the ledger is Purchase Exempted as No required data found to verify the Assumption.
        # Assumption name of the ledger is 'Purchase Exempted'
        if not CGST_data and not SGST_data:
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount":0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }
            iChargeAmount=dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] -dictExtractedData.get("SubTotal",0) -iChargeAmount
        else:
            # Group by tax rates and sum taxable amounts
            tax_groups = {}
            for cgst, sgst in zip(CGST_data, SGST_data):
                if cgst['rate'] == sgst['rate']:  # Ensure CGST and SGST rates match
                    total_rate = cgst['rate'] + sgst['rate']
                    rate_key = int(total_rate) if total_rate else total_rate
                    if rate_key not in tax_groups:
                        tax_groups[rate_key] = 0
                    tax_groups[rate_key] += cgst['taxable']  # Assuming CGST and SGST taxable amounts are same

            # Create ledgers for each tax rate group
            for total_rate, taxable_amount in tax_groups.items():
                ledger_name = f"Cleaning Exp. GST {total_rate}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False,
                        "amount": -taxable_amount  # Store taxable amount
                    }
                else:
                    ledger_details[ledger_name]["amount"] = -taxable_amount

            for dictTax in dictExtractedData.get("Taxes").get("MainTaxes"):
                if dictTax.get("TaxName") == "Exempted" and  dictTax.get("TaxableAmount") != 0:
                    taxable_amount =dictTax.get("TaxableAmount")
                    ledger_name = "Purchase Exempted"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False,
                            "amount": -taxable_amount  # Store taxable amount
                        }

        return list(ledger_details.values())

    @staticmethod
    def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []

        # Accessing the main taxes from the new response
        taxes = dictExtractedData.get("Taxes", {})
        main_taxes = taxes.get("MainTaxes", [])

        for tax in main_taxes:
            try:
                iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                iTaxAmount = tax.get("TaxAmount", 0)

                # Extract CGST and SGST
                if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                    gst_ledger_details.append({
                        "ledger_name": f"Input CGST {iTaxRate}%",
                        "amount": -iTaxAmount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

                # For SGST
                if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                    sgst_rate = iTaxRate  # Since both CGST and SGST have the same rate
                    sgst_amount = tax.get("TaxAmount", 0)  # Same amount as CGST
                    gst_ledger_details.append({
                        "ledger_name": f"Input SGST {sgst_rate}%",
                        "amount": -sgst_amount,
                        "is_deemed_positive": True,  # Debit
                        "is_party_ledger": False
                    })

            except Exception as e:
                raise

        return gst_ledger_details


    @staticmethod
    def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        lsRoundoffLedgers = []
        dictRoundoffLedgerInfo = {
            "ledger_name": "Round Off",
            "amount": 0,
            "is_deemed_positive": True,
            "is_party_ledger": False
        }
        try:
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
        except Exception as e:
            raise

        return lsRoundoffLedgers

    @staticmethod
    def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        lsLedgersInfo = []

        lsCreditLedgers = CZeelPestSolutionLlp.MSGetCreditLedgerInfo(dictExtractedData)
        lsDebitLedgers = CZeelPestSolutionLlp.MSGetDebitLedgerInfo(dictExtractedData)
        lsGSTLedgerDetails = CZeelPestSolutionLlp.MSGetGSTLedgerInfo(dictExtractedData)
        lsRoundoffLedgers = CZeelPestSolutionLlp.MSGetRoundOffLedgerInfo(dictExtractedData)

        lsLedgersInfo.extend(lsCreditLedgers)
        lsLedgersInfo.extend(lsDebitLedgers)
        lsLedgersInfo.extend(lsGSTLedgerDetails)
        lsLedgersInfo.extend(lsRoundoffLedgers)

        return lsLedgersInfo


    @staticmethod
    def MSGenerateNarration(iInvoiceDate):
        objDate = datetime.strptime(str(iInvoiceDate), "%Y%m%d")
        strMonth = objDate.strftime("%b")  # Abbreviated month (Dec)
        strYear = objDate.strftime("%Y")
        strDate = strMonth+" "+strYear
        strNarration = "General Pest & Rodent Control " + strDate
        return strNarration.upper()



    @staticmethod
    def MSCreateXML(dictExtractedData,lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Zeel Pest Solution Llp.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            # e.g. 220125 => 22-Jan-2025 if your helper function is the same
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # iInvoiceDate = DateHelper.MSConvertIntToDateFromDDMMYYYY(invoice_date=iExtractedInvoiceDate)

            # 2) Typically you might create a narration or keep it simple
            strNarration = CZeelPestSolutionLlp.MSGenerateNarration(iInvoiceDate)

            # 3) Gather ledger entries
            lsLedgerEntries = CZeelPestSolutionLlp.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CZeelPestSolutionLlp._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Lapkamana Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type = "Servies",
                udf_data=lsUdfData,
                voucher_entry_mode = "Accounting Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            CZeelPestSolutionLlp._mStrTracebackLogs = CZeelPestSolutionLlp._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CZeelPestSolutionLlp._mStrTracebackLogs

        return dictResponse


class CShriArihantSalesAgency:

    _mStrLedgerName = "Shri Arihant Sales Agency"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "17-C, HARIPARK SOCIETY,",
            "NR. PAVAN PARTY PLOT,",
            "ANKUR ROAD, NARANPURA",
            "AHMEDABAD",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24APEPS6026B1Z8",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Shri Arihant Sales Agency.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CShriArihantSalesAgency._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }

            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetCreditLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            # Extract CGST and SGST rates and taxable amounts
            CGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                         for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST']
            SGST_data = [{'rate': tax['TaxRate'], 'taxable': tax['TaxableAmount']}
                         for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST']

            # If no rates, set exempted ledger
            if not CGST_data and not SGST_data:
                ledger_name = "Purchase Exempted"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    }
                iChargeAmount = dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
                ledger_details[ledger_name]["amount"] = ledger_details[ledger_name]["amount"] - dictExtractedData.get("SubTotal", 0) - iChargeAmount
            else:
                # Group by tax rates and sum taxable amounts
                tax_groups = {}
                for cgst, sgst in zip(CGST_data, SGST_data):
                    if cgst['rate'] == sgst['rate']:
                        total_rate = cgst['rate'] + sgst['rate']
                        rate_key = int(total_rate) if total_rate else total_rate
                        if rate_key not in tax_groups:
                            tax_groups[rate_key] = 0
                        tax_groups[rate_key] += cgst['taxable']

                 # Create ledgers for each tax rate group
                for total_rate, taxable_amount in tax_groups.items():
                    ledger_name = f"Purchase {total_rate}%"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": -taxable_amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        }
                    else:
                        ledger_details[ledger_name]["amount"] = -taxable_amount # Store taxable amount

                for dictTax in dictExtractedData.get("Taxes").get("MainTaxes"):
                    if dictTax.get("TaxName") == "Exempted" and dictTax.get("TaxableAmount") != 0:
                        taxable_amount = dictTax.get("TaxableAmount")
                        ledger_name = "Purchase Exempted"
                        if ledger_name not in ledger_details:
                            ledger_details[ledger_name] = {
                                "ledger_name": ledger_name,
                                "amount": -taxable_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            }

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
             # Accessing the main taxes from the new response
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                    iTaxAmount = tax.get("TaxAmount", 0)
                     # Extract CGST and SGST
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                    # For SGST
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {iTaxRate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")

            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
             # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                 # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []

            lsCreditLedgers = await CShriArihantSalesAgency.MSGetCreditLedgerInfo(dictExtractedData)
            lsDebitLedgers = await CShriArihantSalesAgency.MSGetDebitLedgerInfo(dictExtractedData)
            lsGSTLedgerDetails = await CShriArihantSalesAgency.MSGetGSTLedgerInfo(dictExtractedData)
            lsRoundoffLedgers = await CShriArihantSalesAgency.MSGetRoundOffLedgerInfo(dictExtractedData)

            lsLedgersInfo.extend(lsCreditLedgers)
            lsLedgersInfo.extend(lsDebitLedgers)
            lsLedgersInfo.extend(lsGSTLedgerDetails)
            lsLedgersInfo.extend(lsRoundoffLedgers)

            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for dictGoodName in lsGoodsList:
                strNarration += dictGoodName.get("Product/GoodsName") + ", "
            return strNarration
        except Exception as objException:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {objException}")
            return ""

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shri Arihant Sales Agency.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Shri Arihant Sales Agency...")
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # 2) Typically you might create a narration or keep it simple
            strNarration = await CShriArihantSalesAgency.MSGenerateNarration(dictExtractedData.get("Products/GoodsNameList"))
            # 3) Gather ledger entries
            lsLedgerEntries = await CShriArihantSalesAgency.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShriArihantSalesAgency._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)

            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            # 6) Generate the XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CShriArihantSalesAgency._mStrTracebackLogs = CShriArihantSalesAgency._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShriArihantSalesAgency._mStrTracebackLogs

        return dictResponse


class CDiamondPrivateSecurityInvestigationServices:

    _mStrLedgerName = "Diamond Private Security & Investigation Services"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "GULBAI TEKRA,",
            "AHMEDABAD",
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AGAPC2939A1Z7",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mTDSAmount = 0
    _mTDSRate = 0.02
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor Diamond Private Security & Investigation Services.
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CDiamondPrivateSecurityInvestigationServices._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
                "is_party_ledger": True
            }

            CDiamondPrivateSecurityInvestigationServices._mTDSAmount = round(dictExtractedData.get("TotalAmount", 0) * CDiamondPrivateSecurityInvestigationServices._mTDSRate)
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0) - CDiamondPrivateSecurityInvestigationServices._mTDSAmount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetCreditLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            ledger_name = "Security Exp - RCM"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

            ledger_details[ledger_name]["amount"] = -dictExtractedData.get("TotalAmount", 0)

            try:
                strMonth, strYear = dictExtractedData.get("MonthAndYearOfBill").split('-')
            except ValueError:
                return "Invalid input format. Use 'Month-YY' or 'Month-YYYY'"

            iYear = int(strYear)
            if iYear < 100:
                iYear += 2000
            iNextYear = iYear + 1 - 2000
            strYear = f"{iYear}-{iNextYear}"

            ledger_name = "TDS on Contractor FY " + strYear
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

            ledger_details[ledger_name]["amount"] = CDiamondPrivateSecurityInvestigationServices._mTDSAmount
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            # Accessing the main taxes from the new response
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                    iTaxAmount = tax.get("TaxAmount", 0)

                    # Extract CGST and SGST
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                     # For SGST
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        sgst_rate = iTaxRate
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {sgst_rate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")

            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Builds a line item for round-off adjustments, if any.
        For zero, it simply won't affect anything.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
             # If it's negative => Tally sees it as credit if is_deemed_positive=True
            # If it's positive => Tally sees it as debit if is_deemed_positive=False
            # For now, replicate the same logic from Sheeba.
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
                # keep is_deemed_positive=True => Tally sees -0.XX
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
                # is_deemed_positive=True => Tally sees negative for +0.XX

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """
        Wrapper to collect all ledger lines (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []

            lsCreditLedgers = await CDiamondPrivateSecurityInvestigationServices.MSGetCreditLedgerInfo(dictExtractedData)
            lsDebitLedgers = await CDiamondPrivateSecurityInvestigationServices.MSGetDebitLedgerInfo(dictExtractedData)
            lsGSTLedgerDetails = await CDiamondPrivateSecurityInvestigationServices.MSGetGSTLedgerInfo(dictExtractedData)
            lsRoundoffLedgers = await CDiamondPrivateSecurityInvestigationServices.MSGetRoundOffLedgerInfo(dictExtractedData)

            lsLedgersInfo.extend(lsCreditLedgers)
            lsLedgersInfo.extend(lsDebitLedgers)
            lsLedgersInfo.extend(lsGSTLedgerDetails)
            lsLedgersInfo.extend(lsRoundoffLedgers)

            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSGenerateNarration(strDate):
        # Split the input string into month and year parts
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strMonth, strYear = strDate.split('-')
            iYear = int(strYear)
            strDate = strMonth[0:3] + " " + str(iYear)
            if iYear < 100:
                iYear += 2000
            strNarration = "month of " + strDate
            return strNarration.upper()
        except ValueError:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error","Error Invalid Date")
            return ""


    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Diamond Private Security & Investigation Services.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Diamond Private Security...")
            # 1) Basic invoice info
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # 2) Typically you might create a narration or keep it simple
            strNarration = await CDiamondPrivateSecurityInvestigationServices.MSGenerateNarration(dictExtractedData.get("MonthAndYearOfBill"))
            # 3) Gather ledger entries
            lsLedgerEntries = await CDiamondPrivateSecurityInvestigationServices.MSGetLedgerInformation(dictExtractedData)

            # 4) Construct Tally schema objects (Pydantic or otherwise)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CDiamondPrivateSecurityInvestigationServices._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            # Convert each ledger dict to LedgerEntrySchema
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # 5) Now build your TallyPurchaseVoucherSchema
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=f"{strInvoiceNumber}",
                cost_center_name="Prahlad Nagar",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Servies",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CDiamondPrivateSecurityInvestigationServices._mStrTracebackLogs = CDiamondPrivateSecurityInvestigationServices._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CDiamondPrivateSecurityInvestigationServices._mStrTracebackLogs

        return dictResponse

class CAdaniTotalGas:
    _mstrPartyName = "Adani Total Gas Ltd"
    _mstrPurchaseLedgerName = "Purchase Gas GST Exempted"
    _mstrVoucherType = "Purchase"
    _mstrPartyState = "Gujarat"
    _mstrPartyGSTIN = "24AAFCA3788D1ZS"
    _mlsTotalStockItems = []       # To keep track of all the stock items from the invoice
    _mlsNonExistingStockItems = []      # Used to identify non existing stock items from the invoice
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetTallyXML(dictExtractedData, lsUDFData = [{}]):
        """
        Build the Tally Purchase Inventory Voucher XML for Adani Total Gas.
        Returns a dictionary with "XMLData", "ErrorMsg", "xmlContent", "allStockItems", and "nonExistingStockItems" keys.
        """
        dictResponse = {
            "XMLData": None,
            "ErrorMsg": None,
            "xmlContent": "",
            "allStockItems": [],
            "nonExistingStockItems": []
        }

        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetTallyXML for Adani Total Gas...")

            # Initialize class variable with empty list
            CAdaniTotalGas._mlsTotalStockItems = []
            CAdaniTotalGas._mlsNonExistingStockItems = []

            # Create sample instances (populate with your actual data)
            company = CompanyInfoSchema(company_name=CGwalia._mDictCompanyData.get("company_name"))

            party = PartyDetailsSchema(
                party_name=CAdaniTotalGas._mstrPartyName,                    # Seller Name
                address_list=CGwalia._mDictCosigneeData.get("address_list"),    # Buyer Address
            )

            consignee = ConsigneeDetailsSchema(
                address_list=CGwalia._mDictCosigneeData.get("address_list"),
                gst_in=CGwalia._mDictCosigneeData.get("gst_in"),
                mailing_name=CGwalia._mDictCosigneeData.get("mailing_name"),
                state_name=CGwalia._mDictCosigneeData.get("state_name"),
                pin_code=CGwalia._mDictCosigneeData.get("pin_code")
            )

            accounting_alloc = AccountingAllocationSchema(
                ledgername=CAdaniTotalGas._mstrPurchaseLedgerName,
                amount=0
            )

            fQuantity = dictExtractedData.get("ItemTable")[0].get("Qty")
            iQuantity = round(fQuantity)
            iItemAmount = round(dictExtractedData.get("TotalAmount"))
            iItemRate = iItemAmount / iQuantity
            if fQuantity == 0:
                iQuantity = 1
            strQuantity = str(iQuantity)

            batch_alloc = BatchAllocationSchema(
                godownname="Main Location",
                batchname="Primary Batch",
                destinationgodownname="Main Location",
                amount=0, # Currently static
                actual_qty=strQuantity,
                billed_qty=strQuantity
            )

            rate_details = [
                RateDetailSchema(gstrate_duty_head="CGST", gstrate=0),
                RateDetailSchema(gstrate_duty_head="SGST/UTGST", gstrate=0),
                RateDetailSchema(gstrate_duty_head="IGST", gstrate=0)    # Here we can specify iIGSTRate for dynamic tax rate
            ]

            inv_entry = InventoryEntrySchema(
                stockitemname="Gas-Adani",
                gstledgersource=CAdaniTotalGas._mstrPurchaseLedgerName,
                hsnitemsource="Ledger",
                gst_ovrd_ineligible_itc = "Applicable",
                gst_ovrd_stored_nature = "",

                gst_hsnname="",
                rate=str(iItemRate),
                discount=0,
                amount=iItemAmount, # Must be negative value
                actual_qty=strQuantity,
                billed_qty=strQuantity,
                batch_allocations=[batch_alloc],
                accounting_allocations=[accounting_alloc],
                rate_details=rate_details
            )

            lsStockItemInfo = [inv_entry]

            # Party Ledger Info
            fTotalAmount = iItemAmount
            partyBillAllocation = BillAllocationSchema(name=dictExtractedData.get("InvoiceNo"), billtype="New Ref", amount=fTotalAmount)
            party_ledger_entry = LedgerEntrySchema(
                ledger_name=CAdaniTotalGas._mstrPartyName,
                amount=fTotalAmount,
                is_deemed_positive=False,
                is_party_ledger=True,
                gst_overridden=False,
                bill_allocation=partyBillAllocation
            )

            voucher = TallyPurchaseInventoryVoucherSchema(
                company_info=company,
                voucher_class="Default Voucher",
                party_details=party,
                consignee_details=consignee,
                voucher_number= datetime.now().strftime("AV/Purc/%d%m%Y-%H%M%S"),
                invoice_date= dictExtractedData.get("InvoiceDate"),
                invoice_no=dictExtractedData.get("InvoiceNo"),
                voucher_type=CAdaniTotalGas._mstrVoucherType,
                # ledger_entries=[party_ledger_entry, gst_entry, roundoff_entry],
                ledger_entries=[party_ledger_entry],
                inventory_entries=lsStockItemInfo,
                udf_data=lsUDFData
            )

            strXMLContent = voucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created Tally XML in MSGetTallyXML.")

            # Set the response values
            dictResponse["XMLData"] = strXMLContent
            dictResponse["xmlContent"] = strXMLContent
            dictResponse["allStockItems"] = CAdaniTotalGas._mlsTotalStockItems
            dictResponse["nonExistingStockItems"] = CAdaniTotalGas._mlsNonExistingStockItems

        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetTallyXML Error: {e}")
            CAdaniTotalGas._mStrTracebackLogs = CAdaniTotalGas._mStrTracebackLogs + f"Error in MSGetTallyXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CAdaniTotalGas._mStrTracebackLogs

        return dictResponse

#Purchase with Inventory incomplete
class CRegentaMFoods:

    _mStrLedgerName = "Regenta M Foods-New"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [],
        "gst_registration_type": "Regular",
        "gst_in": "24ABGFR6390B1ZA",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Sunrise Mall Mansi Circle", "Near Swaminarayan Temple", "Vastrapur Ahmedabad -380015", "Fssi No. 10017021002884"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor (Regenta M Foods-New).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).

        But if you're matching the exact approach from 'CNakodaTrading',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CRegentaMFoods._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }

            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        For Shri Ram Enterprise, we have a single major 'purchase' item with an Amount=13920
        that matches the Table1 TaxableValue=13920.
        We derive total GST rate by CGSTRate + SGST/UTGSTRate.

        Steps:
            1) Read 'Table1' to find CGSTRate, SGSTRate,
                compute totalRate = CGSTRate + SGSTRate.
            2) From 'Table', pick the main line item(s) with an actual quantity/amount
                (e.g. "200ML Shri Ram Enterprise GALLONS...").
            3) Combine them under a ledger called "Purchase18%"
                (or "Purchase {totalRate}%" dynamically).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                taxable_amount = tax["TaxableAmount"]

                if tax_name == "CGST":
                    ledger_name = f"Purchase {int(tax_rate * 2)}%"
                elif tax_name == "SGST":
                    ledger_name = f"Purchase {int(tax_rate * 2)}%"

                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    }
              # Add taxable amount to the appropriate ledger (exclude same tax type) and divide by 2
            # if tax_name == "CGST":
            #     # For CGST ledger, sum only the CGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)
            # elif tax_name == "SGST":
            #     # For SGST ledger, sum only the SGST taxable amount, divide by 2 and round
            #     ledger_details[ledger_name]["amount"] += round(-taxable_amount / 2, 2)
            for key, ledger in ledger_details.items():
                if 'Purchase' in key:
                    gst_rate = int(key.split(' ')[1][:-1])
                    for tax in MainTaxes:
                        if tax['TaxRate'] == gst_rate / 2:
                            ledger['amount'] = -tax['TaxableAmount']
                            break

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)

                    if isinstance(tax_rate, float):
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and tax_amount:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {tax_rate}%",
                            "amount": -tax_amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                    elif tax_name == "SGST" and tax_amount:
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {tax_rate}%",
                            "amount": -tax_amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")

            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff

            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []

            lsCreditLedgers =await  CRegentaMFoods.MSGetCreditLedgerInfo(dictExtractedData)
            lsDebitLedgers = await CRegentaMFoods.MSGetDebitLedgerInfo(dictExtractedData)
            lsGSTLedgerDetails = await CRegentaMFoods.MSGetGSTLedgerInfo(dictExtractedData)
            lsRoundoffLedgers =await  CRegentaMFoods.MSGetRoundOffLedgerInfo(dictExtractedData)

            lsLedgersInfo.extend(lsCreditLedgers)
            lsLedgersInfo.extend(lsDebitLedgers)
            lsLedgersInfo.extend(lsGSTLedgerDetails)
            lsLedgersInfo.extend(lsRoundoffLedgers)

            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Regenta M Foods.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Regenta M Foods...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CRegentaMFoods.MSGenerateNarration(dictExtractedData["ItemTable"])
            lsLedgerEntries =await  CRegentaMFoods.MSGetLedgerInformation(dictExtractedData)

            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CRegentaMFoods._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CRegentaMFoods._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = objPurchaseVoucher.to_string(pretty=True)
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CRegentaMFoods._mStrTracebackLogs = CRegentaMFoods._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CRegentaMFoods._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsLineItems):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = str(dictItemInfo.get("DescriptionOfGoods", "")).strip()
                strQuantity = str(dictItemInfo.get("Quantity", "")).strip()
                strNarration += f"({iIndex+1}). {strItemName} \n" # |  Quantity: {strQuantity}
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

class CNakodaTrading:

    _mStrLedgerName = "Nakoda Trading"  # This is your ledger name in Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "51, BHADRESHWAR SOCIETY, B/H. H.B. KAPADIA SCHOOL",
            "O/S. DELHI DARWAJA, SHAHIBAUG ROAD,",
            "AHMEDABAD -380004"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24BTDPS5333R1Z3",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """
        Returns the ledger entry for the vendor (Nakoda Trading).
        In a typical Purchase Voucher, the vendor is credited.
        is_deemed_positive=False means Tally interprets it as a Debit (which is unusual for a purchase).
        But if you're matching the exact approach from 'CSheebaDairy',
        keep the same sign logic. Otherwise, invert it if needed.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CNakodaTrading._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # 'False' => Tally sees it as Debit
                "is_party_ledger": True
            }

            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Creates separate purchase ledgers for each line item in 'Table'
        based on the GSTRate(In %), ensuring that items with the same HSN
        but different GST rates are not lumped into a single ledger.
        Additionally, distributes 'FREIGHT ON SALES' equally among all ledgers.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            CGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST'), 0)
            SGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST'), 0)
            if CGSTRate and SGSTRate:
                ledger_name = f"Purchase {int(CGSTRate + SGSTRate)}%"
            else:
                ledger_name = f"Purchase Exempted"

            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

            # Calculate amount including freight charges
            iChargeAmount = dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = -(dictExtractedData.get("SubTotal", 0) + iChargeAmount)  # Negative for debit

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (CGST/SGST) for tax amounts
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    iTaxAmount = tax.get("TaxAmount", 0)

                    # CGST entry
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                    # SGST entry
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {iTaxRate}%",
                            "amount": -iTaxAmount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CNakodaTrading.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CNakodaTrading.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CNakodaTrading.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CNakodaTrading.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Nakoda Trading.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Nakoda Trading...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CNakodaTrading.MSGenerateNarration(dictExtractedData["ItemTable"])  # Build narration
            lsLedgerEntries = await CNakodaTrading.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CNakodaTrading._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = objPurchaseVoucher.to_string(pretty=True)
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CNakodaTrading._mStrTracebackLogs = CNakodaTrading._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CNakodaTrading._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsLineItems):
        # Creates a narration string from item descriptions
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsLineItems):
                product_name = dictGoodName.get("DescriptionOfGoods", "")
                strNarration += product_name + ("" if iIndex == len(lsLineItems) - 1 else ", ")
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CShreeDuttkrupaLamination:

    _mStrLedgerName = "Shree Duttkrupa Lamination"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "D/1/C, DIAMOND PARK",
            "NR. MARUTI SUZUKI SHOW ROOM",
            "N.H. HIGHWAY, HIMATNAGAR ROAD,",
            "NARODA, AHMEDABAD - 382 330"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ACBFS3788G1ZY",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for Shree Duttkrupa Lamination in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CShreeDuttkrupaLamination._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledgers for purchase items based on GST rates from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])  # Extract MainTaxes
            ledger_details = {}

            # Process each tax entry for CGST/SGST
            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                taxable_amount = tax["TaxableAmount"]

                # Set ledger name based on combined GST rate
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Purchase Packing {int(tax_rate * 2)}%"  # Double rate for total GST
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,  # True means Tally treats as Debit
                        "is_party_ledger": False
                    }

            # Assign taxable amounts to ledgers
            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[2][:-1])  # Extract rate from ledger name
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Negative for debit
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CShreeDuttkrupaLamination.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    tax_amount = tax.get("TaxAmount", 0)

                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float):
                        tax_rate = int(tax_rate)

                    # Add CGST entry
                    if tax_name == "CGST" and tax_amount:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {tax_rate}%",
                            "amount": -tax_amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                    # Add SGST entry
                    elif tax_name == "SGST" and tax_amount:
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {tax_rate}%",
                            "amount": -tax_amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CShreeDuttkrupaLamination.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShreeDuttkrupaLamination.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShreeDuttkrupaLamination.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShreeDuttkrupaLamination.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shree Duttkrupa Lamination.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Shree Duttkrupa Lamination...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration =await  CShreeDuttkrupaLamination.MSGenerateNarration(dictExtractedData["ItemTable"])  # Build narration
            lsLedgerEntries = await CShreeDuttkrupaLamination.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShreeDuttkrupaLamination._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries] # Convert ledgers to schema

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Namkeen Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CShreeDuttkrupaLamination._mStrTracebackLogs = CShreeDuttkrupaLamination._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShreeDuttkrupaLamination._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsLineItems):
        # Creates narration from item descriptions, extracting part after "for"
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = dictItemInfo["DescriptionOfGoods"]
                if "for" in strItemName.lower():
                    for_pos = strItemName.lower().find("for")
                    part_after_for = strItemName[for_pos + len("for"):].strip()
                    if part_after_for:
                        strNarration += f"for {part_after_for}" + ("" if iIndex == len(lsLineItems) - 1 else ", ")
                else:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", f'No "for" found in: {strItemName}')
            return strNarration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CShivambicaSalesCorporation:

    _mStrLedgerName = "Shivambica Sales Corporation"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "C/1/2, NEW ANJALI COOP HOUSING SOC,",
            "MEMNAGAR",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AHNPP7336A1ZE",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for Shivambica Sales Corporation in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CShivambicaSalesCorporation._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledgers for purchase items based on GST rates
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            CGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'CGST'), 0)
            SGSTRate = next((tax['TaxRate'] for tax in dictExtractedData['Taxes']['MainTaxes'] if tax['TaxName'] == 'SGST'), 0)
            # Set ledger name based on combined GST rate
            if CGSTRate and SGSTRate:
                ledger_name = f"Purchase Dairy {int(CGSTRate + SGSTRate)}%"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": 0,
                    "is_deemed_positive": True,  # True means Tally treats it as Debit
                    "is_party_ledger": False
                }

            # Calculate amount including freight charges
            iChargeAmount = dictExtractedData["Charges"][0].get("ChargeAmount", 0) if dictExtractedData["Charges"] else 0
            ledger_details[ledger_name]["amount"] = -(dictExtractedData.get("SubTotal", 0) + iChargeAmount)  # Negative for debit

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CShivambicaSalesCorporation.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    iTaxAmount = tax.get("TaxAmount", 0)

                    # Add CGST entry
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                    # Add SGST entry
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        sgst_rate = iTaxRate
                        sgst_amount = tax.get("TaxAmount", 0)
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {sgst_rate}%",
                            "amount": -sgst_amount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CShivambicaSalesCorporation.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivambicaSalesCorporation.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivambicaSalesCorporation.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivambicaSalesCorporation.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shivambica Sales Corporation.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Shivambica Sales Corporation...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CShivambicaSalesCorporation.MSGenerateNarration(dictExtractedData["ItemTable"])  # Build narration
            lsLedgerEntries =await  CShivambicaSalesCorporation.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShivambicaSalesCorporation._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            # Generate XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CShivambicaSalesCorporation._mStrTracebackLogs = CShivambicaSalesCorporation._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShivambicaSalesCorporation._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsLineItems):
        # Creates narration from item descriptions
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsLineItems):
                product_name = dictGoodName.get("DescriptionOfGoods", "")
                strNarration += product_name + ("" if iIndex == len(lsLineItems) - 1 else ", ")
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CSachiProducts:

    _mStrLedgerName = "Sachi Products"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "1ST FLOOR, SACHI HOUSE,",
            "ABOVE SURYAKANT ISHWARLAL MITHAIWALA,",
            "COMMERCE SIX ROAD, NAVRANGPURA,",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AHVPK1541N1ZX",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Near Swaminarayan Temple", "Vastrapur Ahmedabad -380015"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for Sachi Products in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CSachiProducts._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledgers for utensil expenses based on GST rates from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])  # Extract MainTaxes
            ledger_details = {}

            # Process each tax entry for CGST/SGST
            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                taxable_amount = tax["TaxableAmount"]

                # Set ledger name based on combined GST rate
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Utensil Exp. {int(tax_rate * 2)}%"  # Double rate for total GST
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,  # True means Tally treats as Debit
                        "is_party_ledger": False
                    }

            # Assign taxable amounts to ledgers
            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[2][:-1])  # Extract rate from ledger name (e.g., 12 from "Utensil Exp. 12%")
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Negative for debit
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes, aggregates duplicates
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CSachiProducts.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    tax_amount = tax.get("TaxAmount", 0)

                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    # Add CGST entry
                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    # Add SGST entry
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CSachiProducts.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSachiProducts.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSachiProducts.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSachiProducts.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Sachi Products.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Sachi Products...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CSachiProducts.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])  # Build narration
            lsLedgerEntries = await CSachiProducts.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSachiProducts._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSachiProducts._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Prahlad Nagar",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            # Generate XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CSachiProducts._mStrTracebackLogs = CSachiProducts._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSachiProducts._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        # Creates narration from product/goods names
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as objException:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {objException}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CGovindToursAndTravels:

    _mStrLedgerName = "Govind Tours and Travels"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [],  # No address provided
        "gst_registration_type": "Regular",
        "gst_in": "",  # Empty GSTIN
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Vastrapur Ahmedabad", "Fssi No. 1071302600232"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for Govind Tours and Travels in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CGovindToursAndTravels._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledger for traveling expenses using total invoice amount
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            ledger_name = "TRAVELING EXP."
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount

            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -total_invoice_amount,  # Negative for debit
                    "is_deemed_positive": True,  # True means Tally treats as Debit
                    "is_party_ledger": False
                }

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CGovindToursAndTravels.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    iTaxAmount = tax.get("TaxAmount", 0)

                    # Add CGST entry
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                    # Add SGST entry
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        sgst_rate = iTaxRate  # CGST and SGST rates are equal
                        sgst_amount = tax.get("TaxAmount", 0)
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {sgst_rate}%",
                            "amount": -sgst_amount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CGovindToursAndTravels.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGovindToursAndTravels.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGovindToursAndTravels.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGovindToursAndTravels.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Govind Tours and Travels.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Govind Tours and Travels...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CGovindToursAndTravels.MSGenerateNarration(dictExtractedData["ItemTable"])  # Build narration
            lsLedgerEntries = await CGovindToursAndTravels.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CGovindToursAndTravels._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGovindToursAndTravels._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Vastrapur Office",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Servies",  # Typo in original code: should be "Services"?
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            # Generate XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CGovindToursAndTravels._mStrTracebackLogs = CGovindToursAndTravels._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CGovindToursAndTravels._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsLineItems):
        # Creates narration from passenger and sector names
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictItemInfo in enumerate(lsLineItems):
                strItemName = dictItemInfo.get("PassengerName", "")
                strSectorName = dictItemInfo.get("SectorName", "")
                combined_str = f"{strItemName} - {strSectorName}"
                strNarration += combined_str + ("" if iIndex == len(lsLineItems) - 1 else ", ")
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CIITraders:

    _mStrLedgerName = "I.I.Traders"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": ["SHOP NO. 98, APMC MARKETYARD,", "VASNA,", "AHMEDABAD."],
        "gst_registration_type": "Regular",
        "gst_in": "",  # Empty GSTIN
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "380001"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for I.I.Traders in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CIITraders._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledger for vegetable purchase (GST exempted) using total invoice amount
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            ledger_name = "Purchase Vegetable GST Exempted"
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount

            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -total_invoice_amount,  # Negative for debit
                    "is_deemed_positive": True,  # False means Tally treats it as Credit (adjusted logic)
                    "is_party_ledger": False
                }

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    iTaxRate = CIITraders.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    iTaxAmount = tax.get("TaxAmount", 0)

                    # Add CGST entry
                    if tax["TaxName"] == "CGST" and iTaxAmount != 0:
                        gst_ledger_details.append({
                            "ledger_name": f"Input CGST {iTaxRate}%",
                            "amount": -iTaxAmount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

                    # Add SGST entry
                    if tax["TaxName"] == "SGST" and iTaxAmount != 0:
                        sgst_rate = iTaxRate  # CGST and SGST rates are equal
                        sgst_amount = tax.get("TaxAmount", 0)
                        gst_ledger_details.append({
                            "ledger_name": f"Input SGST {sgst_rate}%",
                            "amount": -sgst_amount,  # Negative for debit
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CIITraders.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CIITraders.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CIITraders.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CIITraders.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for I.I.Traders.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for I.I.Traders...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CIITraders.MSGenerateNarration()  # Build static narration
            lsLedgerEntries = await CIITraders.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CIITraders._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGwalia._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                voucher_type="AV-Purchase (Credit)",  # Typo in original code: should be "Services"?
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            # Generate XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CIITraders._mStrTracebackLogs = CIITraders._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CIITraders._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration():
        # Returns static narration for vegetable purchase
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = "POTATO"
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CSovereignSales:

    _mStrLedgerName = "Sovereign Sales"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "B-226, POPULAR CENTER,",
            "NR. SHYAMAL CROSS ROAD,",
            "SATELLITE ROAD,AHMEDABAD."
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24DPJPS6101D1Z4",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Near Swaminarayan Temple", "Vastrapur Ahmedabad -380015"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        # Creates credit ledger entry for Sovereign Sales in purchase voucher
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CSovereignSales._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)  # Fetch total amount
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        # Generates debit ledgers for purchases based on GST rates from MainTaxes
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])  # Extract MainTaxes
            ledger_details = {}

            # Process each tax entry for CGST/SGST
            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]

                # Set ledger name based on combined GST rate
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Purchase {int(tax_rate * 2)}%"  # Double rate for total GST
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,  # True means Tally treats as Debit
                        "is_party_ledger": False
                    }

            # Assign taxable amounts to ledgers
            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[1][:-1])  # Extract rate from ledger name (e.g., 12 from "Purchase 12%")
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Negative for debit
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        # Builds GST ledger entries (Input CGST/SGST) from MainTaxes, aggregates duplicates
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CSovereignSales.MSFormatTaxRate(tax.get("TaxRate", 0))  # Format tax rate
                    tax_amount = tax.get("TaxAmount", 0)

                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    # Add CGST entry
                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    # Add SGST entry
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        # Handles round-off adjustments in the ledger
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        # Combines all ledger entries (credit, debit, GST, round-off)
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CSovereignSales.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSovereignSales.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSovereignSales.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSovereignSales.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Sovereign Sales.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Sovereign Sales...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))  # Convert date to int
            strNarration = await CSovereignSales.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])  # Build narration
            lsLedgerEntries = await CSovereignSales.MSGetLedgerInformation(dictExtractedData)  # Get all ledgers

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSovereignSales._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSovereignSales._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]  # Convert ledgers to schema

            # Additional ledger info for quantity difference
            objAdditionalLedgerInfo = AdditionalLedgerInfo()
            objAdditionalLedgerInfo.bDiffActualQTY = False

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                additionalInfo=objAdditionalLedgerInfo,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Accounting Invoice"
            )

            # Generate XML
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CSovereignSales._mStrTracebackLogs = CSovereignSales._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSovereignSales._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        # Creates narration from product/goods names
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as objException:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {objException}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        # Placeholder for tax rate formatting (assumed from context)
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CDharmSalesCompany:

    _mStrLedgerName = "Dharm Sales Company" # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "18,JAYPUNJ COMPLEX NR. MASTER PETROL PUMP,",
            "SHANKER BHUVAN,SHAHPUR,",
            "AHMEDABAD",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24BEVPS2230C1ZL",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": "" # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Fourth Floor", "Sunrise Mall Near Mansi Circle", "Vastrapur Ahmedabad", "Fssi No. **************"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd (2021-22)",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CDharmSalesCompany._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Purchase Packing {int(tax_rate * 2)}%"
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    }

            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[2][:-1])
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CDharmSalesCompany.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)
                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)


                    # Add CGST entry
                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}")
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CDharmSalesCompany.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CDharmSalesCompany.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CDharmSalesCompany.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CDharmSalesCompany.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Dharm Sales Company.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Dharm Sales Company...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CDharmSalesCompany.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CDharmSalesCompany.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CDharmSalesCompany._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CDharmSalesCompany._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Vastrapur Office",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Servies",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CDharmSalesCompany._mStrTracebackLogs = CDharmSalesCompany._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CDharmSalesCompany._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as objException:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {objException}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        return int(tax_rate)

class CSavnathEnterpriseLLP:
    _mStrLedgerName = "Savnath Enterprise Llp"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "5th Floor,519-520,Swati Crimson",
            "Clover,Nr.Shilaj Circle,",
            "Shilaj,Ahmedbad"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AEUFS4625P1Z6",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""
    }

    _mDictCosigneeData = {
        "address_list": [
            "Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884",
            "Fssi No. **************"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo = {
                "ledger_name": CSavnathEnterpriseLLP._mStrLedgerName,
                "amount": total_invoice_amount,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            return [dictCreditLedgerInfo]
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Credit Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Water Exp. {int(tax_rate * 2)}%"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        }

            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[2][:-1])
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -round(tax['TaxableAmount'], 2)
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Debit Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                tax_name = tax.get("TaxName", "")
                tax_rate = CSavnathEnterpriseLLP.MSFormatTaxRate(tax.get("TaxRate", 0))
                taxable_amount = tax.get("TaxableAmount", 0)
                value = Decimal(str(taxable_amount)) * Decimal(str(tax_rate)) / Decimal('100')
                rounded_value = value.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

                if isinstance(tax_rate, float) :
                    tax_rate = int(tax_rate)

                ledger_name = f"Input {tax_name} {tax_rate}%"
                if tax_name in ["CGST", "SGST"] and rounded_value:
                    existing = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                    if existing:
                        existing["amount"] += -rounded_value
                    else:
                        gst_ledger_details.append({
                            "ledger_name": ledger_name,
                            "amount": -rounded_value,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"GST Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            iTotalAmount = Decimal(str(dictExtractedData.get("TotalAmount", 0)))
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            TotalTaxAmount = Decimal('0')
            for tax in main_taxes:
                taxable_amount = Decimal(str(tax.get("TaxableAmount", 0)))
                tax_rate = Decimal(str(tax.get("TaxRate", 0)))
                tax_amount = (taxable_amount * tax_rate / Decimal('100')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                TotalTaxAmount += tax_amount

            iSubTotal = Decimal(str(dictExtractedData.get("SubTotal", 0)))
            dRoundOff = iTotalAmount - (iSubTotal + TotalTaxAmount)
            roundoff_amount = -dRoundOff if dRoundOff >= 0 else abs(dRoundOff)

            return [{
                "ledger_name": "Round Off",
                "amount": roundoff_amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                "is_deemed_positive": True,
                "is_party_ledger": False
            }]
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Round-Off Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CSavnathEnterpriseLLP.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSavnathEnterpriseLLP.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSavnathEnterpriseLLP.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSavnathEnterpriseLLP.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Ledger Info Aggregation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Savnath Enterprise Llp.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            # Updated to await the narration function since it is now asynchronous
            strNarration = await CSavnathEnterpriseLLP.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CSavnathEnterpriseLLP.MSGetLedgerInformation(dictExtractedData)

            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSavnathEnterpriseLLP._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSavnathEnterpriseLLP._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Gwalbhog Tapovan",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to create XML: {e}")
            CSavnathEnterpriseLLP._mStrTracebackLogs = CSavnathEnterpriseLLP._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSavnathEnterpriseLLP._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """
        Generates a narration string by concatenating product/goods names.
        Now using asynchronous logging instead of print statements.
        """
        strNarration = ""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
        except Exception as objException:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Narration Generation Error: {objException}")
        return strNarration.upper()

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        return int(tax_rate)


class CUnicornEnterprise:

    _mStrLedgerName = "Unicorn Enterprise"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "F-19,1st Floor,Shubh Complex,",
            "Nr.Rajasthan Hospital,",
            "Shahibaug,Ahmedabad"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "",  # Empty GSTIN
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": [
            "Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884",
            "Fssi No. **************"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Unicorn Enterprise in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CUnicornEnterprise._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}"
            )
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """Generates debit ledgers for exempted purchases and transportation expenses."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}

            # Add Purchase Exempted ledger
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -dictExtractedData.get("SubTotal", 0),  # Negative for debit from subtotal
                    "is_deemed_positive": True,  # True means Tally treats as Debit
                    "is_party_ledger": False
                }

            # Add Transportation Exp. ledger
            ledger_name = "Transportation Exp."
            charges = dictExtractedData.get("Charges", [])
            total_charges = sum(charge.get("ChargeAmount", 0) for charge in charges)
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -total_charges,  # Negative for debit from charges
                    "is_deemed_positive": True,
                    "is_party_ledger": False
                }

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        """Builds GST ledger entries (Input CGST/SGST) from MainTaxes and aggregates duplicates."""
        gst_ledger_details = []
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CUnicornEnterprise.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)

                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}"
                    )
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """Handles round-off adjustments in the ledger."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """Combines all ledger entries (credit, debit, GST, round-off)."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CUnicornEnterprise.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CUnicornEnterprise.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CUnicornEnterprise.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CUnicornEnterprise.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}"
            )
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Unicorn Enterprise.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Info", "Entering MSCreateXML for Unicorn Enterprise..."
            )
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CUnicornEnterprise.MSGenerateNarration(
                dictExtractedData["Products/GoodsNameList"]
            )
            lsLedgerEntries = await CUnicornEnterprise.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CUnicornEnterprise._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CUnicornEnterprise._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML."
            )
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}"
            )
            CUnicornEnterprise._mStrTracebackLogs = CUnicornEnterprise._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CUnicornEnterprise._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """Creates narration from product/goods names."""
        strNarration = ""
        try:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Info", "Entering MSGenerateNarration..."
            )
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
        except Exception as objException:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {objException}"
            )
        return strNarration.upper()

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        """Placeholder for tax rate formatting (assumed from context)."""
        return int(tax_rate)  # Simplistic; adjust as per actual logic

class CGurukrupaTraders:

    _mStrLedgerName = "Gurukrupa Traders-SC"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "Shop No.371/3/1/2 Katpitiya Mahajan",
            "Wadi,Kalupur Chokha Baazar",
            "Udhyam"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AALFG4713B1ZV",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": [
            "401-405 Sunrise Mall Mansi Circle",
            " Near Swaminarayan Temple",
            " Vastrapur Ahmedabad -380015",
            " Fssi No. 10017021002884"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Gurukrupa Traders in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CGurukrupaTraders._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """Generates debit ledger for exempted purchases using subtotal."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -dictExtractedData.get("SubTotal", 0),  # Negative for debit from subtotal
                    "is_deemed_positive": True,  # True means Tally treats as Debit
                    "is_party_ledger": False
                }
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Builds GST ledger entries (Input CGST/SGST) from MainTaxes and aggregates duplicates.
        """
        gst_ledger_details = []
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CGurukrupaTraders.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)

                    # Convert tax_rate to int if whole number
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}"
                    )
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """Handles round-off adjustments in the ledger."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """
        Combines all ledger entries (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CGurukrupaTraders.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGurukrupaTraders.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGurukrupaTraders.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGurukrupaTraders.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Gurukrupa Traders.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Gurukrupa Traders...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CGurukrupaTraders.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CGurukrupaTraders.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CGurukrupaTraders._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGurukrupaTraders._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CGurukrupaTraders._mStrTracebackLogs = CGurukrupaTraders._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CGurukrupaTraders._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """Creates narration from product/goods names."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        """Placeholder for tax rate formatting (assumed from context)."""
        return int(tax_rate)

class CGrainsMore:

    _mStrLedgerName = "Grains & More"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "Survey No.287/288.Shed No.23,32 & 33,",
            "Krishna Estate,Near Panchratna Estae,",
            "Sanand,ChangodaR,",
            "Ahmedanad"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24ABBFG2079D1ZS",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": [
            "Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884",
            "Fssi No. **************"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Grains & More in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CGrainsMore._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}"
            )
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """Generates debit ledger entry for exempted purchases using subtotal."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            ledger_details = {}
            ledger_name = "Purchase Exempted"
            if ledger_name not in ledger_details:
                ledger_details[ledger_name] = {
                    "ledger_name": ledger_name,
                    "amount": -dictExtractedData.get("SubTotal", 0),  # Negative for debit from subtotal
                    "is_deemed_positive": True,  # True means Tally treats as Debit
                    "is_party_ledger": False
                }
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """Combines credit and debit ledger entries."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CGrainsMore.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CGrainsMore.MSGetDebitLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}"
            )
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Grains & More.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Grains & More...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CGrainsMore.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CGrainsMore.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects (ensure CGwalia._mDictCompanyData is defined elsewhere)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CGrainsMore._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CGrainsMore._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Sweet Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML."
            )
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}"
            )
            CGrainsMore._mStrTracebackLogs = CGrainsMore._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CGrainsMore._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """Creates narration from product/goods names."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}"
            )
            return ""

class CShivayEnterprise:

    _mStrLedgerName = "SHIVAY ENTERPRISE"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [
            "15,NIJANAND SHOPPING CENTER,",
            "NR.NIRANT CROSS ROAD VASTRAL,",
            "AHMEDABAD"
        ],
        "gst_registration_type": "Regular",
        "gst_in": "24AEOFS2869B1ZV",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": [
            "401-405 Sunrise Mall Mansi Circle",
            " Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Shivay Enterprise in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CShivayEnterprise._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}"
            )
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Generates debit ledgers for packing purchases based on GST rates from MainTaxes.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            # Process each tax entry for CGST/SGST and build ledger name
            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]

                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Purchase Packing {int(tax_rate * 2)}%"  # Double rate for total GST
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,  # True means Tally treats as Debit
                            "is_party_ledger": False
                        }

            # Assign taxable amounts to the corresponding ledgers
            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[2][:-1])  # e.g., extract 12 from "Purchase Packing 12%"
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Negative for debit
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Builds GST ledger entries (Input CGST/SGST) from MainTaxes, aggregating duplicates.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CShivayEnterprise.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)

                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -tax_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId, "Error", f"Error processing GST ledger info row: {inner_e}"
                    )
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Handles round-off adjustments in the ledger.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            dRoundOff = dictExtractedData.get("RoundingOff", 0)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """
        Combines all ledger entries (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CShivayEnterprise.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivayEnterprise.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivayEnterprise.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CShivayEnterprise.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}"
            )
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Shivay Enterprise.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Shivay Enterprise...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CShivayEnterprise.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CShivayEnterprise.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects (ensure CGwalia._mDictCompanyData is defined)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CShivayEnterprise._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CShivayEnterprise._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objAdditionalLedgerInfo = AdditionalLedgerInfo()
            objAdditionalLedgerInfo.bDiffActualQTY = True

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Gwalbhog Tapovan",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                additionalInfo=objAdditionalLedgerInfo,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML."
            )
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}"
            )
            CShivayEnterprise._mStrTracebackLogs = CShivayEnterprise._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CShivayEnterprise._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """
        Creates narration from product/goods names.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                strNarration += product_name + ("" if iIndex == len(lsGoodsList) - 1 else ", ")
            return strNarration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}"
            )
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        """
        Placeholder for tax rate formatting (assumed from context).
        """
        return int(tax_rate)

class CYashTradelink:
    _mStrLedgerName = "Yash Tradelink"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": ["Lg-28,Someshwara-2,", "Opp.Star Bazar", "Satalite,Ahmedabad"],
        "gst_registration_type": "Regular",
        "gst_in": "24DFOPR1590J1ZX",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""
    }
    _mDictCosigneeData = {
        "address_list": ["401-405 Sunrise Mall Mansi Circle", " Near Swaminarayan Temple", "Vastrapur Ahmedabad -380015", "Fssi No. 10017021002884"],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            return [{
                "ledger_name": CYashTradelink._mStrLedgerName,
                "amount": total_invoice_amount,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }]
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Credit Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Purchase {int(tax_rate * 2)}%"
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        }

            for key, ledger in ledger_details.items():
                gst_rate = int(key.split(' ')[1][:-1])
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -round(tax['TaxableAmount'], 2)
                        break

            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Debit Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        gst_ledger_details = []
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            main_taxes = dictExtractedData.get("Taxes", {}).get("MainTaxes", [])

            for tax in main_taxes:
                tax_name = tax.get("TaxName", "")
                tax_rate = CYashTradelink.MSFormatTaxRate(tax.get("TaxRate", 0))
                taxable_amount = tax.get("TaxableAmount", 0)
                amount = Decimal(str(taxable_amount)) * Decimal(str(tax_rate)) / Decimal('100')
                amount = amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)

                if isinstance(tax_rate, float) :
                    tax_rate = int(tax_rate)

                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Input {tax_name} {tax_rate}%"
                    existing = next((entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None)
                    if existing:
                        existing["amount"] += -amount
                    else:
                        gst_ledger_details.append({
                            "ledger_name": ledger_name,
                            "amount": -amount,
                            "is_deemed_positive": True,
                            "is_party_ledger": False
                        })

            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"GST Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            main_taxes = dictExtractedData.get('Taxes', {}).get('MainTaxes', [])
            credit_amount = Decimal(str(dictExtractedData.get("TotalAmount", 0)))
            sub_total = Decimal(str(dictExtractedData.get("SubTotal", 0)))
            charges = dictExtractedData.get("Charges", [])
            total_charges = sum(Decimal(str(c.get("ChargeAmount", 0))) for c in charges)

            total_tax_value = Decimal('0')
            for tax in main_taxes:
                try:
                    taxable_amount = Decimal(str(tax.get("TaxableAmount", 0)))
                    tax_rate = Decimal(str(tax.get("TaxRate", 0)))
                    total_tax_value += (taxable_amount * tax_rate / Decimal('100')).quantize(Decimal('0.01'), rounding=ROUND_HALF_UP)
                except Exception as e:
                    await CLogController.MSWriteLog(CGwalia._mIUserId, "Warning", f"Partial Tax Calc Error: {e}")

            total_before_rounding = sub_total + total_charges + total_tax_value
            dRoundOff = credit_amount - total_before_rounding

            amount = -dRoundOff if dRoundOff >= 0 else abs(dRoundOff)

            return [{
                "ledger_name": "Round Off",
                "amount": amount.quantize(Decimal('0.01'), rounding=ROUND_HALF_UP),
                "is_deemed_positive": True,
                "is_party_ledger": False
            }]
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Round-Off Ledger Info Error: {e}")
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CYashTradelink.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CYashTradelink.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CYashTradelink.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CYashTradelink.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Ledger Info Aggregation Error: {e}")
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Yash Tradelink.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CYashTradelink.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CYashTradelink.MSGetLedgerInformation(dictExtractedData)

            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CYashTradelink._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CYashTradelink._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Topnotch",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Accounting Invoice"
            )
            dictResponse["XMLData"] = objPurchaseVoucher.to_string(pretty=True)
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"XML Creation Error: {e}")
            CYashTradelink._mStrTracebackLogs = CYashTradelink._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CYashTradelink._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        try:
            narration = ", ".join(g.get("Product/GoodsName", "") for g in lsGoodsList)
            return narration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"Narration Generation Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        return int(tax_rate)

class CSwiggyDineoutPalladium:

    _mStrLedgerName = "Swiggy Dineout-184849-Palladium 873430"  # Ledger name for Tally
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [],  # No address provided
        "gst_registration_type": "Regular",
        "gst_in": "24AAFCB7707D1Z0",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""  # Empty pin code as per schema
    }
    _mDictCosigneeData = {
        "address_list": [
            "Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884",
            "Fssi No. **************"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Swiggy Dineout Palladium in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CSwiggyDineoutPalladium._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,  # False means Tally treats it as Debit (unusual for purchase)
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"Failed to Get Credit Ledger Info: {e}"
            )
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """
        Generates debit ledgers for sales promotion expenses based on GST rates from MainTaxes.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            # Process each tax entry for CGST/SGST and build the ledger name.
            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                if tax_name in ["CGST", "SGST"]:
                    ledger_name = f"Sales Promotion Exp. {int(tax_rate * 2)}%"  # Double rate for total GST
                    if ledger_name not in ledger_details:
                        ledger_details[ledger_name] = {
                            "ledger_name": ledger_name,
                            "amount": 0,
                            "is_deemed_positive": True,  # Tally treats as Debit
                            "is_party_ledger": False
                        }
            # Assign taxable amounts to the appropriate ledgers.
            for key, ledger in ledger_details.items():
                # Extract the rate from ledger name, e.g., "Sales Promotion Exp. 12%"
                gst_rate = int(key.split(' ')[3][:-1])
                for tax in MainTaxes:
                    if tax['TaxRate'] == gst_rate / 2:
                        ledger['amount'] = -tax['TaxableAmount']  # Negative for debit
                        break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetDebitLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        """
        Builds GST ledger entries (Input CGST/SGST) from MainTaxes by aggregating duplicates.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CSwiggyDineoutPalladium.MSFormatTaxRate(tax.get("TaxRate", 0))
                    tax_amount = tax.get("TaxAmount", 0)

                    # Ensure tax_rate is an int if it is a whole number.
                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and tax_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        totalTaxableAmount = round((tax["TaxableAmount"] * tax_rate) / 100, 2)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount  # Aggregate amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -totalTaxableAmount,  # Negative for debit
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and tax_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        totalTaxableAmount = round((tax["TaxableAmount"] * tax_rate) / 100, 2)
                        if existing_ledger:
                            existing_ledger["amount"] += -tax_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -totalTaxableAmount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId, "Error", f"Error processing GST ledger row: {inner_e}"
                    )
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetGSTLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """
        Handles round-off adjustments in the ledger.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,  # True means debit for positive, credit for negative
                "is_party_ledger": False
            }
            # Use the first tax entry from MainTaxes for calculations.
            first_tax = dictExtractedData.get('Taxes', {}).get('MainTaxes', [])[0]
            credit_amount = dictExtractedData.get("TotalAmount", 0)
            taxable_amount = first_tax.get('TaxableAmount', 0)
            tax_rate = first_tax.get('TaxRate', 0)
            totalTaxValue = round((taxable_amount * tax_rate) / 100, 2) * 2
            dRoundOff = credit_amount - (totalTaxValue + taxable_amount)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)  # Credit adjustment
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff  # Debit adjustment
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetRoundOffLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """
        Combines all ledger entries (credit, debit, GST, round-off).
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsLedgersInfo.extend(await CSwiggyDineoutPalladium.MSGetCreditLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSwiggyDineoutPalladium.MSGetDebitLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSwiggyDineoutPalladium.MSGetGSTLedgerInfo(dictExtractedData))
            lsLedgersInfo.extend(await CSwiggyDineoutPalladium.MSGetRoundOffLedgerInfo(dictExtractedData))
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId, "Error", f"MSGetLedgerInformation Error: {e}"
            )
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Swiggy Dineout Palladium.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Swiggy Dineout Palladium...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CSwiggyDineoutPalladium.MSGenerateNarration(dictExtractedData)
            lsLedgerEntries = await CSwiggyDineoutPalladium.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects (ensure CGwalia._mDictCompanyData is defined in your context)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSwiggyDineoutPalladium._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSwiggyDineoutPalladium._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objAdditionalLedgerInfo = AdditionalLedgerInfo()
            objAdditionalLedgerInfo.bDiffActualQTY = True

            # Build purchase voucher
            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Vanakam Peladium Mall",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                additionalInfo=objAdditionalLedgerInfo,
                voucher_type="AV-Servies",  # Note: Verify if this should be "Services"
                udf_data=lsUdfData,
                voucher_entry_mode="Item Invoice"
            )

            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSCreateXML Error: {e}")
            CSwiggyDineoutPalladium._mStrTracebackLogs = CSwiggyDineoutPalladium._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSwiggyDineoutPalladium._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(dictExtractedData):
        """
        Creates narration from the period range in extracted data.
        """
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            # For Swiggy Dineout Palladium, the narration is built from the period range.
            strNarration = f"Period From: {dictExtractedData['PeriodFrom']}, Period To: {dictExtractedData['PeriodTo']}"
            return strNarration
        except Exception as e:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Error", f"MSGenerateNarration Error: {e}")
            return ""

    @staticmethod
    def MSFormatTaxRate(tax_rate):
        """
        Placeholder for tax rate formatting (assumed from context).
        """
        return int(tax_rate)

class CSatyamSteelHouse:

    _mStrLedgerName = "Satyam Steel House"
    _mDictPartyData = {
        "party_name": _mStrLedgerName,
        "address_list": [],
        "gst_registration_type": "Regular",
        "gst_in": "24ABUPV9903G1Z0",
        "state_name": "Gujarat",
        "country_name": "India",
        "pin_code": ""
    }
    _mDictCosigneeData = {
        "address_list": [
            "401-405 Sunrise Mall Mansi Circle",
            " Near Swaminarayan Temple",
            "Vastrapur Ahmedabad -380015",
            "Fssi No. 10017021002884"
        ],
        "gst_in": "24AAACG5535F1ZY",
        "mailing_name": "Gwalia Sweets Pvt Ltd",
        "state_name": "Gujarat",
        "pin_code": "",
        "country_name": "India"
    }
    _mStrTracebackLogs = ""

    @staticmethod
    async def MSGetCreditLedgerInfo(dictExtractedData):
        """Creates credit ledger entry for Satyam Steel House in purchase voucher."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetCreditLedgerInfo...")
            lsCreditLedgers = []
            dictCreditLedgerInfo = {
                "ledger_name": CSatyamSteelHouse._mStrLedgerName,
                "amount": 0,
                "is_deemed_positive": False,
                "is_party_ledger": True
            }
            total_invoice_amount = dictExtractedData.get("TotalAmount", 0)
            dictCreditLedgerInfo["amount"] = total_invoice_amount
            lsCreditLedgers.append(dictCreditLedgerInfo)
            return lsCreditLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"Failed to Get Credit Ledger Info: {e}"
            )
            raise

    @staticmethod
    async def MSGetDebitLedgerInfo(dictExtractedData):
        """Generates debit ledger entries for utensil expenses from GST taxes."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetDebitLedgerInfo...")
            table_tax = dictExtractedData.get("Taxes", {})
            MainTaxes = table_tax.get("MainTaxes", [])
            ledger_details = {}

            for tax in MainTaxes:
                tax_name = tax["TaxName"]
                tax_rate = tax["TaxRate"]
                taxable_amount = tax["TaxableAmount"]

                if tax_name == "CGST":
                    ledger_name = f"Utensil Exp. {int(tax_rate * 2)}%"
                elif tax_name == "SGST":
                    ledger_name = f"Utensil Exp. {int(tax_rate * 2)}%"
                # Add ledger entry if not already recorded.
                if ledger_name not in ledger_details:
                    ledger_details[ledger_name] = {
                        "ledger_name": ledger_name,
                        "amount": 0,
                        "is_deemed_positive": True,
                        "is_party_ledger": False
                    }

            for key, ledger in ledger_details.items():
                if 'Utensil Exp.' in key:
                    gst_rate = int(key.split(' ')[2][:-1])
                    for tax in MainTaxes:
                        if tax['TaxRate'] == gst_rate / 2:
                            ledger['amount'] = -tax['TaxableAmount']
                            break
            return list(ledger_details.values())
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSGetDebitLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetGSTLedgerInfo(dictExtractedData):
        """Builds GST ledger entries (Input CGST/SGST) by aggregating calculated amounts."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetGSTLedgerInfo...")
            gst_ledger_details = []
            taxes = dictExtractedData.get("Taxes", {})
            main_taxes = taxes.get("MainTaxes", [])

            for tax in main_taxes:
                try:
                    tax_name = tax.get("TaxName", "")
                    tax_rate = CGwalia.MSFormatTaxRate(tax.get("TaxRate", 0))
                    taxable_amount = tax.get("TaxableAmount", 0)

                    if isinstance(tax_rate, float) :
                        tax_rate = int(tax_rate)

                    if tax_name == "CGST" and taxable_amount:
                        ledger_name = f"Input CGST {tax_rate}%"
                        calculated_amount = round(taxable_amount * tax_rate / 100, 2)
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -calculated_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -calculated_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                    elif tax_name == "SGST" and taxable_amount:
                        ledger_name = f"Input SGST {tax_rate}%"
                        calculated_amount = round(taxable_amount * tax_rate / 100, 2)
                        existing_ledger = next(
                            (entry for entry in gst_ledger_details if entry["ledger_name"] == ledger_name), None
                        )
                        if existing_ledger:
                            existing_ledger["amount"] += -calculated_amount
                        else:
                            gst_ledger_details.append({
                                "ledger_name": ledger_name,
                                "amount": -calculated_amount,
                                "is_deemed_positive": True,
                                "is_party_ledger": False
                            })
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId,
                        "Error",
                        f"Error processing GST ledger info row: {inner_e}"
                    )
            return gst_ledger_details
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSGetGSTLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetRoundOffLedgerInfo(dictExtractedData):
        """Handles round-off adjustments in the ledger."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetRoundOffLedgerInfo...")
            lsRoundoffLedgers = []
            dictRoundoffLedgerInfo = {
                "ledger_name": "Round Off",
                "amount": 0,
                "is_deemed_positive": True,
                "is_party_ledger": False
            }
            main_taxes = dictExtractedData.get('Taxes', {}).get('MainTaxes', [])
            credit_amount = dictExtractedData.get("TotalAmount", 0)
            sub_total = dictExtractedData.get("SubTotal", 0)
            charges = dictExtractedData.get("Charges", [])
            total_charges = sum(charge.get("ChargeAmount", 0) for charge in charges)
            total_tax_value = 0
            for tax in main_taxes:
                try:
                    taxable_amount = tax.get("TaxableAmount", 0)
                    tax_rate = tax.get("TaxRate", 0)
                    calculated_tax_amount = round((taxable_amount * tax_rate) / 100, 2)
                    total_tax_value += calculated_tax_amount
                except Exception as inner_e:
                    await CLogController.MSWriteLog(
                        CGwalia._mIUserId,
                        "Error",
                        f"Error processing tax entry: {inner_e}"
                    )
            total_before_rounding = sub_total + total_charges + total_tax_value
            dRoundOff = round(credit_amount - total_before_rounding, 2)
            if dRoundOff < 0:
                dictRoundoffLedgerInfo["amount"] = abs(dRoundOff)
            else:
                dictRoundoffLedgerInfo["amount"] = -dRoundOff
            lsRoundoffLedgers.append(dictRoundoffLedgerInfo)
            return lsRoundoffLedgers
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSGetRoundOffLedgerInfo Error: {e}"
            )
            raise

    @staticmethod
    async def MSGetLedgerInformation(dictExtractedData):
        """Combines all ledger entries: credit, debit, GST and round-off."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGetLedgerInformation...")
            lsLedgersInfo = []
            lsCreditLedgers = await CSatyamSteelHouse.MSGetCreditLedgerInfo(dictExtractedData)
            lsDebitLedgers = await CSatyamSteelHouse.MSGetDebitLedgerInfo(dictExtractedData)
            lsGSTLedgerDetails = await CSatyamSteelHouse.MSGetGSTLedgerInfo(dictExtractedData)
            lsRoundoffLedgers = await CSatyamSteelHouse.MSGetRoundOffLedgerInfo(dictExtractedData)
            lsLedgersInfo.extend(lsCreditLedgers)
            lsLedgersInfo.extend(lsDebitLedgers)
            lsLedgersInfo.extend(lsGSTLedgerDetails)
            lsLedgersInfo.extend(lsRoundoffLedgers)
            return lsLedgersInfo
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSGetLedgerInformation Error: {e}"
            )
            raise

    @staticmethod
    async def MSCreateXML(dictExtractedData, lsUdfData=[{}]):
        """
        Build the Tally Purchase Voucher XML for Satyam Steel House.
        Returns a dictionary with "XMLData" and "ErrorMsg" keys.
        """
        dictResponse = {
            "XMLData" : None,
            "ErrorMsg" : None
        }
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSCreateXML for Satyam Steel House...")
            strInvoiceNumber = str(dictExtractedData.get("InvoiceNo", "NA"))
            iInvoiceDate = int(dictExtractedData.get("InvoiceDate", 0))
            strNarration = await CSatyamSteelHouse.MSGenerateNarration(dictExtractedData["Products/GoodsNameList"])
            lsLedgerEntries = await CSatyamSteelHouse.MSGetLedgerInformation(dictExtractedData)

            # Initialize schema objects (ensure CGwalia._mDictCompanyData is defined in your context)
            objCompanyInfo = CompanyInfoSchema(**CGwalia._mDictCompanyData)
            objPartyDetails = PartyDetailsSchema(**CSatyamSteelHouse._mDictPartyData)
            objConsigneeDetails = ConsigneeDetailsSchema(**CSatyamSteelHouse._mDictCosigneeData)
            lsLedgerEntries = [LedgerEntrySchema(**ld) for ld in lsLedgerEntries]

            objPurchaseVoucher = TallyPurchaseVoucherSchema(
                company_info=objCompanyInfo,
                party_details=objPartyDetails,
                consignee_details=objConsigneeDetails,
                voucher_number=strInvoiceNumber,
                cost_center_name="Lapkamana Factory",
                invoice_date=str(iInvoiceDate),
                invoice_no=strInvoiceNumber,
                narration=strNarration,
                ledger_entries=lsLedgerEntries,
                diffQty=False,
                voucher_type="AV-Purchase (Credit)",
                udf_data=lsUdfData,
                voucher_entry_mode="Accounting Invoice"
            )
            xml_str = objPurchaseVoucher.to_string(pretty=True)
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Successfully created XML in MSCreateXML.")
            dictResponse["XMLData"] = xml_str
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSCreateXML Error: {e}"
            )
            CSatyamSteelHouse._mStrTracebackLogs = CSatyamSteelHouse._mStrTracebackLogs + f"Error in MSCreateXML: {traceback.format_exc()}"
            dictResponse["ErrorMsg"] = CSatyamSteelHouse._mStrTracebackLogs

        return dictResponse

    @staticmethod
    async def MSGenerateNarration(lsGoodsList):
        """Creates narration from product/goods names."""
        try:
            await CLogController.MSWriteLog(CGwalia._mIUserId, "Info", "Entering MSGenerateNarration...")
            strNarration = ""
            for iIndex, dictGoodName in enumerate(lsGoodsList):
                product_name = dictGoodName.get("Product/GoodsName", "")
                if iIndex == len(lsGoodsList) - 1:
                    strNarration += product_name
                else:
                    strNarration += product_name + ", "
            return strNarration.upper()
        except Exception as e:
            await CLogController.MSWriteLog(
                CGwalia._mIUserId,
                "Error",
                f"MSGenerateNarration Error: {e}"
            )
            return ""


if __name__ == "__main__":
    # api_response_dir=r"GitIgnore\TestXml\apiResponse\DiamondPrivateSecurityInvestigationServices"
    # output_dir=r"GitIgnore\TestXml\XmlFiles\DiamondPrivateSecurityInvestigationServices"
    api_response_dir=r"GitIgnore\TestXml\apiResponse"
    output_dir=r"GitIgnore\TestXml\XmlFiles"


    for filename in os.listdir(api_response_dir):

        if filename.endswith("_gptResponse.json"):
            #
            json_file_path = os.path.join(api_response_dir, filename)


            with open(json_file_path, "r") as file:
                api_response = json.load(file)


            content = api_response['choices'][0]['message']['content']
            content = json.loads(content)


            XmlOutput = CIITraders.MSCreateXML(content)


            invoice_no = content.get("InvoiceNo", "NA")
            invoice_no = invoice_no.replace("/", "_")


            xml_file_name = f"Invoiceno_{invoice_no}_xmlfile.xml"
            xml_file_path = os.path.join(output_dir, xml_file_name)



            with open(xml_file_path, "w") as xml_file:
                xml_file.write(XmlOutput)


