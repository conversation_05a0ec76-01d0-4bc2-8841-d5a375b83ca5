{"type": "json_schema", "json_schema": {"name": "PICD", "strict": true, "schema": {"type": "object", "properties": {"InvoiceNumber": {"type": "string", "description": "Unique identifier for the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date (convert to ddmmyy format from ddmmyyyy format) on which the invoice was issued."}, "JobNumbers": {"type": "array", "items": {"type": "string"}, "description": "it is a list of elements all alphanumeric without any symbols so you need to separate and space between two word as one string , for instance  'JOB NO. 11297,11298,11424,TIHI MOVEMENT' to [ '11297','11298','11424','TIHI MOVEMENT'] , and 'ICD,RAIL,EP-10938,10937' to 'ICD','RAIL','EP-10938','10937'"}, "DeliveryNoteDate": {"type": "integer", "description": "Date when the invoice was issued in ddmmyy format"}, "SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the supplier or seller"}, "SellerStateName": {"type": "string", "description": "Name of state from address of the seller issuing the invoice"}, "SellerStatecode": {"type": "integer", "description": "State code of the seller issuing the invoice"}, "SellerCINNo": {"type": "string", "description": "Corporate Identification Number (CIN) of the seller, issued by the Registrar of Companies to uniquely identify companies."}, "SellerEmail": {"type": "string", "description": "Email address of the seller for communication purposes."}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"}, "City/PortOfLoading": {"type": "string", "description": "The city or port where the goods were loaded for transportation or shipment,if not present give empty string"}, "City/PortOfDischarge": {"type": "string", "description": "The city or port where the goods were unloaded or discharged upon arrival,if not present give empty string"}, "ItemTotal": {"type": "number", "description": "Total value of the invoice before taxes, including the sum of all item amounts."}, "ContainerNo": {"type": "string", "description": "The container number used for transporting the goods."}, "ContainerSize": {"type": "string", "description": "The container size used for transporting the goods, generally in double or more digit,for example 'GLDU9553775 / 20' so size would be 20 "}, "ShippingBill/BOENo": {"type": "integer", "description": "The unique Bill of Entry (BOE) number associated with the shipment, used for customs clearance."}, "ShippingBill/BOEDate": {"type": "integer", "description": "The date when the Bill of Entry (BOE) was filed, indicating customs clearance for the shipment in ddmmyy format."}, "CGSTAmount": {"type": "number", "description": "The amount of Central Goods and Services Tax (CGST) applied to the invoice."}, "SGSTAmount": {"type": "number", "description": "The amount of State Goods and Services Tax (SGST) applied to the invoice."}, "IGSTAmount": {"type": "number", "description": "The amount of Integrated Goods and Services Tax (IGST) applied to the invoice, typically for inter-state transactions."}, "RoundOff": {"type": "number", "description": "The amount added or subtracted to adjust the total invoice amount to the nearest value, ensuring accuracy in the final payable amount."}, "NetAmount": {"type": "number", "description": "The final total amount payable after including all applicable taxes."}, "FinancialYear": {"type": "string", "description": "The financial year mentioned in the invoice terms, indicating the applicable fiscal year, like 2024-25."}, "PaymentDiscrepancyTimeline": {"type": "string", "description": "The timeline within which discrepancies or issues with the invoice must be reported, like 7 days."}, "Remarks": {"type": "string", "description": "Additional remarks on the invoice"}, "PAN": {"type": "string", "description": "PAN number of the seller"}, "PreparedBy": {"type": "string", "description": "Name of the person who prepared the invoice"}, "Table": {"type": "array", "items": {"type": "object", "properties": {"Sl.No": {"type": "string", "description": "Serial number of the item in the invoice"}, "DescriptionofServices": {"type": "string", "description": "Description of the goods or services"}, "HSN/SAC": {"type": "integer", "description": "HSN (Harmonized System of Nomenclature) or SAC (Services Accounting Code)"}, "GSTRate": {"type": "number", "description": "The percentage rate of Goods and Services Tax (GST) applied to the item or service."}, "Amount": {"type": "number", "description": "Amount for the item"}}, "required": ["Sl.No", "DescriptionofServices", "HSN/SAC", "GSTRate", "Amount"], "additionalProperties": false}, "description": "Details of all the items in the invoice"}}, "required": ["InvoiceNumber", "InvoiceDate", "JobNumbers", "DeliveryNoteDate", "<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerGSTINNo", "SellerStateName", "SellerStatecode", "SellerCINNo", "Seller<PERSON>mail", "BuyerName", "BuyerAddress", "BuyerGSTIN", "City/PortOfLoading", "City/PortOfDischarge", "ItemTotal", "ContainerNo", "ContainerSize", "ShippingBill/BOENo", "ShippingBill/BOEDate", "CGSTAmount", "SGSTAmount", "IGSTAmount", "RoundOff", "NetAmount", "FinancialYear", "PaymentDiscrepancyTimeline", "Remarks", "PAN", "PreparedBy", "Table"], "additionalProperties": false}}}