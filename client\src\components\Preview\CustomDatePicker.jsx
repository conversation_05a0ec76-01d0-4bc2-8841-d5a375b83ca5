import { useEffect, useState } from 'react';
import PropTypes from 'prop-types';
import { AdapterDayjs } from '@mui/x-date-pickers/AdapterDayjs';
import { LocalizationProvider } from '@mui/x-date-pickers/LocalizationProvider';
import { DatePicker } from '@mui/x-date-pickers/DatePicker';
import dayjs from 'dayjs';
import { TextField } from '@mui/material';

function useWindowSize() {
    const [windowSize, setWindowSize] = useState({
        width: undefined,
        height: undefined,
    });

    useEffect(() => {
        function handleResize() {
            setWindowSize({
                width: window.innerWidth,
                height: window.innerHeight,
            });
        }

        window.addEventListener('resize', handleResize);
        handleResize();
        return () => window.removeEventListener('resize', handleResize);
    }, []);

    return windowSize;
}

const CustomDatePicker = ({ k, dateValue, customHandleChange, handleChange, formateOfDate }) => {
    const [startDate, setStartDate] = useState(dayjs(dateValue));
    const { width } = useWindowSize();

    const isXLScreen = width >= 1280 && width < 1600;

    const handleDateChange = (date) => {
        setStartDate(date);
        const event = {
            preventDefault: () => { }, // No-op function for preventDefault
            target: {
                name: k,
                value: date.toDate(), // Convert Day.js date to JavaScript Date object
            },
        };
        customHandleChange(event, handleChange);
    };

    return (
        <>
            <LocalizationProvider dateAdapter={AdapterDayjs}>
                <DatePicker
                    value={startDate}
                    views={['year', 'month', 'day']}
                    onChange={handleDateChange}
                    format={formateOfDate}
                    renderInput={(params) => (
                        <TextField
                            {...params}
                            fullWidth
                        />
                    )}
                    sx={{
                        width: '100%',
                        marginRight: '10%', // Add margin-right to the DatePicker
                        ...(width <= 1280 && { maxWidth: '12rem' }),
                        '& .MuiInputBase-root': {
                            height: '35px',
                            border: 'none', // Remove the default border
                            '&:before': {
                                borderBottom: 'none', // Remove the default border before
                            },
                            '&:after': {
                                borderBottom: 'none', // Remove the default border after
                            },
                            '&:hover:not(.Mui-disabled):before': {
                                borderBottom: 'none', // Remove the border on hover
                            },
                            '& input': {
                                paddingRight: '32px', // padding to the input field to accommodate the icon
                                paddingLeft: '4px', // padding to the input field to accommodate the icon
                                fontSize: '0.875rem', // Match font size
                                fontFamily: 'Inter, sans-serif !important', // Match font family
                                color: '#000', // Match text color
                                WebkitFontSmoothing: 'antialiased',
                                MozOsxFontSmoothing: 'grayscale',
                            },
                        },
                        '& .MuiInputBase-input': {
                            fontSize: isXLScreen ? '0.8rem' : '1rem',
                        },
                        '& .MuiInputLabel-root': {
                            marginLeft: 'auto',
                            marginRight: 'auto',
                            top: isXLScreen ? '6px' : '-6px',
                            fontSize: '0.875rem', // Match font size
                            fontFamily: 'Inter, sans-serif !important', // Match font family
                            color: '#000', // Match text color
                        },
                        '& .MuiSvgIcon-root': {
                            position: 'absolute',
                            marginLeft: isXLScreen ? '-2rem' : 'auto',
                            fontSize: isXLScreen ? '1rem' : '1.25rem',
                        },
                        '& .MuiOutlinedInput-root': {
                            '& fieldset': {
                                border: 'none', // Remove the default border
                            },
                            '&:hover fieldset': {
                                border: 'none', // Remove the border on hover
                            },
                            '&.Mui-focused fieldset': {
                                border: 'none', // Remove the border on focus
                            },
                        },
                        position: 'relative', // Ensure the position of the parent container is relative
                    }}
                />
            </LocalizationProvider>
        </>
    );
};

CustomDatePicker.propTypes = {
    k: PropTypes.string.isRequired,
    dateValue: PropTypes.oneOfType([
        PropTypes.instanceOf(Date),
        PropTypes.string,
    ]),
    customHandleChange: PropTypes.func.isRequired,
    handleChange: PropTypes.func.isRequired,
    formateOfDate: PropTypes.string.isRequired,
};

export default CustomDatePicker;
