{"custom_id": "4-39-2024-10-21 18:19:12", "method": "POST", "url": "/v1/chat/completions", "body": {"model": "gpt-4o-2024-08-06", "response_format": {"type": "json_schema", "json_schema": {"name": "simpolo", "strict": true, "schema": {"type": "object", "properties": {"SellerName": {"type": "string", "description": "Name of the seller to whom the invoice is addressed"}, "SellerAddress": {"type": "string", "description": "Address of the seller to whom the invoice is addressed"}, "SellerCIN": {"type": "string", "description": "Corporate Identification Number (CIN) of the seller providing the goods or services"}, "SellerGSTIN": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN or ) of the supplier or seller"}, "SellerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the seller"}, "SellerTel": {"type": "integer", "description": "Telephone Number of the seller providing the goods or services"}, "SellerTollFree": {"type": "integer", "description": "Toll Free Number of the seller company"}, "SellerWeb": {"type": "string", "description": "Website of the seller company"}, "SellerEmailId": {"type": "string", "description": "Email ID of the seller company"}, "SellerIRN": {"type": "string", "description": "Full Invoice Reference Number (IRN) of the issuing company"}, "Category": {"type": "string", "description": "The category or classification of the invoice, such as product type, service type, or other relevant classifications"}, "InvoiceNo": {"type": "integer", "description": "Unique identification number assigned to the invoice"}, "InvoiceDate": {"type": "integer", "description": "The date when the invoice issued strickly convert in MMDDYYYY format from DDMMYYYY format in indian invoice"}, "BuyerName": {"type": "string", "description": "Name of the buyer"}, "BuyerAddress": {"type": "string", "description": "Address of the buyer"}, "BuyerState": {"type": "string", "description": "State Name of the buyer", "enum": ["Andhra Pradesh", "Arunachal Pradesh", "Assam", "Bihar", "Chhattisgarh", "Goa", "Gujarat", "Haryana", "Himachal Pradesh", "Jharkhand", "Karnataka", "Kerala", "Madhya Pradesh", "Maharashtra", "Manipur", "<PERSON><PERSON><PERSON>", "Mizoram", "Nagaland", "Odisha", "Punjab", "Rajasthan", "Sikkim", "Tamil Nadu", "Telangana", "<PERSON>ura", "Uttar Pradesh", "Uttarakhand", "West Bengal", "null"]}, "BuyerStateCode": {"type": "integer", "description": "State code of the buyer"}, "BuyerGSTINNo": {"type": "string", "description": "Goods and Services Tax Identification Number (GSTIN) of the buyer"}, "BuyerPANNo": {"type": "string", "description": "Permanent Account Number (PAN) of the buyer"}, "PlaceOfSupply": {"type": "string", "description": "State or location where the supply of goods or services is made"}, "TransporterName": {"type": "string", "description": "Name of the transporter"}, "VehicleNo": {"type": "string", "description": "Registration number of the vehicle used for transporting goods"}, "LRNo": {"type": "string", "description": "LR (Lorry Receipt) number issued by the transport company for the shipment"}, "NameOfGoods": {"type": "string", "description": "Name of the goods"}, "PORefNo": {"type": "string", "description": "Purchase Order Reference Number used for transport"}, "PODate": {"type": "integer", "description": "Purchase Order Date (In ddmmyy format)."}, "TotalQty(Box)": {"type": "integer", "description": "Total number of boxes included in the invoice"}, "TotalQty(SqMt)": {"type": "number", "description": "Total quantity in square meters included in the invoice"}, "TotalAmount": {"type": "number", "description": "Total Amount of the invoice before taxes and before any additional charges"}, "SubTotal": {"type": "number", "description": "The subtotal amount of the invoice before taxes and additional charges"}, "IGSTrateIn%": {"type": "number", "description": "IGST rate in percentage", "enum": [0, 0.1, 0.25, 3, 5, 12, 18, 28]}, "IGSTamount": {"type": "number", "description": "IGST amount"}, "RoundingOff": {"type": "number", "description": "The rounding adjustment applied to the total amount of the invoice"}, "GrandTotal": {"type": "number", "description": "The final total amount payable on the invoice, including all taxes and rounding adjustments"}, "GrandTotalInWords": {"type": "string", "description": "The final total amount payable on the invoice, written in words, including all taxes and rounding adjustments"}, "OutstandingAmountInclusiveOfBill": {"type": "number", "description": "The total outstanding amount in the account (Subject to realization of payment)"}, "CurrentYearBusinessWithSimpolo": {"type": "number", "description": "The total business volume or value conducted with Simpolo  (Excluding Insu., Freight, GST and this Invoice Value)"}, "OverdueOnAssignedCreditDays": {"type": "number", "description": "The number of overdue days beyond the assigned credit period, based on the company policy"}, "SalesTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the sales target set for the Growth Booster Scheme"}, "PendingTargetForGrowthBoosterSchemeAmount": {"type": "number", "description": "Amount of the remaining sales target to be achieved for the Growth Booster Scheme"}, "BankName": {"type": "string", "description": "The name of the bank involved in the transaction"}, "BankA/CNo": {"type": "integer", "description": "The bank account number involved in the transaction"}, "BankIFSCCode": {"type": "string", "description": "The IFSC code of the bank branch used for the transaction"}, "BankBranch": {"type": "string", "description": "The name of the bank branch involved in the transaction"}, "AmountOfTaxSubjectToReverseCharge": {"type": "string", "description": "The total amount of tax that is subject to reverse charge"}, "ItemTable": {"type": "array", "items": {"type": "object", "properties": {"SrNo": {"type": "integer", "description": "The serial number of the item in the invoice"}, "Description/TradeMark": {"type": "string", "description": "Description and the trade mark or brand associated with the product or service"}, "HSNCode/Grade": {"type": "string", "description": "The HSN code or grade of the product or service"}, "Batch/Shade": {"type": "string", "description": "The batch number or shade for the product, indicating production details"}, "Qty(Box)": {"type": "integer", "description": "The quantity of the product measured in boxes"}, "Qty(SqMt)": {"type": "number", "description": "The quantity of the product measured in square meters"}, "Rate/Box": {"type": "number", "description": "The rate per box for the product"}, "BasicRate/SqFt": {"type": "number", "description": "The basic rate per square foot for the product"}, "TradeDiscUnit": {"type": "number", "description": "Trade Disc. Unit, if present (In percentage)"}, "Project/SpelDisc.": {"type": "number", "description": "Proj / Spcl Disc., if present (In percentage)"}, "SchemeDisc": {"type": "number", "description": "Scheme Disc., if present (In percentage)"}, "ProgressiveDisc": {"type": "number", "description": "Progressive Disc., if present (In percentage)"}, "LoyalyDisc": {"type": "number", "description": "Loyaly Disc., if present (In percentage)"}, "GrBoostDisc": {"type": "number", "description": "<PERSON><PERSON><PERSON>, if present (In percentage)"}, "CashDisc": {"type": "number", "description": "Cash Disc., if present (In percentage)"}, "NetRate/SqFt": {"type": "number", "description": "The net rate per square foot for the product after discounts"}, "Amount": {"type": "number", "description": "The total amount for the item, after applying discounts and taxes"}}, "required": ["SrNo", "Description/TradeMark", "HSNCode/Grade", "Batch/Shade", "Qty(Box)", "Qty(SqMt)", "Rate/Box", "BasicRate/SqFt", "TradeDiscUnit", "Project/SpelDisc.", "SchemeDisc", "ProgressiveDisc", "LoyalyDisc", "GrBoostDisc", "CashDisc", "NetRate/SqFt", "Amount"], "additionalProperties": false}, "description": "Include all rows related to the services or products invoiced including duplicate rows"}}, "required": ["<PERSON><PERSON><PERSON><PERSON>", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "SellerCIN", "SellerGSTIN", "SellerPANNo", "SellerTel", "SellerTollFree", "<PERSON><PERSON><PERSON><PERSON>", "SellerEmailId", "SellerIRN", "Category", "InvoiceNo", "InvoiceDate", "BuyerName", "BuyerAddress", "BuyerState", "BuyerStateCode", "BuyerGSTINNo", "BuyerPANNo", "PlaceOfSupply", "TransporterName", "VehicleNo", "LRNo", "NameOfGoods", "PORefNo", "PODate", "TotalQty(Box)", "TotalQty(SqMt)", "TotalAmount", "SubTotal", "IGSTrateIn%", "RoundingOff", "GrandTotal", "GrandTotalInWords", "OutstandingAmountInclusiveOfBill", "CurrentYearBusinessWithSimpolo", "OverdueOnAssignedCreditDays", "SalesTargetForGrowthBoosterSchemeAmount", "PendingTargetForGrowthBoosterSchemeAmount", "BankName", "BankA/CNo", "BankIFSCCode", "BankBranch", "AmountOfTaxSubjectToReverseCharge", "IGSTamount", "ItemTable"], "additionalProperties": false}}}, "messages": [{"role": "system", "content": "You are a completely obedient accountant who is an expert at structured data extraction from invoices. Follow these steps to perform the complete task: \n\nStep 1: The conversion of a PDF to a text invoice is provided in UserContent in unstrucutred form. \n\nStep 2: Give output in the given structure:\n\nStep 3: Analyze UserContent completely that is given in the following JSON structure \n{\n    \"Text\" : {\n        [ActualPageNumber]: [('[ActualText]', [PageNumber], [x1], [y1], [x2], [y2]),...],...\n    }\n}\n\nTables\nHeading1, Heading2, ... HeadingN\nCell1, Cell2,..., CellN\n\nHere x1, y1, x2, y2 represents standard bounding box coordinates of actual text. \n\nStep 4: Consider bounding box coordinates and reconstruct the exact layout for proper information fetching by considering the relative location of the text perfectly. \n\nStep 5: Find relevant information from the invoice and fill out the required output JSON file. For tables, consider \"Tables\" key-value. If something is not found in the invoice then keep the respective value of the output as ''. \n\nStep 6: Check again for missing any line item in LineItemTable and strictly include all line items from all pages in the output JSON. DO NOT ASSUME ANYTHING."}, {"role": "user", "content": {"1": [["W\n", 1, 438, 160, 409, 140], ["SIMPOLU VIKIFIED PRIVATE LIMITED\n", 1, 670, 166, 1296, 185], ["Registered Office: Survey No. 606, Old Ghuntu Road, Tal: Morbi, Ghuntu -363642, State: GUJARAT\n", 1, 566, 201, 1403, 220], ["CIN: U26914GJ200 051766 GSTIN: 24**********IZU PAN NO: **********\n", 1, 608, 235, 1363, 256], ["○\n", 1, 152, 193, 237, 258], ["<PERSON><PERSON><PERSON>\n", 1, 28, 270, 361, 348], ["Tel.: +91 72289 22222 Toll Free: 1800 233 2223 Website: www.simpolo.net Email Id: <EMAIL>\n", 1, 545, 268, 1423, 289], ["IRN: 32991388726ab60f775a6883d2c076d4975a57d3030c0dfc30f0112b20cee38d\n", 1, 644, 304, 1327, 321], ["Tiles & Bathware\n", 1, 79, 365, 308, 387], ["TAX INVOICE\n", 1, 724, 361, 926, 385], ["CATEGORY: ENIGMA DIAMOND\n", 1, 78, 424, 573, 449], ["Invoice No: 2412008444\n", 1, 663, 422, 980, 448], ["Date/Time: 01.09.2024/23:53:41\n", 1, 1159, 425, 1525, 445], ["Name & Address of Receiver\n", 1, 6, 467, 401, 491], ["Details of Consignee\n", 1, 830, 465, 1109, 490], ["Vhd Distributors Llp\n", 1, 6, 511, 243, 533], ["Vhd Distributors Llp\n", 1, 830, 508, 1069, 530], ["201, <PERSON>, 12,Race Course Road,,\n", 1, 6, 545, 427, 569], ["201,<PERSON>, 12,Race Course Road,,\n", 1, 829, 543, 1251, 565], ["INDORE 452001 MADHYA PRADESH State Code: 23\n", 1, 6, 615, 644, 635], ["INDORE 452001 MADHYA PRADESH State Code: 23\n", 1, 830, 611, 1461, 632], ["GSTIN No: 23**********1ZX - PAN :**********\n", 1, 6, 676, 549, 695], ["Name of Goods:\n", 1, 1365, 672, 1524, 690], ["PO Ref.No:\n", 1, 1896, 670, 2026, 687], ["Transporter Name: AMARDEEP TRANSPORT COMPANY\n", 1, 666, 671, 1303, 695], ["Vehicle No:MP04HE8212 L.R. No:622\n", 1, 665, 707, 1088, 727], ["Ceramic Wall/Floor/Vitrified/Other Tiles & Bathware\n", 1, 1365, 706, 1864, 725], ["PO Date\n", 1, 1895, 706, 1985, 722], [":\n", 1, 2022, 724, 2017, 713], ["Place of Supply: MADHYA PRADESH\n", 1, 5, 712, 388, 734], ["Qty(Box) Qty(Sq Mt) Rate / Box Basic Rate/\n", 1, 788, 756, 1199, 776], ["Trade\n", 1, 1232, 757, 1279, 772], ["Scheme\n", 1, 1435, 756, 1499, 770], ["Proj / Spcl\n", 1, 1319, 757, 1404, 775], ["Progressive Loyaly\n", 1, 1528, 754, 1710, 775], ["<PERSON><PERSON><PERSON>.\n", 1, 1747, 756, 1824, 770], ["Cash\n", 1, 1871, 755, 1910, 768], ["Net Rate /\n", 1, 1956, 754, 2039, 770], ["Batch/\n", 1, 675, 759, 734, 774], ["Amount\n", 1, 2098, 754, 2164, 770], ["HSN Code\n", 1, 515, 760, 603, 777], ["Sr No\n", 1, 14, 763, 63, 777], ["Description |\n", 1, 234, 761, 341, 781], ["Sq Ft\n", 1, 1126, 792, 1171, 809], ["Disc. Unit\n", 1, 1213, 792, 1298, 807], ["Disc.\n", 1, 1341, 793, 1384, 808], ["Disc.\n", 1, 1446, 792, 1489, 807], ["Disc.\n", 1, 1554, 793, 1596, 807], ["Disc.\n", 1, 1660, 792, 1701, 805], ["Shade\n", 1, 678, 795, 728, 809], ["Disc.\n", 1, 1764, 791, 1807, 806], ["Grade\n", 1, 538, 797, 587, 811], ["Disc.\n", 1, 1871, 792, 1913, 805], ["Sqft\n", 1, 1980, 790, 2015, 808], ["Trade Mark\n", 1, 238, 798, 336, 811], ["104,265.33\n", 1, 2084, 824, 2174, 843], ["196 V\n", 1, 810, 831, 877, 847], ["02F4PRES04\n", 1, 647, 834, 759, 849], ["4.0%\n", 1, 1867, 829, 1916, 845], ["34.32\n", 1, 1972, 830, 2016, 843], ["4.0%\n", 1, 1550, 831, 1600, 846], ["282.237\n", 1, 898, 833, 964, 848], ["589.00\n", 1, 1011, 832, 1068, 847], ["38.00\n", 1, 1123, 832, 1167, 846], ["2.0%\n", 1, 1657, 830, 1705, 845], ["FB DESERT SAHARA GV600X600X8.5 -\n", 1, 108, 835, 466, 852], ["6907.21.00 -\n", 1, 512, 836, 615, 850], ["1\n", 1, 32, 839, 40, 852], ["PREMIUM\n", 1, 518, 870, 609, 884], ["2.0%\n", 1, 1657, 904, 1705, 919], ["34.32\n", 1, 1973, 903, 2016, 917], ["4.0%\n", 1, 1550, 905, 1600, 919], ["4.0%\n", 1, 1868, 903, 1917, 918], ["32,981.88\n", 1, 2089, 904, 2168, 919], ["6907.21.00 -\n", 1, 513, 909, 616, 923], ["FB DESERT SAHARA GV600X600X8.5.\n", 1, 109, 908, 463, 925], ["29F4PRES03\n", 1, 648, 909, 759, 923], ["589.00\n", 1, 1012, 906, 1068, 921], ["38.00\n", 1, 1123, 907, 1168, 922], ["62\n", 1, 815, 910, 835, 923], ["89.279\n", 1, 904, 909, 960, 921], ["2\n", 1, 31, 914, 41, 926], ["PREMIUM\n", 1, 516, 944, 611, 958], ["137,247.21\n", 1, 2085, 977, 2172, 992], ["Total\n", 1, 683, 984, 726, 996], ["258\n", 1, 809, 983, 839, 996], ["371.52\n", 1, 904, 981, 959, 995], ["Sub Total\n", 1, 1483, 1018, 1562, 1033], ["137,247.21\n", 1, 2093, 1012, 2205, 1032], ["IGST @18.00%\n", 1, 1454, 1052, 1588, 1071], ["24,704.50\n", 1, 2104, 1047, 2206, 1067], ["State Bank of India A/C No. ***********, IFSC Code SBIN0016390, <PERSON> (Morvi)\n", 1, 7, 1020, 942, 1041], ["Amount of Tax subject to reverse charge - No\n", 1, 7, 1053, 491, 1078], ["Certified that the particular given are true & correct.\n", 1, 7, 1087, 553, 1114], ["Loyalty and Growth Booster Discount are applicable if there is no overdues.\n", 1, 8, 1122, 811, 1147], ["* Marked scheme discount are from Growth lifting scheme.\n", 1, 9, 1156, 632, 1178], ["0.29\n", 1, 2161, 1081, 2205, 1099], ["Rounding Off\n", 1, 1465, 1087, 1581, 1106], ["<PERSON><PERSON><PERSON>\n", 1, 719, 1191, 860, 1229], ["Remark:\n", 1, 8, 1196, 95, 1213], ["&\n", 1, 1970, 1311, 2003, 1354], ["Amount (Words) :RUPEE ONE LAKH SIXTY-ONE THOUSAND NINE HUNDRED FIFTY-TWO ONLY\n", 1, 9, 1345, 1148, 1370], ["Grand Total\n", 1, 1435, 1337, 1614, 1360], ["161,952.00\n", 1, 2093, 1339, 2207, 1360], ["161,952.00\n", 1, 1252, 1427, 1355, 1444], ["Outstanding in your account inclusive of the bill is (Subject to realization of payment)\n", 1, 8, 1427, 908, 1453], ["********.38\n", 1, 2059, 1423, 2180, 1441], ["Current Year Business with Simpolo\n", 1, 1408, 1422, 1790, 1445], ["Overdue on Assigned Credit days as per company policy\n", 1, 9, 1469, 538, 1494], ["(Excluding Insu., Freight, GST and this Invoice Value)\n", 1, 1415, 1463, 1984, 1489], ["*********.38 Pending Target For Growth Booster Scheme\n", 1, 1247, 1504, 1871, 1530], ["********.00\n", 1, 2061, 1505, 2181, 1522], ["Sales Target For Growth Booster Scheme\n", 1, 10, 1509, 447, 1534], ["①\n", 1, 1050, 1649, 1163, 1745], ["(Page: 1/2 )\n", 1, 1898, 1692, 2047, 1713], ["ORIGNAL FOR RECEIPIENT\n", 1, 11, 1699, 304, 1717]]}}]}}