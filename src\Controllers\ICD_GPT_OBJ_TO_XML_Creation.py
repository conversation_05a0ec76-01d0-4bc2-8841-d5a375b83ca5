import sys
sys.path.append(".")
import os
import json
import xml.etree.ElementTree as ET
from  src.Controllers.ICDController import CPegasus_XML
import asyncio
async def process_json_files(input_dir: str, output_dir: str):
    """Reads JSON files from input_dir, converts them to XML, and saves them to output_dir."""
    if not os.path.exists(output_dir):
        os.makedirs(output_dir)
    
    for file_name in os.listdir(input_dir):
        if file_name.endswith(".json"):
            json_path = os.path.join(input_dir, file_name)
            with open(json_path, "r", encoding="utf-8") as f:
                json_data = json.load(f)
            
            content_dict = json.loads(json_data.get("choices", [{}])[0].get("message", {}).get("content", {}))
            lsAdditionalExcelTemplate = [
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/DD/1030",
                                    "Job Number": "E-03807",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/DD/1030",
                                    "Job Number": "E-03808",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "GRASIM INDUSTRIES LIMITED",
                                    "Invoice No": "2024-25/DD/1031",
                                    "Job Number": "E-03632",
                                    "Container No (Please specify if not provided)": "CSLU2326869 "
                                },
                                {
                                    "A/C Name": "GRASIM INDUSTRIES LIMITED",
                                    "Invoice No": "2024-25/DD/1031",
                                    "Job Number": "E-03633",
                                    "Container No (Please specify if not provided)": "TLLU2283436 "
                                },
                                {
                                    "A/C Name": "GRASIM INDUSTRIES LIMITED",
                                    "Invoice No": "2024-25/DD/1031",
                                    "Job Number": "E-03651",
                                    "Container No (Please specify if not provided)": "TRHU3959847"
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7018",
                                    "Job Number": "E-03500 ",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7093",
                                    "Job Number": "E-03837",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "SOURCEIMEX CONNEXIONS LLP",
                                    "Invoice No": "2024-25/7112",
                                    "Job Number": "E-03531 ",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/7119",
                                    "Job Number": "24-2025\nI-02945 ",
                                    "Container No (Please specify if not provided)": "TCKU7032910,TCKU6958281"
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7125",
                                    "Job Number": "E-03636",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7128",
                                    "Job Number": "E-03554",
                                    "Container No (Please specify if not provided)": "TCLU8260949,TCNU4963977"
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7393",
                                    "Job Number": "E-02713",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7394",
                                    "Job Number": "E-02520",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7395",
                                    "Job Number": "E-02704 ",
                                    "Container No (Please specify if not provided)": "BEAU5302361,DRYU6057304"
                                },
                                {
                                    "A/C Name": " VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7515",
                                    "Job Number": "E-03849",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": " VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7579",
                                    "Job Number": "E-03862",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/7586",
                                    "Job Number": "I-03099",
                                    "Container No (Please specify if not provided)": "CMAU3346115,TRHU6123531"
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7606",
                                    "Job Number": "E-03875",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES",
                                    "Invoice No": "2024-25/7609",
                                    "Job Number": "I-03085",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES",
                                    "Invoice No": "2024-25/7609",
                                    "Job Number": "I-03086",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES",
                                    "Invoice No": "2024-25/7609",
                                    "Job Number": "I-03087",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/7620",
                                    "Job Number": "I-02971",
                                    "Container No (Please specify if not provided)": "TGBU4140562, CMAU6205893"
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/7622",
                                    "Job Number": "I-02972",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/7624",
                                    "Job Number": "E-03937",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/DD/1089",
                                    "Job Number": "I-02921",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD ",
                                    "Invoice No": "2024-25/DD/1090",
                                    "Job Number": "E-03969",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VE COMMERCIAL VEHICLES LTD",
                                    "Invoice No": "2024-25/DD/1091",
                                    "Job Number": "E-03969",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/DD/1094",
                                    "Job Number": "I-02972",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/DD/1095",
                                    "Job Number": "I-02971",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/DD/1097",
                                    "Job Number": "I-03264",
                                    "Container No (Please specify if not provided)": ""
                                },
                                {
                                    "A/C Name": "VOLVO GROUP INDIA PRIVATE LIMITED",
                                    "Invoice No": "2024-25/DD/1098",
                                    "Job Number": "I-03264",
                                    "Container No (Please specify if not provided)": ""
                                }
                            ]
            try:
                strXMLData = await CPegasus_XML.MSCreateXML(dictExtractedData=content_dict, 
                                                            iUserId=4, 
                                                            AdditionalExcelTemplate=lsAdditionalExcelTemplate)
                
                xml_filename = os.path.splitext(file_name)[0] + ".xml"
                xml_path = os.path.join(output_dir, xml_filename)
                with open(xml_path, "wb") as f:
                    f.write(strXMLData)
            except Exception as e:
                import traceback
                print(traceback.print_exc())
                print(f"Failed to create xml for file: {file_name}")
            print(f"Processed: {file_name} ")

if __name__ == "__main__":
    input_directory = r"H:\DEVELOPER_PUBLIC\Developers\Mohit\indianInvoicePosting\data\apiResponses\1_PICD\Run3"  # Change this to your JSON directory
    output_directory = r"H:\AI Data\18_FairdealInternational\TallyXML\All Invoices XML"  # Change this to your XML output directory
    asyncio.run(process_json_files(input_directory, output_directory))
