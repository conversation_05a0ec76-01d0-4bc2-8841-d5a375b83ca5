from typing import Optional
from pydantic import BaseModel
from datetime import datetime

class GetAllLogFilterQuery(BaseModel):
    bSectionAsc: Optional[bool] = None
    bMessageAsc: Optional[bool] = None
    bLogDateTimeAsc: Optional[bool] = None
    strLogType: Optional[str] = None
    strSection: Optional[str] = None
    strSearch: Optional[str] = None
    strStartdate: Optional[datetime] = None
    strEnddate: Optional[datetime] = None
    iUserId: Optional[int] = None
    bUserIdAsc: Optional[bool] = None