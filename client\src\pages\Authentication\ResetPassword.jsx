import toast, { Toaster } from 'react-hot-toast';
import { useEffect, useState } from "react";
import { IoMdEyeOff, IoMdEye } from "react-icons/io";
import { jwtDecode } from 'jwt-decode';
import { useParams } from "react-router-dom";
import axios from "axios";
import logo from '../../assets/logo.svg'
import { string, object } from 'yup'; // Import yup validation functions
import Slider from "react-slick";
import {
    Box,
    Image,
    SimpleGrid,
} from "@chakra-ui/react";
import Login_Image_1 from "../../assets/Login_Image_1.svg";
import Login_Image_2 from "../../assets/Login_Image_2.svg";
import Login_Image_3 from "../../assets/Login_Image_3.svg";
import Login_Image_4 from "../../assets/Login_Image_4.svg";
import Login_Image_5 from "../../assets/Login_Image_5.svg";
import Login_Image_6 from "../../assets/Login_Image_6.svg";

export default function ResetPassword() {
    const { token } = useParams();
    const [email, setEmail] = useState('');
    const [password, setPassword] = useState('');
    const [passwordCriteria, setPasswordCriteria] = useState({
        minLength: false,
        oneUppercase: false,
        oneNumber: false,
        oneSymbol: false,
    });
    const [confirmPassword, setConfirmPassword] = useState('');
    const [isTokenValid, setIsTokenValid] = useState(null); // null: checking, true: valid, false: expired
    const [showPassword, setShowPassword] = useState(false);
    const [showConfPassword, setConfShowPassword] = useState(false);

    // Define the password validation schema
    const passwordValidationSchema = object({
        password: string()
            .required('Password is required')
            .min(8, 'Password must be at least 8 characters long')
            .matches(/[A-Z]/, 'Password must contain at least one uppercase letter')
            .matches(/\d/, 'Password must contain at least one number')
            .matches(/[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/, 'Password must contain at least one symbol'),
    });

    const handleTogglePasswordVisibility = () => setShowPassword(!showPassword);
    const handleToggleConfPasswordVisibility = () => setConfShowPassword(!showConfPassword);

    var settings = {
        infinite: true,
        arrows: false,
        fade: true,
        speed: 500,
        slidesToShow: 1,
        slidesToScroll: 1,
        autoplay: true,
        autoplaySpeed: 10000,
    };

    useEffect(() => {
        try {
            if (localStorage.getItem('token') !== null) {
                localStorage.removeItem('token')
            }
            const decoded = jwtDecode(token);
            setEmail(decoded.email);
            const currentTime = Date.now() / 1000; // Convert to seconds to match JWT exp format
            if (decoded.exp < currentTime) {
                setIsTokenValid(false);
            } else {
                setIsTokenValid(true);
            }
        } catch (error) {
            console.error('Error decoding token', error);
            setIsTokenValid(false); // Assume invalid if there's an error
        }
    }, [token]); // Run once when the component mounts

    const updatePasswordCriteria = (value) => {
        const trimmedValue = value.replace(/\s/g, ''); // Remove leading and trailing white spaces
        setPasswordCriteria({
            minLength: trimmedValue.length >= 8,
            oneUppercase: /[A-Z]/.test(trimmedValue),
            oneNumber: /\d/.test(trimmedValue),
            oneSymbol: /[\s!@#$%^&*()_+\-=\[\]{};':"\\|,.<>\/?]+/.test(trimmedValue),
        });
    };

    const validatePassword = async (value) => {
        updatePasswordCriteria(value); // Update the criteria every time the password changes
        try {
            await passwordValidationSchema.validate({ password: value });
            // You can set a state for the valid password if you want to display some message or indication
        } catch (error) {
            // Handle validation error, you can also set state here if you want to show the error message
            if (error.path === 'password') {
                // Handle the specific error where password does not meet validation criteria
                throw new Error('Password must be 8+ characters, with uppercase, number, symbol, and no spaces');
            } else {
                // Handle other validation errors
                throw error;
            }
        }
    };

    const handleLogin = async () => {
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/login`,
                {
                    email: email,
                    password: password,
                }
            );
            if (response.status === 200 && response.data !== null) {
                toast.success("Login successful");
                localStorage.setItem("token", response.data.jwt_token);
                setTimeout(() => {
                    window.location.href = '/';
                }, 2000);
            }
            else {
                toast.error("Login failed.");
            }
        } catch (error) {
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error("An unexpected error occurred"); // Fallback message
            }
            toast.error(error.response?.data?.detail || "An unexpected error occurred.");
            console.error("Login failed:", error);
        }
    };

    const handleSubmitEmail = async (e) => {
        e.preventDefault();
        try {
            await passwordValidationSchema.validate({ password });
        } catch (error) {
            toast.error("Password does not meet requirements.");
            return;
        }

        if (password !== confirmPassword) {
            toast.error("Passwords do not match. Please ensure both passwords are the same.");
            return;
        }

        const requestData = { password };
        try {
            const response = await axios.post(
                `${import.meta.env.VITE_SERVER}/reset-password`,
                requestData,
                {
                    headers: {
                        "Content-Type": "application/json",
                        "Authorization": `Bearer ${token}`
                    },
                }
            );
            if (response.status === 200) {
                toast.success("Password Updated Successfully!");
                // Instead of navigating immediately, call handleLogin to perform automatic login
                handleLogin(); // Automatically attempt to log in with the new password
            }
        } catch (error) {
            // console.log(error)
            if (error.response && error.response.data && error.response.data.detail) {
                toast.error(error.response.data.detail);
            } else {
                toast.error(error.response?.data?.detail || "Failed to update password.");// Fallback message
            }
            console.error("Password update failed:", error);
        }
    };

    if (!isTokenValid) {
        return (
            <div className="min-h-screen flex justify-center items-center">
                <div>This link is expired.</div>
            </div>
        );
    }

    const svg1 = (
        <svg className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M5 13l4 4L19 7" />
        </svg>
    )

    const svg2 = (
        <svg className="h-4 w-4 inline mr-1" fill="none" viewBox="0 0 24 24" stroke="currentColor">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth="2" d="M6 18L18 6M6 6l12 12" />
        </svg>
    )

    return (
        <div className="min-h-screen flex flex-col lgo:flex-row bg-[#ffff]">
            <Toaster position="top-center"></Toaster>
            <SimpleGrid height={"100vh"} columns={2}>
                <Box height={"100vh"} display="flex" alignItems="center" justifyContent='center' className="overflow-auto">
                    <Box
                        mx={"auto"}
                        className="max-w-lg"
                        width={{ xl: "23vw", lg: "fit-content" }}
                        height={"fit-content"}
                        paddingY={"50px"}
                    >
                        <div className="flex flex-col mb-8 items-center justify-center">
                            <img src={logo} alt="AccuVelocity" className="h-12 w-auto mb-4" />
                        </div>
                        <div className='flex justify-center mdo:justify-start'>
                            <h2 className="text-xl smo:text-2xl lgo:text-3xl font-semibold text-gray-900 text-center mdo:text-left">Set a new password</h2>
                        </div>
                        <p className="mt-2 text-xs smo:text-sm lgo:text-base text-gray-600 mb-6 text-center lgo:text-left">
                            Please set a new password. We suggest it be distinct from any prior password.
                        </p>
                        <form className="space-y-6" onSubmit={handleSubmitEmail}>
                            <div className="relative">
                                <label htmlFor="password" className="block text-xs smo:text-sm lgo:text-base font-medium font-inter text-gray-800 mb-1">
                                    Password<span className="text-red-500">*</span>
                                </label>
                                <div className="mt-1 flex rounded-md shadow-sm">
                                    <input
                                        type={showPassword ? 'text' : 'password'}
                                        required
                                        className="block w-full p-3 border border-r-0 border-gray-300 text-xs smo:text-sm lgo:text-base rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Enter your new password"
                                        value={password}
                                        onChange={(e) => {
                                            const trimmedValue = e.target.value.replace(/\s/g, ''); // Remove spaces from the input
                                            setPassword(trimmedValue);
                                            validatePassword(trimmedValue);
                                        }}
                                        pattern="^\S+$" // Pattern disallowing spaces
                                        title="Password cannot contain spaces" // Error message for invalid pattern
                                    />
                                    <button
                                        type="button"
                                        onClick={handleTogglePasswordVisibility}
                                        className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3"
                                    >
                                        {showPassword ? (
                                            <IoMdEyeOff className="h-5 w-5 text-gray-600" />
                                        ) : (
                                            <IoMdEye className="h-5 w-5 text-gray-600" />
                                        )}
                                    </button>
                                </div>
                            </div>
                            <ul className="list-none mt-2 mb-6">
                                <li className={`${passwordCriteria.minLength ? 'text-green-500' : 'text-gray-500'} text-xs smo:text-sm lgo:text-base`}>
                                    {passwordCriteria.minLength ? (
                                        svg1
                                    ) : (
                                        svg2
                                    )}
                                    At least 8 characters
                                </li>
                                <li className={`${passwordCriteria.oneUppercase ? 'text-green-500' : 'text-gray-500'} text-xs smo:text-sm lgo:text-base`}>
                                    {passwordCriteria.oneUppercase ? (
                                        svg1
                                    ) : (
                                        svg2
                                    )}
                                    At least one uppercase letter
                                </li>
                                <li className={`${passwordCriteria.oneNumber ? 'text-green-500' : 'text-gray-500'} text-xs smo:text-sm lgo:text-base`}>
                                    {passwordCriteria.oneNumber ? (
                                        svg1
                                    ) : (
                                        svg2
                                    )}
                                    At least one number
                                </li>
                                <li className={`${passwordCriteria.oneSymbol ? 'text-green-500' : 'text-gray-500'} text-xs smo:text-sm lgo:text-base`}>
                                    {passwordCriteria.oneSymbol ? (
                                        svg1
                                    ) : (
                                        svg2
                                    )}
                                    At least one symbol
                                </li>
                            </ul>
                            <div className="relative">
                                <label htmlFor="confirm-password" className="block text-xs smo:text-sm lgo:text-base font-medium font-inter text-gray-800 mb-1">
                                    Confirm Password<span className="text-red-500">*</span>
                                </label>
                                <div className="mt-1 flex rounded-md shadow-sm">
                                    <input
                                        type={showConfPassword ? 'text' : 'password'}
                                        required
                                        className="block w-full p-3 border border-r-0 border-gray-300 text-xs smo:text-sm lgo:text-base rounded-l-md shadow-sm focus:outline-none focus:ring-blue-500 focus:border-blue-500"
                                        placeholder="Confirm password"
                                        value={confirmPassword}
                                        onChange={(e) => {
                                            const trimmedValue = e.target.value.replace(/\s/g, ''); // Remove spaces from the input
                                            setConfirmPassword(trimmedValue);
                                        }}
                                        pattern="^\S+$" // Pattern disallowing spaces
                                        title="Password cannot contain spaces" // Error message for invalid pattern
                                    />
                                    <button
                                        type="button"
                                        onClick={handleToggleConfPasswordVisibility}
                                        className="inline-flex items-center border border-l-0 border-gray-300 bg-gray-50 rounded-r-md p-3"
                                    >
                                        {showConfPassword ? (
                                            <IoMdEyeOff className="h-5 w-5 text-gray-600" />
                                        ) : (
                                            <IoMdEye className="h-5 w-5 text-gray-600" />
                                        )}
                                    </button>
                                </div>
                            </div>
                            <div>
                                <button
                                    type="submit"
                                    className="w-full text-xs smo:text-sm lgo:text-base flex justify-center py-2 px-4 border border-transparent rounded-md shadow-sm font-medium text-white bg-[#003654] hover:bg-[#002744] focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-[#003654]"
                                >
                                    Reset Password
                                </button>
                            </div>
                        </form>
                    </Box>
                </Box>
                <Box height={"100vh"} overflow={"hidden"}>
                    <Box position={"relative"}>
                        {" "}
                        <Slider {...settings}>
                            {[
                                { id: 1, image: Login_Image_1 },
                                { id: 2, image: Login_Image_2 },
                                { id: 3, image: Login_Image_3 },
                                { id: 4, image: Login_Image_4 },
                                { id: 5, image: Login_Image_5 },
                                { id: 6, image: Login_Image_6 },
                            ].map((item) => {
                                return (
                                    <Box key={item.id} position={"relative"}>


                                        <Image
                                            src={item.image}
                                            alt="Decorative"
                                            objectFit={"cover"}
                                            minH={"100vh"}
                                            height={"100vh"}
                                            className="max-w-full max-h-full"
                                        />
                                    </Box>
                                );
                            })}
                        </Slider>
                    </Box>
                </Box>
            </SimpleGrid>
        </div >
    );
}
