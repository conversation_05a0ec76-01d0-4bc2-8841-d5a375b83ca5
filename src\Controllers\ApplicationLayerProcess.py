from sqlalchemy.orm import Session
from sqlalchemy.exc import SQLAlchemyError
import traceback
from fastapi.responses import JSONResponse
from fastapi import FastAPI, HTTPException, status, Depends
from src.Controllers.DocumentData_controller import CDocumentData
from src.Controllers.Logs_Controller import CLogController
# from src.Controllers.GPTResponse_controller import CGPTResponseData
from src.utilities.DBHelper import CExtractionTable


class ApplicationLayerProcessing:
    
    @staticmethod
    async def prepareDocumentExtractionResponse(user_id: int, document_id: int, dictAPIRespnse, strVoucherType = None):
        """Prepare and respond with document extraction details and handle any potential errors during the process."""
        try:
            from src.Controllers.auth_controller import CAuthController
            userData = await CAuthController.MSGetUserMetaData(user_id=user_id)
            # Log success in processing
            await CLogController.MSWriteLog(user_id, "Info", f"Document {document_id} processed successfully.")
            objDocumentData = await CDocumentData.MSGetDocMetaDataByDocId(userid=user_id,doc_id=document_id, strVoucherType=strVoucherType)
            # Return a successful JSON response with document details
            # dictAPIEndpointResponse = {
            #         "document_id": document_id,
            #         "page_limit_left":userData.get("page_limit_left"),
            #         "total_allowed_page_limit":userData.get("total_allowed_page_limit"),
            #         "free_page_limit_usage":userData.get("free_page_limit_usage"),
            #         "total_allowed_free_page_limit":userData.get("total_allowed_free_page_limit"),
            #         "Document": dictAPIRespnse,
            #         "APIStatusCode": 500 if (objDocumentData.get("doc_extraction_api_status_code","") is None or (objDocumentData.get("doc_extraction_api_status_code")==0)) else objDocumentData.get("doc_extraction_api_status_code",""),
            #         "DocErrorMsg": objDocumentData.get("error_msg",""),
            #         "DocExtractionStatus": objDocumentData.get("Status"),
            #         "model_id": objDocumentData.get("model_id"),
            #         "model_name": objDocumentData.get("model_name"),
            #         "model_family": objDocumentData.get("model_family"),
            #         "IsPaidModel": objDocumentData.get("IsPaidModel")
            #     }
            # dictAPIEndpointResponse["bExisingGPTResponseAvailable"] = True if dictAPIEndpointResponse["APIStatusCode"] == 200 else False
            # return dictAPIEndpointResponse
            return {
                    "document_id": document_id,
                    "page_limit_left":userData.get("page_limit_left"),
                    "total_allowed_page_limit":userData.get("total_allowed_page_limit"),
                    "free_page_limit_usage":userData.get("free_page_limit_usage"),
                    "total_allowed_free_page_limit":userData.get("total_allowed_free_page_limit"),
                    "Document": dictAPIRespnse,
                    "APIStatusCode": 500 if (objDocumentData.get("doc_extraction_api_status_code","") is None or (objDocumentData.get("doc_extraction_api_status_code")==0)) else objDocumentData.get("doc_extraction_api_status_code",""),
                    "DocErrorMsg": objDocumentData.get("error_msg",""),
                    "DocExtractionStatus": objDocumentData.get("Status"),
                    "model_id": objDocumentData.get("model_id"),
                    "model_name": objDocumentData.get("model_name"),
                    "model_family": objDocumentData.get("model_family"),
                    "IsPaidModel": objDocumentData.get("IsPaidModel"),
                    "DocumentPages":objDocumentData.get("DocPages")
                }
        except Exception as e:
            # Log and respond with a generic server error message
            await CLogController.MSWriteLog(user_id, "Error", f"Exception while processing document {document_id}.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="It seems there might be a temporary issue. Please refresh the page to ensure you have the latest updates.")

    @staticmethod
    async def CheckExistDocumentExtractionResponse(user_id: int, document_id: int, strVoucherType = None):
        """Prepare and respond with document extraction details and handle any potential errors during the process."""
        try:
            
            from src.Controllers.auth_controller import CAuthController
            userData = await CAuthController.MSGetUserMetaData(user_id=user_id)
            
            # Log success in processing
            await CLogController.MSWriteLog(user_id, "Info", f"Document {document_id} processed successfully.")
            objDocumentData = await CDocumentData.MSGetDocMetaDataByDocId(userid=user_id,doc_id=document_id,strVoucherType = strVoucherType)
            if objDocumentData.get("doc_extraction_api_status_code","")==200:
                dictresponse_data= await CExtractionTable.MSGetLatestExtractedData(user_id,document_id)
                dictAPIRespnse=dictresponse_data["DocVerifiedData"]
                # Return a successful JSON response with document details
                dictAPIEndpointResponse = {
                        "document_id": document_id,
                        "page_limit_left":userData.get("page_limit_left"),
                        "total_allowed_page_limit":userData.get("total_allowed_page_limit"),
                        "free_page_limit_usage":userData.get("free_page_limit_usage"),
                        "total_allowed_free_page_limit":userData.get("total_allowed_free_page_limit"),
                        "Document": dictAPIRespnse,
                        "APIStatusCode": 500 if (objDocumentData.get("doc_extraction_api_status_code","") is None or (objDocumentData.get("doc_extraction_api_status_code")==0)) else objDocumentData.get("doc_extraction_api_status_code",""),
                        "DocErrorMsg": objDocumentData.get("error_msg",""),
                        "DocExtractionStatus": objDocumentData.get("Status"),
                        "model_id": objDocumentData.get("model_id"),
                        "model_name": objDocumentData.get("model_name"),
                        "model_family": objDocumentData.get("model_family"),
                        "IsPaidModel": objDocumentData.get("IsPaidModel"),
                        "DocumentPages":objDocumentData.get("DocPages")
                    }
                dictAPIEndpointResponse["bExisingGPTResponseAvailable"] = True if dictAPIEndpointResponse["APIStatusCode"] == 200 else False
                return dictAPIEndpointResponse
            else:
                return False
        except Exception as e:
            # Log and respond with a generic server error message
            await CLogController.MSWriteLog(user_id, "Error", f"Exception while processing document {document_id}.")
            await CLogController.MSWriteLog(user_id, "Debug", f"Traceback: {str(traceback.format_exc())}")
            raise HTTPException(status_code=500, detail="It seems there might be a temporary issue. Please refresh the page to ensure you have the latest updates.")
