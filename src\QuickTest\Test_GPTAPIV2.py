import sys
sys.path.append(".")
from src.GPTAPIV2 import CGPTAPIResponseV2
import json
import os
import asyncio
import datetime
import time

# Run the asynchronous function
lsTestCases = [
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T1_Parag_SystemPrompt.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T1_Parag_GPTAPI_UserContent.txt",
    #     "dictResponseFormatFile":r"GitIgnore\QuickTestData\T1_Parag_Simpolo_GPTResponseFormat.json",
    #     "strClientName":"Parag Trader",
    #     "strVoucherType":"Purchase With Inventory",
    #     "strExtractAPIName":"GPT4o",
    #     "reasoning_effort":"high",
    #     "bIsReasoningModel":False,
    #     "bIsGrokAPI":False,
    #     "max_tokens":20000,
    #     "doc_id":4225
    # },
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T2_Parag_GPTAPI_SystemPrompt.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T2_Parag_UserContent.txt",
    #     "dictResponseFormatFile":r"GitIgnore\QuickTestData\T2_Parag_Hansgrohe_GPTResponseFormat.json",
    #     "strClientName":"Parag Trader",
    #     "strVoucherType":"Purchase With Inventory",
    #     "strExtractAPIName":"GPT4o",
    #     "reasoning_effort":"low",
    #     "bIsReasoningModel":False,
    #     "bIsGrokAPI":False,
    #     "max_tokens":20000,
    #     "doc_id":4590
    # },
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T3_Parag_GPTAPI_SystemPrompt.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T3_Parag_UserContent.txt",
    #     "dictResponseFormatFile":r"GitIgnore\QuickTestData\T3_Parag_BSNL_GPTResponseFormat.json",
    #     "strClientName":"Parag Trader",
    #     "strVoucherType":"Purchase Without Inventory",
    #     "strExtractAPIName":"GrokReasoning",
    #     "reasoning_effort":"high",
    #     "bIsReasoningModel":True,
    #     "bIsGrokAPI":True,
    #     "max_tokens":27000,
    #     "doc_id":4409
    # },
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T4_Parag_GPTAPI_SystemPrompt.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T4_Parag_UserContent.txt",
    #     "dictResponseFormatFile":r"Data\Customer\17_ParagTraders\APIResponseFormat.json",
    #     "strClientName":"Parag Trader",
    #     "strVoucherType":"Purchase With Inventory",
    #     "strExtractAPIName":"GPT4o",
    #     "reasoning_effort":"low",
    #     "bIsReasoningModel":False,
    #     "bIsGrokAPI":False,
    #     "max_tokens":20000,
    #     "doc_id":5222
    # },
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T5_Abhinav_GPTAPI_GeneralPWI_SystemPrompt_V3.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T5_Abhinav_GPTAPI_UserContent.txt",
    #     "dictResponseFormatFile":r"Data\Customer\Abhinav InfraBuild\APIResponseFormat.json",
    #     "strClientName":"Abhinav Infra",
    #     "strVoucherType":"Purchase With Inventory",
    #     "strExtractAPIName":"Grok3Fast",
    #     "reasoning_effort":"high",
    #     "bIsReasoningModel":True,
    #     "bIsGrokAPI":True,
    #     "max_tokens":27000,
    #     "doc_id":4794
    # },
    {
        "strSystemPromptFile":r"GitIgnore\QuickTestData\T6_Vedansh_GPTAPI_GeneralPWOI_SystemPrompt_V3.txt",
        "strUserContentFile":r"GitIgnore\QuickTestData\T6_Vedansh_UserContent.txt",
        "dictResponseFormatFile":r"Data\Customer\VedanshSchool\GPTAPI_GeneralPWOI_ResponseSchema_V3.json",
        "strClientName":"Vedansh",
        "strVoucherType":"Receipt Note",
        "strExtractAPIName":"Grok3Fast",
        "reasoning_effort":"low",
        "bIsReasoningModel":False,
        "bIsGrokAPI":True,
        "max_tokens":27000,
        "doc_id":4789
    },
    # {
    #     "strSystemPromptFile":r"GitIgnore\QuickTestData\T7_ICD_GPTAPI_SystemPrompt.txt",
    #     "strUserContentFile":r"GitIgnore\QuickTestData\T7_ICD_Concor_UserContent.txt",
    #     "dictResponseFormatFile":r"GitIgnore\QuickTestData\T7_ICD_Concor_GPTResponseFormat.json",
    #     "strClientName":"ICD",
    #     "strVoucherType":"Journal",
    #     "strExtractAPIName":"GrokReasoning",
    #     "reasoning_effort":"high",
    #     "bIsReasoningModel":True,
    #     "bIsGrokAPI":True,
    #     "max_tokens":27000,
    #     "doc_id":4351
    # },
]
# Ensure the output directory exists
output_dir = r"GitIgnore\QuickTestData\Responses"
os.makedirs(output_dir, exist_ok=True)

for dictTestCase in lsTestCases:
    try:
        # Prepare metadata
        dictMetaData = CGPTAPIResponseV2.MSBuildOpenAIMetadata(
            strClientName=dictTestCase["strClientName"],
            strvourcherType=dictTestCase["strVoucherType"],
            bDevelopmentMode=True
        )

        # Load system prompt, user content, and response format
        with open(dictTestCase["dictResponseFormatFile"]) as f:
            dictResponseFormat = json.load(f)

        with open(dictTestCase["strUserContentFile"]) as f:
            strUserContent = f.read()

        with open(dictTestCase["strSystemPromptFile"]) as f:
            strSystemContent = f.read()

        # Create test object
        objTest = CGPTAPIResponseV2(
            document_id=dictTestCase["doc_id"],
            user_id=4,
            bDebug=False,
            strClientName=dictTestCase["strClientName"],
            strVoucherType="Purchase With Inventory",
            isDevelopmentMode=True
        )

        # Prepare params for API call
        params = CGPTAPIResponseV2.MSSetExtractAPIConfig(
            strExtractAPIName=dictTestCase["strExtractAPIName"],
            strUserContent=strUserContent,
            strSystemContent=strSystemContent,
            dictResponseFormat=dictResponseFormat,
            dictMetaData=dictMetaData
        )
        
        # Record start time
        start_time = time.time()
        start_dt = datetime.datetime.now()
        print(f"Start Time: {start_dt.strftime('%Y-%m-%d %H:%M:%S')}")

        # Run extraction API
        response = asyncio.run(objTest.runExtractAPI(**params))

        # Record end time
        end_time = time.time()
        end_dt = datetime.datetime.now()
        print(f"End Time: {end_dt.strftime('%Y-%m-%d %H:%M:%S')}")

        # Calculate duration
        elapsed = end_time - start_time
        mins, secs = divmod(elapsed, 60)
        print(f"Total Time Taken: {int(mins)} min {int(secs)} sec")

        # Save json_objects to file
        file_name = f'{dictTestCase['doc_id']}_response.json'
        file_name2 = f'{dictTestCase['doc_id']}_object.json'
        file_path = os.path.join(output_dir, file_name)
        file_path2 = os.path.join(output_dir, file_name2)

        with open(file_path, "w") as f:
            json.dump(response.get("json_objects"), f, indent=4)

        with open(file_path2, "w") as f:
            json.dump(response.get("response"), f, indent=4)

        print(f"Saved response to {file_path}, Object Saved to {file_path2}")
        print(response)
    except Exception as e:
        print(f"Error in test case {dictTestCase['doc_id']}: {str(e)}")
        continue