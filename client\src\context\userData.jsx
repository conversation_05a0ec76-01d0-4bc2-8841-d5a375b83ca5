import { atom, useSetRecoilState, useRecoilValue } from "recoil";
import { jwtDecode } from "jwt-decode";

// Recoil atom for storing user data decoded from JWT token
const userDataState = atom({
  key: 'userDataState',
  default: (() => {
    const token = localStorage.getItem("token");
    const notOrgDetailsToken = localStorage.getItem("NotOrgDetailsToken");

    try {
      const decoded = token ? jwtDecode(token) : null;
      return {
        userData: decoded,
        isLoggedIn: decoded ? true : false,
        isOrgDetailsNotGiven : notOrgDetailsToken ? true : false 
      };
    } catch (error) {
      return {
        userData: null,
        isLoggedIn: false,
        isOrgDetailsNotGiven : false
      };
    }
  })(),
});


export const isUserInitializedStateAtom = atom({
  key: 'isUserInitializedState',
  default: false,
});

export const userNameAtom = atom({
  key: 'userName',
  default: undefined
});


export const useIsUserInitializedValue = () => useRecoilValue(isUserInitializedStateAtom);

export const useIsUserInitialized = () => useSetRecoilState(isUserInitializedStateAtom);

export const useUserNameGetter = () => useRecoilValue(userNameAtom);

export const useUserNameSetter = () => useSetRecoilState(userNameAtom);

export default userDataState;
