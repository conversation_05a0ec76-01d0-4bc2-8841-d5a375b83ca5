# global_rate_limit_middleware.py
from datetime import timedelta
from fastapi.responses import JSONResponse
from config.constants import Constants
from fastapi import Request, HTTPException
from fastapi import Depends, Request
from src.utilities.RateLimitMiddleware import RateLimitingMiddleware

# Create a global instance with desired settings
global_rate_limiter = RateLimitingMiddleware(rate_limit_requests=Constants.GeneralRateLimitForDocExtractionService, rate_limit_duration=timedelta(minutes=1))



def rate_limiter():
    """
    This function acts as a rate limiter middleware for FastAPI applications. It creates a dependency that can be used to apply rate limiting to specific routes or endpoints.
    
    Inputs:
        None
    
    Outputs:
        A FastAPI dependency function that can be used as a decorator for routes or endpoints.

    The `rate_limiter` function returns the `dependency` function wrapped in a `Depends` decorator, making it a valid FastAPI dependency that can be used as a decorator for routes or endpoints.
    """
    async def dependency(request: Request):
        response = await global_rate_limiter.dispatch(request)
        if response.status_code == 429:
            raise HTTPException(status_code=429, detail="Rate limit exceeded. Please try again later.")
        return response
    return Depends(dependency)
